package org.springcenter.product.wsc.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.wsc.modules.model.product.JicVidBdInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/2/26 9:25
 */
public interface JicVidBdInfoMapper extends BaseMapper<JicVidBdInfo> {

    JicVidBdInfo selectByVidAndBrandId(@Param("vid") String vid, @Param("brandId") String brandId, @Param("proBrandId") Long proBrandId);


}
