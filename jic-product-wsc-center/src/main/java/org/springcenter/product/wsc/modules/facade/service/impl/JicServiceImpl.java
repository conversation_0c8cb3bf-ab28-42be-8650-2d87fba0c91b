package org.springcenter.product.wsc.modules.facade.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springcenter.product.wsc.modules.voucher.context.WeimoCalcDiscountContext;
import org.springcenter.product.wsc.modules.voucher.entity.*;
import org.springcenter.product.wsc.modules.facade.service.IJicService;
import org.springcenter.product.wsc.modules.webapi.IJIcHttpApi;
import org.springcenter.product.wsc.modules.webapi.jic.JicSdkCommonResponse;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/29 10:51
 * @description
 */
@Service
@Slf4j
public class JicServiceImpl implements IJicService {

     @Resource
     private IJIcHttpApi ijIcHttpApi;


     @Override
     public WeimoCalcDiscountEntity weimoCalcDiscount(WeimoCalcDiscountContext weimoCalcDiscountContext) {
          log.info("weimob计算价格:{}",weimoCalcDiscountContext);
          try {
               Response<JicSdkCommonResponse<WeimoCalcDiscountEntity>> result = ijIcHttpApi.weimoCalcDiscount(weimoCalcDiscountContext).execute();
               if (!result.isSuccessful()) {
                    throw new RuntimeException("weimob计算价格失败接口异常" + result.errorBody());
               }
               JicSdkCommonResponse<WeimoCalcDiscountEntity> response = result.body();
               if(!response.getSuccess()){
                    throw new RuntimeException("weimob计算价格失败接口异常" + response.getMsg());
               }
               log.info("weimob计算价格接口，param={}, response={}", JSON.toJSONString(weimoCalcDiscountContext),JSON.toJSONString(response.getData()));
               return response.getData();
          } catch (Exception e) {
               log.error("weimob计算价格失败接口异常，param={}", JSON.toJSONString(weimoCalcDiscountContext), e);
               throw new RuntimeException("weimob计算价格失败接口异常 e:" + e);
          }
     }


     @Override
     public VoucherAvailableRespEntity voucherAvailable(VoucherAvailableReqEntity voucherAvailableReqEntity) {
          try {
               Response<JicSdkCommonResponse<VoucherAvailableRespEntity>> result = ijIcHttpApi.voucherAvailable(voucherAvailableReqEntity).execute();
               if (!result.isSuccessful()) {
                    throw new RuntimeException("用户可用券外部接口调用失败:" + result.errorBody());
               }
               JicSdkCommonResponse<VoucherAvailableRespEntity> response = result.body();
               if(!response.getSuccess()){
                    throw new RuntimeException("用户可用券外部接口调用失败:" + response.getMsg());
               }
               return response.getData();
          }catch (Exception e){
               log.error("用户可用券外部接口调用异常,param={}", JSON.toJSONString(voucherAvailableReqEntity), e);
               throw new RuntimeException("用户可用券外部接口调用异常：" + e.getMessage());
          }
     }

     @Override
     public String sendVouByPoints(SendVouByPointsEntity sendVouByPoints) {
          try {
               Response<JicSdkCommonResponse<SendVouByPointsRespEntity>> result = ijIcHttpApi.sendVouByPoints(sendVouByPoints).execute();
               if (!result.isSuccessful()) {
                    throw new RuntimeException("积分换券外部接口调用失败:" + result.errorBody());
               }
               JicSdkCommonResponse<SendVouByPointsRespEntity> response = result.body();
               if(!response.getSuccess()){
                    throw new RuntimeException("积分换券外部接口调用失败:" + response.getMsg());
               }
               if(ObjectUtils.isEmpty(response.getData()) || ObjectUtils.isEmpty(response.getData().getCode())){
                    throw new RuntimeException("积分换券外部接口调用失败,返回data为空");
               }
               if(!"0".equals(response.getData().getCode().getResult())){
                    throw new RuntimeException("积分换券外部接口调用失败," + response.getData().getCode().getResultMsg());
               }
               return response.getData().getVoucherNo();
          }catch (Exception e){
               log.error("积分换券外部接口调用异常,param={}", JSON.toJSONString(sendVouByPoints), e);
               throw new RuntimeException("积分换券外部接口调用异常：" + e.getMessage());
          }
     }

     @Override
     public List<ExchangeVouListRespEntity> exchangeList(ExchangeVouListReqEntity exchangeVouListReq) {
          try {
               Response<JicSdkCommonResponse<BaseExchangeVouRespEntity>> result = ijIcHttpApi.exchangeList(exchangeVouListReq).execute();
               if (!result.isSuccessful()) {
                    throw new RuntimeException("积分换券列表外部接口调用失败:" + result.errorBody());
               }
               JicSdkCommonResponse<BaseExchangeVouRespEntity> response = result.body();
               if(!response.getSuccess()){
                    throw new RuntimeException("积分换券列表外部接口调用失败:" + response.getMsg());
               }
               if(ObjectUtils.isEmpty(response.getData()) || ObjectUtils.isEmpty(response.getData().getCode())){
                    throw new RuntimeException("积分换券列表外部接口调用失败,返回data为空");
               }
              /* if(!"0".equals(response.getData().getCode().getResult())){
                    throw new RuntimeException("积分换券列表外部接口调用失败," + response.getData().getCode().getResultMsg());
               }*/
               return response.getData().getItems();
          }catch (Exception e){
               log.error("积分换券列表外部接口调用异常,param={}", JSON.toJSONString(exchangeVouListReq), e);
               throw new RuntimeException("积分换券列表外部接口调用异常：" + e.getMessage());
          }
     }



     @Override
     public List<WmGoodInfoResp> getGoodsInfoList(WmGoodInfoReq context) {
          log.info("获取商品信息:{}",context);
          try {
               Response<JicSdkCommonResponse<List<WmGoodInfoResp>>> result = ijIcHttpApi.getGoodsInfoList(context).execute();
               if (!result.isSuccessful()) {
                    throw new RuntimeException("weimob获取商品信息失败接口异常" + result.errorBody());
               }
               JicSdkCommonResponse<List<WmGoodInfoResp>> response = result.body();
               if(!response.getSuccess()){
                    throw new RuntimeException("weimob获取商品信息失败接口异常" + response.getMsg());
               }
               log.info("weimob获取商品信息接口，param={}, response={}", JSON.toJSONString(context),JSON.toJSONString(response.getData()));
               return response.getData();
          } catch (Exception e) {
               log.error("weimob获取商品信息失败接口异常，param={}", JSON.toJSONString(context), e);
               throw new RuntimeException("weimob获取商品信息失败接口异常 e:" + e);
          }
     }
}
