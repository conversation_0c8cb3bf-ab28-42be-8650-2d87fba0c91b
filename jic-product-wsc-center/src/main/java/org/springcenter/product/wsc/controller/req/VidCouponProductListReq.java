package org.springcenter.product.wsc.controller.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/4/25 14:04
 */
@Data
public class VidCouponProductListReq {

    @ApiModelProperty(value = "门店", required = true)
    @NotBlank(message = "门店不能为空")
    private String vid;

    @ApiModelProperty(value = "当前门店品牌", required = true)
    @NotNull(message = "当前门店品牌不能为空")
    private Long weId;

    @ApiModelProperty(value = "当前用户id", required = true)
    @NotBlank(message = "当前用户id不能为空")
    private String unionId;

    @ApiModelProperty(value = "分类id")
    private List<Long> cids;

    @ApiModelProperty(value = "品牌")
    private List<Long> brandIds;

    @ApiModelProperty(value = "尺码信息")
    private List<String> sizeNos;

    @ApiModelProperty(value = "尺码类型 0:衣服 1:鞋子 2:其他")
    private List<Integer> sizeClassType;

    @ApiModelProperty(value = "颜色标签")
    private List<String> codes;

    @ApiModelProperty(value = "风格标签")
    private List<String> styleCodes;

    @ApiModelProperty(value = "排序 0综合排序 1销量排序 2新品排序 3价格由高到低 4价格由低到高 5不走排序")
    private Integer sorted = 0;

    @ApiModelProperty(value = "搜索")
    private String searchWord;

    @ApiModelProperty(value = "高价")
    private Integer gPrice;

    @ApiModelProperty(value = "低价")
    private Integer lPrice;

    @ApiModelProperty(value = "包含的商品id")
    private List<Long> includeProductIds;

    @ApiModelProperty(value = "排除的商品id")
    private List<Long> excludeProductIds;

    @ApiModelProperty(value = "大数据搜索词全局id")
    private String wscSearchWordReqId;

    @ApiModelProperty(value = "大数据统计数据用")
    private Long wid;

    @ApiModelProperty(value = "多个项目排查问题使用")
    private String traceId;

    /*@ApiModelProperty(value = "临时 该字段为0则走大数据")
    private Integer temporarily;*/
    @ApiModelProperty(value = "判断是否是压测")
    private Boolean pTest = false;


    @ApiModelProperty(value = "大数据搜索使用")
    private String requestSource;

    @ApiModelProperty(value = "券规则ID，与商品包ID二选一必填", required = true)
//    @NotBlank(message = "券列表的券规则Id不能为空")
    private String couponRuleId;

    @ApiModelProperty(value = "商品包ID，与券规则ID二选一必填", required = true)
    private String productPackageId;

    @ApiModelProperty(value = "大数据搜索使用")
    private String requestScenes;

}
