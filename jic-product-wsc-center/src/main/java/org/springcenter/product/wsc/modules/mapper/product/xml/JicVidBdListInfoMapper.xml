<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.wsc.modules.mapper.product.JicVidBdListInfoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.wsc.modules.model.product.JicVidBdListInfo">
        <id column="ID" property="id" />
        <result column="VID" property="vid" />
        <result column="BRAND_ID" property="brandId" />
        <result column="BIG_CATE_ID" property="bigCateId" />
        <result column="SMALL_CATE_ID" property="smallCateId" />
        <result column="BD_LIST" property="bdList" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="PRO_BRAND_ID" property="proBrandId" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, VID, BRAND_ID, BIG_CATE_ID, SMALL_CATE_ID, BD_LIST, IS_DELETED,
        CREATE_TIME, UPDATE_TIME, PRO_BRAND_ID
    </sql>
    <select id="selectByCateAndVidAndBrandId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM JIC_VID_BD_LIST_INFO
        WHERE BIG_CATE_ID = #{bigCateId}
        AND SMALL_CATE_ID = #{smallCateId}
        AND VID = #{vid}
        AND BRAND_ID = #{brandId}
        <if test="proBrandId != null and proBrandId != ''">
            AND PRO_BRAND_ID = #{proBrandId}
        </if>
        AND IS_DELETED = 0
    </select>


</mapper>
