package org.springcenter.product.wsc.modules.product.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.jnby.authority.common.util.DateUtils;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springcenter.product.api.IProductPackageApi;
import org.springcenter.product.api.dto.background.ProductPackageExportEntity;
import org.springcenter.product.api.dto.wsc.VidCidProductEsSearchResp;
import org.springcenter.product.api.dto.wsc.VidProductEsSearchReq;
import org.springcenter.product.api.enums.wsc.ProductStatusEnum;
import org.springcenter.product.wsc.controller.req.VidCouponProductListFilterReq;
import org.springcenter.product.wsc.controller.req.VidCouponProductListReq;
import org.springcenter.product.wsc.controller.req.VidProductListReq;
import org.springcenter.product.wsc.modules.facade.service.IBigDataService;
import org.springcenter.product.wsc.modules.mapper.bojun.MProductMapper;
import org.springcenter.product.wsc.modules.product.entity.CouponRuleProductEntity;
import org.springcenter.product.wsc.modules.product.entity.ProductInfoEsSearchResp;
import org.springcenter.product.wsc.modules.product.entity.VidProductFilterRespEntity;
import org.springcenter.product.wsc.modules.product.entity.VidProductListRespEntity;
import org.springcenter.product.wsc.modules.product.enums.*;
import org.springcenter.product.wsc.modules.product.service.IProductVidCouponService;
import org.springcenter.product.wsc.modules.product.service.IProductVidService;
import org.springcenter.product.wsc.modules.util.EsUtil;
import org.springcenter.product.wsc.modules.util.RedisService;
import org.springcenter.product.wsc.modules.webapi.jic.BigDataComprehensiveData;
import org.springcenter.product.wsc.modules.webapi.jic.BigDataComprehensiveReq;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/11/18 9:24
 */
@Service
@Slf4j
@RefreshScope
public class ProductVidCouponServiceImpl implements IProductVidCouponService {

    @Autowired
    private IProductVidService productVidService;

    @Autowired
    private MProductMapper mProductMapper;

    @Autowired
    private IBigDataService bigDataService;


    @Autowired
    private RedisService redisService;
    @Autowired
    private IProductPackageApi productPackageApi;

    @Override
    public List<VidProductListRespEntity> queryVidCouponGoods(VidCouponProductListReq requestData, Page page) {
        String lSql = null;
        String productPackageId = requestData.getProductPackageId();
        if (StringUtils.isNotBlank(productPackageId)) {
            CommonRequest<String> pkgRequest = new CommonRequest<>();
            pkgRequest.setRequestData(productPackageId);
            ResponseResult<List<ProductPackageExportEntity>> response = productPackageApi.getProductPackageForc(pkgRequest);
            if (response == null || CollectionUtils.isNotEmpty(response.getData())) {
                log.error("商品包未返回商品信息");
                return null;
            }
            List<Long> pids = response.getData().stream().map(ProductPackageExportEntity::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            lSql = "in (" + StringUtils.join(pids, ",") + ")";
        } else {
            // 获取优惠券的sql
            Object o = redisService.get(RedisKeysEnum.COUPON_RULE_SQL.join(requestData.getCouponRuleId()));
            if (o == null) {
                lSql = mProductMapper.selectCouponRuleByRuleId(requestData.getCouponRuleId());
                redisService.set(RedisKeysEnum.COUPON_RULE_SQL.join(requestData.getCouponRuleId()), lSql, 86400);
            } else {
                lSql = (String) o;
            }
        }
        if (StringUtils.isBlank(lSql)) {
            log.error("没有查到该优惠券id/商品包id的信息");
            return null;
        }

        // 参数校验
        VidProductListReq req = new VidProductListReq();
        BeanUtils.copyProperties(requestData, req);
        productVidService.checkParams(req);

        VidProductEsSearchReq esSearchReq = new VidProductEsSearchReq();
        List<VidProductListRespEntity> ret = new ArrayList<>();

        // 实际查询
        if (StringUtils.isNotBlank(requestData.getSearchWord())) {
            // 走大数据搜索
            List<Long> spuIds = productVidService.searchInBigDataBySearch(req, false);

            page.setCount(spuIds.size());

            if (CollectionUtils.isEmpty(spuIds)) {
                return Collections.emptyList();
            } else {
                esSearchReq.setProductIds(spuIds);
                log.info("1.2.2 queryVidGoods走大数据搜索查询es组装");
                ret = regularBuildByBdSearch(requestData, page, esSearchReq, lSql);
            }

        } else {
            // 判断是否走大数据综合排序
            if (Objects.equals(requestData.getSorted(), ProductVidSortedEnum.SYNTHESIS.getCode())) {
                // 1代表从第一页开始 前面没满足条数的list 顺序的list
                ret = regularComprehensiveByBdSearch(requestData, page, esSearchReq, 1, new ArrayList<>(),
                        new ArrayList<>(), lSql);
            } else {
                log.info("1.4.1 纯走es搜索");
                // 前面没满足条数的list 1代表从第一页开始  顺序的list
                ret = regularBuildByEsSearch(requestData, page, esSearchReq, new ArrayList<>(), 1, lSql);
            }

        }
        return ret;
    }

    private List<VidProductListRespEntity> regularComprehensiveByBdSearch(VidCouponProductListReq requestData, Page page,
                                                                          VidProductEsSearchReq esSearchReq,
                                                                          int i,
                                                                          List<VidProductListRespEntity> oldRet,
                                                                          List<Long> bDProductList,
                                                                          String lSql) {
        //如果 递归发现数据够了   就处理之后出去
        if (oldRet.size() >= page.getPageNo() * page.getPageSize()) {

            return getComprehensiveOrder(oldRet, bDProductList)
                    .stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());
        }
        if (i > 10 || (page.getPages() != 0 && i > page.getPages())) {
            return getComprehensiveOrder(oldRet, bDProductList)
                    .stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());
        }

        int pageSize = 100;
        // 查询对应的商品id
        String yyyyMMdd = new SimpleDateFormat("yyyyMMdd").format(new Date());
        List<CouponRuleProductEntity> entities = new ArrayList<>();
        Object o = redisService.get(RedisKeysEnum.COUPON_RULE_PAGE_DATA.join(i, pageSize, null, lSql, requestData.getLPrice(),
                requestData.getGPrice(), requestData.getBrandIds(), Long.valueOf(yyyyMMdd)));
        Integer total = 0;
        Integer totalPage = 0;
        if (o == null) {
            com.github.pagehelper.Page<Long> hPage = PageHelper.startPage(i, pageSize);
            entities = mProductMapper.selectByCouponRuleAndOtherParams(null, lSql, requestData.getLPrice(), requestData.getGPrice(),
                    requestData.getBrandIds(), Long.valueOf(yyyyMMdd));
            total = (int) hPage.getTotal();
            totalPage = (int) hPage.getPages();
            redisService.set(RedisKeysEnum.COUPON_RULE_PAGE_DATA.join(i, pageSize, null, lSql, requestData.getLPrice(),
                    requestData.getGPrice(), requestData.getBrandIds(), Long.valueOf(yyyyMMdd)), JSONObject.toJSONString(entities), 86400);
            redisService.set(RedisKeysEnum.COUPON_RULE_FIRST_PAGE_DATA.join(i, pageSize, null, lSql, requestData.getLPrice(),
                    requestData.getGPrice(), requestData.getBrandIds(), Long.valueOf(yyyyMMdd)),
                    hPage.getTotal() + "@" + hPage.getPages(), 86400);
        } else {
            entities = JSONObject.parseArray(o.toString(), CouponRuleProductEntity.class);
        }

        if (CollectionUtils.isEmpty(entities)) {
            return getComprehensiveOrder(oldRet, bDProductList)
                    .stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());
        }
        List<Long> productList = entities.stream().map(CouponRuleProductEntity::getId).collect(Collectors.toList());
        Pair<List<Long>, Integer> productIdInfos = searchInBdComprehensive(productList, requestData, 0, 800 - 1);
        if (CollectionUtils.isEmpty(productIdInfos.getKey())) {
            if (i == 1) {
                // 如果第一次大数数综合排序报错或者没有值 走es销量排序
                requestData.setSorted(ProductVidSortedEnum.SALES_VOLUME.getCode());
                return regularBuildByEsSearch(requestData, page, esSearchReq, new ArrayList<>(), 1, lSql);
            } else {
                //大数据没有数据的时候出去
                log.info("******* 大数据综合排序 查询大数据组装数据 第" + i + "次");
                return getComprehensiveOrder(oldRet, bDProductList)
                        .stream()
                        .skip((page.getPageNo() - 1) * page.getPageSize())
                        .limit(page.getPageSize())
                        .collect(Collectors.toList());
            }
        } else {
            if (i == 1) {
                Object o1 = redisService.get(RedisKeysEnum.COUPON_RULE_FIRST_PAGE_DATA.join(i, 800, null, lSql, requestData.getLPrice(),
                        requestData.getGPrice(), requestData.getBrandIds(), Long.valueOf(yyyyMMdd)));
                if (o1 != null) {
                    try {
                        String[] split = Objects.toString(o1).split("@");
                        Integer totals = Integer.valueOf(split[0]);
                        Integer totalPages = Integer.valueOf(split[1]);
                        page.setCount(totals);
                        page.setPages(totalPages);
                    } catch (Exception e) {
                        log.error("转换的分页数据报错");
                    }
                } else {
                    page.setCount(total);
                    page.setPages(totalPage);
                }
            }
        }

        esSearchReq.setProductIds(productIdInfos.getKey());
        bDProductList.addAll(productIdInfos.getKey());

        return regularEsBuild(requestData, page, esSearchReq, i, oldRet, bDProductList, lSql);
    }

    private List<VidProductListRespEntity> regularBuildByEsSearch(VidCouponProductListReq requestData, Page page,
                                                                  VidProductEsSearchReq esSearchReq,
                                                                  List<VidProductListRespEntity> oldRet, int i,
                                                                  String lSql) {
        // 查询当前条件是否有数据
        if (oldRet.size() >= page.getPageNo() * page.getPageSize()) {
            return  oldRet
                    .stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());
        }

        // 如果当前页数超过100页
        if (i > 10 || (page.getPages() != 0 && i > page.getPages())) {
            return oldRet
                    .stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());
        }

        // 各种筛选条件[除尺码、颜色外]
        BeanUtils.copyProperties(requestData, esSearchReq);


        // 获取数据库中的数据
        List<CouponRuleProductEntity> productEntities = new ArrayList<>();


        Object o = redisService.get(RedisKeysEnum.COUPON_RULE_PAGE_DATA.join(i, 800, null, lSql, requestData.getLPrice(),
                requestData.getGPrice(), requestData.getBrandIds(), Long.valueOf(new SimpleDateFormat("yyyyMMdd").format(new Date()))));

        if (o == null) {
            com.github.pagehelper.Page<Long> hPage = PageHelper.startPage(i, 800);
            productEntities = mProductMapper.selectByCouponRuleAndOtherParams(null, lSql, requestData.getLPrice(),
                    requestData.getGPrice(), requestData.getBrandIds(), Long.valueOf(new SimpleDateFormat("yyyyMMdd").format(new Date())));
            redisService.set(RedisKeysEnum.COUPON_RULE_PAGE_DATA.join(i, 800, null, lSql, requestData.getLPrice(),
                    requestData.getGPrice(), requestData.getBrandIds(), Long.valueOf(new SimpleDateFormat("yyyyMMdd").format(new Date()))),
                    JSONObject.toJSONString(productEntities), 86400);
        } else {
            productEntities = JSONObject.parseArray(Objects.toString(o), CouponRuleProductEntity.class);
        }


        if (CollectionUtils.isEmpty(productEntities)) {
            return oldRet
                    .stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());
        }

        List<Long> productIds = productEntities.stream().map(CouponRuleProductEntity::getId).collect(Collectors.toList());
        esSearchReq.setPageNo(1);
        esSearchReq.setPageSize(productIds.size() + 100);
        // 如果没有尺码、颜色、风格的筛选条件 || 查询数据为空
        if ((CollectionUtils.isEmpty(requestData.getSizeNos())
                && CollectionUtils.isEmpty(requestData.getSizeClassType())
                && CollectionUtils.isEmpty(requestData.getCodes())
                && CollectionUtils.isEmpty(requestData.getStyleCodes()))
                || CollectionUtils.isEmpty(productIds)) {

            List<VidProductListRespEntity> list = searchInVidEs(esSearchReq, productIds, new ArrayList<>(), null, true);
            oldRet.addAll(list);
            return regularBuildByEsSearch(requestData, page, esSearchReq, oldRet, i + 1, lSql);
        }


        // 过滤尺码、颜色、风格
        List<Long> filterSizeNos = new ArrayList<>();
        List<Long> filterColors = new ArrayList<>();
        List<Long> filterStyles = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(requestData.getSizeNos()) || CollectionUtils.isNotEmpty(requestData.getSizeClassType())) {
            List<ProductInfoEsSearchResp> esSearchResps = productVidService.esInSearchByProductInfo(productIds, requestData.getSizeNos(),
                    EsParamSearchEnum.SIZE_INFO, esSearchReq.getSizeClassType(), new ArrayList<>());
            filterSizeNos = esSearchResps.stream().map(ProductInfoEsSearchResp::getId).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(requestData.getCodes())) {
            List<ProductInfoEsSearchResp> esSearchResps = productVidService.esInSearchByProductInfo(productIds, requestData.getCodes(),
                    EsParamSearchEnum.COLOR_CODE, esSearchReq.getSizeClassType(), new ArrayList<>());
            filterColors = esSearchResps.stream().map(ProductInfoEsSearchResp::getId).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(requestData.getStyleCodes())) {
            List<ProductInfoEsSearchResp> esSearchResps = productVidService.esInSearchByProductInfo(productIds, requestData.getStyleCodes(),
                    EsParamSearchEnum.STYLE_CODE, esSearchReq.getSizeClassType(), new ArrayList<>());
            filterStyles = esSearchResps.stream().map(ProductInfoEsSearchResp::getId).collect(Collectors.toList());
        }

        log.info("1.4.1.2 纯走es搜索 过滤尺码、颜色、风格查询取交集" + "第" + i + "次");
        // 返回信息 取尺码和颜色的交集 对返回数据进行处理
        List<String> sizeNos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(requestData.getSizeClassType())) {
            requestData.getSizeClassType().forEach(v -> {
                sizeNos.add(String.valueOf(v));
            });
        }
        List<Long> retList = productVidService.getRetList(filterSizeNos, filterColors, sizeNos, requestData.getCodes());
        List<Long> retList1 = productVidService.getRetList(retList, filterStyles,
                CollectionUtils.isEmpty(sizeNos) ? requestData.getCodes() : sizeNos,
                requestData.getStyleCodes());


        if (CollectionUtils.isEmpty(retList1)) {
            return regularBuildByEsSearch(requestData, page, esSearchReq, oldRet, i + 1, lSql);
        }

        List<VidProductListRespEntity> list = searchInVidEs(esSearchReq, retList1, new ArrayList<>(), null, true);
        oldRet.addAll(list);
        return regularBuildByEsSearch(requestData, page, esSearchReq, oldRet, i + 1, lSql);

    }

    private List<VidProductListRespEntity> regularEsBuild(VidCouponProductListReq requestData, Page page,
                                                          VidProductEsSearchReq esSearchReq, int i,
                                                          List<VidProductListRespEntity> oldRet,
                                                          List<Long> bDProductList, String lSql) {
        BeanUtils.copyProperties(requestData, esSearchReq);
        esSearchReq.setPageNo(0);
        esSearchReq.setPageSize(esSearchReq.getProductIds().size() + 100);
        // 如果没有尺码、颜色、风格的筛选条件 || 查询数据为空
        if ((CollectionUtils.isEmpty(requestData.getSizeNos())
                && CollectionUtils.isEmpty(requestData.getSizeClassType())
                && CollectionUtils.isEmpty(requestData.getCodes())
                && CollectionUtils.isEmpty(requestData.getStyleCodes()))
                || CollectionUtils.isEmpty(bDProductList)) {
            // 查询当前上架且可售的数据
            if (CollectionUtils.isEmpty(bDProductList)) {
                return Collections.emptyList();
            }
            List<VidProductListRespEntity> list = searchInVidEs(esSearchReq, esSearchReq.getProductIds(), new ArrayList<>(), null, true);
            oldRet.addAll(list);
            return regularComprehensiveByBdSearch(requestData, page, esSearchReq, i + 1, oldRet, bDProductList, lSql);
        }



        // 查询对应风格和颜色、尺码
        log.info("1.2.2.2 大数据搜索es过滤尺码、颜色、风格查询");
        List<Long> filterSizeNos = new ArrayList<>();
        List<Long> filterColors = new ArrayList<>();
        List<Long> filterStyles = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(requestData.getSizeNos()) || CollectionUtils.isNotEmpty(requestData.getSizeClassType())) {
            List<ProductInfoEsSearchResp> esSearchResps = productVidService.esInSearchByProductInfo(esSearchReq.getProductIds(), requestData.getSizeNos(),
                    EsParamSearchEnum.SIZE_INFO, esSearchReq.getSizeClassType(), new ArrayList<>());
            filterSizeNos = esSearchResps.stream().map(ProductInfoEsSearchResp::getId).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(requestData.getCodes())) {
            List<ProductInfoEsSearchResp> esSearchResps = productVidService.esInSearchByProductInfo(esSearchReq.getProductIds(), requestData.getCodes(),
                    EsParamSearchEnum.COLOR_CODE, esSearchReq.getSizeClassType(), new ArrayList<>());
            filterColors = esSearchResps.stream().map(ProductInfoEsSearchResp::getId).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(requestData.getStyleCodes())) {
            List<ProductInfoEsSearchResp> esSearchResps = productVidService.esInSearchByProductInfo(esSearchReq.getProductIds(), requestData.getStyleCodes(),
                    EsParamSearchEnum.STYLE_CODE, esSearchReq.getSizeClassType(), new ArrayList<>());
            filterStyles = esSearchResps.stream().map(ProductInfoEsSearchResp::getId).collect(Collectors.toList());
        }

        log.info(" 大数据搜索过滤尺码、颜色、风格查询取交集");
        // 返回信息 取尺码和颜色的交集 对返回数据进行处理
        List<String> sizeNos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(requestData.getSizeClassType())) {
            requestData.getSizeClassType().forEach(v -> {
                sizeNos.add(String.valueOf(v));
            });
        }
        List<Long> retList = productVidService.getRetList(filterSizeNos, filterColors, sizeNos, requestData.getCodes());
        List<Long> retList1 = productVidService.getRetList(retList, filterStyles,
                CollectionUtils.isEmpty(sizeNos) ? requestData.getCodes() : sizeNos,
                requestData.getStyleCodes());


        if (CollectionUtils.isEmpty(retList1)) {
            return regularComprehensiveByBdSearch(requestData, page, esSearchReq, i + 1, oldRet, bDProductList, lSql);
        }

        List<VidProductListRespEntity> list = searchInVidEs(esSearchReq, retList1, new ArrayList<>(), null, true);
        oldRet.addAll(list);
        return regularComprehensiveByBdSearch(requestData, page, esSearchReq, i + 1, oldRet, bDProductList, lSql);
    }

    private Pair<List<Long>, Integer> searchInBdComprehensive(List<Long> productList,
                                                              VidCouponProductListReq requestData, int pageNo, int pageSize) {
        BigDataComprehensiveReq req = new BigDataComprehensiveReq();
        req.setSpuIds(productList);
        req.setUnionId(requestData.getUnionId());
        req.setVId(requestData.getVid());
        req.setWeid(Objects.toString(requestData.getWeId()));
        req.setTarIdType(BigDataComprehensiveTarEnum.PRODUCT_ID.getCode());
        req.setRequestSource(BigDataComprehensiveEnum.COUPON_MALL.getCode());
        req.setPTest(requestData.getPTest());
        req.setTraceId(requestData.getTraceId());

        req.setTopKGt(pageNo);
        req.setTopKLt(pageSize);

        Pair<List<BigDataComprehensiveData>, Integer> pair = bigDataService.searchComprehensiveList(req);
        return Pair.of(pair.getKey().stream().map(BigDataComprehensiveData::getM_product_id).collect(Collectors.toList()), pair.getValue());
    }



    @Value("${product.brand.info}")
    private String productBrandInfo;

    @Value("${product.color.info}")
    private String productColorInfo;

    @Value("${product.style.info}")
    private String productStyleInfo;

    @Override
    public VidProductFilterRespEntity queryVidCouponFilters(VidCouponProductListFilterReq requestData, Page page) {

        Object o = redisService.get(RedisKeysEnum.COUPON_RULE_SQL.join(requestData.getCouponRuleId()));
        String lSql;
        if (o == null) {
            lSql = mProductMapper.selectCouponRuleByRuleId(requestData.getCouponRuleId());
            redisService.set(RedisKeysEnum.COUPON_RULE_SQL.join(requestData.getCouponRuleId()), lSql, 86400);
        } else {
            lSql = (String) o;
        }

        if (StringUtils.isBlank(lSql)) {
            log.error("没有查到该优惠券id的信息");
            return null;
        }


        VidProductFilterRespEntity entity = new VidProductFilterRespEntity();
        // 如果是 奥莱||江南布衣+ 则需要返回品牌信息
        if (Objects.equals(requestData.getWeId(), BigDataBrandEnum.JNBY_PLUS.getCode())
                || Objects.equals(requestData.getWeId(), BigDataBrandEnum.AO_LAI.getCode())) {
            List<VidProductFilterRespEntity.BrandEntity> brandEntities = JSONObject
                    .parseArray(productBrandInfo, VidProductFilterRespEntity.BrandEntity.class);
            entity.setBrandFilters(brandEntities);
        }
        // 直接返回颜色筛选条件
        List<VidProductFilterRespEntity.ColorEntity> colorEntities = JSONObject
                .parseArray(productColorInfo, VidProductFilterRespEntity.ColorEntity.class);
        entity.setColorFilters(colorEntities);

        // 直接返回风格筛选条件
        List<VidProductFilterRespEntity.StyleEntity> styleEntities = JSONObject
                .parseArray(productStyleInfo, VidProductFilterRespEntity.StyleEntity.class);
        entity.setStyleFilters(styleEntities);

        // 根据大数据返回尺码信息
        List<VidProductFilterRespEntity.SizeEntity> sizeInfoRets = new ArrayList<>();
        List<Long> productIds = new ArrayList<>();
        if (StringUtils.isNotBlank(requestData.getKeywords())) {
            VidProductListReq vidProductListReq = new VidProductListReq();
            BeanUtils.copyProperties(requestData, vidProductListReq);
            vidProductListReq.setSearchWord(requestData.getKeywords());
            productIds = productVidService.searchInBigDataBySearch(vidProductListReq, false);

            if (CollectionUtils.isEmpty(productIds)) {
                return entity;
            }
        }


        String yyyyMMdd = new SimpleDateFormat("yyyyMMdd").format(new Date());
        List<CouponRuleProductEntity> secProductNames = new ArrayList<>();
        Object o1 = redisService.get(RedisKeysEnum.COUPON_RULE_FILTER_DATA.join(productIds, lSql, Long.valueOf(yyyyMMdd)));
        if (o1 == null) {
            com.github.pagehelper.Page<Long> hPage = PageHelper.startPage(1, 999);
            secProductNames = mProductMapper.selectByCouponRuleAndOtherParams(productIds,
                    lSql, null, null, null, Long.valueOf(yyyyMMdd));
            redisService.set(RedisKeysEnum.COUPON_RULE_FILTER_DATA.join(productIds, lSql, Long.valueOf(yyyyMMdd)),
                    JSONObject.toJSONString(secProductNames), 86400);
        } else {
            secProductNames = JSONObject.parseArray(o1.toString(), CouponRuleProductEntity.class);
        }


        List<String> names = secProductNames.stream().map(CouponRuleProductEntity::getOrigName).collect(Collectors.toList());
        VidProductEsSearchReq searchReq = new VidProductEsSearchReq();
        searchReq.setVid(requestData.getVid());
        List<VidProductListRespEntity> entities = searchInVidEs(searchReq, new ArrayList<>(), names
                , requestData.getKeywords(), false);
        if (CollectionUtils.isEmpty(entities)) {
            return entity;
        }

        List<ProductInfoEsSearchResp> esSearchResps = productVidService.esInSearchByProductInfo(new ArrayList<>(),
                Collections.emptyList(), null, Collections.emptyList(), names);
        if (CollectionUtils.isEmpty(esSearchResps)) {
            return entity;
        }
        sizeInfoRets = productVidService.buildProductInfoSize(esSearchResps, sizeInfoRets);


        entity.setSizeFilters(sizeInfoRets);
        return entity;
    }

    private List<VidProductListRespEntity> regularBuildByBdSearch(VidCouponProductListReq requestData, Page page,
                                                                  VidProductEsSearchReq esSearchReq, String lSql) {
        // 查询商品列表
        BeanUtils.copyProperties(requestData, esSearchReq);

        List<String> sortedList;
        List<CouponRuleProductEntity> secProductNames;
        // 查询数据库 对应数据的逻辑  排序+尺码类型
        String yyyyMMdd = new SimpleDateFormat("yyyyMMdd").format(new Date());

        Object o = redisService.get(RedisKeysEnum.COUPON_RULE_PAGE_DATA.join(1, 800, esSearchReq.getProductIds(),
                lSql, requestData.getLPrice(), requestData.getGPrice(), requestData.getBrandIds(), Long.valueOf(yyyyMMdd)));
        if (o == null) {
            com.github.pagehelper.Page<Long> hPage = PageHelper.startPage(1, 800);
            secProductNames = mProductMapper.selectByCouponRuleAndOtherParams(esSearchReq.getProductIds(),
                    lSql, requestData.getLPrice(), requestData.getGPrice(),
                    requestData.getBrandIds(), Long.valueOf(yyyyMMdd));
            redisService.set(RedisKeysEnum.COUPON_RULE_PAGE_DATA.join(1, 800, esSearchReq.getProductIds(),
                    lSql, requestData.getLPrice(), requestData.getGPrice(), requestData.getBrandIds(), Long.valueOf(yyyyMMdd)),
                    JSONObject.toJSONString(secProductNames), 86400);
        } else {
            secProductNames = JSONObject.parseArray(o.toString(), CouponRuleProductEntity.class);
        }


        if (CollectionUtils.isEmpty(secProductNames)) {
            return Collections.emptyList();
        }

        sortedList = secProductNames.stream().map(CouponRuleProductEntity::getOrigName).collect(Collectors.toList());
        // 如果没有尺码和颜色的筛选条件 || 查询数据为空
        if ((CollectionUtils.isEmpty(requestData.getSizeNos())
                && CollectionUtils.isEmpty(requestData.getSizeClassType())
                && CollectionUtils.isEmpty(requestData.getCodes())
                && CollectionUtils.isEmpty(requestData.getStyleCodes()))
                || CollectionUtils.isEmpty(sortedList)) {

            List<VidProductListRespEntity> entities = buildComprehensiveRetForBdSearch(new ArrayList<>(), sortedList, esSearchReq,
                    requestData.getSearchWord(), page);
            return entities;
        }

        // 查询对应风格和颜色、尺码
        log.info("1.2.2.2 大数据搜索es过滤尺码、颜色、风格查询");
        List<Long> filterSizeNos = new ArrayList<>();
        List<Long> filterColors = new ArrayList<>();
        List<Long> filterStyles = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(requestData.getSizeNos()) || CollectionUtils.isNotEmpty(requestData.getSizeClassType())) {
            List<ProductInfoEsSearchResp> esSearchResps = productVidService.esInSearchByProductInfo(new ArrayList<>(), requestData.getSizeNos(),
                    EsParamSearchEnum.SIZE_INFO, esSearchReq.getSizeClassType(), sortedList);
            filterSizeNos = esSearchResps.stream().map(ProductInfoEsSearchResp::getId).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(requestData.getCodes())) {
            List<ProductInfoEsSearchResp> esSearchResps = productVidService.esInSearchByProductInfo(new ArrayList<>(), requestData.getCodes(),
                    EsParamSearchEnum.COLOR_CODE, esSearchReq.getSizeClassType(), sortedList);
            filterColors = esSearchResps.stream().map(ProductInfoEsSearchResp::getId).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(requestData.getStyleCodes())) {
            List<ProductInfoEsSearchResp> esSearchResps = productVidService.esInSearchByProductInfo(new ArrayList<>(), requestData.getStyleCodes(),
                    EsParamSearchEnum.STYLE_CODE, esSearchReq.getSizeClassType(), sortedList);
            filterStyles = esSearchResps.stream().map(ProductInfoEsSearchResp::getId).collect(Collectors.toList());
        }

        log.info(" 大数据搜索过滤尺码、颜色、风格查询取交集");
        // 返回信息 取尺码和颜色的交集 对返回数据进行处理
        List<String> sizeNos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(requestData.getSizeClassType())) {
            requestData.getSizeClassType().forEach(v -> {
                sizeNos.add(String.valueOf(v));
            });
        }
        List<Long> retList = productVidService.getRetList(filterSizeNos, filterColors, sizeNos, requestData.getCodes());
        List<Long> retList1 = productVidService.getRetList(retList, filterStyles,
                CollectionUtils.isEmpty(sizeNos) ? requestData.getCodes() : sizeNos,
                requestData.getStyleCodes());


        if (CollectionUtils.isEmpty(retList1)) {
            // 处理搜索词手动分页
            page.setCount(retList1.size());
            page.setPages(retList1.size() % page.getPageSize() == 0 ?
                    retList1.size() / page.getPageSize() :
                    retList1.size() / page.getPageSize() + 1);
            return buildComprehensiveRetForBdSearch(retList1, new ArrayList<>(), esSearchReq, requestData.getSearchWord(), page);
        }


        log.info("1.2.2.4 大数据搜索组装返回参数");
        List<VidProductListRespEntity> entities = buildComprehensiveRetForBdSearch(retList1, new ArrayList<>(), esSearchReq,
                requestData.getSearchWord(), page);
        return entities;
    }

    private List<VidProductListRespEntity> buildComprehensiveRetForBdSearch(List<Long> sortedProList,
                                                                            List<String> sortedNamesList,
                                                                            VidProductEsSearchReq esSearchReq,
                                                                            String searchWord,
                                                                            Page page) {
        // 查询当前上架且可售的数据
        List<VidProductListRespEntity> oldRets = new ArrayList<>();
        if (CollectionUtils.isEmpty(sortedProList) && CollectionUtils.isEmpty(sortedNamesList)) {
            return Collections.emptyList();
        }
        oldRets = searchInVidEs(esSearchReq, sortedProList, sortedNamesList, searchWord, true);


        if (CollectionUtils.isEmpty(esSearchReq.getProductIds()) || CollectionUtils.isEmpty(oldRets)) {
            return Collections.emptyList();
        }

        List<VidProductListRespEntity> ret;
        // 走综合搜索
        if (Objects.equals(esSearchReq.getSorted(), 0)) {
            ret = getComprehensiveOrder(oldRets, esSearchReq.getProductIds());
        }else {
            ret = oldRets;
        }

        // 处理搜索词的分页 手动分页
        page.setCount(ret.size());
        return ret.stream().skip((page.getPageNo() - 1) * page.getPageSize())
                .limit(page.getPageSize()).collect(Collectors.toList());
    }

    @Value("${vid.index}")
    private String vidIndex;

    @Autowired
    private EsUtil esUtil;

    private List<VidProductListRespEntity> searchInVidEs(VidProductEsSearchReq esSearchReq,
                                                         List<Long> productIds,
                                                         List<String> sortedNames,
                                                         String searchWord,
                                                         Boolean isList) {
        List<VidProductListRespEntity> rets = new ArrayList<>();
        SearchRequest request = new SearchRequest();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(esSearchReq.getVid())) {
            queryBuilder.filter(QueryBuilders.termQuery("vid", esSearchReq.getVid()));
        }
        if (CollectionUtils.isNotEmpty(productIds)) {
            queryBuilder.filter(QueryBuilders.termsQuery("product_id", productIds));
        }
        if (CollectionUtils.isNotEmpty(sortedNames)) {
            queryBuilder.filter(QueryBuilders.termsQuery("product_no.keyword", sortedNames));
        }
        queryBuilder.filter(QueryBuilders.termQuery("is_can_sell", ProductStatusEnum.CAN_ALLOW.getCode()));
        queryBuilder.filter(QueryBuilders.termQuery("is_putaway", ProductStatusEnum.CAN_ALLOW.getCode()));

        if (!Objects.equals(esSearchReq.getSorted(), ProductVidSortedEnum.SYNTHESIS.getCode())) {
            ProductVidSortedEnum code = ProductVidSortedEnum.getByCode(esSearchReq.getSorted());
            if (code != null) {
                sourceBuilder.sort(code.getField(), code.getOrder());
            }
        }

        sourceBuilder.query(queryBuilder);
        if (StringUtils.isNotBlank(searchWord) || Objects.equals(esSearchReq.getSorted(), ProductVidSortedEnum.SYNTHESIS.getCode())) {
            sourceBuilder.from(0);
            sourceBuilder.size(CollectionUtils.isEmpty(sortedNames) ? productIds.size() : sortedNames.size());
        } else if (isList) {
            sourceBuilder.from((esSearchReq.getPageNo() - 1) * esSearchReq.getPageSize());
            sourceBuilder.size(esSearchReq.getPageSize());
        }

        request.indices(vidIndex);
        request.source(sourceBuilder);

        // 使用主分片查询
        request.preference("primary");
        try {
            log.info("查询微商城门店商品信息 query = {}", request.source().toString());
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return Collections.emptyList();
            }


            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                VidCidProductEsSearchResp entity = VidCidProductEsSearchResp.fromJson(hit.getSourceAsString(), VidCidProductEsSearchResp.class);
                if (entity != null) {
                    VidProductListRespEntity listRespEntity = new VidProductListRespEntity();
                    listRespEntity.setProductId(entity.getProduct_id());
                    listRespEntity.setIsNew(entity.getIs_new());
                    listRespEntity.setWmGoodId(entity.getMall_id());
                    listRespEntity.setArcBrandId(entity.getBrand_id());
                    rets.add(listRespEntity);
                }
            }
        } catch (IOException e) {
            log.error("==================查询微商城门店商品信息失败：{}", e);
        }

        return rets.stream().distinct().collect(Collectors.toList());
    }

    private List<VidProductListRespEntity> getComprehensiveOrder(List<VidProductListRespEntity> oldRet, List<Long> productIds) {
        List<VidProductListRespEntity> ret = new ArrayList<>();
        HashMap<Long, VidProductListRespEntity> map = oldRet.stream().collect(HashMap::new, (k, v) -> k.put(v.getProductId(), v), HashMap::putAll);
        if (MapUtils.isEmpty(map)) {
            return ret;
        }
        productIds.forEach(v -> {
            VidProductListRespEntity oldEntity = map.get(v);
            if (oldEntity == null) {
                return;
            }
            VidProductListRespEntity newEntity = new VidProductListRespEntity();
            BeanUtils.copyProperties(oldEntity, newEntity);
            ret.add(newEntity);
        });
        return ret;
    }
}
