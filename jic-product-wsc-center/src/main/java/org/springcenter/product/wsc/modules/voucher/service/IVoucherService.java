package org.springcenter.product.wsc.modules.voucher.service;

import org.springcenter.product.wsc.controller.req.AvailableVouListReq;
import org.springcenter.product.wsc.controller.req.ExchangeVouReq;
import org.springcenter.product.wsc.modules.voucher.entity.VoucherColumnEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/18 13:29
 * @description
 */
public interface IVoucherService {


     List<VoucherColumnEntity> availableVouList(AvailableVouListReq availableVouListReq);


     String exchangeVou(ExchangeVouReq exchangeVouReq);
}
