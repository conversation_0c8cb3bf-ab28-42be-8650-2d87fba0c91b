package org.springcenter.product.wsc.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2025/2/25 17:49
 */
@Data
@TableName(value = "JIC_VID_BD_INFO")
public class JicVidBdInfo {

    @TableId(value = "ID")
    private String id;

    @TableField(value = "VID")
    private String vid;

    @TableField(value = "BRAND_ID")
    @ApiModelProperty(value = "品牌id")
    private String brandId;

    @TableField(value = "BD_HEAD")
    @ApiModelProperty(value = "头部信息")
    private String bdHead;


    @TableField(value = "BD_PROD_ORDER")
    @ApiModelProperty(value = "排序信息")
    private String bdProdOrder;

    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    @TableField(value = "PRO_BRAND_ID")
    private Long proBrandId;
}
