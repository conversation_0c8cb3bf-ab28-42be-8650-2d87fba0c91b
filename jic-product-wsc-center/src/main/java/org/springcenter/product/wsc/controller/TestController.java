package org.springcenter.product.wsc.controller;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.wsc.controller.req.VidProductListFilterReq;
import org.springcenter.product.wsc.modules.product.service.IAsyncCheckRedisService;
import org.springcenter.product.wsc.modules.product.service.IProductDetailService;
import org.springcenter.product.wsc.modules.product.service.IProductVidService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/3/21 17:58
 */
@Slf4j
@RestController
@RequestMapping("/product/test/api")
@Api(value = "TestApi",tags = "测试接口")
public class TestController {

    @Autowired
    private IProductDetailService productDetailService;

    @Autowired
    private IProductVidService productVidService;

    @Autowired
    private IAsyncCheckRedisService asyncCheckRedisService;

    // 尺码
    @ApiOperation(value = "根据伯俊商品id获取尺码")
    @RequestMapping(value = "/getSizeInfoByProductId",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<List<String>>> getSizeInfoByProductId(@RequestBody CommonRequest<Long> request){
        return ResponseResult.success(productDetailService.getSizeInfoByProductId(request.getRequestData()));
    }


    @ApiOperation(value = "处理vid-cid redis数据")
    @RequestMapping(value = "/dealVidCidRedis",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<Boolean> dealVidCidRedis(@RequestBody CommonRequest<VidProductListFilterReq> request){
        asyncCheckRedisService.checkVidCidRedisSizeNums(request.getRequestData().getVid(),
                request.getRequestData().getCids(), request.getRequestData().getTraceId());
        return ResponseResult.success(true);
    }

    @ApiOperation(value = "处理vid redis数据")
    @RequestMapping(value = "/dealVidRedis",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<Boolean> dealVidRedis(@RequestBody CommonRequest<VidProductListFilterReq> request){
        asyncCheckRedisService.checkVidRedisSizeNums(request.getRequestData().getVid(),
                request.getRequestData().getTraceId());
        return ResponseResult.success(true);
    }


}
