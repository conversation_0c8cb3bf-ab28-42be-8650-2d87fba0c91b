package org.springcenter.product.wsc.modules.product.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.wsc.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/2/24 10:57
 */
public interface IProductBdListService {

    /**
     * 根据vid获取榜单头部
     * @param req 入参
     * @return 返回
     */
    VidBdHeadResp getBdHeadInfo(VidBdHeadReq req);


    /**
     * 根据vid和小类id获取榜单商品列表
     * @param req 入参
     * @return 返回
     */
    List<VidProductSalesResp> getBdListByVidSmallCateId(VidBdCateListReq req, Page page);

    /**
     * 获取门店下商品的各个排序
     * @param req 入参
     * @return 返回
     */
    List<VidProdOrderInfoResp> getBatchProdOrderInfo(VidProdOrderInfoReq req);


    /**
     * 获取商品价格
     * @param req 入参
     * @return 返回
     */
    List<VidBdPriceResp> getBatchProdPrice(VidBdPriceReq req);

    /**
     * 榜单老接口
     * @param requestData 入参
     * @return 返回
     */
    List<VidProdOrderInfoResp> getBatchProdOrderInfoOld(VidProdOrderInfoOldReq requestData);
}
