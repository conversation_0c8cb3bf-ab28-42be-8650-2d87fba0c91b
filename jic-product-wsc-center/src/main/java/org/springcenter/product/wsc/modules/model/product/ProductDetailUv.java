package org.springcenter.product.wsc.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName(value = "PRODUCT_DETAIL_UV")
public class ProductDetailUv {
    @TableId(value = "ID")
    @ApiModelProperty(value = "微盟商品ID")
    private String id;

    @TableField(value = "UV")
    @ApiModelProperty(value = "浏览UV")
    private Integer uv;
}
