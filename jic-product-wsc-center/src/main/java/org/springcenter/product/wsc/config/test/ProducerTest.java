package org.springcenter.product.wsc.config.test;

import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.remoting.common.RemotingHelper;

/**
 * <AUTHOR>
 * @Date:2024/8/13 9:41
 */
public class ProducerTest {
    public static void main(String[] args) throws Exception {
        // 创建一个生产者，并指定一个组名
        DefaultMQProducer producer = new DefaultMQProducer("CID_VID_CLASSIFY_DEV_INFO_CONSUMER");
        // 指定NameServer地址
        producer.setNamesrvAddr("192.168.0.45:9876");
        // 启动生产者
        producer.start();

        try {
            // 创建一个消息，并指定Topic，Tag和消息体
            Message msg = new Message("TopicTest" /* Topic */,
                    "JIC_PRODUCT_PUTAWAY" /* Tag */,
                    ("{\"columns\":[{\"check\":true,\"column\":{\"name\":\"WEID\",\"type\":3},\"value\":12},{\"check\":true,\"column\":{\"name\":\"CREATE_TIME\",\"type\":93},\"value\":1723442068000},{\"check\":true,\"column\":{\"name\":\"MALL_ID\",\"type\":3},\"value\":130143288757671},{\"check\":true,\"column\":{\"name\":\"CLASSIFY_LEVEL\",\"type\":3},\"value\":2},{\"check\":true,\"column\":{\"name\":\"P_CLASSIFY_ID\",\"type\":3},\"value\":13917795757671},{\"check\":true,\"column\":{\"name\":\"CLASSIFY_ID\",\"type\":3},\"value\":13917217757671},{\"check\":true,\"column\":{\"name\":\"P_CLASSIFY_NAME\",\"type\":12},\"value\":\"测试千人千面\"},{\"check\":true,\"column\":{\"name\":\"ONE_LEVEL_ID\",\"type\":3},\"value\":13917795757671},{\"check\":true,\"column\":{\"name\":\"CLASSIFY_NAME\",\"type\":12},\"value\":\"千人千面1\"},{\"check\":true,\"column\":{\"name\":\"UPDATE_TIME\",\"type\":93},\"value\":1723470647000}],\"discardType\":\"NONE\",\"opType\":\"U\",\"primaryKeys\":[{\"check\":true,\"column\":{\"name\":\"ID\",\"type\":3},\"value\":136628050}],\"rowId\":{\"check\":true,\"column\":{\"name\":\"ROWID\",\"type\":-8},\"value\":{\"bytes\":\"QUFDZStTQUJJQUFLbVNWQUFF\",\"length\":18,\"stream\":{}}},\"schemaName\":\"USERWX\",\"tableName\":\"JIC_PRODUCT_MALL_CLASSIFY\"}").getBytes(RemotingHelper.DEFAULT_CHARSET) /* Message body */
            );
            // 发送消息
            SendResult sendResult = producer.send(msg);
            // 打印发送结果
            System.out.printf("%s%n", sendResult);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭生产者
            producer.shutdown();
        }
    }
}
