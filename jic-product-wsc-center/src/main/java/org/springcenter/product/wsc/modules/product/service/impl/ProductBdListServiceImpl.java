package org.springcenter.product.wsc.modules.product.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.google.gson.Gson;
import com.jnby.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.dto.wsc.*;
import org.springcenter.product.wsc.modules.facade.service.IJicService;
import org.springcenter.product.wsc.modules.mapper.product.JicVidBdInfoMapper;
import org.springcenter.product.wsc.modules.mapper.product.JicVidBdListInfoMapper;
import org.springcenter.product.wsc.modules.model.product.JicVidBdInfo;
import org.springcenter.product.wsc.modules.model.product.JicVidBdListInfo;
import org.springcenter.product.wsc.modules.product.service.IProductBdListService;
import org.springcenter.product.wsc.modules.util.ConstantUtil;
import org.springcenter.product.wsc.modules.util.RedisService;
import org.springcenter.product.wsc.modules.voucher.entity.WmGoodInfoReq;
import org.springcenter.product.wsc.modules.voucher.entity.WmGoodInfoResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2025/2/24 10:57
 */
@Service
@Slf4j
@RefreshScope
public class ProductBdListServiceImpl implements IProductBdListService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private IJicService jicService;

    @Autowired
    private JicVidBdListInfoMapper jicVidBdListInfoMapper;

    @Autowired
    private JicVidBdInfoMapper jicVidBdInfoMapper;

    @Value("${switch.bd}")
    private Boolean switchBd;

    @Value("${filter.brand.id}")
    private String  filterBrandId;



    @Value("${vid.brand.bd.index}")
    private String vidBrandBdIndex;

    @Override
    public VidBdHeadResp getBdHeadInfo(VidBdHeadReq req) {
        List<String> brandIds = Arrays.stream(filterBrandId.split(",")).collect(Collectors.toList());
        if (!switchBd || brandIds.contains(req.getWeId())) {
            return null;
        }

        Map<String, String> brandMap = JSONObject.parseObject(vidBrandBdIndex, Map.class);
        String proBrand = brandMap.get(req.getWeId());
        if (StringUtils.isNotBlank(proBrand)) {
            req.setProBrandId(Long.valueOf(proBrand));
        }
        String key = ConstantUtil.STORE_BD_CATE_HEAD + req.getWeId() + ConstantUtil.COLON + req.getVid()+ ConstantUtil.COLON + req.getProBrandId();
        log.info("getBdHeadInfo 的 key{}",  key);
        Object o = redisService.get(key);
        VidBdHeadResp resp = null;
        String info = null;
        if (o == null) {
            JicVidBdInfo jicVidBdInfo = jicVidBdInfoMapper.selectByVidAndBrandId(req.getVid(), req.getWeId(), req.getProBrandId());
            if (jicVidBdInfo == null) {
                return null;
            }
            info = jicVidBdInfo.getBdHead();
            if (StringUtils.isNotBlank(info)) {
                redisService.set(key, info, 60 * 60);
            }
        } else {
            info = o.toString();
        }

        try {
            Gson gson = new Gson();
            resp = gson.fromJson(info, VidBdHeadResp.class);
        } catch (Exception e) {
            // 处理异常，例如记录日志或抛出自定义异常
            log.error("解析响应失败", e);
        }
        return resp;
    }

    @Override
    public List<VidProductSalesResp> getBdListByVidSmallCateId(VidBdCateListReq req, Page page) {
        List<String> brandIds = Arrays.stream(filterBrandId.split(",")).collect(Collectors.toList());
        if (!switchBd || brandIds.contains(req.getWeId())) {
            return null;
        }

        Map<String, String> brandMap = JSONObject.parseObject(vidBrandBdIndex, Map.class);
        String proBrand = brandMap.get(req.getWeId());
        if (StringUtils.isNotBlank(proBrand)) {
            req.setProBrandId(Long.valueOf(proBrand));
        }
        String key = ConstantUtil.STORE_SMALL_CATE_LIST + req.getWeId()
                + ConstantUtil.COLON + req.getVid() + ConstantUtil.TRANSVERSE_LINE
                + req.getProBrandId() + ConstantUtil.TRANSVERSE_LINE +
                req.getBigCateId() + ConstantUtil.TRANSVERSE_LINE + req.getSmallCateId();
        log.info("getBdListByVidSmallCateId 的 key{}",  key);

        Object o = redisService.get(key);
        List<VidProductSalesResp> resp = new ArrayList<>();
        VidBdCateListResp json = new VidBdCateListResp();
        String jsonStr = "";

        if (o == null) {
            JicVidBdListInfo jicVidBdListInfo = jicVidBdListInfoMapper
                    .selectByCateAndVidAndBrandId(req.getSmallCateId(), req.getBigCateId(), req.getVid(),
                                                    req.getWeId(), req.getProBrandId());
            if (jicVidBdListInfo == null) {
                return null;
            }
            jsonStr = jicVidBdListInfo.getBdList();
            if (StringUtils.isNotBlank(jsonStr)) {
                redisService.set(key, jsonStr, 60 * 60);
            }
        } else {
            jsonStr = o.toString();
        }

        try {
            Gson gson = new Gson();
            json = gson.fromJson(jsonStr, VidBdCateListResp.class);
        } catch (Exception e) {
            // 处理异常，例如记录日志或抛出自定义异常
            log.error("解析响应失败", e);
        }
        resp = json.getProductList();

        page.setCount(resp.size());
        page.setPages(resp.size() / page.getPageSize() == 0 ?
                resp.size() / page.getPageSize() : resp.size() / page.getPageSize() + 1);
        List<VidProductSalesResp> rets = resp.stream()
                .skip((page.getPageNo() - 1) * page.getPageSize()).limit(page.getPageSize())
                .collect(Collectors.toList());
        return rets;
    }

    @Override
    public List<VidProdOrderInfoResp> getBatchProdOrderInfo(VidProdOrderInfoReq req) {
        List<String> brandIds = Arrays.stream(filterBrandId.split(",")).collect(Collectors.toList());
        if (!switchBd || brandIds.contains(req.getWeId())) {
            return null;
        }

        Map<String, String> brandMap = JSONObject.parseObject(vidBrandBdIndex, Map.class);
        String proBrand = brandMap.get(req.getWeId());
        if (StringUtils.isNotBlank(proBrand)) {
            req.getData().forEach(v -> v.setProBrandId(Long.valueOf(proBrand)));
        }

        List<Long> proBrandIds = req.getData().stream().map(VidProdOrderInfoReq.VidProdInfoData::getProBrandId).distinct()
                .collect(Collectors.toList());
        HashMap map = new HashMap();
        List<VidProdOrderInfoResp> resp = new ArrayList<>();
        proBrandIds.forEach(proBrandId -> {
            String key =  ConstantUtil.STORE_VID_CATE_ORDER + req.getWeId()
                + ConstantUtil.COLON + req.getVid() + ConstantUtil.COLON + proBrandId + ConstantUtil.COLON;
            Object o = redisService.get(key);
            String json = "";
            if (o == null) {
                JicVidBdInfo jicVidBdInfo = jicVidBdInfoMapper.selectByVidAndBrandId(req.getVid(), req.getWeId(), proBrandId);
                if (jicVidBdInfo == null) {
                    return;
                }
                json = jicVidBdInfo.getBdProdOrder();
                if (StringUtils.isNotBlank(json)) {
                    redisService.set(key, json, 60 * 60);
                }
            } else {
                json = o.toString();
            }

            List<String> infos = JSONObject.parseArray(json, String.class);
            for (String info : infos) {
                String[] split = StringUtils.split(info, ConstantUtil.TRANSVERSE_LINE);
                map.put(split[3], split[0] + ConstantUtil.TRANSVERSE_LINE + split[1]
                        + ConstantUtil.TRANSVERSE_LINE + split[2]
                        + ConstantUtil.TRANSVERSE_LINE + split[4]);

                //  0：大类id 1：小类id 2：小类名称 3：商品id 4：排序
                //  String value = resp1.getBigClassId() + ConstantUtil.TRANS_LINE +
                //  resp1.getSmallClassId() + ConstantUtil.TRANS_LINE +
                //  resp1.getSmallClassName() + ConstantUtil.TRANS_LINE +
                //  vv.getMall_id() + ConstantUtil.TRANS_LINE + i.getAndIncrement();
            }
        });

        req.getData().forEach(x -> {
            VidProdOrderInfoResp resp1 = new VidProdOrderInfoResp();
            resp1.setMallId(x.getMallIds());
            resp1.setVid(req.getVid());
            if (MapUtils.isEmpty(map) || !map.containsKey(x.getMallIds())) {
                return;
            }
            Object o1 = map.get(x.getMallIds());
            if (o1 == null) {
                return;
            }
            // 大类id 小类id 小类名称 排序
            String[] split = StringUtils.split((String) o1, ConstantUtil.TRANSVERSE_LINE);
            resp1.setBigCateId(split[0]);
            resp1.setSmallCateId(split[1]);
            resp1.setSmallCateName(split[2]);
            resp1.setOrderNum(split[3]);
            resp1.setProBrandId(x.getProBrandId());
            resp.add(resp1);
        });
        return resp;
    }

    @Override
    public List<VidBdPriceResp> getBatchProdPrice(VidBdPriceReq req) {
        List<String> brandIds = Arrays.stream(filterBrandId.split(",")).collect(Collectors.toList());
        if (!switchBd || brandIds.contains(req.getWeId())) {
            return null;
        }
        WmGoodInfoReq infoReq = new WmGoodInfoReq();
        infoReq.setWid(Long.valueOf(req.getWid()));
        infoReq.setBrandId(req.getWeId());
        infoReq.setGoodsIdList(req.getMallId());
        infoReq.setVid(Long.valueOf(req.getVid()));
        infoReq.setRequestId(UUID.randomUUID().toString());
        List<WmGoodInfoResp> infoList = jicService.getGoodsInfoList(infoReq);

        if (CollectionUtils.isEmpty(infoList)) {
            return null;
        }
        List<VidBdPriceResp> rets = new ArrayList<>();
        infoList.forEach(v -> {
            VidBdPriceResp resp = new VidBdPriceResp();
            WmGoodInfoResp.GoodsPriceInfo goodsPrice = v.getGoodsPrice();
            if (Objects.nonNull(goodsPrice)) {
                // 价格： 主价格取值按照规则有限价: 最小活动价>最小会员价>最小销售价
                resp.setPrice(getPrice(goodsPrice));
                // 划线价 : 根据b端配置的划线价规则,判断取销售价或者市场价，目前后台配置的都是市场价
                resp.setOriPrice(Optional.ofNullable(goodsPrice.getMinMarketPrice()).orElse(goodsPrice.getMinSalePrice()));
                resp.setMallId(Objects.toString(v.getGoodsId()));
                rets.add(resp);
            }
        });
        return rets;
    }

    @Override
    public List<VidProdOrderInfoResp> getBatchProdOrderInfoOld(VidProdOrderInfoOldReq req) {
        List<String> brandIds = Arrays.stream(filterBrandId.split(",")).collect(Collectors.toList());
        if (!switchBd || brandIds.contains(req.getWeId())) {
            return null;
        }
        Object o = redisService.get(ConstantUtil.STORE_VID_CATE_ORDER + req.getWeId()
                + ConstantUtil.COLON + req.getVid() + ConstantUtil.COLON);
        List<VidProdOrderInfoResp> resp = new ArrayList<>();
        HashMap map = new HashMap();
        String json = "";
        if (o == null) {
            JicVidBdInfo jicVidBdInfo = jicVidBdInfoMapper.selectByVidAndBrandId(req.getVid(), req.getWeId(), null);
            if (jicVidBdInfo == null) {
                return null;
            }
            json = jicVidBdInfo.getBdProdOrder();
            if (StringUtils.isNotBlank(json)) {
                redisService.set(ConstantUtil.STORE_VID_CATE_ORDER + req.getWeId()
                        + ConstantUtil.COLON + req.getVid() + ConstantUtil.COLON, json, 60 * 60);
            }
        } else {
            json = o.toString();
        }

        List<String> infos = JSONObject.parseArray(json, String.class);
        for (String info : infos) {
            String[] split = StringUtils.split(info, ConstantUtil.TRANSVERSE_LINE);
            map.put(split[3], split[0] + ConstantUtil.TRANSVERSE_LINE + split[1]
                    + ConstantUtil.TRANSVERSE_LINE + split[2]
                    + ConstantUtil.TRANSVERSE_LINE + split[4]);

            //  0：大类id 1：小类id 2：小类名称 3：商品id 4：排序
            //  String value = resp1.getBigClassId() + ConstantUtil.TRANS_LINE +
            //  resp1.getSmallClassId() + ConstantUtil.TRANS_LINE +
            //  resp1.getSmallClassName() + ConstantUtil.TRANS_LINE +
            //  vv.getMall_id() + ConstantUtil.TRANS_LINE + i.getAndIncrement();
        }
        req.getMallIds().stream().forEach(x -> {
            VidProdOrderInfoResp resp1 = new VidProdOrderInfoResp();
            resp1.setMallId(x);
            resp1.setVid(req.getVid());
            if (MapUtils.isEmpty(map) || !map.containsKey(x)) {
                return;
            }
            Object o1 = map.get(x);
            if (o1 == null) {
                return;
            }
            // 大类id 小类id 小类名称 排序
            String[] split = StringUtils.split((String) o1, ConstantUtil.TRANSVERSE_LINE);
            resp1.setBigCateId(split[0]);
            resp1.setSmallCateId(split[1]);
            resp1.setSmallCateName(split[2]);
            resp1.setOrderNum(split[3]);
            resp.add(resp1);
        });
        return resp;
    }


    private BigDecimal getPrice(WmGoodInfoResp.GoodsPriceInfo goodsPrice) {
        // 主价格取值按照规则有限价: 最小活动价>最小会员价>最小销售价
        WmGoodInfoResp.ActivityPriceInfo activityPrice = goodsPrice.getActivityPrice();
        if (Objects.nonNull(activityPrice) && Objects.nonNull(activityPrice.getMinActivityPrice())) {
            return activityPrice.getMinActivityPrice();
        }
        WmGoodInfoResp.MemberPriceInfo memberPrice = goodsPrice.getMemberPrice();
        if (Objects.nonNull(memberPrice) && Objects.nonNull(memberPrice.getMinActivityPrice())) {
            return memberPrice.getMinActivityPrice();
        }
        return goodsPrice.getMinSalePrice();
    }


}
