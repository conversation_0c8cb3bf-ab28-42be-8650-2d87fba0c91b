package org.springcenter.product.wsc.modules.voucher.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.core.tool.utils.DateUtil;
import org.springcenter.paycenter.api.IPayCenterApi;
import org.springcenter.product.wsc.controller.req.AvailableVouListReq;
import org.springcenter.product.wsc.controller.req.ExchangeVouReq;
import org.springcenter.product.wsc.modules.facade.service.IJicService;
import org.springcenter.product.wsc.modules.facade.service.hystrix.JicExchangeVouCommand;
import org.springcenter.product.wsc.modules.facade.service.hystrix.JicVoucherAvailableCommand;
import org.springcenter.product.wsc.modules.mapper.bojun.CheckVouMapper;
import org.springcenter.product.wsc.modules.model.wx.VoucherBase;
import org.springcenter.product.wsc.modules.model.wx.WxScruleProduct;
import org.springcenter.product.wsc.modules.voucher.entity.*;
import org.springcenter.product.wsc.modules.voucher.enums.VoucherTypeEnum;
import org.springcenter.product.wsc.modules.voucher.service.IVoucherBaseService;
import org.springcenter.product.wsc.modules.voucher.service.IVoucherService;
import org.springcenter.product.wsc.modules.voucher.service.IWxScruleProductService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/18 13:29
 * @description
 */
@Service
@Slf4j
@RefreshScope
public class VoucherServiceImpl implements IVoucherService {

     @Resource
     private IJicService jicService;

     @Resource
     private IWxScruleProductService iWxScruleProductService;


     @Resource
     private CheckVouMapper checkVouMapper;


     @Resource
     private IVoucherBaseService voucherBaseService;

     @Value("${available.vou.list.switch.flag}")
     private String availableVouListSwitchFlag;

     @Override
     public List<VoucherColumnEntity> availableVouList(AvailableVouListReq availableVouListReq) {
          if (Objects.equals(availableVouListSwitchFlag, "0")) {
               log.info("availableVouList 开关关闭， 不走逻辑");
               return Collections.emptyList();
          }
          List<VoucherColumnEntity> list = new ArrayList<>();
          // 获取可用券
         // VoucherAvailableReqEntity vouAvaReq = new VoucherAvailableReqEntity();
          JicVoucherAvailableCommand jicVoucherAvailableCommand = new JicVoucherAvailableCommand();
          jicVoucherAvailableCommand.setJicService(jicService);
          jicVoucherAvailableCommand.setParam(availableVouListReq.getAvaReq());
          Future<VoucherAvailableRespEntity> voucherAvailableRespFuture = jicVoucherAvailableCommand.queue();

          JicExchangeVouCommand jicExchangeVouCommand = new JicExchangeVouCommand();
          jicExchangeVouCommand.setJicService(jicService);
          jicExchangeVouCommand.setParam(availableVouListReq.getExchangeVouListReq());
          Future<List<ExchangeVouListRespEntity>> exchangeVouListFuture = jicExchangeVouCommand.queue();

          VoucherAvailableRespEntity voucherAvailableRespEntity = null;
          List<ExchangeVouListRespEntity> exchangeVouList = null;
          try {
               voucherAvailableRespEntity = voucherAvailableRespFuture.get();
               exchangeVouList = exchangeVouListFuture.get();
          } catch (InterruptedException e) {
               log.error("availableVouList jicVoucherAvailableCommand.queue() InterruptedException", e);
          } catch (ExecutionException e) {
               log.error("availableVouList jicVoucherAvailableCommand.queue() ExecutionException", e);
          }
          VoucherAvailableReqEntity.SkuInfo skuInfo = availableVouListReq.getAvaReq().getGoodsInfos().get(0).getGoods().get(0).getSkus().get(0);
          BigDecimal price = new BigDecimal(skuInfo.getPrice());
          if(CollectionUtils.isNotEmpty(voucherAvailableRespEntity.getAvailableVouchers())){
               log.info("availableVouList 获取可用券{}", voucherAvailableRespEntity.getAvailableVouchers().size());
               // 去重并计算优惠力度
               List<VoucherColumnEntity> voucherColumnEntities = sameRuleVou(voucherAvailableRespEntity.getAvailableVouchers(),price);
               log.info("availableVouList 去重并计算优惠力度{}", voucherColumnEntities.size());
               list.addAll(voucherColumnEntities);
          }

          // 获取可兑换券
          if(CollectionUtils.isNotEmpty(exchangeVouList)){
               List<VoucherColumnEntity> listByExchangeVou = getListByExchangeVou(exchangeVouList, skuInfo,availableVouListReq.getExchangeVouListReq().getSpuId());
               list.addAll(listByExchangeVou);
          }

          // 按照优惠力度从大到小排序
          return list.stream()
                  .sorted(Comparator.comparing(VoucherColumnEntity :: getPrePower).reversed())
                  .collect(Collectors.toList());
     }

     @Override
     public String exchangeVou(ExchangeVouReq exchangeVouReq) {
          SendVouByPointsEntity sendVouByPointsEntity = new SendVouByPointsEntity();
          BeanUtils.copyProperties(exchangeVouReq,sendVouByPointsEntity);
          return jicService.sendVouByPoints(sendVouByPointsEntity);
     }

     private List<VoucherColumnEntity> sameRuleVou(List<VoucherAvailableEntity> vouAvaList, BigDecimal priceActual){
          List<String> voucherNos = vouAvaList.stream().map(VoucherAvailableEntity::getVouchersNo).collect(Collectors.toList());
          LambdaQueryWrapper<VoucherBase> queryWrapper = new LambdaQueryWrapper<>();
          queryWrapper.in(VoucherBase :: getLinksource,voucherNos);
          queryWrapper.select(VoucherBase :: getLinksource,VoucherBase :: getInsertdate);
          List<VoucherBase> list = voucherBaseService.list(queryWrapper);
          log.info("VoucherBase中获取券{}", JSON.toJSONString(list));
          Map<String, List<VoucherBase>> map = list.stream().collect(Collectors.groupingBy(VoucherBase::getLinksource));
          // 去重并取出有效期最小的
          return vouAvaList.stream()
                  .collect(Collectors.toMap(
                          VoucherAvailableEntity::getRuleId,
                          Function.identity(),
                          (vou1, vou2) -> Long.parseLong(vou1.getValiddate()) < Long.parseLong(vou2.getValiddate()) ? vou1 : vou2
                  ))
                  .values()
                  .stream().map(e -> {
                       VoucherColumnEntity voucherColumnEntity = new VoucherColumnEntity();
                       BeanUtils.copyProperties(e,voucherColumnEntity);
                       voucherColumnEntity.setAwardId(e.getAwarid());
                       List<VoucherBase> voucherBases = map.get(e.getVouchersNo());
                       // 到账时间
                       voucherColumnEntity.setInsertdate(DateUtil.format(voucherBases.get(0).getInsertdate(), DateUtil.PATTERN_DATE_MINI));
                       voucherColumnEntity.setBusinessType(1);
                       BigDecimal prePower;
                       if(VoucherTypeEnum.CASH_COUPON.getAvaType().equals(e.getVouType())){
                            // 如果金额门槛为0，取商品金额
                            // 如果都为0，直接取面额
                            BigDecimal vouJeLimit = Optional.ofNullable(e.getJeLimit()).map(BigDecimal :: new).orElse(BigDecimal.ZERO);
                            if(BigDecimal.ZERO.compareTo(vouJeLimit) >= 0 && BigDecimal.ZERO.compareTo(priceActual) >= 0){
                                 prePower = e.getAmt();
                            } else{
                                 BigDecimal limit;
                                 if(BigDecimal.ZERO.compareTo(vouJeLimit) < 0){
                                      limit = vouJeLimit;
                                 }else{
                                      limit = priceActual;
                                 }
                                 prePower = e.getAmt().divide(limit,5, RoundingMode.UP);
                            }
                            voucherColumnEntity.setVouType(VoucherTypeEnum.CASH_COUPON.getCode());
                       }else{
                            prePower = BigDecimal.ONE.subtract(e.getDis());
                            voucherColumnEntity.setVouType(VoucherTypeEnum.DIS_COUPON.getCode());
                       }
                       voucherColumnEntity.setPrePower(prePower);
                       return voucherColumnEntity;
                  }).collect(Collectors.toList());
     }


     // 取出包含可用券的券包
     private List<VoucherColumnEntity> getListByExchangeVou(List<ExchangeVouListRespEntity> exchangeVouList,VoucherAvailableReqEntity.SkuInfo goodsInfo,Long spuId){
          BigDecimal price = new BigDecimal(goodsInfo.getPrice());
          if(BigDecimal.ZERO.compareTo(price) >= 0){
               return new ArrayList<>();
          }
          List<Long> ruleIds = new ArrayList<>();
          exchangeVouList.forEach(e -> {
               List<Long> rules = e.getItems().stream().map(ExchangeVouListRespEntity.ExchangeVou::getRuleId).collect(Collectors.toList());
               ruleIds.addAll(rules);
          });
          // 去重
          List<Long> disRuleIds = ruleIds.stream().distinct().collect(Collectors.toList());
          List<WxScruleProduct> wxScruleProductList = iWxScruleProductService.listByIds(disRuleIds);
          Map<Long, List<WxScruleProduct>> map = wxScruleProductList.stream().collect(Collectors.groupingBy(WxScruleProduct::getRuleid));
          List<VoucherColumnEntity> list = new ArrayList<>();
          for (ExchangeVouListRespEntity exchangeVouListRespEntity : exchangeVouList) {
               List<ExchangeVouListRespEntity.ExchangeVou> vouList = exchangeVouListRespEntity.getItems();
               List<PointsVouEntity> pointsVouEntityList = new ArrayList<>();
               boolean flag = false;
               for (ExchangeVouListRespEntity.ExchangeVou exchangeVou : vouList) {
                    PointsVouEntity pointsVouEntity = new PointsVouEntity();
                    pointsVouEntity.setVouName(exchangeVou.getVoucherName());
                    pointsVouEntity.setVouType(exchangeVou.getVoucherType());
                    pointsVouEntity.setRuleId(exchangeVou.getRuleId());
                    pointsVouEntity.setAwardId(exchangeVou.getAwardId());
                    pointsVouEntity.setPrePower(getPrePower(exchangeVou, price));
                    List<WxScruleProduct> wxScruleProduct = map.get(exchangeVou.getRuleId());
                    // 金额门槛
                   /* if (price.compareTo(Optional.ofNullable(exchangeVou.getAmountLimit()).orElse(BigDecimal.ZERO)) < 0) {
                         // 金额不满足
                         pointsVouEntity.setFlag(false);
                         pointsVouEntityList.add(pointsVouEntity);
                         continue;
                    }*/
                    // 商品限制
                    if (CollectionUtils.isNotEmpty(wxScruleProduct) && StringUtils.isNotBlank(wxScruleProduct.get(0).getGoodsFilter())) {
                         // 获取商品
                         List<Long> okSpu = checkVouMapper.checkProduct(Collections.singletonList(spuId), wxScruleProduct.get(0).getGoodsFilter());
                         if (CollectionUtils.isEmpty(okSpu)) {
                              pointsVouEntity.setFlag(false);
                              pointsVouEntityList.add(pointsVouEntity);
                              // 商品不满足
                              continue;
                         }
                    }
                    pointsVouEntity.setFlag(true);
                    pointsVouEntityList.add(pointsVouEntity);
                    flag = true;
               }
               if (!flag) {
                    continue;
               }
               List<PointsVouEntity> okSortList = pointsVouEntityList.stream()
                       .filter(PointsVouEntity::isFlag)
                       .sorted(Comparator.comparing(PointsVouEntity::getPrePower).reversed())
                       .collect(Collectors.toList());
               // 可用的优惠力度最大的券
               PointsVouEntity pointsVouEntity = okSortList.get(0);
               ExchangeVouListRespEntity.ExchangeVou exVou = vouList.stream().filter(e -> pointsVouEntity.getRuleId().equals(e.getRuleId())).findFirst().orElse(null);
               if (ObjectUtils.isEmpty(exVou)) {
                    continue;
               }
               VoucherColumnEntity voucherColumnEntity = new VoucherColumnEntity();
               voucherColumnEntity.setRuleId(exVou.getRuleId());
               voucherColumnEntity.setAwardId(exVou.getAwardId());
               voucherColumnEntity.setVouName(exVou.getVoucherName());
               voucherColumnEntity.setVouType(exVou.getVoucherType());
               voucherColumnEntity.setJeLimit(Optional.ofNullable(exVou.getAmountLimit()).orElse(BigDecimal.ZERO).toString());
               voucherColumnEntity.setQtyLimit(exVou.getQtyLimit().toString());
               voucherColumnEntity.setZkLimit(Optional.ofNullable(exVou.getDiscountLimit()).orElse(BigDecimal.ZERO).toString());
               voucherColumnEntity.setAmt(exVou.getAmount());
               voucherColumnEntity.setDis(exVou.getDiscount());
               voucherColumnEntity.setBusinessType(2);
               voucherColumnEntity.setNeedPoints(new BigDecimal(exchangeVouListRespEntity.getSubIntegral()));
               voucherColumnEntity.setIfVouPackage(pointsVouEntityList.size() > 1);
               voucherColumnEntity.setPointsVouEntityList(pointsVouEntityList);
               voucherColumnEntity.setRequestId(exchangeVouListRespEntity.getRequestId());
               voucherColumnEntity.setPrePower(pointsVouEntity.getPrePower());
               voucherColumnEntity.setPp(exVou.getUseBrandNames());
               voucherColumnEntity.setProductImg(exchangeVouListRespEntity.getProductImg());
               voucherColumnEntity.setProductName(exchangeVouListRespEntity.getProductName());
               list.add(voucherColumnEntity);
          }
          return list;
     }


     private BigDecimal getPrePower(ExchangeVouListRespEntity.ExchangeVou exchangeVou,BigDecimal priceActual){
          BigDecimal prePower;
          if(VoucherTypeEnum.CASH_COUPON.getCode().equals(exchangeVou.getVoucherType())){
               // 如果金额门槛为0，取商品金额
               // 如果都为0，直接取面额
               BigDecimal vouJeLimit = Optional.ofNullable(exchangeVou.getAmountLimit()).orElse(BigDecimal.ZERO);
               if(BigDecimal.ZERO.compareTo(vouJeLimit) >= 0 && BigDecimal.ZERO.compareTo(priceActual) >= 0){
                    prePower = exchangeVou.getAmount();
               } else{
                    BigDecimal limit;
                    if(BigDecimal.ZERO.compareTo(vouJeLimit) < 0){
                         limit = vouJeLimit;
                    }else{
                         limit = priceActual;
                    }
                    prePower = exchangeVou.getAmount().divide(limit,5, RoundingMode.UP);
               }
          }else{
               prePower = BigDecimal.ONE.subtract(Optional.ofNullable(exchangeVou.getDiscount()).orElse(BigDecimal.ONE));
          }
          return prePower;
     }


}
