package org.springcenter.product.wsc.modules.product.entity;

import com.alibaba.fastjson.JSONObject;
import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springcenter.product.api.dto.ProductSpuFabEsResp;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/3/21 15:09
 */
@Data
public class ProductDetailInfoEsResp extends RemotingSerializable implements Serializable {

    @ApiModelProperty(value = "商品id")
    private Long id;

    @ApiModelProperty(value = "款号")
    private String name;

    @ApiModelProperty(value = "版型图")
    private String model_image;

    @ApiModelProperty(value = "是否新款 1是 0不是")
    private Integer is_new;

    @ApiModelProperty(value = "体型")
    private String ti_xing;

    @ApiModelProperty(value = "一级风格标签")
    private String first_style_list;

    @ApiModelProperty(value = "系列标签")
    private String file1name_list;

    @ApiModelProperty(value = "品牌")
    private Integer c_arcbrand_id;

    @ApiModelProperty(value = "品类")
    private Integer small_class_id;

    private List<String> styleList;

    private List<String> file1nameList;

    public void buildStyleList(){
        if (this.getFirst_style_list() == null || "".equals(this.getFirst_style_list())){
            return;
        }
        this.styleList = JSONObject.parseArray(this.getFirst_style_list(), String.class);

    }

    public void buildFileNameList(){
        if (this.getFile1name_list() == null || "".equals(this.getFile1name_list())){
            return;
        }
        this.file1nameList = JSONObject.parseArray(this.getFile1name_list(), String.class);

    }
}
