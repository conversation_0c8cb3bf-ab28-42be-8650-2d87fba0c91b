package org.springcenter.product.wsc.controller;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.api.dto.wsc.*;
import org.springcenter.product.wsc.modules.product.service.IProductBdListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/2/19 10:19
 */
@Slf4j
@RestController
@RequestMapping("/product/pd/list/api")
@Api(value = "ProductPdListApi",tags = "榜单商品相关信息接口")
public class ProductPdListController {

    @Autowired
    private IProductBdListService productDetailService;


    @ApiOperation(value = "根据vid获取榜单头部")
    @RequestMapping(value = "/getBdHeadInfo",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<VidBdHeadResp> getBdHeadInfo(@RequestBody @Validated CommonRequest<VidBdHeadReq> request){
        return ResponseResult.success(productDetailService.getBdHeadInfo(request.getRequestData()));
    }


    @ApiOperation(value = "根据vid和类目获取榜单商品列表")
    @RequestMapping(value = "/getBdListByVidSmallCateId",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<VidProductSalesResp>> getBdListByVidSmallCateId(@RequestBody @Validated CommonRequest<VidBdCateListReq> request){
        return ResponseResult.success(productDetailService.getBdListByVidSmallCateId(request.getRequestData(), request.getPage()),
                request.getPage());
    }


    @ApiOperation(value = "获取门店下商品的各个排序v1")
    @RequestMapping(value = "/getBatchProdOrderInfoV1",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<VidProdOrderInfoResp>> getBatchProdOrderInfo(@RequestBody @Validated CommonRequest<VidProdOrderInfoReq> request){
        return ResponseResult.success(productDetailService.getBatchProdOrderInfo(request.getRequestData()));
    }

    @ApiOperation(value = "获取门店下商品的各个排序老")
    @RequestMapping(value = "/getBatchProdOrderInfo",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<VidProdOrderInfoResp>> getBatchProdOrderInfoOld(@RequestBody @Validated CommonRequest<VidProdOrderInfoOldReq> request){
        return ResponseResult.success(productDetailService.getBatchProdOrderInfoOld(request.getRequestData()));
    }



    @ApiOperation(value = "获取商品价格")
    @RequestMapping(value = "/getBatchProdPrice",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<VidBdPriceResp>> getBatchProdPrice(@RequestBody @Validated CommonRequest<VidBdPriceReq> request){
        return ResponseResult.success(productDetailService.getBatchProdPrice(request.getRequestData()));
    }
}
