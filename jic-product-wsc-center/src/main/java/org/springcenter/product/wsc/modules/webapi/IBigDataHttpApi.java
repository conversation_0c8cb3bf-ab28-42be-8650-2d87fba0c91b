package org.springcenter.product.wsc.modules.webapi;

/**
 * <AUTHOR>
 * @Date:2024/4/25 17:13
 */

import org.springcenter.product.wsc.modules.product.entity.BigDataSearchFirstRespEntity;
import org.springcenter.product.wsc.modules.product.entity.BigDataSearchReqEntity;
import org.springcenter.product.wsc.modules.webapi.jic.BigDataCommonResponse;
import org.springcenter.product.wsc.modules.webapi.jic.BigDataComprehensiveReq;
import org.springcenter.product.wsc.modules.webapi.jic.BigDataComprehensiveResponse;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;

/**
 * 调用大数据接口
 */
public interface IBigDataHttpApi {


    /**
     * 计算价格
     * @param bigDataSearchReqEntity
     * @return
     */
    @POST("/text_search/text_search")
    Call<BigDataCommonResponse<List<BigDataSearchFirstRespEntity>>> searchByText(@Body BigDataSearchReqEntity bigDataSearchReqEntity);

    /**
     * 综合排序  包含的商品进行综合排序
     * @param bigDataComprehensiveReq 入参
     * @return 返回
     */
    //------内网专线
    //@POST("/ProdList/app")
    //-------公网
    //@POST("/ProdListwai/app")
    //-------内网ip
    // @POST("/app")
    // 限制商品
    @POST("/ProdPersonalized/app")
    Call<BigDataComprehensiveResponse> searchComprehensiveList(@Body BigDataComprehensiveReq bigDataComprehensiveReq);
}
