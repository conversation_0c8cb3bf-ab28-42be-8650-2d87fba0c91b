package org.springcenter.product.wsc.modules.voucher.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/14 17:13
 * @description
 */

@Data
public class VoucherColumnEntity {
     @ApiModelProperty("券规则id")
     private Long ruleId;

     @ApiModelProperty("券名称")
     private String vouName;

     @ApiModelProperty("折扣券VOU4/现金券VOU3")
     private String vouType;

     @ApiModelProperty("金额门槛")
     private String jeLimit;

     @ApiModelProperty("数量门槛")
     private String qtyLimit;

     @ApiModelProperty("折扣门槛")
     private String zkLimit;

     @ApiModelProperty("面额")
     private BigDecimal amt;

     @ApiModelProperty("折扣")
     private BigDecimal dis;

     @ApiModelProperty("品牌")
     private String pp;

     @ApiModelProperty("开始时间")
     private String validMinDate;

     @ApiModelProperty("结束时间")
     private String validdate;

     @ApiModelProperty("可用券1/积分换券2")
     private int businessType;

     @ApiModelProperty("所需积分")
     private BigDecimal needPoints;

     @ApiModelProperty("是否是券包")
     private boolean ifVouPackage = false;

     @ApiModelProperty("券包里的券")
     private List<PointsVouEntity> pointsVouEntityList;

     @ApiModelProperty("优惠力度")
     private BigDecimal prePower;

     @ApiModelProperty("积分换券的请求id")
     private String requestId;

     @ApiModelProperty("券到账时间")
     private String insertdate;

     @ApiModelProperty("券包图片")
     private String productImg;

     @ApiModelProperty("券包名称")
     private String productName;


     @ApiModelProperty("awardId")
     private Long awardId;


}
