<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.wsc.modules.mapper.product.JicVidBdInfoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.wsc.modules.model.product.JicVidBdInfo">
        <id column="ID" property="id" />
        <result column="VID" property="vid" />
        <result column="BRAND_ID" property="brandId" />
        <result column="BD_HEAD" property="bdHead" />
        <result column="BD_PROD_ORDER" property="bdProdOrder" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="PRO_BRAND_ID" property="proBrandId" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, VID, BRAND_ID, BD_HEAD, BD_PROD_ORDER, IS_DELETED,
        CREATE_TIME, UPDATE_TIME, PRO_BRAND_ID
    </sql>
    <select id="selectByVidAndBrandId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM jic_vid_bd_info
        WHERE IS_DELETED = 0 AND VID = #{vid} AND BRAND_ID = #{brandId}
          <if test="proBrandId != null and proBrandId != ''">
            AND PRO_BRAND_ID = #{proBrandId}
          </if>
    </select>


</mapper>
