package org.springcenter.product.wsc.modules.mapper.bojun;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.wsc.modules.model.bojun.BoxMProduct;
import org.springcenter.product.wsc.modules.product.entity.BoxMProductInfoEntity;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/3/11 13:26
 * @description
 */
public interface BoxMProductMapper extends BaseMapper<BoxMProduct> {


    List<BoxMProductInfoEntity> selectBrandByIds(@Param("ids") List<Long> ids);
}
