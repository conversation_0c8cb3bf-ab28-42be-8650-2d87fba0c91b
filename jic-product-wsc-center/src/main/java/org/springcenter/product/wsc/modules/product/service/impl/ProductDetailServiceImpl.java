package org.springcenter.product.wsc.modules.product.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.product.api.dto.BoxMProductDto;
import org.springcenter.product.api.dto.ProductSizeInfoEsResp;
import org.springcenter.product.wsc.modules.mapper.bojun.BoxMProductMapper;
import org.springcenter.product.wsc.modules.mapper.product.*;
import org.springcenter.product.api.dto.back.ProductFabInfoSizeResp;
import org.springcenter.product.wsc.modules.mapper.product.JicProductMallRelationMapper;
import org.springcenter.product.wsc.modules.mapper.product.ProductDetailNetDiskImgMapper;
import org.springcenter.product.wsc.modules.mapper.product.ProductSizeInfoCellSettingMapper;
import org.springcenter.product.wsc.modules.mapper.product.ProductSizeInfoSettingMapper;
import org.springcenter.product.wsc.modules.model.product.ProductSizeInfoCellSetting;
import org.springcenter.product.wsc.modules.model.product.ProductSizeInfoSetting;
import org.springcenter.product.wsc.modules.model.product.TenYearRepairableInfo;
import org.springcenter.product.wsc.modules.product.entity.*;
import org.springcenter.product.wsc.modules.product.enums.RedisKeysEnum;
import org.springcenter.product.wsc.modules.product.service.IProductDetailService;
import org.springcenter.product.wsc.modules.util.EsUtil;
import org.springcenter.product.wsc.modules.util.RedisService;
import org.springcenter.product.wsc.modules.util.SizeUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/3/20 16:25
 */
@Slf4j
@Service
@RefreshScope
public class ProductDetailServiceImpl implements IProductDetailService {

    @Value(value = "${product.size.info.index}")
    private String productSizeInfoIndex;

    @Value(value = "${product.detail.info.index}")
    private String productDetailIndex;

    @Value(value = "${product.detail.img.fabric.info.index}")
    private String productDetailImgFabricIndex;

    @Value(value = "${product.size.head.str}")
    private String sizeInfoHeadStr;

    @Value(value = "${store.special.vids}")
    private String storeSpecialVids;

    @Autowired
    private JicProductMallRelationMapper jicProductMallRelationMapper;

    @Autowired
    private ProductDetailNetDiskImgMapper productDetailNetDiskImgMapper;

    @Autowired
    private EsUtil esUtil;

    @Autowired
    private ProductSizeInfoSettingMapper productSizeInfoSettingMapper;

    @Autowired
    private ProductSizeInfoCellSettingMapper productSizeInfoCellSettingMapper;

    @Autowired
    private RedisService redisService;

    @Value("${ten.year.redis.key}")
    private String tenYearRedisKey;

    @Autowired
    private TenYearRepairableInfoMapper tenYearRepairableInfoMapper;

    @Value(value = "${product.detail.param.info.index}")
    private String productDetailParamInfoIndex;

    @Value(value = "${fabric.icon.img}")
    private String fabricIcon;

    @Value(value = "${fabric.kz.dw}")
    private String dw;


    private String DETAIL_GOOD_ID = "DETAIL_GOOD_ID:";

    @Autowired
    private BoxMProductMapper boxMProductMapper;

    @Override
    public List<String> getProductDetailsByGoodId(Long requestData) {
        if (requestData == null) {
            throw new RuntimeException("传入的微盟商品id不能为空!");
        }

        Object o = redisService.get(RedisKeysEnum.DETAIL_IMG.join(requestData));
        List<String> imgs = new ArrayList<>();
        if (o == null) {
            List<BojunProductInfoByWmGoodId> bojunProductInfos = exchangeBojunIdAndName(Lists.newArrayList(requestData));
            if (CollectionUtils.isEmpty(bojunProductInfos)) {
                log.error("====================未找到对应的商品的伯俊id");
                redisService.set(RedisKeysEnum.DETAIL_IMG.join(requestData), JSONObject.toJSONString(imgs), 86400);
                return Collections.emptyList();
            }
            BojunProductInfoByWmGoodId boInfoData = bojunProductInfos.get(0);
            // 根据goodId查询
            String name = boInfoData.getName();
            if (StringUtils.isBlank(name)) {
                log.error("====================当前商品的款号为空, goodId:{}", requestData);
                redisService.set(RedisKeysEnum.DETAIL_IMG.join(requestData), JSONObject.toJSONString(imgs), 86400);
                return Collections.emptyList();
            }
            imgs = productDetailNetDiskImgMapper.selectDetailListByName(name);
            redisService.set(RedisKeysEnum.DETAIL_IMG.join(requestData), JSONObject.toJSONString(imgs), 86400);
        } else {
            imgs = JSONObject.parseArray((String) o, String.class);
        }

        return imgs;
    }


    @Override
    public List<BojunProductInfoByWmGoodId> exchangeWmToBjProId(List<Long> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            throw new RuntimeException("当前入参不能为空！");
        }

        Object o = redisService.get(RedisKeysEnum.DETAIL_GOOD_IDS.join(JSONObject.toJSONString(requestData)));
        if (o == null) {
            List<BojunProductInfoByWmGoodId> rets = exchangeBojunIdAndName(requestData);
            if (CollectionUtils.isEmpty(rets)) {
                return Collections.emptyList();
            }
            redisService.set(RedisKeysEnum.DETAIL_GOOD_IDS.join(JSONObject.toJSONString(requestData)),
                    JSONObject.toJSONString(rets), 86400);
            return rets;
        } else {
            return JSONObject.parseArray(o.toString(), BojunProductInfoByWmGoodId.class);
        }

    }

    @Override
    public Integer getIsNewByGoodId(Long requestData) {
        if (requestData == null) {
            throw new RuntimeException("传入的商品id不能为空");
        }

        Object o = redisService.get(RedisKeysEnum.NEW_PRODUCT.join(requestData));
        Integer ret = 0;
        if (o == null) {
            List<BojunProductInfoByWmGoodId> bojunProductInfos = exchangeBojunIdAndName(Lists.newArrayList(requestData));
            if (CollectionUtils.isEmpty(bojunProductInfos)) {
                log.error("====================未找到对应的商品的伯俊id");
                redisService.set(RedisKeysEnum.NEW_PRODUCT.join(requestData), JSONObject.toJSONString(ret), 86400);
                return 0;
            }
            BojunProductInfoByWmGoodId boInfoData = bojunProductInfos.get(0);
            ProductDetailInfoEsResp detailInfo = getProdDetailInfoByProductId(boInfoData.getId() + "");
            if (detailInfo == null) {
                log.error("====================未找到对应索引里的商品信息");
                ret = 0;
            } else {
                ret = detailInfo.getIs_new();
            }
            redisService.set(RedisKeysEnum.NEW_PRODUCT.join(requestData), JSONObject.toJSONString(ret), 86400);
        } else {
            ret = JSONObject.parseObject((String) o, Integer.class);
        }

        return ret;
    }

    @Override
    public ProductDetailSizeAndModelInfoEntity getSizeAndModelByGoodId(Long requestData) {
        if (requestData == null) {
            throw new RuntimeException("微盟商品id不能为空");
        }

        Object o = redisService.get(RedisKeysEnum.DETAIL_SIZE_AND_MODEL.join(requestData));

        ProductDetailSizeAndModelInfoEntity entity = new ProductDetailSizeAndModelInfoEntity();
        if (o == null) {
            List<BojunProductInfoByWmGoodId> bojunProductInfos = exchangeBojunIdAndName(Lists.newArrayList(requestData));
            if (CollectionUtils.isEmpty(bojunProductInfos)) {
                log.error("====================未找到对应的商品的伯俊id");
                redisService.set(RedisKeysEnum.DETAIL_SIZE_AND_MODEL.join(requestData), JSONObject.toJSONString(entity), 86400);
                return null;
            }
            BojunProductInfoByWmGoodId boInfoData = bojunProductInfos.get(0);


            // 获取版型图
            ProductDetailInfoEsResp detailInfo = getProdDetailInfoByProductId(boInfoData.getId() + "");
            if (detailInfo == null) {
                log.error("====================未找到对应索引里的商品信息");
                redisService.set(RedisKeysEnum.DETAIL_SIZE_AND_MODEL.join(requestData), JSONObject.toJSONString(entity), 86400);
                return entity;
            }
            entity.setModel(detailInfo.getTi_xing());
            entity.setModelImg(detailInfo.getModel_image());

            // 获取尺码表
            List<List<String>> sizeInfos = getSizeInfoByProductId(boInfoData.getId());
            entity.setSizeInfo(sizeInfos);
            redisService.set(RedisKeysEnum.DETAIL_SIZE_AND_MODEL.join(requestData), JSONObject.toJSONString(entity), 86400);
        } else {
            entity = JSONObject.parseObject(Objects.toString(o), ProductDetailSizeAndModelInfoEntity.class);
        }

        return entity;
    }



    public List<List<String>> getSizeInfoByProductId(Long id) {
        // 查询当前商品的尺码信息
        List<ProductSizeInfoEsResp> sizeInfoEsResps = searchProductSizeInfoInEs(id);
        if (CollectionUtils.isEmpty(sizeInfoEsResps)) {
            return Collections.emptyList();
        }

        // 获取当前品牌和当前小类的数据 过滤需要的部位
        List<ProductSizeInfoSetting> settingInfo = productSizeInfoSettingMapper
                .selectBrandAndSmallClassInfo(sizeInfoEsResps.get(0).getC_arcbrand_id(), sizeInfoEsResps.get(0).getSmall_class_id());


        // 获取品牌和品类id
        Integer brandSettingId = null;
        Integer smallClassSettingId = null;
        if (CollectionUtils.isNotEmpty(settingInfo)) {
            ProductSizeInfoSetting brandSetting = settingInfo.stream().filter(v -> Objects.equals(v.getType(), 0)).findFirst().orElse(null);
            brandSettingId = brandSetting == null ? brandSettingId : brandSetting.getId();
            ProductSizeInfoSetting smallClassSetting = settingInfo.stream().filter(v -> Objects.equals(v.getType(), 1)).findFirst().orElse(null);
            smallClassSettingId = smallClassSetting == null ? smallClassSettingId : smallClassSetting.getId();
        }

        // 获取配置的表头信息
        List<ProductSizeInfoCellSetting> cellSettings = new ArrayList<>();
        if (smallClassSettingId != null && brandSettingId != null) {
            cellSettings = productSizeInfoCellSettingMapper.selectByBandAndSmallClassIds(brandSettingId, org.assertj.core.util.Lists.newArrayList(smallClassSettingId));
            cellSettings = cellSettings.stream().sorted(Comparator.comparing(ProductSizeInfoCellSetting::getId)).collect(Collectors.toList());
            // 特殊处理 若当前的xiu为插肩袖 则不取肩宽信息
            if (StringUtils.isNotBlank(sizeInfoEsResps.get(0).getXiu()) && sizeInfoEsResps.get(0).getXiu().contains("插肩袖")) {
                cellSettings = cellSettings.stream().filter(v -> !Objects.equals(v.getOutsideFiled(), "肩宽")).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(cellSettings)) {return Collections.emptyList();}


        // 处理获取行数据
        // 如果是婴童
        List<String> model = new ArrayList<>();
        if (Objects.equals(sizeInfoEsResps.get(0).getC_arcbrand_id(), 4)) {
            List<String> sizeInfos = sizeInfoEsResps.stream()
                    .filter(v -> org.apache.commons.lang3.StringUtils.isNotBlank(v.getSize_num()))
                    .filter(v -> org.apache.commons.lang3.StringUtils.isNotBlank(v.getStylepartsize_model()))
                    .map(ProductSizeInfoEsResp::getStylepartsize_model)
                    .distinct().collect(Collectors.toList());
            model = SizeUtil.getStylepartsize_modelPx(sizeInfos);
        } else {
            model = sizeInfoEsResps.stream()
                    .filter(v -> org.apache.commons.lang3.StringUtils.isNotBlank(v.getSize_num()))
                    .filter(v -> org.apache.commons.lang3.StringUtils.isNotBlank(v.getStylepartsize_model()))
                    .map(ProductSizeInfoEsResp::getStylepartsize_model)
                    .distinct().sorted().collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(model)) {
            return Collections.emptyList();
        }

        // 获取所有尺码对应数据各个部位的数据
        Map<String, List<ProductSizeInfoEsResp>> sizeMap = sizeInfoEsResps.stream()
                .filter(v -> StringUtils.isNotBlank(v.getStylepartsize_model()))
                .collect(Collectors.groupingBy(ProductSizeInfoEsResp::getStylepartsize_model));
        if (MapUtils.isEmpty(sizeMap)) {
            return Collections.emptyList();
        }
        // 各个尺码对应的双部位对应的数据
        HashMap<String, Map<String, List<ProductSizeInfoEsResp>>> sizePartMap = new HashMap<>();
        sizeMap.forEach((key, value1) -> {
            Map<String, List<ProductSizeInfoEsResp>> value = value1.stream()
                    .filter(v -> org.apache.commons.lang3.StringUtils.isNotBlank(v.getSize_num()))
                    .collect(Collectors.groupingBy(ProductSizeInfoEsResp::getPart));
            sizePartMap.put(key, value);
        });
        if (MapUtils.isEmpty(sizePartMap)) {
            return Collections.emptyList();
        }
        // {"系统名称":{"部位说明1", "部位说名2"}}
        Map<String, Set<String>> map = new HashMap<>();
        // {"尺码"：{"系统名称@部位说明": "尺寸", "系统名称@部位说明2":"尺寸2"}}
        Map<String, Map<String, String>> size2PartClffMap = new HashMap<>();
        for (ProductSizeInfoEsResp sizeInfoEsResp : sizeInfoEsResps) {
            sizeInfoEsResp.setClff(org.apache.commons.lang3.StringUtils.defaultIfBlank(sizeInfoEsResp.getClff(), ""));
            //根据part 获取 该part下的clff数据
            Set<String> clffSet = map.get(sizeInfoEsResp.getPart());
            if (clffSet == null) {
                clffSet = new HashSet<>();
                map.put(sizeInfoEsResp.getPart(), clffSet);
            }
            clffSet.add(sizeInfoEsResp.getClff());


            //将尺寸对应partclff数据记录一下
            Map<String, String> partClffMap = size2PartClffMap.get(sizeInfoEsResp.getStylepartsize_model());
            if (partClffMap == null) {
                partClffMap = new HashMap<>();
                size2PartClffMap.put(sizeInfoEsResp.getStylepartsize_model(), partClffMap);
            }
            partClffMap.put(sizeInfoEsResp.getPart() + "@" + org.apache.commons.lang3.StringUtils.defaultIfBlank(sizeInfoEsResp.getClff(), ""), sizeInfoEsResp.getSize_num());

        }

        List<String> title = new ArrayList<>();
        title.add("尺码");
        List<String> clffTitle = new ArrayList<>();
        clffTitle.add("部位测量说明");

        List<List<String>> sizeTableInfo = new ArrayList<>();
        for (String m : model) {
            List<String> sizeTableRow = new ArrayList<>();
            sizeTableRow.add(m);

            sizeTableInfo.add(sizeTableRow);
        }

        HashMap<String, Integer> needSkipPart = new HashMap<>();
        // {"系统名称":{"部位说明1", "部位说名2"}}

        for (ProductSizeInfoCellSetting setting : cellSettings) {
            if (needSkipPart.get(setting.getOutsideFiled()) != null) {
                continue;
            }
            Set<String> clffSet = map.get(setting.getSystemFiled());
            if (clffSet != null) {
                needSkipPart.put(setting.getOutsideFiled(), setting.getSort());
                for (String clff : clffSet) {
                    title.add(setting.getOutsideFiled());
                    clffTitle.add(clff);

                    for (List<String> sizeTableRow : sizeTableInfo) {
                        sizeTableRow.add(size2PartClffMap.get(sizeTableRow.get(0)).get(setting.getSystemFiled() + "@" + clff));
                    }
                    break;
                }
            }
        }

        //sizeTableInfo.add(0, clffTitle);
        sizeTableInfo.add(0, title);

        return sizeTableInfo;
    }

    @Override
    public List<BojunProductInfo> getBJProductInfoByWmSkuId(List<Long> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            return Collections.emptyList();
        }

        Object o = redisService.get(RedisKeysEnum.DETAIL_SKU_IDS.join(JSONObject.toJSONString(requestData)));
        if (o == null) {
            com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, 2000);
            List<String> goods = requestData.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            List<BojunProductInfo> rets = jicProductMallRelationMapper.getBJProductInfoByWmSkuId(goods);
            if (CollectionUtils.isEmpty(rets)) {
                return Collections.emptyList();
            }
            redisService.set(RedisKeysEnum.DETAIL_SKU_IDS.join(JSONObject.toJSONString(requestData)),
                    JSONObject.toJSONString(rets), 86400);
            return rets;
        } else {
            return JSONObject.parseArray(o.toString(), BojunProductInfo.class);
        }
    }


    @Autowired
    private JicMallOrgMapper jicMallOrgMapper;

    @Override
    public String getVidNameByVid(String requestData) {
        if (StringUtils.isBlank(requestData)) {
            return "";
        }

        // 查询缓存
        Object o = redisService.get(RedisKeysEnum.VID_NAME.join(requestData));
        if (o == null) {
            String name = jicMallOrgMapper.selectNameByVid(requestData);
            if (StringUtils.isBlank(name)) {
                return "";
            }
            String s = removeParentheses(name);
            // 存七天
            redisService.set(RedisKeysEnum.VID_NAME.join(requestData), s, 86400);
            return s;
        } else {
            return Objects.toString(o);
        }

    }

    private static String removeParentheses(String input) {
        // 正则表达式匹配括号及其内容
        String regex = "\\([^()]*\\)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        // 使用空字符串替换所有匹配到的括号及其内容
        return matcher.replaceAll("");
    }

    @Override
    public ProductBaseParamInfo getDetailParamInfoByGoodId(Long requestData) {
        if (requestData == null) {
            throw new RuntimeException("传入的商品id不能为空");
        }

        Object o = redisService.get(RedisKeysEnum.BASIC_PARAM_INFO.join(requestData));
        ProductBaseParamInfo productBaseParamInfo = null;
        if (o == null) {
            List<BojunProductInfoByWmGoodId> bojunProductInfos = exchangeBojunIdAndName(Lists.newArrayList(requestData));
            if (CollectionUtils.isEmpty(bojunProductInfos)) {
                log.error("====================未找到对应的商品的伯俊id");
                redisService.set(RedisKeysEnum.BASIC_PARAM_INFO.join(requestData), JSONObject.toJSONString(productBaseParamInfo), 86400);
                return null;
            }
            BojunProductInfoByWmGoodId boInfoData = bojunProductInfos.get(0);
            ProductBaseParamInfoEsResp detailParamInfo = getProdDetailParamInfoByProductId(boInfoData.getId() + "");
            if (detailParamInfo == null) {
                log.error("====================未找到对应索引里的商品信息");
                redisService.set(RedisKeysEnum.BASIC_PARAM_INFO.join(requestData), JSONObject.toJSONString(productBaseParamInfo), 86400);
                return null;
            }
            productBaseParamInfo = buildProductBaseParamInfo(detailParamInfo);
            redisService.set(RedisKeysEnum.BASIC_PARAM_INFO.join(requestData), JSONObject.toJSONString(productBaseParamInfo), 86400);
        } else {
            productBaseParamInfo = JSONObject.parseObject((String) o, ProductBaseParamInfo.class);
        }

        return productBaseParamInfo;
    }

    private ProductBaseParamInfo buildProductBaseParamInfo(ProductBaseParamInfoEsResp detailParamInfo) {
        ProductBaseParamInfo paramInfo = new ProductBaseParamInfo();
        BeanUtils.copyProperties(detailParamInfo, paramInfo);
        paramInfo.setComponentFabric(detailParamInfo.getSjcf());
        paramInfo.setFabricPromotion(detailParamInfo.getSc_popularize());
        paramInfo.setBrand(detailParamInfo.getBrand());
        paramInfo.setStyleId(detailParamInfo.getStyle_id());
        paramInfo.setYear(detailParamInfo.getYear());
        paramInfo.setClothElastic(detailParamInfo.getCloth_elastic());
        paramInfo.setWeidu(detailParamInfo.getWeidu());
        paramInfo.setKunxing(detailParamInfo.getKunxing());
        paramInfo.setTixing(detailParamInfo.getTixing());
        paramInfo.setXiuxing(detailParamInfo.getXiuxing());
        paramInfo.setLingxing(detailParamInfo.getLingxing());
        paramInfo.setXiu(detailParamInfo.getXiu());
        paramInfo.setYouli(detailParamInfo.getYouli());
        paramInfo.setXiabai(detailParamInfo.getXiabai());
        paramInfo.setKaicha(detailParamInfo.getKaicha());
        paramInfo.setYimenjin(detailParamInfo.getYimenjin());
        paramInfo.setKumenjin(detailParamInfo.getKumenjin());
        paramInfo.setKujiaokou(detailParamInfo.getKujiaokou());
        paramInfo.setFacade(detailParamInfo.getFacade());
        paramInfo.setThick(detailParamInfo.getThick());
        paramInfo.setYrImportArea(detailParamInfo.getYr_import_area());
        paramInfo.setScStiffness(detailParamInfo.getSc_stiffness());
        paramInfo.setScEffect(detailParamInfo.getSc_effect());
        paramInfo.setBomarea(detailParamInfo.getBomarea());
        paramInfo.setScEnvironmental(detailParamInfo.getSc_environmental());
        paramInfo.setFiller(detailParamInfo.getFiller());
        paramInfo.setFillcontent(detailParamInfo.getFillcontent());
        paramInfo.setGyxj(detailParamInfo.getGyxj());
        paramInfo.setWashInfo(detailParamInfo.getWash_info());
        paramInfo.setWashNo(detailParamInfo.getWash_no());
        if (CollectionUtils.isNotEmpty(detailParamInfo.getWashInfoList())) {
            List<ProductBaseParamInfo.IconDataInfo> washDataList = new ArrayList<>();
            detailParamInfo.getWashInfoList().forEach(v -> {
                ProductBaseParamInfo.IconDataInfo washData = new ProductBaseParamInfo.IconDataInfo();
                washData.setName(v.getWashName());
                washData.setIcon(v.getWashNo());
                washDataList.add(washData);
            });
            paramInfo.setWashData(washDataList);
        }


        List<ProductBaseParamInfo.IconDataInfo> fabricIconInfoList = new ArrayList<>();
        ProductBaseParamInfo.IconDataInfo fabricIconInfo = new ProductBaseParamInfo.IconDataInfo();
        fabricIconInfo.setName(detailParamInfo.getSjcf());
        fabricIconInfo.setIcon(fabricIcon);
        fabricIconInfoList.add(fabricIconInfo);
        paramInfo.setFabricIconInfo(fabricIconInfoList);

        if (CollectionUtils.isNotEmpty(detailParamInfo.getFabricMaterialDataList())) {
            List<ProductBaseParamInfo.IconDataInfo> materialIconInfoList = new ArrayList<>();
            detailParamInfo.getFabricMaterialDataList().forEach(v -> {
                ProductBaseParamInfo.IconDataInfo materialIconInfo = new ProductBaseParamInfo.IconDataInfo();
                materialIconInfo.setName(v.getLabel_name());
                materialIconInfo.setIcon(v.getMc_pic());
                materialIconInfoList.add(materialIconInfo);
            });
            paramInfo.setMaterialIconInfo(materialIconInfoList);
        }

        if (CollectionUtils.isNotEmpty(detailParamInfo.getFabricServerFunctionDataList())) {
            List<ProductBaseParamInfo.IconDataInfo> serveFunctionInfoList = new ArrayList<>();
            detailParamInfo.getFabricServerFunctionDataList().forEach(v -> {
                ProductBaseParamInfo.IconDataInfo serveFunctionInfo = new ProductBaseParamInfo.IconDataInfo();
                serveFunctionInfo.setName(v.getLabel_name());
                serveFunctionInfo.setIcon(v.getMc_pic());
                serveFunctionInfoList.add(serveFunctionInfo);
            });
            paramInfo.setServeFunctionInfo(serveFunctionInfoList);
        }


        if (CollectionUtils.isNotEmpty(detailParamInfo.getFabricKzDataList())) {
            List<String> kzList = detailParamInfo.getFabricKzDataList().stream()
                    .map(ProductBaseParamInfoEsResp.FabricKzData::getFabric_kz).distinct().collect(Collectors.toList());
            if (kzList.size() == 1) {
                paramInfo.setKz(StringUtils.isBlank(kzList.get(0)) ? "" : kzList.get(0) + dw);
            } else {
                List<String> kzName = new ArrayList<>();
                detailParamInfo.getFabricKzDataList().forEach(v -> {
                    if (StringUtils.isBlank(v.getFabric_kz())) {
                        return;
                    }
                    String kz = v.getColor_name() + ":" + v.getFabric_kz() + dw;
                    kzName.add(kz);
                });
                paramInfo.setKz(kzName.stream().collect(Collectors.joining(";")));
            }
        }

        return paramInfo;
    }



    private ProductBaseParamInfoEsResp getProdDetailParamInfoByProductId(String id) {

        GetRequest request = new GetRequest(productDetailParamInfoIndex, id);
        ProductBaseParamInfoEsResp resp = null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                resp = ProductBaseParamInfoEsResp.fromJson(response.getSourceAsString(), ProductBaseParamInfoEsResp.class);
                resp.buildFabricKzData();
                resp.buildMaterialData();
                resp.buildServeFunctionData();
                resp.buildWashInfo();
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品详情的信息异常e = {}", e.getMessage());
        }
        return resp;
    }

    @Override
    public DetailImgResp getProductDetailsByGoodIdV1(Long requestData) {
        if (requestData == null) {
            throw new RuntimeException("传入的微盟商品id不能为空!");
        }

        Object o = redisService.get(RedisKeysEnum.NEW_DETAIL_IMG.join(requestData));
        DetailImgResp imgResp = new DetailImgResp();;
        if (o == null) {
            List<BojunProductInfoByWmGoodId> bojunProductInfos = exchangeBojunIdAndName(Lists.newArrayList(requestData));
            if (CollectionUtils.isEmpty(bojunProductInfos)) {
                log.error("====================未找到对应的商品的伯俊id");
                redisService.set(RedisKeysEnum.NEW_DETAIL_IMG.join(requestData), JSONObject.toJSONString(imgResp), 86400);
                return null;
            }
            BojunProductInfoByWmGoodId boInfoData = bojunProductInfos.get(0);
            // 查询es的数据
            ProductDetailFabricImgInfoEsResp fabricImgInfo = getProdDetailFabricImgInfoByProductId(boInfoData.getId() + "");
            if (fabricImgInfo == null) {
                redisService.set(RedisKeysEnum.NEW_DETAIL_IMG.join(requestData), JSONObject.toJSONString(imgResp), 86400);
                return null;
            }

            imgResp.setDetailImgs(fabricImgInfo.getDetailImgs());
            imgResp.setFabricComposition(fabricImgInfo.getSjcf());
            imgResp.setWashInstruction(fabricImgInfo.getWash_name());
            imgResp.setFabricTitle(fabricImgInfo.getSc_outsidename());
            if (CollectionUtils.isNotEmpty(fabricImgInfo.getIconDatas())) {
                List<DetailImgResp.FabricCharacterData> fabricCharacteristic = new ArrayList<>();
                fabricImgInfo.getIconDatas().forEach(v -> {
                    DetailImgResp.FabricCharacterData data = new DetailImgResp.FabricCharacterData();
                    data.setIcon(v.getMc_pic());
                    data.setIconName(v.getLabel_name());
                    fabricCharacteristic.add(data);
                });
                imgResp.setFabricCharacteristic(fabricCharacteristic);
            }
            redisService.set(RedisKeysEnum.NEW_DETAIL_IMG.join(requestData), JSONObject.toJSONString(imgResp), 86400);
        } else {
            imgResp = JSONObject.parseObject((String) o, DetailImgResp.class);
        }

        return imgResp;
    }

    @Override
    public ProductLabelInfoEntity getProductLabelByGoodId(Long requestData) {
        ProductLabelInfoEntity entity = new ProductLabelInfoEntity();
        entity.setIsNew(0);
        if (requestData == null) {
            throw new RuntimeException("传入的商品id不能为空");
        }

        Object goodObject = redisService.get(RedisKeysEnum.PRODUCT_LABEL_INFO.join(requestData));

        if (goodObject == null) {
            List<BojunProductInfoByWmGoodId> bojunProductInfos = exchangeBojunIdAndName(Lists.newArrayList(requestData));
            if (CollectionUtils.isEmpty(bojunProductInfos)) {
                log.error("====================未找到对应的商品的伯俊id");
                redisService.set(RedisKeysEnum.PRODUCT_LABEL_INFO.join(requestData), JSONObject.toJSONString(entity), 86400);
                return entity;
            }
            BojunProductInfoByWmGoodId boInfoData = bojunProductInfos.get(0);
            ProductDetailInfoEsResp detailInfo = getProdDetailInfoByProductId(boInfoData.getId() + "");
            if (detailInfo == null) {
                log.error("====================未找到对应索引里的商品信息");
                redisService.set(RedisKeysEnum.PRODUCT_LABEL_INFO.join(requestData), JSONObject.toJSONString(entity), 86400);
                return entity;
            }
            entity.setIsNew(detailInfo.getIs_new());
            entity.setStyleLabels(detailInfo.getStyleList());
            entity.setFile1nameLabels(detailInfo.getFile1nameList());

            Object o = redisService.get(RedisKeysEnum.TEN_YEAR_INFO.join(tenYearRedisKey));
            if (o == null) {
                TenYearRepairableInfo info = tenYearRepairableInfoMapper.selectById(1);
                if (info == null) {
                    entity.setIsShowTenYear(0);
                } else {
                    redisService.set(RedisKeysEnum.TEN_YEAR_INFO.join(tenYearRedisKey), info.getIncludeBrandInfo() + "@" + info.getExcludeSmallCategory(), 86400);
                    List<String> brands = Arrays.stream(info.getIncludeBrandInfo().split(",")).collect(Collectors.toList());
                    List<String> notInCategries = Arrays.stream(info.getExcludeSmallCategory().split(",")).collect(Collectors.toList());if (brands.contains(Objects.toString(detailInfo.getC_arcbrand_id())) &&
                            !notInCategries.contains(Objects.toString(detailInfo.getSmall_class_id()))) {
                        entity.setIsShowTenYear(1);
                    } else {
                        entity.setIsShowTenYear(0);
                    }
                }
            } else {
                String params = Objects.toString(o);
                String[] split = params.split("@");
                String brand = split.length == 0 ? "" : split[0];
                List<String> brands = Arrays.stream(brand.split(",")).collect(Collectors.toList());
                String cate = split.length <= 1 ? "" : split[1];
                List<String> notInCategries = Arrays.stream(cate.split(",")).collect(Collectors.toList());
                if (brands.contains(Objects.toString(detailInfo.getC_arcbrand_id())) &&
                        !notInCategries.contains(Objects.toString(detailInfo.getSmall_class_id()))) {
                    entity.setIsShowTenYear(1);
                } else {
                    entity.setIsShowTenYear(0);
                }
            }
            redisService.set(RedisKeysEnum.PRODUCT_LABEL_INFO.join(requestData), JSONObject.toJSONString(entity), 86400);
        } else {
            entity = JSONObject.parseObject((String) goodObject, ProductLabelInfoEntity.class);
        }

        return entity;
    }

    @Override
    public SpecialStoreListResp getSpecialStoreList() {
        Map storeSpecialMap = JSONObject.parseObject(storeSpecialVids, Map.class);
        if (MapUtils.isEmpty(storeSpecialMap)) {
            return null;
        }

        String inPurchase = (String) storeSpecialMap.get("inPurchase");
        String sample = (String) storeSpecialMap.get("sample");

        SpecialStoreListResp specialStoreListResp = new SpecialStoreListResp();
        specialStoreListResp.setInPurchaseStoreList(Arrays.asList(inPurchase.split(",")));
        specialStoreListResp.setSampleStoreList(Arrays.asList(sample.split(",")));
        return specialStoreListResp;
    }

    private List<ProductSizeInfoEsResp> searchProductSizeInfoInEs(Long productId) {
        SearchRequest request = new SearchRequest();
        request.indices(productSizeInfoIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("product_id", productId));
        queryBuilder.must(QueryBuilders.termQuery("is_sync", "Y"));

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(10000);
        sourceBuilder.sort("product_id", SortOrder.ASC);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品SPU_SIZE_INFO入参 {}", request.source().toString());
        List<ProductSizeInfoEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }

            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                ProductSizeInfoEsResp entity = ProductSizeInfoEsResp.fromJson(hit.getSourceAsString(), ProductSizeInfoEsResp.class);
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品SPU_SIZE_INFO入参异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }

    private ProductDetailInfoEsResp getProdDetailInfoByProductId(String productId) {
        GetRequest request = new GetRequest(productDetailIndex, productId);
        ProductDetailInfoEsResp resp = null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                resp = ProductDetailInfoEsResp.fromJson(response.getSourceAsString(), ProductDetailInfoEsResp.class);
                resp.buildFileNameList();
                resp.buildStyleList();
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品详情的信息异常e = {}", e.getMessage());
        }
        return resp;
    }


    private ProductDetailFabricImgInfoEsResp getProdDetailFabricImgInfoByProductId(String productId) {
        GetRequest request = new GetRequest(productDetailImgFabricIndex, productId);
        ProductDetailFabricImgInfoEsResp resp = null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                resp = ProductDetailFabricImgInfoEsResp.fromJson(response.getSourceAsString(), ProductDetailFabricImgInfoEsResp.class);
                resp.buildIconDataList();
                resp.buildDetailImgsList();
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品详情的信息异常e = {}", e.getMessage());
        }
        return resp;
    }


    /**
     * 根据微盟goodId转换成伯俊productId
     * @param goodIds 微盟goodId转换productId
     * @return 返回
     */
    private List<BojunProductInfoByWmGoodId> exchangeBojunIdAndName(List<Long> goodIds) {
        List<String> goods = goodIds.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, 999);
        List<BojunProductInfoByWmGoodId> wmGoodIds = jicProductMallRelationMapper.exchangeBojunIdAndName(goods);
        if (CollectionUtils.isEmpty(wmGoodIds)) {
            return Collections.emptyList();
        }
        List<Long> Ids = wmGoodIds.stream().map(BojunProductInfoByWmGoodId::getId).collect(Collectors.toList());
        List<BoxMProductInfoEntity> brands = boxMProductMapper.selectBrandByIds(Ids);
        HashMap<Long, Long> map = brands.stream().collect(HashMap::new, (k, v) -> k.put(v.getId(), v.getBrandId()), HashMap::putAll);
        wmGoodIds.forEach(v -> {
            if (MapUtils.isEmpty(map) || !map.containsKey(v.getId())) {
                return;
            }
            v.setProBrandId(map.get(v.getId()));
        });
        return wmGoodIds;
    }
}
