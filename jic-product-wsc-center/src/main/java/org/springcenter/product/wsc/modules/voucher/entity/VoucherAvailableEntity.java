package org.springcenter.product.wsc.modules.voucher.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/3/29 15:00
 * @description
 */
@Data
public class VoucherAvailableEntity {
     // 券名称
     private String vouName;

     // 券数量
     private Long voucherCount;

     // 规则id
     private Long ruleId;

     // 券id
     private Long awarid;

     // 券类型
     private String vouType;

     // 用户id
     private String unionId;

     // 金额门槛
     private String jeLimit;

     // 数量门槛
     private String qtyLimit;

     // 品牌
     private String pp;

     // 折扣门槛
     private String zkLimit;

     // 面额
     private BigDecimal amt;

     // 折扣
     private BigDecimal dis;

     // 券号
     private String vouchersNo;

     // 券开始时间
     private String validMinDate;

     // 券有效期
     private String validdate;
}
