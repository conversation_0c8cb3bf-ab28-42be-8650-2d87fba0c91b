package org.springcenter.product.wsc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.wsc.controller.req.VidCouponProductListFilterReq;
import org.springcenter.product.wsc.controller.req.VidCouponProductListReq;
import org.springcenter.product.wsc.controller.req.VidProductListFilterReq;
import org.springcenter.product.wsc.controller.req.VidProductListReq;
import org.springcenter.product.wsc.modules.product.entity.VidProductFilterRespEntity;
import org.springcenter.product.wsc.modules.product.entity.VidProductListRespEntity;
import org.springcenter.product.wsc.modules.product.service.IProductVidCouponService;
import org.springcenter.product.wsc.modules.product.service.IProductVidService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/4/25 14:01
 */
@Slf4j
@RestController
@RequestMapping("/product/vid/api")
@Api(value = "ProductVidApi",tags = "门店商品接口")
public class ProductVidApiController {

    @Autowired
    private IProductVidService productVidService;

    @Autowired
    private IProductVidCouponService productVidCouponService;


    @ApiOperation(value = "获取门店商品信息")
    @PostMapping("/queryVidGoods")
    public ResponseResult<List<VidProductListRespEntity>> queryVidGoods(@Validated @RequestBody CommonRequest<VidProductListReq> request){
        Page page = request.getPage();
        log.info("获取门店下SKC数据 入参：{}", JSONObject.toJSONString(request));
        List<VidProductListRespEntity> result = productVidService.queryVidGoods(request.getRequestData(), page);
        return ResponseResult.success(result, page);
    }


    @ApiOperation(value = "获取商品列表筛选条件 -- 入参为门店品牌")
    @PostMapping("/queryVidGoodsFilters")
    public ResponseResult<VidProductFilterRespEntity> queryVidGoodsFilters(@Validated @RequestBody CommonRequest<VidProductListFilterReq> request){
        log.info("获取商品列表筛选条件数据 入参：{}", JSONObject.toJSONString(request));
        VidProductFilterRespEntity result = productVidService.queryVidGoodsFilters(request.getRequestData());
        return ResponseResult.success(result);
    }



    @ApiOperation(value = "获取优惠券门店商品信息")
    @PostMapping("/queryVidCouponGoods")
    public ResponseResult<List<VidProductListRespEntity>> queryVidCouponGoods(@Validated @RequestBody CommonRequest<VidCouponProductListReq> request){
        Page page = request.getPage();
        log.info("获取优惠券门店商品信息 入参：{}", JSONObject.toJSONString(request));
        List<VidProductListRespEntity> result = productVidCouponService.queryVidCouponGoods(request.getRequestData(), page);
        return ResponseResult.success(result, page);
    }



    @ApiOperation(value = "获取优惠券筛选信息")
    @PostMapping("/queryVidCouponFilters")
    public ResponseResult<VidProductFilterRespEntity> queryVidCouponFilters(@Validated @RequestBody CommonRequest<VidCouponProductListFilterReq> request){
        Page page = request.getPage();
        log.info("获取优惠券筛选信息 入参：{}", JSONObject.toJSONString(request));
        VidProductFilterRespEntity result = productVidCouponService.queryVidCouponFilters(request.getRequestData(), page);
        return ResponseResult.success(result);
    }

}
