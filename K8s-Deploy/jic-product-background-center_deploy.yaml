apiVersion: apps/v1
kind: Deployment
metadata:
  name: jic-product-background-center
  labels:
    app: jic-product-background-center
spec:
  replicas: 1
  template:
    metadata:
      name: jic-product-background-center
      labels:
        app: jic-product-background-center
    spec:
      containers:
        - name: jic-product-background-center
          image: "harbor.jnby.com/jic-product-center/jic-product-background-center:latest"
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 9351
              name: jic-background
              protocol: TCP
          lifecycle:
            preStop:
              exec:
                command:
                  - sh
                  - c
                  - "sleep 5"
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 20
            successThreshold: 1
            tcpSocket:
              port: 9351
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 20
            successThreshold: 1
            tcpSocket:
              port: 9351
            timeoutSeconds: 1
      restartPolicy: Always
      imagePullSecrets:
        - name: 152harbor

  selector:
    matchLabels:
      app: jic-product-background-center