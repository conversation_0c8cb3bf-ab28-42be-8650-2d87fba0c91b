apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    web: jnby-box-prod
  name: jnby-box-prod
  namespace: box
spec:
  replicas: {api-replicas}
  selector:
    matchLabels:
      web: jnby-box-prod
  template:
    metadata:
      labels:
        web: jnby-box-prod
      namespace: box
    spec:
      containers:
      - env:
        - name: prod
          value: prod
        image: jnbyharbor.jnby.com/box-group/jnby-master/jnby-box:latest
        imagePullPolicy: Always
        lifecycle:
          preStop:
            exec:
              command:
              - sh
              - c
              - sleep 5
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 30
          successThreshold: 1
          tcpSocket:
            port: 9088
          timeoutSeconds: 3
        name: jnby-box-prod
        ports:
        - containerPort: 9088
          name: 9088tcp
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 30
          successThreshold: 3
          tcpSocket:
            port: 9088
          timeoutSeconds: 1
      imagePullSecrets:
      - name: box-group