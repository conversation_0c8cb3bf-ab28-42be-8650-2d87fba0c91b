<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.spring.center.product</groupId>
    <artifactId>jic-product-center</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <properties>
        <java.version>1.8</java.version>
        <!-- 正常使用1.1 商品包也是在1.1 只有只会大屏1.2 -->
        <jic.product.version>1.7-SNAPSHOT</jic.product.version>
        <!-- 正常使用1.1 商品包也是在1.1 只有只会大屏1.2 升级到 1.4 -->

        <jnby.common.version>1.1.5</jnby.common.version>
        <jnby.boot.base>1.3.4-SNAPSHOT</jnby.boot.base>


        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.plugin.version>3.8.1</maven.plugin.version>

        <jic.reference.version>2.0.0</jic.reference.version>
        <jic.base.version>2.0.0</jic.base.version>
        <jic.rmq.version>2.0.0</jic.rmq.version>
        <jic.integralmall.version>2.0.0</jic.integralmall.version>
        <jic.job.version>2.0.0</jic.job.version>
        <jnbyimage.version></jnbyimage.version>
        <jic.image.version>${jnbyimage.version}</jic.image.version>
        <tx.queue>1.0.5</tx.queue>

        <spring.boot.version>2.2.12.RELEASE</spring.boot.version>
        <spring.cloud.version>Hoxton.SR9</spring.cloud.version>
        <spring.platform.version>Cairo-SR8</spring.platform.version>

        <!-- 推荐使用Harbor -->
        <docker.registry.url></docker.registry.url>
        <docker.registry.host>http://${docker.registry.url}:2375</docker.registry.host>
        <docker.plugin.version>1.2.0</docker.plugin.version>
    </properties>

    <modules>
        <module>jic-product-api</module>
        <!-- 分享业务 & 服务实现 -->
        <module>jic-product-api-center</module>
        <module>jic-product-center-gateway</module>
        <module>jic-product-center-unlimited-gateway</module>
        <module>jic-product-background-center</module>
        <module>jic-product-wsc-center</module>
        <module>jic-product-wsc-gateway</module>
        <module>jic-wsc-consumer-center</module>
        <module>jic-wsc-job-center</module>
        <module>jic-common-product</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.spring.center.reference</groupId>
                <artifactId>jic-core-boot</artifactId>
                <version>${jic.reference.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.spring.platform</groupId>
                <artifactId>platform-bom</artifactId>
                <version>${spring.platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>2.2.5.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.16</version>
            </dependency>

            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc6</artifactId>
                <version>11.2.0.3</version>
            </dependency>

            <dependency>
                <groupId>com.jnby</groupId>
                <artifactId>jnby-authority-api</artifactId>
                <version>${jnby.boot.base}</version>
            </dependency>

            <dependency>
                <groupId>com.jnby</groupId>
                <artifactId>tx-queue</artifactId>
                <version>1.0.5</version>
                <!--                <version>1.0.2</version>-->
            </dependency>
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>ST4</artifactId>
                <version>4.3.1</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>stringtemplate</artifactId>
                <version>3.2.1</version>
                <optional>true</optional>
            </dependency>
        </dependencies>
    </dependencyManagement>




    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>snapshots</id>
            <url>http://172.18.0.152:8081/repository/maven-snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>release</id>
            <name>Release Repository</name>
            <url>http://172.18.0.152:8081/repository/maven-releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>release</id>
            <url>http://172.18.0.152:8081/repository/maven-releases/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <!--正式版本-->
        <repository>
            <id>releases</id>
            <url>http://172.18.0.152:8081/repository/maven-releases/</url>
        </repository>
        <!--快照-->
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://172.18.0.152:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


</project>