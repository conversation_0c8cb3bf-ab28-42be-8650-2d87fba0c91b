<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.wsc.consumer.modules.mapper.product.JicProductMallClassifyMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.wsc.consumer.modules.model.product.JicProductMallClassify">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="weid" column="WEID" jdbcType="DECIMAL"/>
            <result property="mallId" column="MALL_ID" jdbcType="DECIMAL"/>
            <result property="pClassifyId" column="P_CLASSIFY_ID" jdbcType="DECIMAL"/>
            <result property="pClassifyName" column="P_CLASSIFY_NAME" jdbcType="VARCHAR"/>
            <result property="classifyId" column="CLASSIFY_ID" jdbcType="DECIMAL"/>
            <result property="classifyName" column="CLASSIFY_NAME" jdbcType="VARCHAR"/>
            <result property="classifyLevel" column="CLASSIFY_LEVEL" jdbcType="DECIMAL"/>
            <result property="oneLevelId" column="ONE_LEVEL_ID" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,WEID,MALL_ID,
        P_CLASSIFY_ID,P_CLASSIFY_NAME,CLASSIFY_ID,
        CLASSIFY_NAME,CLASSIFY_LEVEL,ONE_LEVEL_ID,
        CREATE_TIME,UPDATE_TIME,IS_DEL
    </sql>

    <select id="selectByVidAndMallIdTodayInfo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM USERWX.JIC_PRODUCT_MALL_CLASSIFY
        WHERE WEID = #{weId} AND MALL_ID = #{mallId}
          AND CLASSIFY_LEVEL = 2 AND IS_DEL = 0
    </select>
</mapper>
