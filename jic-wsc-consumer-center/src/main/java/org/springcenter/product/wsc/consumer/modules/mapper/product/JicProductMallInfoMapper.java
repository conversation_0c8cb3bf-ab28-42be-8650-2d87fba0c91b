package org.springcenter.product.wsc.consumer.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.wsc.consumer.modules.entity.VidBrandInfoDataEntity;
import org.springcenter.product.wsc.consumer.modules.entity.VidCidInfoDataEntity;
import org.springcenter.product.wsc.consumer.modules.model.product.JicProductMallInfo;

import java.util.List;


public interface JicProductMallInfoMapper extends BaseMapper<JicProductMallInfo> {


    List<JicProductMallInfo> selectByMallIdAndWeId(@Param("mallId") Long mallId, @Param("weId") Long weId);


    List<VidCidInfoDataEntity> selectVidCidValidProdInfo(@Param("vid") String vid, @Param("cid") String cid);

    List<VidBrandInfoDataEntity> selectVidAndNotCanSell(@Param("vid") String v, @Param("brandId") String cid);

    List<VidBrandInfoDataEntity> selectVidCidNotValidByVidAndBrandId(@Param("vid") String v, @Param("brandId") String cid);


    List<VidCidInfoDataEntity> selectVidValidProdInfo(@Param("vid") String vid, @Param("cid") String cid);
}
