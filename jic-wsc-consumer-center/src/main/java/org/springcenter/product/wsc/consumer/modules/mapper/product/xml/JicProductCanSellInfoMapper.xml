<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.wsc.consumer.modules.mapper.product.JicProductCanSellInfoMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.wsc.consumer.modules.model.product.JicProductCanSellInfo">
            <result property="id" column="ID"/>
            <result property="weId" column="WEID" jdbcType="DECIMAL"/>
            <result property="mallId" column="MALL_ID" jdbcType="DECIMAL"/>
            <result property="isCanSell" column="IS_CANSELL" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,WEID,MALL_ID,
        IS_CANSELL,CREATE_TIME,UPDATE_TIME,
        IS_DELETED
    </sql>



    <select id="selectByWeIdAndGoodsIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
            from JIC_PRODUCT_CANSELL_INFO
        WHERE WEID = #{weId}
        AND MALL_ID IN
            <foreach item="item" index="index" collection="list"
                    open="(" close=")" separator=",">
                #{item}
            </foreach>
        AND IS_DELETED = 0
    </select>
    <select id="getId" resultType="java.lang.Long" useCache="false" flushCache="true">
        select seq_JICPROCANSELL_ID.nextval from dual
    </select>


    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO JIC_PRODUCT_CANSELL_INFO
            (ID, WEID, MALL_ID, IS_CANSELL, CREATE_TIME, UPDATE_TIME)
            VALUES
            (#{item.id}, #{item.weId}, #{item.mallId}, #{item.isCanSell},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>


    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update JIC_PRODUCT_CANSELL_INFO
            set
            IS_CANSELL = #{item.isCanSell},
            UPDATE_TIME = #{item.updateTime}
            where ID = #{item.id}
        </foreach>
    </update>


</mapper>
