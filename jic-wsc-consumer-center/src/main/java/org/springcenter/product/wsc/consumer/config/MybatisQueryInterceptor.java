package org.springcenter.product.wsc.consumer.config;

import com.alibaba.druid.DbType;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.statement.SQLSelectStatement;
import com.alibaba.druid.sql.dialect.oracle.parser.OracleStatementParser;
import com.alibaba.druid.sql.parser.SQLStatementParser;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.RoutingStatementHandler;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.*;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.Arrays;
import java.util.List;


/**
 * 拦截mybatis的查询语句，限制最大返回2000条数据
 */
@Component
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class MybatisQueryInterceptor implements Interceptor {
     private static final List<String> KEY_WORD = Arrays.asList("count(", "limit", "sum(", "avg(", "min(", "max(", "rownum","nextval");

     private static final List<String> FILTER_WORD = Arrays.asList("intersect");
     /**
      * 拦截后要执行的方法
      */
     public Object intercept(Invocation invocation) throws Throwable {
          /*
           我们在PageInterceptor类上已经用@Signature标记了该Interceptor只拦截StatementHandler接口的prepare方法，又因为Mybatis只有在建立RoutingStatementHandler的时候
           是通过Interceptor的plugin方法进行包裹的，所以我们这里拦截到的目标对象肯定是RoutingStatementHandler对象。
           */
          RoutingStatementHandler handler = (RoutingStatementHandler) invocation.getTarget();
          //通过反射获取到当前RoutingStatementHandler对象的delegate属性
          StatementHandler delegate = (StatementHandler) ReflectUtil.getFieldValue(handler, "delegate");
          //RoutingStatementHandler实现的所有StatementHandler接口方法里面都是调用的delegate对应的方法。
          BoundSql boundSql = delegate.getBoundSql();

          String sql = boundSql.getSql();
          if(FILTER_WORD.stream().anyMatch(sql.toLowerCase()::contains)){
               // 放行特殊查询
               return invocation.proceed();
          }
          //拿到当前绑定Sql的参数对象，就是我们在调用对应的Mapper映射语句时所传入的参数对象
          SQLStatementParser sqlStatementParser;
          if (getDbType(delegate) == DbType.oracle) {
               sqlStatementParser = new OracleStatementParser(sql);
          } else {
               sqlStatementParser = new SQLStatementParser(sql,getDbType(delegate));
          }
          SQLStatement statement = sqlStatementParser.parseStatement();
          //这里我们简单的通过传入的是Page对象就认定它是需要进行分页操作的。
          if (statement instanceof SQLSelectStatement){
               // 排除分页语句
               if(KEY_WORD.stream().noneMatch(sql.toLowerCase()::contains)){
                    Page<Object> page = new Page<>();
                    page.setPageNo(1);
                    page.setPageSize(10000);
                    //获取分页Sql语句
                    String pageSql = this.getPageSql(page, sql,sqlStatementParser.getDbType());
                    //利用反射设置当前BoundSql对应的sql属性为我们建立好的分页Sql语句
                    ReflectUtil.setFieldValue(boundSql, "sql", pageSql);
               }
          }
          return invocation.proceed();

     }

     /**
      * 拦截器对应的封装原始对象的方法
      */
     public Object plugin(Object target) {
          return Plugin.wrap(target, this);
     }


     /**
      * 通过反射获取数据库类型
      */
     private DbType getDbType(StatementHandler delegate){
          Executor executor =  (Executor) ReflectUtil.getFieldValue(delegate, "executor");
          DruidDataSource dataSource =  (DruidDataSource) ReflectUtil.getFieldValue(executor.getTransaction(), "dataSource");
          String dbType = dataSource.getDbType();
          return dbType.equals("mysql")? DbType.mysql:DbType.oracle;
     }

     /**
      * 根据page对象获取对应的分页查询Sql语句，这里只做了两种数据库类型，Mysql和Oracle
      * 其它的数据库都 没有进行分页
      *
      * @param page 分页对象
      * @param sql  原sql语句
      * @return
      */
     private String getPageSql(Page<?> page, String sql, DbType dbType) {

          StringBuffer sqlBuffer = new StringBuffer(sql);

          if (dbType.equals(DbType.mysql)) {
               return getMysqlPageSql(page, sqlBuffer);
          } else if (dbType.equals(DbType.oracle)) {
               return getOraclePageSql(page, sqlBuffer);
          }

          return sqlBuffer.toString();

     }

     /**
      * 获取Mysql数据库的分页查询语句
      *
      * @param page      分页对象
      * @param sqlBuffer 包含原sql语句的StringBuffer对象
      * @return Mysql数据库分页语句
      */
     private String getMysqlPageSql(Page<?> page, StringBuffer sqlBuffer) {

          //计算第一条记录的位置，Mysql中记录的位置是从0开始的。
          int offset = (page.getPageNo() - 1) * page.getPageSize();
          sqlBuffer.append(" limit ").append(offset).append(",").append(page.getPageSize());
          return sqlBuffer.toString();

     }

     /**
      * 获取Oracle数据库的分页查询语句
      *
      * @param page      分页对象
      * @param sqlBuffer 包含原sql语句的StringBuffer对象
      * @return Oracle数据库的分页查询语句
      */
     private String getOraclePageSql(Page<?> page, StringBuffer sqlBuffer) {

          //计算第一条记录的位置，Oracle分页是通过rownum进行的，而rownum是从1开始的
          int offset = (page.getPageNo() - 1) * page.getPageSize() + 1;
          sqlBuffer.insert(0, "select u.*, rownum r from (").append(") u where rownum < ").append(offset + page.getPageSize());
          sqlBuffer.insert(0, "select * from (").append(") where r >= ").append(offset);
          return sqlBuffer.toString();

     }


     /**
      * 利用反射进行操作的一个工具类
      */
     private static class ReflectUtil {

          /**
           * 利用反射获取指定对象的指定属性
           *
           * @param obj       目标对象
           * @param fieldName 目标属性
           * @return 目标属性的值
           */
          public static Object getFieldValue(Object obj, String fieldName) {

               Object result = null;

               Field field = ReflectUtil.getField(obj, fieldName);

               if (field != null) {
                    field.setAccessible(true);
                    try {
                         result = field.get(obj);
                    } catch (IllegalArgumentException e) {
                         e.printStackTrace();
                    } catch (IllegalAccessException e) {
                         e.printStackTrace();
                    }
               }
               return result;

          }


          /**
           * 利用反射获取指定对象里面的指定属性
           *
           * @param obj       目标对象
           * @param fieldName 目标属性
           * @return 目标字段
           */
          private static Field getField(Object obj, String fieldName) {

               Field field = null;

               for (Class<?> clazz = obj.getClass(); clazz != Object.class; clazz = clazz.getSuperclass()) {
                    try {
                         field = clazz.getDeclaredField(fieldName);
                         break;
                    } catch (NoSuchFieldException e) {
                         //这里不用做处理，子类没有该字段可能对应的父类有，都没有就返回null。
                    }
               }
               return field;

          }


          /**
           * 利用反射设置指定对象的指定属性为指定的值
           *
           * @param obj        目标对象
           * @param fieldName  目标属性
           * @param fieldValue 目标值
           */
          public static void setFieldValue(Object obj, String fieldName, String fieldValue) {
               Field field = ReflectUtil.getField(obj, fieldName);
               if (field != null) {
                    try {
                         field.setAccessible(true);
                         field.set(obj, fieldValue);
                    } catch (IllegalArgumentException e) {
                         e.printStackTrace();
                    } catch (IllegalAccessException e) {
                         e.printStackTrace();
                    }
               }
          }
     }


}