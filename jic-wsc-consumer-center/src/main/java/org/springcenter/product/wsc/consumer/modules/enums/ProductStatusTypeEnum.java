package org.springcenter.product.wsc.consumer.modules.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date:2024/4/29 13:41
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ProductStatusTypeEnum {

    // 0:不可售 1可售
    /**
     * 是否可售
     */
    CAN_SELL(0, "是否可售"),
    /**
     * 是否上架
     */
    PUTWAY(1, "是否上架"),;

    private Integer code;

    private String desc;
}
