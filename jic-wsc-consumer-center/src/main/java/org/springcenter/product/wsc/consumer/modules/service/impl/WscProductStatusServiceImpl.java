package org.springcenter.product.wsc.consumer.modules.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.wsc.consumer.modules.entity.*;
import org.springcenter.product.wsc.consumer.modules.mapper.product.JicProductCanSellInfoMapper;
import org.springcenter.product.wsc.consumer.modules.mapper.product.JicProductMallInfoMapper;
import org.springcenter.product.wsc.consumer.modules.model.product.JicProductCanSellInfo;
import org.springcenter.product.wsc.consumer.modules.service.WscProductStatusService;
import org.springcenter.product.wsc.consumer.util.ConstantUtil;
import org.springcenter.product.wsc.consumer.util.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Date:2024/12/10 13:08
 */
@Slf4j
@Service
@RefreshScope
public class WscProductStatusServiceImpl implements WscProductStatusService {

    @Autowired
    private JicProductCanSellInfoMapper jicProductCanSellInfoMapper;

    @Value("${product.can.sell.leaf.tag}")
    private String productCanSellLeafTag;

    @Value("${redis.vid.cid.key}")
    private String redisVidCidKey;

    @Value("${redis.vid.key}")
    private String redisVidKey;

    @Autowired
    private RedisService redisService;

    @Override
    public Boolean dealProductIsCanSell(ProductIsCanSellEntity requestData) {
        try {
            ProductIsCanSellEntity.MsgBody body = requestData.getMsg_body();
            if (body == null) {
                log.info("dealProductIsCanSell body信息为空");
                return true;
            }

            if (StringUtils.isBlank(requestData.getBrandId())) {
                log.info("dealProductIsCanSell brandId为空");
                return true;
            }

            Long weid = Long.parseLong(requestData.getBrandId());
            Integer isCanSell = body.getIsCanSell();
            List<Long> goodsIdList = body.getGoodsIdList();
            if (CollectionUtils.isEmpty(goodsIdList) || isCanSell == null) {
                log.info("dealProductIsCanSell body信息为空");
                return true;
            }

            // 查询当前数据
            List<JicProductCanSellInfo> jicProductCanSellInfos = jicProductCanSellInfoMapper
                    .selectByWeIdAndGoodsIdList(weid, goodsIdList);

            List<JicProductCanSellInfo> inserts = new ArrayList<>();
            List<JicProductCanSellInfo> updates = new ArrayList<>();
            if (CollectionUtils.isEmpty(jicProductCanSellInfos)) {
                // 没有查到任何数据 需要批量插入
                goodsIdList.forEach(v -> {
                    buildData(weid, isCanSell, inserts, v);
                });
            } else {
                // 有数据，需要判断是否需要更新
                HashMap<Long, JicProductCanSellInfo> map = jicProductCanSellInfos.stream()
                        .collect(HashMap::new, (k, v) -> k.put(v.getMallId(), v), HashMap::putAll);
                if (MapUtils.isEmpty(map)) {
                    goodsIdList.forEach(v -> {
                        buildData(weid, isCanSell, inserts, v);
                    });
                } else {
                    goodsIdList.forEach(v -> {
                        if (map.get(v) == null) {
                            buildData(weid, isCanSell, inserts, v);
                        } else {
                            JicProductCanSellInfo jicProductCanSellInfo = map.get(v);
                            if (!Objects.equals(jicProductCanSellInfo.getIsCanSell(), isCanSell)) {
                                jicProductCanSellInfo.setIsCanSell(isCanSell);
                                jicProductCanSellInfo.setUpdateTime(new Date());
                                updates.add(jicProductCanSellInfo);
                            }
                        }
                    });
                }
            }

            if (CollectionUtils.isNotEmpty(inserts)) {
                jicProductCanSellInfoMapper.batchInsert(inserts);
            }

            if (CollectionUtils.isNotEmpty(updates)) {
                jicProductCanSellInfoMapper.batchUpdate(updates);
            }
            return true;
        } catch (Exception e) {
            log.error("dealProductIsCanSell error", e);
            return false;
        }

    }

    @Autowired
    private JicProductMallInfoMapper jicProductMallInfoMapper;

    @Override
    public Boolean dealVidCidProdInfo(VidCidInfoEntity requestData) {
        // 先删除数据

         /*String key = ConstantUtil.STORE_VID_CATE_ORDER + "5" + ":*";
        Object o = redisService.get(key);
        redisService.batchDel(key);*/
        String key = redisVidCidKey + requestData.getVid() + ConstantUtil.TRANSVERSE_LINE + requestData.getCid();
        Object o = redisService.sGet(key);
        log.info("dealVidCidProdInfo 删除redis key：{}, value:{}", key, o);
        redisService.del(key);


        List<VidCidInfoDataEntity> entities = jicProductMallInfoMapper.selectVidCidValidProdInfo(requestData.getVid(), requestData.getCid());
        List<String> values = new ArrayList<>();
        entities.forEach(entity -> {
            values.add(entity.getProductId() + ConstantUtil.SEMICOLON + 1 + ConstantUtil.SEMICOLON + 1);

        });
        redisService.sSet(key, values.toArray(new String[0]));
        log.info("dealVidCidProdInfo 批量增加redis key：{}, value:{}", key, values);

        return true;
    }


    @Override
    public Boolean dealVidProdInfo(VidCidInfoEntity requestData) {
        // 先删除数据
        String key = redisVidKey + requestData.getVid();
        Object o = redisService.sGet(key);
        log.info("dealVidCidProdInfo 删除redis key：{}, value:{}", key, o);
        redisService.del(key);


        List<VidCidInfoDataEntity> entities = jicProductMallInfoMapper.selectVidValidProdInfo(requestData.getVid(), requestData.getCid());
        List<String> values = new ArrayList<>();
        entities.forEach(entity -> {
            values.add(entity.getProductId() + ConstantUtil.SEMICOLON + 1 + ConstantUtil.SEMICOLON + 1);

        });
        redisService.sSet(key, values.toArray(new String[0]));
        log.info("dealVidCidProdInfo 批量增加redis key：{}, value:{}", key, values);

        return true;
    }

    @Override
    public Boolean updateVidCidProdInfo(DealVidCidProdInfoEntity requestData) {
        String key = redisVidCidKey + requestData.getVid() + ConstantUtil.TRANSVERSE_LINE + requestData.getCid();
        Set<Object> o  = redisService.sGet(key);
        log.info("updateVidCidProdInfo redis key：{}, value:{}", key, o);


        List<RedisProdStatusEntity> redisProdStatusEntities = new ArrayList<>();
        o.forEach(x -> {
            String s = Objects.toString(x);
            String[] split = StringUtils.split(s, ConstantUtil.SEMICOLON);
            RedisProdStatusEntity entity1 = new RedisProdStatusEntity();
            entity1.setProductId(Long.valueOf(split[0]));
            entity1.setIsPutway(Integer.valueOf(split[1]));
            entity1.setIsCanSell(Integer.valueOf(split[2]));
            redisProdStatusEntities.add(entity1);
        });

        HashMap<Long, RedisProdStatusEntity> map = redisProdStatusEntities.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getProductId(), v), HashMap::putAll);
        requestData.getProductIds().forEach(v -> {
            if (MapUtils.isEmpty(map) || !map.containsKey(Long.valueOf(v))) {
                return;
            }
            RedisProdStatusEntity entity = map.get(Long.valueOf(v));
            String value = entity.getProductId() + ConstantUtil.SEMICOLON + entity.getIsPutway() + ConstantUtil.SEMICOLON + entity.getIsCanSell();
            redisService.setRemove(key, value);
        });
        return true;
    }

    private void buildData(Long weid, Integer isCanSell, List<JicProductCanSellInfo> inserts, Long mallId) {
        JicProductCanSellInfo jicProductCanSellInfo = new JicProductCanSellInfo();
        jicProductCanSellInfo.setId(jicProductCanSellInfoMapper.getId());
        jicProductCanSellInfo.setIsCanSell(isCanSell);
        jicProductCanSellInfo.setWeId(weid);
        jicProductCanSellInfo.setMallId(mallId);
        jicProductCanSellInfo.setCreateTime(new Date());
        jicProductCanSellInfo.setUpdateTime(new Date());
        inserts.add(jicProductCanSellInfo);
    }
}
