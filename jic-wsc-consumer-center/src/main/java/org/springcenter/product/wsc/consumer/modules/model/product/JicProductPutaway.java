package org.springcenter.product.wsc.consumer.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2024/8/16 15:55
 */
@Data
@TableName(value = "USERWX.JIC_PRODUCT_PUTAWAY")
public class JicProductPutaway {

    @TableId(value = "ID")
    private Long id;

    @TableField(value = "WEID")
    private String weId;

    @TableField(value = "PRODUCT_ID")
    private Long productId;

    @TableField(value = "PRODUCT_NO")
    private String productNo;

    @TableField(value = "MALL_PRODUCT_ID")
    @ApiModelProperty(value = "微商城商品id")
    private String mallProductId;

    @TableField(value = "STOREID")
    @ApiModelProperty(value = "伯俊门店id")
    private Long storeId;

    @TableField(value = "MALL_STOREID")
    @ApiModelProperty(value = "微商城门店id")
    private Long mallStoreId;

    @TableField(value = "PUTWAY")
    @ApiModelProperty(value = "微商城是都上架 0上架 1下架")
    private Integer putway;

    @TableField(value = "CREATE_TIME")
    private Date createTime;
}