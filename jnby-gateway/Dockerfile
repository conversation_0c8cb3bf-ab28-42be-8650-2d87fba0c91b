FROM jnbyharbor.jnby.com/base-images/baseimagejdk8:v1.3

MAINTAINER box-group

RUN mkdir -p /jnby-master/jnby-gateway

WORKDIR /jnby-master/jnby-gateway

EXPOSE 9301

ADD ./target/jnby-gateway.jar ./jnby-gateway.jar

#COPY --from=hengyunabc/arthas:latest /opt/arthas /opt/arthas

ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java", "-Xmx2g", "-Xms2g","-XX:NewRatio=3","-Xss512k", "-Xmn1g","-XX:SurvivorRatio=2", "-XX:+UseParallelGC","-Dreactor.netty.pool.leasingStrategy=lifo", "-jar", "jnby-gateway.jar"]

CMD ["--spring.profiles.active=test"]
