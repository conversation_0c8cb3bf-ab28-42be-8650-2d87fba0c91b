package com.jnby;

import com.jnby.load.DynamicRouteLoader;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import reactor.netty.resources.LoopResources;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/6/26
 * <p>
 */
@EnableDiscoveryClient
@SpringBootApplication
public class GatewayApp  implements CommandLineRunner {
    @Resource
    private DynamicRouteLoader dynamicRouteLoader;

    public static void main(String[] args) {
        SpringApplication.run(GatewayApp.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        dynamicRouteLoader.refresh();
    }
}
