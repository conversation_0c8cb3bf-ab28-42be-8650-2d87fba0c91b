package com.jnby.common.util.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.ListUtils;

import java.nio.charset.Charset;
import java.util.Date;
import java.util.List;

/**
 * Excel读取工具类，支持大文件读取
 */
public class EasyExcelUtil {

    /**
     * 读取Excel列
     * @param path
     * @param clazz
     * @param readerListener
     * @param <T>
     */
    public static <T> void read(String path,  Class<T> clazz, BaseAbstractReaderListener readerListener){
        EasyExcel.read(path, clazz, readerListener).sheet().doRead();
    }

    public static <T> void readCsv(String path,  Class<T> clazz, BaseAbstractReaderListener readerListener){
        EasyExcel.read(path, clazz, readerListener).excelType(ExcelTypeEnum.CSV).sheet().doRead();
    }

    /**
     * 不创建对象读
     * {0=童装, 1=5G450034, 2=001, 3=null, 4=null, 5=长裙, 6=null, 7=null, 8=null, 9=null, 10=null, 11=null, 12=null, 13=null, 14=null, 15=null, 16=null, 17=null, 18=null}
     * @param path
     * @param readerListener
     */
    public static void read(String path, BaseNoModelDataAbstractListener readerListener){
        EasyExcel.read(path, readerListener).sheet().doRead();
    }

    public static <T> void write(String path, Class<T> tClass, IWriteDataExcel<T> writeDataExcel){
        EasyExcel.write(path, tClass).head(tClass).sheet("sheet1").doWrite(writeDataExcel.getData());
    }

    static class DemoData{
        @ExcelProperty({"标题"})
        private String string;

        @ExcelProperty({"日期"})
        private Date date;

        @ExcelProperty({"数字"})
        private Double doubleData;

        public String getString() {
            return string;
        }

        public void setString(String string) {
            this.string = string;
        }

        public Date getDate() {
            return date;
        }

        public void setDate(Date date) {
            this.date = date;
        }

        public Double getDoubleData() {
            return doubleData;
        }

        public void setDoubleData(Double doubleData) {
            this.doubleData = doubleData;
        }
    }
    public static void main(String[] args) {
        String url = "/Users/<USER>/Downloads/abc12356.xlsx";
//        EasyExcelUtil.read(url, new BaseNoModelDataAbstractListener() {
//            @Override
//            protected void saveData() {
//                System.out.println(this.cachedDataList.get(0));
//            }
//        });
        EasyExcelUtil.write(url, DemoData.class, new IWriteDataExcel<DemoData>() {
            @Override
            public List<DemoData> getData() {
                List<DemoData> list = ListUtils.newArrayList();
                for (int i = 0; i < 10; i++) {
                    DemoData data = new DemoData();
                    data.setString("字符串" + i);
                    data.setDate(new Date());
                    data.setDoubleData(0.56);
                    list.add(data);
                }
                return list;
            }
        });
    }
}
