package com.jnby.common.util.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * 抽象读取工具类
 */
public abstract class BaseAbstractReaderListener<T> implements ReadListener<T> {
    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    private String fileName = null;
    /**
     * 缓存的数据
     */
    protected List<T> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);


    public BaseAbstractReaderListener() {
        // 这里是demo，所以随便new一个。实际使用如果到了spring,请使用下面的有参构造函数
    }

    public BaseAbstractReaderListener(String fileName) {
        this.fileName = fileName;
    }
    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(T data, AnalysisContext context) {
        cachedDataList.add(data);
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        //这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        if (fileName != null){
            try {
                Files.deleteIfExists(Paths.get(fileName));
            } catch (IOException e) {

            }
        }
    }

    /**
     * 从cachedDataList获取数据进行业务逻辑处理
     */
    protected abstract void saveData();
}
