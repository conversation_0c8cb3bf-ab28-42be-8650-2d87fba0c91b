package com.jnby.common.hystrix.test;

import com.jnby.common.hystrix.AbstractAsyncServiceCommand;

public class ServiceACommand extends AbstractAsyncServiceCommand<String,String> {
    private static final String GROUP_KEY = "ServiceA";
    private static final int TIMEOUT = 1000;
    public ServiceACommand() {
        super(G<PERSON><PERSON>_KEY, TIMEOUT);
    }

    @Override
    protected String executeService() {
        try {
            System.out.println("ServiceACommand param: " + this.getParam());
            System.out.println("ServiceACommand started at: " + System.currentTimeMillis());
            Thread.sleep(500); // 模拟耗时操作
            System.out.println("ServiceACommand finished at: " + System.currentTimeMillis());
        } catch (InterruptedException e) {
            // 处理异常
        }
        return "ServiceACommand....";
    }

    @Override
    protected String handleFallback() {
        return "Fallback result for Service A";
    }
}
