package org.springcenter.product.wsc.job.modules.product.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.common.system.vo.DictModel;
import com.jnby.common.util.IdLeaf;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.core.tool.utils.BeanUtil;
import org.springcenter.core.tool.utils.CollectionUtil;
import org.springcenter.product.api.dto.wsc.VidBdCateListResp;
import org.springcenter.product.api.dto.wsc.VidBdHeadResp;
import org.springcenter.product.api.dto.wsc.VidProductEsSalesSearchResp;
import org.springcenter.product.api.dto.wsc.VidProductSalesResp;
import org.springcenter.product.api.enums.wsc.ProductStatusEnum;
import org.springcenter.product.wsc.job.modules.entity.DealRedisBrandInfoReq;
import org.springcenter.product.wsc.job.modules.entity.GenerateBdInfoReq;
import org.springcenter.product.wsc.job.modules.entity.MallProductVo;
import org.springcenter.product.wsc.job.modules.entity.VidBdListResp;
import org.springcenter.product.wsc.job.modules.mapper.product.JicMallOrgMapper;
import org.springcenter.product.wsc.job.modules.mapper.product.JicVidBdInfoMapper;
import org.springcenter.product.wsc.job.modules.mapper.product.JicVidBdListInfoMapper;
import org.springcenter.product.wsc.job.modules.model.JicVidBdInfo;
import org.springcenter.product.wsc.job.modules.model.JicVidBdListInfo;
import org.springcenter.product.wsc.job.modules.product.service.ITestGenerateBdListService;
import org.springcenter.product.wsc.job.modules.remote.entity.WmGoodInfoReq;
import org.springcenter.product.wsc.job.modules.remote.entity.WmGoodInfoResp;
import org.springcenter.product.wsc.job.modules.remote.service.IWmService;
import org.springcenter.product.wsc.job.util.ConstantUtil;
import org.springcenter.product.wsc.job.util.EsUtil;
import org.springcenter.product.wsc.job.util.RedisService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.SimpleFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2025/2/20 11:24
 */
@Service
@Slf4j
@RefreshScope
public class TestGenerateBdListServiceImpl implements ITestGenerateBdListService {
    /**
     * 榜单图片封面图字典
     */
    private static final String BD_DICT_CODE = "mall_top_order_pic";

    @Autowired
    private EsUtil esUtil;

    @Autowired
    private JicMallOrgMapper jicMallOrgMapper;

    @Value("${small.class.id}")
    private String smallClassIds;

    @Value("${big.class.id}")
    private String bigClassIds;

    @Value("${vid.sales.index}")
    private String vidSalesIndex;

    @Value("${vid.head.img}")
    private String vidHeadImg;


    @Value("${vid.head.share.img}")
    private String vidHeadShareImg;

    @Value("${vid.product.bd.tag}")
    private String vidProductBdTag;

    @Value("${exclude.product.id}")
    private String excludeProductId;

    @Value("${vid.brand.bd.index}")
    private String vidBrandBdIndex;


    @Value("${vid.brand.name.bd.index}")
    private String vidBrandNameBdIndex;

    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private IWmService wmService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private JicVidBdListInfoMapper jicVidBdListInfoMapper;

    @Autowired
    private JicVidBdInfoMapper jicVidBdInfoMapper;

    @Autowired
    private ISysBaseAPI iSysBaseAPI;

    @Value("${vid.single.brand}")
    private String vidSingleBrand;


    @Override
    public Boolean generateBrandInfo(GenerateBdInfoReq request) {
        List<String> excludeLists = Arrays.stream(excludeProductId.split(",")).collect(Collectors.toList());
        List<String> singleBrands = Arrays.stream(vidSingleBrand.split(",")).collect(Collectors.toList());
        boolean contains = singleBrands.contains(Objects.toString(request.getBrandId()));

        // 根据品牌筛选门店信息
        // todo 测试限制一家门店 com.github.pagehelper.Page<String> hPage = PageHelper.startPage(1, 1);
        List<String> vids = jicMallOrgMapper.selectVidByBrand(Objects.toString(request.getBrandId()), request.getVid());
        if (CollectionUtils.isEmpty(vids)) {
            log.info("没有查到对应的门店id");
            return true;
        }

        List<String> smallClassList = Arrays.stream(smallClassIds.split(",")).collect(Collectors.toList());
        List<String> bigClassList = Arrays.stream(bigClassIds.split(",")).collect(Collectors.toList());
        Map<String, String> imgMap = JSONObject.parseObject(vidHeadImg, Map.class);
        Map<String, String> shareImgMap = JSONObject.parseObject(vidHeadShareImg, Map.class);

        String imgValue = imgMap.get(Objects.toString(request.getBrandId()));
        //获取数据字典中配置的榜单封面图
        List<DictModel> dictList = iSysBaseAPI.queryDictItemsByCode("mall_top_order_pic");
        if (CollectionUtils.isNotEmpty(dictList)) {
            //循环dictList，将dictList中的数据转换成Map
            Map<String, String> dictMap = dictList.stream().collect(Collectors.toMap(DictModel::getText, DictModel::getValue));
            if (dictMap.containsKey(Objects.toString(request.getBrandId()))) {
                imgValue = dictMap.get(Objects.toString(request.getBrandId()));
            }
        }

        String img = imgValue;
        String shareImg = imgValue;

        Map<String, String> brandMap = JSONObject.parseObject(vidBrandBdIndex, Map.class);
        String brandStr = brandMap.get(Objects.toString(request.getBrandId()));
        List<Long> brandList = Arrays.stream(brandStr.split(",")).map(v -> Long.valueOf(v)).collect(Collectors.toList());
        Map<String, String> brandNameMap = JSONObject.parseObject(vidBrandNameBdIndex, Map.class);

        List<JicVidBdInfo> vidBdInfos = new ArrayList<>();
        List<JicVidBdListInfo> vidBdListInfos = new ArrayList<>();
        // 根据门店处理es数据 不同分类同时查询 查询前50的数据 在进行微盟过滤
        vids.forEach(vid -> {
            log.info("根据门店处理es数据 vid:{}", vid);

            brandList.forEach(brand -> {
                List<CompletableFuture<List<VidProductEsSalesSearchResp>>> futures = new ArrayList<>();
                for (int i = 0; i < smallClassList.size(); i++) {
                    // 异步任务
                    int finalI = i;
                    CompletableFuture<List<VidProductEsSalesSearchResp>> salesFuture = CompletableFuture.supplyAsync(() -> {
                        // 模拟耗时操作
                        List<VidProductEsSalesSearchResp> rets = new ArrayList<>();
                        rets = getVidProductEsSalesSearchRespList(vid, smallClassList.get(finalI), contains ? null : brand);
                        return rets; // 返回数据
                    }, threadPoolTaskExecutor).exceptionally(e -> {
                        log.error("获取商品销量失败 门店：{}， 小类：{}, 品牌：{}", e, vid, smallClassList.get(finalI), brand);
                        return Collections.emptyList();
                    });
                    futures.add(salesFuture);
                }



                List<VidProductEsSalesSearchResp> rets = new ArrayList<>();
                futures.forEach(salesFuture -> {
                    try {
                        List<VidProductEsSalesSearchResp> searchResps = salesFuture.get();
                        rets.addAll(searchResps);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    } catch (ExecutionException e) {
                        throw new RuntimeException(e);
                    }
                });


                // 查询微盟 微盟限制数量 一次性只能查20条
                List<List<VidProductEsSalesSearchResp>> partition = Lists.partition(rets, 20);
                List<WmGoodInfoResp> allList = new ArrayList<>();
                partition.forEach(item -> {
                    if (excludeLists.contains(Objects.toString(item.get(0).getMall_id()))) {
                        return;
                    }
                    WmGoodInfoReq req = new WmGoodInfoReq();
                    req.setGoodsIdList(item.stream().map(v -> Objects.toString(v.getMall_id())).collect(Collectors.toList()));
                    req.setBrandId(item.get(0).getWeid());
                    req.setVid(Long.valueOf(vid));
                    req.setWid(1L);
                    req.setRequestId(UUID.randomUUID().toString());
                    List<WmGoodInfoResp> infoList = wmService.getGoodsInfoList(req);
                    allList.addAll(infoList);
                });
                if (CollectionUtils.isEmpty(allList)) {
                    log.info("没有查到对应的商品");
                    return;
                }

                List<VidProductSalesResp> stockInfoList = new ArrayList<>();
                HashMap<Long, WmGoodInfoResp> allWmInfoMap = allList.stream().collect(HashMap::new, (k, v) -> k.put(v.getGoodsId(), v), HashMap::putAll);
                rets.forEach(v -> {
                    if (excludeLists.contains(Objects.toString(v.getMall_id()))) {
                        return;
                    }
                    VidProductSalesResp resp = new VidProductSalesResp();
                    BeanUtils.copyProperties(v, resp);
                    WmGoodInfoResp infoResp = allWmInfoMap.get(v.getMall_id());
                    if (infoResp == null || infoResp.getGoodsStock() == null || infoResp.getGoodsStock().getIsAllStockEmpty()) {
                        return;
                    }
                    // 获取图片
                    if (infoResp != null && CollectionUtils.isNotEmpty(infoResp.getSkuSpecList())) {
                        List<MallProductVo.SkuSpecValue> skcList = getSkcList(infoResp.getSkuSpecList(), new HashSet<>());
                        resp.setSkuImgs(skcList.stream().map(MallProductVo.SkuSpecValue::getImageUrl).collect(Collectors.toList()));
                    }

                    resp.setDefaultImageUrl(infoResp.getDefaultImageUrl());
                    resp.setTitle(infoResp.getTitle());
                    resp.setStock(infoResp.getGoodsStock().getGoodsStockNum());
                    stockInfoList.add(resp);
                });

                if (CollectionUtils.isEmpty(stockInfoList)) {
                    log.info("没有查到对应的商品库存");
                    return;
                }


                Map<String, List<VidProductSalesResp>> bigType = stockInfoList.stream()
                        .collect(Collectors.groupingBy(VidProductSalesResp::getBig_cate_id));

                // 表头榜单按照门店处理
                // 列表数据 redis按vid+大类id存储    20250630修改成列表根据vid-brandId-大类id
                VidBdHeadResp headResp = new VidBdHeadResp();
                headResp.setBdImg(img);
                headResp.setShareImg(shareImg);
                // 如果是江南布衣+ || 奥莱 则 需要返回品牌信息
                if (Objects.equals(request.getBrandId(), 5L) || Objects.equals(request.getBrandId(), 11L)) {
                    List<VidBdHeadResp.BrandData> proBrandList = new ArrayList<>();
                    brandList.forEach(prob -> {
                        VidBdHeadResp.BrandData brandData = new VidBdHeadResp.BrandData();
                        brandData.setBrandId(prob);
                        String brandName = brandNameMap.get(Objects.toString(prob));
                        brandData.setBrandName(brandName);
                        proBrandList.add(brandData);
                    });
                    headResp.setBrandList(proBrandList);
                }

                List<VidBdHeadResp.CateData> bigCate = new ArrayList<>();

                // 数据库保存
                JicVidBdInfo vidBdInfo = new JicVidBdInfo();
                vidBdInfo.setId(IdLeaf.getDateId(vidProductBdTag));
                vidBdInfo.setVid(vid);
                // 门店品牌
                vidBdInfo.setBrandId(Objects.toString(request.getBrandId()));
                // 商品品牌
                vidBdInfo.setProBrandId(brand);
                vidBdInfo.setIsDeleted(0);
                vidBdInfo.setUpdateTime(new Date());
                vidBdInfo.setCreateTime(new Date());

                List<String> smallNumClassList = new ArrayList<>();

                // 先根据 商品品牌分类

                bigClassList.forEach(v -> {
                    // 处理大类
                    VidBdHeadResp.CateData cateData = new VidBdHeadResp.CateData();
                    cateData.setClassId(v);
                    // 默认a开头为大类
                    cateData.setIsHome(v.contains("A") ? true : false);

                    // 所有小类
                    List<VidProductSalesResp> smallList = bigType.get(v);
                    // 根据小类处理
                    if (CollectionUtils.isEmpty(smallList)) {
                        log.info("没有查到对应的商品库存");
                        return;
                    }
                    Map<String, List<VidProductSalesResp>> smallMap = smallList.stream()
                            .collect(Collectors.groupingBy(VidProductSalesResp::getSmall_cate_id));

                    VidBdListResp vidRet = new VidBdListResp();
                    vidRet.setVid(vid);
                    vidRet.setBigClassId(v);
                    vidRet.setProBrandId(brand);

                    VidBdCateListResp resp1 = new VidBdCateListResp();
                    resp1.setVid(vid);
                    resp1.setBigClassId(v);
                    resp1.setProBrandId(brand);
                    List<VidBdListResp.SmallData> smallDataList = new ArrayList<>();

                    smallMap.entrySet().forEach(entry -> {
                        vidRet.setBigClassName(entry.getValue().get(0).getBig_cate_type());
                        cateData.setClassName(entry.getValue().get(0).getBig_cate_type());
                        VidBdListResp.SmallData resp = new VidBdListResp.SmallData();
                        resp.setNumSize(entry.getValue().size());
                        Long reduce = entry.getValue().stream().map(VidProductSalesResp::getSales_num).reduce(0L, Long::sum);
                        resp.setSaleNum(reduce);
                        resp.setProductList(entry.getValue());
                        resp.setSmallClassId(entry.getValue().get(0).getSmall_cate_id());
                        resp.setSmallClassName(entry.getValue().get(0).getPro_small_type());


                        resp1.setBigClassName(entry.getValue().get(0).getBig_cate_type());
                        resp1.setNumSize(entry.getValue().size());
                        resp1.setSaleNum(reduce);
                        resp1.setProductList(entry.getValue());
                        resp1.setSmallClassId(entry.getValue().get(0).getSmall_cate_id());
                        resp1.setSmallClassName(entry.getValue().get(0).getPro_small_type());
                        if (entry.getValue().size() >= 5) {
                            // 存库
                            JicVidBdListInfo info = new JicVidBdListInfo();
                            info.setId(IdLeaf.getDateId(vidProductBdTag));
                            info.setVid(vid);
                            info.setProBrandId(brand);
                            info.setBigCateId(v);
                            info.setSmallCateId(resp.getSmallClassId());
                            info.setIsDeleted(0);
                            info.setCreateTime(new Date());
                            info.setUpdateTime(new Date());
                            info.setBrandId(Objects.toString(request.getBrandId()));
                            info.setBdList(JSONObject.toJSONString(resp1));
                            vidBdListInfos.add(info);

                            smallDataList.add(resp);
                            redisService.set(ConstantUtil.STORE_SMALL_CATE_LIST + Objects.toString(request.getBrandId())
                                            + ConstantUtil.COLON + vid + ConstantUtil.TRANS_LINE + brand + ConstantUtil.TRANS_LINE +
                                            resp1.getBigClassId() + ConstantUtil.TRANS_LINE + resp1.getSmallClassId(),
                                    JSONObject.toJSONString(resp1));

                            AtomicInteger i = new AtomicInteger(1);
                            entry.getValue().forEach(vv -> {
                                String value =  resp1.getBigClassId() + ConstantUtil.TRANS_LINE +
                                        resp1.getSmallClassId() + ConstantUtil.TRANS_LINE +
                                        resp1.getSmallClassName() + ConstantUtil.TRANS_LINE +
                                        vv.getMall_id() + ConstantUtil.TRANS_LINE + i.getAndIncrement();
                                smallNumClassList.add(value);
                            });

                        }
                    });
                    if (CollectionUtils.isEmpty(smallDataList)) {
                        return;
                    }
                    vidRet.setSmallDataList(smallDataList);

                    // 处理小类数据 相同按上架时间
                    List<VidBdListResp.SmallData> sortedSmallClass = smallDataList.stream()
                            .sorted(Comparator.comparing(VidBdListResp.SmallData::getSaleNum).reversed())
                            .collect(Collectors.toList());
                    List<VidBdHeadResp.CateData> smallClass = new ArrayList<>();
                    sortedSmallClass.stream().forEach(x -> {
                        VidBdHeadResp.CateData data = new VidBdHeadResp.CateData();
                        data.setClassId(x.getSmallClassId());
                        data.setClassName(x.getSmallClassName());
                        smallClass.add(data);
                    });

                    cateData.setSmallClassList(smallClass);
                    cateData.setImg(sortedSmallClass.get(0).getProductList().get(0).getDefaultImageUrl());
                    bigCate.add(cateData);
                });
                headResp.setBigClassList(bigCate);

                // 缓存头部
                redisService.set(ConstantUtil.STORE_BD_CATE_HEAD + Objects.toString(request.getBrandId())
                                + ConstantUtil.COLON + vid + ConstantUtil.COLON + brand,
                        JSONObject.toJSONString(headResp));


                // 处理数据
                redisService.set(ConstantUtil.STORE_VID_CATE_ORDER + Objects.toString(request.getBrandId())
                                + ConstantUtil.COLON + vid + ConstantUtil.COLON+ brand + ConstantUtil.COLON,
                        JSONObject.toJSONString(smallNumClassList));


                vidBdInfo.setBdHead(JSONObject.toJSONString(headResp));
                vidBdInfo.setBdProdOrder(JSONObject.toJSONString(smallNumClassList));
                vidBdInfos.add(vidBdInfo);

            });
        });

        // 处理数据库数据
        /*List<JicVidBdInfo> vidBdInfos = new ArrayList<>();
        List<JicVidBdListInfo> vidBdListInfos = new ArrayList<>();*/
        if (CollectionUtils.isNotEmpty(vidBdInfos)) {
            Lists.partition(vidBdInfos, 900).stream().forEach(v -> {
                List<String> searchVids = v.stream().map(JicVidBdInfo::getVid).collect(Collectors.toList());
                List<JicVidBdInfo> list = jicVidBdInfoMapper.selectByVids(searchVids);
                List<JicVidBdInfo> inserts = new ArrayList<>();
                /*if (CollectionUtils.isNotEmpty(list)) {
                    HashMap<String, JicVidBdInfo> map = list.stream().collect(HashMap::new,
                            (k, va) -> k.put(va.getVid(), va), HashMap::putAll);
                    list.forEach(x -> {
                        if (MapUtils.isNotEmpty(map) && map.get(x.getVid()) != null) {
                            updates.add(x);
                        } else {
                            inserts.add(x);
                        }
                    });
                } else {
                    inserts.addAll(v);
                }*/
                inserts.addAll(v);

                if (CollectionUtils.isNotEmpty(list)) {
                    jicVidBdInfoMapper.batchUpdate(list);
                }
                if (CollectionUtils.isNotEmpty(inserts)) {
                    jicVidBdInfoMapper.batchInsert(inserts);
                }


            });
        }

        if (CollectionUtils.isNotEmpty(vidBdListInfos)) {
            Lists.partition(vidBdListInfos, 900).stream().forEach(v -> {
                List<String> searchVids = v.stream().map(JicVidBdListInfo::getVid).collect(Collectors.toList());
                List<JicVidBdListInfo> list = jicVidBdListInfoMapper.selectByVids(searchVids);
                List<JicVidBdListInfo> inserts = new ArrayList<>();
                List<JicVidBdListInfo> updates = new ArrayList<>();
                /*if (CollectionUtils.isNotEmpty(list)) {
                    HashMap<String, JicVidBdListInfo> map = list.stream().collect(HashMap::new,
                            (k, va) -> k.put(va.getVid(), va), HashMap::putAll);
                    list.forEach(x -> {
                        if (MapUtils.isNotEmpty(map) && map.get(x.getVid()) != null) {
                            updates.add(x);
                        } else {
                            inserts.add(x);
                        }
                    });
                } else {
                    inserts.addAll(v);
                }*/

                inserts.addAll(v);
                if (CollectionUtils.isNotEmpty(list)) {
                    jicVidBdListInfoMapper.batchUpdate(list);
                }

                if (CollectionUtils.isNotEmpty(inserts)) {
                    jicVidBdListInfoMapper.batchInsert(inserts);
                }


            });
        }

        return true;
    }

    @Override
    public Boolean delRedisBrandInfo(DealRedisBrandInfoReq requestData) {
        String key = requestData.getKey() + ":" + requestData.getBrandId() + ":*";
        // Object o = redisService.get(key);
        log.info("delRedisBrandInfo key:{}", key);
        redisService.batchDel(key);
        return true;
    }


    private List<VidProductEsSalesSearchResp> getVidProductEsSalesSearchRespList(String vid, String smallClassId, Long brandId) {
        List<VidProductEsSalesSearchResp> rets = new ArrayList<>();
        SearchRequest request = new SearchRequest();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(vid)) {
            queryBuilder.filter(QueryBuilders.termQuery("vid", vid));
        }
        if (StringUtils.isNotBlank(smallClassId)) {
            queryBuilder.filter(QueryBuilders.termsQuery("small_cate_id", smallClassId));
        }

        if (brandId != null) {
            queryBuilder.filter(QueryBuilders.termQuery("brand_id", brandId));
        }

        if (Objects.equals(brandId, 8348044436L)) {
            queryBuilder.filter(QueryBuilders.termQuery("is_home", 1));
        }
        queryBuilder.must(QueryBuilders.rangeQuery("sales_num").gt(0));

        queryBuilder.filter(QueryBuilders.termQuery("is_can_sell", ProductStatusEnum.CAN_ALLOW.getCode()));
        queryBuilder.filter(QueryBuilders.termQuery("is_putaway", ProductStatusEnum.CAN_ALLOW.getCode()));

        sourceBuilder.sort("sales_num", SortOrder.DESC);

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(50);

        request.indices(vidSalesIndex);
        request.source(sourceBuilder);

        // 使用主分片查询
        request.preference("primary");
        try {
            log.info("查询微商城门店七天商品销量信息 query = {}", request.source().toString());
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return Collections.emptyList();
            }


            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                VidProductEsSalesSearchResp entity = VidProductEsSalesSearchResp.fromJson(hit.getSourceAsString(), VidProductEsSalesSearchResp.class);
                if (entity != null) {
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date = formatter.parse(entity.getCreate_time());
                    entity.setCreateTime(date);
                    rets.add(entity);
                }
            }
        } catch (IOException e) {
            log.error("==================查询微商城门店七天商品销量信息失败：{}", e);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        return rets.stream().distinct()
                .sorted(Comparator.comparing(VidProductEsSalesSearchResp::getSales_num).reversed()
                .thenComparing(VidProductEsSalesSearchResp::getCreateTime))
                .collect(Collectors.toList());
    }




    private List<MallProductVo.SkuSpecValue> getSkcList(List<WmGoodInfoResp.SpecInfo> specInfoList, Set<String> colorCodeSets) {
        if (CollectionUtil.isEmpty(specInfoList)) {
            return com.beust.jcommander.internal.Lists.newArrayList();
        }
        WmGoodInfoResp.SpecInfo colorSpecInfo = specInfoList.stream().filter(spec ->
                        BooleanUtils.isTrue(spec.getSpecImgEnable()) && org.apache.commons.lang3.StringUtils.equals("颜色", spec.getSpecName()))
                .findFirst().orElse(null);
        if (Objects.isNull(colorSpecInfo)) {
            // 没有有效的skc
            return Collections.emptyList();
        }
        // skc列表
        List<WmGoodInfoResp.SpecInfo.SkuSpecValue> skuSpecValueList = colorSpecInfo.getSkuSpecValueList();
        if (CollectionUtil.isEmpty(skuSpecValueList)) {
            return com.beust.jcommander.internal.Lists.newArrayList();
        }
        // 如果图片链接一样，保留名称较短的
        skuSpecValueList = distinctByImg(skuSpecValueList);
        // skc联动，用户筛选红色，则红色skc前置
        List<MallProductVo.SkuSpecValue> skcList = com.beust.jcommander.internal.Lists.newArrayList();
        skuSpecValueList.forEach(spec -> {
            MallProductVo.SkuSpecValue skc = BeanUtil.copy(spec, MallProductVo.SkuSpecValue.class);
            skc.setSpecId(colorSpecInfo.getSpecId());
            skc.setColorCode(getSpecColorCode(spec.getSpecValueName()));
            skc.setIsHit(colorCodeSets.contains(skc.getColorCode()) ? 1 : 0);
            skcList.add(skc);
        });
        // 命中的skc前置展示
        skcList.sort(Comparator.comparing(MallProductVo.SkuSpecValue::getIsHit).reversed());
        return skcList;
    }


    private static List<WmGoodInfoResp.SpecInfo.SkuSpecValue> distinctByImg(List<WmGoodInfoResp.SpecInfo.SkuSpecValue> specList) {
        try {
            return new ArrayList<>(specList.stream().collect(Collectors.toMap(
                    WmGoodInfoResp.SpecInfo.SkuSpecValue::getImageUrl,
                    v -> v, (v1, v2) -> v1.getSpecValueName().length() < v2.getSpecValueName().length() ? v1 : v2,
                    LinkedHashMap::new)).values());
        } catch (Exception e) {
            log.error("distinctByImg error, specList:{},e:", specList, e);
        }
        return specList;
    }

    private static final String COLOR_CODE_PATTERN = "^[0-9]+";

    private static String getSpecColorCode(String specValueName) {
        if (org.apache.commons.lang3.StringUtils.isBlank(specValueName)) {
            return "";
        }
        Matcher matcher = Pattern.compile(COLOR_CODE_PATTERN).matcher(specValueName);
        return matcher.find() ? matcher.group() : specValueName;
    }
}
