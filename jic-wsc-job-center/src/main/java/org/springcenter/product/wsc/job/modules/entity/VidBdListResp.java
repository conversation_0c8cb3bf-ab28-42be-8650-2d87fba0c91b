package org.springcenter.product.wsc.job.modules.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springcenter.product.api.dto.wsc.VidProductSalesResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/2/21 13:42
 */
@Data
public class VidBdListResp {
    @ApiModelProperty(value = "大类id")
    private String bigClassId;

    @ApiModelProperty(value = "大类名称")
    private String bigClassName;

    @ApiModelProperty(value = "组织id")
    private String vid;

    @ApiModelProperty(value = "小类信息列表")
    private List<SmallData> smallDataList;

    @ApiModelProperty(value = "商品品牌id")
    private Long proBrandId;


    @Data
    public static class SmallData {
        @ApiModelProperty(value = "小类id")
        private String smallClassId;

        @ApiModelProperty(value = "小类名称")
        private String smallClassName;

        @ApiModelProperty(value = "商品数量")
        private Integer numSize;

        @ApiModelProperty(value = "销量")
        private Long saleNum;

        @ApiModelProperty(value = "商品列表")
        private List<VidProductSalesResp> productList;

    }


}
