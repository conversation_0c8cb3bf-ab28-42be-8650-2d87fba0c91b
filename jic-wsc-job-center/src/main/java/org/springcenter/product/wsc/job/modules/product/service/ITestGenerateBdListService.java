package org.springcenter.product.wsc.job.modules.product.service;

import org.springcenter.product.wsc.job.modules.entity.DealRedisBrandInfoReq;
import org.springcenter.product.wsc.job.modules.entity.GenerateBdInfoReq;

/**
 * <AUTHOR>
 * @Date:2025/2/20 11:24
 */
public interface ITestGenerateBdListService {
    /**
     * 根据品牌生成榜单信息
     * @param req 入参
     * @return 返回
     */
    Boolean generateBrandInfo(GenerateBdInfoReq req);

    /**
     * 删除redis品牌信息
     * @param requestData 入参
     * @return 返回
     */
    Boolean delRedisBrandInfo(DealRedisBrandInfoReq requestData);
}
