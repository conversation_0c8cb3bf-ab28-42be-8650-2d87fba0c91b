package org.springcenter.product.wsc.job.job;

import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.wsc.job.modules.entity.GenerateBdInfoReq;
import org.springcenter.product.wsc.job.modules.product.service.ITestGenerateBdListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date:2025/2/20 10:35
 */
@Slf4j
@Component
public class GenerateLessVidPdJob extends IJobHandler {

    @Autowired
    private ITestGenerateBdListService testGenerateBdListService;

    @Override
    @XxlJob("generateLessVidPdJob")
    public void execute() throws Exception {
        log.info("generateLessVidPdJob 开始");
        GenerateBdInfoReq req = new GenerateBdInfoReq();
        req.setBrandId(2822095692L);
        testGenerateBdListService.generateBrandInfo(req);
        log.info("generateLessVidPdJob 结束");
    }
}
