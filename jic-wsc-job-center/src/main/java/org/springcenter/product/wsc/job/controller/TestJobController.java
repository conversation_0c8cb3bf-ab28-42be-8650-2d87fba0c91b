package org.springcenter.product.wsc.job.controller;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.wsc.job.modules.entity.DealRedisBrandInfoReq;
import org.springcenter.product.wsc.job.modules.entity.GenerateBdInfoReq;
import org.springcenter.product.wsc.job.modules.product.service.ITestGenerateBdListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date:2025/2/20 10:30
 */
@Slf4j
@RestController
@RequestMapping("/product/job/api")
@Api(value = "TestJobApi",tags = "测试job接口")
public class TestJobController {

    @Autowired
    private ITestGenerateBdListService testGenerateBdListService;


    @ApiOperation(value = "根据品牌生成榜单信息")
    @RequestMapping(value = "/generateBrandInfo",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<Boolean> generateBrandInfo(@RequestBody CommonRequest<GenerateBdInfoReq> request){
        return ResponseResult.success(testGenerateBdListService.generateBrandInfo(request.getRequestData()));
    }


    @ApiOperation(value = "手动删除品牌下某个数据")
    @RequestMapping(value = "/delRedisBrandInfo",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<Boolean> delRedisBrandInfo(@RequestBody CommonRequest<DealRedisBrandInfoReq> request){
        return ResponseResult.success(testGenerateBdListService.delRedisBrandInfo(request.getRequestData()));
    }
}
