package org.springcenter.product.wsc.job.modules.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date:2025/7/1 14:28
 */
@Data
public class DealRedisBrandInfoReq {

    @ApiModelProperty(value = "品牌id")
    @NotNull(message = "品牌id不能为空")
    private Long brandId;

    @ApiModelProperty(value = "redis key")
    @NotEmpty(message = "redis key不能为空")
    private String key;
}
