<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.wsc.job.modules.mapper.product.JicVidBdInfoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.wsc.job.modules.model.JicVidBdInfo">
        <id column="ID" property="id" />
        <result column="VID" property="vid" />
        <result column="BRAND_ID" property="brandId" />
        <result column="BD_HEAD" property="bdHead"  jdbcType="CLOB" />
        <result column="BD_PROD_ORDER" property="bdProdOrder"  jdbcType="CLOB" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="PRO_BRAND_ID" property="proBrandId" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, VID, BRAND_ID, BD_HEAD, BD_PROD_ORDER, IS_DELETED,
        CREATE_TIME, UPDATE_TIME, PRO_BRAND_ID
    </sql>

    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO JIC_VID_BD_INFO
            (ID, VID, BRAND_ID, BD_HEAD, BD_PROD_ORDER, IS_DELETED,
            CREATE_TIME, UPDATE_TIME, PRO_BRAND_ID) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.vid,jdbcType=VARCHAR}, #{item.brandId,jdbcType=VARCHAR},
            #{item.bdHead,jdbcType=CLOB}, #{item.bdProdOrder,jdbcType=CLOB}, #{item.isDeleted,jdbcType=DECIMAL},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.proBrandId})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>


    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update JIC_VID_BD_INFO
            set
            IS_DELETED = 1,
            UPDATE_TIME = sysdate
            where ID = #{item.id}
        </foreach>
    </update>

    <select id="selectByVids" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
            FROM JIC_VID_BD_INFO
        WHERE VID in
        <foreach collection="vids" item="item" index="index"  open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND IS_DELETED = 0
        ORDER BY CREATE_TIME, ID DESC
    </select>


</mapper>
