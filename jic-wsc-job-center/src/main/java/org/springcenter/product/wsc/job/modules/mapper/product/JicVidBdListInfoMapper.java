package org.springcenter.product.wsc.job.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.wsc.job.modules.model.JicVidBdListInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/2/26 9:25
 */
public interface JicVidBdListInfoMapper extends BaseMapper<JicVidBdListInfo> {
    List<JicVidBdListInfo> selectByVids(@Param("vids") List<String> searchVids);


    void batchInsert(@Param("list") List<JicVidBdListInfo> inserts);

    void batchUpdate(@Param("list") List<JicVidBdListInfo> updates);
}
