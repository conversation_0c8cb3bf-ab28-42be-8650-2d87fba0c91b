package org.springcenter.product.wsc.job.modules.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2025/2/25 17:49
 */
@Data
@TableName(value = "JIC_VID_BD_INFO")
public class JicVidBdListInfo {

    @TableId(value = "ID")
    private String id;

    @TableField(value = "VID")
    private String vid;

    @TableField(value = "BRAND_ID")
    @ApiModelProperty(value = "品牌id")
    private String brandId;

    @TableField(value = "PRODUCT_BRAND_ID")
    @ApiModelProperty(value = "商品品牌id")
    private Long proBrandId;

    @TableField(value = "BIG_CATE_ID")
    @ApiModelProperty(value = "大类id")
    private String bigCateId;

    @TableField(value = "SMALL_CATE_ID")
    @ApiModelProperty(value = "小类id")
    private String smallCateId;

    @TableField(value = "BD_LIST")
    @ApiModelProperty(value = "小类id")
    private String bdList;

    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;
}
