package org.springcenter.product.wsc.job.modules.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/2/21 11:03
 */
@Data
public class WmGoodInfoResp {
    @ApiModelProperty("商品 ID")
    private Long goodsId;

    @ApiModelProperty("商品名称")
    private String title;

    @ApiModelProperty("商品子标题")
    private String subGoodsTitle;

    @ApiModelProperty("线下销量")
    private Integer initialSaleNum;

    @ApiModelProperty("商品已支付销量")
    private Integer payGoodsNum;

    @ApiModelProperty("商品下单量，为线下销量+线上销量")
    private Integer totalGoodsNum;

    @ApiModelProperty("商品销售模式1-现货；2- 预售；3-抽签购。")
    private Integer soldType;

    @ApiModelProperty("是否限购,true限购")
    private Boolean limitSwitch;

    @ApiModelProperty("当前组织 ID")
    private Long vid;

    @ApiModelProperty("二级商品类型101-普通商品；102-海淘商品；103-无需物流实物商品；201-普通虚拟商品；202-付费券虚拟商品；203-预约到店；204-知识付费。")
    private Integer subGoodsType;

    @ApiModelProperty("当前商品创建组织的组织类型2-品牌；10-门店。")
    private Integer createVidType;

    @ApiModelProperty("当前商品创建组织的组织 ID")
    private Long createVid;

    @ApiModelProperty("外部商品编码，是商户自有 ERP 系统的商品编码")
    private String outerGoodsCode;

    @ApiModelProperty("商品主图 URL")
    private String defaultImageUrl;

    @ApiModelProperty("商品图片 URL 列表")
    private List<String> goodsImageUrl;

    @ApiModelProperty("商品销售渠道类型 1-线上；2-线下；3-线上+线下")
    private Integer saleChannelType;

    @ApiModelProperty("商品库存信息")
    private GoodsStockInfo goodsStock;

    @ApiModelProperty("一级商品类型")
    private Integer goodsType;

    @ApiModelProperty("商品是否隐藏")
    private Integer goodsShowType;

    @ApiModelProperty("是否已分配到当前门店")
    private Boolean isAssigned;

    @ApiModelProperty("商品活动标签")
    private List<ActivityTag> activityTags;

    @ApiModelProperty("商品价格")
    private GoodsPriceInfo goodsPrice;

    @ApiModelProperty("库存减扣方式,1-下单减库存；2-支付减库存。")
    private Integer deductStockType;

    @ApiModelProperty("商品类目 ID")
    private Long categoryId;

    @ApiModelProperty("是否上架")
    private Boolean isOnline;

    @ApiModelProperty("是否可售")
    private Boolean isCanSell;

    @ApiModelProperty("是否多规格商品")
    private Boolean isMultiSku;

    @ApiModelProperty("是否删除")
    private Boolean isDeleted;

    @ApiModelProperty("商品视频url")
    private String goodsVideoUrl;
    @ApiModelProperty("商品视频主图url")
    private String goodsVideoImageUrl;

    @ApiModelProperty("sku信息")
    private List<Sku> skuList;

    @ApiModelProperty("商品规格列表")
    private List<SpecInfo> skuSpecList;
    @ApiModelProperty("商品标签")
    private GoodsTag goodsTag;

    @Data
    public static class GoodsTag {
        private Long tagId;
        @ApiModelProperty("商品标签")
        private String name;
        @ApiModelProperty("售完标识显示状态0-显示1-不显示")
        private Integer soldOutLogoDisplayStatus;
    }

    @Data
    public static class GoodsStockInfo {
        @ApiModelProperty("是否存在空库存")
        private Boolean existNullStock;
        @ApiModelProperty("是否全部 SKU 库存为空")
        private Boolean isAllStockEmpty;
        @ApiModelProperty("预售的商品库存数量")
        private Integer preGoodsStockNum;
        @ApiModelProperty("商品可售库存(全部sku库存之和)")
        private Integer goodsStockNum;
    }

    @Data
    public static class ActivityTag {
        @ApiModelProperty("活动ID")
        private Long activityId;
        @ApiModelProperty("活动状态")
        private Integer activityStatus;
        @ApiModelProperty("活动标签（自定义标签）")
        private String fullTagText;
        @ApiModelProperty("标签简称")
        private String tagText;
        @ApiModelProperty("活动类型")
        private Integer activityType;
        @ApiModelProperty("活动标签全称")
        private String tagName;
        @ApiModelProperty("二级活动类型")
        private Integer subActivityType;
        @ApiModelProperty("限时折扣预热是否可原价购买0-不可原价购买，1-可")
        private Integer isPreheatCanOriginPriceBuy;
    }

    @Data
    public static class GoodsPriceInfo {
        @ApiModelProperty("最小市场价,所有sku中的最小市场价")
        private BigDecimal minMarketPrice;
        private ActivityPriceInfo activityPrice;
        @ApiModelProperty("会员价格")
        private MemberPriceInfo memberPrice;
        @ApiModelProperty("最小销售价")
        private BigDecimal minSalePrice;
        @ApiModelProperty("最大市场价")
        private BigDecimal maxMarketPrice;
        @ApiModelProperty("最大销售价")
        private BigDecimal maxSalePrice;
        @ApiModelProperty("最小销售价格对应的商品 SKU 规格 ID")
        private Long minSalePriceSkuId;
    }

    @Data
    public static class ActivityPriceInfo {
        @ApiModelProperty("活动ID")
        private Long activityId;
        @ApiModelProperty("最小活动价")
        private BigDecimal minActivityPrice;
        @ApiModelProperty("最小活动价格对应的商品 SKU 规格 ID")
        private Long minActivityPriceSkuId;
        @ApiModelProperty("最大活动价")
        private BigDecimal maxActivityPrice;
        @ApiModelProperty("活动类型")
        private Integer activityType;
    }

    @Data
    public static class MemberPriceInfo {
        @ApiModelProperty("活动ID")
        private Long activityId;
        @ApiModelProperty("最小会员价")
        private BigDecimal minActivityPrice;
        @ApiModelProperty("最小会员价对应的商品 SKU 规格 ID")
        private Long minActivityPriceSkuId;
        @ApiModelProperty("最小会员价格对应的商品 SKU 的销售价")
        private BigDecimal minActivityPriceSkuSalePrice;
        @ApiModelProperty("最大会员价")
        private BigDecimal maxActivityPrice;
        @ApiModelProperty("活动类型")
        private Integer activityType;
    }


    @Data
    public static class Sku {

        private Long skuId;
        @ApiModelProperty("sku名称")
        private String title;
        @ApiModelProperty("sku默认图片")
        private String defaultImgUrl;
        @ApiModelProperty("sku图片")
        private List<String> imageUrl;
        @ApiModelProperty("规格编码")
        private String outerSkuCode;
        @ApiModelProperty("规格条码")
        private String skuBarCode;
        @ApiModelProperty("sku是否禁用")
        private Boolean isDisabled;
        @ApiModelProperty("市场价")
        private BigDecimal marketPrice;
        @ApiModelProperty("销售价")
        private BigDecimal salePrice;
        @ApiModelProperty("成本价")
        private BigDecimal costPrice;
        @ApiModelProperty("sku库存数")
        private Integer skuStockNum;
        @ApiModelProperty("预售数量")
        private Integer skuPreStockNum;
        @ApiModelProperty("重量")
        private String weight;
        @ApiModelProperty("体积")
        private String volume;
    }

    @Data
    public static class SpecInfo {
        @ApiModelProperty("规格ID")
        private Long specId;
        @ApiModelProperty("规格名称")
        private String specName;
        @ApiModelProperty("是否可添加规格图片")
        private Boolean specImgEnable;
        @ApiModelProperty("规格值信息")
        private List<SkuSpecValue> skuSpecValueList;

        @Data
        public static class SkuSpecValue {
            @ApiModelProperty("商品规格值 ID")
            private Long specValueId;
            @ApiModelProperty("规格值名称")
            private String specValueName;
            @ApiModelProperty("规格图片 URL")
            private String imageUrl;
            @ApiModelProperty("规格值图片列表")
            private List<String> imageUrls;
        }
    }
}
