package org.springcenter.product.common.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.springcenter.product.common.service.ProductDetailInfoService;
import org.springcenter.product.common.service.es.ProductParamSalesInfoEsResp;
import org.springcenter.product.common.util.CommonEsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date:2025/3/13 13:44
 */
@Service
@Slf4j
public class ProductDetailInfoServiceImpl implements ProductDetailInfoService {

    @Autowired
    private CommonEsUtil esUtil;

    public ProductParamSalesInfoEsResp searchProductSalesInfo(String requestData, String index) {

        GetRequest request = new GetRequest(index, requestData);
        ProductParamSalesInfoEsResp resp = null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                resp = ProductParamSalesInfoEsResp.fromJson(response.getSourceAsString(), ProductParamSalesInfoEsResp.class);
                resp.buildFabricKzData();
                resp.buildMaterialData();
                resp.buildServeFunctionData();
                resp.buildLabels();
                resp.buildLabelLevels();
                resp.buildWashInfo();
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品详情的信息异常e = {}", e.getMessage());
        }
        return resp;
    }
}
