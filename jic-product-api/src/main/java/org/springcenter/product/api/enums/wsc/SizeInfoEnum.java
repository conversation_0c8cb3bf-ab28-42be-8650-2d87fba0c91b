package org.springcenter.product.api.enums.wsc;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date:2024/4/28 9:59
 * 品牌枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SizeInfoEnum {
    // 0:不可售 1可售
    /**
     * 鞋子
     */
    CLOTH(0, "衣服"),
    /**
     * 衣服
     */
    SHOE(1, "鞋子"),
    /**
     * 其他
     */
    OTHER(2, "其他"),;

    private Integer code;

    private String desc;
}
