package org.springcenter.product.api;


import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.ApiOperation;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.factory.ProductStoreApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Component
@FeignClient(contextId = "productStoreRemoteApi", value = "jic-product-api-center", fallbackFactory = ProductStoreApiFallbackFactory.class)
public interface IProductStoreApi {

    /**
     * 查询门店下SKC
     * @param request
     * @return
     */
    @PostMapping("/product/store/api/queryStoreGoodSkc")
    ResponseResult<List<ProductSkcResp>> queryStoreGoodSkc(@RequestBody CommonRequest<QueryGoodsSkcListReq> request);

    /**
     * 查询微商城门店下SKC
     * @param request
     * @return
     */
    @PostMapping("/product/store/api/queryStoreMallGoodSkc")
    ResponseResult<List<ProductSkcResp>> queryStoreMallGoodSkc(@RequestBody CommonRequest<QueryGoodsSkcListReq> request);

    /**
     * 查询门店下商品数据
     * @param request
     * @return
     */
    @PostMapping("/product/store/api/getGoodsListByStore")
    ResponseResult<List<StoreGoodSpuResp>> getGoodsListByStore(@RequestBody CommonRequest<StoreGoodsReq> request);

    /**
     * 获取微商城门店下SKC数据
     * @param request
     * @return
     */
    @PostMapping("/product/store/api/queryVidMallGoodSkc")
    ResponseResult<List<ProductVidSkcResp>> queryVidMallGoodSkc(@RequestBody CommonRequest<QueryGoodsSkcListReq> request);


}
