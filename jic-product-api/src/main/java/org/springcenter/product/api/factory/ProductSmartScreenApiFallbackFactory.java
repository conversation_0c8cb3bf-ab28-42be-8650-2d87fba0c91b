package org.springcenter.product.api.factory;

import feign.hystrix.FallbackFactory;
import org.springcenter.product.api.IProductSmartScreenApi;
import org.springcenter.product.api.fallback.ProductSmartScreenApiFallback;
import org.springframework.stereotype.Component;

@Component
public class ProductSmartScreenApiFallbackFactory implements FallbackFactory<IProductSmartScreenApi> {

    @Override
    public IProductSmartScreenApi create(Throwable throwable) {
        ProductSmartScreenApiFallback fallback = new ProductSmartScreenApiFallback();
        fallback.setCause(throwable);
        return fallback;
    }
}
