package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date:2023/5/17 15:46
 */
@Data
public class ProductByWeiMenInfoReq {

    @NotBlank(message = "weId不能为空")
    @ApiModelProperty(value = "小程序品牌id")
    private String weId;

    @NotBlank(message = "微盟goodId不能为空")
    @ApiModelProperty(value = "微盟goodId")
    private String weiMenGoodId;

    @NotBlank(message = "微盟skuId不能为空")
    @ApiModelProperty(value = "微盟skuId")
    private String weiMenSkuId;
}