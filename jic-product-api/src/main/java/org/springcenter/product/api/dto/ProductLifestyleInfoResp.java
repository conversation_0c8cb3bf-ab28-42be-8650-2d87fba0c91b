package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/7/1 10:33
 */
@Data
public class ProductLifestyleInfoResp {

    @ApiModelProperty(value = "商品id")
    private Long productId;

    @ApiModelProperty(value = "商品tag")
    private String productTag;

    @ApiModelProperty(value = "商品标识")
    private Long productStatus;


    @ApiModelProperty(value = "正价 1  季末 2  奥莱 3")
    private Integer productAttr;
 }
