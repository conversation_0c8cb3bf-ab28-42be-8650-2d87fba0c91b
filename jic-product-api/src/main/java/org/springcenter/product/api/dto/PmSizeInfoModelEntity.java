package org.springcenter.product.api.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date:2023/12/22 16:25
 */
@Data
@ApiModel(value = "蓬马尺码表格导出")
@EqualsAndHashCode
public class PmSizeInfoModelEntity {

    @ExcelProperty(value = "款号", index = 0)
    private String style_id;

    @ExcelProperty(value = "年份", index = 1)
    private String year;

    @ExcelProperty(value = "季节", index = 2)
    private String sample_id;

    @ExcelProperty(value = "品牌", index = 3)
    private String d_pp;

    @ExcelProperty(value = "部位", index = 4)
    private String part;

    @ExcelProperty(value = "测量说明", index = 5)
    private String clff;

    // 110，120，130，140，150，160，165，170

    // @ExcelProperty(value = "/")
    @ExcelProperty(value = "110")
    private String model1;

    // @ExcelProperty(value = "110/50")
    @ExcelProperty(value = "120")
    private String model2;

    // @ExcelProperty(value = "110/52")
    @ExcelProperty(value = "130")
    private String model3;

    // @ExcelProperty(value = "120/53")
    @ExcelProperty(value = "140")
    private String model4;

    // @ExcelProperty(value = "120/56")
    @ExcelProperty(value = "150")
    private String model5;

    // @ExcelProperty(value = "130/56")
    @ExcelProperty(value = "160")
    private String model6;

    // @ExcelProperty(value = "130/60")
    @ExcelProperty(value = "165")
    private String model7;

    // @ExcelProperty(value = "140/55")
    @ExcelProperty(value = "170")
    private String model8;

    /*@ExcelProperty(value = "140/57")
    private String model9;

    @ExcelProperty(value = "140/64")
    private String model10;

    @ExcelProperty(value = "150/58")
    private String model11;

    @ExcelProperty(value = "150/60")
    private String model12;

    @ExcelProperty(value = "150/68")
    private String model13;

    @ExcelProperty(value = "160/64A")
    private String model14;

    @ExcelProperty(value = "160/66")
    private String model15;

    @ExcelProperty(value = "160/76")
    private String model16;

    @ExcelProperty(value = "160/80A")
    private String model17;

    @ExcelProperty(value = "165/66A")
    private String model18;

    @ExcelProperty(value = "165/84A")
    private String model19;

    @ExcelProperty(value = "170/70A")
    private String model20;

    @ExcelProperty(value = "170/84A")
    private String model21;*/
}
