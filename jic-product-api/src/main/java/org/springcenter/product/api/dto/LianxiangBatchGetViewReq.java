package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class LianxiangBatchGetViewReq {



    @ApiModelProperty(value = "是否为缩略图预览  false  否  ture 是 ")
    private boolean thumbtail = false ;

    @ApiModelProperty(value = "宽度")
    private String width;

    @ApiModelProperty(value = "高度")
    private String height;

    private String preview_type = "original";

    private List<BatchGetViewUrlReq.LianxiangData> file_array;
}
