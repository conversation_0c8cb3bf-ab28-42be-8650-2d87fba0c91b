package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 11:20
 */
@Data
@ApiModel(value = "经销库存查询")
public class ProductAgentStockReq {
    @ApiModelProperty(value = "门店id")
    @NotNull(message = "门店id为空")
    private List<Long> storeId;

    @ApiModelProperty(value = "规格id")
    @NotEmpty(message = "规格id不能为空")
    private List<Long> skuIds;
}
