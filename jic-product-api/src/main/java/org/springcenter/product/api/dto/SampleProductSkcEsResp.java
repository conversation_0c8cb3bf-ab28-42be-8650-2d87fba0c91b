package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/24 3:08 下午
 * @Version 1.0
 */
@Data
public class SampleProductSkcEsResp extends RemotingSerializable implements Serializable {
    private String id;
    private String sample_id;
    private String year;
    private String design_name;
    private String color_category;
    private String d_pp;
    private List<String> label_levels;
    private List<String> labels;
    private String img;
    //波段
    private String season;
    private String sample_code;
}
