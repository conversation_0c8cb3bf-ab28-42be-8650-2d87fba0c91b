package org.springcenter.product.api.dto.background.fab;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springcenter.product.api.dto.ProductSpuFabEsResp;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/10/25 17:02
 */
@Data
public class ProductSellingPointEsResp extends RemotingSerializable implements Serializable {
    @ApiModelProperty(value = "pruductId")
    private String id;

    @ApiModelProperty(value = "款号")
    private String name;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "fab说明")
    private String fab;

    @ApiModelProperty(value = "电商组合品名")
    private String dsPartAutoName;

    @ApiModelProperty(value = "组合品名参考")
    private String dsWholeAutoName;

    @ApiModelProperty(value = "数智组合品名")
    private String wscAutoName;

    @ApiModelProperty(value = "设计卖点")
    private String design_selling_point;

    @ApiModelProperty(value = "设计卖点解释")
    private String design_selling_explain;

    //-----------面料-----------------------
    @ApiModelProperty(value = "面料故事")
    private String fabric_story;

    @ApiModelProperty(value = "面料名称")
    private String bom_name;

    @ApiModelProperty(value = "成分面料")
    private String sjcf;

    @ApiModelProperty(value = "产品推广资料（内部参考）")
    private String sc_popularize;

    @ApiModelProperty(value = "产品推广资料（对外参考）")
    private String out_sc_popularize;

    @ApiModelProperty(value = "辅料推广资料（对内参考）")
    private String fl_sc_popularize;

    @ApiModelProperty(value = "辅料推广资料（外部参考）")
    private String acc_point;

    @ApiModelProperty(value = "不同skc名称图")
    private String color_name_list;

    @ApiModelProperty(value = "服用功能")
    private String sc_fzyt;

    @ApiModelProperty(value = "环保标签")
    private String sc_origin_area;

    @ApiModelProperty(value = "产品大类")
    private String scdl;

    @ApiModelProperty(value = "原料产地")
    private String sc_environmental;

    @ApiModelProperty(value = "面料进口地")
    private String bom_area;

    @ApiModelProperty(value = "羽绒填充进口")
    private String filling_area;

    @ApiModelProperty(value = "羽绒填充物")
    private String filler;

    @ApiModelProperty(value = "羽绒填充物")
    private String fill_content;

    @ApiModelProperty(value = "特殊砂织")
    private String special_weave;

    @ApiModelProperty(value = "组织")
    private String tex_ture;

    @ApiModelProperty(value = "针型")
    private String pin_type;

    @ApiModelProperty(value = "材料等级")
    private String sc_material_level;

    @ApiModelProperty(value = "硬挺度")
    private String sc_stiffness;

    @ApiModelProperty(value = "布面效果")
    private String sc_effect;

    @ApiModelProperty(value = "外观")
    private String facade;

    @ApiModelProperty(value = "硬挺度")
    private String sc_elastic;

    @ApiModelProperty(value = "环保挂牌说明")
    private String environmental_listing;


    @ApiModelProperty(value = "服用功能说明")
    private String sc_fzyt_explain;

    @ApiModelProperty(value = "成衣弹力")
    private String cloth_elastic;

    @ApiModelProperty(value = "不同色名称图片")
    private List<ColorName> colorNames;

    @Data
    public static class ColorName extends RemotingSerializable implements Serializable{
        @ApiModelProperty(value = "skc")
        private String skc_code;

        @ApiModelProperty(value = "色代码")
        private String color_code;

        @ApiModelProperty(value = "色名称")
        private String color_name;

        @ApiModelProperty(value = "图片")
        private String img;

        @ApiModelProperty(value = "透明抠图")
        private String matted_img;

        @ApiModelProperty(value = "keylook_logo")
        private String key_look_logo;

        @ApiModelProperty(value = "明星同款")
        private String star_logo;

        @ApiModelProperty(value = "定T30%")
        private String rate_logo;

        @ApiModelProperty(value = "自动风格")
        private String style_name;

        @ApiModelProperty(value = "面料号")
        private String bom_code;

        @ApiModelProperty(value = "面料名称")
        private String bom_name;

        @ApiModelProperty(value = "克重")
        private String gram_weight;
    }



    public void buildColorNameList(){
        if (this.getColor_name_list() == null || "".equals(this.getColor_name_list())){
            return;
        }
        this.colorNames = ProductSellingPointEsResp.ColorName.decodeArr(this.getColor_name_list(), ProductSellingPointEsResp.ColorName.class);
    }


    //------------图案----------------------
    @ApiModelProperty(value = "图案大类")
    private String pattern_type;

    @ApiModelProperty(value = "图案名称")
    private String pattern_name;

    @ApiModelProperty(value = "合作艺术家")
    private String artist;

    @ApiModelProperty(value = "图案灵感说明")
    private String inspirations;

    @ApiModelProperty(value = "文字含义说明")
    private String meaning_desc;

    @ApiModelProperty(value = "是否随机")
    private String is_random_pattern;


    //------------版型工艺卖点------------------
    @ApiModelProperty(value = "合体度")
    private String weidu;

    @ApiModelProperty(value = "体型")
    private String tixing;

    @ApiModelProperty(value = "领型")
    private String ling_xing;

    @ApiModelProperty(value = "袖型")
    private String xiu;

    @ApiModelProperty(value = "长度")
    private String changdu;

    @ApiModelProperty(value = "袖长")
    private String xiuchang;

    @ApiModelProperty(value = "特殊工艺小类")
    private String tyxl;

    @ApiModelProperty(value = "特殊工艺明细")
    private String tymx;

    @ApiModelProperty(value = "工艺细节")
    private String gyxj;

    @ApiModelProperty(value = "袖最大")
    private String xiu_max_size;

    @ApiModelProperty(value = "袖最小")
    private String xiu_min_size;

    @ApiModelProperty(value = "衣长最小")
    private String chang_min_size;

    @ApiModelProperty(value = "衣长最大")
    private String chang_max_size;

    @ApiModelProperty(value = "装饰工艺名词")
    private String special_craft;

    @ApiModelProperty(value = "横机工艺明细")
    private String join_craft;

    @ApiModelProperty(value = "是否有里")
    private String you_li;

    @ApiModelProperty(value = "口袋样式")
    private String kou_dai;

    @ApiModelProperty(value = "下摆设计")
    private String xia_bai;

    @ApiModelProperty(value = "开衩方式")
    private String kai_cha;

    @ApiModelProperty(value = "衣门襟")
    private String yi_men_jin;

    @ApiModelProperty(value = "下装门襟")
    private String ku_men_jin;

    @ApiModelProperty(value = "裤脚口")
    private String ku_jiao_kou;

    @ApiModelProperty(value = "绗缝切线")
    private String hfqx;

    //private String pattern_info_list;

    @ApiModelProperty(value = "图案相关信息")
    private List<ProductSpuFabEsResp.PatternInfo> pattern_info_list;
}
