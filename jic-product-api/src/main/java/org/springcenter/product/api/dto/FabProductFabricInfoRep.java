package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/7/1 16:13
 */
@Data
@ApiModel(value = "重点面料返回信息")
public class FabProductFabricInfoRep {

    @ApiModelProperty(value = "产品名称")
    private String bomName;

    @ApiModelProperty(value = "商检成分")
    private String sjcf;

    @ApiModelProperty(value = "产品推广材料")
    private String productPromotionMaterials;

    @ApiModelProperty(value = "品牌id不能为空")
    @NotEmpty(message = "品牌信息不能为空")
    private List<Long> brandIds;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "波段")
    private List<Long> bandIds = new ArrayList<>();

    public static List<FabProductFabricInfoRep> build(List<ProductSpuFabEsResp> resps) {
        if (CollectionUtils.isEmpty(resps)) {
            return Collections.emptyList();
        }
        List<FabProductFabricInfoRep> rets = new ArrayList<>();
        HashSet<FabProductFabricInfoRep> set = new HashSet<>();
        resps.forEach(v -> {
            FabProductFabricInfoRep rep = new FabProductFabricInfoRep();
            rep.setSjcf(v.getSjcf());
            rep.setBomName(v.getBom_name());
            rep.setProductPromotionMaterials(v.getSc_popularize());
            rets.add(rep);
            set.add(rep);
        });
        if (rets.size() == set.size()) {
            return rets;
        } else {
            return set.stream().collect(Collectors.toList());
        }

    }
}
