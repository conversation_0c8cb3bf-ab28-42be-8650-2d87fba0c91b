package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/7/6 15:09
 */
@Data
@ApiModel(value = "修改属性篇配置详情相关")
public class PVSaveQueryStatsDetailReq {

    @ApiModelProperty(value = "属性id")
    private Long attributeId;

    @ApiModelProperty(value = "属性名称")
    private String attributeName;

    @ApiModelProperty(value = "属性code")
    private String attributeCode;

    @ApiModelProperty(value = "属性值")
    private List<AttrValueData> attrValueList;

    @ApiModelProperty(value = "二级属性")
    private List<AttrValueData> secAttrList;

    @ApiModelProperty(value = "类目关联 - 只回传改变的")
    private List<CategoryData> categoryList;

    @ApiModelProperty(value = "二级属性应用")
    private List<CategoryData> secAttrConnectList;

    @Data
    public static class AttrValueData {
        @ApiModelProperty(value = "属性id")
        private Long attrId;

        @ApiModelProperty(value = "属性code")
        private String attrCode;

        @ApiModelProperty(value = "属性名称")
        private String attrDesc;

        @ApiModelProperty(value = "0是未选中 1是选中")
        private Integer isSelected;
    }

    @Data
    public static class CategoryData {
        @ApiModelProperty(value = "大类属性id")
        private Long bigCategoryId;

        @ApiModelProperty(value = "大类属性code")
        private String bigCategoryCode;

        @ApiModelProperty(value = "大类属性名称")
        private String bigCategoryDesc;

        @ApiModelProperty(value = "小类属性id")
        private Long smallCategoryId;

        @ApiModelProperty(value = "小类属性code")
        private String smallCategoryCode;

        @ApiModelProperty(value = "小类属性名称")
        private String smallCategoryDesc;

        @ApiModelProperty(value = "属性id")
        private Long attrId;

        @ApiModelProperty(value = "属性code")
        private String attrCode;

        @ApiModelProperty(value = "属性名称")
        private String attrDesc;

        @ApiModelProperty(value = "0是不选中 1是选中")
        private Integer isSelected;

        @ApiModelProperty(value = "关联id")
        private String id;
    }

}
