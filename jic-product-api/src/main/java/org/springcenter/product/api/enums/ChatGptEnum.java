package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date:2024/6/20 14:47
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum ChatGptEnum {

    PRODUCT_CENTER_FAB(0, "商品中心生成fab", "product-center-fab"),
    LOOK_FAB(1, "素材fab", "look-fab"),
    SAMPLE_CLO_FAB(0, "样衣生成fab", "sample-clo-fab"),;

    private Integer code;

    private String desc;

    private String englishDesc;
}
