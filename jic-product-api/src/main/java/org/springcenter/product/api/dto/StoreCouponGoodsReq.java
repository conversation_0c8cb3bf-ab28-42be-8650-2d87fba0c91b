package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/1 10:39 上午
 * @Version 1.0
 */
@Data
public class StoreCouponGoodsReq implements Serializable {

    @ApiModelProperty(value = "搜索的条码")
    private String productCode;

    @ApiModelProperty(value = "年份集合")
    private List<String> year;

    @ApiModelProperty(value = "品牌架构ID")
    private List<Long> m_brand_id;

    @ApiModelProperty(value = "条码")
    private String name;

    @ApiModelProperty(value = "关键字搜索")
    private String keywords;

    //排序方式，0 不排序、1，最新，2，销量排序
    @ApiModelProperty(value = "排序方式，0 不排序、1，最新，2，销量排序 4：券排序规则")
    private int sorted = 4;

    //库存方式 0 有库存，1 全部商品
    @ApiModelProperty(value = "门店库存筛选 1 门店有库存[但指门店store]，0 全部商品[默认这个 以其他qty真实的情况获取列表]")
    private int isStockNotEmpty = 0;

    @ApiModelProperty(value = "门店库存筛选：默认查询门店有货商品，false表示查询所有")
    private boolean isQty = true;

    @ApiModelProperty(value = "是否查询微商城有货商品；默认不过滤")
    private boolean isMallQty = false;

    @ApiModelProperty(value = "是否查询内淘有货商品；默认不过滤")
    private boolean isEbQty = false;

    @ApiModelProperty(value = "波段")
    private Long m_band_id;

    @ApiModelProperty(value = "波段id")
    private List<Long> m_band_ids;
    //大类
    @ApiModelProperty(value = "大类")
    private Long m_big_category_id;

    @ApiModelProperty(value = "小类")
    private Long m_small_category_id;

    @ApiModelProperty(value = "门店ID集合")
    private List<String> storeId;

    @ApiModelProperty(value = "商品ID")
    private List<String> productIds;

    @ApiModelProperty(value = "包含的商品标签集合")
    private List<String> mustLabels = new ArrayList<>();

    @ApiModelProperty(value = "包含的商品标签集合父code集合")
    private List<String> mustLabelLevels = new ArrayList<>();

    @ApiModelProperty(value = "商品标签Tag集合, 全部则不传 新品上市：NEW,热销TOP：TOP,促销折扣：DISCOUNT")
    private String productTag;

    @ApiModelProperty(value = "大类集合")
    private List<Long> mBigCategoryIds;

    @ApiModelProperty(value = "小类集合")
    private List<Long> mSmallCategoryIds;

    @ApiModelProperty(value = "生命周期 3705代表新品 3708代表N季末")
    private List<Long> lifeStyleIds;

    @ApiModelProperty(value = "排除商品ID")
    private List<String> excludeProductIds;


    @ApiModelProperty(value = "是否查询千万店城市仓有货商品；默认不过滤")
    private boolean isQwQty = false;

    @ApiModelProperty(value = "是否查询城市仓有货商品；默认不过滤")
    private boolean isCityQty = false;


    @ApiModelProperty(value = "券规则")
    @NotBlank(message = "券列表的券规则Id不能为空")
    private String couponRuleId;

    @ApiModelProperty(value = "券类型 正常券0 礼品券1")
    @NotBlank(message = "券类型不能为空")
    private Integer couponType = 0;
}
