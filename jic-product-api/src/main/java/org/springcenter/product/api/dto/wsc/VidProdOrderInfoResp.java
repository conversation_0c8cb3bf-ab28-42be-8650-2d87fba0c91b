package org.springcenter.product.api.dto.wsc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2025/2/24 11:27
 */
@Data
public class VidProdOrderInfoResp {
    @ApiModelProperty(value = "微盟组织id")
    private String vid;

    @ApiModelProperty(value = "商品id")
    private String mallId;

    @ApiModelProperty(value = "小类名称")
    private String smallCateName;

    @ApiModelProperty(value = "小类排序")
    private String orderNum;

    @ApiModelProperty(value = "小类名称")
    private String smallCateId;

    @ApiModelProperty(value = "大类名称")
    private String bigCateId;

    @ApiModelProperty(value = "商品品牌id")
    private Long proBrandId;
}
