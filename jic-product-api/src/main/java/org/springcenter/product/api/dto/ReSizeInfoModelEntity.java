package org.springcenter.product.api.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date:2023/12/22 16:25
 */
@Data
@ApiModel(value = "RE;RE;RE;LAB尺码表格导出")
@EqualsAndHashCode
public class ReSizeInfoModelEntity {

    @ExcelProperty(value = "款号", index = 0)
    private String style_id;

    @ExcelProperty(value = "年份", index = 1)
    private String year;

    @ExcelProperty(value = "季节", index = 2)
    private String sample_id;

    @ExcelProperty(value = "品牌", index = 3)
    private String d_pp;

    @ExcelProperty(value = "部位", index = 4)
    private String part;

    @ExcelProperty(value = "测量说明", index = 5)
    private String clff;

    @ExcelProperty(value = "155/60A XS")
    private String model1;

    @ExcelProperty(value = "155/76A XS")
    private String model2;

    @ExcelProperty(value = "160/64A S")
    private String model3;

    @ExcelProperty(value = "160/80A S")
    private String model4;

    @ExcelProperty(value = "165/68A M")
    private String model5;

    @ExcelProperty(value = "165/74A XS")
    private String model6;

    @ExcelProperty(value = "165/84A M")
    private String model7;

    @ExcelProperty(value = "165/88A XS")
    private String model8;

    @ExcelProperty(value = "170/72A L")
    private String model9;

    @ExcelProperty(value = "170/78A S")
    private String model10;

    @ExcelProperty(value = "170/88A L")
    private String model11;

    @ExcelProperty(value = "170/92A S")
    private String model12;

    @ExcelProperty(value = "175/76A XL")
    private String model13;

    @ExcelProperty(value = "175/82A M")
    private String model14;

    @ExcelProperty(value = "175/92A XL")
    private String model15;

    @ExcelProperty(value = "175/96A M")
    private String model16;

    @ExcelProperty(value = "180/100A L")
    private String model17;

    @ExcelProperty(value = "180/86A L")
    private String model18;

    @ExcelProperty(value = "185/104A XL")
    private String model19;

    @ExcelProperty(value = "185/90A XL")
    private String model20;

}
