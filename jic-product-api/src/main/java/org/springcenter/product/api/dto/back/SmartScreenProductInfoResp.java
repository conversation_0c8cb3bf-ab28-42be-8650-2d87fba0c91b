package org.springcenter.product.api.dto.back;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/9/29 20:50
 */
@Data
public class SmartScreenProductInfoResp {

    @ApiModelProperty(value = "商品基本信息")
    private BaseInfo baseInfo;

    @ApiModelProperty(value = "视频信息")
    private VideoInfo videoInfo;

    @ApiModelProperty(value = "图集信息")
    private List<String> bannerImgs;

    @ApiModelProperty(value = "细节图信息")
    private List<String> detailImgs;

    @ApiModelProperty(value = "尺码信息")
    private List<List<String>> sizeInfo;

    @ApiModelProperty(value = "参数信息")
    private ParamInfo paramInfo;


    @Data
    public static class BaseInfo {
        @ApiModelProperty(value = "商品id")
        private String productId;

        @ApiModelProperty(value = "商品款号")
        private String productName;

        @ApiModelProperty(value = "商品名称")
        private String combName;

        @ApiModelProperty(value = "商品价格")
        private BigDecimal price;

        @ApiModelProperty(value = "商品标签")
        private List<String> productLabel;

        @ApiModelProperty(value = "封面图")
        private String cover_img;
    }


    @Data
    public static class VideoInfo {
        @ApiModelProperty(value = "视频链接")
        private String videoUrl;

        @ApiModelProperty(value = "视频图片")
        private String videoImg;
    }

    @Data
    public static class ParamInfo {
        // 品类、合体度、厚薄、面料、上市时间、材质成分、风格
        @ApiModelProperty(value = "品类")
        private String category;

        @ApiModelProperty(value = "合体度")
        private String fitness;

        @ApiModelProperty(value = "厚薄")
        private String thick;

        @ApiModelProperty(value = "面料")
        private String fabric;

        @ApiModelProperty(value = "上市时间")
        private String marketTime;

        @ApiModelProperty(value = "材质成分")
        private String material;

        @ApiModelProperty(value = "风格")
        private String styleName;

        @ApiModelProperty(value = "Fab")
        private String fab;
    }
}
