package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date:2023/12/22 13:14
 */
@Data
public class ProductSizeInfoEsResp extends RemotingSerializable implements Serializable {

    @ApiModelProperty(value = "商品id-部位id-尺寸id-尺寸信息")
    private String id;

    @ApiModelProperty(value = "尺寸id")
    private String size_id;

    @ApiModelProperty(value = "尺寸数据")
    private String size_num;

    @ApiModelProperty(value = "商品id")
    private Long product_id;

    @ApiModelProperty(value = "品牌id")
    private Integer c_arcbrand_id;

    @ApiModelProperty(value = "波段id")
    private Integer band_id;

    @ApiModelProperty(value = "大类id")
    private Integer big_class_id;

    @ApiModelProperty(value = "大类")
    private String big_class;

    @ApiModelProperty(value = "小类")
    private String small_class;

    @ApiModelProperty(value = "小类id")
    private Integer small_class_id;

    @ApiModelProperty(value = "款号")
    private String style_id;

    @ApiModelProperty(value = "季节")
    private String sampleId;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "大品牌")
    private String d_pp;

    @ApiModelProperty(value = "波段")
    private String season;

    @ApiModelProperty(value = "对应看板单号")
    private String bill_no;

    @ApiModelProperty(value = "看板id")
    private Long bill_id;

    @ApiModelProperty(value = "部位")
    private String part;

    @ApiModelProperty(value = "尺寸信息说明")
    private String clff;

    @ApiModelProperty(value = "全局数据")
    private String size_info;

    @ApiModelProperty(value = "部位id")
    private Integer part_id;

    @ApiModelProperty(value = "小品牌")
    private String pp;

    @ApiModelProperty(value = "尺码")
    private String stylepartsize_model;

    @ApiModelProperty(value = "部位+测量说明")
    private String part_clff;

    @ApiModelProperty(value = "袖")
    private String xiu;
}
