package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2024/6/17 10:03
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ProductCenterSearchCenterEnum {

    ALL(0, "全部范围", ""),
    NAME(1, "款号", "name"), // keyword
    CATEGORY(2, "小类", "m_small_category"), // keyword
    COLOR(3, "颜色", "color_name.keyword"),// text
    VALUE(4, "品名", "value"), // keyword
    ARTIST(5, "工艺师", "gys"), // keyword
    // DESIGNER(6, "设计师", "designer"), // keyword
    FABRIC(7, "面料", "bom_text"), // keyword
    BOM_CODE(8, "面料号", "bom_code"),

    SAMPLE_CODE(9, "样衣号", "sample_code"),
    PATTERN(10, "图案搜索", "pattern_name"),
    // FABRIC_NUMBER(8, "面料号", ""),
    ;

    private Integer code;

    private String desc;

    private String field;

    public static String getField(Integer code) {
        ProductCenterSearchCenterEnum centerEnum = Arrays.stream(ProductCenterSearchCenterEnum.values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst().orElse(null);
        if (centerEnum == null) {
            return "";
        }

        return centerEnum.getField();
    }


}
