package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/24 3:08 下午
 * @Version 1.0
 */
@Data
public class SampleProductSkcResp extends RemotingSerializable implements Serializable {
    private String skc_id;
    private String big_season;
    private String year;
    private String name;
    private String color_name;
    private String brand;
    private List<String> label_levels;
    private List<String> labels;
    private String img;
    //波段
    private String m_band;
    private String sample_code;

    private String autoName;

    private String fabInfo;

    public static SampleProductSkcResp build(SampleProductSkcEsResp entity) {
        if (entity == null) {
            return null;
        }
        SampleProductSkcResp skcResp = new SampleProductSkcResp();
        skcResp.setSkc_id(entity.getId());
        skcResp.setBig_season(entity.getSample_id());
        skcResp.setColor_name(entity.getColor_category());
        skcResp.setName(entity.getDesign_name());
        skcResp.setBrand(entity.getD_pp());
        skcResp.setYear(entity.getYear());
        skcResp.setM_band(entity.getSeason());
        skcResp.setSample_code(entity.getSample_code());
        return skcResp;
    }
}
