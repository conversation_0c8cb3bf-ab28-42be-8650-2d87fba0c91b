package org.springcenter.product.api.fallback;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.api.IProductStoreApi;
import org.springcenter.product.api.dto.*;

import java.util.ArrayList;
import java.util.List;


/**
 * 进入fallback的方法 检查是否token未设置
 */
@Slf4j
public class ProductStoreApiFallback implements IProductStoreApi {

    @Setter
    private Throwable cause;

    @Override
    public ResponseResult<List<ProductSkcResp>> queryStoreGoodSkc(CommonRequest<QueryGoodsSkcListReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<ProductSkcResp>> queryStoreMallGoodSkc(CommonRequest<QueryGoodsSkcListReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult getGoodsListByStore(CommonRequest<StoreGoodsReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<ProductVidSkcResp>> queryVidMallGoodSkc(CommonRequest<QueryGoodsSkcListReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }


}
