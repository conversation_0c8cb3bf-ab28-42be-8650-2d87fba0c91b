package org.springcenter.product.api.factory;

import feign.hystrix.FallbackFactory;
import org.springcenter.product.api.IProductApi;
import org.springcenter.product.api.IProductFabApi;
import org.springcenter.product.api.fallback.ProductApiFallback;
import org.springcenter.product.api.fallback.ProductFabApiFallback;
import org.springframework.stereotype.Component;

@Component
public class ProductFabApiFallbackFactory implements FallbackFactory<IProductFabApi> {

    @Override
    public IProductFabApi create(Throwable throwable) {
        ProductFabApiFallback fallback = new ProductFabApiFallback();
        fallback.setCause(throwable);
        return fallback;
    }
}
