package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2023/11/14 14:41
 */
@Data
public class FabVolumeInfoDetailResp {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "品牌id")
    private Long brandCode;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "波段id")
    private Long bandId;

    @ApiModelProperty(value = "波段名称")
    private String bandName;

    @ApiModelProperty(value = "货季")
    private String seasonGoods;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "JSON")
    private String clobJson;

    @ApiModelProperty(value = "FAB_NAME")
    private String fabName;
}
