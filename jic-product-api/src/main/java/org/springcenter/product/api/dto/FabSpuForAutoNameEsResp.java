package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date:2023/12/11 14:06
 */
@Data
public class FabSpuForAutoNameEsResp extends RemotingSerializable implements Serializable {

    @ApiModelProperty(value = "商品id")
    private Integer id;

    @ApiModelProperty(value = "系列")
    private String file1_name;

    @ApiModelProperty(value = "大品牌")
    private String d_pp;

    @ApiModelProperty(value = "面料进口地")
    private String bom_area;

    @ApiModelProperty(value = "环境标签")
    private String sc_origin_area;

    @ApiModelProperty(value = "服用功能")
    private String sc_fzyt;

    @ApiModelProperty(value = "对外名称")
    private String sc_outside_name;

    @ApiModelProperty(value = "品名")
    private String design_name;

    @ApiModelProperty(value = "企划品名")
    private String adjust_name;

    @ApiModelProperty(value = "合体度")
    private String weidu;

    @ApiModelProperty(value = "特殊工艺明细")
    private String tymx;

    @ApiModelProperty(value = "针型")
    private String pin_type;

    @ApiModelProperty(value = "组织")
    private String tex_ture;

    @ApiModelProperty(value = "毛衫标签")
    private String cat_type;

    @ApiModelProperty(value = "是否有里")
    private String you_li;

    @ApiModelProperty(value = "衣门襟")
    private String yi_men_jin;

    @ApiModelProperty(value = "工艺细节")
    private String gyxj;

    @ApiModelProperty(value = "体型")
    private String tixing;

    @ApiModelProperty(value = "厚薄")
    private String thick;

    @ApiModelProperty(value = "穿着方式")
    private String czfs;

    @ApiModelProperty(value = "填充物")
    private String filter;

    @ApiModelProperty(value = "货季")
    private String good_season;

    @ApiModelProperty(value = "季节")
    private String corres_season;

    @ApiModelProperty(value = "Y当季 N往季")
    private String product_life_cycle;

    @ApiModelProperty(value = "图案名称")
    private String pattern_name;

    @ApiModelProperty(value = "小类")
    private String ccchr4;

    @ApiModelProperty(value = "风格名称")
    private String style_name;

    @ApiModelProperty(value = "款号")
    private String style_id;

    @ApiModelProperty(value = "成衣弹力")
    private String cloth_elastic;

    @ApiModelProperty(value = "领型")
    private String lingxing;

    @ApiModelProperty(value = "下摆设计")
    private String xiabai;

    @ApiModelProperty(value = "口袋样式")
    private String koudai;

    @ApiModelProperty(value = "袖型")
    private String xiu;

    @ApiModelProperty(value = "绗缝切线")
    private String hfqx;

    @ApiModelProperty(value = "开衩方式")
    private String kaicha;

    //-----------------样衣AI使用----------------
    @ApiModelProperty(value = "细分类")
    private String ccchr5;

    @ApiModelProperty(value = "产品大类")
    private String scdl;

    @ApiModelProperty(value = "图案大类")
    private String pattern_type;

    @ApiModelProperty(value = "面料特性")
    private String sc_quality;

    @ApiModelProperty(value = "原料产地")
    private String sc_environmental;

    @ApiModelProperty(value = "辅料卖点")
    private String acc_point;

    @ApiModelProperty(value = "产品细分")
    private String scxfl;

    @ApiModelProperty(value = "产品推广资料")
    private String sc_popularize;

    @ApiModelProperty(value = "样衣号")
    private String sample_code;

    @ApiModelProperty(value = "数字最大的色号")
    private String sty_color_id;

    @ApiModelProperty(value = "硬挺度")
    private String sc_stiffness;

    @ApiModelProperty(value = "布面效果")
    private String sc_effect;

    @ApiModelProperty(value = "长度")
    private String kuanxing;

    @ApiModelProperty(value = "袖长")
    private String xiuxing;

    @ApiModelProperty(value = "面料名称")
    private String fabric_name;

    // --------------二期自动品名增加-----
    @ApiModelProperty(value = "二级风格")
    private String style;

    @ApiModelProperty(value = "特殊纱织")
    private String special_sha;


    @ApiModelProperty(value = "成分简写")
    private String cfjx;

    @ApiModelProperty(value = "反绒感官")
    private String rong;

    @ApiModelProperty(value = "肩型")
    private String jian;

    @ApiModelProperty(value = "羽绒进口地")
    private String yr_import_area;

    @ApiModelProperty(value = "长度")
    private String changdu;

    @ApiModelProperty(value = "裤脚口")
    private String ku_jiao_kou;

    @ApiModelProperty(value = "设计卖点")
    private String design_piont;

    @ApiModelProperty(value = "外观")
    private String facade;
}
