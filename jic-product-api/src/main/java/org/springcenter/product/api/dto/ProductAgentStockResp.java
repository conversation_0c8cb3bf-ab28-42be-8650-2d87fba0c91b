package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2023/3/10 11:20
 */
@Data
@ApiModel(value = "经销库存返回")
public class ProductAgentStockResp {

    @ApiModelProperty(value = "skuId")
    private long skuId;

    @ApiModelProperty(value = "skuNo")
    private String skuNo;

    @ApiModelProperty(value = "库存")
    private long qty;

}
