package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2023/5/17 15:47
 */
@Data
public class ProductIsTryBeforeBuyResp {

    @ApiModelProperty(value = "款号")
    private String name;

    @ApiModelProperty(value = "0：是先试后买商品，1：不是先试后买商品")
    private Integer type;

    @ApiModelProperty(value = "微盟商品id")
    private String weiMoGoodId;
}
