package org.springcenter.product.api.factory;


import feign.hystrix.FallbackFactory;
import org.springcenter.product.api.IProductBizTagApi;
import org.springcenter.product.api.fallback.ProductBizTagApiFallback;
import org.springframework.stereotype.Component;

@Component
public class ProductBizTagApiFallbackFactory implements FallbackFactory<IProductBizTagApi> {
    @Override
    public IProductBizTagApi create(Throwable throwable) {
        ProductBizTagApiFallback fallback = new ProductBizTagApiFallback();
        fallback.setCause(throwable);
        return fallback;
    }
}
