package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GoodSpuResp extends BaseProductResp implements Serializable {
    @ApiModelProperty(value = "商品SPUID")
    private long id;

    @ApiModelProperty(value = "FAB详情")
    private String fab;

    @ApiModelProperty(value = "微商城名称")
    private String mall_title;

    @ApiModelProperty(value = "SKU集合")
    private List<Sku> skus;

    @ApiModelProperty(value = "生命周期 3712是未上市 3705：0-4 3706：4-8 3707：N正价期 3708：N季末期 3710：N+2 3709：N+1 3711：N+3")
    private Long lifestyle_tag;

    private long m_product_id;

    @Data
    public static class Sku extends RemotingSerializable implements Serializable{
        @ApiModelProperty(value = "SKUID")
        private String id;

        @ApiModelProperty(value = "条码")
        private String no;

        @ApiModelProperty(value = "国际码")
        private String gbcode;

        @ApiModelProperty(value = "尺码号")
        private String sizeno;

        @ApiModelProperty(value = "尺码名称")
        private String size_name;

        @ApiModelProperty(value = "SKC图片地址")
        private String imgurl;
        //扣完的图片
        @ApiModelProperty(value = "SKC白底图")
        private String matted_url;

        @ApiModelProperty(value = "SKU库存数量")
        private int stock;

        private String stylepartsize_model;

        @ApiModelProperty(value = "色号")
        private String colorno;

        @ApiModelProperty(value = "色名称")
        private String color_name;
    }

    public void buildSku(){
        if (this.getSku_list() == null || "".equals(this.getSku_list())){
            return;
        }
        this.skus = Sku.decodeArr(this.getSku_list(), Sku.class);
    }
}
