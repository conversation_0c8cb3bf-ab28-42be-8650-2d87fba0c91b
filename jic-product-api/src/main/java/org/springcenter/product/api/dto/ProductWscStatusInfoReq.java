package org.springcenter.product.api.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/6/8 14:08
 */
@Data
public class ProductWscStatusInfoReq {

    @NotEmpty(message = "商品ID不能为空")
    private List<Long> productIds;

    @NotNull(message = "门店ID不能为空")
    private Long vid;

    @NotNull(message = "品牌ID不能为空")
    private String weId;
}
