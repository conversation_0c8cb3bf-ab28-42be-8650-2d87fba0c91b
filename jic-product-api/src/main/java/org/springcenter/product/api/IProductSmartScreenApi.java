package org.springcenter.product.api;

import com.alibaba.fastjson.JSONArray;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import org.springcenter.product.api.dto.back.SmartScreenProductInfoResp;
import org.springcenter.product.api.dto.back.SmartScreenSkcInfoResp;
import org.springcenter.product.api.dto.back.screen.StoreRecommendGoodsReq;
import org.springcenter.product.api.dto.back.screen.StoreRecommendGoodsResp;
import org.springcenter.product.api.factory.ProductSmartScreenApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/9/30 14:05
 */
@Component
@FeignClient(contextId = "productSmartScreenRemoteApi", value = "jic-product-background-center", fallbackFactory = ProductSmartScreenApiFallbackFactory.class)
public interface IProductSmartScreenApi {

    @PostMapping("/product/smart/screen/api/search/skc")
    ResponseResult<List<SmartScreenSkcInfoResp>> searchProductSkcInfo(@RequestBody CommonRequest<String> request);


    @PostMapping("/product/smart/screen/api/search/product/info")
    ResponseResult<SmartScreenProductInfoResp> searchProductInfo(@RequestBody CommonRequest<String> request);


    @PostMapping("/product/smart/screen/api/search/param/info")
    ResponseResult<JSONArray> searchParamInfo(@RequestBody CommonRequest request);


    @PostMapping("/product/smart/screen/api/screen/recommend/product/list")
    ResponseResult<List<StoreRecommendGoodsResp>> screenRecommendProductList(@RequestBody CommonRequest<StoreRecommendGoodsReq> request);

    @PostMapping("/product/center/getSizeNos")
    ResponseResult<JSONArray> getSizeNos(@RequestBody CommonRequest request);
}
