package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/8/11 19:06
 */
@Data
public class ExchangeByProductIdReq {

    @ApiModelProperty(value = "品牌id")
    @NotBlank(message = "品牌id不能为空")
    private String weId;


    @ApiModelProperty(value = "商品id")
    @NotEmpty(message = "商品id不能为空")
    private List<String> productId;
}
