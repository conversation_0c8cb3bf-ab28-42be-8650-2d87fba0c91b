package org.springcenter.product.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import org.springcenter.product.api.dto.background.SearchProductIdInPacReq;
import org.springcenter.product.api.dto.background.SearchProductIdInPacResp;
import org.springcenter.product.api.dto.background.SearchProductIdIsExistResp;
import org.springcenter.product.api.fallback.ProductPackageApiFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/9/23 20:08
 */
@Component
@FeignClient(contextId = "productPackageApi", value = "jic-product-background-center", fallbackFactory = ProductPackageApiFallback.class)
public interface IProductPackageApi {


    /**
     * 查询商品包中包含哪些商品id
     * @param request 入参
     * @return 返回
     */
    @PostMapping("/product/package/api/product/package/pac/product/forc")
    ResponseResult<List<SearchProductIdInPacResp>> getPackageProductIdsForc(@RequestBody @Validated CommonRequest<SearchProductIdInPacReq> request);

    /**
     * 查询商品id是否存在入参的商品包中
     * @param request 入参
     * @return 返回
     */
    @PostMapping("/product/package/api/product/package/product/exist/forc")
    ResponseResult<List<SearchProductIdIsExistResp>> getPackageIsExistProductIdsForc(@RequestBody @Validated CommonRequest<SearchProductIdInPacReq> request);
}
