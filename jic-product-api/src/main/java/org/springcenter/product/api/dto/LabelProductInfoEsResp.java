package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LabelProductInfoEsResp extends RemotingSerializable implements Serializable {
    @ApiModelProperty(value = "skc_code")
    private String id;

    @ApiModelProperty(value = "款号")
    private String style_id;

    @ApiModelProperty(value = "色号")
    private String sty_color_id;

    @ApiModelProperty(value = "品名")
    private String design_name;

    @ApiModelProperty(value = "口袋样式")
    private String koudai;

    @ApiModelProperty(value = "服用功能")
    private String scfyt;

    @ApiModelProperty(value = "图案大类")
    private String pattern_type;

    @ApiModelProperty(value = "品牌")
    private String d_pp;

    @ApiModelProperty(value = "特殊工艺明细")
    private String tymx;

    @ApiModelProperty(value = "面料效果")
    private String sc_effect;

    @ApiModelProperty(value = "skc")
    private String skc;

    @ApiModelProperty(value = "产品细分")
    private String scxfl;

    @ApiModelProperty(value = "企划品名")
    private String adjust_name;

    @ApiModelProperty(value = "标签")
    private List<String> code;

    @ApiModelProperty(value = "商品id")
    private Long productId;

    @ApiModelProperty(value = "绗缝")
    private String hfqx;

    @ApiModelProperty(value = "面料效果")
    private String file1_name;

    @ApiModelProperty(value = "对外名称")
    private String outside_name;
}
