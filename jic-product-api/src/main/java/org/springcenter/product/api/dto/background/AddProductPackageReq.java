package org.springcenter.product.api.dto.background;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/7/26 14:34
 */
@Data
@ApiModel(value = "增加商品包需要的参数")
public class AddProductPackageReq {

    @ApiModelProperty(value = "商品包名称")
    @NotBlank(message = "商品包名称不能为空")
    private String packageName;

    @ApiModelProperty(value = "商品包介绍")
    private String packageIntroduction;

    @ApiModelProperty(value = "是否有条件筛选 0:无筛选条件 1:有筛选条件")
    @NotNull(message = "是否有筛选条件不能为空")
    private Integer hasFilter;

    @ApiModelProperty(value = "是否名单导入  0:无名单导入 1:有名单导入")
    @NotNull(message = "是否名单导入条件不能为空")
    private Integer hasImport;

    @ApiModelProperty(value = "生效类型 0长期有效 1定期有效")
    @NotNull(message = "生效类型不能为空")
    private Integer validType;

    @ApiModelProperty(value = "开始时间 格式：yyyy-MM-dd HH:mm:ss")
    @NotBlank(message = "开始时间不能为空")
    private String startDate;

    @ApiModelProperty(value = "结束时间 格式：yyyy-MM-dd HH:mm:ss")
    private String endDate;

    @ApiModelProperty(value = "筛选条件")
    private List<FilterInfoData> filterInfoDataList;

    @ApiModelProperty(value = "填写条件")
    private List<ImportInfoData> importInfoDataList;

    @ApiModelProperty(value = "更新人")
    @NotBlank(message = "更新人不能为空")
    private String updator;

    @ApiModelProperty(value = "部门")
    @NotBlank(message = "部门不能为空")
    private String department;

    @Data
    public static class FilterInfoData {
        @ApiModelProperty(value = "筛选字段")
        private String field;

        @ApiModelProperty(value = "筛选字段名称")
        private String fieldName;

        @ApiModelProperty(value = "value值")
        private List<String> codes;

        @ApiModelProperty(value = "0：正选 1：反选")
        private Integer condition;
    }

    @Data
    public static class ImportInfoData {
        @ApiModelProperty(value = "名单类型 0：白名单 1：黑名单")
        private Integer linkType;

        @ApiModelProperty(value = "款号，用逗号分割")
        private String linkContent;

    }
}
