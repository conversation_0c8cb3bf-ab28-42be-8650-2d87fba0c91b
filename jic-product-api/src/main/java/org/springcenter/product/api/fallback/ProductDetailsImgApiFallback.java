package org.springcenter.product.api.fallback;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import lombok.Setter;
import org.springcenter.product.api.IProductDetailsImgApi;
import org.springcenter.product.api.dto.*;

import java.util.ArrayList;
import java.util.List;

public class ProductDetailsImgApiFallback implements IProductDetailsImgApi {
    @Setter
    private Throwable cause;


    @Override
    public ResponseResult<List<FindProductDetailImgResp>> findProductDetailImg(CommonRequest<FindProductDetailImgReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<LianxiangBatchGetViewResp>> batchGetViewUrl(CommonRequest<LianxiangBatchGetViewReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<SampleProductDetailImg>> findSampleClothNetDiskImgs(CommonRequest<FindSampleClothNetDiskImgReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }
}
