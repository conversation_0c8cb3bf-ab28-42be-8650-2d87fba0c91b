package org.springcenter.product.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class CustomsDeclarationResp {

    /**
     * 主键id
     */
    private String id;

    /**
     * 报告名称
     */
    private String name;

    /**
     * 货季
     */
    private String season;

    /**
     * 0 未删除  1 已删除
     */
    private Integer isDel;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * neid
     */
    private String neid;

    /**
     * NSID
     */
    private String nsid;
}
