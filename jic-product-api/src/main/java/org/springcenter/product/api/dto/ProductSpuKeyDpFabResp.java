package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/3/29 9:30
 */
@Data
public class ProductSpuKeyDpFabResp {

    @ApiModelProperty(value = "商品款号")
    private List<NoKeyMatchData> noKeyMatchDataList;

    @ApiModelProperty(value = "搭配图相关图片")
    private List<ProductSpuFabEsResp.MatchPicExpend> matchPicExpends;

    @Data
    public static class NoKeyMatchData implements Serializable {
        @ApiModelProperty(value = "商品id")
        private String productId;

        @ApiModelProperty(value = "name")
        private String name;

        @ApiModelProperty(value = "名称")
        private String value;

        @ApiModelProperty(value = "名称")
        private String suitNum;

        @ApiModelProperty(value = "skc")
        private String skc;
    }

    public static ProductSpuKeyDpFabResp buildRet(List<ProductSpuFabEsResp.MatchPicExpend> matchPicExpends, List<NoKeyMatchData> noKeyMatchDataList) {
        ProductSpuKeyDpFabResp resp = new ProductSpuKeyDpFabResp();
        resp.setMatchPicExpends(matchPicExpends);
        resp.setNoKeyMatchDataList(noKeyMatchDataList);
        return resp;
    }
}
