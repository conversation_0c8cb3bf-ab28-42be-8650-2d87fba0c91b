package org.springcenter.product.api.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date:2023/12/22 16:25
 */
@Data
@ApiModel(value = "婴童尺码表格导出")
@EqualsAndHashCode
public class YtSizeInfoModelEntity {

    @ExcelProperty(value = "款号", index = 0)
    private String style_id;

    @ExcelProperty(value = "年份", index = 1)
    private String year;

    @ExcelProperty(value = "季节", index = 2)
    private String sample_id;

    @ExcelProperty(value = "品牌", index = 3)
    private String d_pp;

    @ExcelProperty(value = "部位", index = 4)
    private String part;

    @ExcelProperty(value = "测量说明", index = 5)
    private String clff;

    // 59cm（21）	66cm（22）	73cm（23）	80cm（24）	90cm（25）	100cm（26）
    // @ExcelProperty(value = "59/44")
    @ExcelProperty(value = "59/21")
    private String model1;

    // @ExcelProperty(value = "66/44")
    @ExcelProperty(value = "66/22")
    private String model2;

    // @ExcelProperty(value = "73/47")
    @ExcelProperty(value = "73/23")
    private String model3;

    // @ExcelProperty(value = "73/48")
    @ExcelProperty(value = "80/24")
    private String model4;

    // @ExcelProperty(value = "80/47")
    @ExcelProperty(value = "90/25")
    private String model5;

    // @ExcelProperty(value = "80/48")
    @ExcelProperty(value = "100/26")
    private String model6;

    @ExcelProperty(value = "110/27")
    private String model7;

    /*@ExcelProperty(value = "90/47")
    private String model7;

    @ExcelProperty(value = "90/48")
    private String model8;

    @ExcelProperty(value = "100/47")
    private String model9;

    @ExcelProperty(value = "100/48")
    private String model10;*/
}
