package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(value = "新增陈列册")
@Data
public class DisplayBookInfoAddReq {

    @ApiModelProperty(value = "年份", required = true)
    @NotBlank(message = "年份不能为空")
    private String year;

    @ApiModelProperty(value = "波段id。type=1时必传")
    private Long bandId;

    @ApiModelProperty(value = "波段名称。type=1时必传")
    private String bandName;

    @ApiModelProperty(value = "品牌名称", required = true)
    @NotBlank(message = "品牌名称不能为空")
    private String brandName;

    @ApiModelProperty(value = "品牌id", required = true)
    @NotNull(message = "品牌id不能为空")
    private Long brandCode;

    @ApiModelProperty(value = "货季", required = true)
    @NotBlank(message = "货季不能为空")
    private String seasonGoods;

    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank(message = "操作人不能为空")
    private String operator;

    @ApiModelProperty(value = "渠道:直营/经销", required = true)
    @NotBlank(message = "渠道不能为空")
    private String channel;

    @ApiModelProperty(value = "陈列册名称")
    private String bookName;

    @ApiModelProperty(value = "类型 1=陈列册 2=陈列指引", example = "1", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type = 1;

    @ApiModelProperty(value = "月份(type=2时必填)")
    private Integer bandNameMonth;
}
