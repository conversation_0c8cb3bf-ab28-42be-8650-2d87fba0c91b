package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/9/17 13:26
 */

@Data
public class QueryProductByAIReq implements Serializable {
    @ApiModelProperty(value = "图片链接")
    private String img;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("搜索文本")
    private String query;

    @ApiModelProperty("包含门店")
    private List<String> storeId;

    @ApiModelProperty("库存方式 0 有库存，1 全部商品，不传默认为0")
    private int isStockNotEmpty = 0;
}
