package org.springcenter.product.api.dto.wsc;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date:2024/4/29 13:53
 */
@Data
public class VidCidProductEsSearchResp extends RemotingSerializable implements Serializable {

    @ApiModelProperty(value = "id 由vid、cid、mall-id组成")
    private String id;

    @ApiModelProperty(value = "微盟门店id")
    private Long vid;

    @ApiModelProperty(value = "分类id")
    private Long cid;

    @ApiModelProperty(value = "伯俊商品id")
    private Long product_id;

    @ApiModelProperty(value = "微盟商品id")
    private Long mall_id;

    @ApiModelProperty(value = "是否新品 1是新品 0不是")
    private Integer is_new;

    @ApiModelProperty(value = "商品销量")
    private Long sales_volume;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal price;

    @ApiModelProperty(value = "是否可售 0不可售 1可售")
    private Integer is_can_sell;

    @ApiModelProperty(value = "是否上架 0不上架 1上架")
    private Integer is_putaway;

    @ApiModelProperty(value = "在分类中是否展示 0不展示 1展示")
    private Integer is_display;

    @ApiModelProperty(value = "款号")
    private String product_no;

    @ApiModelProperty(value = "门店品牌id")
    private Long weId;

    @ApiModelProperty(value = "商品品牌id")
    private Long brand_id;

    @ApiModelProperty(value = "大类id")
    private Long big_class_id;

    @ApiModelProperty(value = "小类id")
    private Long small_class_id;

    private Integer class_type;
}
