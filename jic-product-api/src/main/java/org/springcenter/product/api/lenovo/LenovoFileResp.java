package org.springcenter.product.api.lenovo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class LenovoFileResp {

    @ApiModelProperty(value = "报错code")
    private String errcode;

    @ApiModelProperty(value = "报错信息")
    private String errmsg;

    private List<LenovoFileModel> fileModelList;

    @ApiModelProperty(value = "总数量")
    private Long total;



    @Data
    public static class LenovoFileModel{

        @ApiModelProperty(value = "是否是文件夹  true 文件夹 false 不是文件夹")
        private Boolean dir;

        @ApiModelProperty(value = "路径")
        private String path;

        @ApiModelProperty(value = "描述")
        private String desc;

        @ApiModelProperty(value = "外链码")
        private String deliveryCode;

        @ApiModelProperty(value = "文件id")
        private String neid;

        @ApiModelProperty(value = "命名空间id")
        private String nsid;

    }

}
