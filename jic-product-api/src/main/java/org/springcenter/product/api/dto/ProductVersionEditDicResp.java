package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/11 12:34
 */
@Data
public class ProductVersionEditDicResp {

    @ApiModelProperty(value = "品牌信息")
    private List<BaseModel> brandList;

    @ApiModelProperty(value = "属性信息")
    private List<BaseModel> attributeList;


    @Data
    public static class BaseModel implements Serializable {
        private String id;

        private String code;

        private String name;

        private String pid;

        private String field;

        @ApiModelProperty(value = "子信息")
        private List<BaseModel> childList;

        @ApiModelProperty(value = "子信息")
        private List<BaseModel> subAttributeList;
    }
}
