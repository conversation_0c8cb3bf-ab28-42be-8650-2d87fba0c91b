package org.springcenter.product.api.fallback;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.api.IProductApi;
import org.springcenter.product.api.dto.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


/**
 * 进入fallback的方法 检查是否token未设置
 */
@Slf4j
public class ProductApiFallback implements IProductApi {

    @Setter
    private Throwable cause;

    @Override
    public ResponseResult<List<SkuResp>> selectGoodsListBySkuIds(CommonRequest<QuerySkuDto> skuIds) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<GoodSpuResp>> queryGoods(CommonRequest<QueryGoodsReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<ProductSkcResp>> queryGoodSkc(CommonRequest<QueryGoodsReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<ProductSkcResp> goodSkcDetail(CommonRequest<ProductReq> request) {
        return null;
    }

    @Override
    public ResponseResult<List<SampleProductSkcResp>> searchSampleProductSkc(CommonRequest<SampleProductReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<SimilarProductResp>> searchSimilarGoods(CommonRequest<SimilarProductReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<HashMap<Long, ProductAgentStockResp>> searchSkuAgentStorageByIds(CommonRequest<ProductAgentStockReq> request) {
        return ResponseResult.success(new HashMap<>());
    }

    @Override
    public ResponseResult<List<SameSpuProductResp>> searchSameSpuProduct(CommonRequest<SameSpuProductReq> commonRequest) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<GoodSpuDetailEntity> searchProductDetail(CommonRequest<String> request) {
        return null;
    }

    @Override
    public ResponseResult<List<ProductSkuIdByWeiMenInfoResp>> searchProductSkuIdByWeiMenInfo(CommonRequest<List<ProductByWeiMenInfoReq>> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<ProductByWeiMenInfoResp>> searchProductByWeiMenInfo(CommonRequest<List<ProductByWeiMenInfoReq>> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<ProductByWeiMenInfoResp>> searchProductByWeiMenInfoNoType(CommonRequest<List<ProductByWeiMenInfoReq>> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<ProductIsTryBeforeBuyResp>> searchProductIsTryBeforeBuy(CommonRequest<ProductIsTryBeforeBuyReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<SearchWeiMoGoodInfoResp>> exchangeWeiMoProductId(CommonRequest<List<SearchWeiMoGoodInfoReq>> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<ExchangeProductIdByWeiMoResp>> exchangeProductIdByWeiMo(CommonRequest<ExchangeProductIdByWeiMoReq> request) {
        return null;
    }

    @Override
    public ResponseResult<List<SearchWeiMoGoodInfoResp>> exchangeWeiMoProductIdAndSkuId(CommonRequest<List<SearchWeiMoGoodInfoReq>> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<ExchangeByProductIdResp>> getWeiMoGoodIdAndBJSkuId(CommonRequest<ExchangeByProductIdReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<SkuProductInfoResp> getProductInfoBySku(CommonRequest<String> request) {
        return null;
    }

    @Override
    public ResponseResult<List<String>> queryGoodsIds(CommonRequest<QueryGoodsReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<ZhuiDanSkuInfoResp>> getSkuInfoByZdSkuId(CommonRequest<List<Long>> request) {
        return ResponseResult.success(new ArrayList<>());
    }
}
