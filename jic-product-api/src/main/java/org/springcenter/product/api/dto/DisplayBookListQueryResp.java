package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("陈列册列表查询响应数据")
@Data
public class DisplayBookListQueryResp {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "货季")
    private String seasonGoods;

    @ApiModelProperty(value = "品牌")
    private String brandName;

    @ApiModelProperty(value = "渠道")
    private String channel;

    @ApiModelProperty(value = "波段/月份")
    private String bandName;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "展示时间")
    private String displayTime;

    @ApiModelProperty(value = "保存时间")
    private String updateTime;

    @ApiModelProperty(value = "0:未设置时间 1:待展示 2:已展示")
    private Integer status;

    @ApiModelProperty(value = "陈列册名称")
    private String bookName;

    @ApiModelProperty(value = "类型 1=陈列册 2=陈列指引")
    private Integer type;

}
