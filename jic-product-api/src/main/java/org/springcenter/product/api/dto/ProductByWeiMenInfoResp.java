package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/5/17 15:46
 */
@Data
public class ProductByWeiMenInfoResp {

    @ApiModelProperty(value = "商品款号")
    private String productNo;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "品牌ID")
    private String weId;

    @ApiModelProperty(value = "品牌名称")
    private String weIdName;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "大季ID")
    private Integer bigSeasonId;

    @ApiModelProperty(value = "大季")
    private String bigSeason;

    @ApiModelProperty(value = "季节ID")
    private Integer seasonId;

    @ApiModelProperty(value = "季节")
    private String season;

    @ApiModelProperty(value = "小季ID")
    private Integer smallSeasonId;

    @ApiModelProperty(value = "小季")
    private String smallSeason;

    @ApiModelProperty(value = "大类ID")
    private Integer bigClassId;

    @ApiModelProperty(value = "大类")
    private String bigClass;

    @ApiModelProperty(value = "小类ID")
    private Integer smallClassId;

    @ApiModelProperty(value = "小类")
    private String smallClass;

    @ApiModelProperty(value = "波段ID")
    private Integer bandId;

    @ApiModelProperty(value = "波段")
    private String band;

    @ApiModelProperty(value = "主题ID")
    private Integer topicId;

    @ApiModelProperty(value = "主题")
    private String topic;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "是否当季")
    private String lifecycle;

    @ApiModelProperty(value = "微商城商品标签")
    private String mallTag;

    @ApiModelProperty(value = "微商城商品标题")
    private String mallTitle;

    @ApiModelProperty(value = "微商城封面图")
    private String mallImage;

    @ApiModelProperty(value = "微商城轮播图")
    private String mallBanner;

    @ApiModelProperty(value = "微商城详情图")
    private String mallDetailImage;

    @ApiModelProperty(value = "微商城分组")
    private String mallGroup;

    @ApiModelProperty(value = "是否上架")
    private String mallPutaway;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "商品变更时间")
    private Date putawayTime;

    @ApiModelProperty(value = "奥莱上架时间")
    private Date aolaiUpdateTime;

    private String productHiddenNo;

    @ApiModelProperty(value = "微盟商品分组")
    private String mallClassify;

    @ApiModelProperty(value = "商品款号ID")
    private Long productId;

    @ApiModelProperty(value = "SKUID")
    private Long skuId;

    @ApiModelProperty(value = "国标码")
    private String gbCode;

    @ApiModelProperty(value = "颜色ID")
    private String colorId;

    @ApiModelProperty(value = "颜色编号")
    private String colorCode;

    @ApiModelProperty(value = "颜色名称")
    private String colorName;

    @ApiModelProperty(value = "尺码ID")
    private String sizeId;

    @ApiModelProperty(value = "尺码编号")
    private String sizeCode;

    @ApiModelProperty(value = "尺码名称")
    private String sizeName;

    @ApiModelProperty(value = "尺码描述")
    private String sizeDescription;

    @ApiModelProperty(value = "封面图")
    private String image;

    @ApiModelProperty(value = "品牌ID")
    private String weid;

    @ApiModelProperty(value = "ERP条码")
    private String productCode;

    @ApiModelProperty(value = "微盟商品ID")
    private String weimoGoodid;

    @ApiModelProperty(value = "微盟SKUID")
    private String weimoSkuid;

    @ApiModelProperty(value = "是否上架")
    private String weimoIsputaway;

    @ApiModelProperty(value = "上架时间")
    private Date weimoPutawayTime;

    @ApiModelProperty(value = "0是先试后买商品，1不是")
    private Integer type;

}
