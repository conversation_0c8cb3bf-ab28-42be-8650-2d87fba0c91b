package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/11/14 13:15
 */
@Data
public class QueryFabVolumeInfoReq {

    @ApiModelProperty(value = "年")
    private List<String> years;

    @ApiModelProperty(value = "波段id")
    private List<Long> bandIds;

    @ApiModelProperty(value = "品牌id")
    private List<Long> brandIds;

    @ApiModelProperty(value = "货季  -forc")
    private List<String> goodSeason;

    @ApiModelProperty(value = "品牌名称")
    private List<String> brandNames;

    @ApiModelProperty(value = "fab产品册名称")
    private String fabName;

    @ApiModelProperty(value = "fab产品册类型 0是直营 1是经销 -forb")
    private Integer fabType;

    @ApiModelProperty(value = "门店id forc")
    private Long storeId;
}
