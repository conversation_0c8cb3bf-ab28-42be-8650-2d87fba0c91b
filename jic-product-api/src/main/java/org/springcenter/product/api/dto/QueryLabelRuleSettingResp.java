package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2023/10/12 17:22
 */
@Data
public class QueryLabelRuleSettingResp {


    @ApiModelProperty(value = "标签配置名称||值code")
    private String labelSettingNameCode;

    @ApiModelProperty(value = "标签配置名称||值NAME")
    private String labelSettingName;

    @ApiModelProperty(value = "0标签 1属性")
    private Integer labelSettingType;

    @ApiModelProperty(value = "属性字段")
    private String labelSettingField;

    @ApiModelProperty(value = "id")
    private String id;
}
