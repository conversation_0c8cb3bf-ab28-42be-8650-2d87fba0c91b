package org.springcenter.product.api.dto;

import com.alibaba.fastjson.JSONObject;
import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class ProductSpcNewArrivalFabEsResp extends RemotingSerializable implements Serializable {
    @ApiModelProperty(value = "skc")
    private String skc_id;

    @ApiModelProperty(value = "品牌")
    private String pp;

    @ApiModelProperty(value = "商品货季")
    private String good_season;

    @ApiModelProperty(value = "对应小季节")
    private String corressample_id;

    @ApiModelProperty(value = "款号")
    private String style_id;

    @ApiModelProperty(value = "吊牌价")
    private Double sell_price;

    @ApiModelProperty(value = "色号")
    private String sty_color_id;

    @ApiModelProperty(value = "颜色名称")
    private String m_color_name;

    @ApiModelProperty(value = "小类")
    private String ccchr5;

    @ApiModelProperty(value = "波段")
    private String season;

    @ApiModelProperty(value = "主题")
    private String theme;

    @ApiModelProperty(value = "计划上市时间")
    private String sale_date;

    @ApiModelProperty(value = "体型")
    private String tixing;

    @ApiModelProperty(value = "合体度")
    private String weidu;

    @ApiModelProperty(value = "袖型")
    private String xiu;

    @ApiModelProperty(value = "袖长")
    private String xiuxing;

    @ApiModelProperty(value = "领型")
    private String lingxing;

    @ApiModelProperty(value = "长度")
    private String kuanxing;

    @ApiModelProperty(value = "厚薄")
    private String thick;

    @ApiModelProperty(value = "填充物")
    private String filler;

    @ApiModelProperty(value = "填充量")
    private String fill_content;

    @ApiModelProperty(value = "标准")
    private String exec_standard;

    @ApiModelProperty(value = "产地")
    private String origin;

    @ApiModelProperty(value = "商检成分")
    private String sjcf;

    @ApiModelProperty(value = "产品名称")
    private String sc_out_side_name;

    @ApiModelProperty(value = "洗涤名称")
    private String wash_name;

    @ApiModelProperty(value = "款号")
    private String name;

    @ApiModelProperty(value = "名称")
    private String value;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "品牌架构id")
    private String c_arcbrand_id;

    @ApiModelProperty(value = "年份")
    private String a_year;

    @ApiModelProperty(value = "小季节")
    private String samll_season;

    @ApiModelProperty(value = "小季节id")
    private Long a_small_season_id = 0L;

    @ApiModelProperty(value = "波段")
    private String band;

    @ApiModelProperty(value = "波段id")
    private Long band_id = 0L;

    @ApiModelProperty(value = "大类")
    private String big_class;

    @ApiModelProperty(value = "小类")
    private String small_class;

    @ApiModelProperty(value = "吊牌价")
    private String price;

    @ApiModelProperty(value = "主题")
    private String topic;

    @ApiModelProperty(value = "色号")
    private String color_code;

    @ApiModelProperty(value = "颜色名称")
    private String color_name;

    @ApiModelProperty(value = "商品id")
    private String product_id;

    @ApiModelProperty(value = "skc")
    private String a_skc_code;

    @ApiModelProperty(value = "标签层级")
    private List<String> label_levels;

    @ApiModelProperty(value = "标签")
    private List<String> labels;

    @ApiModelProperty(value = "C17设计档案对应ID [设计编号]")
    private String design_id_new;

    @ApiModelProperty(value = "品名")
    private String design_name;

    @ApiModelProperty(value = "款式性别")
    private String sex;

    @ApiModelProperty(value = "汇总定额（含放量")
    private Long add_amt = 0L;

    @ApiModelProperty(value = "计划入库时间")
    private String in_date;

    @ApiModelProperty(value = "搭配建议")
    private String match_advice;

    @ApiModelProperty(value = "尺码编号")
    private String size_id;

    @ApiModelProperty(value = "尺码名称")
    private String size_name;

    @ApiModelProperty(value = "搭配关系 (款号) ")
    private String skc_list;

    @ApiModelProperty(value = "搭配关系 (款号) ")
    private List<String> skcList;

    @ApiModelProperty(value = "亲子款")
    private String qzk;

    @ApiModelProperty(value = "自动风格")
    private String style_name;

    @ApiModelProperty(value = "fab描述")
    private String fab;

    @ApiModelProperty(value = "内配")
    private String np_name;

    @ApiModelProperty(value = "完整吊牌成分")
    private String ingredient;

    @ApiModelProperty("环保标签")
    private String sc_origin_area;

    @ApiModelProperty("服用功能")
    private String sc_fzyt;

    @ApiModelProperty("面料推广资料")
    private String sc_popularize;

    @ApiModelProperty("辅料推广资料")
    private String acc_point;

    @ApiModelProperty("是否有里")
    private String you_li;

    @ApiModelProperty("口袋样式")
    private String kou_dai;

    @ApiModelProperty("开衩方式")
    private String kai_cha;

    @ApiModelProperty("衣门襟")
    private String yi_men_jin;

    @ApiModelProperty("外观")
    private String facade;

    @ApiModelProperty("成衣弹力")
    private String cloth_elastic;

    @ApiModelProperty("图案名称")
    private String pattern_name;

    @ApiModelProperty("小类id")
    private Long small_class_id;

    @ApiModelProperty("面料号")
    private String bom_code;

    @ApiModelProperty("吊牌说明")
    private String tag_info;

    @ApiModelProperty("图案是否随机")
    private String pattern_random;

    @ApiModelProperty("OEKOTEX认证")
    private String oekotex_tag;

    @ApiModelProperty("面料特性")
    private String fabric_charac_list;

    @ApiModelProperty("内配款号")
    private String np_spu;


    public void buildSkcList() {
        if (this.getSkc_list() == null || "".equals(this.getSkc_list()) || "[]".equals(this.getSkc_list())){
            return;
        }
        this.skcList = JSONObject.parseArray(this.getSkc_list(), String.class);
    }


    public void buildFabricList() {
        if (this.getFabric_charac_list() == null || "".equals(this.getFabric_charac_list()) || "[]".equals(this.getFabric_charac_list())){
            return;
        }
        List<String> strings = JSONObject.parseArray(this.getFabric_charac_list(), String.class);
        this.fabric_charac_list = strings.stream().collect(Collectors.joining("、"));
    }
}
