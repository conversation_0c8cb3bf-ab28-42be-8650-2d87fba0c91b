package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/6/19 17:15
 */
@Data
public class ProductZhuiDanInfoResp {

    @ApiModelProperty(value = "商品id")
    private Long productId;

    @ApiModelProperty(value = "款号")
    private String name;

    @ApiModelProperty(value = "商品名称")
    private String value;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "图片")
    private String img;

    @ApiModelProperty(value = "sku信息")
    private List<Sku> skus;

    @ApiModelProperty(value = "是否原款翻版 0不是 1是")
    private Integer isOriginSpu;


    @Data
    public static class Sku implements Serializable {
        @ApiModelProperty(value = "SKUID")
        private String id;

        @ApiModelProperty(value = "条码")
        private String no;

        @ApiModelProperty(value = "国际码")
        private String gbCode;

        @ApiModelProperty(value = "尺码号")
        private String sizeNo;

        @ApiModelProperty(value = "尺码名称")
        private String sizeName;

        @ApiModelProperty(value = "SKC图片地址")
        private String imgUrl;
        //扣完的图片
        @ApiModelProperty(value = "SKC白底图")
        private String mattedUrl;

        private String stylePartSizeModel;

        @ApiModelProperty(value = "色号")
        private String colorNo;

        @ApiModelProperty(value = "色名称")
        private String colorName;
    }

}
