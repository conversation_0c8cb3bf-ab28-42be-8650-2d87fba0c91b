package org.springcenter.product.api.factory;

import org.springcenter.product.api.IProductApi;
import org.springcenter.product.api.fallback.ProductApiFallback;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class ProductApiFallbackFactory implements FallbackFactory<IProductApi> {

    @Override
    public IProductApi create(Throwable throwable) {
        ProductApiFallback fallback = new ProductApiFallback();
        fallback.setCause(throwable);
        return fallback;
    }
}
