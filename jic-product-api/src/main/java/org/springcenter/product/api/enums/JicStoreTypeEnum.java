package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2023/8/13 15:30
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum JicStoreTypeEnum {

    NT("NT", "NTGX0000"),
    CD("CD", "CD01"),
    QUYU("QUYU", "NTGX0001");

    private String oldType;

    private String desc;

    public static JicStoreTypeEnum getDesc(String oldType) {
        return Arrays.stream(JicStoreTypeEnum.values())
                .filter(v -> Objects.equals(v.getOldType(), oldType))
                .findFirst()
                .orElse(null);
    }
}
