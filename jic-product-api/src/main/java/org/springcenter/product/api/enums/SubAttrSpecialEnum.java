package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2022/12/11 14:35
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SubAttrSpecialEnum {

    SKIRT_TYPE_1("297", "连衣裙", "Q10", "裙型", "", ""),
    SKIRT_TYPE_2("298", "腰裙", "Q10", "不规则裙", "Q10-S15", "摆围"),;

    private String categoryCode;

    private String categoryName;

    private String mainAttrCode;

    private String mainAttrName;

    private String subAttrCode;

    private String subAttrName;

    public static List<String> getByCategoryAndMainAttr(String categoryCode, String mainAttrCode) {
        return Arrays.stream(SubAttrSpecialEnum.values())
                .filter(v -> Objects.equals(v.getCategoryCode(), categoryCode) && Objects.equals(v.getMainAttrCode(), mainAttrCode))
                .distinct()
                .map(SubAttrSpecialEnum::getSubAttrCode)
                .collect(Collectors.toList());
    }
}
