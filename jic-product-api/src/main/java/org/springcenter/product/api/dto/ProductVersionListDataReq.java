package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/6 10:40
 */
@Data
public class ProductVersionListDataReq {

    @ApiModelProperty(value = "搜索项")
    private String name;

    @ApiModelProperty(value = "品牌")
    private List<String> brandList;

    @ApiModelProperty(value = "类目")
    private List<String> categoryList;

    @ApiModelProperty(value = "属性")
    private List<String> attributeList;

    @ApiModelProperty(value = "排序")
    private String sort;

    @ApiModelProperty(value = "0是开启 1是关闭")
    private Integer isClose;
}
