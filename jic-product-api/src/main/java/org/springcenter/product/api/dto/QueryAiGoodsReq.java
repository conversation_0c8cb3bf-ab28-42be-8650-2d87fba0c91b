package org.springcenter.product.api.dto;
import com.jnby.common.BaseSerializable;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/26/21 11:15 AM
 */
public class QueryAiGoodsReq extends BaseSerializable {
    private static final long serialVersionUID = 3440843844882810738L;

    private String unionId;
    //小类
    private Long categoryId;
    //品牌ID
    private Long brandId;

    //上次选中的SKU
    private List<Long> selectedAliasId;

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public List<Long> getSelectedAliasId() {
        return selectedAliasId;
    }

    public void setSelectedAliasId(List<Long> selectedAliasId) {
        this.selectedAliasId = selectedAliasId;
    }
}
