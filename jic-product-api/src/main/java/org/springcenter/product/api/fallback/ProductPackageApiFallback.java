package org.springcenter.product.api.fallback;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import lombok.Setter;
import org.springcenter.product.api.IProductDetailsImgApi;
import org.springcenter.product.api.IProductPackageApi;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.dto.background.ProductPackageExportEntity;
import org.springcenter.product.api.dto.background.SearchProductIdInPacReq;
import org.springcenter.product.api.dto.background.SearchProductIdInPacResp;
import org.springcenter.product.api.dto.background.SearchProductIdIsExistResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ProductPackageApiFallback implements IProductPackageApi {
    @Setter
    private Throwable cause;


    @Override
    public ResponseResult<List<SearchProductIdInPacResp>> getPackageProductIdsForc(CommonRequest<SearchProductIdInPacReq> request) {
        return ResponseResult.success(Collections.emptyList());
    }

    @Override
    public ResponseResult<List<SearchProductIdIsExistResp>> getPackageIsExistProductIdsForc(CommonRequest<SearchProductIdInPacReq> request) {
        return ResponseResult.success(Collections.emptyList());
    }

    @Override
    public ResponseResult<List<ProductPackageExportEntity>> getProductPackageForc(CommonRequest<String> request) {
        return ResponseResult.success(Collections.emptyList());
    }
}
