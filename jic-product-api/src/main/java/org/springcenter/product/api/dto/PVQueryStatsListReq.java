package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date:2023/7/5 15:14
 */
@Data
@ApiModel(value = "配置列表查询")
public class PVQueryStatsListReq {

    @ApiModelProperty(value = "大类id")
    private Integer bigCategoryId;

    @ApiModelProperty(value = "小类id")
    private Integer smallCategoryId;

    @ApiModelProperty(value = "0是启用 1是禁用")
    private Integer isDisabled;
}
