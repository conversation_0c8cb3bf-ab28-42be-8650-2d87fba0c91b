package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


@ApiModel(value = "陈列册列表查询入参模型")
@Data
public class DisplayBookListForPosQueryReq {

    @ApiModelProperty(value = "年")
    private List<String> years;

    @ApiModelProperty(value = "品牌id")
    private List<Long> brandIds;

    @ApiModelProperty(value = "品牌名称")
    private List<String> brandNames;

    @ApiModelProperty(value = "门店ID")
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    @ApiModelProperty(value = "货季:年份+SS/AW", example = "2024SS")
    private String seasonGoods;

    @ApiModelProperty(value = "类型 1=陈列册 2=陈列指引")
    private Integer type = 1;
}
