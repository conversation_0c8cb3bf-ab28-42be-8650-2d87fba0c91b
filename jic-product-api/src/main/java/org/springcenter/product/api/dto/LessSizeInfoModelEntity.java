package org.springcenter.product.api.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date:2023/12/22 16:25
 */
@Data
@ApiModel(value = "Less尺码表格导出")
@EqualsAndHashCode
public class LessSizeInfoModelEntity {

    @ExcelProperty(value = "款号", index = 0)
    private String style_id;

    @ExcelProperty(value = "年份", index = 1)
    private String year;

    @ExcelProperty(value = "季节", index = 2)
    private String sample_id;

    @ExcelProperty(value = "品牌", index = 3)
    private String d_pp;

    @ExcelProperty(value = "部位", index = 4)
    private String part;

    @ExcelProperty(value = "测量说明", index = 5)
    private String clff;
    // 155/XS，160/S，165/M，170/L，175/XL
    //@ExcelProperty(value = "155/60A XS")
    @ExcelProperty(value = "155/XS")
    private String model1;

    //@ExcelProperty(value = "155/76A  XS")
    @ExcelProperty(value = "160/S")
    private String model2;

    //@ExcelProperty(value = "160/64A  S")
    @ExcelProperty(value = "165/M")
    private String model3;

    //@ExcelProperty(value = "160/80A  S")
    @ExcelProperty(value = "170/L")
    private String model4;

    //@ExcelProperty(value = "165/68A  M")
    @ExcelProperty(value = "175/XL")
    private String model5;

    /*@ExcelProperty(value = "165/84A  M")
    private String model6;

    @ExcelProperty(value = "170/72A  L")
    private String model7;

    @ExcelProperty(value = "170/88A  L")
    private String model8;

    @ExcelProperty(value = "175/76A  XL")
    private String model9;

    @ExcelProperty(value = "175/92A  XL")
    private String model10;*/
}
