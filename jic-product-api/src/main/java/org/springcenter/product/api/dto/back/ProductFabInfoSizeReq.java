package org.springcenter.product.api.dto.back;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date:2023/7/1 14:49
 */
@Data
@ApiModel(value = "尺码信息查询")
public class ProductFabInfoSizeReq {

    @ApiModelProperty(value = "商品id")
    @NotBlank(message = "商品id不能为空")
    private String productId;

    @ApiModelProperty(value = "是否需要尺码信息")
    private Boolean isNeedCliff = false;
}
