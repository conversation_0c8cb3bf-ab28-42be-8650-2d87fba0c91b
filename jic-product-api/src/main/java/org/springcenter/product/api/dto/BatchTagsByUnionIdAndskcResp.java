package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BatchTagsByUnionIdAndskcResp implements Serializable {


    @ApiModelProperty(value = "商品信息  款号和款色")
    private String productCodeAndColor;


    @ApiModelProperty(value = "款号")
    private String productCode;

    @ApiModelProperty(value = "款色")
    private String productColor;


    @ApiModelProperty(value = "0 无任何标签   1 搭配过  2 搭配过同款   3 购买过  4 购买过同款   5 加购过")
    private Integer status = 0;

    @ApiModelProperty(value = " 0 不展示推荐标签    1 展示推荐标签")
    private Integer inLook = 0;

}
