package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2023/6/14 16:18
 */
@Data
public class SearchLabelSceneResp {
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "场景id")
    private String sceneId;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "标签")
    private String labelCode;

    @ApiModelProperty(value = "渠道id")
    private String channelId;

    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "是都删除标识")
    private Integer isDeleted;

}
