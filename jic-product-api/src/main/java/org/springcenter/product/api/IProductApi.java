package org.springcenter.product.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.ApiOperation;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.factory.ProductApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;

@Component
@FeignClient(contextId = "productRemoteApi", value = "jic-product-api-center", fallbackFactory = ProductApiFallbackFactory.class)
public interface IProductApi {

    /**
     * 通过SKUID批量查询商品信息
     * @param skuIds
     * @return
     */
    @PostMapping("/product/api/selectGoodsListBySkuIds")
    ResponseResult<List<SkuResp>> selectGoodsListBySkuIds(@RequestBody CommonRequest<QuerySkuDto> skuIds);

    /**
     * 查询SPU列表,含有商品标签
     * @param request
     * @return
     */
    @PostMapping("/product/api/queryGoods")
    ResponseResult<List<GoodSpuResp>> queryGoods(@RequestBody CommonRequest<QueryGoodsReq> request);

    /**
     * 查询SKC列表
     * @param request
     * @return
     */
    @PostMapping("/product/api/queryGoodSkc")
    ResponseResult<List<ProductSkcResp>> queryGoodSkc(@RequestBody CommonRequest<QueryGoodsReq> request);

    /**
     * 查询商品SKC详情
     * @param request
     * @return
     */
    @PostMapping("/product/api/goodSkcDetail")
    ResponseResult<ProductSkcResp> goodSkcDetail(@RequestBody CommonRequest<ProductReq> request);

    /**
     * 查询新款样衣SKC
     * @param request
     * @return
     */
    @PostMapping("/product/api/searchSampleProductSkc")
    ResponseResult<List<SampleProductSkcResp>> searchSampleProductSkc(@RequestBody CommonRequest<SampleProductReq> request);

    /**
     * 查询相似商品
     */
    @PostMapping("/product/api/searchSimilarGoods")
    ResponseResult<List<SimilarProductResp>> searchSimilarGoods(@RequestBody CommonRequest<SimilarProductReq> request);

    /**
     * 查询经销库存
     */
    @PostMapping("/product/api/searchSkuAgentStorageByIds")
    ResponseResult<HashMap<Long, ProductAgentStockResp>> searchSkuAgentStorageByIds(@RequestBody CommonRequest<ProductAgentStockReq> request);

    /**
     * 查询同款商品
     * @param commonRequest
     * @return
     */
    @PostMapping("/product/api/searchSameSpuProduct")
    ResponseResult<List<SameSpuProductResp>> searchSameSpuProduct(@RequestBody CommonRequest<SameSpuProductReq> commonRequest);

    /**
     * 查询商品详情
     * @param request 入参
     * @return 返回
     */
    @PostMapping("/product/api/searchProductDetail")
    ResponseResult<GoodSpuDetailEntity> searchProductDetail(@RequestBody CommonRequest<String> request);

    /**
     * 根据微盟skuId、weId、productNo(spu) 查询商品库skuId
     */
    @PostMapping("/product/api/searchProductSkuIdByWeiMenInfo")
    ResponseResult<List<ProductSkuIdByWeiMenInfoResp>> searchProductSkuIdByWeiMenInfo(@RequestBody
                                                                                      CommonRequest<List<ProductByWeiMenInfoReq>> request);

    /**
     * 根据微盟skuId、weId、productNo(spu) 查询商品库信息 -- 先试后买
     */
    @PostMapping("/product/api/searchProductByWeiMenInfo")
    ResponseResult<List<ProductByWeiMenInfoResp>> searchProductByWeiMenInfo(@RequestBody
                                                                            CommonRequest<List<ProductByWeiMenInfoReq>> request);


    /**
     * 根据微盟skuId、weId、productNo(spu) 查询商品库信息 -- 陪逛
     */
    @PostMapping("/product/api/searchProductByWeiMenInfoNoType")
    ResponseResult<List<ProductByWeiMenInfoResp>> searchProductByWeiMenInfoNoType(@RequestBody
                                                                                  CommonRequest<List<ProductByWeiMenInfoReq>> request);

    /**
     * 根据微盟goodId返回是否是先试后买商品
     */
    @PostMapping("/product/api/searchProductIsTryBeforeBuy")
    ResponseResult<List<ProductIsTryBeforeBuyResp>> searchProductIsTryBeforeBuy(@RequestBody
                                                                                CommonRequest<ProductIsTryBeforeBuyReq> request);

    /**
     * 转换成微盟productId
     * @param request
     * @return
     */
    @PostMapping("/product/api/exchangeWeiMoProductId")
    ResponseResult<List<SearchWeiMoGoodInfoResp>> exchangeWeiMoProductId(@RequestBody
                                                                         CommonRequest<List<SearchWeiMoGoodInfoReq>> request);

    /**
     * 根据微盟的skuId和vid转换成为我们的skuId和productId
     */
    @PostMapping("/product/api/exchangeProductId")
    ResponseResult<List<ExchangeProductIdByWeiMoResp>> exchangeProductIdByWeiMo(@RequestBody
                                                                                CommonRequest<ExchangeProductIdByWeiMoReq> request);



    @PostMapping("/product/api/exchangeWeiMoProductIdAndSkuId")
    ResponseResult<List<SearchWeiMoGoodInfoResp>> exchangeWeiMoProductIdAndSkuId(@RequestBody
                                                                                 CommonRequest<List<SearchWeiMoGoodInfoReq>> request);

    @PostMapping("/product/api/getWeiMoGoodIdAndBJSkuId")
    ResponseResult<List<ExchangeByProductIdResp>> getWeiMoGoodIdAndBJSkuId(@RequestBody
                                                                           CommonRequest<ExchangeByProductIdReq> request);



    @PostMapping("/product/api/getProductInfoBySku")
    ResponseResult<SkuProductInfoResp> getProductInfoBySku(@RequestBody CommonRequest<String> request);


    @PostMapping("/product/api/queryGoodsIds")
    ResponseResult<List<String>> queryGoodsIds(@RequestBody CommonRequest<QueryGoodsReq> request);

    @PostMapping("/product/api/getSkuInfoByZdSkuId")
    ResponseResult<List<ZhuiDanSkuInfoResp>> getSkuInfoByZdSkuId(@RequestBody CommonRequest<List<Long>> request);

}
