package org.springcenter.product.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
@ApiModel("陈列册更新入参模型")
@Data
public class DisplayBookInfoUpdateReq {

    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空")
    private String Id;

    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank(message = "操作人不能为空")
    private String operator;

    @ApiModelProperty(value = "修改类型。UPDATE_DETAIL:编辑详情, UPDATE_TIME:编辑时间, PUBLISH:立即发布", required = true)
    @NotNull(message = "修改类型不能为空")
    private OperateType operateType;

    @ApiModelProperty(value = "详情。修改类型为 UPDATE_DETAIL 时必传")
    private String clobJson;

    @ApiModelProperty(value = "陈列册名称")
    private String bookName;

    @ApiModelProperty(value = "展示时间。修改类型为 UPDATE_TIME 时必传")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayTime;

    public enum OperateType {
        UPDATE_DETAIL(1, "编辑详情"),
        UPDATE_TIME(2, "编辑时间"),
        PUBLISH(3, "立即发布"),
        DELETE(4, "删除"),
        ;
        private Integer code;
        private String desc;
        OperateType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
}
