package org.springcenter.product.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import org.springcenter.product.api.dto.ProductBizTagResp;
import org.springcenter.product.api.dto.QueryProductBizTags;
import org.springcenter.product.api.factory.ProductBizTagApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@Component
@FeignClient(contextId = "productBizTagRemoteApi", value = "jic-product-api-center", fallbackFactory = ProductBizTagApiFallbackFactory.class)
public interface IProductBizTagApi {

    @PostMapping("/product/biztag/api/queryProductBizTags")
    ResponseResult<List<ProductBizTagResp>> queryProductBizTags(@RequestBody CommonRequest<QueryProductBizTags> request);
}
