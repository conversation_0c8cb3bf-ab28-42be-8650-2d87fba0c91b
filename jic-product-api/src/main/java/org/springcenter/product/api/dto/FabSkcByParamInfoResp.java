package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2023/10/31 15:48
 */
@Data
public class FabSkcByParamInfoResp {

    @ApiModelProperty(value = "款号")
    private String name;

    @ApiModelProperty(value = "品名")
    private String designName;

    @ApiModelProperty(value = "颜色code")
    private String colorCode;

    @ApiModelProperty(value = "颜色名称")
    private String colorName;

    @ApiModelProperty(value = "图片")
    private String img;

    @ApiModelProperty(value = "吊牌价")
    private String price;

    public static List<FabSkcByParamInfoResp> build(List<ProductSpuFabEsResp> resps) {
        List<FabSkcByParamInfoResp> rets = new ArrayList<>();
        resps.forEach(v -> {
            if (CollectionUtils.isEmpty(v.getColorNames())) {
                return;
            }
            v.getColorNames().forEach(x -> {
                FabSkcByParamInfoResp skc = new FabSkcByParamInfoResp();
                skc.setName(v.getName());
                skc.setPrice(Objects.toString(v.getPrice()));
                skc.setImg(x.getImg());
                skc.setColorCode(x.getColor_code());
                skc.setColorName(x.getColor_name());
                skc.setDesignName(v.getSmall_class());
                rets.add(skc);
            });
        });
        return rets;
    }
}
