package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2024/5/22 14:39
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum FabVolumeTypeEnum {

    ZHI_YING(0, "直营"),
    DISTRIBUTION(1, "经销");

    private Integer code;

    private String desc;


    public static String getByCode(Integer code) {
        FabVolumeTypeEnum fabVolumeTypeEnum = Arrays.stream(FabVolumeTypeEnum.values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst().orElse(null);
        return fabVolumeTypeEnum == null ? "" : fabVolumeTypeEnum.getDesc();
    }

}
