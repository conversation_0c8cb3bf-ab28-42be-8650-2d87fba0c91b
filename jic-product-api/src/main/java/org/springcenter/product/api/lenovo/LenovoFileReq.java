package org.springcenter.product.api.lenovo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LenovoFileReq {

    @ApiModelProperty(value = "需要查询的路径")
    private String path;

    @ApiModelProperty(value = "企业空间 ent  个人空间 self")
    private String path_type = "ent";

    @ApiModelProperty(value = "排序方式 asc desc")
    private String sort = "desc";

    @ApiModelProperty(value = "name  文件名称 size  大小  mtime 更新时间")
    private String order_by = "mtime";

    @ApiModelProperty(value = "页码 默认 0")
    private String page_num = "0";

    @ApiModelProperty(value = "默认一页 900个")
    private String page_size = "900";



}
