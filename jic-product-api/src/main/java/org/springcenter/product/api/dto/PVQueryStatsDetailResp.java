package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/7/6 10:08
 */
@Data
public class PVQueryStatsDetailResp {

    @ApiModelProperty(value = "属性id")
    private Long attributeId;

    @ApiModelProperty(value = "属性名称")
    private String attributeName;

    @ApiModelProperty(value = "属性code")
    private String attributeCode;

    @ApiModelProperty(value = "属性值")
    private List<AttrData> attrValue;

    @ApiModelProperty(value = "二级属性值")
    private List<AttrData> secondAttrValue;

    @ApiModelProperty(value = "类目")
    private List<CategoryData> mainCategoriesConnect;

    @ApiModelProperty(value = "二级属性应用")
    private List<AttrValueData> attrValueConnectDataList;


    @Data
    public static class AttrData {
        @ApiModelProperty(value = "属性id")
        private Long id;

        @ApiModelProperty(value = "属性code")
        private String code;

        @ApiModelProperty(value = "属性名称")
        private String desc;

        @ApiModelProperty(value = "0是没选中 1是选中")
        private Integer isSelected;
    }

    @Data
    public static class CategoryData {
        @ApiModelProperty(value = "属性id")
        private Long id;

        @ApiModelProperty(value = "属性code")
        private String code;

        @ApiModelProperty(value = "属性名称")
        private String desc;

        @ApiModelProperty(value = "0是没选中 1是选中")
        private Integer isSelected;

        @ApiModelProperty(value = "选中的关联id")
        private String connectId;

        @ApiModelProperty(value = "子类目")
        private List<CategoryData> subCategories;
    }


    @Data
    public static class AttrValueData {
        @ApiModelProperty(value = "大类属性id")
        private Long bigCategoryId;

        @ApiModelProperty(value = "大类属性code")
        private String bigCategoryCode;

        @ApiModelProperty(value = "大类属性名称")
        private String bigCategoryDesc;

        @ApiModelProperty(value = "小类属性id")
        private Long smallCategoryId;

        @ApiModelProperty(value = "小类属性code")
        private String smallCategoryCode;

        @ApiModelProperty(value = "小类属性名称")
        private String smallCategoryDesc;

        @ApiModelProperty(value = "选中二级属性")
        private List<ConnectAttrData> attrDataList;
    }

    @Data
    public static class ConnectAttrData {
        @ApiModelProperty(value = "全属性id")
        private Long id;

        @ApiModelProperty(value = "属性code")
        private String code;

        @ApiModelProperty(value = "属性名称")
        private String desc;

        @ApiModelProperty(value = "0是未选中 1是选中")
        private Integer isSelected;

        @ApiModelProperty(value = "0正常 1是已删除")
        private Integer isDeleted;

        @ApiModelProperty(value = "关联id")
        private String connectId;
    }


}
