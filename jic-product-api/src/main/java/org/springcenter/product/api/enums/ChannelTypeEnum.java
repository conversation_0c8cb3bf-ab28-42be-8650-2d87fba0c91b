package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum ChannelTypeEnum {

    DIRECT(0, "直营"),
    DISTRIBUTION(1, "经销");

    private Integer code;

    private String desc;

    /**
     * 获取所有的枚举desc
     * @return
     */
    public static List<String> listDesc() {
        return Arrays.stream(ChannelTypeEnum.values())
                .map(ChannelTypeEnum::getDesc)
                .collect(Collectors.toList());
    }


}
