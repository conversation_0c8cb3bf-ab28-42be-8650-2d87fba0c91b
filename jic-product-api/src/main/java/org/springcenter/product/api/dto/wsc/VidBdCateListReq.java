package org.springcenter.product.api.dto.wsc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/2/21 13:42
 */
@Data
public class VidBdCateListReq {

    @ApiModelProperty(value = "门店id")
    @NotBlank(message = "门店id不能为空")
    private String vid;

    @ApiModelProperty(value = "小类id")
    @NotBlank(message = "小类id不能为空")
    private String smallCateId;

    @ApiModelProperty(value = "大类id")
    @NotBlank(message = "大类id不能为空")
    private String bigCateId;

    @ApiModelProperty(value = "品牌id")
    @NotBlank(message = "品牌id不能为空")
    private String weId;

    @ApiModelProperty(value = "商品品牌id")
    private Long proBrandId;


}
