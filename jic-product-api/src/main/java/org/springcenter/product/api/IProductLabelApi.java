package org.springcenter.product.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import org.springcenter.product.api.dto.MarkingLabelReq;
import org.springcenter.product.api.dto.SysProductLabelResp;
import org.springcenter.product.api.factory.ProductLabelApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(contextId = "productLabelRemoteApi", value = "jic-product-api-center", fallbackFactory = ProductLabelApiFallbackFactory.class)
public interface IProductLabelApi {


    /**
     * 商品打标
     * @param request
     * @return
     */
    @PostMapping("/product/label/api/markingLabel")
    ResponseResult markingLabel(@RequestBody CommonRequest<MarkingLabelReq> request);

    /**
     * 批量查询商品标签
     * @param skcCodes
     * @return
     */
    @PostMapping("/product/label/api/getProductLabelBySkcCodes")
    ResponseResult<List<SysProductLabelResp>> getProductLabelBySkcCodes(@RequestBody List<String> skcCodes);
}
