package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ProductInLooksReq {

    @ApiModelProperty(value = "款号")
    @NotBlank(message = "款号不能为空")
    private String productCode;


    @ApiModelProperty(value = "颜色编码")
    @NotBlank(message = "颜色编码不能为空")
    private String colorNo;

    @ApiModelProperty(value = "选中的货舱信息")
    private String storeId;
}
