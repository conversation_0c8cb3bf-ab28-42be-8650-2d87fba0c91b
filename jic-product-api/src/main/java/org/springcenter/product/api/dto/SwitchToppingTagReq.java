package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date:2023/5/16 10:32
 */
@Data
public class SwitchToppingTagReq {
    @NotBlank(message = "id不能为空")
    @ApiModelProperty(value = "id")
    private String id;

    @NotBlank(message = "操作按钮不能为空")
    @ApiModelProperty(value = "是否开启 0：关闭 1：开启")
    private Integer isOpen;
}
