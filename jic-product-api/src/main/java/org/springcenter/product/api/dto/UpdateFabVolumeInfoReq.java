package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date:2023/11/14 15:14
 */
@Data
public class UpdateFabVolumeInfoReq{

    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空")
    private String Id;

    @ApiModelProperty(value = "操作人")
    @NotBlank(message = "操作人不能为空")
    private String operator;

    @ApiModelProperty(value = "大json")
    @NotBlank(message = "大json不能为空")
    private String clobJson;

    @ApiModelProperty(value = "标题名称")
    @NotBlank(message = "标题名称不能为空")
    private String fabName;
}
