package org.springcenter.product.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import org.springcenter.product.api.dto.ProductFabInfoReq;
import org.springcenter.product.api.dto.ProductSizeInfoResp;
import org.springcenter.product.api.factory.IProductToppingTagApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/3/19 11:53
 */
@Component
@FeignClient(contextId = "productFabApi", value = "jic-product-api-center", fallbackFactory = IProductToppingTagApiFallbackFactory.class)
public interface IProductFabApi {

    @PostMapping("/product/fab/api/query/fab/size/info")
    public ResponseResult<List<ProductSizeInfoResp>> queryFabSizeInfo(@RequestBody CommonRequest<ProductFabInfoReq> request);
}
