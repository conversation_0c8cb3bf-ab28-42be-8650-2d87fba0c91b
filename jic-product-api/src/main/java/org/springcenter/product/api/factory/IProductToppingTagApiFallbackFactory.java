package org.springcenter.product.api.factory;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.api.IProductToppingTagApi;
import org.springcenter.product.api.fallback.IProductToppingTagApiFallback;

/**
 * <AUTHOR>
 * @Date:2023/5/25 19:55
 */
@Slf4j
public class IProductToppingTagApiFallbackFactory  implements FallbackFactory<IProductToppingTagApi> {

    @Override
    public IProductToppingTagApi create(Throwable throwable) {
        IProductToppingTagApiFallback fallback = new IProductToppingTagApiFallback();
        fallback.setCause(throwable);
        return fallback;
    }
}
