package org.springcenter.product.api.dto;

import com.alibaba.fastjson.JSONObject;
import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ProductSpuFabEsResp extends RemotingSerializable implements Serializable {
    @ApiModelProperty(value = "pruductId")
    private String id;

    @ApiModelProperty(value = "不同skc图")
    private String color_url_list;

    @ApiModelProperty(value = "不同skc名称图")
    private String color_name_list;

    @ApiModelProperty(value = "款号")
    private String name;

    @ApiModelProperty(value = "商品名称")
    private String value;

    @ApiModelProperty(value = "品牌名称")
    private String brand;

    @ApiModelProperty(value = "品牌架构id")
    private Long c_arcbrand_id = 0L;

    @ApiModelProperty(value = "年份")
    private String a_year;

    @ApiModelProperty(value = "小季节")
    private String samll_season;

    @ApiModelProperty(value = "小季节id")
    private Long a_small_season_id = 0L;

    @ApiModelProperty(value = "波段名称")
    private String band;

    @ApiModelProperty(value = "波段id")
    private Long band_id = 0L;

    @ApiModelProperty(value = "大类")
    private String big_class;

    @ApiModelProperty(value = "小类")
    private String small_class;

    @ApiModelProperty(value = "小类id")
    private Long small_class_id = 0L;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "主题")
    private String topic;

    @ApiModelProperty(value = "标签层级")
    private List<String> label_levels;

    @ApiModelProperty(value = "标签属性")
    private List<String> labels;

    @ApiModelProperty(value = "款号")
    private String style_id;

    @ApiModelProperty(value = "品名")
    private String design_name;

    @ApiModelProperty(value = "品牌")
    private String pp;

    @ApiModelProperty(value = "商品货季")
    private String good_season;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "对应小季节")
    private String corressample_id;

    @ApiModelProperty(value = "波段")
    private String season;

    @ApiModelProperty(value = "大类")
    private String ccchr3;

    @ApiModelProperty(value = "小类")
    private String ccchr5;

    @ApiModelProperty(value = "吊牌价")
    private String sell_price;

    @ApiModelProperty(value = "面料名称")
    private String bom_name;

    @ApiModelProperty(value = "图案大类")
    private String pattern_type;

    @ApiModelProperty(value = "成分面料")
    private String sccf;

    @ApiModelProperty(value = "图案名称")
    private String pattern_name;

    @ApiModelProperty(value = "面料特征")
    private String sc_quality;

    @ApiModelProperty(value = "是否有里")
    private String you_li;

    @ApiModelProperty(value = "原料产地")
    private String sc_environmental;

    @ApiModelProperty(value = "填充物")
    private String filler;

    @ApiModelProperty(value = "材料等级")
    private String sc_material_level;

    @ApiModelProperty(value = "填充量")
    private String fill_content;

    @ApiModelProperty(value = "环保标签")
    private String sc_origin_area;

    @ApiModelProperty(value = "内配[配件]")
    private String np_name;

    @ApiModelProperty(value = "产品弹力")
    private String sc_elastic;

    @ApiModelProperty(value = "功能性")
    private String function;

    @ApiModelProperty(value = "硬挺度")
    private String sc_stiffness;

    @ApiModelProperty(value = "外观")
    private String facade;

    @ApiModelProperty(value = "布面效果")
    private String sc_effect;

    @ApiModelProperty(value = "厚薄")
    private String thick;

    @ApiModelProperty(value = "服用功能")
    private String sc_fzyt;

    @ApiModelProperty(value = "体型")
    private String tixing;

    /*@ApiModelProperty(value = "产品风险评估")
    private String sc_riskassessment;*/

    @ApiModelProperty(value = "合体度")
    private String weidu;


    @ApiModelProperty(value = "长度")
    private String changdu;

    @ApiModelProperty(value = "袖长")
    private String xiuchang;

    @ApiModelProperty(value = "是否追单 0是追单 1不是追单")
    private Integer not_zhuidan;

    @ApiModelProperty(value = "亲子款")
    private String qzk;

    @ApiModelProperty(value = "产品推广资料")
    private String sc_popularize;

    @ApiModelProperty(value = "辅料卖点")
    private String acc_point;

    @ApiModelProperty(value = "fab说明")
    private String fab;

    @ApiModelProperty(value = "穿着方式")
    private String czfs;

    @ApiModelProperty(value = "洗涤说明")
    private String wash_name;

    @ApiModelProperty(value = "洗涤图片")
    private String wash_no;

    @ApiModelProperty(value = "搭配图")
    private String collocation_list;

    @ApiModelProperty(value = "细节图片")
    private String particulars_list;

    @ApiModelProperty(value = "图案图片")
    private String pattern_url_list;

    @ApiModelProperty(value = "图案名称")
    private String pattern_name_list;

    @ApiModelProperty(value = "商检成分")
    private String sjcf;

    @ApiModelProperty(value = "不同色名称图片")
    private List<ColorName> colorNames;

    @ApiModelProperty(value = "搭配图")
    private List<MatchPic> matchPics;

    @ApiModelProperty(value = "细节图")
    private List<DetailPic> detailPics;

    @ApiModelProperty(value = "skc图")
    private List<String> colorPics;

    @ApiModelProperty(value = "图案图片")
    private List<String> patternUrls;

    @ApiModelProperty(value = "图案名称")
    private List<String> patternNames;

    @ApiModelProperty(value = "产品工艺")
    private String scfj_gy;

    @ApiModelProperty(value = "工艺细节")
    private String gyxj;

    @ApiModelProperty(value = "特殊工艺小类")
    private String tyxl;

    @ApiModelProperty(value = "特殊工艺明细")
    private String tymx;

    @ApiModelProperty(value = "面料进口地")
    private String bom_area;

    @ApiModelProperty(value = "搭配建议")
    private String match_advice;

    @ApiModelProperty(value = "是否有160")
    private String has160;

    @ApiModelProperty(value = "是否有170")
    private String has170;

    @ApiModelProperty(value = "产品细分类")
    private String scxfl;

    @ApiModelProperty(value = "系列名称")
    private String file1_name;

    @ApiModelProperty(value = "设计师")
    private String designer;

    @ApiModelProperty(value = "工艺师")
    private String gongyishi;

    @ApiModelProperty(value = "搭配图")
    private List<MatchPicExpend> matchPicExpends;

    @ApiModelProperty(value = "图案相关信息")
    private List<PatternInfo> pattern_info_list;

    @ApiModelProperty(value = "主推设计")
    private String design_logo;

    @ApiModelProperty(value = "尺码编号")
    private String size_name;

    @ApiModelProperty(value = "针型")
    private String pin_type;

    @ApiModelProperty(value = "组织")
    private String tex_ture;

    @ApiModelProperty(value = "性别")
    private String bandg;

    @ApiModelProperty(value = "领型")
    private String ling_xing;

    @ApiModelProperty(value = "袖型")
    private String xiu;

    @ApiModelProperty(value = "绗缝切线")
    private String hfqx;

    @ApiModelProperty(value = "口袋样式")
    private String kou_dai;

    @ApiModelProperty(value = "下摆设计")
    private String xia_bai;

    @ApiModelProperty(value = "开衩方式")
    private String kai_cha;

    @ApiModelProperty(value = "衣门襟")
    private String yi_men_jin;

    @ApiModelProperty(value = "下装门襟")
    private String ku_men_jin;

    @ApiModelProperty(value = "裤脚口")
    private String ku_jiao_kou;

    @ApiModelProperty(value = "成衣弹力")
    private String cloth_elastic;

    @ApiModelProperty(value = "细分类")
    private String cpdl;

    @ApiModelProperty(value = "产品大类")
    private String scdl;


    @ApiModelProperty(value = "直营——定量")
    private Long quantify_qty;

    @ApiModelProperty(value = "经销定量")
    private Long jx_quantify_qty;

    @ApiModelProperty(value = "袖最大")
    private String xiu_max_size;

    @ApiModelProperty(value = "袖最小")
    private String xiu_min_size;

    @ApiModelProperty(value = "衣长最小")
    private String chang_min_size;

    @ApiModelProperty(value = "衣长最大")
    private String chang_max_size;

    @ApiModelProperty(value = "风格信息")
    private String style_list;

    private List<StyleInfo> styleInfos;

    @Data
    public static class StyleInfo extends RemotingSerializable implements Serializable{
        @ApiModelProperty(value = "标签code")
        private String label_code;

        @ApiModelProperty(value = "标签名称")
        private String label_name;

        @ApiModelProperty(value = "skc")
        private String skc_code;

    }

    @Data
    public static class PatternInfo extends RemotingSerializable implements Serializable{
        @ApiModelProperty(value = "图案链接")
        private String pattern_url;

        @ApiModelProperty(value = "图案名称")
        private String pattern_name;

        @ApiModelProperty(value = "灵感")
        private String inspiration;

        @ApiModelProperty(value = "图案类型")
        private String pattern_type;

        @ApiModelProperty(value = "合作艺术家")
        private String artist;

        @ApiModelProperty(value = "文字含义说明")
        private String meaning_desc;
    }

    @Data
    public static class ColorName extends RemotingSerializable implements Serializable{
        @ApiModelProperty(value = "skc")
        private String skc_code;

        @ApiModelProperty(value = "色代码")
        private String color_code;

        @ApiModelProperty(value = "色名称")
        private String color_name;

        @ApiModelProperty(value = "图片")
        private String img;

        @ApiModelProperty(value = "透明抠图")
        private String matted_img;

        @ApiModelProperty(value = "keylook_logo")
        private String key_look_logo;

        @ApiModelProperty(value = "明星同款")
        private String star_logo;

        @ApiModelProperty(value = "定T30%")
        private String rate_logo;

        @ApiModelProperty(value = "自动风格")
        private String style_name;

        @ApiModelProperty(value = "面料号")
        private String bom_code;

        @ApiModelProperty(value = "面料名称")
        private String bom_name;

        @ApiModelProperty(value = "是否存在OEKOTEX认证")
        private String is_oekotex;
    }

    @Data
    public static class MatchPic extends RemotingSerializable implements Serializable{
        @ApiModelProperty(value = "图片")
        private String url;

        @ApiModelProperty(value = "名称")
        private String name;

        @ApiModelProperty(value = "01 正面")
        private String value;
    }


    @Data
    public static class MatchPicExpend extends RemotingSerializable implements Serializable{
        @ApiModelProperty(value = "序号")
        private Integer id;

        @ApiModelProperty(value = "图片")
        private String url;

        @ApiModelProperty(value = "名称")
        private String name;

        @ApiModelProperty(value = "01 正面")
        private String value;

        @ApiModelProperty(value = "重点突出")
        private Integer isKey = 0;

        List<MatchPicExpendData> matchPicRecSpus;
    }

    @Data
    public static class MatchPicExpendData extends RemotingSerializable implements Serializable{
        @ApiModelProperty(value = "图片")
        private String productId;

        @ApiModelProperty(value = "skc")
        private String skc;

        @ApiModelProperty(value = "名称")
        private String value;
    }

    @Data
    public static class DetailPic extends RemotingSerializable implements Serializable{
        @ApiModelProperty(value = "图片")
        private String url;

        @ApiModelProperty(value = "名称")
        private String name;

        @ApiModelProperty(value = "")
        private String COLOR_ID;

        private String value;
    }
    public void buildColorNameList(){
        if (this.getColor_name_list() == null || "".equals(this.getColor_name_list())){
            return;
        }
        this.colorNames = ProductSpuFabEsResp.ColorName.decodeArr(this.getColor_name_list(), ProductSpuFabEsResp.ColorName.class);
    }

    public void buildMatchPic(){
        if (this.getCollocation_list() == null || "".equals(this.getCollocation_list())){
            return;
        }
        this.matchPics = ProductSpuFabEsResp.MatchPic.decodeArr(this.getCollocation_list(), ProductSpuFabEsResp.MatchPic.class);
    }

    public void buildDetailPic(){
        if (this.getParticulars_list() == null || "".equals(this.getParticulars_list())){
            return;
        }
        this.detailPics = ProductSpuFabEsResp.DetailPic.decodeArr(this.getParticulars_list(), ProductSpuFabEsResp.DetailPic.class);
    }


    public void buildStyleInfo(){
        if (this.getStyle_list() == null || "".equals(this.getStyle_list())){
            return;
        }
        this.styleInfos = ProductSpuFabEsResp.StyleInfo.decodeArr(this.getStyle_list(), ProductSpuFabEsResp.StyleInfo.class);
    }

    public void buildColorUrlList(){
        if (this.getColor_url_list() == null || "".equals(this.getColor_url_list())){
            return;
        }
        this.colorPics = JSONObject.parseArray(this.getColor_url_list(), String.class);
    }

    public void buildPatternUrlList(){
        if (this.getPattern_url_list() == null || "".equals(this.getPattern_url_list())){
            return;
        }
        this.patternUrls = JSONObject.parseArray(this.getPattern_url_list(), String.class);
    }

    public void buildPatternList(){
        if (this.getPattern_name_list() == null || "".equals(this.getPattern_name_list())){
            return;
        }
        this.patternNames = JSONObject.parseArray(this.getPattern_name_list(), String.class);
    }
}
