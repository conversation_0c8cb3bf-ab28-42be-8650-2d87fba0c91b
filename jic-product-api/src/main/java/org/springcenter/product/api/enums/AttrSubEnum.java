package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2022/12/5 14:12
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum AttrSubEnum {

    T("296", getAttrs(), "T"),;

    private String categoryCode;

    private List<AttributeSub> attrs;

    private String desc;

    public static AttrSubEnum getByCategory(String code) {
        return Arrays.stream(AttrSubEnum.values()).filter(v -> {
            return Objects.equals(v.getCategoryCode(), code);
        }).findFirst().orElse(null);
    }

    @Data
    public static class AttributeSub {
        private String mainAttr;

        private List<String> subAttr;
    }

    public static List<AttributeSub> getAttrs() {
        List<AttributeSub> list = new ArrayList<>();
        AttributeSub attributeSub = new AttributeSub();
        //合体度
        attributeSub.setMainAttr("Q1");
        List<String> sub = new ArrayList<>();
        sub.add("胸围");
        sub.add("腰围");
        sub.add("摆围");
        attributeSub.setSubAttr(sub);
        list.add(attributeSub);
        return list;
    }

}
