package org.springcenter.product.api.dto.background;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ChangeStatusReq {

    @ApiModelProperty(value = "主表主键id")
    private String id;

    @ApiModelProperty(value = " 状态  0 未审核  1 复审  2 审核完成")
    private Integer status;

    @ApiModelProperty(value = "标记信息  无问题 面料问题 等等")
    private String remark;

    @ApiModelProperty(value = "标记信息")
    private List<SubmitRelationShip> submitRelationShipList;


    @Data
    public static class SubmitRelationShip{

        private String wrongGoodsProductId;

        private String wrongGoodsImgsId;

    }



}
