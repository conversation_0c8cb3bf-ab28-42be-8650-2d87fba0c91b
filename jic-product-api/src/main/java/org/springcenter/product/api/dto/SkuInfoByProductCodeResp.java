package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2024/4/12 9:30
 */
@Data
public class SkuInfoByProductCodeResp {

    @ApiModelProperty(value = "规格id")
    private Long skuId;

    @ApiModelProperty(value = "产品编码")
    private String productCode;

    @ApiModelProperty(value = "颜色")
    private String colorNo;

    @ApiModelProperty(value = "码")
    private String sizeNo;
}
