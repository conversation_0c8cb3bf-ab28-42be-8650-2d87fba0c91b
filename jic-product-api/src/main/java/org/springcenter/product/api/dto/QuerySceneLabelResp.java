package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2023/5/19 15:33
 */
@Data
public class QuerySceneLabelResp {

    @ApiModelProperty(value = "场景标签关系id")
    private String id;

    @ApiModelProperty(value = "标签code")
    private String labelCode;

    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "标签类型 人工标签(0500)和系统标签(0501)")
    private String labelType;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "删除")
    private Integer isDeleted;
}
