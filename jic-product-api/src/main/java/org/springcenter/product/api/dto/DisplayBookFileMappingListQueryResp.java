package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("陈列册文件映射列表查询响应数据")
@Data
public class DisplayBookFileMappingListQueryResp {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "货季")
    private String seasonGoods;

    @ApiModelProperty(value = "品牌")
    private String brandName;

    @ApiModelProperty(value = "渠道")
    private String channel;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "保存时间")
    private String createTime;

    @ApiModelProperty(value = "人台图Excel地址。为空代表未上传")
    private String mannequinExcelUrl;

    @ApiModelProperty(value = "货杆图Excel地址。为空代表未上传")
    private String hangerExcelUrl;


}
