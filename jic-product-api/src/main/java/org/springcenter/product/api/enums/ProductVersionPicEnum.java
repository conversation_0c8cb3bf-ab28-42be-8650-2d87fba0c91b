package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2022/12/6 9:43
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ProductVersionPicEnum {

    FRONT(0, "正面", 38),
    LEFT(1, "左面", 39),
    RIGHT(2, "右面", 40),
    BEHIND(3, "后面", 41),
    DETAILS(4, "细节", 42),;

    private Integer code;

    private String desc;

    private Integer excelPos;

    public static ProductVersionPicEnum getByExcelPos(int i) {
        return Arrays.stream(ProductVersionPicEnum.values())
                .filter(v -> Objects.equals(v.getExcelPos(), i))
                .findFirst()
                .orElse(null);
    }
}
