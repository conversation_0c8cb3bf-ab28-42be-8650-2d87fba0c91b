package org.springcenter.product.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/6/11 17:38
 */
@Data
public class SingleProductRecommendationReq {

    @ApiModelProperty(value = "unionId")
    private String unionId;

    @ApiModelProperty(value = "性别 男：male  女：female 未知：unknown")
    private String sex;

    @ApiModelProperty(value = "根据推的商品id")
    @NotBlank(message = "根据推的商品id不能为空")
    private String productId;

    @ApiModelProperty(value = "门店信息")
    @NotEmpty(message = "门店信息为空")
    private List<Store> storeList;

    @Data
    public static class Store {

        private String storeId;

        private String brandId;
    }
}
