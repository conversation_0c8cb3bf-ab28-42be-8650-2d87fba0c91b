package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.dto.*;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Date:2023/12/22 17:37
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum BrandSizeInfoEntityEnum {

    JNBY(2L, "JNBY", JnbySizeInfoModelEntity.class),
    LESS(5L, "LESS", LessSizeInfoModelEntity.class),
    PM(12L, "蓬马", PmSizeInfoModelEntity.class),
    SX(3L, "CROQUIS", SxSizeInfoModelEntity.class),
    TZ(4L, "童装", TzSizeInfoModelEntity.class),
    YT(4L, "婴童", YtSizeInfoModelEntity.class),
    APN(57L, "APN 73", ApnSizeInfoModelEntity.class),
    RE(77L, "RE;RE;RE;LAB", ReSizeInfoModelEntity.class),;

    private Long cArcBrandId;

    private String brandName;

    private Class cla;


    public static BrandSizeInfoEntityEnum getClassByBrand(Long cArcBrandId, String brandName) {
        if (StringUtils.isBlank(brandName)) {
            return Arrays.stream(BrandSizeInfoEntityEnum.values())
                    .filter(v -> Objects.equals(v.getCArcBrandId(), cArcBrandId))
                    .findFirst()
                    .orElse(null);
        } else {
            return Arrays.stream(BrandSizeInfoEntityEnum.values())
                    .filter(v -> Objects.equals(v.getCArcBrandId(), cArcBrandId) && Objects.equals(v.getBrandName(), brandName))
                    .findFirst()
                    .orElse(null);
        }
    }

    public static BrandSizeInfoEntityEnum getClassByBrands(Long cArcBrandId, List<String> brandNames) {
        if (CollectionUtils.isEmpty(brandNames)) {
            return Arrays.stream(BrandSizeInfoEntityEnum.values())
                    .filter(v -> Objects.equals(v.getCArcBrandId(), cArcBrandId))
                    .findFirst()
                    .orElse(null);
        } else {
            return Arrays.stream(BrandSizeInfoEntityEnum.values())
                    .filter(v -> Objects.equals(v.getCArcBrandId(), cArcBrandId) && brandNames.contains(v.getBrandName()))
                    .findFirst()
                    .orElse(null);
        }
    }
}
