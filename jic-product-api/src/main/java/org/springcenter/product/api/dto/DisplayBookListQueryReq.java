package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;


@ApiModel(value = "陈列册列表查询入参模型")
@Data
public class DisplayBookListQueryReq {

    @ApiModelProperty(value = "年")
    private List<String> years;

    @ApiModelProperty(value = "波段id")
    private List<Long> bandIds;

    @ApiModelProperty(value = "品牌id")
    private List<Long> brandIds;

    @ApiModelProperty(value = "货季")
    private List<String> goodSeason;

    @ApiModelProperty(value = "品牌名称")
    private List<String> brandNames;

    @ApiModelProperty(value = "渠道:直营/经销")
    private String channel;

    @ApiModelProperty(value = "类型 1=陈列册 2=陈列指引")
    private Integer type = 1;
}
