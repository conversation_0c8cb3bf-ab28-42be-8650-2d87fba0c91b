package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/7 15:28
 */
@Data
@ApiModel(value = "编辑版型商品详情的入参")
public class ProductVersionEditDetailReq {

    @ApiModelProperty(value = "id")
    @NotBlank(message = "id必填")
    private String id;

    @ApiModelProperty(value = "品牌Code")
    private String brandCode;

    @ApiModelProperty(value = "主料")
    private String mainIngredient;

    @ApiModelProperty(value = "主料编号")
    private String mainIngredientNumber;

    @ApiModelProperty(value = "0开启 1关闭")
    private Integer isClose;

    @ApiModelProperty(value = "小品名")
    private String smallName;

    @ApiModelProperty(value = "属性")
    private List<ProductVersionDetailResp.BaseInfo> attrs;

}
