package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2022/12/6 10:35
 */
@Data
public class ProductVersionListDataResp {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "图片")
    private String pic;

    @ApiModelProperty(value = "样衣号")
    private String sampleNo;

    @ApiModelProperty(value = "版型号")
    private String modelNumber;

    @ApiModelProperty(value = "合体度名称")
    private String degree;

    @ApiModelProperty(value = "类目名称")
    private String category;

    @ApiModelProperty(value = "引用次数")
    private String citationNumber;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "skcId")
    private String skc_id;

    @ApiModelProperty(value = "小品名")
    private String smallName;
}
