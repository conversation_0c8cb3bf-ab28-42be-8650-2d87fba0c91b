package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel("陈列册列表查询响应数据")
@Data
public class DisplayBookListForPosQueryResp {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "货季")
    private String seasonGoods;

    @ApiModelProperty(value = "品牌")
    private String brandName;

    @ApiModelProperty(value = "渠道")
    private String channel;

    @ApiModelProperty(value = "波段")
    private String bandName;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "展示时间")
    private String displayTime;

    @ApiModelProperty(value = "保存时间")
    private String updateTime;

    @ApiModelProperty(value = "状态  0：未解锁  1：已解锁")
    private Integer status;

    @ApiModelProperty(value = "x天后结束")
    private Integer day;

    @ApiModelProperty(value = "陈列册名称")
    private String bookName;

    @ApiModelProperty(value = "类型 1=陈列册 2=陈列指引")
    private Integer type = 1;
}
