package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springcenter.product.api.enums.DisplayBookPictureEnum;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


@ApiModel(value = "人台图上传入参模型")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisplayBookPictureMappingAddReq {
    @ApiModelProperty(value = "主键ID")
    @NotBlank(message = "主键ID不能为空")
    private String id;
    @ApiModelProperty(value = "操作人")
    @NotBlank(message = "操作人不能为空")
    private String operator;
    @ApiModelProperty(value = "excel地址")
    @NotBlank(message = "excel地址不能为空")
    private String url;
    @ApiModelProperty(value = "图片类型:PICTURE_MANNEQUIN=人台图;PICTURE_HANGER=货杆图")
    @NotNull(message = "图片类型不能为空")
    private DisplayBookPictureEnum type;
}
