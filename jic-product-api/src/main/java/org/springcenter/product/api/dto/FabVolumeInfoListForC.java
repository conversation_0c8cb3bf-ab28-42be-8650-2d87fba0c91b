package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2023/11/14 16:21
 */
@Data
public class FabVolumeInfoListForC {

    @ApiModelProperty(value = "产品册id")
    private String id;

    @ApiModelProperty(value = "产品册名称")
    private String fabName;

    @ApiModelProperty(value = "状态  0：未解锁  1：已解锁")
    private Integer status;

    @ApiModelProperty(value = "品牌id")
    private Long brandCode;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "波段id")
    private Long bandId;

    @ApiModelProperty(value = "波段名称")
    private String bandName;

    @ApiModelProperty(value = "货季")
    private String seasonGoods;

    @ApiModelProperty(value = "年份")
    private String year;

}
