package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2023/7/4 15:37
 */
@Data
public class ProductSpuFabImgInfoResp {


    @ApiModelProperty(value = "skc图")
    private List<ImgData> imgDataList;

    @ApiModelProperty(value = "风格信息")
    private List<StyleData> styleList;

    @Data
    public static class StyleData {
        @ApiModelProperty(value = "标签名称")
        private String style;

        @ApiModelProperty(value = "标签")
        private String labelCode;
    }

    public static ProductSpuFabImgInfoResp build(List<ProductSpuFabEsResp> resps, String skc, Map<String, BuildFabImgInfo> buildFabImgInfo) {
        if (CollectionUtils.isEmpty(resps)) {
            return null;
        }
        List<ProductSpuFabEsResp.ColorName> colorNames = resps.get(0).getColorNames();
        List<ProductSpuFabEsResp.StyleInfo> styleInfos = resps.get(0).getStyleInfos();
        ProductSpuFabImgInfoResp resp = new ProductSpuFabImgInfoResp();

        List<ImgData> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(colorNames)) {
            colorNames.stream().filter(v -> Objects.equals(v.getSkc_code(), skc)).forEach(v -> {
                ImgData imgData = new ImgData();
                // skc图用网盘图
                /*if (StringUtils.isNotBlank(v.getImg()) && StringUtils.contains(v.getImg(), "weimob")) {
                    imgData.setImg(v.getImg());
                } else*/
                if (buildFabImgInfo != null && buildFabImgInfo.get(StringUtils.substring(skc, 0, skc.length() - 3)) != null &&
                        StringUtils.isNotBlank(buildFabImgInfo.get(StringUtils.substring(skc, 0, skc.length() - 3)).getSkcImgMap().get(v.getColor_code()))) {
                    imgData.setImg(buildFabImgInfo.get(StringUtils.substring(skc, 0, skc.length() - 3)).getSkcImgMap().get(v.getColor_code()));
                } else {
                    imgData.setImg(v.getImg());
                }

                imgData.setSkcCode(v.getSkc_code());
                imgData.setPrice(Objects.toString(resps.get(0).getPrice()));
                imgData.setName(resps.get(0).getName());
                imgData.setSmallClassName(resps.get(0).getSmall_class());
                imgData.setColorCode(v.getColor_code());
                imgData.setColorName(v.getColor_name());
                list.add(imgData);
            });
        }

        List<StyleData> styleDatas = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(styleInfos)) {
            styleInfos.stream().filter(v -> Objects.equals(v.getSkc_code(), skc)).forEach(v -> {
                StyleData styleData = new StyleData();
                styleData.setLabelCode(v.getLabel_code());
                styleData.setStyle(v.getLabel_name());
                styleDatas.add(styleData);
            });
        }

        resp.setImgDataList(list);
        resp.setStyleList(styleDatas);
        return resp;
    }

    @Data
    public static class ImgData{
        private String skcCode;

        private String img;

        private String name;

        private String colorCode;

        private String colorName;

        private String price;

        private String smallClassName;

    }

}
