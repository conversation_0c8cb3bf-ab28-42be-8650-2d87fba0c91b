package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/7 17:43
 */
@Data
@ApiModel(value = "版型商品销量返回信息")
public class ProductVersionSalesResp {

    @ApiModelProperty(value = "总计销量")
    private String sumSalesNumber;

    @ApiModelProperty(value = "总计销售金额")
    private String sumSalesPrice;

    @ApiModelProperty(value = "返回数据")
    private List<SalesEntity> salesEntities;

    @Data
    public static class SalesEntity {
        @ApiModelProperty(value = "商品图片")
        private String pic;

        @ApiModelProperty(value = "品名")
        private String categoryName;

        @ApiModelProperty(value = "款号")
        private String spu;

        @ApiModelProperty(value = "价格")
        private String price;

        @ApiModelProperty(value = "销量")
        private String salesNumber;

        @ApiModelProperty(value = "销售金额")
        private String salesPrice;

        @ApiModelProperty(value = "商品id")
        private String productId;
    }



}
