package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date:2023/7/4 15:35
 */
@Data
@ApiModel(value = "查询fab图片相关信息")
public class ProductSpuFabImgInfoReq {

    @ApiModelProperty(value = "skc")
    @NotBlank(message = "skc不能为空")
    private String skc;

    @ApiModelProperty(value = "产品册类型 0直营 1经销")
    @NotNull(message = "产品册类型不能为空")
    private Integer type = 0;
}
