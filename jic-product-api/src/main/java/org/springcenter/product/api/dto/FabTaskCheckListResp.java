package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/3/15 10:28
 */
@Data
public class FabTaskCheckListResp {

    private Long id;

    @ApiModelProperty(value = "品牌id")
    private Long cArcBrandId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "大区id")
    private Long regionId;

    @ApiModelProperty(value = "大区名称")
    private String regionName;

    @ApiModelProperty(value = "城市id")
    private Long cityId;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "店铺id")
    private Long cStoreId;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "产品册id")
    private String volumeId;

    @ApiModelProperty(value = "产品册名称")
    private String volumeName;

    @ApiModelProperty(value = "导购")
    private String saleName;

    @ApiModelProperty(value = "导购id")
    private Long hrId;

    @ApiModelProperty(value = "反馈时间")
    private Date feedbackTime;

    @ApiModelProperty(value = "反馈时间字符串")
    private String feedbackTimeStr;

    @ApiModelProperty(value = "反馈人")
    private String feedbackPeople;

    @ApiModelProperty(value = "考核材料")
    private String feedbackJson;

    @ApiModelProperty(value = "反馈建议")
    private String feedbackSuggestion;

    @ApiModelProperty(value = "产品册波段")
    private String bandName;

}
