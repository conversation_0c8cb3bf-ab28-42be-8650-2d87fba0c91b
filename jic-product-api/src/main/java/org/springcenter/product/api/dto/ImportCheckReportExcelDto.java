package org.springcenter.product.api.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ImportCheckReportExcelDto {

    @ExcelProperty(value = "报告编码 必填")
    String reportCode ;
    @ExcelProperty(value = "面料号  必填")
    String fabirc ;
    @ExcelProperty(value = "横机非横机  必填")
    String type ;
    @ExcelProperty(value = "商品编码 多个用英文逗号分隔")
    String productCodes ;
    @ExcelProperty(value = "文件夹名称  必填")
    String dirName;
}
