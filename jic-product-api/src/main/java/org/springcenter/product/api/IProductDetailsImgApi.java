package org.springcenter.product.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.factory.ProductApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/5/30 15:05
 */
@Component
@FeignClient(contextId = "productRemoteApi", value = "jic-product-background-center", fallbackFactory = ProductApiFallbackFactory.class)
public interface IProductDetailsImgApi {

    @PostMapping("/product/detailsImg/findProductDetailImg")
    ResponseResult<List<FindProductDetailImgResp>> findProductDetailImg(@RequestBody CommonRequest<FindProductDetailImgReq> request);

    @PostMapping("/product/detailsImg/batchGetViewUrl")
    ResponseResult<List<LianxiangBatchGetViewResp>> batchGetViewUrl(@RequestBody CommonRequest<LianxiangBatchGetViewReq> request);


    @PostMapping("/product/detailsImg/findSampleClothNetDiskImgs")
    ResponseResult<List<SampleProductDetailImg>> findSampleClothNetDiskImgs(@RequestBody CommonRequest<FindSampleClothNetDiskImgReq> request);
}
