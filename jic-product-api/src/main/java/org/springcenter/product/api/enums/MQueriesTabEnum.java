package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date:2022/12/2 13:10
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MQueriesTabEnum {
    BRAND("PRODUCT_VERSION_LIB", "PRODUCT_VERSION_LIB_LIST", 1, "品牌"),
    CATEGORY("PRODUCT_VERSION_LIB", "PRODUCT_VERSION_LIB_LIST", 2, "类目"),
    ATTRIBUTE("PRODUCT_VERSION_LIB", "PRODUCT_VERSION_LIB_LIST", 3, "属性"),

    ATTRIBUTE_SUB("PRODUCT_VERSION_LIB", "PRODUCT_VERSION_LIB_LIST", 4, "版型库-列表-子属性"),

    LIST_FILTER("PRODUCT_VERSION_LIB", "PRODUCT_VERSION_LIB_LIST", 5, "版型库列表筛选"),

    SALES_LIST_FILTER("PRODUCT_VERSION_LIB", "PRODUCT_VERSION_SALES_LIST", 6, "版型库销售列表筛选"),
    ;

    private String node;

    private String subNode;

    private Integer code;

    private String desc;

}
