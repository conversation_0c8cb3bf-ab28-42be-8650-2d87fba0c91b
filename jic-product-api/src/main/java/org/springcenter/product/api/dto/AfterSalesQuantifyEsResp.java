package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date:2024/6/25 16:36
 */
@Data
public class AfterSalesQuantifyEsResp extends RemotingSerializable implements Serializable {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "款号")
    private String style_id;

    @ApiModelProperty(value = "颜色code")
    private String sty_color_id;

    @ApiModelProperty(value = "种类")
    private String sample_cat;

    @ApiModelProperty(value = "主面料")
    private String bomcode;

    @ApiModelProperty(value = "主要成分")
    private String bomname;

    @ApiModelProperty(value = "加工厂")
    private String supply_shutname;

    @ApiModelProperty(value = "样衣名称")
    private String sample_code;

    @ApiModelProperty(value = "面料推广")
    private String sc_popular;

    @ApiModelProperty(value = "入库时间")
    private String st_indate;

    @ApiModelProperty(value = "计划上市时间")
    private String date_list;

    @ApiModelProperty(value = "上级机构")
    private String updept;

    @ApiModelProperty(value = "细分类")
    private String design_type4;

    @ApiModelProperty(value = "成衣内检")
    private String cy_jcjg;

    @ApiModelProperty(value = "成衣外检")
    private String cy_wj_jcjg;

    @ApiModelProperty(value = "原料前期检测")
    private String yl_qq_jcjg;

    @ApiModelProperty(value = "原料大货内检")
    private String yl_dh_jcjg;

    @ApiModelProperty(value = "原料大货外检")
    private String rstsub_fault;

    @ApiModelProperty(value = "原料不合格确认意见")
    private String yl_sug;

    @ApiModelProperty(value = "面料查货")
    private String mlch;

    @ApiModelProperty(value = "成衣不合格确认意见")
    private String unquantify_sug;

    @ApiModelProperty(value = "成衣查货")
    private String cych;

    @ApiModelProperty(value = "成衣入库意见")
    private String cy_in_ku;

    @ApiModelProperty(value = "供应商")
    private String sc_spu_id;

    @ApiModelProperty(value = "风险报告")
    private String sc_riskassessment;

    @ApiModelProperty(value = "颜色名称")
    private String sty_color_name;
}
