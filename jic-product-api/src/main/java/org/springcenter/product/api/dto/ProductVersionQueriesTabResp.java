package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/2 11:03
 */
@Data
public class ProductVersionQueriesTabResp {

    @ApiModelProperty(value = "品牌信息")
    private List<BaseModel> brandList;

    @ApiModelProperty(value = "类目信息")
    private List<BaseModel> categoryList;

    @ApiModelProperty(value = "属性信息")
    private List<BaseModel> attributeList;

    @ApiModelProperty(value = "筛选信息")
    private List<BaseModel> filterList;

    @Data
    public static class BaseModel implements Serializable {
        private String id;

        private String code;

        private String name;

        private String pid;

        @ApiModelProperty(value = "子信息")
        private List<BaseModel> children;
    }
}
