package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date:2023/7/1 15:05
 */
@Data
@ApiModel(value = "添加商品fab穿着方式")
public class AddProductFabWearingReq {

    @ApiModelProperty(value = "商品id")
    @NotBlank(message = "商品id不能为空")
    private String productId;

    @ApiModelProperty(value = "商品款号")
    @NotBlank(message = "款号不能为空")
    private String name;

    @ApiModelProperty(value = "商品图片，多张用逗号分割传进来")
    private String urls;

    @ApiModelProperty(value = "操作人")
    @NotBlank(message = "操作人不能为空")
    private String operates;
}
