package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/6/15 13:28
 */
@Data
public class UpdateLabelSceneReq {

    @ApiModelProperty(value = "场景数据")
    @NotEmpty(message = "场景数据不能为空")
    private List<updateDataEntity> updateDataEntityList;

    @ApiModelProperty(value = "本级code")
    private String code;

    @Data
    public static class updateDataEntity {
        @ApiModelProperty(value = "标签")
        private String labelCode;

        @ApiModelProperty(value = "id")
        private String id;

        @ApiModelProperty(value = "场景id")
        @NotBlank(message = "场景id不能为空")
        private String sceneId;

        @ApiModelProperty(value = "排序")
        @NotBlank(message = "sort不能为空")
        private Integer sort;

        @ApiModelProperty(value = "是否删除 0删除 1正常")
        @NotBlank(message = "是否删除不能为空")
        private Integer isDeleted;
    }
}
