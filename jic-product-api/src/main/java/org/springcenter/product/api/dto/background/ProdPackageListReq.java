package org.springcenter.product.api.dto.background;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date:2024/7/28 22:00
 */
@Data
public class ProdPackageListReq {

    @ApiModelProperty(value = "商品包名称")
    private String packageName;

    @ApiModelProperty(value = "商品包ID")
    private String pacId;

    @ApiModelProperty(value = "是否启用 0停用 1启用")
    private Integer isOpen;

    @ApiModelProperty(value = "必传当前页面componet 备注！！！！ 当前字段为登录人部门")
    @NotBlank(message = "登录人部门不能为空")
    private String department;
}
