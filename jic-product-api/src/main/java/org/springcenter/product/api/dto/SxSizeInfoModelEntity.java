package org.springcenter.product.api.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date:2023/12/22 16:25
 */
@Data
@ApiModel(value = "速写尺码表格导出")
@EqualsAndHashCode
public class SxSizeInfoModelEntity {

    @ExcelProperty(value = "款号", index = 0)
    private String style_id;

    @ExcelProperty(value = "年份", index = 1)
    private String year;

    @ExcelProperty(value = "季节", index = 2)
    private String sample_id;

    @ExcelProperty(value = "品牌", index = 3)
    private String d_pp;

    @ExcelProperty(value = "部位", index = 4)
    private String part;

    @ExcelProperty(value = "测量说明", index = 5)
    private String clff;

    // 160/XS，165/S，170/M，175/L，180/XL，185/XXL

    // @ExcelProperty(value = "160/66A  XS")
    @ExcelProperty(value = "160/XS")
    private String model1;

    // @ExcelProperty(value = "160/80A  XS")
    @ExcelProperty(value = "165/S")
    private String model2;

    // @ExcelProperty(value = "165/70A  S")
    @ExcelProperty(value = "170/M")
    private String model3;

    // @ExcelProperty(value = "165/84A  S")
    @ExcelProperty(value = "175/L")
    private String model4;

    // @ExcelProperty(value = "170/74A  M")
    @ExcelProperty(value = "180/XL")
    private String model5;

    // @ExcelProperty(value = "170/88A  M")
    @ExcelProperty(value = "185/XXL")
    private String model6;

    /*@ExcelProperty(value = "175/78A  L")
    private String model7;

    @ExcelProperty(value = "175/92A  L")
    private String model8;

    @ExcelProperty(value = "180/82A  XL")
    private String model9;

    @ExcelProperty(value = "180/96A  XL")
    private String model10;

    @ExcelProperty(value = "185/100A  XXL")
    private String model11;

    @ExcelProperty(value = "185/86A  XXL")
    private String model12;*/
}
