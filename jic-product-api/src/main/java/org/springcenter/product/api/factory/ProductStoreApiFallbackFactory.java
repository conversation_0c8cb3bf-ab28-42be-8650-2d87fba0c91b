package org.springcenter.product.api.factory;


import feign.hystrix.FallbackFactory;
import org.springcenter.product.api.IProductStoreApi;
import org.springcenter.product.api.fallback.ProductStoreApiFallback;
import org.springframework.stereotype.Component;

@Component
public class ProductStoreApiFallbackFactory implements FallbackFactory<IProductStoreApi> {

    @Override
    public IProductStoreApi create(Throwable throwable) {
        ProductStoreApiFallback fallback = new ProductStoreApiFallback();
        fallback.setCause(throwable);
        return fallback;
    }
}
