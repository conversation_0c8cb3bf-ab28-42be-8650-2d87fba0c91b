package org.springcenter.product.api.factory;


import feign.hystrix.FallbackFactory;
import org.springcenter.product.api.IProductLabelApi;
import org.springcenter.product.api.fallback.ProductLabelApiFallback;
import org.springframework.stereotype.Component;

@Component
public class ProductLabelApiFallbackFactory implements FallbackFactory<IProductLabelApi> {
    @Override
    public IProductLabelApi create(Throwable throwable) {
        ProductLabelApiFallback fallback = new ProductLabelApiFallback();
        fallback.setCause(throwable);
        return fallback;
    }
}
