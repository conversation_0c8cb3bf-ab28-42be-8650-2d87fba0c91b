package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/1/17 10:09
 */
@Data
public class SampleProductAutoNameReq {

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "波段")
    private String bandStr;

    @ApiModelProperty(value = "样衣号")
    private List<String> sampleCodes;

    @ApiModelProperty(value = "品牌, 成人品牌和童装品牌不能同时刷")
    @NotEmpty(message = "品牌不能为空")
    private List<String> brands;

    @ApiModelProperty(value = "货季")
    private String goodSeason;
}
