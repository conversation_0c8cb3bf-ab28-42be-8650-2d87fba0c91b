package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/20 16:08
 */
@Data
public class GoodSpuDetailEntity extends RemotingSerializable {
    @ApiModelProperty(value = "商品ID")
    private long id;

    @ApiModelProperty(value = "商品款号")
    private String name;

    @ApiModelProperty(value = "商品名称")
    private String value;

    private String m_big_category;
    private String brand;
    private long m_big_category_id;
    private long m_small_category_id;
    private String sku_list;

    @ApiModelProperty(value = "商品价格")
    private double price;
    private String small_season;
    private String big_season;
    private long big_season_id;
    private String m_band;
    private long c_arcbrand_id;
    private long small_season_id;
    private long m_band_id;
    private String m_small_category;
    private String year;
    private String mark_style;
    private long m_brand_id;
    private List<Sku> skus;
    private long sales_num;

    @ApiModelProperty(value = "商品详情图")
    private String detail_imgs;

    @ApiModelProperty(value = "商品封面图")
    private String cover_imgs;
    private List<String> labels;
    private List<String> label_levels;

    @ApiModelProperty(value = "主题")
    private String topic;

    @ApiModelProperty(value = "标签")
    private List<String> product_tag_list;

    @ApiModelProperty(value = "销售话术")
    private String salesTalk;


    @ApiModelProperty(value = "商品细节图")
    private String banner_imgs;

    @ApiModelProperty(value = "微商城名称")
    private String mall_title;

    @ApiModelProperty(value = "重点面料")
    private String sc_outsidename;

    @ApiModelProperty(value = "应用功能")
    private String scfzyt;

    @ApiModelProperty(value = "重点图案")
    private String pattern_name;

    @ApiModelProperty(value = "特殊工艺")
    private String tymx;

    @ApiModelProperty(value = "面料推广资料")
    private String sc_popularize;

    @ApiModelProperty(value = "风格")
    private String style_name;

    @ApiModelProperty(value = "品牌描述")
    private String description_brand;

    @ApiModelProperty(value = "生命周期")
    private Long lifestyle_tag;


    @Data
    public static class Sku extends RemotingSerializable implements Serializable {
        private String id;
        private String no;
        private String gbcode;
        private String colorno;
        private String color_name;
        private String sizeno;
        private String size_name;
        private String imgurl;
        //扣完的图片
        private String matted_url;
        private int stock;
        private String stylepartsize_model;
    }

    public void buildSku(){
        if (this.sku_list == null || "".equals(this.sku_list)){
            return;
        }
        this.skus = Sku.decodeArr(this.sku_list, Sku.class);
    }

    @ApiModelProperty(value = "合体度")
    private String wei_du;

    @ApiModelProperty(value = "年季")
    private String good_season;

    @ApiModelProperty(value = "品类")
    private String ccchr4;

    @ApiModelProperty(value = "fab")
    private String fab;

    @ApiModelProperty(value = "样衣号")
    private String sampleCode;

    @ApiModelProperty(value = "0不是新品 1是新品")
    private Integer isNew = 0;

    @ApiModelProperty(value = "商品周期")
    private String productCircle;

    @ApiModelProperty(value = "上市时间")
    private Long marketTime;
}
