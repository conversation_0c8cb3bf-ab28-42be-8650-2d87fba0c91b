package org.springcenter.product.api.dto.wsc;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date:2025/2/24 11:20
 */
@Data
public class VidBdHeadReq {

    @ApiModelProperty(value = "门店id")
    @NotBlank(message = "门店id不能为空")
    private String vid;

    @ApiModelProperty(value = "品牌id")
    @NotBlank(message = "品牌id不能为空")
    private String weId;


    @ApiModelProperty(value = "商品品牌id")
    private Long proBrandId;
}
