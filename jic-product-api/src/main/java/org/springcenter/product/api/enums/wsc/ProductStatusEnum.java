package org.springcenter.product.api.enums.wsc;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date:2024/4/29 13:41
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ProductStatusEnum {

    // 0:不可售 1可售
    /**
     * 不上架、不售卖
     */
    NOT_ALLOW(0, "不上架、不售卖"),
    /**
     * 上架、可售卖
     */
    CAN_ALLOW(1, "上架、可售卖"),;

    private Integer code;

    private String desc;
}
