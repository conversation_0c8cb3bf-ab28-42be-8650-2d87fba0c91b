package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ProductSpuFabMatchImgEsResp extends RemotingSerializable implements Serializable {
    @ApiModelProperty(value = "spu + url")
    private String id;

    @ApiModelProperty(value = "图片链接")
    private String url;

    @ApiModelProperty(value = "品名")
    private String design_name;

    @ApiModelProperty(value = "款号")
    private String style_id;

    @ApiModelProperty(value = "商品id")
    private Long product_id;


    @ApiModelProperty(value = "skc")
    private String custumes;


}
