package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date:2023/8/15 15:07
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum JicBrandEnum {

    APN(15L, "APN73"),
    JNBY_BY_JNBY(4L, "小江南"),
    JNBY_PLUS(5L, "江南布衣+"),
    OUTLES(11L, "奥莱"),
    CROQUIS(2504948039L, "速写"),
    JNBY(2738574294L, "江南布衣"),
    LESS(2822095692L, "LESS"),
    POMME_DE_TERRE(6924108367L, "蓬马"),
    JNB<PERSON><PERSON>OM<PERSON>(8348044436L, "JNBYHOME");

    private Long id;

    private String desc;
}
