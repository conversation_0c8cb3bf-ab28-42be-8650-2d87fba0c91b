package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date:2023/7/5 15:20
 */
@Data
@ApiModel(value = "版型库配置列表返回")
public class PVQueryStatsListResp {
    @ApiModelProperty(value = "一级id")
    private Long attributeId;

    @ApiModelProperty(value = "一级属性")
    private String firstAttribute;

    @ApiModelProperty(value = "类目信息")
    private String category;

    @ApiModelProperty(value = "一级属性值")
    private String firstAttributeValue;

    @ApiModelProperty(value = "二级属性值")
    private String secondAttributeValue;

    @ApiModelProperty(value = "0是启用 1是禁用")
    private Integer isDeleted;
}
