package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springcenter.product.api.constant.ImageConstant;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2023/8/17 13:16
 */
@Data
@ApiModel(value = "预售商品详情返回")
public class PreSaleProductDetailResp {

    @ApiModelProperty(value = "合体度")
    private String fitness;

    @ApiModelProperty(value = "年季")
    private String goodSeason;

    @ApiModelProperty(value = "品类")
    private String category;

    @ApiModelProperty(value = "模特图")
    private List<String> modelPics;

    @ApiModelProperty(value = "商品款号")
    private String name;

    @ApiModelProperty(value = "商品名称")
    private String value;

    @ApiModelProperty(value = "价格")
    private String price;

    @ApiModelProperty(value = "封面图")
    private String coverImg;

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "sku信息")
    private List<Sku> skuList;

    @ApiModelProperty(value = "fab")
    private String fab;

    @ApiModelProperty(value = "样衣号")
    private String sampleCode;

    private List<String> labels;

    private List<String> label_levels;

    @Data
    public static class Sku extends RemotingSerializable implements Serializable {
        private String id;
        private String no;
        private String gbCode;
        private String colorNo;
        private String colorName;
        private String sizeNo;
        private String sizeName;
        private String imgUrl;
        //扣完的图片
        private String mattedUrl;
        private int stock;
        private String stylePartSizeModel;
    }

    public static PreSaleProductDetailResp build(GoodSpuDetailEntity entity, List<String> modelPics, String prefixSingleSkcUrl) {
        PreSaleProductDetailResp resp = new PreSaleProductDetailResp();
        resp.setCategory(entity.getCcchr4());
        resp.setName(entity.getName());
        resp.setProductId(Objects.toString(entity.getId()));
        resp.setFitness(entity.getWei_du());
        resp.setValue(entity.getValue());
        resp.setPrice(Objects.toString(entity.getPrice()));
        resp.setGoodSeason(entity.getGood_season());
        resp.setCoverImg(entity.getCover_imgs());
        resp.setFab(entity.getFab());
        resp.setSampleCode(entity.getSampleCode());
        resp.setLabels(entity.getLabels());
        resp.setLabel_levels(entity.getLabel_levels());
        if (CollectionUtils.isNotEmpty(modelPics)) {
            resp.setModelPics(modelPics);
        }
        if (CollectionUtils.isNotEmpty(entity.getSkus())) {
            List<Sku> skus = new ArrayList<>();
            entity.getSkus().forEach(v -> {
                Sku sku = new Sku();
                sku.setId(v.getId());
                sku.setNo(v.getNo());
                sku.setColorName(v.getColor_name());
                sku.setColorNo(v.getColorno());
                sku.setGbCode(v.getGbcode());
                sku.setImgUrl(prefixSingleSkcUrl + entity.getName() + v.getColorno() + ImageConstant.JPG);
                sku.setStock(v.getStock());
                sku.setMattedUrl(v.getMatted_url());
                sku.setSizeName(v.getSize_name());
                sku.setSizeNo(v.getSizeno());
                sku.setStylePartSizeModel(v.getStylepartsize_model());
                skus.add(sku);
            });
            resp.setSkuList(skus);
        }
        return resp;
    }

}
