package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/6/2 13:10
 */
@Data
public class ProductVidSkcResp extends BaseProductResp implements Serializable {

    @ApiModelProperty(value = "SKCID(款号+色号)")
    private String id;

    @ApiModelProperty(value = "SKCID(款号+色号)")
    private String primaryKey;

    @ApiModelProperty(value = "SKC图片地址")
    private String imgurl;

    @ApiModelProperty(value = "商品SPUID(废弃)")
    private long m_product_id;

    @ApiModelProperty(value = "商品SPUID")
    private long product_id;

    @ApiModelProperty(value = "门店ID")
    private String vid;

    @ApiModelProperty(value = "品牌id")
    private Long weId;

    @ApiModelProperty(value = "skc")
    private String skc_code;

    @ApiModelProperty(value = "skc_code1")
    private String skc_code1;

    @ApiModelProperty(value = "色号")
    private String colorno;

    @ApiModelProperty(value = "色名称")
    private String color_name;

    @ApiModelProperty(value = "面料")
    private String element;

    @ApiModelProperty(value = "主题")
    private String topic;

    @ApiModelProperty(value = "SKU集合")
    private List<ProductVidSkcResp.Sku> skus;

    @ApiModelProperty(value = "是否有库存，1有，0无")
    private long qty;

    //是否加购过
    @ApiModelProperty(value = "0 默认未加购  1 加购过")
    private int isAddTrolley;

    @ApiModelProperty(value = "映射的微盟商品Id")
    private long wei_mo_product_id;


    @ApiModelProperty(value = "是否展示 0不展示 1展示")
    private int is_display;


    @Data
    public static class Sku extends RemotingSerializable implements Serializable{
        @ApiModelProperty(value = "SKUID")
        private String id;

        @ApiModelProperty(value = "条码")
        private String no;

        @ApiModelProperty(value = "国际码")
        private String gbcode;

        @ApiModelProperty(value = "尺码号")
        private String sizeno;

        @ApiModelProperty(value = "尺码名称")
        private String size_name;

        @ApiModelProperty(value = "SKU库存数量")
        private int stock;

        @ApiModelProperty(value = "是否有库存1有")
        private String qty;

        private String stylepartsize_model;
    }

    public void buildSku(){
        if (this.getSku_list() == null || "".equals(this.getSku_list())){
            return;
        }
        this.skus = ProductVidSkcResp.Sku.decodeArr(this.getSku_list(), ProductVidSkcResp.Sku.class);
    }
}
