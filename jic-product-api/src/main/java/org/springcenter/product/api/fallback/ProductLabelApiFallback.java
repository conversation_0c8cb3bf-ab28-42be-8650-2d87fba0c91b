package org.springcenter.product.api.fallback;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import lombok.Setter;
import org.springcenter.product.api.IProductLabelApi;
import org.springcenter.product.api.dto.MarkingLabelReq;
import org.springcenter.product.api.dto.SysProductLabelResp;

import java.util.List;

public class ProductLabelApiFallback implements IProductLabelApi {
    @Setter
    private Throwable cause;

    @Override
    public ResponseResult markingLabel(CommonRequest<MarkingLabelReq> request) {
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<List<SysProductLabelResp>> getProductLabelBySkcCodes(List<String> skcCodes) {
        return ResponseResult.success();
    }
}
