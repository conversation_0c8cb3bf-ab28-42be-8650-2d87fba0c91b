package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryProductBizTags implements Serializable {

    @ApiModelProperty(value = "需要查询标签的款号")
    private List<String> productCodes;

    @ApiModelProperty(value = "业务类型")
    private String bizType;

    @ApiModelProperty(value = "业务下标签CODE")
    private String bizCode;

    private List<String> smallCategoryIds;

    @ApiModelProperty(value = "选中的季节id ")
    private List<Long> seasonIds;

    @ApiModelProperty(value = "选中的年份信息")
    private List<String> years;

    private List<Long> brandIds;
}
