package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("陈列册详情查询响应数据")
@Data
public class DisplayBookInfoQueryResp {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "类型 1=陈列册 2=陈列指引")
    private Integer type = 1;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "货季")
    private String seasonGoods;

    @ApiModelProperty(value = "品牌")
    private String brandName;

    @ApiModelProperty(value = "渠道")
    private String channel;

    @ApiModelProperty(value = "波段")
    private String bandName;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "陈列册名称")
    private String bookName;

    @ApiModelProperty(value = "陈列册详细信息JSON数据")
    private String clobJson;
    @ApiModelProperty(value = "人台图映射")
    private List<MannequinOrHangerInfo> mannequinList;
    @ApiModelProperty(value = "货杆图映射")
    private List<MannequinOrHangerInfo> hangerList;

    @Data
    public static class MannequinOrHangerInfo {
        @ApiModelProperty(value = "图片集合（多个图片）")
        private List<String> pictureList;
        @ApiModelProperty(value = "人台单品集合（不超过4个）")
        private List<SingleListData> singleList;
        @ApiModelProperty(value = "标题")
        private String outName;
    }

    @Data
    public static class SingleListData {
        @ApiModelProperty(value = "SKC集合(不超过8个)")
        private List<Skc> skcList;
        @ApiModelProperty(value = "标题")
        private String outName;
    }

    @Data
    public static class Skc {
        @ApiModelProperty(value = "款色号")
        private String skcCode;
        @ApiModelProperty(value = "SKC图片")
        private String skcImgUrl;
        @ApiModelProperty(value = "SKC小类名称")
        private String skcSmallClassName;
        @ApiModelProperty(value = "状态 0=正常 1=异常")
        private Integer status;
    }
}
