package org.springcenter.product.api.dto;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date:2025/3/12 16:43
 */
@Data
public class ProductSalesInfoResp {


    @ApiModelProperty(value = "成分面料")
    private List<IconDataInfo> fabricIconInfo;

    @ApiModelProperty(value = "面料特性")
    private List<IconDataInfo> materialIconInfo;

    @ApiModelProperty(value = "服用功能")
    private List<IconDataInfo> serveFunctionInfo;

    @ApiModelProperty(value = "洗涤说明")
    private List<WashInfo> washingInfo;

    @ApiModelProperty(value = "其它参数")
    private List<DataInfo> params;

    @ApiModelProperty(value = "面料推广资料（对内）")
    private String fabricPromotionMaterials;

    @ApiModelProperty(value = "辅料推广资料（对内）")
    private String excipientPromotionMaterials;

    private List<String> labelsList;

    private List<String> labelLevels;

    @Data
    public static class IconDataInfo {
        @ApiModelProperty(value = "图片icon")
        private String icon;

        @ApiModelProperty(value = "名称解释")
        private String name;
    }

    @Data
    public static class WashInfo extends RemotingSerializable implements Serializable {
        @ApiModelProperty(value = "洗涤说明")
        private String washName;

        @ApiModelProperty(value = "洗涤图片")
        private String washNo;
    }


    @Data
    public static class DataInfo {
        @ApiModelProperty(value = "key")
        private String key;

        @ApiModelProperty(value = "value")
        private String value;
    }
}
