package org.springcenter.product.api.dto.background;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date:2025/5/16 14:14
 */
@Data
public class ReminderAddInfoReq {

    @ApiModelProperty(value = "类型")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "类型名称")
    @NotEmpty(message = "类型名称不能为空")
    private String typeName;

    @ApiModelProperty(value = "模版code")
    @NotEmpty(message = "模版code不能为空")
    private String templateCode;

    @ApiModelProperty(value = "模版名称")
    private String templateTitle;

    @ApiModelProperty(value = "模版联系人")
    @NotEmpty(message = "模版联系人不能为空")
    private String templateConcat;

}
