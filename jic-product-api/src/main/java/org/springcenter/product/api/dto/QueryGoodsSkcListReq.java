package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryGoodsSkcListReq implements Serializable {
    //款号
    @ApiModelProperty(value = "款号 ")
    private String name;

    @ApiModelProperty(value = "款号 批量 ")
    private List<String> names;

    @ApiModelProperty(value = "选中的品牌信息 ")
    private List<Long> brandIds;

    @ApiModelProperty(value = "选中的货舱信息")
    private String storeId;

    @ApiModelProperty(value = "排序方式，0 不排序、1，最新，2，销量排序, 4:按照定量从高到低[原先有个3 不知道啥逻辑 暂时不动]")
    private int sorted = 0;

    @ApiModelProperty(value = "库存方式 0 有库存，1 全部商品")
    private int isStockNotEmpty = 0;

    /*@ApiModelProperty(value = "选中的波段信息 ")
    private List<Long> bandIds;*/

    @ApiModelProperty(value = "选中的波段信息 ")
    private List<Long> bandId;

//    //大类
//    private List<CategorysReq> categorysReqs;

    private List<String> smallCategoryIds;

    @ApiModelProperty(value = "选中的季节id ")
    private List<Long> seasonIds;

    @ApiModelProperty(value = "选中的年份信息")
    private List<String> years;

//    @ApiModelProperty(value = "如选中了其他年份  此参数传递 1  ")
//    private Integer isOtherYears = 0;

    @ApiModelProperty(value = "选中的尺码")
    private List<String> sizeNos;

    private Integer gPrice;
    private Integer lPrice;


//    private List<String> mustLabels;
//    private List<String> mustNotLabels;

    private Integer productId;

    @ApiModelProperty(value = "是否加购过   0  默认不加购   1 加购过")
    private Integer isAddTrolley = 0;

    @ApiModelProperty(value = "查询指定的款色此参数前台无需传递  多个款号 废弃")
    private List<String> productCodes;

    @ApiModelProperty(value = "排除不查询的款,最多100个")
    private List<Long> excludeProductIds;

    @ApiModelProperty(value = "查询指定的款,最多100个")
    private List<Long> includeProductIds;

    @ApiModelProperty(value = "此参数前台无需传递  多个颜色")
    private List<String> productColorNos;

    @ApiModelProperty(value = "此参数是非货仓 需有货  1 代表查询非货舱有货 未找到映射的微盟商品id")
    private Integer weiMenProductId = 0;

    @ApiModelProperty(value = "品牌小程序ID")
    private Long weid;

    @ApiModelProperty(value = "排除不查询的spu")
    private List<String> excludeNames;

    @ApiModelProperty("搜索标签code值")
    private List<String> labelCodes;

}
