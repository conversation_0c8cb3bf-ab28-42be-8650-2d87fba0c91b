package org.springcenter.product.api.fallback;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.api.IProductToppingTagApi;
import org.springcenter.product.api.dto.QueryToppingTagListReq;
import org.springcenter.product.api.dto.QueryToppingTagListResp;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/26 10:13
 */
@Slf4j
public class IProductToppingTagApiFallback implements IProductToppingTagApi {

    @Setter
    private Throwable cause;

    @Override
    public ResponseResult<List<QueryToppingTagListResp>> queryToppingTagList(CommonRequest<QueryToppingTagListReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }
}
