package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/10 16:19
 */
@Data
public class ProductVersionSalesQueriesTabResp {

    @ApiModelProperty(value = "版型筛选")
    private List<String> modelNumberList;

    @ApiModelProperty(value = "条件筛选")
    private List<Base> filterList;

    @Data
    public static class Base {
        private String code;

        private String name;
    }
}
