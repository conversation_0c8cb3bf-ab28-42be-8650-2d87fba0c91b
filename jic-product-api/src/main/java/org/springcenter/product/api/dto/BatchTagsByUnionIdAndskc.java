package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchTagsByUnionIdAndskc implements Serializable {


    @ApiModelProperty(value = "用户unionId")
    private String unionId;

    @ApiModelProperty(value = "款号和色号拼接   举例 :  111222,733 ")
    private List<String> productCodesAndColors;

}
