package org.springcenter.product.api.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date:2023/12/22 16:25
 */
@Data
@ApiModel(value = "JNBY尺码表格导出")
@EqualsAndHashCode
public class JnbySizeInfoModelEntity {

    @ExcelProperty(value = "款号", index = 0)
    private String style_id;

    @ExcelProperty(value = "年份", index = 1)
    private String year;

    @ExcelProperty(value = "季节", index = 2)
    private String sample_id;

    @ExcelProperty(value = "品牌", index = 3)
    private String d_pp;

    @ExcelProperty(value = "部位", index = 4)
    private String part;

    @ExcelProperty(value = "测量说明", index = 5)
    private String clff;

    // 150/XS，155/S，160/M，165/L，170/XL
    // @ExcelProperty(value = "150/62A XS")
    @ExcelProperty(value = "150/XS")
    private String model1;

    //@ExcelProperty(value = "150/76A XS")
    @ExcelProperty(value = "155/S")
    private String model2;

    //@ExcelProperty(value = "155/66A  S")
    @ExcelProperty(value = "160/M")
    private String model3;

    //@ExcelProperty(value = "155/80A S")
    @ExcelProperty(value = "165/L")
    private String model4;

    //@ExcelProperty(value = "160/68A  M")
    @ExcelProperty(value = "170/XL")
    private String model5;

    /*@ExcelProperty(value = "160/84A  M")
    private String model6;

    @ExcelProperty(value = "165/72A  L")
    private String model7;

    @ExcelProperty(value = "165/88A  L")
    private String model8;

    @ExcelProperty(value = "170/74A  XL")
    private String model9;

    @ExcelProperty(value = "170/92A  XL")
    private String model10;*/
}
