package org.springcenter.product.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import org.springcenter.product.api.dto.QueryToppingTagListReq;
import org.springcenter.product.api.dto.QueryToppingTagListResp;
import org.springcenter.product.api.factory.IProductToppingTagApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/25 18:02
 */
@Component
@FeignClient(contextId = "productToppingTagApi", value = "jic-product-api-center", fallbackFactory = IProductToppingTagApiFallbackFactory.class)
public interface IProductToppingTagApi {

    @PostMapping("/product/topping/tag/api/queryToppingTagList")
    public ResponseResult<List<QueryToppingTagListResp>> queryToppingTagList(@RequestBody CommonRequest<QueryToppingTagListReq> request);
}
