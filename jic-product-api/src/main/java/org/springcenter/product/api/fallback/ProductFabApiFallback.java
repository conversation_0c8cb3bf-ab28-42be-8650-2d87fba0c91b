package org.springcenter.product.api.fallback;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import lombok.Setter;
import org.springcenter.product.api.IProductFabApi;
import org.springcenter.product.api.dto.ProductFabInfoReq;
import org.springcenter.product.api.dto.ProductSizeInfoResp;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/3/19 11:56
 */
public class ProductFabApiFallback implements IProductFabApi {

    @Setter
    private Throwable cause;

    @Override
    public ResponseResult<List<ProductSizeInfoResp>> queryFabSizeInfo(CommonRequest<ProductFabInfoReq> request){
        return ResponseResult.success(new ArrayList<>());
    }
}
