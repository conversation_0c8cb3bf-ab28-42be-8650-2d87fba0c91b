package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/7/1 16:13
 */
@Data
@ApiModel(value = "重点面料返回信息")
public class FabProductPatternInfoRep {

    @ApiModelProperty(value = "图案名称")
    private String patternName;

    @ApiModelProperty(value = "图案大类")
    private String patternType;

    @ApiModelProperty(value = "图案url")
    private String url;

    @ApiModelProperty(value = "灵感说明")
    private String inspiration;

    @ApiModelProperty(value = "品牌id不能为空")
    @NotEmpty(message = "品牌信息不能为空")
    private List<Long> brandIds;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "波段")
    private List<Long> bandIds;


    public static List<FabProductPatternInfoRep> build(List<ProductSpuFabEsResp> resps, String oldPrefix, String newPrefix, String imOldPrefix) {
        List<FabProductPatternInfoRep> rets = new ArrayList<>();
        Set<FabProductPatternInfoRep> set = new HashSet<>();
        resps.forEach(v -> {
            if (CollectionUtils.isEmpty(v.getPattern_info_list())) {
                return;
            }
            v.getPattern_info_list().stream().filter(k -> StringUtils.isNotBlank(k.getPattern_name())
            && StringUtils.isNotBlank(k.getInspiration()) && StringUtils.isNotBlank(k.getPattern_url())
            && StringUtils.isNotBlank(k.getInspiration())).forEach(x -> {
                FabProductPatternInfoRep rep = new FabProductPatternInfoRep();
                rep.setPatternName(x.getPattern_name());
                rep.setPatternType(x.getPattern_type());
                if (x.getPattern_url().contains("/im/")) {
                    rep.setUrl(x.getPattern_url().replace(imOldPrefix, newPrefix));
                } else {
                    rep.setUrl(x.getPattern_url().replace(oldPrefix, newPrefix));
                }
                rep.setInspiration(x.getInspiration());
                rets.add(rep);
                set.add(rep);
            });
        });

        if (rets.size() == set.size()) {
            return rets;
        } else {
            return set.stream().collect(Collectors.toList());
        }

    }
}
