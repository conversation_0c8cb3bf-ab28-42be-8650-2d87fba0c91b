package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2022/12/11 13:53
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum MainAttrSpecialEnum {

    SKIRT_TYPE_1("297", "连衣裙", "Q10-A01", "A型"),
    SKIRT_TYPE_2("297", "连衣裙", "Q10-A02", "H型"),
    SKIRT_TYPE_3("297", "连衣裙", "Q10-A03", "其他(T/Y/V)"),
    SKIRT_TYPE_4("297", "连衣裙", "Q10-A04", "异型"),
    SKIRT_TYPE_5("297", "连衣裙", "Q10-A05", "O型"),
    SKIRT_TYPE_6("297", "连衣裙", "Q10-A06", "X型"),
    SKIRT_TYPE_7("297", "连衣裙", "Q10-A07", "T型"),
    SKIRT_TYPE_8("297", "连衣裙", "Q10-A08", "S型"),
    SKIRT_TYPE_9("297", "连衣裙", "Q10-A09", "吊带连衣裙"),
    SKIRT_TYPE_10("297", "连衣裙", "Q10-A10", "抹胸连衣裙"),
    SKIRT_TYPE_11("298", "腰裙", "Q10-A11", "直筒裙"),
    SKIRT_TYPE_12("298", "腰裙", "Q10-A12", "A型裙"),
    SKIRT_TYPE_13("298", "腰裙", "Q10-A13", "圆裙"),
    SKIRT_TYPE_14("298", "腰裙", "Q10-A14", "百褶裙"),
    SKIRT_TYPE_15("298", "腰裙", "Q10-A15", "包裹裙"),
    SKIRT_TYPE_16("298", "腰裙", "Q10-A16", "不规则裙"),;

    private String categoryCode;
    
    private String cateGoryName;
    
    private String attrValueCode;
    
    private String attrValueName;
    
    public static List<String> getByCategoryCode(String categoryCode) {
        return Arrays.stream(MainAttrSpecialEnum.values())
                .filter(v -> Objects.equals(categoryCode, v.getCategoryCode()))
                .distinct()
                .map(MainAttrSpecialEnum::getAttrValueCode)
                .collect(Collectors.toList());
    }

}
