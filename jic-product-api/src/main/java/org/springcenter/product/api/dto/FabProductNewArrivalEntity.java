package org.springcenter.product.api.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date:2023/7/1 16:56
 */
@Data
@ApiModel(value = "上新表格导出")
@EqualsAndHashCode
public class FabProductNewArrivalEntity {
    @ExcelProperty("款式品牌")
    private String pp;

    @ExcelProperty("款式编号")
    private String style_id;

    @ExcelProperty("设计编号")
    private String design_id_new;

    @ExcelProperty("颜色编号")
    private String sty_color_id;

    @ExcelProperty("颜色名称")
    private String m_color_name;

    @ExcelProperty("吊牌价")
    private String sell_price;

    @ExcelProperty("配件货号")
    private String np_spu;

    @ExcelProperty("年份大季节")
    private String good_season;

    @ExcelProperty("款式季节")
    private String corressample_id;

    @ExcelProperty("款式波段")
    private String season;

    @ExcelProperty("款式小类")
    private String ccchr5;

    @ExcelProperty("品名")
    private String design_name;

    @ExcelProperty("电商组合品名")
    private String dsPartAutoName;

    @ExcelProperty("数智组合品名")
    private String wscAutoName;

    @ExcelProperty("组合品名参考")
    private String dsWholeAutoName;

    @ExcelProperty("款式主题")
    private String topic;

    @ExcelProperty("款式性别")
    private String sex;

    @ExcelProperty("通知数量(汇总定量)")
    private String add_amt;

    @ExcelProperty("计划入库时间")
    private String in_date;

    @ExcelProperty("上货日期")
    private String sale_date;

    @ExcelProperty("现体型")
    private String tixing;

    @ExcelProperty("现合体度")
    private String weidu;

    @ExcelProperty("袖型")
    private String xiu;

    @ExcelProperty("袖长")
    private String xiuxing;

    @ExcelProperty("领型")
    private String lingxing;

    @ExcelProperty("长度")
    private String kuanxing;

    @ExcelProperty("搭配建议")
    private String match_advice;

    @ExcelProperty("搭配关系 (款号)")
    private String skc_list;

    @ExcelProperty("重点套装 (款号)")
    private String key_combinations;

    @ExcelProperty("内配")
    private String np_name;

    @ExcelProperty("尺码段")
    private String size_name;

    @ExcelProperty("羽绒厚薄度")
    private String spe_thick;

    @ExcelProperty("羽绒填充物")
    private String filler;

    @ExcelProperty("羽绒服克重")
    private String fill_content;

    @ExcelProperty("标准")
    private String exec_standard;

    @ExcelProperty("产地")
    private String origin;

    @ExcelProperty("吊牌成分")
    private String ingredient;

    @ExcelProperty("主成分（吊牌）")
    private String sjcf;

    @ExcelProperty("面料是否不同")
    private String isSkcDiff;

    @ExcelProperty("面料名称")
    private String sc_out_side_name;

    @ExcelProperty("亲子款")
    private String qzk;

    @ExcelProperty("自动风格")
    private String style_name;

    @ExcelProperty("fab描述")
    private String fab;

    // 新增环保标签、服用功能、面料推广资料、辅料推广资料、是否有里、口袋样式、开衩方式、衣门襟、厚薄、外观、成衣弹力
    @ExcelProperty("环保标签")
    private String sc_origin_area;

    @ExcelProperty("服用功能")
    private String sc_fzyt;

    @ExcelProperty("面料特性")
    private String fabric_charac_list;

    @ExcelProperty("面料推广资料")
    private String sc_popularize;

    @ExcelProperty("辅料推广资料")
    private String acc_point;

    @ExcelProperty("图案名称")
    private String pattern_name;

    @ExcelProperty("图案是否随机")
    private String pattern_random;

    @ExcelProperty("克重")
    private String fabricWeight;

    @ExcelProperty("是否有里")
    private String you_li;

    @ExcelProperty("是否加绒")
    private String frg_name;

    @ExcelProperty("口袋样式")
    private String kou_dai;

    @ExcelProperty("开衩方式")
    private String kai_cha;

    @ExcelProperty("衣门襟")
    private String yi_men_jin;

    @ExcelProperty("厚薄")
    private String thick;

    @ExcelProperty("外观")
    private String facade;

    @ExcelProperty("成衣弹力")
    private String cloth_elastic;

    @ExcelProperty("维护方法1")
    private String washName1;

    @ExcelProperty("维护方法2")
    private String washName2;

    @ExcelProperty("维护方法3")
    private String washName3;

    @ExcelProperty("维护方法4")
    private String washName4;

    @ExcelProperty("维护方法5")
    private String washName5;

    @ExcelProperty("维护方法6")
    private String washName6;

    @ExcelProperty("吊牌说明")
    private String tag_info;

    @ExcelProperty("OEKOTEX认证")
    private String oekotex_tag;

}
