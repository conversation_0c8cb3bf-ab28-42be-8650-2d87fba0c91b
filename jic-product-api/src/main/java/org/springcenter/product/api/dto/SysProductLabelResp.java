package org.springcenter.product.api.dto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
public class SysProductLabelResp implements java.io.Serializable{

    private String id;


    private String productId;


    private String skcCode;


    private String labelId;


    private String labelLevel;


    private Long isDel;


    private String createBy;

    private Date createTime;

    private String updateBy;

    private Date updateTime;

    @ApiModelProperty(value = "1代表来自数栈 3来自手工")
    private Integer labelFrom;

    @ApiModelProperty(value = "1代表skc 2代表spu")
    private Integer labelType;
}
