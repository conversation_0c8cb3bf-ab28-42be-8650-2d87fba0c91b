package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2022/12/5 13:17
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum DegreeFitCodeEnum {
    CULTIVATE("1.5", "修身"),
    PARTIAL_COMBINED("2", "偏合体"),
    COMBINED("2.5", "合体"),
    ON_THE_LOOSE("3", "偏宽松"),
    LOOSE("3.5", "宽松"),
    OVERSIZE("4", "OVERSIZE"),
    SUPER_OVERSIZE("4.5", "超OVERSIZE");


    private String code;

    private String desc;

    public static DegreeFitCodeEnum getByCode(String code) {
        return Arrays.stream(DegreeFitCodeEnum.values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(null);
    }
}
