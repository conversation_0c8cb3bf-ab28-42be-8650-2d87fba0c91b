package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class FindProductDetailImgResp {

    @ApiModelProperty(value = "款号")
    private String productCode;

    @ApiModelProperty(value = "商品主图")
    private List<ImgData> mainImgs;


    @ApiModelProperty(value = "细节图")
    private List<ImgData> detailsImgs;


    @ApiModelProperty(value = "搭配图")
    private List<ImgCollocationData> collocationImgs;


    @ApiModelProperty(value = "商品一览图 pngs")
    private List<ImgData> pngs;

    @Data
    public static class ImgCollocationData{

        @ApiModelProperty(value = "搭配号")
        String collocationCode;


        @ApiModelProperty(value = "搭配数据")
        List<ImgData> imgDatas;

    }

    @Data
    public static class ImgData{

        @ApiModelProperty(value = "neid")
        private String neid;

        @ApiModelProperty(value = "nsid")
        private String nsid;

        @ApiModelProperty(value = "主键id")
        private String id;

        @ApiModelProperty(value = "款号")
        private String productCode;

        @ApiModelProperty(value = "色号")
        private String colorNo;

        @ApiModelProperty(value = "仅为搭配图的时候有此值")
        private String collocationCode;

        @ApiModelProperty(value = "0 非海外  1 海外    a1的图为海外  就是  1  ")
        private Integer overseaFlag;


    }


}
