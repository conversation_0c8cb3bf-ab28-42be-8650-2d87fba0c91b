package org.springcenter.product.api.factory;

import feign.hystrix.FallbackFactory;
import org.springcenter.product.api.IProductDetailsImgApi;
import org.springcenter.product.api.IProductPackageApi;
import org.springcenter.product.api.fallback.ProductDetailsImgApiFallback;
import org.springcenter.product.api.fallback.ProductPackageApiFallback;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date:2024/5/30 15:07
 */
@Component
public class ProductPackageApiFallbackFactory implements FallbackFactory<IProductPackageApi> {
    @Override
    public IProductPackageApi create(Throwable cause) {
        ProductPackageApiFallback fallback = new ProductPackageApiFallback();
        fallback.setCause(cause);
        return fallback;
    }
}
