package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date:2023/5/19 15:59
 */
@Data
public class DisassociateSceneLabelsReq {

    @ApiModelProperty(value = "id")
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "0正常 1删除")
    @NotNull(message = "isDeleted不能为空")
    private Integer isDeleted;


    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    private Integer sort;
}
