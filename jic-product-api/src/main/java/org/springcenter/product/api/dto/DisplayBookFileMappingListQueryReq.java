package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@ApiModel(value = "陈列册映射列表查询入参模型")
@Data
public class DisplayBookFileMappingListQueryReq {
    @ApiModelProperty(value = "年，可多选")
    private List<String> years;
    @ApiModelProperty(value = "品牌id，可多选")
    private List<Long> brandIds;
    @ApiModelProperty(value = "品牌名称，可多选")
    private List<String> brandNames;
}
