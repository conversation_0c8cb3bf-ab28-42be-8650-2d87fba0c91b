package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date:2023/5/19 15:33
 */
@Data
public class QueryListSceneLabelResp {

    @ApiModelProperty(value = "场景标签关系id")
    private String id;

    @ApiModelProperty(value = "标签code")
    private String labelCode;

    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "0不是新 1是新")
    private Integer isNew;

    @ApiModelProperty(value = "二级标签")
    private List<QueryListSceneLabelResp> subSceneLabels;


}
