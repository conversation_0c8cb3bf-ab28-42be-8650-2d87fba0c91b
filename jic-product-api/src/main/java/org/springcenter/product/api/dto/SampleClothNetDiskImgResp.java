package org.springcenter.product.api.dto;

import lombok.Data;

import java.util.Date;

@Data
public class SampleClothNetDiskImgResp {

    /**
     * 主键
     */
    private String id;

    /**
     * 样衣号
     */
    private String sampleClothCode;

    /**
     * 文件名称  均是数字 可以按照这个进行排序
     */
    private String fileName;

    /**
     * 0 正常  1 删除
     */
    private Integer isDel;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 七牛云路径
     */
    private String qiniuImgUrl;

    /**
     * NEID
     */
    private String neid;

    /**
     * NSID
     */
    private String nsid;
}
