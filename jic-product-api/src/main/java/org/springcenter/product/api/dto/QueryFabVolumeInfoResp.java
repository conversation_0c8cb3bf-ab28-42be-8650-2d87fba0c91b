package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2023/11/14 13:24
 */
@Data
public class QueryFabVolumeInfoResp {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "货季")
    private String seasonGoods;

    @ApiModelProperty(value = "品牌")
    private String brandName;

    @ApiModelProperty(value = "波段")
    private String bandName;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "展示时间")
    private String displayTime;

    @ApiModelProperty(value = "保存时间")
    private String updateTime;

    @ApiModelProperty(value = "0:未设置时间 1:待展示 2:已展示")
    private Integer status;

    @ApiModelProperty(value = "0:直营 1:经销")
    private Integer type;
}
