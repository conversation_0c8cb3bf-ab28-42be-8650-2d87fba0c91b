package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2022/12/5 15:32
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ProductVersionExcelEnum {

    // 二级属性
    CHEST(7, "胸围", "Chest"),
    WAIST(8, "腰围", "Waist"),
    PLACE_AROUND(9, "摆围", "PlaceAround"),
    HIT(10, "臀围", "Hit"),
    NECK_LINE(13, "领围", "NeckLine"),
    NECK_HORIZONTAL(14, "横开领", "NeckHorizontal"),
    NECK_FRONT(15, "前深领", "NeckFront"),
    SHOULDER_BREATH(17, "肩宽", "ShoulderBreath"),
    CLAMP_RING(19, "夹圈", "ClamRing"),
    SUIT_BICEP(20, "袖肥", "SuitBicep"),
    SUIT_BACK_MIDDLE(22, "后中袖长", "SuitBackMiddle"),
    LENGTH_SHOULDER_BREATH(24, "肩点量衣长", "LengthShoulderBreath"),
    PANTS_SIZE(26, "裤长尺寸", "PantsSize"),
    PANTS_OF_FOOT(28, "脚口围", "PantsOfFoot"),
    LENGTH_OF_SKIRT(30, "裙长尺寸", "LengthOfSkirt"),
    SKIRT_PLACE_AROUND(32, "摆围", "SkirtPlaceAAround"),
    WAIST_TYPE(34, "下装腰围", "WaistType"),

    //主属性
    DEGREE(6, "合体度", "Q1"),
    BODY_SIZE(11, "体型", "Q2"),
    COLLAR(12, "领型", "Q3"),
    SHOULDER(16, "肩型", "Q4"),
    SLEEVE(18, "袖型", "Q5"),
    SLEEVE_LENGTH(21, "袖长", "Q6"),
    CLOTH_LENGTH(23, "衣长", "Q7"),
    PANTS_TYPE(25, "裤长", "Q9"),
    PANTS_LENGTH(27, "裤型", "Q8"),
    SKIRTS_LENGTH(29, "裙长", "Q13"),
    SKIRTS_TYPE(31, "裙型", "Q10"),
    WAIST_SHAPE1(33, "腰类型1", "Q11"),
    WAIST_SHAPE2(35, "腰类型2", "Q12"),
    ;


    private Integer code;

    private String desc;

    private String attrCode;

    public static ProductVersionExcelEnum getByName(String name) {
        return Arrays.stream(ProductVersionExcelEnum.values())
                .filter(v -> Objects.equals(v.getDesc(), name))
                .findFirst().orElse(null);
    }
}
