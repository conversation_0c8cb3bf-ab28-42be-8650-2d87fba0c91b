package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2023/8/17 13:04
 */
@Data
@ApiModel(value = "预售商品详情")
public class PreSaleProdCoverPicsResp {

    @ApiModelProperty(value = "商品款号")
    private String name;

    @ApiModelProperty(value = "商品图片")
    private String pics;

    @ApiModelProperty(value = "样衣号")
    private String sampleCode;

    @ApiModelProperty(value = "look图片")
    private String lookPic;
}
