package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2022/12/12 14:43
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum MainAttrEnum {
    ATTR1("Q1", "合体度", "Q1-A01", "修身"),
    ATTR2("Q1", "合体度", "Q1-A02", "偏合体"),
    ATTR3("Q1", "合体度", "Q1-A03", "合体"),
    ATTR4("Q1", "合体度", "Q1-A04", "偏宽松"),
    ATTR5("Q1", "合体度", "Q1-A05", "宽松"),
    ATTR6("Q1", "合体度", "Q1-A06", "OVERSIZE"),
    ATTR7("Q1", "合体度", "Q1-A07", "超OVERSIZE"),
    ATTR8("Q2", "体型", "Q2-A01", "A型"),
    ATTR9("Q2", "体型", "Q2-A02", "H型"),
    ATTR10("Q2", "体型", "Q2-A03", "其他(T/Y/V)"),
    ATTR11("Q2", "体型", "Q2-A04", "异型"),
    ATTR12("Q2", "体型", "Q2-A05", "O型"),
    ATTR13("Q2", "体型", "Q2-A06", "X型"),
    ATTR14("Q2", "体型", "Q2-A07", "T型"),
    ATTR15("Q2", "体型", "Q2-A08", "S型"),
    ATTR16("Q3", "领型", "Q3-A01", "V领"),
    ATTR17("Q3", "领型", "Q3-A02", "一字领"),
    ATTR18("Q3", "领型", "Q3-A03", "圆领"),
    ATTR19("Q3", "领型", "Q3-A04", "polo领"),
    ATTR20("Q3", "领型", "Q3-A05", "翻领"),
    ATTR21("Q3", "领型", "Q3-A06", "半高领"),
    ATTR22("Q3", "领型", "Q3-A07", "高领"),
    ATTR23("Q3", "领型", "Q3-A08", "戴帽领"),
    ATTR24("Q3", "领型", "Q3-A09", "立领"),
    ATTR25("Q3", "领型", "Q3-A10", "衬衫领"),
    ATTR26("Q3", "领型", "Q3-A11", "西装领"),
    ATTR27("Q3", "领型", "Q3-A12", "无领(外套)"),
    ATTR28("Q3", "领型", "Q3-A13", "风衣领"),
    ATTR29("Q3", "领型", "Q3-A14", "棒球领"),
    ATTR30("Q3", "领型", "Q3-A15", "青果领"),
    ATTR31("Q3", "领型", "Q3-A16", "不规则领"),
    ATTR32("Q4", "肩型", "Q4-A01", "正常肩"),
    ATTR33("Q4", "肩型", "Q4-A02", "窄肩"),
    ATTR34("Q4", "肩型", "Q4-A03", "宽肩"),
    ATTR35("Q4", "肩型", "Q4-A04", "落肩"),
    ATTR36("Q4", "肩型", "Q4-A05", "吊带"),
    ATTR37("Q4", "肩型", "Q4-A06", "超落肩"),
    ATTR38("Q5", "袖型", "Q5-A01", "插肩袖"),
    ATTR39("Q5", "袖型", "Q5-A02", "连身袖"),
    ATTR40("Q5", "袖型", "Q5-A03", "无袖"),
    ATTR41("Q5", "袖型", "Q5-A04", "吊带"),
    ATTR42("Q5", "袖型", "Q5-A05", "正常袖"),
    ATTR43("Q6", "袖长", "Q6-A01", "无袖"),
    ATTR44("Q6", "袖长", "Q6-A02", "短袖"),
    ATTR45("Q6", "袖长", "Q6-A03", "中袖"),
    ATTR46("Q6", "袖长", "Q6-A04", "七分袖"),
    ATTR47("Q6", "袖长", "Q6-A05", "长袖"),
    ATTR48("Q6", "袖长", "Q6-A06", "超长袖"),
    ATTR49("Q6", "袖长", "Q6-A07", "九分袖"),
    ATTR50("Q7", "衣长", "Q7-A01", "露脐"),
    ATTR51("Q7", "衣长", "Q7-A02", "高腰"),
    ATTR52("Q7", "衣长", "Q7-A03", "低腰"),
    ATTR53("Q7", "衣长", "Q7-A04", "及腰"),
    ATTR54("Q7", "衣长", "Q7-A05", "上臀围"),
    ATTR55("Q7", "衣长", "Q7-A06", "上臀围下5cm"),
    ATTR56("Q7", "衣长", "Q7-A07", "臀围"),
    ATTR57("Q7", "衣长", "Q7-A08", "虎口处"),
    ATTR58("Q7", "衣长", "Q7-A09", "露指一手长"),
    ATTR59("Q7", "衣长", "Q7-A10", "指尖"),
    ATTR60("Q7", "衣长", "Q7-A11", "及膝"),
    ATTR61("Q7", "衣长", "Q7-A12", "膝上10cm"),
    ATTR62("Q7", "衣长", "Q7-A13", "膝上6cm"),
    ATTR63("Q7", "衣长", "Q7-A14", "小腿肚"),
    ATTR64("Q7", "衣长", "Q7-A15", "小腿肚下13cm"),
    ATTR65("Q7", "衣长", "Q7-A16", "小腿肚下8cm"),
    ATTR66("Q7", "衣长", "Q7-A17", "脚踝上10cm"),
    ATTR67("Q7", "衣长", "Q7-A18", "脚踝上5cm"),
    ATTR68("Q7", "衣长", "Q7-A19", "脚踝"),
    ATTR69("Q7", "衣长", "Q7-A20", "脚踝下"),
    ATTR70("Q7", "衣长", "Q7-A21", "脚面"),
    ATTR71("Q8", "裤型", "Q8-A01", "锥形裤"),
    ATTR72("Q8", "裤型", "Q8-A02", "直筒裤"),
    ATTR73("Q8", "裤型", "Q8-A03", "喇叭裤"),
    ATTR74("Q8", "裤型", "Q8-A04", "落裆裤"),
    ATTR75("Q8", "裤型", "Q8-A05", "阔腿裤"),
    ATTR76("Q8", "裤型", "Q8-A06", "束脚裤"),
    ATTR77("Q8", "裤型", "Q8-A07", "紧身裤"),
    ATTR78("Q8", "裤型", "Q8-A08", "裙裤"),
    ATTR79("Q8", "裤型", "Q8-A09", "弯刀裤"),
    ATTR80("Q9", "裤长", "Q9-A01", "超短裤"),
    ATTR81("Q9", "裤长", "Q9-A02", "短裤"),
    ATTR82("Q9", "裤长", "Q9-A03", "中裤"),
    ATTR83("Q9", "裤长", "Q9-A04", "六分裤"),
    ATTR84("Q9", "裤长", "Q9-A05", "七分裤"),
    ATTR85("Q9", "裤长", "Q9-A06", "八分裤"),
    ATTR86("Q9", "裤长", "Q9-A07", "九分裤"),
    ATTR87("Q9", "裤长", "Q9-A08", "长裤"),
    ATTR88("Q9", "裤长", "Q9-A09", "超长裤"),
    ATTR89("Q10", "裙型", "Q10-A11", "直筒裙"),
    ATTR90("Q10", "裙型", "Q10-A12", "A型裙"),
    ATTR91("Q10", "裙型", "Q10-A13", "圆裙"),
    ATTR92("Q10", "裙型", "Q10-A14", "百褶裙"),
    ATTR93("Q10", "裙型", "Q10-A15", "包裹裙"),
    ATTR94("Q10", "裙型", "Q10-A16", "不规则裙"),
    ATTR95("Q10", "裙型", "Q10-A01", "A型"),
    ATTR96("Q10", "裙型", "Q10-A02", "H型"),
    ATTR97("Q10", "裙型", "Q10-A03", "其他(T/Y/V)"),
    ATTR98("Q10", "裙型", "Q10-A04", "异型"),
    ATTR99("Q10", "裙型", "Q10-A05", "O型"),
    ATTR100("Q10", "裙型", "Q10-A06", "X型"),
    ATTR101("Q10", "裙型", "Q10-A07", "T型"),
    ATTR102("Q10", "裙型", "Q10-A08", "S型"),
    ATTR103("Q10", "裙型", "Q10-A09", "吊带连衣裙"),
    ATTR104("Q10", "裙型", "Q10-A10", "抹胸连衣裙"),
    ATTR105("Q11", "腰类型1", "Q11-A01", "直腰"),
    ATTR106("Q11", "腰类型1", "Q11-A02", "弧腰"),
    ATTR107("Q11", "腰类型1", "Q11-A03", "松紧腰"),
    ATTR108("Q11", "腰类型1", "Q11-A04", "半松紧腰"),
    ATTR109("Q11", "腰类型1", "Q11-A05", "系带腰"),
    ATTR110("Q11", "腰类型1", "Q11-A06", "织片腰"),
    ATTR111("Q12", "腰类型2", "Q12-A01", "高腰"),
    ATTR112("Q12", "腰类型2", "Q12-A02", "中高腰"),
    ATTR113("Q12", "腰类型2", "Q12-A03", "中腰"),
    ATTR114("Q12", "腰类型2", "Q12-A04", "中低腰"),
    ATTR115("Q12", "腰类型2", "Q12-A05", "低腰"),
    ATTR116("Q12", "腰类型2", "Q12-A06", "超低腰"),
    ATTR117("Q13", "裙长", "Q13-A1", "指尖"),
    ATTR118("Q13", "裙长", "Q13-A2", "大腿中部"),
    ATTR119("Q13", "裙长", "Q13-A3", "及膝"),
    ATTR120("Q13", "裙长", "Q13-A4", "膝上10cm"),
    ATTR121("Q13", "裙长", "Q13-A5", "膝上6cm"),
    ATTR122("Q13", "裙长", "Q13-A6", "小腿肚"),
    ATTR123("Q13", "裙长", "Q13-A7", "小腿肚下13cm"),
    ATTR124("Q13", "裙长", "Q13-A8", "小腿肚下8cm"),
    ATTR125("Q13", "裙长", "Q13-A9", "脚踝上10cm"),
    ATTR126("Q13", "裙长", "Q13-A10", "脚踝上5cm"),
    ATTR127("Q13", "裙长", "Q13-A11", "脚踝下"),
    ATTR128("Q13", "裙长", "Q13-A12", "脚面"),
    ATTR139("Q4", "肩型", "Q4-A07", "/"),;
    
    private String mainAttrCode;
    
    private String mainAttrName;
    
    private String mainAttrValueCode;
    
    private String mainAttrValueName;

    public static List<String> getByMainAttrCode(String mainAttrCode, String category) {
        List<String> ret = new ArrayList<>();
        //297 连衣裙     298 腰裙
        if ("297".equals(category) && Objects.equals("Q10", mainAttrCode)) {
            ret.add(ATTR95.getMainAttrValueName());
            ret.add(ATTR96.getMainAttrValueName());
            ret.add(ATTR97.getMainAttrValueName());
            ret.add(ATTR98.getMainAttrValueName());
            ret.add(ATTR99.getMainAttrValueName());
            ret.add(ATTR100.getMainAttrValueName());
            ret.add(ATTR101.getMainAttrValueName());
            ret.add(ATTR102.getMainAttrValueName());
            ret.add(ATTR103.getMainAttrValueName());
            ret.add(ATTR104.getMainAttrValueName());
        } else if ("298".equals(category) && Objects.equals("Q10", mainAttrCode)) {
            ret.add(ATTR89.getMainAttrValueName());
            ret.add(ATTR90.getMainAttrValueName());
            ret.add(ATTR91.getMainAttrValueName());
            ret.add(ATTR92.getMainAttrValueName());
            ret.add(ATTR93.getMainAttrValueName());
            ret.add(ATTR94.getMainAttrValueName());
        } else {
            ret = Arrays.stream(MainAttrEnum.values())
                    .filter(v -> Objects.equals(mainAttrCode, v.getMainAttrCode()))
                    .map(MainAttrEnum::getMainAttrValueName)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return ret;
    }

    public static List<String> mainAttrValueCode(String mainAttrCode, String category) {
         return Arrays.stream(MainAttrEnum.values())
                .filter(v -> Objects.equals(mainAttrCode, v.getMainAttrCode()))
                .map(MainAttrEnum::getMainAttrValueCode)
                .distinct()
                .collect(Collectors.toList());
    }

}
