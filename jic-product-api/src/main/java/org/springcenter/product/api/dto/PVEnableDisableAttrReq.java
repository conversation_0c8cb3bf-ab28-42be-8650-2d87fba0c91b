package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date:2023/7/18 17:16
 */
@Data
@ApiModel(value = "属性起禁用")
public class PVEnableDisableAttrReq {
    @ApiModelProperty(value = "属性id")
    @NotNull(message = "属性id不能为null")
    private Long attrId;

    @ApiModelProperty(value = "0是启用 1是禁用")
    @NotNull(message = "起禁用不能为null")
    private Integer isDisabled;
}
