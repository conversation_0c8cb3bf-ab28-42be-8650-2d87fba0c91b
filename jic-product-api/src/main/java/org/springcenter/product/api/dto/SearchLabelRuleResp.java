package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/10/12 14:25
 */
@Data
public class SearchLabelRuleResp {

    @ApiModelProperty(value = "父级标签id 比如给都市设置规则 传都市的id")
    private String pid;

    @ApiModelProperty(value = "父级标签code")
    private String pCode;

    @ApiModelProperty(value = "父级标签name")
    private String pName;

    @ApiModelProperty(value = "基础主条件")
    private List<MainRuleData> mainRules;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @Data
    public static class MainRuleData {
        @ApiModelProperty(value = "主键")
        private String id;

        @ApiModelProperty(value = "主条件的运算规则 0是且 1是或")
        private Integer calcRelation;

        @ApiModelProperty(value = "主条件的子条件")
        private List<RuleDetailData> ruleDetails;

        @ApiModelProperty(value = "0正常 1已删除")
        private Integer isDeleted;
    }

    @Data
    public static class RuleDetailData {

        @ApiModelProperty(value = "主键")
        private String id;

        @ApiModelProperty(value = "条件名称的code")
        private String labelNameCode;

        @ApiModelProperty(value = "条件名称的名称")
        private String labelName;

        @ApiModelProperty(value = "条件名称类型 0是标签 1是属性")
        private Integer labelNameType;

        @ApiModelProperty(value = "条件名称和值的关系 0是包含 1是不包含")
        private Integer nameValueRelation;

        @ApiModelProperty(value = "条件值的名称")
        private String labelValue;

        @ApiModelProperty(value = "条件值的code")
        private String labelValueCode;

        @ApiModelProperty(value = "基础主条件的运算规则 0是且 1是或")
        private Integer calcRelation;

        @ApiModelProperty(value = "0正常 1已删除")
        private Integer isDeleted;
    }
}
