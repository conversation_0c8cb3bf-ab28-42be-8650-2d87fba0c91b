package org.springcenter.product.api.fallback;
import com.alibaba.fastjson.JSONArray;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.api.IProductSmartScreenApi;
import org.springcenter.product.api.dto.back.SmartScreenProductInfoResp;
import org.springcenter.product.api.dto.back.SmartScreenSkcInfoResp;
import org.springcenter.product.api.dto.back.screen.StoreRecommendGoodsReq;
import org.springcenter.product.api.dto.back.screen.StoreRecommendGoodsResp;

import java.util.List;


/**
 * 进入fallback的方法 检查是否token未设置
 */
@Slf4j
public class ProductSmartScreenApiFallback implements IProductSmartScreenApi {

    @Setter
    private Throwable cause;


    @Override
    public ResponseResult<List<SmartScreenSkcInfoResp>> searchProductSkcInfo(CommonRequest<String> request) {
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<SmartScreenProductInfoResp> searchProductInfo(CommonRequest<String> request) {
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<JSONArray> searchParamInfo(CommonRequest request) {
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<List<StoreRecommendGoodsResp>> screenRecommendProductList(CommonRequest<StoreRecommendGoodsReq> request) {
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<JSONArray> getSizeNos(CommonRequest request) {
        return ResponseResult.success();
    }
}
