package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/6 14:08
 */
@Data
public class ProductVersionDetailResp {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "品牌code")
    private String brandCode;

    @ApiModelProperty(value = "品牌名称")
    private String branCodeName;

    @ApiModelProperty(value = "spu 大货号")
    private String spu;

    @ApiModelProperty(value = "productId")
    private String productId;

    @ApiModelProperty(value = "版型号")
    private String modelNumber;

    @ApiModelProperty(value = "样衣号")
    private String sampleNo;

    @ApiModelProperty(value = "主料Code")
    private String mainIngredientCode;

    @ApiModelProperty(value = "主料信息")
    private String mainIngredientName;

    @ApiModelProperty(value = "品类代码")
    private String categoryCode;

    @ApiModelProperty(value = "品类名称")
    private String categoryName;

    /*@ApiModelProperty(value = "合体度")
    private BaseInfo degreeOfFit;

    @ApiModelProperty(value = "体型")
    private BaseInfo bodySize;

    @ApiModelProperty(value = "领型")
    private BaseInfo collar;

    @ApiModelProperty(value = "肩型")
    private BaseInfo shoulderType;

    @ApiModelProperty(value = "袖型")
    private BaseInfo sleeveType;

    @ApiModelProperty(value = "袖长")
    private BaseInfo sleeveLength;

    @ApiModelProperty(value = "衣长")
    private BaseInfo clothLength;

    @ApiModelProperty(value = "裤型")
    private BaseInfo pantsType;

    @ApiModelProperty(value = "裤长")
    private BaseInfo pantsLength;

    @ApiModelProperty(value = "裙型")
    private BaseInfo skirtsType;

    @ApiModelProperty(value = "裙长")
    private BaseInfo skirtsLength;

    @ApiModelProperty(value = "腰型1")
    private BaseInfo waistShape1;

    @ApiModelProperty(value = "腰型2")
    private BaseInfo waistShape2;*/

    @ApiModelProperty(value = "属性")
    private List<BaseInfo> attrs;

    @ApiModelProperty(value = "引用次数")
    private String citationNumber;

    @ApiModelProperty(value = "销量")
    private String soldNumber;

    @ApiModelProperty(value = "是否开启 0开启 1关闭")
    private Integer isClosed;

    @ApiModelProperty(value = "照片")
    private String pics;

    @ApiModelProperty(value = "有大货号时的照片")
    private String mainPic;

    @ApiModelProperty(value = "商详图")
    private String detailImgs;

    @ApiModelProperty(value = "小品名")
    private String smallName;

    @Data
    public static class BaseInfo{
        @ApiModelProperty(value = "属性关联值id")
        private String id;

        @ApiModelProperty(value = "属性名称code")
        private String attrCode;

        @ApiModelProperty(value = "属性名称")
        private String attrName;

        @ApiModelProperty(value = "属性值code")
        private String valueCode;

        @ApiModelProperty(value = "属性值名称")
        private String valueName;

        private List<BaseInfo> child;
    }

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "skcId")
    private String skc_id;
}
