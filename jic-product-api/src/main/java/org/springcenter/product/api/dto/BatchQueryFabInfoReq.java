package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/7/1 16:08
 */
@Data
@ApiModel(value = "批量查询重点面料和重点图案")
public class BatchQueryFabInfoReq {
    @ApiModelProperty(value = "商品ids")
    @NotEmpty(message = "商品信息不能为空")
    private List<String> productIds;
}
