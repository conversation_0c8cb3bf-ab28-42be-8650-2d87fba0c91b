package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date:2023/10/12 17:11
 */
@Data
public class QueryLabelRuleSettingReq {

    @ApiModelProperty(value = "查询参数")
    @NotBlank(message = "查询参数不能为空")
    private String queryParam;

    @ApiModelProperty(value = "0标签 1属性")
    private Integer type;

    @ApiModelProperty(value = "名称的id")
    private String id;

    @ApiModelProperty(value = "code")
    private String settingCode;
}
