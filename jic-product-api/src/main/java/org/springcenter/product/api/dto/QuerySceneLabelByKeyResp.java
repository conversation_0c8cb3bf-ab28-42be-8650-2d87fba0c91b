package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/19 15:33
 */
@Data
public class QuerySceneLabelByKeyResp {

    @ApiModelProperty(value = "标签code")
    private String labelCode;

    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "三级标签")
    private List<QuerySceneLabelByKeyResp> subCodes;


}
