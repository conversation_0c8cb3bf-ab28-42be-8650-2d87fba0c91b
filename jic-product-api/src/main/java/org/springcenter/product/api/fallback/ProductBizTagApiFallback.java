package org.springcenter.product.api.fallback;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import lombok.Setter;
import org.springcenter.product.api.IProductBizTagApi;
import org.springcenter.product.api.dto.ProductBizTagResp;
import org.springcenter.product.api.dto.QueryProductBizTags;

import java.util.ArrayList;
import java.util.List;

public class ProductBizTagApiFallback implements IProductBizTagApi {
    @Setter
    private Throwable cause;

    @Override
    public ResponseResult<List<ProductBizTagResp>> queryProductBizTags(CommonRequest<QueryProductBizTags> request) {
        return ResponseResult.success(new ArrayList<>());
    }
}
