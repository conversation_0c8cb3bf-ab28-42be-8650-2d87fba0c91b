package org.springcenter.product.api.factory;

import feign.hystrix.FallbackFactory;
import org.springcenter.product.api.IProductDetailsImgApi;
import org.springcenter.product.api.fallback.ProductDetailsImgApiFallback;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date:2024/5/30 15:07
 */
@Component
public class ProductDetailsImgApiFallbackFactory implements FallbackFactory<IProductDetailsImgApi> {
    @Override
    public IProductDetailsImgApi create(Throwable cause) {
        ProductDetailsImgApiFallback fallback = new ProductDetailsImgApiFallback();
        fallback.setCause(cause);
        return fallback;
    }
}
