package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2022/12/11 15:08
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum CategoryAttrEnum {
    CA1("296", "T恤", "Q1", "合体度", "Q1-S1", "胸围"),
    CA2("296", "T恤", "Q1", "合体度", "Q1-S2", "腰围"),
    CA3("296", "T恤", "Q1", "合体度", "Q1-S4", "摆围"),
    CA4("296", "T恤", "Q2", "体型", "", ""),
    CA5("296", "T恤", "Q3", "领型", "Q3-S5", "领围"),
    CA6("296", "T恤", "Q3", "领型", "Q3-S6", "横开领"),
    CA7("296", "T恤", "Q3", "领型", "Q3-S7", "前领深"),
    CA8("296", "T恤", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA9("296", "T恤", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA10("296", "T恤", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA11("296", "T恤", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA12("296", "T恤", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA13("302", "背心", "Q1", "合体度", "Q1-S1", "胸围"),
    CA14("302", "背心", "Q1", "合体度", "Q1-S2", "腰围"),
    CA15("302", "背心", "Q1", "合体度", "Q1-S4", "摆围"),
    CA16("302", "背心", "Q2", "体型", "", ""),
    CA17("302", "背心", "Q3", "领型", "Q3-S5", "领围"),
    CA18("302", "背心", "Q3", "领型", "Q3-S6", "横开领"),
    CA19("302", "背心", "Q3", "领型", "Q3-S7", "前领深"),
    CA20("302", "背心", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA21("302", "背心", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA22("302", "背心", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA23("302", "背心", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA24("302", "背心", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA25("301", "衬衣", "Q1", "合体度", "Q1-S1", "胸围"),
    CA26("301", "衬衣", "Q1", "合体度", "Q1-S2", "腰围"),
    CA27("301", "衬衣", "Q1", "合体度", "Q1-S4", "摆围"),
    CA28("301", "衬衣", "Q2", "体型", "", ""),
    CA29("301", "衬衣", "Q3", "领型", "Q3-S5", "领围"),
    CA30("301", "衬衣", "Q3", "领型", "Q3-S6", "横开领"),
    CA31("301", "衬衣", "Q3", "领型", "Q3-S7", "前领深"),
    CA32("301", "衬衣", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA33("301", "衬衣", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA34("301", "衬衣", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA35("301", "衬衣", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA36("301", "衬衣", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA37("294", "毛衫", "Q1", "合体度", "Q1-S1", "胸围"),
    CA38("294", "毛衫", "Q1", "合体度", "Q1-S2", "腰围"),
    CA39("294", "毛衫", "Q1", "合体度", "Q1-S4", "摆围"),
    CA40("294", "毛衫", "Q2", "体型", "", ""),
    CA41("294", "毛衫", "Q3", "领型", "Q3-S5", "领围"),
    CA42("294", "毛衫", "Q3", "领型", "Q3-S6", "横开领"),
    CA43("294", "毛衫", "Q3", "领型", "Q3-S7", "前领深"),
    CA44("294", "毛衫", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA45("294", "毛衫", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA46("294", "毛衫", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA47("294", "毛衫", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA48("294", "毛衫", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA49("2428", "卫衣", "Q1", "合体度", "Q1-S1", "胸围"),
    CA50("2428", "卫衣", "Q1", "合体度", "Q1-S2", "腰围"),
    CA51("2428", "卫衣", "Q1", "合体度", "Q1-S4", "摆围"),
    CA52("2428", "卫衣", "Q2", "体型", "", ""),
    CA53("2428", "卫衣", "Q3", "领型", "Q3-S5", "领围"),
    CA54("2428", "卫衣", "Q3", "领型", "Q3-S6", "横开领"),
    CA55("2428", "卫衣", "Q3", "领型", "Q3-S7", "前领深"),
    CA56("2428", "卫衣", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA57("2428", "卫衣", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA58("2428", "卫衣", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA59("2428", "卫衣", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA60("2428", "卫衣", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA61("3176", "西服", "Q1", "合体度", "Q1-S1", "胸围"),
    CA62("3176", "西服", "Q1", "合体度", "Q1-S2", "腰围"),
    CA63("3176", "西服", "Q1", "合体度", "Q1-S4", "摆围"),
    CA64("3176", "西服", "Q2", "体型", "", ""),
    CA65("3176", "西服", "Q3", "领型", "Q3-S5", "领围"),
    CA66("3176", "西服", "Q3", "领型", "Q3-S6", "横开领"),
    CA67("3176", "西服", "Q3", "领型", "Q3-S7", "前领深"),
    CA68("3176", "西服", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA69("3176", "西服", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA70("3176", "西服", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA71("3176", "西服", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA72("3176", "西服", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA73("1221", "夹克", "Q1", "合体度", "Q1-S1", "胸围"),
    CA74("1221", "夹克", "Q1", "合体度", "Q1-S2", "腰围"),
    CA75("1221", "夹克", "Q1", "合体度", "Q1-S4", "摆围"),
    CA76("1221", "夹克", "Q2", "体型", "", ""),
    CA77("1221", "夹克", "Q3", "领型", "Q3-S5", "领围"),
    CA78("1221", "夹克", "Q3", "领型", "Q3-S6", "横开领"),
    CA79("1221", "夹克", "Q3", "领型", "Q3-S7", "前领深"),
    CA80("1221", "夹克", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA81("1221", "夹克", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA82("1221", "夹克", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA83("1221", "夹克", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA84("1221", "夹克", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA85("3158", "马甲", "Q1", "合体度", "Q1-S1", "胸围"),
    CA86("3158", "马甲", "Q1", "合体度", "Q1-S2", "腰围"),
    CA87("3158", "马甲", "Q1", "合体度", "Q1-S4", "摆围"),
    CA88("3158", "马甲", "Q2", "体型", "", ""),
    CA89("3158", "马甲", "Q3", "领型", "Q3-S5", "领围"),
    CA90("3158", "马甲", "Q3", "领型", "Q3-S6", "横开领"),
    CA91("3158", "马甲", "Q3", "领型", "Q3-S7", "前领深"),
    CA92("3158", "马甲", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA93("3158", "马甲", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA94("3158", "马甲", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA95("3158", "马甲", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA96("3158", "马甲", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA97("3177", "大衣", "Q1", "合体度", "Q1-S1", "胸围"),
    CA98("3177", "大衣", "Q1", "合体度", "Q1-S2", "腰围"),
    CA99("3177", "大衣", "Q1", "合体度", "Q1-S4", "摆围"),
    CA100("3177", "大衣", "Q2", "体型", "", ""),
    CA101("3177", "大衣", "Q3", "领型", "Q3-S5", "领围"),
    CA102("3177", "大衣", "Q3", "领型", "Q3-S6", "横开领"),
    CA103("3177", "大衣", "Q3", "领型", "Q3-S7", "前领深"),
    CA104("3177", "大衣", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA105("3177", "大衣", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA106("3177", "大衣", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA107("3177", "大衣", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA108("3177", "大衣", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA109("3178", "风衣", "Q1", "合体度", "Q1-S1", "胸围"),
    CA110("3178", "风衣", "Q1", "合体度", "Q1-S2", "腰围"),
    CA111("3178", "风衣", "Q1", "合体度", "Q1-S4", "摆围"),
    CA112("3178", "风衣", "Q2", "体型", "", ""),
    CA113("3178", "风衣", "Q3", "领型", "Q3-S5", "领围"),
    CA114("3178", "风衣", "Q3", "领型", "Q3-S6", "横开领"),
    CA115("3178", "风衣", "Q3", "领型", "Q3-S7", "前领深"),
    CA116("3178", "风衣", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA117("3178", "风衣", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA118("3178", "风衣", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA119("3178", "风衣", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA120("3178", "风衣", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA121("3179", "皮衣皮草", "Q1", "合体度", "Q1-S1", "胸围"),
    CA122("3179", "皮衣皮草", "Q1", "合体度", "Q1-S2", "腰围"),
    CA123("3179", "皮衣皮草", "Q1", "合体度", "Q1-S4", "摆围"),
    CA124("3179", "皮衣皮草", "Q2", "体型", "", ""),
    CA125("3179", "皮衣皮草", "Q3", "领型", "Q3-S5", "领围"),
    CA126("3179", "皮衣皮草", "Q3", "领型", "Q3-S6", "横开领"),
    CA127("3179", "皮衣皮草", "Q3", "领型", "Q3-S7", "前领深"),
    CA128("3179", "皮衣皮草", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA129("3179", "皮衣皮草", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA130("3179", "皮衣皮草", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA131("3179", "皮衣皮草", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA132("3179", "皮衣皮草", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA133("31791", "仿皮衣皮草", "Q1", "合体度", "Q1-S1", "胸围"),
    CA134("31791", "仿皮衣皮草", "Q1", "合体度", "Q1-S2", "腰围"),
    CA135("31791", "仿皮衣皮草", "Q1", "合体度", "Q1-S4", "摆围"),
    CA136("31791", "仿皮衣皮草", "Q2", "体型", "", ""),
    CA137("31791", "仿皮衣皮草", "Q3", "领型", "Q3-S5", "领围"),
    CA138("31791", "仿皮衣皮草", "Q3", "领型", "Q3-S6", "横开领"),
    CA139("31791", "仿皮衣皮草", "Q3", "领型", "Q3-S7", "前领深"),
    CA140("31791", "仿皮衣皮草", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA141("31791", "仿皮衣皮草", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA142("31791", "仿皮衣皮草", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA143("31791", "仿皮衣皮草", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA144("31791", "仿皮衣皮草", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA145("293", "棉衣", "Q1", "合体度", "Q1-S1", "胸围"),
    CA146("293", "棉衣", "Q1", "合体度", "Q1-S2", "腰围"),
    CA147("293", "棉衣", "Q1", "合体度", "Q1-S4", "摆围"),
    CA148("293", "棉衣", "Q2", "体型", "", ""),
    CA149("293", "棉衣", "Q3", "领型", "Q3-S5", "领围"),
    CA150("293", "棉衣", "Q3", "领型", "Q3-S6", "横开领"),
    CA151("293", "棉衣", "Q3", "领型", "Q3-S7", "前领深"),
    CA152("293", "棉衣", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA153("293", "棉衣", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA154("293", "棉衣", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA155("293", "棉衣", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA156("293", "棉衣", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA157("295", "羽绒服", "Q1", "合体度", "Q1-S1", "胸围"),
    CA158("295", "羽绒服", "Q1", "合体度", "Q1-S2", "腰围"),
    CA159("295", "羽绒服", "Q1", "合体度", "Q1-S4", "摆围"),
    CA160("295", "羽绒服", "Q2", "体型", "", ""),
    CA161("295", "羽绒服", "Q3", "领型", "Q3-S5", "领围"),
    CA162("295", "羽绒服", "Q3", "领型", "Q3-S6", "横开领"),
    CA163("295", "羽绒服", "Q3", "领型", "Q3-S7", "前领深"),
    CA164("295", "羽绒服", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA165("295", "羽绒服", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA166("295", "羽绒服", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA167("295", "羽绒服", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA168("295", "羽绒服", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA169("1026", "连体衣", "Q1", "合体度", "Q1-S1", "胸围"),
    CA170("1026", "连体衣", "Q1", "合体度", "Q1-S2", "腰围"),
    CA171("1026", "连体衣", "Q1", "合体度", "Q1-S3", "臀围"),
    CA172("1026", "连体衣", "Q8", "裤型", "Q8-S13", "脚口围"),
    CA173("1026", "连体衣", "Q3", "领型", "Q3-S5", "领围"),
    CA174("1026", "连体衣", "Q3", "领型", "Q3-S6", "横开领"),
    CA175("1026", "连体衣", "Q3", "领型", "Q3-S7", "前领深"),
    CA176("1026", "连体衣", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA177("1026", "连体衣", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA178("1026", "连体衣", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA179("1026", "连体衣", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA180("1026", "连体衣", "Q9", "裤长", "Q9-S14", "裤长尺寸"),
    CA181("297", "连衣裙", "Q1", "合体度", "Q1-S1", "胸围"),
    CA182("297", "连衣裙", "Q1", "合体度", "Q1-S2", "腰围"),
    CA183("297", "连衣裙", "Q1", "合体度", "Q1-S3", "臀围"),
    CA184("297", "连衣裙", "Q1", "合体度", "Q1-S4", "摆围"),
    CA185("297", "连衣裙", "Q10", "裙型", "", ""),
    CA186("297", "连衣裙", "Q3", "领型", "Q3-S5", "领围"),
    CA187("297", "连衣裙", "Q3", "领型", "Q3-S6", "横开领"),
    CA188("297", "连衣裙", "Q3", "领型", "Q3-S7", "前领深"),
    CA189("297", "连衣裙", "Q4", "肩型", "Q4-S8", "肩宽"),
    CA190("297", "连衣裙", "Q5", "袖型", "Q5-S9", "夹圈"),
    CA191("297", "连衣裙", "Q5", "袖型", "Q5-S10", "袖肥"),
    CA192("297", "连衣裙", "Q6", "袖长", "Q6-S11", "后中袖长"),
    CA193("297", "连衣裙", "Q7", "衣长", "Q7-S12", "肩点量衣长"),
    CA194("299", "裤子", "Q1", "合体度", "Q1-S3", "臀围"),
    CA195("299", "裤子", "Q8", "裤型", "Q8-S13", "脚口围"),
    CA196("299", "裤子", "Q11", "腰类型1", "Q11-S16", "下装腰围"),
    CA197("299", "裤子", "Q12", "腰类型2", "", ""),
    CA198("299", "裤子", "Q9", "裤长", "Q9-S14", "裤长尺寸"),
    CA199("298", "腰裙", "Q1", "合体度", "Q1-S3", "臀围"),
    CA200("298", "腰裙", "Q10", "裙型", "Q10-S15", "摆围"),
    CA201("298", "腰裙", "Q11", "腰类型1", "Q11-S16", "下装腰围"),
    CA202("298", "腰裙", "Q12", "腰类型2", "", ""),
    CA203("298", "腰裙", "Q13", "裙长", "Q13-S17", "裙长尺寸"),;

    private String categoryCode;

    private String categoryName;

    private String mainAttrTitleCode;

    private String mainAttrTitleName;

    private String subAttrCode;

    private String subAttrName;

    public static List<String> getMainAttrByCategory(String categoryCode) {
        return Arrays.stream(CategoryAttrEnum.values())
                .filter(v -> Objects.equals(v.getCategoryCode(), categoryCode))
                .map(CategoryAttrEnum::getMainAttrTitleCode)
                .distinct()
                .collect(Collectors.toList());
    }

    public static List<String> getSubAttrByCategory(String categoryCode, String mainAttrTitleCode) {
        return Arrays.stream(CategoryAttrEnum.values())
                .filter(v -> Objects.equals(v.getCategoryCode(), categoryCode) &&
                        Objects.equals(v.getMainAttrTitleCode(), mainAttrTitleCode))
                .map(CategoryAttrEnum::getSubAttrCode)
                .distinct()
                .collect(Collectors.toList());
    }
}
