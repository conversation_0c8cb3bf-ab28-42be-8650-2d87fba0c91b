package org.springcenter.product.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2022/12/10 15:04
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum MQueriesTabFilterEnum {

    /**
     * 模板列表版型从高到低
     */
    MODEL_NUMBER("T", 0, "模板列表版型从高到低"),
    /**
     * 模板列表使用次数从低到高
     */
    CITATION_NUMBER("C", 1, "模板列表使用次数从低到高"),
    /**
     * 销量列表近一个月
     */
    ONE_MONTH("M1", 0, "销量列表近一个月"),
    /**
     * 销量列表近三个月
     */
    THREE_MONTH("M2", 1, "销量列表近三个月"),
    /**
     * 销量列表近六个月
     */
    SIC_MONTH("M3", 2, "销量列表近六个月"),
    /**
     * 销量列表近一年
     */
    ONE_YEAR("M4", 3, "销量列表近一年");
    ;

    private String code;

    private Integer dealFlag;

    private String desc;

    public static MQueriesTabFilterEnum getByCode(String code) {
         return Arrays.stream(MQueriesTabFilterEnum.values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(null);
    }
}
