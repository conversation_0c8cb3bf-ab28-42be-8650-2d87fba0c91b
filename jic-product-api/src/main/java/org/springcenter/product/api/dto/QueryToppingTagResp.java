package org.springcenter.product.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/5/16 9:20
 */
@Data
public class QueryToppingTagResp {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "款号")
    private String spu;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "置顶类型")
    private Integer type;

    @ApiModelProperty(value = "是否流转 0：不流转 1：流转")
    private Integer isFlow;

    @ApiModelProperty(value = "有效时间")
    private Integer duringDay;

}
