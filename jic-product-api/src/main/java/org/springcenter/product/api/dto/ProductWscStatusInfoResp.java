package org.springcenter.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/6/8 14:08
 */
@Data
public class ProductWscStatusInfoResp {

    @ApiModelProperty("商品ID")
    private Long productId;

    @ApiModelProperty("0不可售 1可售")
    private Integer isCanSell;

    @ApiModelProperty("0下架 1上架 虚拟状态 可售+上下架")
    private Integer isPutway;

    @ApiModelProperty("0下架 1上架")
    private Integer isInfactPutway;
}
