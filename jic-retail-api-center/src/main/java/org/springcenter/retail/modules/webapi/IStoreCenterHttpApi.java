package org.springcenter.retail.modules.webapi;

import com.jnby.common.ResponseResult;
import org.springcenter.retail.modules.webapi.req.ListStoreReq;
import org.springcenter.retail.modules.webapi.resp.ListStoreResp;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;

public interface IStoreCenterHttpApi {
    /**
     * 根据门店包号获取门店信息
     */
    @POST("sdk/store-center/store/package/location")
    Call<ResponseResult<List<ListStoreResp>>> listStore(@Body ListStoreReq req);

    /**
     * 根据门店包号获取门店信息
     */
    @POST("sdk/store-center/store/list")
    Call<ResponseResult<List<ListStoreResp>>> list(@Body ListStoreReq req);

}
