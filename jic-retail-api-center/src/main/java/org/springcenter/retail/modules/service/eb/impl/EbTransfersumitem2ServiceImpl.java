package org.springcenter.retail.modules.service.eb.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springcenter.product.api.dto.SkuResp;
import org.springcenter.retail.api.dto.req.AddAircraftBoxDTO;
import org.springcenter.retail.api.dto.resp.AircraftBoxListDTO;
import org.springcenter.retail.modules.exception.RetailException;
import org.springcenter.retail.modules.mapper.bojun.EbTransfersumitem2Mapper;
import org.springcenter.retail.modules.model.bojun.EbTransfersumitem2;
import org.springcenter.retail.modules.model.bojun.MProductAlias;
import org.springcenter.retail.modules.service.base.IMProductAliasService;
import org.springcenter.retail.modules.service.base.IProductService;
import org.springcenter.retail.modules.service.eb.IEbTransfersumitem2Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

;

/**
 * @auther wangchun
 * @create 2023-05-06 13:51:14
 * @describe 服务实现类
 */
@Service
public class EbTransfersumitem2ServiceImpl extends ServiceImpl<EbTransfersumitem2Mapper, EbTransfersumitem2> implements IEbTransfersumitem2Service {

    @Resource
    private IProductService productService;

    @Resource
    private EbTransfersumitem2Mapper ebTransfersumitem2Mapper;

    @Resource
    private IMProductAliasService imProductAliasService;

    @Override
    public void addAircraftBox(AddAircraftBoxDTO addAircraftBoxDTO) {
        QueryWrapper<EbTransfersumitem2> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("eb_transfersum_id", addAircraftBoxDTO.getEbId());
        queryWrapper.eq("m_productalias_id", addAircraftBoxDTO.getSkuId());
        queryWrapper.eq("isactive", "Y");
        queryWrapper.select("id", "qty", "pricelist");
        List<EbTransfersumitem2> list = this.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            EbTransfersumitem2 updateEntity = new EbTransfersumitem2();
            updateEntity.setId(list.get(0).getId());
            updateEntity.setQty(list.get(0).getQty() + 1L);
            updateEntity.setTotAmtList(list.get(0).getPricelist().multiply(BigDecimal.valueOf(updateEntity.getQty())));
            updateEntity.setModifieddate(new Date());
            this.updateById(updateEntity);
            return;
        }
        List<SkuResp> skuRespList = productService.selectBySkuIds(Collections.singletonList(addAircraftBoxDTO.getSkuId()));
        SkuResp skuResp = skuRespList.get(0);
        // 批量查询商品信息
        QueryWrapper<MProductAlias> mProductAliasQueryWrapper = new QueryWrapper<>();
        mProductAliasQueryWrapper.eq("id", addAircraftBoxDTO.getSkuId());
        mProductAliasQueryWrapper.select("id,m_product_id as mProductId,M_ATTRIBUTESETINSTANCE_ID as mAttributesetinstanceId,no");
        List<MProductAlias> mProductAliasList = imProductAliasService.list(mProductAliasQueryWrapper);
        MProductAlias product = mProductAliasList.get(0);
        EbTransfersumitem2 insertEntity = new EbTransfersumitem2();
        insertEntity.setId(this.getMaxId());
        insertEntity.setAdClientId(37L);
        insertEntity.setAdOrgId(27L);
        insertEntity.setEbTransfersumId(Long.valueOf(addAircraftBoxDTO.getEbId()));
        insertEntity.setmProductId(product.getmProductId());
        insertEntity.setmProductaliasId(addAircraftBoxDTO.getSkuId());
        insertEntity.setmAttributesetinstanceId(product.getmAttributesetinstanceId());
        insertEntity.setQty(1L);
        insertEntity.setPricelist(BigDecimal.valueOf(skuResp.getPrice()));
        insertEntity.setTotAmtList(BigDecimal.valueOf(skuResp.getPrice()));
        insertEntity.setOwnerid(893L);
        insertEntity.setModifierid(893L);
        insertEntity.setCreationdate(new Date());
        insertEntity.setModifieddate(new Date());
        insertEntity.setIsactive("Y");
        this.save(insertEntity);
    }

    @Override
    public Long getMaxId() {
        return ebTransfersumitem2Mapper.getMaxId();
    }

    @Override
    public void delAircraftBox(AddAircraftBoxDTO addAircraftBoxDTO) {
        QueryWrapper<EbTransfersumitem2> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("eb_transfersum_id", addAircraftBoxDTO.getEbId());
        queryWrapper.eq("m_productalias_id", addAircraftBoxDTO.getSkuId());
        queryWrapper.eq("isactive", "Y");
        queryWrapper.select("id", "qty", "pricelist");
        List<EbTransfersumitem2> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            throw new RetailException("未查询到飞机盒信息");
        }
        EbTransfersumitem2 ebTransfersumitem2 = list.get(0);
        if (ebTransfersumitem2.getQty() > 1) {
            EbTransfersumitem2 updateEntity = new EbTransfersumitem2();
            updateEntity.setId(ebTransfersumitem2.getId());
            updateEntity.setQty(ebTransfersumitem2.getQty() - 1L);
            updateEntity.setTotAmtList(ebTransfersumitem2.getPricelist().multiply(BigDecimal.valueOf(updateEntity.getQty())));
            updateEntity.setModifieddate(new Date());
            this.updateById(updateEntity);
            return;
        }
        this.removeById(ebTransfersumitem2.getId());
    }

    @Override
    public List<AircraftBoxListDTO> aircraftBoxList(String ebId) {
        QueryWrapper<EbTransfersumitem2> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("eb_transfersum_id", ebId);
        queryWrapper.eq("isactive", "Y");
        queryWrapper.select("id", "qty", "pricelist", "m_productalias_id as mProductaliasId");
        List<EbTransfersumitem2> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        // 需要将数量大于1的进行拆分
        List<AircraftBoxListDTO> result = new ArrayList<>();
        List<Long> skuIds = list.stream().map(EbTransfersumitem2::getmProductaliasId).collect(Collectors.toList());
        List<SkuResp> skuRespList = productService.selectBySkuIds(skuIds);
        List<EbTransfersumitem2> sortList = list.stream().sorted(Comparator.comparing(EbTransfersumitem2::getCreationdate)).collect(Collectors.toList());
        Iterator<EbTransfersumitem2> iterator = sortList.iterator();
        if (iterator.hasNext()) {
            EbTransfersumitem2 next = iterator.next();
            SkuResp skuResp = skuRespList.stream().filter(e -> e.getId().equals(next.getmProductaliasId().toString())).findFirst().orElse(null);
            if (ObjectUtils.isNotEmpty(skuResp)) {
                AircraftBoxListDTO aircraftBoxListDTO = new AircraftBoxListDTO();
                aircraftBoxListDTO.setSkuId(next.getmProductaliasId().toString());
                aircraftBoxListDTO.setSku(skuResp.getNo());
                aircraftBoxListDTO.setName(skuResp.getValue());
                for (int i = 0; i < next.getQty(); i++) {
                    result.add(aircraftBoxListDTO);
                }
            }
        }
        return result;
    }
}
