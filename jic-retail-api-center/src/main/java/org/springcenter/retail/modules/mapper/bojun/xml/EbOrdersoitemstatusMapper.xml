<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.retail.modules.mapper.bojun.EbOrdersoitemstatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.retail.modules.model.bojun.EbOrdersoitemstatus">
        <result column="ID" property="id" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="ORDERNO" property="orderno" />
        <result column="EB_ORDERSO_ID" property="ebOrdersoId" />
        <result column="EB_ORDERSOITEM_ID" property="ebOrdersoitemId" />
        <result column="M_PRODUCTALIAS_ID" property="mProductaliasId" />
        <result column="M_PRODUCT_ID" property="mProductId" />
        <result column="UNIQUESKU" property="uniquesku" />
        <result column="STATUS" property="status" />
        <result column="OWNERID" property="ownerid" />
        <result column="MODIFIERID" property="modifierid" />
        <result column="CREATIONDATE" property="creationdate" />
        <result column="MODIFIEDDATE" property="modifieddate" />
        <result column="ISACTIVE" property="isactive" />
        <result column="SALESREPID" property="salesrepid" />
        <result column="M_UNIQUESKUITEM_ID" property="mUniqueskuitemId" />
        <result column="TYPE" property="type" />
        <result column="IS_DEL" property="isDel" />
        <result column="PRICE" property="isDel" />
        <result column="M_ATTRIBUTESETINSTANCE_ID" property="mAttributesetinstanceId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, ORDERNO, EB_ORDERSO_ID, EB_ORDERSOITEM_ID, M_PRODUCTALIAS_ID, M_PRODUCT_ID, UNIQUESKU, STATUS, OWNERID, MODIFIERID, CREATIONDATE, MODIFIEDDATE, ISACTIVE, SALESREPID, M_UNIQUESKUITEM_ID, TYPE,IS_DEL,PRICE,M_ATTRIBUTESETINSTANCE_ID
    </sql>

    <!-- 获取序列id -->
    <select id="getMaxId" resultType="java.lang.Long" useCache="false" flushCache="true">
        select seq_eb_ordersoitemstatus.nextval as seq from dual
    </select>
</mapper>