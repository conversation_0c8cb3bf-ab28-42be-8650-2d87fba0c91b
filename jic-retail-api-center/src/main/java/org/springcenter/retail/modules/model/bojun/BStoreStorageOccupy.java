package org.springcenter.retail.modules.model.bojun;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: wangchun
 * @Date: 2023-05-08 10:11:05
 * @Description: 库存占用单
 */
@TableName("B_STORE_STORAGE_OCCUPY")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="BStoreStorageOccupy对象", description="库存占用单")
public class BStoreStorageOccupy implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private Long id;
    @ApiModelProperty(value = "是否可用: Y是 N否")
    @TableField("IS_ACTIVE")
    private String isActive;
    @ApiModelProperty(value = "类型: 1占用 -1释放")
    @TableField("OCCUPY_TYPE")
    private Integer occupyType;
    @ApiModelProperty(value = "门店id")
    @TableField("C_STORE_ID")
    private Long cStoreId;
    @ApiModelProperty(value = "spuId")
    @TableField("M_PRODUCT_ID")
    private Long mProductId;
    @ApiModelProperty(value = "skuId")
    @TableField("M_PRODUCTALIAS_ID")
    private Long mProductaliasId;
    @ApiModelProperty(value = "数量")
    @TableField("QTY")
    private Long qty;
    @ApiModelProperty(value = "来源单号")
    @TableField("SOURCE_NO")
    private String sourceNo;
    @ApiModelProperty(value = "描述")
    @TableField("DESCRIPTION")
    private String description;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "是否删除 1是 0否")
    @TableField("IS_DEL")
    private Integer isDel;

    @ApiModelProperty(value = "业务单号")
    @TableField("BUSINESS_TYPE")
    private String businessType;


    @ApiModelProperty(value = "导购id")
    @TableField("hr_employee_id")
    private String hrEmployeeId;


    @ApiModelProperty(value = "导购名称")
    @TableField("employee_name")
    private String employeeName;

    public String getHrEmployeeId() {
        return hrEmployeeId;
    }

    public void setHrEmployeeId(String hrEmployeeId) {
        this.hrEmployeeId = hrEmployeeId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public Long getId() {
        return id;
    }

    public BStoreStorageOccupy setId(Long id) {
        this.id = id;
        return this;
    }

    public String getIsActive() {
        return isActive;
    }

    public BStoreStorageOccupy setIsActive(String isActive) {
        this.isActive = isActive;
        return this;
    }

    public Integer getOccupyType() {
        return occupyType;
    }

    public BStoreStorageOccupy setOccupyType(Integer occupyType) {
        this.occupyType = occupyType;
        return this;
    }

    public Long getcStoreId() {
        return cStoreId;
    }

    public BStoreStorageOccupy setcStoreId(Long cStoreId) {
        this.cStoreId = cStoreId;
        return this;
    }

    public Long getmProductId() {
        return mProductId;
    }

    public BStoreStorageOccupy setmProductId(Long mProductId) {
        this.mProductId = mProductId;
        return this;
    }

    public Long getmProductaliasId() {
        return mProductaliasId;
    }

    public BStoreStorageOccupy setmProductaliasId(Long mProductaliasId) {
        this.mProductaliasId = mProductaliasId;
        return this;
    }

    public Long getQty() {
        return qty;
    }

    public BStoreStorageOccupy setQty(Long qty) {
        this.qty = qty;
        return this;
    }

    public String getSourceNo() {
        return sourceNo;
    }

    public BStoreStorageOccupy setSourceNo(String sourceNo) {
        this.sourceNo = sourceNo;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public BStoreStorageOccupy setDescription(String description) {
        this.description = description;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public BStoreStorageOccupy setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public BStoreStorageOccupy setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public BStoreStorageOccupy setIsDel(Integer isDel) {
        this.isDel = isDel;
        return this;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    @Override
    public String toString() {
        return "BStoreStorageOccupyModel{" +
            "id=" + id +
            ", isActive=" + isActive +
            ", occupyType=" + occupyType +
            ", cStoreId=" + cStoreId +
            ", mProductId=" + mProductId +
            ", mProductaliasId=" + mProductaliasId +
            ", qty=" + qty +
            ", sourceNo=" + sourceNo +
            ", description=" + description +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDel=" + isDel +
            ", businessType=" + businessType +
            "}";
    }
}
