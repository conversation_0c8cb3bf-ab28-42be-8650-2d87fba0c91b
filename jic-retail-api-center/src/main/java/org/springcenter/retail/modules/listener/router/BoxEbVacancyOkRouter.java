package org.springcenter.retail.modules.listener.router;

import lombok.extern.slf4j.Slf4j;
import org.springcenter.retail.modules.event.ProducerMqEvent;
import org.springcenter.retail.modules.listener.IBroadCastRouter;
import org.springcenter.retail.modules.service.base.IMqService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Service
@RefreshScope
@Slf4j
public class BoxEbVacancyOkRouter implements IBroadCastRouter {
     public static String tags  = "boxEbVacancyOkTags";

     @Resource
     private IMqService mqService;

     @Override
     public String getTags() {
          return tags;
     }

     @Override
     public void consumeKillBillQueue(ProducerMqEvent event) throws Exception {
           mqService.sendMsg(this.getMessageDTO(event, HashMap.class), tags);
     }
}
