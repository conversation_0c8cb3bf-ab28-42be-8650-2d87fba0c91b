package org.springcenter.retail.modules.service.eb;

import org.springcenter.retail.api.dto.req.SummarizeStockDTO;
import org.springcenter.retail.modules.context.stock.SummarizeStockContext;
import org.springcenter.retail.modules.entity.eb.AllStoreNoStock;
import org.springcenter.retail.modules.entity.eb.Stock2EbEntity;

import java.util.List;


public interface ISummarizeStockService {

    /**
     * 组装需要集货的商品对象
     */
    void summarizeStock(SummarizeStockContext context);

    /**
     * 按照summarizeStock返回的集货对象进行调拨
     */
    void allotsBySummarizeResult(List<Stock2EbEntity> resultStoreStock, String sourceNo);
}
