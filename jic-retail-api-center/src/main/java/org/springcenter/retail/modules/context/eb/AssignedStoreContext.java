package org.springcenter.retail.modules.context.eb;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springcenter.retail.modules.entity.eb.AllStoreNoStock;
import org.springcenter.retail.modules.entity.eb.JICMapLocationRespEntity;
import org.springcenter.retail.modules.model.wx.BEbStore;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssignedStoreContext {

    @ApiModelProperty("门店信息")
    private List<BEbStore> storeList;

    @ApiModelProperty("门店商品库存")
    private List<AllStoreNoStock> allStoreNoStockList;

    @ApiModelProperty("要匹配的商品")
    private List<String> skuList;

    @ApiModelProperty("地址作坐标")
    private JICMapLocationRespEntity.Location location;

    @ApiModelProperty("匹配完成的返回体")
    private List<AllStoreNoStock> resp;

    @ApiModelProperty("会员门店id")
    private Long vipStoreId;

    @ApiModelProperty("来源编号")
    private String sourceCode;

    private String address;
}
