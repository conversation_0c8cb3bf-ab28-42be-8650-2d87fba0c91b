package org.springcenter.retail.modules.model.bojun;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springcenter.retail.modules.exception.RetailException;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 券规则表
 */
@Data
@TableName("VOUCHER_RULE")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value = "VoucherRule对象", description = "券规则表")
public class VoucherRule implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "ID")
    @TableId(value = "ID")
    private String ID;
    @TableField("AD_CLIENT_ID")
    private Long AD_CLIENT_ID;
    @TableField("AD_ORG_ID")
    private Long AD_ORG_ID;
    @TableField("ISACTIVE")
    private String ISACTIVE;
    @TableField("MODIFIERID")
    private Long MODIFIERID;
    @TableField("CREATIONDATE")
    private Date CREATIONDATE;
    @TableField("MODIFIEDDATE")
    private Date MODIFIEDDATE;
    @TableField("OWNERID")
    private Long OWNERID;
    @ApiModelProperty(value = "券名称")
    @TableField("VOU_NAME")
    private String VOU_NAME;
    @ApiModelProperty(value = "券类型")
    @TableField("VOU_TYPE")
    private String VOU_TYPE;
    @ApiModelProperty(value = "面额")
    @TableField("AMT_ACOUNT")
    private BigDecimal AMT_ACOUNT;
    @ApiModelProperty(value = "折扣")
    @TableField("VOU_DIS")
    private BigDecimal VOU_DIS;
    @ApiModelProperty(value = "详情")
    @TableField("DETAILS")
    private String DETAILS;
    @ApiModelProperty(value = "是否指定会员可用 ")
    @TableField("ISVIPUSE")
    private String ISVIPUSE;
    @ApiModelProperty(value = "数量限制")
    @TableField("QTY_LIMIT")
    private Long QTY_LIMIT;
    @ApiModelProperty(value = "使用渠道")
    @TableField("USE_FOR")
    private String USE_FOR;
    @ApiModelProperty(value = "商品限制")
    @TableField("PRODUCT_FILTER")
    private String PRODUCT_FILTER;
    @ApiModelProperty(value = "店铺限制")
    @TableField("STORE_FILTER")
    private String STORE_FILTER;
    @ApiModelProperty(value = "是否一单用一张")
    @TableField("ISONLY")
    private String ISONLY;
    @ApiModelProperty(value = "是否现金折扣同享")
    @TableField("ISZDJ")
    private String ISZDJ;
    @ApiModelProperty(value = "是否允许折上折")
    @TableField("ISZSZ")
    private String ISZSZ;
    @ApiModelProperty(value = "是否允许与其他规则券同时使用")
    @TableField("ISSTACK")
    private String ISSTACK;
    @ApiModelProperty(value = "金额限制（使用门槛）")
    @TableField("JELIMIT")
    private BigDecimal JELIMIT;
    @ApiModelProperty(value = "折扣限制（ 实付最低折扣）")
    @TableField("ZKLIMIT")
    private BigDecimal ZKLIMIT;
    @ApiModelProperty(value = "是否允许退券 ")
    @TableField("IS_BACK")
    private String IS_BACK;
    @ApiModelProperty(value = "转增次数限制 ")
    @TableField("IS_GIVE")
    private Long IS_GIVE;
    @ApiModelProperty(value = "券logo")
    @TableField("LOGO")
    private String LOGO;
    @ApiModelProperty(value = "跳转链接")
    @TableField("REDIRECT_URL")
    private String REDIRECT_URL;
    @ApiModelProperty(value = "wx_ruler_id")
    @TableField("RULEID")
    private String RULEID;
    @ApiModelProperty(value = "礼券周期(天)")
    @TableField("VOU_CYCLE")
    private Long VOU_CYCLE;
    @ApiModelProperty(value = "结束时间")
    @TableField("VALID_DATE")
    private Long VALID_DATE;
    @ApiModelProperty(value = "开始时间")
    @TableField("START_DATE")
    private Long START_DATE;
    @ApiModelProperty(value = "ZD指定时间SY按领取时间顺延")
    @TableField("DATE_TYPE")
    private String DATE_TYPE;
    @ApiModelProperty(value = "最新")
    @TableField("ISNEW")
    private String ISNEW;
    @ApiModelProperty(value = "最抢手")
    @TableField("ISHOT")
    private String ISHOT;
    @ApiModelProperty(value = "积分")
    @TableField("INTEGRAL")
    private String INTEGRAL;
    @ApiModelProperty(value = "允许兑换月份 (Y为全年，N为 new_activitymonth 表  月份  )")
    @TableField("EXMONTHID")
    private String EXMONTHID;
    @ApiModelProperty(value = "券类型   Y 优惠券    H  活动券")
    @TableField("VOUTYPE")
    private String VOUTYPE;
    @ApiModelProperty(value = "使用渠道WXSC微信商城BOX不止盒子UNDER线下")
    @TableField("USE_CHANNEL")
    private String USE_CHANNEL;
    @ApiModelProperty(value = "兑换说明")
    @TableField("EXDETAILS")
    private String EXDETAILS;
    @ApiModelProperty(value = "领取后几天生效")
    @TableField("VOU_USE_CYCLE")
    private Long VOU_USE_CYCLE;
    @ApiModelProperty(value = "券业绩归属")
    @TableField("STOREID")
    private Long STOREID;
    @ApiModelProperty(value = "使用渠道-微信商城")
    @TableField("C_WXSC")
    private String C_WXSC;
    @ApiModelProperty(value = "使用渠道-不止盒子")
    @TableField("C_BOX")
    private String C_BOX;
    @ApiModelProperty(value = "线下")
    @TableField("C_UNDER")
    private String C_UNDER;
    @ApiModelProperty(value = "发券部门")
    @TableField("DEPARTMENT")
    private String DEPARTMENT;
    @TableField("DESCRIPTION")
    private String DESCRIPTION;
    @TableField("AWARDID")
    private String AWARDID;
    @ApiModelProperty(value = "单人最大发券数量。空默认不限")
    @TableField("SENDTIMES")
    private Long SENDTIMES;
    @ApiModelProperty(value = "发券总数量")
    @TableField("SENDTIMES_COUNT")
    private Long SENDTIMES_COUNT;
    @ApiModelProperty(value = "微商城可用weid逗号隔开")
    @TableField("USE_APP")
    private String USE_APP;
    @ApiModelProperty(value = "可用JNBY")
    @TableField("A_JNBY")
    private String A_JNBY;
    @ApiModelProperty(value = "可用LESS")
    @TableField("A_LESS")
    private String A_LESS;
    @ApiModelProperty(value = "可用速写")
    @TableField("A_QS")
    private String A_QS;
    @ApiModelProperty(value = "可用蓬马")
    @TableField("A_PM")
    private String A_PM;
    @ApiModelProperty(value = "可用jnbybyJNBY")
    @TableField("A_JN")
    private String A_JN;
    @ApiModelProperty(value = "可用奥莱")
    @TableField("A_OUTLET")
    private String A_OUTLET;
    @ApiModelProperty(value = "可用APN")
    @TableField("A_APN")
    private String A_APN;
    @ApiModelProperty(value = "可用home")
    @TableField("A_HOME")
    private String A_HOME;
    @ApiModelProperty(value = "可用JNBY+")
    @TableField("A_JNBYA")
    private String A_JNBYA;
    @ApiModelProperty(value = "是否发模板消息")
    @TableField("SEND_MSG")
    private String SEND_MSG;
    @ApiModelProperty(value = "品牌限制")
    @TableField("PP")
    private String PP;
    @ApiModelProperty(value = "叠加活动")
    @TableField("ACTIVITY_FILTER")
    private String ACTIVITY_FILTER;
    @ApiModelProperty(value = "是否同步商家券Y微信Z支付宝N不同步")
    @TableField("ISWX_VOU")
    private String ISWX_VOU;
    @TableField("STOCK_ID")
    private String STOCK_ID;
    @ApiModelProperty(value = "可同时使用——积分")
    @TableField("D_SCORE")
    private String D_SCORE;
    @ApiModelProperty(value = "可同时使用——余额")
    @TableField("D_BALANCE")
    private String D_BALANCE;
    @ApiModelProperty(value = "可同时使用——储值卡")
    @TableField("D_CARD")
    private String D_CARD;
    @ApiModelProperty(value = "是否允许多次核销")
    @TableField("MULTI_VERIFY")
    private String MULTI_VERIFY;
    @TableField("STATUS")
    private Integer STATUS;
    @TableField("STATUSERID")
    private Long STATUSERID;
    @TableField("STATUSTIME")
    private Date STATUSTIME;
    @ApiModelProperty(value = "最少数量限制")
    @TableField("MINIMUM_QTY_LIMIT")
    private Long MINIMUM_QTY_LIMIT;
    @ApiModelProperty(value = "金额限制（使用上限）")
    @TableField("MAX_JELIMIT")
    private BigDecimal MAX_JELIMIT;
    @ApiModelProperty(value = "购物券用途")
    @TableField("VOU_USE_TYPE")
    private Long VOU_USE_TYPE;
    @ApiModelProperty(value = "购物券分类")
    @TableField("VOU_CATEGORY")
    private String VOU_CATEGORY;
    @ApiModelProperty(value = "券领取方式")
    @TableField("RECEIVE_TYPE")
    private Integer RECEIVE_TYPE;
    @ApiModelProperty(value = "OA活动流程编号")
    @TableField("OA_PROCEDURE_NO")
    private String OA_PROCEDURE_NO;
    @ApiModelProperty(value = "跨部门承担费用（仅针对需财务分摊费用的）")
    @TableField("CROSS_DEPART_COST")
    private String CROSS_DEPART_COST;
    @ApiModelProperty(value = "结束时间(时分秒)")
    @TableField("VALID_DATE_TYPE")
    private Date VALID_DATE_TYPE;
    @ApiModelProperty(value = "开始时间(时分秒)")
    @TableField("START_DATE_TYPE")
    private Date START_DATE_TYPE;
    @ApiModelProperty(value = "跳转地址名称")
    @TableField("REDIRECT_NAME")
    private String REDIRECT_NAME;
    @ApiModelProperty(value = "店仓使用渠道-微信商城")
    @TableField("C_WXSC_STORE")
    private String C_WXSC_STORE;
    @ApiModelProperty(value = "店仓使用渠道-不止盒子")
    @TableField("C_BOX_STORE")
    private String C_BOX_STORE;
    @ApiModelProperty(value = "店仓-线下")
    @TableField("C_UNDER_STORE")
    private String C_UNDER_STORE;

    public void addMustParam(Long nextId) {
        if (this == null) {
            throw new RetailException("对象为空，无法处理");
        }
        this.setID(nextId.toString());
        this.setAD_CLIENT_ID(37L);
        this.setAD_ORG_ID(27L);
        this.setOWNERID(893L);
        this.setMODIFIERID(893L);
        this.setCREATIONDATE(new Date());
        this.setMODIFIEDDATE(new Date());
        // 默认 审核通过
        this.setSTATUS(6);
    }
}
