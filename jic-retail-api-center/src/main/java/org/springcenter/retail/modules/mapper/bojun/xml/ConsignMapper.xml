<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.retail.modules.mapper.bojun.ConsignMapper">

    <select id="selectCustomerUnOrder" resultType="java.lang.String">
        select a.description from b_consign a,c_client_vip b
        where a.c_vip_id = b.id
        and b.isactive ='Y'
        and a.isactive='Y'
        and a.paystatus = 'N'
        and b.unionid = #{unionId}
        <if test="vipId != null">
            and a.c_vip_id = #{vipId}
        </if>
    </select>

    <select id="selectCreateByDescription" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1) from b_consign a
        where a.isactive='Y'
        and a.paystatus = 'N'
        and a.description like #{description}
    </select>

    <select id="selectSubByDescription" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1) from b_consign a
        where a.isactive='Y'
        and a.status = 2
        and a.description = #{description}
    </select>

    <select id="selectOmsByCode" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1) from  t_omsonlineorder
        where sourcecode= #{code}
    </select>

    <select id="cancelConsign" parameterType="Map" statementType="CALLABLE">
            {call b_consign_cancel(
                #{boxNo,jdbcType=VARCHAR,mode=IN},
                #{r_code,jdbcType=DECIMAL,mode=OUT},
                #{r_message,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>
    <!--<resultMap id="" type="">-->

    <!--</resultMap>-->

    <!-- 创建寄售单 -->
    <select id="createConsign" parameterType="Map" statementType="CALLABLE">
            {call b_consign_insert(
                #{In_Json,jdbcType=CLOB,mode=IN},
                #{r_code,jdbcType=DECIMAL,mode=OUT},
                #{r_message,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <!-- 创建寄售单 取消日志自动commit-->
    <select id="createBoxConsign" parameterType="Map" statementType="CALLABLE">
        {call box_b_consign_insert(
                #{In_Json,jdbcType=CLOB,mode=IN},
                #{r_code,jdbcType=DECIMAL,mode=OUT},
                #{r_message,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>


    <!-- 提交寄售单 -->
    <select id="submitConsign" parameterType="Map" statementType="CALLABLE">
            {call b_consign_express(
                #{boxNo,jdbcType=VARCHAR,mode=IN},
                #{expressno,jdbcType=VARCHAR,mode=IN},
                #{boxId,jdbcType=VARCHAR,mode=IN},
                #{r_code,jdbcType=DECIMAL,mode=OUT},
                #{r_message,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <!-- 提交零售单 适用所有正常交易零售 -->
    <select id="submitCommonRetail" parameterType="Map" statementType="CALLABLE">
        {call b_consign_retail2022(
                #{In_Json,jdbcType=CLOB,mode=IN},
                #{r_code,jdbcType=DECIMAL,mode=OUT},
                #{r_message,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <!-- 提交零售单，先试后买网点零售 -->
    <select id="submitBuyBeforeTryRetail" parameterType="Map" statementType="CALLABLE">
        {call box_m_retail_Insert(
                #{In_Json,jdbcType=VARCHAR,mode=IN},
                #{Out_Orderid,jdbcType=VARCHAR,mode=OUT},
                #{Ret,jdbcType=DECIMAL,mode=OUT},
                #{Errmsg,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <!-- 提交零售单 购买导购飞机盒 -->
    <select id="submitSaleCarton" parameterType="Map" statementType="CALLABLE">
        {call box_sale_retail_Insert(
                #{In_Json,jdbcType=VARCHAR,mode=IN},
                #{Out_Orderid,jdbcType=VARCHAR,mode=OUT},
                #{Ret,jdbcType=DECIMAL,mode=OUT},
                #{Errmsg,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>


    <select id="submitPortalEbRetail" parameterType="Map" statementType="CALLABLE">
        {call portal_eb_orderso_Insert2019(
                #{in_json,jdbcType=VARCHAR,mode=IN},
                #{ret,jdbcType=DECIMAL,mode=OUT},
                #{errmsg,jdbcType=VARCHAR,mode=OUT},
                #{orderid,jdbcType=DECIMAL,mode=OUT}
            )}
    </select>

    <select id="submitWebPostRetail" parameterType="Map" statementType="CALLABLE">
        {call WEBPOS_RETAIL_INSERT(
                #{IN_JSON,jdbcType=VARCHAR,mode=IN},
                #{OUT_ORDERID,jdbcType=VARCHAR,mode=OUT},
                #{RET,jdbcType=DECIMAL,mode=OUT},
                #{ERRMSG,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <select id="submitTransferWd" parameterType="Map" statementType="CALLABLE">
        {call box_m_transfer_insert_in_wddc(
                #{pid,jdbcType=VARCHAR,mode=IN},
                #{r_code,jdbcType=DECIMAL,mode=OUT},
                #{r_message,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <select id="selectDocnoByDescription" parameterType="java.lang.String" resultType="java.lang.String">
        select docno from b_consign a
        where a.isactive='Y'
          and a.paystatus != 'C'
          and a.description like #{description}
    </select>

    <!--门店确认还货-->
    <select id="consignReturn" parameterType="Map" statementType="CALLABLE">
        {call b_consign_return2022(
                #{boxNo,jdbcType=VARCHAR,mode=IN},
                #{r_code,jdbcType=DECIMAL,mode=OUT},
                #{r_message,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <select id="virtualSendOms" parameterType="Map" statementType="CALLABLE">
        {call sp_oms_api_logisticssend(
                #{orderid,jdbcType=DECIMAL,mode=IN},
                #{logisticsno,jdbcType=VARCHAR,mode=IN},
                #{logisticsid,jdbcType=VARCHAR,mode=IN},
                #{origstore,jdbcType=VARCHAR,mode=IN},
                #{rweight,jdbcType=VARCHAR,mode=IN},
                #{code,jdbcType=DECIMAL,mode=OUT},
                #{msg,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <!--批量生成零售单-->
    <select id="batchCreateMRetail" parameterType="Map" statementType="CALLABLE">
        {call box_batch_create_retail(
                #{In_Json,jdbcType=CLOB,mode=IN},
                #{retailNo,jdbcType=VARCHAR,mode=OUT},
                #{Ret,jdbcType=DECIMAL,mode=OUT},
                #{Errmsg,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <select id="selectFirstDocnoByDescription" parameterType="java.lang.String" resultType="java.lang.String">
        select docno from b_consign a
        where a.isactive='Y'
          and a.paystatus != 'C'
          and a.description = #{description}
          and rownum =1
    </select>

    <select id="selectConsignIdByDescription" parameterType="java.lang.String" resultType="java.lang.Long">
        select id from b_consign a
        where a.isactive='Y' and a.paystatus='N'
          and a.description = #{description}
    </select>


    <update id="updateAddressById" parameterType="java.util.Map">
        update b_consign
        set ADDRESS = #{address,jdbcType=VARCHAR},
            MOBIL=#{mobil,jdbcType=VARCHAR},
            VIPNAME=#{vipName,jdbcType=VARCHAR},
            EXPRESSNO=#{expressNo,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=DECIMAL}
    </update>


    <!-- b_consign寄售单封装-->
    <resultMap id="BConsignMap" type="org.springcenter.retail.modules.entity.order.BConsignEntity">
        <id column="id" property="id"/>
        <result column="docno" property="docNo"/>
        <result column="c_store_id" property="cStoreId"/>
        <result column="description" property="description"/>
        <result column="c_vip_id" property="cVipId"/>
        <result column="status" property="status"/>
        <result column="paystatus" property="payStatus"/>
    </resultMap>
    <sql id="BConsignColumn">
        id,docno,c_store_id,description,c_vip_id,status,paystatus
    </sql>
    <select id="selectConsignByDescription" parameterType="java.lang.String" resultMap="BConsignMap">
        select
        <include refid="BConsignColumn"/>
        from b_consign a
        where a.isactive='Y'
        and a.description = #{description}
    </select>


    <select id="selectRetailNumByRefNo" parameterType="java.lang.String" resultType="java.lang.String">
        select docno
        from m_retail
        where refno = #{refNo}
    </select>


    <select id="selectHomeOrderLogNumBySourcecode" parameterType="java.lang.String" resultType="java.lang.String">
        select error_msg
        from home_order_log
        where sourcecode = #{sourcecode}
    </select>

    <select id="selectRetailByOridNumAndDesc" parameterType="java.lang.String" resultType="java.lang.String">
        select docno
        from m_retail
        where oridocnum = #{oridNum}
        and description = '由BOX购买飞机盒生成'
    </select>

</mapper>
