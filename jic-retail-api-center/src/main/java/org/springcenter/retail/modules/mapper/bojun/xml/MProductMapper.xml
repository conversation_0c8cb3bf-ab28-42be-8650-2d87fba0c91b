<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.retail.modules.mapper.bojun.MProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.retail.modules.model.bojun.MProduct">
        <id column="ID" property="id" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="ISACTIVE" property="isactive" />
        <result column="CREATIONDATE" property="creationdate" />
        <result column="OWNERID" property="ownerid" />
        <result column="MODIFIEDDATE" property="modifieddate" />
        <result column="MODIFIERID" property="modifierid" />
        <result column="C_UOM_ID" property="cUomId" />
        <result column="NAME" property="name" />
        <result column="VALUE" property="value" />
        <result column="M_SIZEGROUP_ID" property="mSizegroupId" />
        <result column="M_ATTRIBUTESET_ID" property="mAttributesetId" />
        <result column="IMAGEURL" property="imageurl" />
        <result column="M_DIM1_ID" property="mDim1Id" />
        <result column="M_DIM2_ID" property="mDim2Id" />
        <result column="M_DIM3_ID" property="mDim3Id" />
        <result column="M_DIM4_ID" property="mDim4Id" />
        <result column="M_DIM5_ID" property="mDim5Id" />
        <result column="M_DIM6_ID" property="mDim6Id" />
        <result column="M_DIM7_ID" property="mDim7Id" />
        <result column="M_DIM8_ID" property="mDim8Id" />
        <result column="M_DIM9_ID" property="mDim9Id" />
        <result column="M_DIM10_ID" property="mDim10Id" />
        <result column="SERIALNO" property="serialno" />
        <result column="PROAREA" property="proarea" />
        <result column="BATCHNO" property="batchno" />
        <result column="FLOWNO" property="flowno" />
        <result column="UNIT" property="unit" />
        <result column="DOCUMENTNOTE" property="documentnote" />
        <result column="PRECOST" property="precost" />
        <result column="PRICELIST" property="pricelist" />
        <result column="HELP" property="help" />
        <result column="DESCRIPTION" property="description" />
        <result column="DESCRIPTIONURL" property="descriptionurl" />
        <result column="VERSIONNO" property="versionno" />
        <result column="M_DIM11_ID" property="mDim11Id" />
        <result column="M_DIM12_ID" property="mDim12Id" />
        <result column="ISFAIRPRO" property="isfairpro" />
        <result column="M_DIM13_ID" property="mDim13Id" />
        <result column="M_DIM14_ID" property="mDim14Id" />
        <result column="M_DIM15_ID" property="mDim15Id" />
        <result column="FAIRPDTTYPE" property="fairpdttype" />
        <result column="COLORS" property="colors" />
        <result column="SIZES" property="sizes" />
        <result column="INTSCODE" property="intscode" />
        <result column="STATUSTIME" property="statustime" />
        <result column="STATUSERID" property="statuserid" />
        <result column="AU_PI_ID" property="auPiId" />
        <result column="AU_STATE" property="auState" />
        <result column="M_DIM16_ID" property="mDim16Id" />
        <result column="M_DIM17_ID" property="mDim17Id" />
        <result column="M_DIM18_ID" property="mDim18Id" />
        <result column="M_DIM19_ID" property="mDim19Id" />
        <result column="M_DIM20_ID" property="mDim20Id" />
        <result column="ADDSDESC" property="addsdesc" />
        <result column="SHORTNAME" property="shortname" />
        <result column="STEPCODE" property="stepcode" />
        <result column="DATASOURCE" property="datasource" />
        <result column="FABCODE" property="fabcode" />
        <result column="FABELEMENT" property="fabelement" />
        <result column="WEIGHT" property="weight" />
        <result column="GENERATED" property="generated" />
        <result column="STATUS" property="status" />
        <result column="PROTYPE" property="protype" />
        <result column="MAINFABREMARK" property="mainfabremark" />
        <result column="WITHFABREMARK" property="withfabremark" />
        <result column="Y_FACTORY_ID" property="yFactoryId" />
        <result column="FABDATE" property="fabdate" />
        <result column="CLOTHDATE" property="clothdate" />
        <result column="OTCDATE" property="otcdate" />
        <result column="STYLEDESCRIPTION" property="styledescription" />
        <result column="BADICON" property="badicon" />
        <result column="CRAFTWORKIMG" property="craftworkimg" />
        <result column="PACKSTYLE" property="packstyle" />
        <result column="TAILORNOTICE" property="tailornotice" />
        <result column="METRICIMG" property="metricimg" />
        <result column="CRAFTWORKIMGTWO" property="craftworkimgtwo" />
        <result column="BATHMODE" property="bathmode" />
        <result column="Y_STYLE_BOM_ID" property="yStyleBomId" />
        <result column="CRAFTWORKIMGTHREE" property="craftworkimgthree" />
        <result column="Y_PROOF_ID" property="yProofId" />
        <result column="CONFIRMCOMMENT" property="confirmcomment" />
        <result column="PRICELIST2" property="pricelist2" />
        <result column="STANDARD" property="standard" />
        <result column="COMPOSITION" property="composition" />
        <result column="BATHAREA" property="batharea" />
        <result column="QUALITY" property="quality" />
        <result column="Y_BATHMODE_ID1" property="yBathmodeId1" />
        <result column="Y_BATHMODE_ID2" property="yBathmodeId2" />
        <result column="Y_BATHMODE_ID3" property="yBathmodeId3" />
        <result column="Y_BATHMODE_ID4" property="yBathmodeId4" />
        <result column="Y_BATHMODE_ID5" property="yBathmodeId5" />
        <result column="Y_BATHMODE_ID6" property="yBathmodeId6" />
        <result column="Y_BATHMODE_ID7" property="yBathmodeId7" />
        <result column="SUGGESTMOQ" property="suggestmoq" />
        <result column="ALLOTSTATE" property="allotstate" />
        <result column="ALLOTASK" property="allotask" />
        <result column="C_SUPPLIER_ID" property="cSupplierId" />
        <result column="TRANSSTATUS" property="transstatus" />
        <result column="ORIGCOST" property="origcost" />
        <result column="PRICE_SUG" property="priceSug" />
        <result column="PRICEDIS" property="pricedis" />
        <result column="ACOST" property="acost" />
        <result column="M_COLOR_ID" property="mColorId" />
        <result column="BOX_QTY" property="boxQty" />
        <result column="B_CODE_FORMAT_ID" property="bCodeFormatId" />
        <result column="IF_RET" property="ifRet" />
        <result column="LOWEST_DISCOUNT" property="lowestDiscount" />
        <result column="ISGIFT" property="isgift" />
        <result column="IS_INSPECTION" property="isInspection" />
        <result column="COMPOSITION2_EN" property="composition2En" />
        <result column="COMPOSITION1_EN" property="composition1En" />
        <result column="VALUE3_CODE" property="value3Code" />
        <result column="VALUE2_CODE" property="value2Code" />
        <result column="COMPOSITION_CODE" property="compositionCode" />
        <result column="DECLARE_CODE" property="declareCode" />
        <result column="IS_NEWGOODS" property="isNewgoods" />
        <result column="DECLARE1_EN" property="declare1En" />
        <result column="DECLARE1" property="declare1" />
        <result column="DECLAREEN_ID" property="declareenId" />
        <result column="DECLARE_ID" property="declareId" />
        <result column="VALUE2_EN" property="value2En" />
        <result column="VALUE3" property="value3" />
        <result column="COMPOSITION1" property="composition1" />
        <result column="COMPOSITION2" property="composition2" />
        <result column="COMPOSITION_ID" property="compositionId" />
        <result column="VALUE2" property="value2" />
        <result column="IF_WHE" property="ifWhe" />
        <result column="M_DIM21_ID" property="mDim21Id" />
        <result column="PLANNEDATE" property="plannedate" />
        <result column="PLAN_QTYIN" property="planQtyin" />
        <result column="IS_RETAIL" property="isRetail" />
        <result column="C_ARCBRAND_ID" property="cArcbrandId" />
        <result column="DELIVERYDATE" property="deliverydate" />
        <result column="NOTLIMITDIS" property="notlimitdis" />
        <result column="IS_MSCODE" property="isMscode" />
        <result column="ENGLISHVALUE" property="englishvalue" />
        <result column="ALLOT_REMARK" property="allotRemark" />
        <result column="PDT_TYPE" property="pdtType" />
        <result column="M_PRODUCT_ORIG" property="mProductOrig" />
        <result column="NOSORT" property="nosort" />
        <result column="HANDDIS" property="handdis" />
        <result column="ONLINE_MONTH" property="onlineMonth" />
        <result column="STYLE_LABEL1" property="styleLabel1" />
        <result column="STYLE_LABEL2" property="styleLabel2" />
        <result column="STYLE_LABEL3" property="styleLabel3" />
        <result column="PRODUCT_LABEL1" property="productLabel1" />
        <result column="PRODUCT_LABEL2" property="productLabel2" />
        <result column="BASIC_LABEL" property="basicLabel" />
        <result column="FIRSTDATE" property="firstdate" />
        <result column="M_DIM29_ID" property="mDim29Id" />
        <result column="M_DIM28_ID" property="mDim28Id" />
        <result column="UOM" property="uom" />
        <result column="DESCRIPTION01" property="description01" />
        <result column="DESCRIPTION02" property="description02" />
        <result column="DESCRIPTION03" property="description03" />
        <result column="DESCRIPTION04" property="description04" />
        <result column="DESCRIPTION05" property="description05" />
        <result column="SKIPAUTHORIZE" property="skipauthorize" />
        <result column="ISFREE" property="isfree" />
        <result column="INTERNAL_PURCHASE" property="internalPurchase" />
        <result column="SEX" property="sex" />
        <result column="IS_YISUI" property="isYisui" />
        <result column="CON_DATE" property="conDate" />
        <result column="M_DIM30_ID" property="mDim30Id" />
        <result column="M_DIM31_ID" property="mDim31Id" />
        <result column="M_DIM32_ID" property="mDim32Id" />
        <result column="LIST_PRODUCT" property="listProduct" />
        <result column="IS_DJ" property="isDj" />
        <result column="IS_TIHUO" property="isTihuo" />
        <result column="PRODUCT_LIFE_CYCLE" property="productLifeCycle" />
        <result column="MATERTYPE_DL" property="matertypeDl" />
        <result column="MATERTYPE_ZL" property="matertypeZl" />
        <result column="MATERTYPE_XL" property="matertypeXl" />
        <result column="MATERTYPE_XLL" property="matertypeXll" />
        <result column="WORKMANSHIP" property="workmanship" />
        <result column="IS_UNIQUE" property="isUnique" />
        <result column="IS_RFID" property="isRfid" />
        <result column="MATER_KZ" property="materKz" />
        <result column="MATER_MF" property="materMf" />
        <result column="MATER_COST" property="materCost" />
        <result column="MATER_BHPRICE" property="materBhprice" />
        <result column="MATER_PFKZ" property="materPfkz" />
        <result column="MATER_PRICEREGION" property="materPriceregion" />
        <result column="CLOTH_CODE" property="clothCode" />
        <result column="CLOTH_FIT" property="clothFit" />
        <result column="CLOTH_BUST" property="clothBust" />
        <result column="CLOTH_TYPE" property="clothType" />
        <result column="CLOTH_COLLAR" property="clothCollar" />
        <result column="CLOTH_LONGTYPE" property="clothLongtype" />
        <result column="CLOTH_SLEEVE" property="clothSleeve" />
        <result column="CLOTH_LONG" property="clothLong" />
        <result column="CLOTH_PANTLONG" property="clothPantlong" />
        <result column="CLOTH_SKIRTLONG" property="clothSkirtlong" />
        <result column="CLOTH_DOWNKZ" property="clothDownkz" />
        <result column="MATER_NAME" property="materName" />
        <result column="CLOTH_DOWNHB" property="clothDownhb" />
        <result column="CLOTH_DL" property="clothDl" />
        <result column="CLOTH_XL" property="clothXl" />
        <result column="CLOTH_NAME" property="clothName" />
        <result column="PRODUCT_SEQ_CODE" property="productSeqCode" />
        <result column="PRODUCT_CODE" property="productCode" />
        <result column="IS_ZDKOMS" property="isZdkoms" />
        <result column="IS_FICTITIOUS" property="isFictitious" />
        <result column="QHXL" property="qhxl" />
        <result column="QHXFL" property="qhxfl" />
        <result column="QHXPM" property="qhxpm" />
        <result column="QHPM" property="qhpm" />
        <result column="BIG_PRODUCT_ID" property="bigProductId" />
        <result column="HTD" property="htd" />
        <result column="TX" property="tx" />
        <result column="IS_CON" property="isCon" />
        <result column="ORI_SEASON" property="oriSeason" />
        <result column="M_DIM2_ID_NEW" property="mDim2IdNew" />
        <result column="M_DIM3_ID_NEW" property="mDim3IdNew" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, ISACTIVE, CREATIONDATE, OWNERID, MODIFIEDDATE, MODIFIERID, C_UOM_ID, NAME, VALUE, M_SIZEGROUP_ID, M_ATTRIBUTESET_ID, IMAGEURL, M_DIM1_ID, M_DIM2_ID, M_DIM3_ID, M_DIM4_ID, M_DIM5_ID, M_DIM6_ID, M_DIM7_ID, M_DIM8_ID, M_DIM9_ID, M_DIM10_ID, SERIALNO, PROAREA, BATCHNO, FLOWNO, UNIT, DOCUMENTNOTE, PRECOST, PRICELIST, HELP, DESCRIPTION, DESCRIPTIONURL, VERSIONNO, M_DIM11_ID, M_DIM12_ID, ISFAIRPRO, M_DIM13_ID, M_DIM14_ID, M_DIM15_ID, FAIRPDTTYPE, COLORS, SIZES, INTSCODE, STATUSTIME, STATUSERID, AU_PI_ID, AU_STATE, M_DIM16_ID, M_DIM17_ID, M_DIM18_ID, M_DIM19_ID, M_DIM20_ID, ADDSDESC, SHORTNAME, STEPCODE, DATASOURCE, FABCODE, FABELEMENT, WEIGHT, GENERATED, STATUS, PROTYPE, MAINFABREMARK, WITHFABREMARK, Y_FACTORY_ID, FABDATE, CLOTHDATE, OTCDATE, STYLEDESCRIPTION, BADICON, CRAFTWORKIMG, PACKSTYLE, TAILORNOTICE, METRICIMG, CRAFTWORKIMGTWO, BATHMODE, Y_STYLE_BOM_ID, CRAFTWORKIMGTHREE, Y_PROOF_ID, CONFIRMCOMMENT, PRICELIST2, STANDARD, COMPOSITION, BATHAREA, QUALITY, Y_BATHMODE_ID1, Y_BATHMODE_ID2, Y_BATHMODE_ID3, Y_BATHMODE_ID4, Y_BATHMODE_ID5, Y_BATHMODE_ID6, Y_BATHMODE_ID7, SUGGESTMOQ, ALLOTSTATE, ALLOTASK, C_SUPPLIER_ID, TRANSSTATUS, ORIGCOST, PRICE_SUG, PRICEDIS, ACOST, M_COLOR_ID, BOX_QTY, B_CODE_FORMAT_ID, IF_RET, LOWEST_DISCOUNT, ISGIFT, IS_INSPECTION, COMPOSITION2_EN, COMPOSITION1_EN, VALUE3_CODE, VALUE2_CODE, COMPOSITION_CODE, DECLARE_CODE, IS_NEWGOODS, DECLARE1_EN, DECLARE1, DECLAREEN_ID, DECLARE_ID, VALUE2_EN, VALUE3, COMPOSITION1, COMPOSITION2, COMPOSITION_ID, VALUE2, IF_WHE, M_DIM21_ID, PLANNEDATE, PLAN_QTYIN, IS_RETAIL, C_ARCBRAND_ID, DELIVERYDATE, NOTLIMITDIS, IS_MSCODE, ENGLISHVALUE, ALLOT_REMARK, PDT_TYPE, M_PRODUCT_ORIG, NOSORT, HANDDIS, ONLINE_MONTH, STYLE_LABEL1, STYLE_LABEL2, STYLE_LABEL3, PRODUCT_LABEL1, PRODUCT_LABEL2, BASIC_LABEL, FIRSTDATE, M_DIM29_ID, M_DIM28_ID, UOM, DESCRIPTION01, DESCRIPTION02, DESCRIPTION03, DESCRIPTION04, DESCRIPTION05, SKIPAUTHORIZE, ISFREE, INTERNAL_PURCHASE, SEX, IS_YISUI, CON_DATE, M_DIM30_ID, M_DIM31_ID, M_DIM32_ID, LIST_PRODUCT, IS_DJ, IS_TIHUO, PRODUCT_LIFE_CYCLE, MATERTYPE_DL, MATERTYPE_ZL, MATERTYPE_XL, MATERTYPE_XLL, WORKMANSHIP, IS_UNIQUE, IS_RFID, MATER_KZ, MATER_MF, MATER_COST, MATER_BHPRICE, MATER_PFKZ, MATER_PRICEREGION, CLOTH_CODE, CLOTH_FIT, CLOTH_BUST, CLOTH_TYPE, CLOTH_COLLAR, CLOTH_LONGTYPE, CLOTH_SLEEVE, CLOTH_LONG, CLOTH_PANTLONG, CLOTH_SKIRTLONG, CLOTH_DOWNKZ, MATER_NAME, CLOTH_DOWNHB, CLOTH_DL, CLOTH_XL, CLOTH_NAME, PRODUCT_SEQ_CODE, PRODUCT_CODE, IS_ZDKOMS, IS_FICTITIOUS, QHXL, QHXFL, QHXPM, QHPM, BIG_PRODUCT_ID, HTD, TX, IS_CON, ORI_SEASON, M_DIM2_ID_NEW, M_DIM3_ID_NEW
    </sql>

</mapper>