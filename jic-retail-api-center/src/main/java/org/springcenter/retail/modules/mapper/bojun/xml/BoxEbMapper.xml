<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.retail.modules.mapper.bojun.BoxEbMapper">
    <!-- 生成要货单 -->
    <select id="transferSumInsert" parameterType="Map" statementType="CALLABLE">
        {call b_transfersum_create(
                #{In_Json,jdbcType=VARCHAR,mode=IN},
                #{r_code,jdbcType=DECIMAL,mode=OUT},
                #{r_message,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <!-- 自动匹配数量 -->
    <select id="autoMatchQty" parameterType="Map" statementType="CALLABLE">
        {call m_in_qtycop(
                #{p_user_id,jdbcType=INTEGER,mode=IN},
                #{p_query,jdbcType=VARCHAR,mode=IN},
                #{r_code,jdbcType=DECIMAL,mode=OUT},
                #{r_message,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <!-- 入库单提交 -->
    <select id="submitMIn" parameterType="Map" statementType="CALLABLE">
        {call m_in_submit(
                #{p_submittedsheetid,jdbcType=INTEGER,mode=IN},
                #{r_code,jdbcType=DECIMAL,mode=OUT},
                #{r_message,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <!-- 发货单提交 -->
    <select id="submitEbTransferSum" parameterType="Map" statementType="CALLABLE">
        {call eb_transfersum_submit(
                #{p_submittedsheetid,jdbcType=INTEGER,mode=IN},
                #{r_code,jdbcType=DECIMAL,mode=OUT},
                #{r_message,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

    <!-- 内淘发货-->
    <select id="boxEbTransferSumExpress" parameterType="Map" statementType="CALLABLE">
        {call box_eb_transfersum_express(
                #{p_id,jdbcType=INTEGER,mode=IN},
                #{express_no,jdbcType=VARCHAR,mode=IN},
                #{consign_json,jdbcType=CLOB,mode=IN},
                #{source_code,jdbcType=VARCHAR,mode=IN},
                #{r_code,jdbcType=DECIMAL,mode=OUT},
                #{r_message,jdbcType=VARCHAR,mode=OUT}
            )}
    </select>

</mapper>
