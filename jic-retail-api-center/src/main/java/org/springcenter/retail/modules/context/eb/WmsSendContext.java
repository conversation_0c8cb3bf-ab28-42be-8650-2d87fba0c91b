package org.springcenter.retail.modules.context.eb;

import lombok.Data;
import org.springcenter.retail.modules.entity.eb.SendExpressEntity;
import org.springcenter.retail.modules.model.bojun.EbTransfersum;
import org.springcenter.retail.modules.model.bojun.EbTransfersumitem;
import org.springcenter.retail.modules.model.wx.EbTransfersumWms;

import java.util.List;

@Data
public class WmsSendContext {

    private SolveBoxBusinessContext context;

    private SendExpressEntity sendExpressEntity;

    private EbTransfersum updateEbTransfersum;

    private List<EbTransfersumitem> updateEbTransfersumitemList;

    private EbTransfersumWms ebTransfersumWms;
}
