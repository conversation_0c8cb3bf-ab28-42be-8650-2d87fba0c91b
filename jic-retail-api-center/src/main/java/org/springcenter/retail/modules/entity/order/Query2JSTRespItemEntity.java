package org.springcenter.retail.modules.entity.order;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/5/29 14:03
 * @description
 */
@Data
public class Query2JSTRespItemEntity {
     @JSONField(name = "io_id")
     private Integer ioId;	//	退仓单号
     @JSONField(name = "amount")
     private BigDecimal amount;
     @JSONField(name = "sku_id")
     private String skuId;	//商品编码
     @JSONField(name = "unit")
     private String unit;	//单位
     @JSONField(name = "qty")
     private Integer qty;	//商品数量
     @JSONField(name = "name")
     private String name;	//商品名称
     @JSONField(name = "properties_value")
     private String propertiesValue;	//属性值
     @JSONField(name = "sale_price")
     private BigDecimal salePrice;	//销售价格
     @JSONField(name = "sale_amount")
     private BigDecimal saleAmount;	//销售总金额
     @JSONField(name = "ioi_id")
     private String ioiId;	//退仓子单号，用于区分货品行的行号


}
