<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.retail.modules.mapper.bojun.MTransferitemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.retail.modules.model.wx.MTransferitem">
        <id column="ID" property="id" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="OWNERID" property="ownerid" />
        <result column="MODIFIERID" property="modifierid" />
        <result column="CREATIONDATE" property="creationdate" />
        <result column="MODIFIEDDATE" property="modifieddate" />
        <result column="ISACTIVE" property="isactive" />
        <result column="STATUS" property="status" />
        <result column="M_TRANSFER_ID" property="mTransferId" />
        <result column="ORDERNO" property="orderno" />
        <result column="M_PRODUCT_ID" property="mProductId" />
        <result column="M_ATTRIBUTESETINSTANCE_ID" property="mAttributesetinstanceId" />
        <result column="QTYOUT" property="qtyout" />
        <result column="QTYIN" property="qtyin" />
        <result column="QTYDIFF" property="qtydiff" />
        <result column="PRICELIST" property="pricelist" />
        <result column="TOT_AMTOUT_LIST" property="totAmtoutList" />
        <result column="TOT_AMTIN_LIST" property="totAmtinList" />
        <result column="DESCRIPTION" property="description" />
        <result column="IN_STATUS" property="inStatus" />
        <result column="OUT_STATUS" property="outStatus" />
        <result column="QTY" property="qty" />
        <result column="TOT_AMTQTY_LIST" property="totAmtqtyList" />
        <result column="M_PRODUCTALIAS_ID" property="mProductaliasId" />
        <result column="BOXNO" property="boxno" />
        <result column="QTYFCAN" property="qtyfcan" />
        <result column="PRECOST" property="precost" />
        <result column="TOT_AMT_PRECOST" property="totAmtPrecost" />
        <result column="TOT_AMTIN_PRECOST" property="totAmtinPrecost" />
        <result column="QTYCAN" property="qtycan" />
        <result column="NC_STATUS" property="ncStatus" />
        <result column="NC_STATUSERID" property="ncStatuserid" />
        <result column="NC_STATUSTIME" property="ncStatustime" />
        <result column="ID1206" property="id1206" />
        <result column="QTYADDCHK" property="qtyaddchk" />
        <result column="QTYADDCHK_BEIYONG" property="qtyaddchkBeiyong" />
        <result column="RESERVATIONDETAIL4" property="reservationdetail4" />
        <result column="RESERVATIONDETAIL1" property="reservationdetail1" />
        <result column="C_CHANNEL_RESULTITEM_ID" property="cChannelResultitemId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, OWNERID, MODIFIERID, CREATIONDATE, MODIFIEDDATE, ISACTIVE, STATUS, M_TRANSFER_ID, ORDERNO, M_PRODUCT_ID, M_ATTRIBUTESETINSTANCE_ID, QTYOUT, QTYIN, QTYDIFF, PRICELIST, TOT_AMTOUT_LIST, TOT_AMTIN_LIST, DESCRIPTION, IN_STATUS, OUT_STATUS, QTY, TOT_AMTQTY_LIST, M_PRODUCTALIAS_ID, BOXNO, QTYFCAN, PRECOST, TOT_AMT_PRECOST, TOT_AMTIN_PRECOST, QTYCAN, NC_STATUS, NC_STATUSERID, NC_STATUSTIME, ID1206, QTYADDCHK, QTYADDCHK_BEIYONG, RESERVATIONDETAIL4, RESERVATIONDETAIL1, C_CHANNEL_RESULTITEM_ID
    </sql>

</mapper>