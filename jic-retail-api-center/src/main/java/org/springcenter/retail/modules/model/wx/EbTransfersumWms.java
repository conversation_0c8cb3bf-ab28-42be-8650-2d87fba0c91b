package org.springcenter.retail.modules.model.wx;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: wangchun
 * @Date: 2024-10-25 17:45:11
 * @Description: 
 */
@TableName("EB_TRANSFERSUM_WMS")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="EbTransfersumWms对象", description="")
public class EbTransfersumWms implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableField("ID")
    private String id;
    @TableField("EB_TRANSFERSUM_ID")
    private String ebTransfersumId;
    @TableField("M_TRANSFER_ID")
    private String mTransferId;
    @ApiModelProperty(value = "状态：0未同步 1同步完成")
    @TableField("STATUS")
    private Long status;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "内淘快递id")
    @TableField("EXPRESS_ID")
    private String expressId;
    @ApiModelProperty(value = "快递单号")
    @TableField("EXPRESS_NO")
    private String expressNo;
    @ApiModelProperty(value = "发货时间")
    @TableField("SEND_TIME")
    private Date sendTime;


    public String getId() {
        return id;
    }

    public EbTransfersumWms setId(String id) {
        this.id = id;
        return this;
    }

    public String getEbTransfersumId() {
        return ebTransfersumId;
    }

    public EbTransfersumWms setEbTransfersumId(String ebTransfersumId) {
        this.ebTransfersumId = ebTransfersumId;
        return this;
    }

    public String getmTransferId() {
        return mTransferId;
    }

    public EbTransfersumWms setmTransferId(String mTransferId) {
        this.mTransferId = mTransferId;
        return this;
    }

    public Long getStatus() {
        return status;
    }

    public EbTransfersumWms setStatus(Long status) {
        this.status = status;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public EbTransfersumWms setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public EbTransfersumWms setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getExpressId() {
        return expressId;
    }

    public EbTransfersumWms setExpressId(String expressId) {
        this.expressId = expressId;
        return this;
    }

    public String getExpressNo() {
        return expressNo;
    }

    public EbTransfersumWms setExpressNo(String expressNo) {
        this.expressNo = expressNo;
        return this;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public EbTransfersumWms setSendTime(Date sendTime) {
        this.sendTime = sendTime;
        return this;
    }

    @Override
    public String toString() {
        return "EbTransfersumWmsModel{" +
            "id=" + id +
            ", ebTransfersumId=" + ebTransfersumId +
            ", mTransferId=" + mTransferId +
            ", status=" + status +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", expressId=" + expressId +
            ", expressNo=" + expressNo +
            ", sendTime=" + sendTime +
            "}";
    }
}
