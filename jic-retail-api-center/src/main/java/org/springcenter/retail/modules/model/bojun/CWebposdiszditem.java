package org.springcenter.retail.modules.model.bojun;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 整单策略
 */
@TableName("C_WEBPOSDISZDITEM")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="CWebposdiszditem对象", description="")
public class CWebposdiszditem implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private Long id;
    @ApiModelProperty(value = "所属公司")
    @TableField("AD_CLIENT_ID")
    private Long adClientId;
    @ApiModelProperty(value = "所属组织")
    @TableField("AD_ORG_ID")
    private Long adOrgId;
    @ApiModelProperty(value = "所属WEBPOS组合策略")
    @TableField("C_WEBPOSDIS_ID")
    private Long cWebposdisId;
    @ApiModelProperty(value = "数量比较方式")
    @TableField("COMPARETYPE")
    private Long comparetype;
    @ApiModelProperty(value = "购买数量")
    @TableField("QTY")
    private Long qty;
    @ApiModelProperty(value = "购买金额")
    @TableField("TOT_AMT_ACTUAL")
    private BigDecimal totAmtActual;
    @ApiModelProperty(value = "关联方式")
    @TableField("RELATIONTYPE")
    private String relationtype;
    @ApiModelProperty(value = "创建人")
    @TableField("OWNERID")
    private Long ownerid;
    @ApiModelProperty(value = "修改人")
    @TableField("MODIFIERID")
    private Long modifierid;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATIONDATE")
    private Date creationdate;
    @ApiModelProperty(value = "修改时间")
    @TableField("MODIFIEDDATE")
    private Date modifieddate;
    @ApiModelProperty(value = "可用")
    @TableField("ISACTIVE")
    private String isactive;
    @ApiModelProperty(value = "金额比较方式")
    @TableField("COMPARETYPEAMT")
    private String comparetypeamt;


    public Long getId() {
        return id;
    }

    public CWebposdiszditem setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getAdClientId() {
        return adClientId;
    }

    public CWebposdiszditem setAdClientId(Long adClientId) {
        this.adClientId = adClientId;
        return this;
    }

    public Long getAdOrgId() {
        return adOrgId;
    }

    public CWebposdiszditem setAdOrgId(Long adOrgId) {
        this.adOrgId = adOrgId;
        return this;
    }

    public Long getcWebposdisId() {
        return cWebposdisId;
    }

    public CWebposdiszditem setcWebposdisId(Long cWebposdisId) {
        this.cWebposdisId = cWebposdisId;
        return this;
    }

    public Long getComparetype() {
        return comparetype;
    }

    public CWebposdiszditem setComparetype(Long comparetype) {
        this.comparetype = comparetype;
        return this;
    }

    public Long getQty() {
        return qty;
    }

    public CWebposdiszditem setQty(Long qty) {
        this.qty = qty;
        return this;
    }

    public BigDecimal getTotAmtActual() {
        return totAmtActual;
    }

    public CWebposdiszditem setTotAmtActual(BigDecimal totAmtActual) {
        this.totAmtActual = totAmtActual;
        return this;
    }

    public String getRelationtype() {
        return relationtype;
    }

    public CWebposdiszditem setRelationtype(String relationtype) {
        this.relationtype = relationtype;
        return this;
    }

    public Long getOwnerid() {
        return ownerid;
    }

    public CWebposdiszditem setOwnerid(Long ownerid) {
        this.ownerid = ownerid;
        return this;
    }

    public Long getModifierid() {
        return modifierid;
    }

    public CWebposdiszditem setModifierid(Long modifierid) {
        this.modifierid = modifierid;
        return this;
    }

    public Date getCreationdate() {
        return creationdate;
    }

    public CWebposdiszditem setCreationdate(Date creationdate) {
        this.creationdate = creationdate;
        return this;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public CWebposdiszditem setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
        return this;
    }

    public String getIsactive() {
        return isactive;
    }

    public CWebposdiszditem setIsactive(String isactive) {
        this.isactive = isactive;
        return this;
    }

    public String getComparetypeamt() {
        return comparetypeamt;
    }

    public CWebposdiszditem setComparetypeamt(String comparetypeamt) {
        this.comparetypeamt = comparetypeamt;
        return this;
    }

    @Override
    public String toString() {
        return "CWebposdiszditemModel{" +
            "id=" + id +
            ", adClientId=" + adClientId +
            ", adOrgId=" + adOrgId +
            ", cWebposdisId=" + cWebposdisId +
            ", comparetype=" + comparetype +
            ", qty=" + qty +
            ", totAmtActual=" + totAmtActual +
            ", relationtype=" + relationtype +
            ", ownerid=" + ownerid +
            ", modifierid=" + modifierid +
            ", creationdate=" + creationdate +
            ", modifieddate=" + modifieddate +
            ", isactive=" + isactive +
            ", comparetypeamt=" + comparetypeamt +
            "}";
    }
}
