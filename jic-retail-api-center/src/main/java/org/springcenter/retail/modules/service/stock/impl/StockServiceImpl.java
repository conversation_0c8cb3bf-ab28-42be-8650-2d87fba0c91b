package org.springcenter.retail.modules.service.stock.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springcenter.retail.modules.entity.eb.StoreNoStock;
import org.springcenter.retail.modules.entity.stock.AllotStorageEntity;
import org.springcenter.retail.modules.entity.stock.EbStorageEntity;
import org.springcenter.retail.modules.entity.stock.EbStorageSkuEntity;
import org.springcenter.retail.modules.entity.stock.ProductStockEntity;
import org.springcenter.retail.modules.exception.RetailException;
import org.springcenter.retail.modules.mapper.bojun.FaStorageMapper;
import org.springcenter.retail.modules.model.bojun.FaStorage;
import org.springcenter.retail.modules.model.bojun.MProductAlias;
import org.springcenter.retail.modules.model.wx.MTransfer;
import org.springcenter.retail.modules.service.base.IMProductAliasService;
import org.springcenter.retail.modules.service.base.IMTransferService;
import org.springcenter.retail.modules.service.stock.IStockService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class StockServiceImpl implements IStockService {

     @Resource
     private FaStorageMapper faStorageMapper;
     @Resource
     private RedisPoolUtil redisPoolUtil;

     @Resource
     private IMProductAliasService imProductAliasService;

     @Resource
     private IMTransferService imTransferService;


     @Override
     public List<StoreNoStock> selectHaveStockStore(List<Long> storeList, List<String> skuList) {
          // 获取安全库存
          String safetyStockStr = RedisTemplateUtil.get(redisPoolUtil, "eb_safety_stock");
          Integer safetyStock = Integer.valueOf(safetyStockStr);
          return this.selectHaveStockStore(storeList,skuList,safetyStock);
     }

     @Override
     public List<StoreNoStock> selectHaveStockStore(List<Long> storeList, List<String> skuList,Integer safetyStock) {
          // 获取安全库存
          List<StoreNoStock> storeNoStocks = faStorageMapper.selectHaveStockStore(storeList, skuList, safetyStock);
          storeNoStocks.forEach(e -> {
               e.setQty(e.getQty()-safetyStock);
          });
          return storeNoStocks;
     }

     @Override
     public List<FaStorage> getStockByStoreAndSkuIds(Long storeId, List<Long> skuIds) {
          return faStorageMapper.getStockByStoreAndSkuIds(storeId,skuIds);
     }

     @Override
     public int releaseOccupiedQty(Long id, Long origQty, Long destQty) {
          return faStorageMapper.releaseOccupiedQty(id,origQty,destQty);
     }

     @Override
     public List<ProductStockEntity> getStorages(List<Long> mProductAliasIds, List<Long> storeIds) {
          return faStorageMapper.getStorageList(mProductAliasIds, storeIds, 0);
     }

     @Override
     public List<ProductStockEntity> getStoragesBySku(List<String> skuList, List<Long> storeIds) {
          return faStorageMapper.getStorageListBySku(skuList, storeIds, 0);
     }


     @Override
     public List<EbStorageSkuEntity> getBoxEbStorageBySkuIds(List<String> skuIds,Integer safetyStock) {
          return faStorageMapper.getBoxEbStorageBySkuIds(skuIds,safetyStock);
     }

     @Override
     public List<EbStorageEntity> getBoxEbStorageBySkuList(List<String> skuList,Integer safetyStock) {
          return faStorageMapper.getBoxEbStorageBySkuList(skuList,safetyStock);
     }

     @Override
     public FaStorage insertFaStorage(Long storeId, Long skuId) {
          MProductAlias mProductAlias = imProductAliasService.getById(skuId);
          if(ObjectUtils.isEmpty(mProductAlias)){
               throw new RetailException("商品库未查询到该商品,清核实");
          }

          FaStorage faStorage = new FaStorage();
          faStorage.setId(faStorageMapper.getMaxId());
          faStorage.setAdClientId(37L);
          faStorage.setAdOrgId(27L);
          faStorage.setOwnerid(893L);
          faStorage.setModifierid(893L);
          faStorage.setCreationdate(new Date());
          faStorage.setModifieddate(new Date());
          faStorage.setIsactive("Y");
          faStorage.setcStoreId(storeId);
          faStorage.setmProductId(mProductAlias.getmProductId());
          faStorage.setmAttributesetinstanceId(mProductAlias.getmAttributesetinstanceId());
          faStorage.setmProductaliasId(skuId);
          faStorage.setQty(0L);
          faStorage.setQtypreout(0L);
          faStorage.setQtyprein(0L);
          faStorage.setQtyvalid(0L);
          faStorage.setQtyOms(0L);
          faStorageMapper.insertSelective(faStorage);
          return faStorage;
     }

     @Override
     public String allAllotsStorage(AllotStorageEntity allotStorageEntity) {
          log.info("库存调拨,入参params = {}", allotStorageEntity.toJson());
          HashMap<String, Object> map = new HashMap<>();
          map.put("In_Json",allotStorageEntity.toJson());
          try {
               // 判断是否已经调拨成功
               QueryWrapper<MTransfer> wrapper = new QueryWrapper<>();
               wrapper.eq("c_orig_id",allotStorageEntity.getOrigStoreId());
               wrapper.eq("c_dest_id",allotStorageEntity.getDestStoreId());
               wrapper.eq("description",allotStorageEntity.getDescription());
               wrapper.eq("wing_docno",allotStorageEntity.getSource());
               wrapper.select("id");
               List<MTransfer> list = imTransferService.list(wrapper);
               if(list.size() > 0){
                    return list.get(0).getId().toString();
               };
               faStorageMapper.boxAllTransferInsertOut(map);
          }catch (RuntimeException e){
               log.error("库存调拨异常 params = {}, error = {}", allotStorageEntity.toJson(), e.getMessage());
               throw new RuntimeException(e.getMessage());
          }
          BigDecimal code = (BigDecimal)map.get("r_code");
          if (code.intValue() != 0) {
               log.error("库存调拨异常 params = {}, error = {}", allotStorageEntity.toJson(), map.get("r_message"));
               throw new RuntimeException(map.get("r_message").toString());
          }
          // 调拨单id
          return String.valueOf(map.get("r_message"));
     }

}
