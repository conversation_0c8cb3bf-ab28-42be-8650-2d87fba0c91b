package org.springcenter.retail.modules.entity.eb;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class JICMapLocationRespEntity implements Serializable {

    @ApiModelProperty("状态码")
    private Integer code;

    @ApiModelProperty("返回消息")
    private String msg;

    @ApiModelProperty("是否成功")
    private boolean success;

    @ApiModelProperty("返回体")
    private Entity1 data;

    @Data
    public class Entity1 {
        private Long precise;
        private Long confidence;
        private Long comprehension;
        private String level;
        private Location location;
    }
    @Data
    public class Location {
        // 经度
        private Double lng;
        // 纬度
        private Double lat;
    }
}
