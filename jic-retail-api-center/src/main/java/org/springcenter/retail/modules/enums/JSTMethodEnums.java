package org.springcenter.retail.modules.enums;

/**
 * <AUTHOR>
 * @date 2023/5/26 17:12
 * @description
 */
public enum JSTMethodEnums {
     UPLOAD("/open/ka/webapi/jiangnanbuyi/aftersalecustome/upload"),
     QUERY("/open/ka/webapi/jiangnanbuyi/aftersalecustome/query"),

     PUSH_ORDER("/open/jushuitan/orders/upload"),

     QUERY_ORDER("/open/orders/single/query"),
     ;

     private String path;

     JSTMethodEnums(String path) {
        this.path = path;
     }

     public String getPath() {
          return this.path;
     }
}
