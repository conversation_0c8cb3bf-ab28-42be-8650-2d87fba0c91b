package org.springcenter.retail.modules.model.bojun;

import java.util.Date;

public class FaStorage {
    private Long id;

    private Long adClientId;

    private Long adOrgId;

    private Long ownerid;

    private Long modifierid;

    private Date creationdate;

    private Date modifieddate;

    private String isactive;

    private Long cStoreId;

    private Long mProductId;

    private Long mAttributesetinstanceId;

    private Long qty;

    private Long qtypreout;

    private Long qtyprein;

    private Long qtyvalid;

    private Long mProductaliasId;

    private Long qtyOms;

    private Long qtycan;

    public Long getQtycan() {
        return qtycan;
    }

    public void setQtycan(Long qtycan) {
        this.qtycan = qtycan;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAdClientId() {
        return adClientId;
    }

    public void setAdClientId(Long adClientId) {
        this.adClientId = adClientId;
    }

    public Long getAdOrgId() {
        return adOrgId;
    }

    public void setAdOrgId(Long adOrgId) {
        this.adOrgId = adOrgId;
    }

    public Long getOwnerid() {
        return ownerid;
    }

    public void setOwnerid(Long ownerid) {
        this.ownerid = ownerid;
    }

    public Long getModifierid() {
        return modifierid;
    }

    public void setModifierid(Long modifierid) {
        this.modifierid = modifierid;
    }

    public Date getCreationdate() {
        return creationdate;
    }

    public void setCreationdate(Date creationdate) {
        this.creationdate = creationdate;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }

    public String getIsactive() {
        return isactive;
    }

    public void setIsactive(String isactive) {
        this.isactive = isactive == null ? null : isactive.trim();
    }

    public Long getcStoreId() {
        return cStoreId;
    }

    public void setcStoreId(Long cStoreId) {
        this.cStoreId = cStoreId;
    }

    public Long getmProductId() {
        return mProductId;
    }

    public void setmProductId(Long mProductId) {
        this.mProductId = mProductId;
    }

    public Long getmAttributesetinstanceId() {
        return mAttributesetinstanceId;
    }

    public void setmAttributesetinstanceId(Long mAttributesetinstanceId) {
        this.mAttributesetinstanceId = mAttributesetinstanceId;
    }

    public Long getQty() {
        return qty;
    }

    public void setQty(Long qty) {
        this.qty = qty;
    }

    public Long getQtypreout() {
        return qtypreout;
    }

    public void setQtypreout(Long qtypreout) {
        this.qtypreout = qtypreout;
    }

    public Long getQtyprein() {
        return qtyprein;
    }

    public void setQtyprein(Long qtyprein) {
        this.qtyprein = qtyprein;
    }

    public Long getQtyvalid() {
        return qtyvalid;
    }

    public void setQtyvalid(Long qtyvalid) {
        this.qtyvalid = qtyvalid;
    }

    public Long getmProductaliasId() {
        return mProductaliasId;
    }

    public void setmProductaliasId(Long mProductaliasId) {
        this.mProductaliasId = mProductaliasId;
    }

    public Long getQtyOms() {
        return qtyOms;
    }

    public void setQtyOms(Long qtyOms) {
        this.qtyOms = qtyOms;
    }
}