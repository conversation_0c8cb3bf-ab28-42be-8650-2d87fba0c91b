<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.retail.modules.mapper.bojun.CWebposdisMapper">

    <select id="getNextId" resultType="java.lang.Long">
        select get_sequences('C_WEBPOSDIS') from dual
    </select>
    <select id="getNextDocNo" resultType="java.lang.String">
        select get_sequenceno('CPD', 37) from dual
    </select>

    <select id="callPolicyAnalysisRefresh" parameterType="Map" statementType="CALLABLE">
        {call policy_analysis_refresh(
                #{p_docno,jdbcType=VARCHAR,mode=IN},
                #{code,jdbcType=DECIMAL,mode=OUT},
                #{msg,jdbcType=VARCHAR,mode=OUT}
              )}
    </select>
</mapper>