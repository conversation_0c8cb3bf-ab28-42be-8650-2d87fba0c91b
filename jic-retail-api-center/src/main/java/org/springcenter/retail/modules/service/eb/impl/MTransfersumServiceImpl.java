package org.springcenter.retail.modules.service.eb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcenter.retail.modules.mapper.bojun.MTransferSumMapper;
import org.springcenter.retail.modules.model.bojun.MTransfersum;
import org.springcenter.retail.modules.service.eb.IMTransfersumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @auther wangchun
 * @create 2022-04-20 10:29:53
 * @describe 服务实现类
 */
@Service
public class MTransfersumServiceImpl extends ServiceImpl<MTransferSumMapper, MTransfersum> implements IMTransfersumService {

     @Autowired
     private MTransferSumMapper mTransferSumMapper;

     @Override
     public Long getMaxId() {
          return mTransferSumMapper.getMaxId();
     }

     @Override
     public String getNextNo() {
          return mTransferSumMapper.getNextNo();
     }
}
