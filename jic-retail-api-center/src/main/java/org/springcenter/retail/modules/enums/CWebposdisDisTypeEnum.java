package org.springcenter.retail.modules.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

public enum CWebposdisDisTypeEnum {

    DZ("1", "打折"),
    TJ ("2", "特价"),
    Y<PERSON> ("3", "优惠"),
    <PERSON><PERSON> ("4", "换购"),
    <PERSON><PERSON><PERSON><PERSON> ("5", "多种折扣"),
    DZYH ("6", "多种优惠"),
    DZTJ ("7", "多种特价"),
    HGYH ("8", "换购优惠"),
    ;

    private static final Map<String, CWebposdisDisTypeEnum> LOOKUP = new HashMap<>();

    static {
        for (CWebposdisDisTypeEnum s : EnumSet.allOf(CWebposdisDisTypeEnum.class)) {
            LOOKUP.put(s.getCode(), s);
        }
    }

    private String code;
    private String name;

    CWebposdisDisTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return this.code;
    }

    public static boolean isDZTJ(String code) {
        return DZTJ.getCode().equals(code);
    }

    public static boolean isHG(String code) {
        return HG.getCode().equals(code);
    }

}
