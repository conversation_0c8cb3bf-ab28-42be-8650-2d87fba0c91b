package org.springcenter.retail.modules.model.wx;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: wangchun
 * @Date: 2024-11-04 14:21:38
 * @Description: 
 */
@TableName("EB_TRANSFERSUM_REQUEST")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="EbTransfersumRequest对象", description="")
public class EbTransfersumRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableField("ID")
    private String id;
    @TableField("SUORCE_NO")
    private String suorceNo;
    @TableField("TYPE")
    private String type;
    @TableField("PARAMS")
    private String params;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;


    public String getId() {
        return id;
    }

    public EbTransfersumRequest setId(String id) {
        this.id = id;
        return this;
    }

    public String getSuorceNo() {
        return suorceNo;
    }

    public EbTransfersumRequest setSuorceNo(String suorceNo) {
        this.suorceNo = suorceNo;
        return this;
    }

    public String getType() {
        return type;
    }

    public EbTransfersumRequest setType(String type) {
        this.type = type;
        return this;
    }

    public String getParams() {
        return params;
    }

    public EbTransfersumRequest setParams(String params) {
        this.params = params;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public EbTransfersumRequest setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public EbTransfersumRequest setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return "EbTransfersumRequestModel{" +
            "id=" + id +
            ", suorceNo=" + suorceNo +
            ", type=" + type +
            ", params=" + params +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            "}";
    }
}
