package org.springcenter.retail.api;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.retail.api.dto.req.EbCreateAssignDTO;
import org.springcenter.retail.api.dto.req.EbCreateDTO;
import org.springcenter.retail.modules.context.eb.CreateMTransferSumAndEbTransferContext;
import org.springcenter.retail.modules.entity.eb.AllBrandStoreNoStock;
import org.springcenter.retail.modules.entity.eb.AllStoreNoStock;
import org.springcenter.retail.modules.entity.eb.TransferSumAllEntity;
import org.springcenter.retail.modules.mapMapper.CreateEbMapper;
import org.springcenter.retail.modules.mapper.bojun.CStoreMapper;
import org.springcenter.retail.modules.model.bojun.CStore;
import org.springcenter.retail.modules.model.bojun.EbTransfersum;
import org.springcenter.retail.modules.model.bojun.MTransfersumUserExpand;
import org.springcenter.retail.modules.service.eb.IBoxEbService;
import org.springcenter.retail.modules.service.eb.IMTransfersumUserExpandService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/retail/test")
@Api(value = "test", tags = "测试接口")
@RefreshScope
public class TestController {

    @Resource
    private IBoxEbService boxEbService;

    @Resource
    private IMTransfersumUserExpandService imTransfersumUserExpandService;

    @Resource
    private CStoreMapper cStoreMapper;

    @Resource
    private RedisPoolUtil redisPoolUtil;

    @ApiOperation("生成发货单")
    @PostMapping(value = "/createEb")
    @ResponseBody
    public ResponseResult<?> createEb() {
        List<AllBrandStoreNoStock> allBrandStoreNoStockList = new ArrayList<>();
        AllStoreNoStock allStoreNoStock = new AllStoreNoStock();
        allStoreNoStock.setCStoreId(427936L);
        List<String> skuList = Collections.singletonList("5L8C1336046504");
        allStoreNoStock.setSkuList(skuList);
        allStoreNoStock.setSkuQty(1);
        AllBrandStoreNoStock allBrandStoreNoStock = new AllBrandStoreNoStock();
        allBrandStoreNoStock.setBrandStoreList(Collections.singletonList(allStoreNoStock));
        allBrandStoreNoStockList.add(allBrandStoreNoStock);
        String description = "box导购调货"+"BOXTEST01";
        Long origId= 421704L;
        MTransfersumUserExpand mTransfersumUserExpand = imTransfersumUserExpandService.getById(959L) ;
        CreateMTransferSumAndEbTransferContext createMTransferSumAndEbTransferContext = boxEbService.packageMTransferSumAndEbTransfer(allBrandStoreNoStockList,description,origId,mTransfersumUserExpand);
        // 设置WMS发货的ebid
        List<Long> storeIds = createMTransferSumAndEbTransferContext.getEbTransfersumList().stream().map(EbTransfersum::getcOrigId).distinct().collect(Collectors.toList());
        List<CStore> cStores = cStoreMapper.selectCStoreByIds(storeIds);
        List<CStore> wmsStores = cStores.stream().filter(e -> "Y".equals(e.getIsCsc()) || "Y".equals(e.getIsWmsstore())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(wmsStores)) {
            List<Long> wmsStoreIds = wmsStores.stream().map(CStore::getId).collect(Collectors.toList());
            createMTransferSumAndEbTransferContext.setWmsStoreIds(wmsStoreIds);
        }
        boxEbService.insertEbData(createMTransferSumAndEbTransferContext);
        return ResponseResult.success();
    }

    @ApiOperation("同步wms发货状态")
    @PostMapping(value = "/syncWmsSendStatus")
    @ResponseBody
    public ResponseResult<?> syncWmsSendStatus() {
        boxEbService.syncWmsSend();
        return ResponseResult.success();
    }

    @ApiOperation("集货加指派")
    @PostMapping(value = "/assignEb")
    @ResponseBody
    public ResponseResult<CreateMTransferSumAndEbTransferContext> assignEb(@Validated @RequestBody EbCreateDTO ebCreateDTO) {
        log.info("接收到大内淘要货参数,params = {}", JSON.toJSONString(ebCreateDTO));
        TransferSumAllEntity transferSumAllEntity = CreateEbMapper.INSTANCE.toEntity(ebCreateDTO);
        CreateMTransferSumAndEbTransferContext eb = boxEbService.createEb(transferSumAllEntity, ebCreateDTO.getFilterStoreId());
        return ResponseResult.success(eb);
    }

    @ApiOperation("要货")
    @PostMapping(value = "/create")
    @ResponseBody
    public ResponseResult<?> create(@Validated @RequestBody EbCreateDTO ebCreateDTO){
        log.info("接收到大内淘要货参数,params = {}", JSON.toJSONString(ebCreateDTO));
        TransferSumAllEntity transferSumAllEntity = CreateEbMapper.INSTANCE.toEntity(ebCreateDTO);
        Long filterStoreId = ebCreateDTO.getFilterStoreId();
        Long type = transferSumAllEntity.getType();
        if(StringUtils.isEmpty(transferSumAllEntity.getSourceSn())){
            transferSumAllEntity.setSourceSn(transferSumAllEntity.getBoxSn());
        }
        // 参数转换
        EbCreateAssignDTO ebCreateAssignDTO = new EbCreateAssignDTO();
        ebCreateAssignDTO.setIfUse(true);
        if(type==10){
            ebCreateAssignDTO.setType(3);
            // 获取安全库存
            String safetyStockStr = RedisTemplateUtil.get(redisPoolUtil, "eb_safety_stock");
            Integer safetyStock = Integer.valueOf(safetyStockStr);
            ebCreateAssignDTO.setSafetyStock(safetyStock);
        }else if(type==20){
            ebCreateAssignDTO.setType(2);
            ebCreateAssignDTO.setSafetyStock(3);
        }else{
            ebCreateAssignDTO.setType(1);
            ebCreateAssignDTO.setStorePkgId(transferSumAllEntity.getStorePackageId());
            ebCreateAssignDTO.setSafetyStock(0);
            ebCreateAssignDTO.setStorePkgType(1);
        }
        transferSumAllEntity.setEbCreateAssignDTO(ebCreateAssignDTO);
        transferSumAllEntity.setIfUseSelfStock(false);
        boxEbService.createEb(transferSumAllEntity, filterStoreId);
        return ResponseResult.success();
    }



    @ApiOperation("发送大内淘发货消息")
    @GetMapping(value = "/sendEbExpressMsg/{ebId}")
    @ResponseBody
    public ResponseResult<?> sendEbExpressMsg(@PathVariable String ebId){
        log.info("发送大内淘发货消息,params = {}", JSON.toJSONString(ebId));

        boxEbService.sendExpressMsg(ebId);

        return ResponseResult.success();
    }
}
