package org.springcenter.retail.api;

import com.alibaba.fastjson.JSON;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.retail.api.dto.req.promotion.PromotionDTO;
import org.springcenter.retail.api.dto.req.promotion.StoreQueryDTO;
import org.springcenter.retail.api.dto.resp.promotion.StoreDTO;
import org.springcenter.retail.api.dto.resp.promotion.SupplierDTO;
import org.springcenter.retail.modules.service.promotion.IPromotionService;
import org.springcenter.retail.modules.util.RedissonUtil;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/bojun/promotion/api")
@Api(value = "促销接口", tags = "促销接口")
@RefreshScope
public class PromotionController {
    @Resource
    private IPromotionService promotionService;
    @Resource
    private RedissonUtil redissonUtil;

    @ApiOperation("提交促销")
    @PostMapping(value = "/submitPromotion")
    @ResponseBody
    public ResponseResult submitPromotion(@RequestBody PromotionDTO promotionDTO) {
        log.info("促销策略 开始处理提交, param={}", JSON.toJSONString(promotionDTO));
        promotionDTO.check();
        String lockKey = "bojun:promotion" + promotionDTO.getId();
        try {
            if (!redissonUtil.tryLock(lockKey)) {
                throw new RuntimeException("当前提交正在被操作中,请勿重复操作!");
            }
            promotionService.savePromotion(promotionDTO);
        } catch (Exception e) {
            log.error("促销策略 处理提交异常, param={}", e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            // 释放锁
            redissonUtil.unlock(lockKey);
        }
        log.info("促销策略 处理提交成功");
        return ResponseResult.success();
    }


    @ApiOperation("查询供应商")
    @PostMapping(value = "/listSupplier")
    @ResponseBody
    public ResponseResult<List<SupplierDTO>> listSupplier(@Validated @RequestBody StoreQueryDTO storeQueryDTO) {
        log.info("查询供应商 入参:{}", JSON.toJSONString(storeQueryDTO));
        Page page = new Page(storeQueryDTO.getPageNo(), storeQueryDTO.getPageSize());
        List<SupplierDTO> rspList = promotionService.listSupplier(storeQueryDTO, page);
        ResponseResult<List<SupplierDTO>> rsp = ResponseResult.success(rspList, page);
        log.info("查询供应商 回参:{}", JSON.toJSONString(rsp));
        return rsp;
    }


    @ApiOperation("查询门店")
    @PostMapping(value = "/listStore")
    @ResponseBody
    public ResponseResult<List<StoreDTO>> listStore(@Validated @RequestBody StoreQueryDTO storeQueryDTO) {
        log.info("查询门店 入参:{}", JSON.toJSONString(storeQueryDTO));
        Page page = new Page(storeQueryDTO.getPageNo(), storeQueryDTO.getPageSize());
        List<StoreDTO> rspList = promotionService.listStore(storeQueryDTO, page);
        ResponseResult<List<StoreDTO>> rsp = ResponseResult.success(rspList, page);
        log.info("查询门店 回参:{}", JSON.toJSONString(rsp));
        return rsp;
    }
}
