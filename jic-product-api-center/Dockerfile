FROM jnbyharbor.jnby.com/base-images/baseimagejdk8:v1.3

MAINTAINER box-group

RUN mkdir -p /jic-product-center/jic-product-api-center

WORKDIR /jic-product-center/jic-product-api-center

EXPOSE 9350
EXPOSE 9999

COPY target/jic-product-api-center.jar jic-product-api-center.jar

ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java", "-Xmx4g", "-Xms4g","-XX:NewRatio=3","-Xss512k", "-Xmn2g","-XX:SurvivorRatio=2", "-XX:+UseParallelGC","-Dreactor.netty.pool.leasingStrategy=lifo", "-jar", "jic-product-api-center.jar"]

CMD ["--spring.profiles.active=prod"]


