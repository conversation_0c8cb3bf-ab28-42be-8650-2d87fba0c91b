package org.springcenter.product.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.modules.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/6/26 10:51
 */
@Slf4j
@RestController
@RequestMapping("/product/fab/api")
@Api(value = "ProductFabApi",tags = "商品fab相关接口")
public class ProductFabApiController {

    @Autowired
    private IProductFabService productFabService;

    @Autowired
    private FabWearingImgInfoService fabWearingImgInfoService;

    @Autowired
    private FabInfoService fabInfoService;

    @Autowired
    private FabVolumeInfoService fabVolumeInfoService;

    @Autowired
    private IProductSizeInfoService productSizeInfoService;

    @Autowired
    private FabAutoNameInfoService fabAutoNameInfoService;

    @ResponseBody
    @PostMapping("/export/fab")
    @ApiOperation(value = "导出fab")
    public ResponseResult<ProductFabResp> exportProductFabInfo(@RequestBody CommonRequest<QueryGoodsFabReq> request) {
        return ResponseResult.success(productFabService.exportProductFabInfo(request.getRequestData(), request.getPage()));
    }


    @ResponseBody
    @PostMapping("/add/single/fab")
    @ApiOperation(value = "添加单独的商品")
    public ResponseResult<ProductSpuFabResp> addSingleFab(@RequestBody CommonRequest<String> request) {
        return ResponseResult.success(productFabService.addSingleFab(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/fab/info")
    @ApiOperation(value = "查看fab详情")
    public ResponseResult<ProductSpuFabResp> queryFabInfo(@RequestBody @Validated CommonRequest<ProductFabInfoReq> request) {
        return ResponseResult.success(productFabService.queryFabInfo(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/fab/imgInfo")
    @ApiOperation(value = "查看fab图片信息详情")
    public ResponseResult<ProductSpuFabImgInfoResp> queryFabImgInfo(@RequestBody @Validated CommonRequest<ProductSpuFabImgInfoReq> request) {
        return ResponseResult.success(productFabService.queryFabImgInfo(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/save/wearing")
    @ApiOperation(value = "保存穿着方式")
    public ResponseResult<Boolean> saveWearing(@RequestBody CommonRequest<AddProductFabWearingReq> request) {
        return ResponseResult.success(fabWearingImgInfoService.saveWearing(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/query/fabric/info")
    @ApiOperation(value = "查询重点面料")
    public ResponseResult<List<FabProductFabricInfoRep>> queryBatchFabricInfo(@RequestBody @Validated CommonRequest<BatchQueryFabInfoReq> request) {
        return ResponseResult.success(productFabService.queryFabricInfo(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/query/pattern/info")
    @ApiOperation(value = "查询重点图案")
    public ResponseResult<List<FabProductPatternInfoRep>> queryBatchPatternInfo(@RequestBody @Validated CommonRequest<BatchQueryFabInfoReq> request) {
        return ResponseResult.success(productFabService.queryBatchPatternInfo(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/query/merchandise/season")
    @ApiOperation(value = "查询商品货季")
    public ResponseResult<List<String>> queryMerchandiseSeason() {
        return ResponseResult.success(productFabService.queryMerchandiseSeason());
    }

    /*@ResponseBody
    @PostMapping("/export/excel/test")
    @ApiOperation(value = "测试导出表格")
    public ResponseResult testExport(@RequestBody CommonRequest<QueryGoodsFabEsReq> request) {
        productFabService.exportProductFabNewArrivalInfo("1", request.getRequestData());
        return ResponseResult.success();
    }*/


    @ResponseBody
    @PostMapping("/save/fab/info")
    @ApiOperation(value = "保存fab信息")
    public ResponseResult<Boolean> saveFabInfo(@RequestBody CommonRequest<AddProductFabInfoReq> request) {
        return ResponseResult.success(fabInfoService.saveFabInfo(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/query/skc/by/fabric/info")
    @ApiOperation(value = "根据重点面料查询skc")
    public ResponseResult<List<FabSkcByParamInfoResp>> querySkcByFabricInfo(@RequestBody @Validated CommonRequest<FabProductFabricInfoRep> request) {
        return ResponseResult.success(productFabService.querySkcByFabricInfo(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/query/skc/by/pattern/info")
    @ApiOperation(value = "根据图案信息查询skc")
    public ResponseResult<List<FabSkcByParamInfoResp>> querySkcByPatternInfo(@RequestBody @Validated CommonRequest<FabProductPatternInfoRep> request) {
        return ResponseResult.success(productFabService.querySkcByPatternInfo(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/query/key/combinations")
    @ApiOperation(value = "根据搭配图查询重点图片")
    public ResponseResult<ProductSpuKeyDpFabResp> queryKeyCombinations(@RequestBody @Validated CommonRequest<ProductSpuKeyDpFabReq> request) {
        return ResponseResult.success(productFabService.queryKeyCombinations(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/save/matching")
    @ApiOperation(value = "保存特殊字段说明")
    public ResponseResult<Boolean> saveMatching(@RequestBody CommonRequest<AddProductFabInfoReq> request) {
        return ResponseResult.success(fabInfoService.saveMatching(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/get/fab/info/by/name")
    @ApiOperation(value = "根据款号查询商品信息")
    public ResponseResult<List<FabInfoByName>> getFabInfoByName(@RequestBody CommonRequest<List<String>> request) {
        return ResponseResult.success(productFabService.getFabInfoByName(request.getRequestData()));
    }

    //--------------------------------- fab 产品册部分-----------------------------------------------

    @ResponseBody
    @PostMapping("/save/fab/volume/info")
    @ApiOperation(value = "保存fab产品册信息")
    public ResponseResult<Boolean> saveFabVolumeInfo(@RequestBody @Validated CommonRequest<AddFabVolumeInfoReq> request) {
        return ResponseResult.success(fabVolumeInfoService.saveFabVolumeInfo(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/fab/volume/info/list")
    @ApiOperation(value = "fab产品册信息列表")
    public ResponseResult<List<QueryFabVolumeInfoResp>> fabVolumeInfoList(@RequestBody @Validated CommonRequest<QueryFabVolumeInfoReq> request) {
        return ResponseResult.success(
                fabVolumeInfoService.fabVolumeInfoList(request.getRequestData(), request.getPage(), request.getComponent()),
                request.getPage());
    }


    @ResponseBody
    @PostMapping("/search/fab/volume/info")
    @ApiOperation(value = "查看fab信息")
    public ResponseResult<FabVolumeInfoDetailResp> searchFabVolumeInfo(@RequestBody @Validated CommonRequest<String> request) {
        return ResponseResult.success(fabVolumeInfoService.searchFabVolumeInfo(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/update/fab/volume/info")
    @ApiOperation(value = "修改fab产品册信息")
    public ResponseResult<Boolean> updateFabVolumeInfo(@RequestBody @Validated CommonRequest<UpdateFabVolumeInfoReq> request) {
        return ResponseResult.success(fabVolumeInfoService.updateFabVolumeInfo(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/set/fab/volume/info/displayTime")
    @ApiOperation(value = "设置时间")
    public ResponseResult<Boolean> setFabVolumeInfoDisplayTime(@RequestBody @Validated CommonRequest<SetFabVolumeInfoDisplayTimeReq> request) {
        return ResponseResult.success(fabVolumeInfoService.setFabVolumeInfoDisplayTime(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/set/fab/volume/info/publish")
    @ApiOperation(value = "立即发布")
    public ResponseResult<Boolean> setFabVolumeInfoPublish(@RequestBody @Validated CommonRequest<SetFabVolumeInfoDisplayTimeReq> request) {
        return ResponseResult.success(fabVolumeInfoService.setFabVolumeInfoPublish(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/delete/fab/volume/info")
    @ApiOperation(value = "删除fab产品册信息")
    public ResponseResult<Boolean> deleteFabVolumeInfo(@RequestBody @Validated CommonRequest<SetFabVolumeInfoDisplayTimeReq> request) {
        return ResponseResult.success(fabVolumeInfoService.deleteFabVolumeInfo(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/query/fab/volume/info/forC")
    @ApiOperation(value = "pos端查询产品册信息")
    public ResponseResult<List<FabVolumeInfoListForC>> queryFabVolumeInfoForC(@RequestBody @Validated CommonRequest<QueryFabVolumeInfoReq> request) {
        return ResponseResult.success(fabVolumeInfoService.queryFabVolumeInfoForC(request.getRequestData(), request.getPage()), request.getPage());
    }

    @ResponseBody
    @PostMapping("/query/fab/volume/info/forTask")
    @ApiOperation(value = "任务中心查询产品册信息")
    public ResponseResult<List<FabVolumeInfoListForC>> queryFabVolumeInfoForTask(@RequestBody @Validated CommonRequest<QueryFabVolumeInfoReq> request) {
        return ResponseResult.success(fabVolumeInfoService.queryFabVolumeInfoForTask(request.getRequestData(), request.getPage()), request.getPage());
    }


    @ResponseBody
    @PostMapping("/query/fab/list/params")
    @ApiOperation(value = "B端查询筛选参数")
    public ResponseResult<List<FabVolumeInfoParam>> queryFabVolumeParams() {
        return ResponseResult.success(fabVolumeInfoService.queryFabVolumeParams());
    }

    @ResponseBody
    @PostMapping("/judge/create/fab")
    @ApiOperation(value = "查询当前是否能创建产品册 true可以 false不可以")
    public ResponseResult<Boolean> judgeCreateFab(@RequestBody @Validated CommonRequest<JudgeCreateFabReq> request) {
        return ResponseResult.success(fabVolumeInfoService.judgeCreateFab(request.getRequestData()));
    }

    //-----------------------------违禁词---------------------------------------------------------------------------------

    @ResponseBody
    @PostMapping("/query/fab/prohibited/words")
    @ApiOperation(value = "fab违禁词查询")
    public ResponseResult<List<String>> queryFabProhibitedWords() {
        return ResponseResult.success(fabVolumeInfoService.queryFabProhibitedWords());
    }

    //---------------------------自动品名--------------------------------------------------------------------------
    @ResponseBody
    @PostMapping("/query/fab/auto/name")
    @ApiOperation(value = "fab查询自动品名 不传参是全部，传参按照款号,分割")
    public ResponseResult<Boolean> queryFabProhibitedWords(@RequestBody CommonRequest<String> request) {
        fabAutoNameInfoService.generateAutoName(request.getRequestData());
        return ResponseResult.success(true);
    }


    //---------------------------------尺码信息-----------------------------------------------------
    /*@ResponseBody
    @PostMapping("/query/fab/size/info")
    @ApiOperation(value = "查询商品的尺码信息")
    public ResponseResult<List<ProductSizeInfoResp>> queryFabSizeInfo(@RequestBody @Validated CommonRequest<ProductFabInfoReq> request) {
        return ResponseResult.success(productSizeInfoService.queryFabSizeInfo(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/export/size/info/data")
    @ApiOperation(value = "导出尺码信息数据")
    public ResponseResult<Boolean> exportSizeInfoData(@RequestBody @Validated CommonRequest<ExportSizeInfoDataReq> request) {
        productSizeInfoService.exportSizeInfoData("1", request.getRequestData());
        return ResponseResult.success(true);
    }*/


    @ResponseBody
    @PostMapping("/query/merchandise/people")
    @ApiOperation(value = "查询工艺师或者设计师 查询工艺师入参为gys 设计师为designer")
    public ResponseResult<List<String>> queryMerchandisePeople(@RequestBody CommonRequest<String> request) {
        if (StringUtils.isBlank(request.getRequestData())) {
            throw new RuntimeException("参数不能为空");
        }
        return ResponseResult.success(productFabService.queryMerchandiseByParam(request.getRequestData()));
    }
}
