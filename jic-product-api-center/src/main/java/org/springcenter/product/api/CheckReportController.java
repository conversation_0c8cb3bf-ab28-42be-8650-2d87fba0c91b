package org.springcenter.product.api;

import com.alibaba.fastjson.JSONObject;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.aop.annotation.NoLogging;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.modules.model.CheckReport;
import org.springcenter.product.modules.service.CheckReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("checkReport/api")
@Api(value = "checkReportController",tags = "检测报告")
@RefreshScope
public class CheckReportController {

    @Autowired
    private CheckReportService checkReportService;

    @Value("${checkReport.condition}")
    private String condition;



    @ApiOperation(value = "获取检测报告列表")
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    @ResponseBody
    @NoLogging
    public ResponseResult<List<CheckReport>> list(@RequestBody CommonRequest<CheckReportReq> request){
        Page page = request.getPage();
        return ResponseResult.success(checkReportService.list(request.getRequestData(),page),page);
    }



    @ApiOperation(value = "编辑检测报告   可修改字段为 ： 报告类型， 报告货季  ，报告状态， 面料/成衣号  ， 是否删除")
    @RequestMapping(value = "/update",method = RequestMethod.POST)
    @ResponseBody
    @NoLogging
    public ResponseResult<Boolean> update(@RequestBody CommonRequest<CheckReportReq> request){
        return ResponseResult.success(checkReportService.update(request.getRequestData()));
    }


    // 根据商品款号查询  面料
    @ApiOperation(value = "根据款号查询 前4个面料编号  和 前4个 成衣编号  按照创建时间倒叙 ， 请求参数直接放在req里面 是款号")
    @RequestMapping(value = "/getReportCode",method = RequestMethod.POST)
    @ResponseBody
    @NoLogging
    public ResponseResult<CheckReportDataResp> getReportCode(@RequestBody CommonRequest<String> request){
        return ResponseResult.success(checkReportService.getReportCode(request.getRequestData(),null, null));
    }


    @ApiOperation(value = "面料  横机  需要根据款号+ 货季去查询" +
            "面料  非横机    根据面料号去查询")
    @RequestMapping(value = "/getReportCodeBySeason",method = RequestMethod.POST)
    @ResponseBody
    @NoLogging
    public ResponseResult<CheckReportDataResp> getReportCodeBySeason(@RequestBody CommonRequest<ReportCodeBySeasonReq> request){
        return ResponseResult.success(checkReportService.getReportCode(request.getRequestData().getProductCode(),request.getRequestData().getSeason(),request.getRequestData().getFabirc()));
    }



    @ApiOperation(value = "获取预览地址")
    @RequestMapping(value = "/getPdfViewUrl",method = RequestMethod.POST)
    @ResponseBody
    @NoLogging
    public ResponseResult<String> getPdfViewUrl(@RequestBody CommonRequest<String> commonRequest){
        return ResponseResult.success(checkReportService.getPdfViewUrl(commonRequest.getRequestData()));
    }


    @ApiOperation(value = "下载 获取文件流")
    @RequestMapping(value = "/download",method = RequestMethod.GET)
    @ResponseBody
    @NoLogging
    public ResponseResult<String> download(@RequestParam(value = "id",required = true) String id, HttpServletResponse response){
        String download = checkReportService.download(id);
        return ResponseResult.success(download);
    }



    @ApiOperation(value = "获取code 和 名称 加载条件")
    @RequestMapping(value = "/getCondition",method = RequestMethod.GET)
    @ResponseBody
    @NoLogging
    public ResponseResult<List<GetCondition>> getCondition(){
        return ResponseResult.success(JSONObject.parseArray(condition,GetCondition.class));
    }
}
