package org.springcenter.product.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.dto.ExportSizeInfoDataReq;
import org.springcenter.product.api.dto.QueryGoodsFabEsReq;
import org.springcenter.product.modules.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Description:监听需要解析的文件
 * @Author: brian
 * @Date: 2021/8/30 15:09
 */
@Component
@Slf4j
public class FileAnalysisListener implements IMessageListener {

    @Value("${mq.upload.analysis.topic}")
    private String topic;

    @Value("${mq.upload.analysis.tags}")
    private String tags;

    @Autowired
    private IProductLabelService productLabelService;

    @Autowired
    private IProductService iProductService;

    @Autowired
    private RedisPoolUtil redisPoolUtil;

    @Autowired
    private IProductVersionService iProductVersionService;

    @Autowired
    private IProductFabService productFabService;

    @Autowired
    private CheckReportService checkReportService;

    @Autowired
    private IProductSizeInfoService productSizeInfoService;

    @Override
    public String getTopic() {
        return topic;
    }

    @Override
    public String getTags() {
        return tags;
    }

    private static final String PRODUCT_VERSION_TAG = "9";

    private static final String PRODUCT_NEW_ARRIVAL_TAG = "PRODUCT_NEW_ARRIVAL_TAG";

    private static final String PRODUCT_UPDATE_LABEL_TAG = "PRODUCT_UPDATE_LABEL_TAG";


    // 检测报告
    private static final String CHECK_REPORT_TAG = "CHECK_REPORT_TAG";

    // 成衣尺码导出
    private static final String FAB_SIZE_INFO_TAG = "FAB_SIZE_INFO_TAG";

    @Override
    public ConsumeConcurrentlyStatus consume(Message msg) {
        String keys = msg.getKeys();
        String msgTags = msg.getTags();
        List<String> confTags = Arrays.asList(tags.split(","));

        if (!confTags.contains(msgTags)){
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        String para = null;
        try {
            para = new String(msg.getBody(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(para);
            if("1".equals(msg.getTags())){
                String url = jsonObject.getString("url");
                productLabelService.downFileTransferMarkLabel(url, keys);
            } else if (PRODUCT_VERSION_TAG.equals(msg.getTags())) {
                String url = jsonObject.getString("url");
                // 0：新增 1：覆盖
                String type = jsonObject.getString("outId");
                log.info("============版型库导入，监听到参数outId:{}, 链接：{}", type, url);
                if (Objects.equals(type, "0")) {
                    iProductVersionService.downFileForProductVersion(url, keys);
                } else if (Objects.equals(type, "1")) {
                    iProductVersionService.downFileForUpdateProductVersion(url, keys);
                }

            } /*else if (Objects.equals(PRODUCT_NEW_ARRIVAL_TAG, msg.getTags())) {
                // 导出商品的上新表格
                *//*String param = jsonObject.getString("outId");*//*
                log.info("导出上新表格入参：{}", jsonObject);
                QueryGoodsFabEsReq req = JSONObject.parseObject(String.valueOf(jsonObject), QueryGoodsFabEsReq.class);
                productFabService.exportProductFabNewArrivalInfo(keys, req);
            }*/ else if(CHECK_REPORT_TAG.equals(msg.getTags())){
                String url = jsonObject.getString("url");
                String message = checkReportService.importCheckReport(url);
                // 返回的url数据
                RedisTemplateUtil.setex(redisPoolUtil, keys, message, 600);
            } /*else if (Objects.equals(FAB_SIZE_INFO_TAG, msg.getTags())) {
                log.info("========导出尺码表入参：{}", jsonObject);
                ExportSizeInfoDataReq req = JSONObject.parseObject(String.valueOf(jsonObject), ExportSizeInfoDataReq.class);
                productSizeInfoService.exportSizeInfoData(keys, req);
            }*/

        } catch (Exception e) {
            log.error("监听上传下载文件处理异常,tags={},param={}",msg.getTags(),para,e);
            if(!RedisTemplateUtil.exists(redisPoolUtil,keys+"error")) {
                // 缓存1分钟
                RedisTemplateUtil.setex(redisPoolUtil, keys + "error", msg.getBody()+"文件处理异常", 60);
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
