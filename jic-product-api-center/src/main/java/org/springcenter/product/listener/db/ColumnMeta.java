package org.springcenter.product.listener.db;

import com.jnby.common.RemotingSerializable;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * 代表一个字段的信息
 *
 * <AUTHOR> 2013-9-3 下午2:46:32
 * @since 3.0.0
 */
public class ColumnMeta extends RemotingSerializable implements Serializable {

    private String name;
    private int    type;

    public ColumnMeta(String name, int type){
        this.name = StringUtils.upperCase(name);// 统一为大写
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public int getType() {
        return type;
    }

    public void setName(String name) {
        this.name = StringUtils.upperCase(name);
    }

    public void setType(int type) {
        this.type = type;
    }

    public ColumnMeta clone() {
        return new ColumnMeta(this.name, this.type);
    }

    @Override
    public String toString() {
        return "ColumnMeta{" +
                "name='" + name + '\'' +
                ", type=" + type +
                '}';
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((name == null) ? 0 : name.hashCode());
        result = prime * result + type;
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;
        ColumnMeta other = (ColumnMeta) obj;
        if (name == null) {
            if (other.name != null) return false;
        } else if (!name.equals(other.name)) return false;
        if (type != other.type) return false;
        return true;
    }

}
