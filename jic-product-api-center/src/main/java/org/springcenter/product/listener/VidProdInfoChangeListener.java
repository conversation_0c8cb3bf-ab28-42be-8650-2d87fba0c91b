package org.springcenter.product.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.Message;
import org.springcenter.product.listener.db.ColumnValue;
import org.springcenter.product.listener.db.StringColumnValue;
import org.springcenter.product.listener.db.StringTable;
import org.springcenter.product.modules.service.IProductStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2023/8/9 9:47
 */
@Component
@Slf4j
@RefreshScope
public class VidProdInfoChangeListener {

    @Autowired
    private IProductStoreService productStoreService;

    public ConsumeConcurrentlyStatus consume(Message msg) {
        if (msg.getTags().equals("JIC_PRODUCT_CANSELL_MSG_LOG")){
            try {
                log.info("JIC_PRODUCT_CANSELL_MSG_LOG msg.getBody()监听变化:{}", JSONObject.toJSONString(new String(msg.getBody(), "UTF-8")));
                String obj = new String(msg.getBody(), "UTF-8");
                log.info("JIC_PRODUCT_CANSELL_MSG_LOG obj监听变化:{}", JSONObject.toJSONString(obj));

                StringTable table = StringTable.fromJson(obj, StringTable.class);
                String brandId = (String)table.getColumnByName("BRANDID").getValue();
                String params = (String)table.getColumnByName("PARAMS").getValue();

                // 修改可售 不可售逻辑
                List<StringColumnValue> primaryKeys = table.getPrimaryKeys();
                StringColumnValue columnValue = primaryKeys.get(0);
                Long isCanSellId = Long.valueOf((String) columnValue.getValue());
                log.info("JIC_PRODUCT_CANSELL_MSG_LOG 监听是否可售入参Id：{}, brandId{}, params：{} ", isCanSellId, brandId, params);
                productStoreService.changeVidProductIsCanSell(isCanSellId, brandId, params);
            } catch (Exception e) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

        } else if (msg.getTags().equals("JIC_PRODUCT_PUTAWAY_MSG_LOG")){
            try {
                log.info("JIC_PRODUCT_PUTAWAY_MSG_LOG msg.getBody()监听变化:{}", JSONObject.toJSONString(new String(msg.getBody(), "UTF-8")));
                String obj = new String(msg.getBody(), "UTF-8");
                log.info("JIC_PRODUCT_PUTAWAY_MSG_LOG obj监听变化:{}", JSONObject.toJSONString(obj));

                StringTable table = StringTable.fromJson(obj, StringTable.class);
                String brandId = (String)table.getColumnByName("BRANDID").getValue();
                String params = (String)table.getColumnByName("PARAMS").getValue();
                String event = (String)table.getColumnByName("EVENT").getValue();
                if (Objects.equals(event, "onlineStatusUpdate") || Objects.equals(event, "goodsStoreRelationUpdate")) {
                    // 更新库存
                    List<StringColumnValue> primaryKeys = table.getPrimaryKeys();
                    StringColumnValue columnValue = primaryKeys.get(0);
                    Long isPutaway = Long.valueOf((String) columnValue.getValue());
                    log.info("JIC_PRODUCT_PUTAWAY_MSG_LOG 监听商品是否上架入参Id：{}, brandId{}, params：{} ", isPutaway, brandId, params);
                    productStoreService.changeVidProductIsPutaway(isPutaway, brandId, params, event);
                }
            } catch (Exception e) {
                log.error("=================监听报错：{}", e);
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
