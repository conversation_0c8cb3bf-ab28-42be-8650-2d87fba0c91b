package org.springcenter.product.listener.db;

import com.google.common.collect.Lists;
import com.jnby.common.RemotingSerializable;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/8/30 18:25
 */
public class StringTable extends RemotingSerializable implements Serializable {

    private String            schemaName;
    private String            tableName;
    private List<StringColumnValue> primaryKeys = Lists.newArrayList();
    private List<StringColumnValue> columns     = Lists.newArrayList();

    public StringTable(){

    }

    public StringTable(String schemaName, String tableName, List<StringColumnValue> primaryKeys, List<StringColumnValue> columns){
        this.schemaName = schemaName;
        this.tableName = tableName;
        this.primaryKeys = primaryKeys;
        this.columns = columns;
    }

    public List<StringColumnValue> getPrimaryKeys() {
        return primaryKeys;
    }

    public void setPrimaryKeys(List<StringColumnValue> primaryKeys) {
        this.primaryKeys = primaryKeys;
    }

    public List<StringColumnValue> getColumns() {
        return columns;
    }


    /**
     * 根据列名查找对应的字段信息(包括主键中的字段)
     */
    public StringColumnValue getColumnByName(String columnName) {
        return getColumnByName(columnName, false);
    }

    /**
     * 根据列名查找对应的字段信息(包括主键中的字段)
     */
    public StringColumnValue getColumnByName(String columnName, boolean returnNullNotExist) {
        for (StringColumnValue column : columns) {
            if (column.getColumn().getName().equalsIgnoreCase(columnName)) {
                return column;
            }
        }

        for (StringColumnValue pk : primaryKeys) {
            if (pk.getColumn().getName().equalsIgnoreCase(columnName)) {
                return pk;
            }
        }

        return null;
    }

    /**
     * 根据列名删除对应的字段信息(包括主键中的字段)
     */
    public StringColumnValue removeColumnByName(String columnName) {
        return removeColumnByName(columnName, false);
    }

    /**
     * 根据列名删除对应的字段信息(包括主键中的字段)
     */
    public StringColumnValue removeColumnByName(String columnName, boolean returnNullNotExist) {
        StringColumnValue remove = null;
        for (StringColumnValue pk : primaryKeys) {
            if (pk.getColumn().getName().equalsIgnoreCase(columnName)) {
                remove = pk;
                break;
            }
        }

        if (remove != null && this.primaryKeys.remove(remove)) {
            return remove;
        } else {
            for (StringColumnValue column : columns) {
                if (column.getColumn().getName().equalsIgnoreCase(columnName)) {
                    remove = column;
                    break;
                }
            }

            if (remove != null && this.columns.remove(remove)) {
                return remove;
            }
        }

        return null;
    }

    public void setColumns(List<StringColumnValue> columns) {
        this.columns = columns;
    }

    public String getSchemaName() {
        return schemaName;
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((columns == null) ? 0 : columns.hashCode());
        result = prime * result + ((primaryKeys == null) ? 0 : primaryKeys.hashCode());
        result = prime * result + ((schemaName == null) ? 0 : schemaName.hashCode());
        result = prime * result + ((tableName == null) ? 0 : tableName.hashCode());
        return result;
    }

    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;
        StringTable other = (StringTable) obj;
        if (columns == null) {
            if (other.columns != null) return false;
        } else if (!columns.equals(other.columns)) return false;
        if (primaryKeys == null) {
            if (other.primaryKeys != null) return false;
        } else if (!primaryKeys.equals(other.primaryKeys)) return false;
        if (schemaName == null) {
            if (other.schemaName != null) return false;
        } else if (!schemaName.equals(other.schemaName)) return false;
        if (tableName == null) {
            if (other.tableName != null) return false;
        } else if (!tableName.equals(other.tableName)) return false;
        return true;
    }

    @Override
    public String toString() {
        return "Table{" +
                "schemaName='" + schemaName + '\'' +
                ", tableName='" + tableName + '\'' +
                ", primaryKeys=" + primaryKeys +
                ", columns=" + columns +
                '}';
    }

}
