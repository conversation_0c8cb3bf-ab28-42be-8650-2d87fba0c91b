package org.springcenter.product.listener;


import org.springcenter.product.listener.db.ColumnValue;
import org.springcenter.product.listener.db.Table;
import org.springcenter.product.modules.service.IProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 15:59
 */
@Component
@Slf4j
@RefreshScope
public class StorageChangeListener {
    @Autowired
    private IProductService iProductService;

    public ConsumeConcurrentlyStatus consume(Message msg) {
        if (msg.getTags().equals("FA_STORAGE")){
            try {
                String obj = new String(msg.getBody(), "UTF-8");
                Table table = Table.fromJson(obj, Table.class);
                Integer storeId = (Integer)table.getColumnByName("C_STORE_ID").getValue();
                Integer productId = (Integer)table.getColumnByName("M_PRODUCT_ID").getValue();
                // 更新库存
                List<ColumnValue> primaryKeys = table.getPrimaryKeys();
                ColumnValue columnValue = primaryKeys.get(0);
                Integer faStoreId = (Integer)columnValue.getValue();
                iProductService.changeStoreGoodsByListenerStorage(storeId.longValue(), productId.longValue(),faStoreId.longValue());
            } catch (Exception e) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
