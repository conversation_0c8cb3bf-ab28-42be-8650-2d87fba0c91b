package org.springcenter.product;


import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.modules.util.oConvertUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 微服务启动类（采用此类启动项目为微服务模式）
 */
//@Slf4j
//@SpringBootApplication(scanBasePackages = {"org.springcenter.product.modules.*","com.jnby"}
//    )
//@EnableDiscoveryClient
//@EnableFeignClients(basePackages = {"org.springcenter.product","com.jnby"})
//@ComponentScan(excludeFilters = {@ComponentScan.Filter(type = FilterType.REGEX,pattern = "com.jnbyframework.boot.common.aspect.*")},
//        basePackages = {"springfox.documentation.spring.web.scanners", "springfox.documentation.spring.web.readers.operation", "springfox.documentation.spring.web.readers.parameter", "springfox.documentation.spring.web.plugins", "springfox.documentation.spring.web.paths"}
//)
@Slf4j
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages =  {"org.springcenter.product","com.jnby"})
public class JicProductCenterApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(JicProductCenterApplication.class);
    }

    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(JicProductCenterApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String path = oConvertUtils.getString(env.getProperty("server.servlet.context-path"));
        log.info("\n----------------------------------------------------------\n\t" +
                "Application JnbyProductCenterApplication is running! Access URLs:\n\t" +
                "Local: \t\thttp://localhost:" + port + path + "/swagger-ui.html\n" +
                "External: \thttp://" + ip + ":" + port + path + "/swagger-ui.html\n" +
                "Swagger文档: \thttp://" + ip + ":" + port + path + "/swagger-ui.html\n" +
                "----------------------------------------------------------");

    }

}
