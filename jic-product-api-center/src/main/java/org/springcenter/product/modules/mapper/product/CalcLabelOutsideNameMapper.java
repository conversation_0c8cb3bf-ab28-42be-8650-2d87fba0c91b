package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springcenter.product.modules.model.CalcLabelOutsideName;
import org.springcenter.product.modules.model.FabAnDynamicFieldSetting;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/12/8 11:05
 */
public interface CalcLabelOutsideNameMapper extends BaseMapper<CalcLabelOutsideName> {
    List<CalcLabelOutsideName> selectOutsideNameList();


    Long getLabelSettingValueId();
}
