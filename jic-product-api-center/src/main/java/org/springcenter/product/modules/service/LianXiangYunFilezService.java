package org.springcenter.product.modules.service;


import org.springcenter.product.api.lenovo.LenovoFileReq;
import org.springcenter.product.api.lenovo.LenovoFileResp;

public interface LianXiangYunFilezService {


    /**
     *  获取accesstoken  加锁 redis 缓存
     * @return
     */
    public String getAccessToken();


    /**
     *  根据配置的路径  去获取路径中的所有文件信息
     */
    public LenovoFileResp getLenovoFile(LenovoFileReq lenovoFileReq);

    /**
     * 获取预览
     * @return
     */
    String getPdfView(String neid,String nsid);

    String download(String neid, String nsid);
}
