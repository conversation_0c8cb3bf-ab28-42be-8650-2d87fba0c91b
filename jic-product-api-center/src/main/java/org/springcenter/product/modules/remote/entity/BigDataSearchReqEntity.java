package org.springcenter.product.modules.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/4/25 17:40
 */
@Data
public class BigDataSearchReqEntity {

    @ApiModelProperty(value = "渠道")
    private Integer channel_dict;

    @ApiModelProperty(value = "品牌")
    // 目前 all samo lasumin 无差别
    private List<String> brand;

    @ApiModelProperty(value = "品牌 商品门店为集合/奥莱店且需要筛选子品牌时通过此参数过滤品牌,默认不过滤")
    private List<String> subBrand;

    @ApiModelProperty(value = "看业务方传什么 需要pos-ai自己传")
    // 大数据逻辑 优先weid 无 再取brand
    private String weid;

    @ApiModelProperty(value = "商品门店为集合/奥莱店且需要筛选子品牌时通过此参数过滤品牌,默认不过滤")
    private List<String> subBrandId;

    @ApiModelProperty(value = "搜索信息")
    private String query;

    @ApiModelProperty(value = "pageNo")
    private Integer list_start;

    @ApiModelProperty(value = "pageSize")
    private Integer list_size;

    @ApiModelProperty(value = "唯一id")
    private String requestId;

    @ApiModelProperty(value = "用户集团ID")
    private String unionID;

    @ApiModelProperty(value = "微商城门店ID")
    private String mallStoreId;

    @ApiModelProperty(value = "微商城商品分类页ID")
    private List<String> classifyId;

    @ApiModelProperty(value = "normalSearch:正常搜索， globalSearchFallback:通过全局搜索标识进入搜索， contentSearch:内容搜索调用")
    // normalSearch: 基于关键词的正常搜索
    // functionSearch: 微商城功能性搜索(如：获取尺码、色系、全店搜索按钮展示、风格、面料、品牌、价格区间等需要进行页面筛选项的展示的功能模块)
    // clothesChange: 算法内部调用 > AI试衣
    // otherSearch: 其他
    // 第一期先走默认
    private String requestSource = "normalSearch";

    private Boolean size_search = false;

    private String traceId;

    @ApiModelProperty(value = "商品搜索来源(如果有新接入的需求请先联系确认)")
    // productListPage: 微商城 > 商品列表搜索
    // searchNULLRec: 微商城 > 商品列表搜索 > （搜索为空时）为您推荐
    // couponPage: 微商城 > 优惠券页面搜索
    // contentSearch: 算法内部调用 > 内容社区搜素
    // posAiAssistant: POS+ > AI助手 > 推荐单品 > 推荐标签词 > 搜索商品
    private String requestScenes;

    private String wid;

    /*@ApiModelProperty(value = "唯一id")
    private String requestSource;*/
}
