package org.springcenter.product.modules.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springcenter.product.api.dto.DisassociateSceneLabelsReq;
import org.springcenter.product.api.dto.UpdateLabelSceneReq;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.api.enums.SceneLabelEnum;
import org.springcenter.product.api.enums.SceneLabelLevelEnum;
import org.springcenter.product.modules.mapper.product.ProductSceneLabelLogMapper;
import org.springcenter.product.modules.mapper.product.ProductSceneLabelMapper;
import org.springcenter.product.modules.mapper.product.SceneChannelConfigMapper;
import org.springcenter.product.modules.mapper.product.SceneLabelConnectionMapper;
import org.springcenter.product.modules.model.*;
import org.springcenter.product.modules.repository.IProductSceneTagRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/19 10:56
 */
@Repository
public class ProductSceneTagRepositoryImpl implements IProductSceneTagRepository {

    @Autowired
    private SceneLabelConnectionMapper sceneLabelConnectionMapper;

    @Autowired
    private SceneChannelConfigMapper sceneChannelConfigMapper;

    @Autowired
    private ProductSceneLabelMapper productSceneLabelMapper;

    @Autowired
    private ProductSceneLabelLogMapper productSceneLabelLogMapper;


    @Override
    public void saveLabelSceneConnections(List<SceneLabelConnection> connections) {
        sceneLabelConnectionMapper.batchInsert(connections);
    }

    @Override
    public List<SceneChannelConfig> querySceneChannelConfig() {
        QueryWrapper<SceneChannelConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        return sceneChannelConfigMapper.selectList(queryWrapper);
    }

    @Override
    public SceneChannelConfig querySceneChannelConfigById(Integer id) {
        QueryWrapper<SceneChannelConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("ID", id);
        return sceneChannelConfigMapper.selectOne(queryWrapper);
    }

    @Override
    public List<ProductSceneLabel> queryLabelSceneByChannelId(Integer channelId) {
        QueryWrapper<ProductSceneLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("OWN_CHANNEL_ID", channelId);
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("STATUS", SceneLabelEnum.OPEN.getCode());
        return productSceneLabelMapper.selectList(queryWrapper);
    }

    @Override
    public void saveProductSceneLabel(ProductSceneLabel sceneLabel) {
        productSceneLabelMapper.insert(sceneLabel);
    }

    @Override
    public void saveProductSceneLabelLog(ProductSceneLabelLog sceneLabelLog) {
        productSceneLabelLogMapper.insert(sceneLabelLog);
    }

    @Override
    public ProductSceneLabel queryProductSceneLabel(String id) {
        QueryWrapper<ProductSceneLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ID", id);
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        return productSceneLabelMapper.selectOne(queryWrapper);
    }

    @Override
    public void updateProductSceneLabel(ProductSceneLabel productLabelScene) {
        productSceneLabelMapper.updateById(productLabelScene);
    }

    @Override
    public List<SceneLabelConnection> querySceneLabelConnectionBySceneId(String id) {
        QueryWrapper<SceneLabelConnection> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("SCENE_ID", id);
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        return sceneLabelConnectionMapper.selectListBySceneIs(id);
    }

    @Override
    public SceneLabelConnection querySceneLabelConnectionById(String id) {
        QueryWrapper<SceneLabelConnection> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ID", id);
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        return sceneLabelConnectionMapper.selectOne(queryWrapper);
    }

    @Override
    public void updateSceneConfigConnection(String name, List<DisassociateSceneLabelsReq> list) {
        sceneLabelConnectionMapper.batchUpdateById(name, list);
    }

    @Override
    public List<ProductSceneLabel> selectSceneListByParam(String sceneName, Integer ownChannelId) {
        return productSceneLabelMapper.selectByParam(sceneName, ownChannelId);
    }

    @Override
    public void updateDisRelLabelCodeByLabelCode(String labelCode, String name) {
        sceneLabelConnectionMapper.updateDisRelLabelCodeByLabelCode(labelCode, name);
    }

    @Override
    public List<SceneLabelConnection> selectSceneByLabelCode(String labelCode) {
        return sceneLabelConnectionMapper.selectSceneByLabelCode(labelCode);
    }

    @Override
    public List<ProductSceneLabelEntity> selectSceneLabelById(List<String> sceneIds) {
        return productSceneLabelMapper.selectSceneLabelEntityByIds(sceneIds);
    }

    @Override
    public void updateLabelSceneConnections(List<UpdateLabelSceneReq.updateDataEntity> updateList, String finalName) {
        sceneLabelConnectionMapper.batchUpdate(updateList, finalName);
    }

    @Override
    public List<SceneLabelConnection> querySceneLabelConnectionBySceneKey(String key) {
        return sceneLabelConnectionMapper.querySceneLabelConnectionBySceneKey(key);
    }
}
