<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.JicMProductAttrMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.JicMProductAttr">
        <id property="id" column="ID" jdbcType="DECIMAL"/>
        <result property="productCode" column="PRODUCT_CODE" jdbcType="VARCHAR"/>
        <result property="productId" column="PRODUCT_ID" jdbcType="DECIMAL"/>
        <result property="gbCode" column="GB_CODE" jdbcType="VARCHAR"/>
        <result property="colorId" column="COLOR_ID" jdbcType="VARCHAR"/>
        <result property="colorCode" column="COLOR_CODE" jdbcType="VARCHAR"/>
        <result property="colorName" column="COLOR_NAME" jdbcType="VARCHAR"/>
        <result property="sizeId" column="SIZE_ID" jdbcType="VARCHAR"/>
        <result property="sizeCode" column="SIZE_CODE" jdbcType="VARCHAR"/>
        <result property="sizeName" column="SIZE_NAME" jdbcType="VARCHAR"/>
        <result property="sizeDescription" column="SIZE_DESCRIPTION" jdbcType="VARCHAR"/>
        <result property="productNo" column="PRODUCT_NO" jdbcType="VARCHAR"/>
        <result property="price" column="PRICE" jdbcType="DECIMAL"/>
        <result property="image" column="IMAGE" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PRODUCT_CODE,PRODUCT_ID,
        GB_CODE,COLOR_ID,COLOR_CODE,
        COLOR_NAME,SIZE_ID,SIZE_CODE,
        SIZE_NAME,SIZE_DESCRIPTION,PRODUCT_NO,
        PRICE,IMAGE,CREATE_TIME,
        UPDATE_TIME
    </sql>


    <select id="selectSkuIdByWeiMenInfo" resultType="org.springcenter.product.api.dto.ProductSkuIdByWeiMenInfoResp">
        <foreach collection="skus" item="list" open="(" close=")" separator="union all">
            select
            a.ID as skuId, b.WEIMO_SKUID as weiMenSkuId, b.PRODUCT_NO as productNo, b.WEID as weId
            from userwx.JIC_M_PRODUCT_ATTR a left join userwx.view_JIC_PRODUCT_MALL_RELATION b
            on a.PRODUCT_CODE = b.PRODUCT_CODE
            where
            b.WEID = #{list.weId}
            and b.WEIMO_GOODID = #{list.weiMenGoodId}
            and b.WEIMO_SKUID = #{list.weiMenSkuId}
            and b.PRODUCT_CODE = #{list.productCode}
        </foreach>
    </select>

    <select id="searchProductByWeiMenInfo" resultType="org.springcenter.product.api.dto.ProductByWeiMenInfoResp">
        <foreach collection="skus" item="list" open="(" close=")" separator="union all">
            select
            d.TYPE as type,
            c.PRODUCT_NO as productNo, c.PRODUCT_NAME as productName, c.BRAND_ID as weId, c.BRAND_NAME as weIdName,
            c.year as year, c.BIG_SEASON_ID as bigSeasonId, c.BIG_SEASON as bigSeason, c.SEASON_ID as seasonId,
            c.season as season, c.SMALL_SEASON_ID as smallSeasonId, c.SMALL_SEASON as smallSeason,
            c.BIG_CLASS_ID as bigClassId, c.BIG_CLASS as bigClass, c.SMALL_CLASS_ID as smallClassId,
            c.SMALL_CLASS as smallClass, c.BAND_ID as bandId, c.BAND as band, c.TOPIC_ID as topicId,
            c.TOPIC as topic, c.PRICE as price, c.LIFECYCLE as lifecycle, c.MALL_TAG as mallTag,
            c.MALL_TITLE as mallTitle, c.MALL_IMAGE as mallImage,c.MALL_BANNER as mallBanner,
            c.MALL_DETAIL_IMAGE as mallDetailImage, c.MALL_GROUP as mallGroup, c.MALL_PUTAWAY as mallPutaway,
            c.BRAND as brand, c.PUTAWAY_TIME as putawayTime, c.AOLAI_UPDATE_TIME as aolaiUpdateTime,
            c.PRODUCT_HIDDEN_NO as productHiddenNo, c.MALL_CLASSIFY as mallClassify, a.PRODUCT_ID as productId,
            a.GB_CODE as gbCode, a.COLOR_ID as colorId, a.COLOR_CODE as colorCode, a.COLOR_NAME as colorName,
            a.SIZE_ID as sizeId, a.SIZE_CODE as sizeCode,a.SIZE_NAME as sizeName, a.SIZE_DESCRIPTION as sizeDescription,
            a.IMAGE as image,b.weid, b.PRODUCT_CODE as productCode, b.WEIMO_GOODID as weimoGoodid, b.WEIMO_SKUID as weimoSkuid,
            b.WEIMO_ISPUTAWAY as weimoIsputaway, b.WEIMO_PUTAWAY_TIME as weimoPutawayTime, a.ID as skuId
            from userwx.JIC_M_PRODUCT_ATTR a
            left join userwx.view_JIC_PRODUCT_MALL_RELATION b
            on a.PRODUCT_CODE = b.PRODUCT_CODE
            left join userwx.view_JIC_M_PRODUCT c
            on a.PRODUCT_ID = c.ID
            left join M_PRODUCT_TRY_BEFORE_BUY d
            on a.PRODUCT_NO = d.NAME
            where
            b.WEID = #{list.weId}
            and b.WEIMO_GOODID = #{list.weiMenGoodId}
            and b.WEIMO_SKUID = #{list.weiMenSkuId}
            and b.PRODUCT_CODE = #{list.productCode}
        </foreach>
    </select>
    <select id="searchProductByWeiMenInfoNoType" resultType="org.springcenter.product.api.dto.ProductByWeiMenInfoResp">
        <foreach collection="skus" item="list" open="(" close=")" separator="union all">
            select
            c.PRODUCT_NO as productNo, c.PRODUCT_NAME as productName, c.BRAND_ID as weId, c.BRAND_NAME as weIdName,
            c.year as year, c.BIG_SEASON_ID as bigSeasonId, c.BIG_SEASON as bigSeason, c.SEASON_ID as seasonId,
            c.season as season, c.SMALL_SEASON_ID as smallSeasonId, c.SMALL_SEASON as smallSeason,
            c.BIG_CLASS_ID as bigClassId, c.BIG_CLASS as bigClass, c.SMALL_CLASS_ID as smallClassId,
            c.SMALL_CLASS as smallClass, c.BAND_ID as bandId, c.BAND as band, c.TOPIC_ID as topicId,
            c.TOPIC as topic, c.PRICE as price, c.LIFECYCLE as lifecycle, c.MALL_TAG as mallTag,
            c.MALL_TITLE as mallTitle, c.MALL_IMAGE as mallImage,c.MALL_BANNER as mallBanner,
            c.MALL_DETAIL_IMAGE as mallDetailImage, c.MALL_GROUP as mallGroup, c.MALL_PUTAWAY as mallPutaway,
            c.BRAND as brand, c.PUTAWAY_TIME as putawayTime, c.AOLAI_UPDATE_TIME as aolaiUpdateTime,
            c.PRODUCT_HIDDEN_NO as productHiddenNo, c.MALL_CLASSIFY as mallClassify, a.PRODUCT_ID as productId,
            a.GB_CODE as gbCode, a.COLOR_ID as colorId, a.COLOR_CODE as colorCode, a.COLOR_NAME as colorName,
            a.SIZE_ID as sizeId, a.SIZE_CODE as sizeCode,a.SIZE_NAME as sizeName, a.SIZE_DESCRIPTION as sizeDescription,
            a.IMAGE as image,b.weid, b.PRODUCT_CODE as productCode, b.WEIMO_GOODID as weimoGoodid, b.WEIMO_SKUID as weimoSkuid,
            b.WEIMO_ISPUTAWAY as weimoIsputaway, b.WEIMO_PUTAWAY_TIME as weimoPutawayTime, a.ID as skuId
            from userwx.JIC_M_PRODUCT_ATTR a
            left join userwx.view_JIC_PRODUCT_MALL_RELATION b
            on a.PRODUCT_CODE = b.PRODUCT_CODE
            left join userwx.view_JIC_M_PRODUCT c
            on a.PRODUCT_ID = c.ID
            where
            b.WEID = #{list.weId}
            and b.WEIMO_GOODID = #{list.weiMenGoodId}
            and b.WEIMO_SKUID = #{list.weiMenSkuId}
            and b.PRODUCT_CODE = #{list.productCode}
        </foreach>
    </select>

</mapper>
