package org.springcenter.product.modules.repository.impl;

import org.springcenter.product.modules.mapper.product.MProductVersionMapper;
import org.springcenter.product.modules.mapper.product.MProductVersionSalesMapper;
import org.springcenter.product.modules.model.MProductVersionSales;
import org.springcenter.product.modules.repository.IMProductVersionSalesRepository;
import org.springcenter.product.api.dto.ProductVersionSalesReq;
import org.springcenter.product.api.enums.MQueriesTabFilterEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2022/12/10 15:46
 */
@Repository
public class MProductVersionSalesRepositoryImpl implements IMProductVersionSalesRepository {

    @Autowired
    private MProductVersionSalesMapper mProductVersionSalesMapper;

    @Autowired
    private MProductVersionMapper mProductVersionMapper;

    @Override
    public List<MProductVersionSales> selectByParams(ProductVersionSalesReq requestData, String sampleCode) {
        Integer sort = 3;
        if (StringUtils.isNotBlank(requestData.getTimeQuantum())) {
            MQueriesTabFilterEnum code = MQueriesTabFilterEnum.getByCode(requestData.getTimeQuantum());
            if (code != null) {
                sort = code.getDealFlag();
            }
        } else {
            sort = MQueriesTabFilterEnum.ONE_YEAR.getDealFlag();
        }
        //分组获取当前数据
        List<MProductVersionSales> mProductVersionSales = mProductVersionSalesMapper.selectByParams(requestData.getModelNumber(), sort, sampleCode);
        //过滤没有销售量的数据
        mProductVersionSales = mProductVersionSales.stream().filter(v -> StringUtils.isNotBlank(v.getModelNumber())).collect(Collectors.toList());
        //组装分组数据的信息
        return mProductVersionSales;
    }

    @Override
    public Pair<BigDecimal, BigDecimal> selectSumByParam(ProductVersionSalesReq requestData, String sampleCode) {
        Integer sort = 3;
        if (StringUtils.isNotBlank(requestData.getTimeQuantum())) {
            MQueriesTabFilterEnum code = MQueriesTabFilterEnum.getByCode(requestData.getTimeQuantum());
            if (code != null) {
                sort = code.getDealFlag();
            }
        }
        MProductVersionSales mProductVersionSales = mProductVersionSalesMapper.selectSumByParam(sort, requestData.getModelNumber(), sampleCode);
        if (mProductVersionSales != null) {
            return Pair.of(mProductVersionSales.getSalesNumber(), mProductVersionSales.getSalesPrice());
        } else {
            return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }
    }

    @Override
    public Integer querySalesByModelNumberOrSampleCode(String modelNumber, String sampleNo) {
        List<MProductVersionSales> mProductVersionSales = mProductVersionSalesMapper.selectSumByParamAndSampleCode(modelNumber, sampleNo);
        return CollectionUtils.isEmpty(mProductVersionSales) ? 0 :
                mProductVersionSales.stream()
                        .map(MProductVersionSales::getSalesNumber)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .intValue();
    }

}
