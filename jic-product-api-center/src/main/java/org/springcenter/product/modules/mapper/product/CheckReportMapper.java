package org.springcenter.product.modules.mapper.product;

import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.CheckReport;

import java.util.List;

public interface CheckReportMapper {
    int deleteByPrimaryKey(String id);

    int insert(CheckReport record);

    int insertSelective(CheckReport record);

    CheckReport selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CheckReport record);

    int updateByPrimaryKey(CheckReport record);

    Integer selectBySyncDir(@Param("syncDir") String syncDir);

    List<CheckReport> selectBySelective(CheckReport checkReport);

    List<CheckReport> selectByProductCode(@Param("productCode") String productCode, @Param("reportSeason") String reportSeason);

    List<CheckReport> selectByFabirc(@Param("productCode") String productCode, @Param("reportSeason") String reportSeason);

    List<CheckReport> findList(@Param("reportCodes") List<String> reportCodes,
                               @Param("fabircs") List<String> fabircs,
                               @Param("reportStatus") Integer reportStatus,
                               @Param("isDel")Integer isDel,
                               @Param("reportFreightSeason")String reportFreightSeason,
                               @Param("notImport") Integer notImport, @Param("reportType") Integer reportType, @Param("reportCode") String reportCode,
                               @Param("fabirc") String fabirc);

    void batchInsert(@Param("list") List<CheckReport> list);

    List<String> selectByReportCodes(@Param("reportCodes") List<String> reportCodes, @Param("dirName") String dirName);

    List<CheckReport> selectByFabircFlatType(@Param("fabircs") List<String> fabircs, @Param("reportSeason") String reportSeason,
                                             @Param("productCode") String productCode);

    List<CheckReport> selectByLikeReportCode(CheckReport params);

    List<CheckReport> selectByProductCodes(@Param("productCodes") List<String> productCodes);
}