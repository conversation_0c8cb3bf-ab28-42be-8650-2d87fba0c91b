package org.springcenter.product.modules.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.assertj.core.util.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/6/17 16:11
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BrandBrandEnum {

    JNBY(2L, Lists.newArrayList("JNBY", "JNBY饰品"), 2L),
    LESS(5L, Lists.newArrayList("LESS", "LESS饰品"), 5L),
    tz(4L, Lists.newArrayList("童装", "童装用品", "童装饰品"), 4L),
    yt(41L, Lists.newArrayList("婴童", "婴童用品", "婴童饰品"), 4L),
    POMME(12L, Lists.newArrayList("蓬马", "蓬马饰品"), 12L),
    APN(57L, Lists.newArrayList("A PERSONAL NOTE 73"), 57L),
    JNBYH(17L, Lists.newArrayList("JNBYHOME"), 17L),
    CROQUIS(3L, Lists.newArrayList("CROQUIS", "CROQUIS饰品"), 3L),
    RERERELAB(77L, Lists.newArrayList("RE;RE;RE;LAB", "RE;RE;RE;LAB饰品"), 77L),
    ;

    private Long outsideCode;

    private List<String> desc;

    private Long insideCode;

    public static List<BrandBrandEnum> getBrandInfoByCode (List<Long> codes) {
        return Arrays.stream(BrandBrandEnum.values())
                .filter(v -> codes.contains(v.getOutsideCode()))
                .collect(Collectors.toList());
    }
}
