package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.context.FabSearchContext;
import org.springcenter.product.modules.model.FabVolumeInfo;

import java.util.List;

public interface FabVolumeInfoMapper extends BaseMapper<FabVolumeInfo> {


    List<FabVolumeInfo> selectByParam(@Param("item") FabSearchContext context);

    void updateParamsById(@Param("info") FabVolumeInfo fabVolumeInfo);
}
