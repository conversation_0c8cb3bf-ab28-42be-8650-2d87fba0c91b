package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.api.dto.ProductByWeiMenInfoResp;
import org.springcenter.product.api.dto.ProductSkuIdByWeiMenInfoResp;
import org.springcenter.product.modules.model.JicMProductAttr;
import org.springcenter.product.modules.model.ProductByWeiMenInfoDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/17 15:53
 */
@Mapper
public interface JicMProductAttrMapper extends BaseMapper<JicMProductAttr> {
    List<ProductSkuIdByWeiMenInfoResp> selectSkuIdByWeiMenInfo(@Param("skus") List<ProductByWeiMenInfoDto> requestData);

    List<ProductByWeiMenInfoResp> searchProductByWeiMenInfo(@Param("skus") List<ProductByWeiMenInfoDto> requestData);

    List<ProductByWeiMenInfoResp> searchProductByWeiMenInfoNoType(@Param("skus") List<ProductByWeiMenInfoDto> retQuestData);
}
