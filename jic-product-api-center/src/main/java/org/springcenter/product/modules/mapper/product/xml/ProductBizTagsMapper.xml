<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.ProductBizTagsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.ProductBizTags">
        <id column="ID" property="id" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="PRODUCT_CODE" property="productCode" />
        <result column="TAG_DICT_CODE" property="tagDictCode" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DEL" property="isDel" />
        <result column="BEGIN_TIME" property="beginTime" />
        <result column="END_TIME" property="endTime" />
        <result column="CREATE_BY" property="createBy" />
        <result column="BIZ_TYPE" property="bizType" />
        <result column="BIG_CATEGORY_ID" property="bigCategoryId"/>
        <result column="SMALL_CATEGORY_ID" property="smallCategoryId"/>
        <result column="ARC_BRAND_ID" property="arcBrandId"/>
        <result column="YEAR" property="year"/>
        <result column="SMALL_SEASION_ID" property="smallSeasonId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PRODUCT_ID, PRODUCT_CODE, TAG_DICT_CODE, CREATE_TIME, UPDATE_TIME, IS_DEL, BEGIN_TIME, END_TIME, CREATE_BY, BIZ_TYPE,
            BIG_CATEGORY_ID,SMALL_CATEGORY_ID,ARC_BRAND_ID,YEAR,SMALL_SEASION_ID
    </sql>

</mapper>