package org.springcenter.product.modules.mapper.product;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.BEbStore;
import org.springcenter.product.modules.model.ProductSceneLabel;
import org.springcenter.product.modules.model.ProductSceneLabelEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 16:47
 */
@Mapper
public interface ProductSceneLabelMapper extends BaseMapper<ProductSceneLabel> {

    List<ProductSceneLabel> selectByParam(@Param("sceneName") String sceneName, @Param("ownChannelId") Integer ownChannelId);

    List<ProductSceneLabelEntity> selectSceneLabelEntityByIds(@Param("list") List<String> sceneIds);
}
