package org.springcenter.product.modules.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.dto.PVQueryStatsListReq;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.modules.mapper.product.MPQueAttrValueConnectMapper;
import org.springcenter.product.modules.mapper.product.MQueriesAttrConnectionMapper;
import org.springcenter.product.modules.model.MPQueAttrValueConnect;
import org.springcenter.product.modules.model.MQueriesAttrConnection;
import org.springcenter.product.modules.repository.IMQueriesAttrConnectionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2022/12/2 13:15
 */
@Service
public class MQueriesAttrConnectionRepositoryImpl implements IMQueriesAttrConnectionRepository {

    @Autowired
    private MQueriesAttrConnectionMapper mQueriesAttrConnectionMapper;

    @Override
    public List<MQueriesAttrConnection> selectStatsListByParams(PVQueryStatsListReq requestData) {
        return mQueriesAttrConnectionMapper.selectStatsListByParams(requestData.getBigCategoryId(), requestData.getSmallCategoryId(), requestData.getIsDisabled());
    }

    @Override
    public List<MQueriesAttrConnection> selectByAttributeInfo(List<MQueriesAttrConnection> list) {
        List<Long> attributeIds = list.stream().map(MQueriesAttrConnection::getAttributeId).collect(Collectors.toList());
        List<String> attributeCodes = list.stream().filter(v -> StringUtils.isNotBlank(v.getAttributeCode())).map(MQueriesAttrConnection::getAttributeCode).collect(Collectors.toList());
        return mQueriesAttrConnectionMapper.selectStatsListByAttributeList(attributeIds, attributeCodes);
    }

    @Override
    public void batchSaveConnections(List<MQueriesAttrConnection> saveConnections) {
        mQueriesAttrConnectionMapper.batchInsert(saveConnections);
    }

    @Override
    public void batchUpdateConnections(List<MQueriesAttrConnection> updateConnections) {
        mQueriesAttrConnectionMapper.batchUpdate(updateConnections);
    }

    @Override
    public List<MQueriesAttrConnection> queryAllNormalConnections() {
        QueryWrapper<MQueriesAttrConnection> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETE", IsDeleteEnum.NORMAL.getCode());
        return mQueriesAttrConnectionMapper.selectList(queryWrapper);
    }

    @Override
    public void batchUpdateBySceAttrIds(List<Long> deletedSecAttr) {
        mQueriesAttrConnectionMapper.batchUpdateBySceAttrIds(deletedSecAttr);
    }

    @Override
    public List<MQueriesAttrConnection> selectBySmallCateCode(String categoryCode) {
        return mQueriesAttrConnectionMapper.selectBySmallCateCode(categoryCode);
    }

    @Override
    public void updateByAttrId(Long attrId) {
        mQueriesAttrConnectionMapper.updateByAttrIds(attrId);
    }

    @Override
    public List<String> selectByAttrId(Long attrId) {
        return mQueriesAttrConnectionMapper.selectByAttrId(attrId);
    }

    @Override
    public void updateValidByIds(List<String> ids) {
        mQueriesAttrConnectionMapper.updateValidByIds(ids);
    }

    @Override
    public List<MQueriesAttrConnection> selectConnectByAttrIds(Long attributeId) {
        return mQueriesAttrConnectionMapper.selectConnectByAttrIds(attributeId);
    }

    @Override
    public List<MQueriesAttrConnection> selectAllConnectBySmallCategoryId(Long id) {
        return mQueriesAttrConnectionMapper.selectBySmallCategory(id);
    }


}
