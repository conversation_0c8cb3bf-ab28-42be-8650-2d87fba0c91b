package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springcenter.product.modules.model.MPQueAttrValueConnect;
import org.springcenter.product.modules.model.MProductVersion;
import org.springcenter.product.modules.model.ProductVersionEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【M_PRODUCT_VERSION(版型库表)】的数据库操作Mapper
* @createDate 2022-12-05 09:43:30
* @Entity generator.domain.MProductVersion
*/
public interface MProductVersionMapper extends BaseMapper<MProductVersion> {


    List<ProductVersionEntity> selectQueryList();

    List<ProductVersionEntity> selectQueryListByParams(@Param("samples") List<String> sampleNos,
                                                       @Param("attrs") List<String> entries,
                                                       @Param("brands") List<String> brandList,
                                                       @Param("categories") List<String> categoryList,
                                                       @Param("sort") Integer sort,
                                                       @Param("name") String name,
                                                       @Param("isClosed") Integer isClosed);

    int updateByEntityId(MProductVersion mProductVersion);

    void batchUpdateMp(@Param("list") List<MProductVersion> baseDataList);

}
