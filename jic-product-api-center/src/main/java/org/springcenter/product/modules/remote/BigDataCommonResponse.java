package org.springcenter.product.modules.remote;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date:2024/4/25 17:31
 */
@ApiModel(description = "大数据搜索 服务基础返回信息")
@Data
public class BigDataCommonResponse<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "状态码", required = true)
    @SerializedName("code")
    private Integer code;


    @ApiModelProperty(value = "数量", required = true)
    @SerializedName("count")
    private Integer count;


    @ApiModelProperty(value = "Info", required = true)
    @SerializedName("info")
    private String info;

    @ApiModelProperty(value = "(0:全局无结果或者未进行全局搜索 1:全局搜索有结果)", required = true)
    @SerializedName("globalSearchResult")
    private String globalSearchResult;


    @ApiModelProperty("响应数据")
    @SerializedName("data")
    private T data;
}
