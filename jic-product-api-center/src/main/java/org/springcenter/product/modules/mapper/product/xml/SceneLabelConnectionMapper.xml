<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.SceneLabelConnectionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.SceneLabelConnection">
        <id column="ID" property="id" />
        <result column="LABEL_CODE" property="labelCode" />
        <result column="SCENE_ID" property="sceneId" />
        <result column="SORT" property="sort" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="OPERATOR" property="operator"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, LABEL_CODE, SCENE_ID, SORT, CREATE_TIME, UPDATE_TIME, IS_DELETED, OPERATOR
    </sql>

    <update id="updateDisRelLabelCodeByLabelCode">
        update SCENE_LABEL_CONNECTION
        SET OPERATOR = #{name}, IS_DELETED = 1, UPDATE_TIME = sysdate
        where LABEL_CODE = #{labelCode}
    </update>

    <select id="selectSceneByLabelCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
            from SCENE_LABEL_CONNECTION
        where LABEL_CODE = #{labelCode} and IS_DELETED = 0
    </select>
    <select id="selectListBySceneIs" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from SCENE_LABEL_CONNECTION
        where SCENE_ID = #{sceneId} and IS_DELETED = 0
    </select>


    <select id="querySceneLabelConnectionBySceneKey" resultMap="BaseResultMap">
        select a.ID, a.LABEL_CODE, a.SCENE_ID, a.SORT, a.CREATE_TIME, a.UPDATE_TIME, a.IS_DELETED, a.OPERATOR
        from SCENE_LABEL_CONNECTION a
        LEFT JOIN PRODUCT_SCENE_LABEL b
        ON a.SCENE_ID = b.ID
        where b.SCENE_KEY = #{key} and a.IS_DELETED = 0 and b.IS_DELETED = 0
        and b.status = 0
        order by a.SORT asc
    </select>

    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO SCENE_LABEL_CONNECTION
            (ID, LABEL_CODE, SCENE_ID, SORT, CREATE_TIME, UPDATE_TIME, OPERATOR) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.labelCode,jdbcType=VARCHAR},
            #{item.sceneId,jdbcType=VARCHAR}, #{item.sort,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.operator,jdbcType=VARCHAR})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update SCENE_LABEL_CONNECTION
            set
            SCENE_ID = #{item.sceneId,jdbcType=VARCHAR},
            SORT = #{item.sort,jdbcType=DECIMAL},
            IS_DELETED = #{item.isDeleted,jdbcType=DECIMAL},
            UPDATE_TIME = sysdate,
            OPERATOR = #{name}
            where ID = #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateById">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update SCENE_LABEL_CONNECTION
            set
            SORT = #{item.sort,jdbcType=DECIMAL},
            IS_DELETED = #{item.isDeleted,jdbcType=DECIMAL},
            UPDATE_TIME = sysdate,
            OPERATOR = #{name}
            where ID = #{item.id}
        </foreach>
    </update>
</mapper>
