package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSON;
import com.jnby.common.cache.RedisPoolUtil;
import org.springcenter.product.modules.entity.AttrEntity;
import org.springcenter.product.modules.mapper.product.CArcbrandMapper;
import org.springcenter.product.modules.model.CArcbrand;
import org.springcenter.product.modules.model.Dim;
import org.springcenter.product.modules.repository.ICategoryRepository;
import org.springcenter.product.modules.service.ICategoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/29/21 11:24 AM
 */
@Service
public class CategoryServiceImpl implements ICategoryService {
    private static final String CACHE_KEY_PREFIX = "product:category:";

    @Autowired
    private ICategoryRepository iCategoryRepository;

    @Autowired
    private RedisPoolUtil redisPoolUtil;

    @Autowired
    private CArcbrandMapper arcbrandMapper;

    @Override
    public List<AttrEntity> getBigCategorys() {
        return getDim("DIM4", AttrEntity.class);
    }

    @Override
    public List<AttrEntity> getSmallCategorys() {
        return getDim("DIM7", AttrEntity.class);
    }

    @Override
    public List<AttrEntity> getBrands() {
        return getDim("DIM1", AttrEntity.class);
    }

    @Override
    public List<AttrEntity> getArcBrands() {
        List<CArcbrand> list = arcbrandMapper.selectActiveList();
        if (list.isEmpty()){
            return new ArrayList<>();
        }
        return list.stream().map(item -> {
            AttrEntity resp = new AttrEntity();
            resp.setId(item.getId());
            resp.setAttribname(item.getName());
            resp.setAttribcode(item.getCode());
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AttrEntity> getBands() {
        return getDim("DIM6", AttrEntity.class);
    }

    @Override
    public List<AttrEntity> getYears() {
        return getDim("DIM2", AttrEntity.class);
    }

    @Override
    public List<AttrEntity> getSeasons() {
        return getDim("DIM3", AttrEntity.class);
    }

    @Override
    public List<AttrEntity> getV2Bands() {
        return getDim("DIM6", AttrEntity.class);
    }



    private <T> List<T> getDim(String dimFlag, Class<T> tClass){
        String obj = redisPoolUtil.exec(jedis -> jedis.get(CACHE_KEY_PREFIX + dimFlag));
        List<Dim> dims = new ArrayList<>();
        if (Objects.nonNull(obj)){
            dims = Dim.decodeArr(obj, Dim.class);
        }else {
            dims = iCategoryRepository.findAll();
            if (!dims.isEmpty()){
                String str = JSON.toJSONString(dims);
                redisPoolUtil.exec(jedis -> jedis.setex(CACHE_KEY_PREFIX + dimFlag, 24*3600, str));
            }
        }

        List<T> list = new ArrayList<>();
        return dims.stream().filter(item -> item.getDimflag().equals(dimFlag)).map(item -> {
            T instance = null;
            try {
                instance = tClass.newInstance();
            } catch (Exception e) {
                e.printStackTrace();
            }
            BeanUtils.copyProperties(item, instance);
            return instance;
        }).collect(Collectors.toList());
    }
}
