package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.JicVidProductIsSellInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/8/15 14:50
 */
public interface JicVidProductIsSellInfoMapper extends BaseMapper<JicVidProductIsSellInfo> {

    void batchInsert(@Param("list") List<JicVidProductIsSellInfo> saveList);

    List<JicVidProductIsSellInfo> selectByNamesAndBrandIdAndVids(@Param("names") List<String> names,
                                                                 @Param("brandId") Long brandId,
                                                                 @Param("vids") List<String> vids);

    void batchUpdate(@Param("list") List<JicVidProductIsSellInfo> updateList);
}
