<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.BoxMProductMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.BoxMProduct">
    <id column="CODEID" jdbcType="DECIMAL" property="codeid" />
    <result column="ID" jdbcType="DECIMAL" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="VALUE" jdbcType="VARCHAR" property="value" />
    <result column="BRANDID" jdbcType="DECIMAL" property="brandid" />
    <result column="BRAND" jdbcType="VARCHAR" property="brand" />
    <result column="YEAR" jdbcType="VARCHAR" property="year" />
    <result column="BIGSEASONID" jdbcType="DECIMAL" property="bigseasonid" />
    <result column="BIG_SEASON" jdbcType="VARCHAR" property="bigSeason" />
    <result column="SMALLSEASONID" jdbcType="DECIMAL" property="smallseasonid" />
    <result column="SMALL_SEASON" jdbcType="VARCHAR" property="smallSeason" />
    <result column="STYLE" jdbcType="VARCHAR" property="style" />
    <result column="BAND" jdbcType="VARCHAR" property="band" />
    <result column="BIGCLASSID" jdbcType="DECIMAL" property="bigclassid" />
    <result column="BIG_CLASS" jdbcType="VARCHAR" property="bigClass" />
    <result column="SMALLCLASSID" jdbcType="DECIMAL" property="smallclassid" />
    <result column="SMALL_CLASS" jdbcType="VARCHAR" property="smallClass" />
    <result column="TOPICID" jdbcType="DECIMAL" property="topicid" />
    <result column="TOPIC" jdbcType="VARCHAR" property="topic" />
    <result column="ELEMENT" jdbcType="VARCHAR" property="element" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="NO" jdbcType="VARCHAR" property="no" />
    <result column="GBCODE" jdbcType="VARCHAR" property="gbcode" />
    <result column="COLORNO" jdbcType="VARCHAR" property="colorno" />
    <result column="COLOR_NAME" jdbcType="VARCHAR" property="colorName" />
    <result column="SIZENO" jdbcType="VARCHAR" property="sizeno" />
    <result column="SIZE_NAME" jdbcType="VARCHAR" property="sizeName" />
    <result column="IMGURL" jdbcType="VARCHAR" property="imgurl" />
    <result column="BASIC_LABEL" jdbcType="VARCHAR" property="basicLabel" />
    <result column="FAVORABLEPRICE" jdbcType="DECIMAL" property="favorableprice" />
    <result column="BANDID" jdbcType="DECIMAL" property="bandid" />
    <result column="LIFESTART" jdbcType="DECIMAL" property="lifestart" />
    <result column="SEX" jdbcType="VARCHAR" property="sex" />
    <result column="C_ARCBRAND_ID" jdbcType="DECIMAL" property="cArcbrandId" />
    <result column="STYLEPARTSIZE_MODEL" jdbcType="VARCHAR" property="stylepartsizeModel" />
    <result column="IS_WARN" jdbcType="DECIMAL" property="isWarn" />
  </resultMap>
  <sql id="Base_Column_List">
    CODEID, ID, NAME, VALUE, BRANDID, BRAND, YEAR, BIGSEASONID, BIG_SEASON, SMALLSEASONID,
    SMALL_SEASON, STYLE, BAND, BIGCLASSID, BIG_CLASS, SMALLCLASSID, SMALL_CLASS, TOPICID,
    TOPIC, ELEMENT, PRICE, NO, GBCODE, COLORNO, COLOR_NAME, SIZENO, SIZE_NAME, IMGURL,
    BASIC_LABEL, FAVORABLEPRICE, BANDID, LIFESTART, SEX, C_ARCBRAND_ID, STYLEPARTSIZE_MODEL,
    IS_WARN, C_ARCBRAND_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from jnby.b_box_m_product
    where CODEID = #{codeid,jdbcType=DECIMAL}
  </select>

  <select id="findOneByCondition" resultMap="BaseResultMap" parameterType="org.springcenter.product.modules.model.BoxMProduct">
    select
    <include refid="Base_Column_List" />
    from jnby.b_box_m_product
    <where>
    <if test="no != null">
      and no = #{no}
    </if>
    <if test="name != null">
      and name = #{name}
    </if>
    <if test="colorName != null">
      and color_name = #{colorName}
    </if>
    <if test="sizeName != null">
      and (size_name = #{sizeName} or stylepartsize_model = #{sizeName})
    </if>
    <if test="gbcode != null">
      and GBCODE = #{gbcode,jdbcType=VARCHAR}
    </if>
    <if test="colorno != null">
      and color_no = #{colorno}
    </if>
    <if test="id != null">
      and id = #{id}
    </if>
    <if test="codeid != null">
      and codeid = #{codeid}
    </if>
    </where>
  </select>

  <select id="findByName" resultMap="BaseResultMap" parameterType="org.springcenter.product.modules.model.BoxMProduct">
    select
    <include refid="Base_Column_List" />
    from jnby.b_box_m_product
    where
    <if test="name != null">
     name = #{name}
    </if>
  </select>

  <select id="findByNoList" resultMap="BaseResultMap">
  select
  <include refid="Base_Column_List" />
  from jnby.b_box_m_product
  where
    <if test="noList != null and noList.size() > 0">
     no in
      <foreach collection="noList" item="no" open="(" close=")" separator=",">
        #{no}
      </foreach>
    </if>
  </select>

  <select id="selectListBySkuIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from jnby.b_box_m_product
    where
    <if test="skuIds != null and skuIds.size() > 0">
       CODEID in
      <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
        #{skuId}
      </foreach>
    </if>
  </select>


  <resultMap id="productApi" type="org.springcenter.product.modules.entity.ProductApiEntity">
    <id property="id" column="id"/>
    <result property="name" column="name"/>
    <result property="value" column="value"/>
    <result property="brand" column="brand"/>
    <result property="year" column="year"/>
    <result property="bigSeason" column="big_season"/>
    <result property="smallSeason" column="small_season"/>
    <result property="style" column="style"/>
    <result property="band" column="band"/>
    <result property="bigClass" column="big_class"/>
    <result property="smallClass" column="small_class"/>
    <result property="topic" column="topic"/>
    <result property="element" column="element"/>
    <result property="price" column="price" javaType="Double"/>
    <result property="codeId" column="codeId"/>
    <result property="no" column="no"/>
    <result property="colorNo" column="colorNo"/>
    <result property="colorName" column="color_name"/>
    <result property="sizeNo" column="sizeNo"/>
    <result property="sizeName" column="size_name"/>
    <result property="stock" column="qty"/>
    <result property="stock1" column="qty1"/>
    <result property="storeId" column="store_id"/>
    <result property="storeName" column="store_name"/>
    <result property="gbCode" column="gbcode"/>
    <result property="imgUrl" column="imgurl"/>
    <result property="favorablePrice" column="favorablePrice"/>
    <result property="basic_Label" column="basic_Label"/>
    <result property="storeCode" column="store_code"/>
  </resultMap>


  <select id="selectProductApi" parameterType="org.springcenter.product.modules.context.ProductApiContext" resultMap="productApi">
    select a.id,
    a.name,
    a.value,
    a.brandId,
    a.brand,
    a.year,
    a.bigSeasonId,
    a.big_season,
    a.smallSeasonId,
    a.small_season,
    a.style,
    a.band,
    a.bigClassId,
    a.big_class,
    a.smallClassId,
    a.small_class,
    a.topicId,
    a.topic,
    a.element,
    a.price,
    a.codeId,
    a.no,
    a.gbcode,
    a.colorNo,
    a.color_name,
    a.sizeNo,
    a.size_name,
    m.qty qty,
    m.qty1 ,
    a.imgurl,
    a.basic_label ,
    a.favorablePrice,
    a.bandId
    from jnby.b_box_m_product a
    inner join
    ( select sum(fa.QTY-fa.qtypreout) qty,sum(case when fa.c_store_id=417608
    then fa.QTY-fa.qtypreout else 0 end) qty1,M_PRODUCTALIAS_ID
    from  fa_storage fa
    <choose>
      <when test=' storeId =="0"'>
        , c_store b
        where fa.c_store_id = b.id
        and b.c_customer_id = 176 and b.isactive='Y' and fa.qty-fa.qtypreout>0
      </when>
      <when test="storeId !=null and storeId != ''">
        where fa.c_store_id=#{storeId}
      </when>
      <otherwise>
        where fa.c_store_id in ( 31066,417608)
      </otherwise>
    </choose>
    group by M_PRODUCTALIAS_ID) m
    on a.codeId = NVL(m.M_PRODUCTALIAS_ID,0)
    <where>
    <if test="stock != null and stock != ''">
      and m.qty >=#{stock}
    </if>
    <if test="name != null and name != ''">
      and regexp_like(a.no,#{name})
    </if>
    <if test="colorNo != null and colorNo != ''">
      and a.colorNo = #{colorNo}
    </if>
    <if test="sizeNo != null and sizeNo != ''">
      and a.size_name = #{sizeNo}
    </if>
    </where>
    order by a.no desc
  </select>


  <select id="findByCondition" resultMap="BaseResultMap" parameterType="org.springcenter.product.modules.model.BoxMProduct">
    select
    <include refid="Base_Column_List" />
    from jnby.b_box_m_product
    <where>
    <if test="name != null">
      and name = #{name}
    </if>
    <if test="colorno != null">
      and colorno = #{colorno}
    </if>
    <if test="id != null">
      and id = #{id}
    </if>
    </where>
  </select>

  <select id="findBatchProductBaseInfoByCondition" resultMap="BaseResultMap">
    SELECT NAME, BRANDID, BRAND, YEAR, BIGSEASONID, BIG_SEASON, SMALLSEASONID,
    SMALL_SEASON, BAND, BIGCLASSID, BIG_CLASS, SMALLCLASSID, SMALL_CLASS,
    BANDID, C_ARCBRAND_ID
    FROM jnby.b_box_m_product bmp
    <where>
    <if test="nameList != null and nameList.size() > 0">
      and name in
      <foreach collection="nameList" item="name" open="(" close=")" separator=",">
        #{name}
      </foreach>
    </if>
    </where>
     GROUP BY NAME, BRANDID, BRAND, YEAR, BIGSEASONID, BIG_SEASON, SMALLSEASONID,
    SMALL_SEASON, BAND, BIGCLASSID, BIG_CLASS, SMALLCLASSID, SMALL_CLASS,
    BANDID, C_ARCBRAND_ID
  </select>

  <select id="selectListByNames" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include>
    from jnby.b_box_m_product
    where name in
    <foreach collection="names" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectListByGbCodes" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include>
    from jnby.b_box_m_product
    where GBCODE in
    <foreach collection="gbCodes" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectByProductIdAndColorNo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from jnby.b_box_m_product
    where id = #{productId} and colorno = #{colorNo}
  </select>
  <select id="selectListBySkcCodesGroupId" resultType="java.lang.Long">
    select id from jnby.b_box_m_product
    where name in
    <foreach collection="names" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
    group by id
  </select>
  <select id="selectListByIds" resultType="org.springcenter.product.modules.model.BoxMProduct">
    select
    <include refid="Base_Column_List" />
    from jnby.b_box_m_product
    where id in
    <foreach item="item" index="index" collection="ids"
             open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectListByNo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include>
    from jnby.b_box_m_product
    where NO = #{no} and rownum = 1
  </select>

</mapper>
