package org.springcenter.product.modules.repository;

import org.springcenter.product.api.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/17 15:50
 */
public interface IJicMProductRepository {

    /**
     * 根据品牌、条码、spu、微盟skuId查找我们skuId信息
     * @param requestData 入参
     * @return 出参
     */
    List<ProductSkuIdByWeiMenInfoResp> selectSkuIdByWeiMenInfo(List<ProductByWeiMenInfoReq> requestData);

    /**
     * 根据品牌、条码、spu、微盟skuId查找我们商品信息
     * @param requestData 入参
     * @return 出参
     */
    List<ProductByWeiMenInfoResp> searchProductByWeiMenInfo(List<ProductByWeiMenInfoReq> requestData);

    /**
     * 根据微盟goodId返回是否是先试后买商品
     * @param requestData 入参
     * @return 出参
     */
    List<ProductIsTryBeforeBuyResp> searchProductIsTryBeforeBuy(ProductIsTryBeforeBuyReq requestData);

    /**
     * 刷新redis【商品先试后买的标识】
     * @return
     */
    String refreshTryBeforeBuy();

    /**
     * 查询陪逛的wsc的商品信息
     * @param requestData
     * @return
     */
    List<ProductByWeiMenInfoResp> searchProductByWeiMenInfoNoType(List<ProductByWeiMenInfoReq> requestData);
}
