package org.springcenter.product.modules.entity;

import lombok.Data;
import org.springcenter.product.api.dto.FabSpuForAutoNameEsResp;
import org.springcenter.product.api.dto.FabSpuForAutoNameResp;
import org.springcenter.product.modules.model.FabAnDynamicFieldSetting;
import org.springcenter.product.modules.model.FabAutoNameSetting;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @Date:2024/1/17 15:44
 */
@Data
public class CommonAutoNameGenerateEntity {

    private List<FabAutoNameSetting> partSortedList;

    private List<FabSpuForAutoNameResp> partList;

    private List<FabSpuForAutoNameResp> wholeList;

    private Integer[] charLen;

    private AtomicReference<String> dpp;

    private AtomicReference<String> designName;

    private List<FabAnDynamicFieldSetting> fabAnDynamicFieldSettings;

    private List<String> forbiddenList;

    private FabSpuForAutoNameEsResp resp;
}
