package org.springcenter.product.modules.mapper.bojun;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.api.dto.ProductInfoByNo;
import org.springcenter.product.modules.model.bojun.MProduct;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/12/28 10:47
 */
public interface MProductMapper extends BaseMapper<MProduct> {

    List<MProduct> selectByNames(@Param("name") String name);

    List<String> selectNameByOrigName(@Param("origNames") List<String> spus);

    List<ProductInfoByNo> selectInfoByNames(@Param("names") List<String> requestData);

    String selectCouponRuleByRuleId(@Param("id") String couponRuleId);


    List<String> selectByCouponRuleAndOtherParams(@Param("lSql") String rule, @Param("nowDate") Long nowDate);
}
