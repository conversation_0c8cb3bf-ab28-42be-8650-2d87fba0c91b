package org.springcenter.product.modules.mapper.zt;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.zt.WeiMoBStorage;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/6/13 13:41
 */
public interface WeiMoBStorageMapper extends BaseMapper<WeiMoBStorage> {

    List<WeiMoBStorage> selectByTypesAndProductIdAndBrandId(@Param("list") List<String> types, @Param("productId") long productId,
                                                            @Param("brandId") String brandId);

    List<WeiMoBStorage> selectByStoreIdAndProductIdAndBrandId(@Param("productId") long productId, @Param("brandId") String brandId,
                                                              @Param("storeId") long storeId);
}
