package org.springcenter.product.modules.context;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.api.dto.QueryFabVolumeInfoReq;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/5/22 14:09
 */
@Data
public class FabSearchContext {

    @ApiModelProperty(value = "年")
    private List<String> years;

    @ApiModelProperty(value = "品牌id")
    private List<Long> brandIds;

    @ApiModelProperty(value = "波段id")
    private List<Long> bandIds;

    @ApiModelProperty(value = "b端还是c端")
    private String type;

    @ApiModelProperty(value = "货季")
    private List<String> goodSeasons;

    @ApiModelProperty(value = "品牌名称")
    private List<String> brandNames;

    @ApiModelProperty(value = "fab名称")
    private String fabName;

    @ApiModelProperty(value = "产品册类型 0直营 1经销")
    private Integer fabType;

    public static FabSearchContext build(QueryFabVolumeInfoReq requestData, String type) {
        FabSearchContext context = new FabSearchContext();
        BeanUtils.copyProperties(requestData, context);
        context.setType(type);
        return context;
    }
}
