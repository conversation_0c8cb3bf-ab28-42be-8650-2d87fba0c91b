package org.springcenter.product.modules.context;

import org.springcenter.product.api.dto.QueryGoodsReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 搭配商品上下文
 */
@Data
public class GoodsContext implements Serializable {
    //款号
    private String name;
    private Long m_brand_id;
    private String storeId;

    //排序方式，0 不排序、1，最新，2，销量排序 3商品ID排序
    private int sorted = 0;
    //库存方式 0 有库存，1 全部商品
    private int isStockNotEmpty = 0;
    //大类
    private List<Long> m_big_category_id = new ArrayList<>();
    private List<Long> m_small_category_id = new ArrayList<>();
    private Long m_band_id;
    private Long big_season_id;
    private String year;
    private Integer gPrice;
    private Integer lPrice;
    private List<String> mustLabels;
    private List<String> mustLabelLevels;
    private List<String> mustNotLabels;
    private Long productId;

    @ApiModelProperty(value = "商品大类集合")
    private List<Long> bigCategoryIds = new ArrayList<>();

    @ApiModelProperty(value = "商品小类集合")
    private List<Long> smallCategoryIds = new ArrayList<>();

    @ApiModelProperty(value = "品牌架构ID集合")
    private List<Long> brandIds = new ArrayList<>();

    @ApiModelProperty(value = "年份集合")
    private List<String> years = new ArrayList<>();

    @ApiModelProperty(value = "波段ID集合")
    private List<Long> bandIds = new ArrayList<>();

    @ApiModelProperty(value = "是否返回SKU")
    private boolean isReturnSku;

    @ApiModelProperty(value = "选中的季节id ")
    private List<Long> seasonIds;

    public void buildMap(QueryGoodsReq req){
        this.name = req.getName();
        this.year = req.getYear();
        this.m_band_id = req.getBandId();
        this.m_brand_id = req.getBrandId();
        setMap(req);
        this.big_season_id = req.getSeasonId();
        this.gPrice = req.getGPrice();
        this.lPrice = req.getLPrice();
        this.isStockNotEmpty = req.getIsStockNotEmpty();
        this.storeId = req.getStoreId();
        this.sorted = req.getSorted();
    }

    //后续做数据映射
    private void setMap(QueryGoodsReq req){
        if (req.getBigCategoryId() != null){
            this.m_big_category_id.add(req.getBigCategoryId());
        }
        if (req.getSmallCategoryId() != null){
            this.m_small_category_id.add(req.getSmallCategoryId());
        }
        if (req.getMustLabels() != null){
            this.mustLabels = req.getMustLabels();
        }
        if (req.getMustNotLabels() != null){
            this.mustNotLabels = req.getMustNotLabels();
        }
    }
}
