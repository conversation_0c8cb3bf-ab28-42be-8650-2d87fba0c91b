package org.springcenter.product.modules.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date:2024/8/8 10:25
 */
@Data
@Builder
public class EgSearchReqEntity implements Serializable {

    @ApiModelProperty(value = "base64格式的图片，必传")
    private String img;

    @ApiModelProperty(value = "搜索来源：pos/wx，必传")
    private String source;

    @ApiModelProperty(value = "品牌：'JNBY', 'LESS', 'CROQUIS', 'POMME', 'TJNBY', 'APN73','JNBYHOME', 'SAMO', 'LASUMIN', 'ALL'，必传")
    private String brand;
}
