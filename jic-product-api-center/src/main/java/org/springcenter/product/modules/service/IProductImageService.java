package org.springcenter.product.modules.service;

import org.springcenter.product.api.dto.IdentifyImageReq;
import org.springcenter.product.api.dto.IdentifyImageResp;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date:2024/8/8 10:19
 */
public interface IProductImageService {
    /**
     * 以图搜图接口
     * @param requestData 入参
     * @return 返回
     */
    IdentifyImageResp identifyPicByPic(IdentifyImageReq requestData) throws IOException;
}
