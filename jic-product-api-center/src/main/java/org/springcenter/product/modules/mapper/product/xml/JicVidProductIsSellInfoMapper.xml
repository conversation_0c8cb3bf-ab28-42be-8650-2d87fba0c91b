<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.JicVidProductIsSellInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.JicVidProductIsSellInfo">
        <id column="ID" property="id" />
        <result column="VID" property="vid" />
        <result column="BRAND_ID" property="brandId" />
        <result column="SPU" property="spu" />
        <result column="IS_CAN_SELL" property="isCanSell" />
        <result column="IS_PUTAWAY" property="isPutaway" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VID, BRAND_ID, SPU, IS_CAN_SELL, IS_PUTAWAY, CREATE_TIME, UPDATE_TIME, IS_DELETED
    </sql>
    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO JIC_VID_PRODUCT_IS_SELL_INFO
            (ID, VID, BRAND_ID, SPU, CREATE_TIME, UPDATE_TIME, IS_CAN_SELL, IS_PUTAWAY) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.vid,jdbcType=VARCHAR}, #{item.brandId,jdbcType=DECIMAL},
            #{item.spu,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isCanSell,jdbcType=DECIMAL}, #{item.isPutaway,jdbcType=DECIMAL})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update JIC_VID_PRODUCT_IS_SELL_INFO
            set
            IS_CAN_SELL = #{item.isCanSell},
            IS_PUTAWAY = #{item.isPutaway},
            UPDATE_TIME = sysdate
            where ID = #{item.id}
        </foreach>
    </update>

    <select id="selectByNamesAndBrandIdAndVids" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from JIC_VID_PRODUCT_IS_SELL_INFO
        where BRAND_ID = #{brandId}
        and SPU in
        <foreach collection="names" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and VID in
        <foreach collection="vids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>


</mapper>
