package org.springcenter.product.modules.service;

import org.springcenter.product.api.dto.AiAssistantReq;
import org.springcenter.product.api.dto.AiLookAssistantReq;
import org.springcenter.product.api.dto.AiLookExistReq;
import org.springcenter.product.api.dto.AiStyleTypeResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/9/19 14:01
 */
public interface IProductAiAssistantService {

    /**
     * 根据商品id和类型返回fab信息
     * @param requestData 入参
     * @return 返回
     */
    String invokeAiAssistant(AiAssistantReq requestData);


    /**
     * 获取风格类型
     * @return 返回
     */
    List<AiStyleTypeResp> getAiStyleType();


    /**
     * 获取Look风格类型
     * @return 返回
     */
    List<AiStyleTypeResp> getLookAiStyleType();

    /**
     * 通过Look的fab信息
     * @param requestData 入参
     * @return 返回
     */
    String invokeLookAiAssistant(AiLookAssistantReq requestData);

    /**
     * Look调用ai助手前判断是否有fab信息
     * @param requestData 入参
     * @return 返回
     */
    Boolean invokeLookExistFab(AiLookExistReq requestData);
}
