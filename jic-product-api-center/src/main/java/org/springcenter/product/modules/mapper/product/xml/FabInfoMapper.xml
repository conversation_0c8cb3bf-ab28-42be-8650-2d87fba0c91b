<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.FabInfoMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.FabInfo">
        <id column="ID" property="id" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="NAME" property="name" />
        <result column="FAB" property="fab" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="OPERATORS" property="operators" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, PRODUCT_ID, NAME, FAB, CREATE_TIME, UPDATE_TIME, OPERATORS,IS_DELETED
    </sql>


    <select id="selectByProductId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM FAB_INFO
        WHERE PRODUCT_ID = #{productId} AND IS_DELETED = 0
        and ROWNUM = 1
    </select>


    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO FAB_INFO
            (ID, PRODUCT_ID, NAME, FAB, OPERATORS, CREATE_TIME, UPDATE_TIME) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.productId,jdbcType=VARCHAR}, #{item.name},
            #{item.fab}, #{item.operators}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>



    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update FAB_INFO
            set
            FAB = #{item.fab},
            OPERATORS = #{item.operators},
            UPDATE_TIME = sysdate
            where ID = #{item.id}
        </foreach>
    </update>
    <update id="updateByName">
        update FAB_INFO
        set
            IS_DELETED = 1,
            UPDATE_TIME = sysdate
        where NAME = #{name}
    </update>
</mapper>
