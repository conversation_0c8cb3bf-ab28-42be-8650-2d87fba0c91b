package org.springcenter.product.modules.service;

import org.springcenter.product.api.dto.AddProductFabWearingReq;
import org.springcenter.product.modules.model.FabWearingImgInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/7/1 15:43
 */
public interface FabWearingImgInfoService {
    /**
     * 保存穿着方式图片
     * @param requestData 请求参数
     * @return 返回
     */
    Boolean saveWearing(AddProductFabWearingReq requestData);


    /**
     * 根据商品id和款号查询穿着方式图片
     * @param productId 商品id
     * @param name 款号
     * @return 返沪穿着方式图片
     */
    FabWearingImgInfo selectWearingByProductIdAndName(String productId, String name);
}
