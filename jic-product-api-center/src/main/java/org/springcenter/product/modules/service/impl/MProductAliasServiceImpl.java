package org.springcenter.product.modules.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcenter.product.modules.mapper.product.MProductAliasMapper;
import org.springcenter.product.modules.model.MProductAlias;
import org.springcenter.product.modules.service.IMProductAliasService;
import org.springframework.stereotype.Service;

/**
 * @auther wangchun
 * @create 2022-04-19 15:38:22
 * @describe 服务实现类
 */
@Service
public class MProductAliasServiceImpl extends ServiceImpl<MProductAliasMapper, MProductAlias> implements IMProductAliasService {

}
