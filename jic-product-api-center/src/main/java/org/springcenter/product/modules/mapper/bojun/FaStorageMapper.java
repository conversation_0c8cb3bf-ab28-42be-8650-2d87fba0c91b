package org.springcenter.product.modules.mapper.bojun;

import org.springcenter.product.modules.entity.ProductStockEntity;
import org.springcenter.product.modules.entity.SkcStockEntity;
import org.springcenter.product.modules.entity.SpuStockEntity;
import org.springcenter.product.modules.model.bojun.FaStorage;
import org.springcenter.product.api.dto.ProductAgentStockResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 9:57
 */

public interface FaStorageMapper {

    List<ProductAgentStockResp> getStorageList(@Param("ids") List<Long> skuIds, @Param("storeIds") List<Long> storeIds,
                                               @Param("safeStock") int safeStock);

    List<SpuStockEntity> getInfoBySpuAndStoreIds(@Param("productList") List<Long> productIds, @Param("storeList") List<Long> storeIds);

    List<SpuStockEntity> getEbStorageBySpuAndStoreIds(@Param("productList") List<Long> productIds, @Param("storeList") List<Long> storeIds);

    List<SpuStockEntity> getSpuStorageList(@Param("productIds") List<Long> productId, @Param("storeIds") List<Long> storeIds);

    FaStorage selectByPrimaryKey(Long id);

    long getStorage(@Param("mProductaliasId") Long mProductaliasId, @Param("storeIds") List<Long> storeIds);

    /**
     * 查询门店SKC库存
     * @param productId
     * @param storeId
     * @return
     */
    List<SkcStockEntity> querySkcStorageList(@Param("productId") Long productId, @Param("storeId") Long storeId);

    /**
     * 查询LJ+BOX仓库存
     * @return
     */
    List<SkcStockEntity> queryLjBoxSkcStorage(@Param("productId") Long productId);

    /**
     * 查询box+lj+jnbyp库存
     * @param productId
     * @return
     */
    List<SkcStockEntity> queryBLJnbypSkcStorageList(@Param("productId") Long productId, @Param("storeIds") List<Long> storeIds);

    List<ProductStockEntity> getJNBYGatherStock(@Param("ids") List<Long> ids, @Param("storeIds") List<Long> storeIds);

}
