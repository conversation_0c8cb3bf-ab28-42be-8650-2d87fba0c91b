<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.MProductTryBeforeBuyMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.MProductTryBeforeBuy">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="weiMoGoodId" column="WEI_MO_GOOD_ID" jdbcType="VARCHAR"/>
        <result property="type" column="TYPE" jdbcType="DECIMAL"/>
        <result property="isDelete" column="IS_DELETE" jdbcType="DECIMAL"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="merchantId" column="MERCHANT_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NAME,TYPE,
        IS_DELETE,CREATE_TIME,UPDATE_TIME,WEI_MO_GOOD_ID,MERCHANT_ID
    </sql>

    <select id="selectAllNormalList" resultMap="BaseResultMap">
        SELECT NAME, TYPE, WEI_MO_GOOD_ID
        FROM M_PRODUCT_TRY_BEFORE_BUY
        WHERE IS_DELETE = 0
    </select>
    <select id="searchProductIsTryBeforeBuy" resultType="org.springcenter.product.api.dto.ProductIsTryBeforeBuyResp">
        SELECT NAME, TYPE, WEI_MO_GOOD_ID
        FROM M_PRODUCT_TRY_BEFORE_BUY
        WHERE IS_DELETE = 0
        <if test="weiMoGoods!= null and weiMoGoods.size()>0">
            and WEI_MO_GOOD_ID in
            <foreach collection="weiMoGoods" item="name" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
    </select>
</mapper>
