package org.springcenter.product.modules.service;

import org.springcenter.product.modules.model.ProductBizTags;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springcenter.product.api.dto.ProductBizTagResp;
import org.springcenter.product.api.dto.QueryProductBizTags;

import java.util.List;

/**
 * @auther yuanxiaozhong
 * @create 2022-12-01 16:17:30
 * @describe 服务类
 */
public interface IProductBizTagsService extends IService<ProductBizTags> {

    /**
     * 批量查询商品标签信息
     * @param queryProductBizTags
     * @return
     */
    List<ProductBizTagResp> queryProductBizTags(QueryProductBizTags queryProductBizTags);
}
