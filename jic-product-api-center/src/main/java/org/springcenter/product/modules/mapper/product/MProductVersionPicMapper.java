package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.MProductVersionPic;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【M_PRODUCT_VERSION_PIC(版型库照片)】的数据库操作Mapper
* @createDate 2022-12-05 09:43:42
* @Entity generator.domain.MProductVersionPic
*/

public interface MProductVersionPicMapper extends BaseMapper<MProductVersionPic> {

    void delAllPicsByMpIds(@Param("list") List<String> ids);

}
