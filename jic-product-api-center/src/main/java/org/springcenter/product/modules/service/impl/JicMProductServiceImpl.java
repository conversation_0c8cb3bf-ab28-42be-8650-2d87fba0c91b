package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.modules.repository.IJicMProductRepository;
import org.springcenter.product.modules.service.IJicMProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/17 15:49
 */
@Service
@Slf4j
public class JicMProductServiceImpl implements IJicMProductService {

    @Autowired
    private IJicMProductRepository jicMProductRepository;

    @Override
    public List<ProductSkuIdByWeiMenInfoResp> searchProductSkuIdByWeiMenInfo(List<ProductByWeiMenInfoReq> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            return Collections.emptyList();
        }
        return jicMProductRepository.selectSkuIdByWeiMenInfo(requestData);
    }

    @Override
    public List<ProductByWeiMenInfoResp> searchProductByWeiMenInfo(List<ProductByWeiMenInfoReq> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            return Collections.emptyList();
        }
        return jicMProductRepository.searchProductByWeiMenInfo(requestData);
    }

    @Override
    public List<ProductIsTryBeforeBuyResp> searchProductIsTryBeforeBuy(ProductIsTryBeforeBuyReq requestData) {
        return jicMProductRepository.searchProductIsTryBeforeBuy(requestData);
    }

    @Override
    public String refreshTryBeforeBuy() {
        return jicMProductRepository.refreshTryBeforeBuy();
    }

    @Override
    public List<ProductByWeiMenInfoResp> searchProductByWeiMenInfoNoType(List<ProductByWeiMenInfoReq> requestData) {
        log.info("=================searchProductByWeiMenInfoNoType购物车转换商品信息 入参:{}", JSONObject.toJSONString(requestData));
        if (CollectionUtils.isEmpty(requestData)) {
            return Collections.emptyList();
        }
        List<ProductByWeiMenInfoResp> resps = jicMProductRepository.searchProductByWeiMenInfoNoType(requestData);
        log.info("=================searchProductByWeiMenInfoNoType购物车转换商品信息 返回:{}", JSONObject.toJSONString(resps));
        return resps;
    }
}
