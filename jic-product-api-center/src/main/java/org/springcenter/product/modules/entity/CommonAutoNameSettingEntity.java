package org.springcenter.product.modules.entity;

import lombok.Data;
import org.springcenter.product.modules.model.FabAnDynamicFieldSetting;
import org.springcenter.product.modules.model.FabAutoNameSetting;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/1/17 14:57
 */
@Data
public class CommonAutoNameSettingEntity {

    private List<FabAutoNameSetting> partSortedList;

    private List<FabAnDynamicFieldSetting> fabAnDynamicFieldSettings;

    private List<String> forbiddenList;

    private Long pageTotal;
}
