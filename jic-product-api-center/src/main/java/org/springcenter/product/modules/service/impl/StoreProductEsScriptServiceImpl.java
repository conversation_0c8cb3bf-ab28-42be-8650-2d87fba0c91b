package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.springcenter.product.api.dto.ProductVidSkcResp;
import org.springcenter.product.modules.mapper.product.JicVidProductIsSellInfoMapper;
import org.springcenter.product.modules.model.JicVidProductIsSellInfo;
import org.springcenter.product.modules.service.IStoreProductEsScriptService;
import org.springcenter.product.modules.util.EsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2023/8/31 9:21
 */
@Service
@Slf4j
@EnableAsync
@RefreshScope
public class StoreProductEsScriptServiceImpl implements IStoreProductEsScriptService {
    @Value("${es.index.store.goods.skc}")
    private String storeProductSkcIndex;

    @Autowired
    private EsUtil esUtil;

    @Value("${es.index.vid.mall.goods.skc}")
    private String vidMallGoodsSkcIndex;
    
    @Autowired
    private JicVidProductIsSellInfoMapper jicVidProductIsSellInfoMapper;

    @Value("${jic.vid.product.leaf.tag}")
    private String jicVidProductIdLeaf;

    @Value("${vid.index.mode}")
    private String vidIndexMode;

    @Override
    @Async
    public void insertSkcInfoProduct(List<ProductVidSkcResp> resps) {
        if (CollectionUtils.isEmpty(resps)) {
            return;
        }

        resps.forEach(v -> {
            IndexRequest indexRequest = new IndexRequest();
            Map<String, String> map = JSONObject.parseObject(vidIndexMode, Map.class);
            String index = map.get(Objects.toString(v.getWeId()));
            indexRequest.index(StringUtils.replace(vidMallGoodsSkcIndex,"mode", index)).id(v.getId());
            indexRequest.source(JSONObject.toJSONString(v), XContentType.JSON);
            try {
                esUtil.index(indexRequest);
            }catch (Exception e){
                log.error("dealNotInVidSkcEs  插入es到vid-skc中  出错 ",e);
            }

        });

        // 判断是否存在预售表中 无则加入有则更新
        List<JicVidProductIsSellInfo> infos = jicVidProductIsSellInfoMapper
                .selectByNamesAndBrandIdAndVids(Lists.newArrayList(resps.get(0).getName()), resps.get(0).getWeId(), Lists.newArrayList(resps.get(0).getVid()));
        if (CollectionUtils.isNotEmpty(infos)) {
            infos.get(0).setIsPutaway(1);
            infos.get(0).setIsCanSell(1);
            infos.get(0).setUpdateTime(new Date());
            jicVidProductIsSellInfoMapper.batchUpdate(Lists.newArrayList(infos.get(0)));
        } else {
            JicVidProductIsSellInfo info = new JicVidProductIsSellInfo();
            info.setId(IdLeaf.getDateId(jicVidProductIdLeaf));
            info.setCreateTime(new Date());
            info.setVid(resps.get(0).getVid());
            info.setSpu(resps.get(0).getName());
            info.setUpdateTime(new Date());
            info.setBrandId(resps.get(0).getWeId());
            info.setIsPutaway(1);
            info.setIsCanSell(1);
            jicVidProductIsSellInfoMapper.batchInsert(Lists.newArrayList(info));
        }

    }
}
