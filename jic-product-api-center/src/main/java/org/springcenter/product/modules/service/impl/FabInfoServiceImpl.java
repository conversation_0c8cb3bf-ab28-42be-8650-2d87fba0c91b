package org.springcenter.product.modules.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springcenter.product.api.dto.AddProductFabInfoReq;
import org.springcenter.product.api.dto.FabInfoByName;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.modules.mapper.bojun.MProductMapper;
import org.springcenter.product.modules.mapper.product.FabInfoLogMapper;
import org.springcenter.product.modules.mapper.product.FabInfoMapper;
import org.springcenter.product.modules.mapper.product.FabMatchingSugInfoLogMapper;
import org.springcenter.product.modules.mapper.product.FabMatchingSugInfoMapper;
import org.springcenter.product.modules.model.FabInfo;
import org.springcenter.product.modules.model.FabInfoLog;
import org.springcenter.product.modules.model.FabMatchingSugInfo;
import org.springcenter.product.modules.model.FabMatchingSugInfoLog;
import org.springcenter.product.modules.model.bojun.MProduct;
import org.springcenter.product.modules.service.FabInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * @Date:2023/9/19 13:19
 */
@Service
@RefreshScope
@Slf4j
public class FabInfoServiceImpl implements FabInfoService {

    @Autowired
    private FabInfoMapper fabInfoMapper;

    @Autowired
    private FabInfoLogMapper fabInfoLogMapper;


    @Autowired
    private FabMatchingSugInfoMapper fabMatchingSugInfoMapper;

    @Autowired
    private FabMatchingSugInfoLogMapper fabMatchingSugInfoLogMapper;

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Value("${fab.wear.tag.id}")
    private String fabWearTagId;

    @Autowired
    private MProductMapper mProductMapper;

    @Override
    public Boolean saveFabInfo(AddProductFabInfoReq requestData) {
        List<FabInfo> fabInfo = this.selectFabInfoByName(requestData.getName());
        List<MProduct> mProducts = mProductMapper.selectByNames(requestData.getName());
        if (CollectionUtils.isEmpty(mProducts)) {
            return true;
        }
        List<FabInfo> insertsFabInfos = new ArrayList<>();
        if (CollectionUtils.isEmpty(fabInfo)) {
            mProducts.forEach(v -> {
                FabInfo info = new FabInfo();
                info.setId(IdLeaf.getId(fabWearTagId));
                info.setName(requestData.getName());
                info.setProductId(Objects.toString(v.getId()));
                info.setFab(requestData.getFabInfo());
                info.setOperators(requestData.getOperates());
                info.setCreateTime(new Date());
                info.setUpdateTime(new Date());
                insertsFabInfos.add(info);
            });
        } else {
            HashMap<String, FabInfo> validateMap = fabInfo.stream()
                    .collect(HashMap::new, (k, v) -> k.put(v.getProductId(), v), HashMap::putAll);
            mProducts.forEach(v -> {
                if (MapUtils.isEmpty(validateMap) || validateMap.get(Objects.toString(v.getId())) == null) {
                    FabInfo info = new FabInfo();
                    info.setId(IdLeaf.getId(fabWearTagId));
                    info.setName(requestData.getName());
                    info.setProductId(Objects.toString(v.getId()));
                    info.setFab(requestData.getFabInfo());
                    info.setOperators(requestData.getOperates());
                    info.setCreateTime(new Date());
                    info.setUpdateTime(new Date());
                    insertsFabInfos.add(info);
                } else {
                    FabInfo info = new FabInfo();
                    FabInfo fabInfo1 = validateMap.get(Objects.toString(v.getId()));
                    BeanUtils.copyProperties(fabInfo1, info);
                    info.setUpdateTime(new Date());
                    info.setOperators(requestData.getOperates());
                    info.setFab(requestData.getFabInfo());
                    info.setId(IdLeaf.getId(fabWearTagId));
                    insertsFabInfos.add(info);
                }
            });
        }

        // 维护日志
        List<FabInfoLog> dealLogs = new ArrayList<>();
        List<FabInfo> needLogs = new ArrayList<>();
        needLogs.addAll(insertsFabInfos);
        needLogs.forEach(v -> {
            FabInfoLog log = new FabInfoLog();
            BeanUtils.copyProperties(v, log);
            log.setId(IdLeaf.getId(fabWearTagId));
            log.setFabInfoId(v.getId());
            log.setCreateTime(new Date());
            log.setUpdateTime(new Date());
            dealLogs.add(log);
        });


        fabInfoMapper.updateByName(requestData.getName());
        template.execute(v -> {

            if (CollectionUtils.isNotEmpty(insertsFabInfos)) {
                fabInfoMapper.batchInsert(insertsFabInfos);
            }

            if (CollectionUtils.isNotEmpty(dealLogs)) {
                fabInfoLogMapper.batchInsert(dealLogs);
            }

            return true;
        });
        return true;
    }

    private List<FabInfo> selectFabInfoByName(String name) {
        LambdaQueryWrapper<FabInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FabInfo::getName, name);
        wrapper.eq(FabInfo::getIsDeleted, 0);
        return fabInfoMapper.selectList(wrapper);
    }

    public FabInfo selectFabInfoByProductIdAndName(String productId, String name) {
        LambdaQueryWrapper<FabInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FabInfo::getProductId, productId);
        wrapper.eq(FabInfo::getName, name);
        wrapper.eq(FabInfo::getIsDeleted, 0);
        return fabInfoMapper.selectOne(wrapper);
    }

    @Override
    public List<FabInfo> selectFabInfoByIdsAndNames(List<String> productIds) {
        QueryWrapper<FabInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("PRODUCT_ID", productIds);
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        return fabInfoMapper.selectList(queryWrapper);
    }

    public FabMatchingSugInfo selectFabMatchingByProductIdAndName(String productId, String name) {
        LambdaQueryWrapper<FabMatchingSugInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FabMatchingSugInfo::getProductId, productId);
        wrapper.eq(FabMatchingSugInfo::getName, name);
        wrapper.eq(FabMatchingSugInfo::getIsDeleted, 0);
        return fabMatchingSugInfoMapper.selectOne(wrapper);
    }

    @Override
    public Boolean saveMatching(AddProductFabInfoReq requestData) {
        Boolean isInsert = false;
        FabMatchingSugInfo fabInfo = this.selectFabMatchingByProductIdAndName(requestData.getProductId(), requestData.getName());
        if (fabInfo == null) {
            fabInfo = new FabMatchingSugInfo();
            fabInfo.setId(IdLeaf.getId(fabWearTagId));
            fabInfo.setName(requestData.getName());
            fabInfo.setProductId(requestData.getProductId());
            fabInfo.setMatchingSug(requestData.getFabInfo());
            fabInfo.setOperators(requestData.getOperates());
            fabInfo.setCreateTime(new Date());
            fabInfo.setUpdateTime(new Date());
            isInsert = true;
        } else {
            fabInfo.setUpdateTime(new Date());
            fabInfo.setOperators(requestData.getOperates());
            fabInfo.setMatchingSug(requestData.getFabInfo());
        }

        // 维护日志
        FabMatchingSugInfoLog log = new FabMatchingSugInfoLog();
        BeanUtils.copyProperties(fabInfo, log);
        log.setId(IdLeaf.getId(fabWearTagId));
        log.setFabSugInfoId(fabInfo.getId());
        log.setCreateTime(new Date());
        log.setUpdateTime(new Date());

        Boolean finalIsInsert = isInsert;
        FabMatchingSugInfo finalFabInfo = fabInfo;
        template.execute(v -> {
            if (finalIsInsert) {
                fabMatchingSugInfoMapper.insert(finalFabInfo);
            } else {
                fabMatchingSugInfoMapper.updateById(finalFabInfo);
            }
            fabMatchingSugInfoLogMapper.insert(log);
            return true;
        });
        return true;
    }
}
