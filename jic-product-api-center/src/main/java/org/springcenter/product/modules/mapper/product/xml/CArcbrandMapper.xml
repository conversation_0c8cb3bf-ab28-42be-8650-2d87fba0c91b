<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.CArcbrandMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.CArcbrand">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="AD_CLIENT_ID" jdbcType="DECIMAL" property="adClientId" />
    <result column="AD_ORG_ID" jdbcType="DECIMAL" property="adOrgId" />
    <result column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
    <result column="MODIFIERID" jdbcType="DECIMAL" property="modifierid" />
    <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
    <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
    <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
    <result column="BRANDNAME" jdbcType="VARCHAR" property="brandname" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, AD_CLIENT_ID, AD_ORG_ID, CODE, NAME, OWNERID, MODIFIERID, CREATIONDATE, MODIFIEDDATE, 
    ISACTIVE, BRANDNAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from C_ARCBRAND
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="selectActiveList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from C_ARCBRAND
    where isactive = 'Y'
  </select>

</mapper>