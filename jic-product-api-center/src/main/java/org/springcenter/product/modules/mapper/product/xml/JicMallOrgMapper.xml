<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.JicMallOrgMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.JicMallOrg">
        <id column="ID" property="id" />
        <result column="VID" property="vid" />
        <result column="BRAND_ID" property="brandId" />
        <result column="INSIDE_ID" property="insideId" />
        <result column="VID_TYPE" property="vidType" />
        <result column="VID_CODE" property="vidCode" />
        <result column="VID_NAME" property="cidName" />
        <result column="PARENT_VID" property="parentVid" />
        <result column="VID_STATUS" property="vidStatus" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, VID, BRAND_ID, INSIDE_ID, VID_TYPE, VID_CODE, VID_NAME, PARENT_VID, VID_STATUS,
        CREATE_TIME, UPDATE_TIME
    </sql>
    <select id="selectWeIdByVid" resultType="java.lang.String">
        select BRAND_ID from userwx.JIC_MALL_ORG
        where VID = #{vid} and VID_TYPE in (10, 2)
    </select>
    <select id="selectBojunInfoByWeimo"
            resultType="org.springcenter.product.api.dto.ExchangeProductIdByWeiMoResp">
        select c.PRODUCT_ID as goodId, c.ID as skuId from userwx.JIC_MALL_ORG a
                 left join userwx.view_JIC_PRODUCT_MALL_RELATION b
                 on a.BRAND_ID = b.WEID
                 left join userwx.JIC_M_PRODUCT_ATTR c
                 on b.PRODUCT_CODE = c.PRODUCT_CODE
        where a.VID = #{vid}
        and WEIMO_SKUID in
        <foreach collection="list" item="list" open="(" close=")" separator=",">
            #{list}
        </foreach>

    </select>

    <select id="selectVidByInsideId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from userwx.JIC_MALL_ORG
        where INSIDE_ID = #{insideId} and VID_TYPE in (10, 2)
    </select>

    <select id="selectByVidCodeAndType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from userwx.JIC_MALL_ORG
        where VID_TYPE in (10, 2) and BRAND_ID = #{weId}
        <if test="vidCodes != null and vidCodes.size() > 0">
            and VID_CODE in
            <foreach collection="vidCodes" item="list" close=")" open="(" separator=",">
                #{list}
            </foreach>
        </if>
    </select>

    <select id="selectVidByWeidAndOnline" resultType="java.lang.String">
        select VID
        from userwx.JIC_MALL_ORG
        where VID_TYPE in (10, 2) and BRAND_ID = #{brandId}
    </select>

    <select id="selectCouponRuleByRuleId" resultType="java.lang.String">
        select goods_filter from userwx.wx_scrule_product where ruleid = #{ruleId}
    </select>


</mapper>
