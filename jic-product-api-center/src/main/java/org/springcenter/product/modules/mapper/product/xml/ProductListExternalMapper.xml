<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.ProductListExternalMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.ProductListExternal">
        <id column="ID" property="id" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="NAME" property="name" />
        <result column="BRAND" property="brand" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, PRODUCT_ID, NAME, BRAND, CREATE_TIME, UPDATE_TIME, IS_DELETED
    </sql>

    <select id="selectValidNames" resultType="java.lang.String">
        select NAME from PRODUCT_LIST_EXTERNAL where IS_DELETED = 0
    </select>

</mapper>
