package org.springcenter.product.modules.remote;

import org.springcenter.product.modules.remote.entity.EgSearchReqEntity;
import org.springcenter.product.modules.remote.entity.EgSearchRespEntity;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/8/8 10:25
 */
public interface IEgSearchRemoteApi {

    /**
     * 以图搜款
     * @param req
     * @return
     */
    @POST("egsearch")
    Call<JicBaseResp<List<EgSearchRespEntity>>> search(@Body EgSearchReqEntity req);
}
