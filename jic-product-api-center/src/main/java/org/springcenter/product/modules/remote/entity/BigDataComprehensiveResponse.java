package org.springcenter.product.modules.remote.entity;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/4/25 17:31
 */
@ApiModel(description = "大数据综合排序 服务基础返回信息")
@Data
public class BigDataComprehensiveResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @SerializedName("request_id")
    private String request_id;

    @ApiModelProperty(value = "状态码", required = true)
    @SerializedName("code")
    private Integer code;

    @ApiModelProperty(value = "是否使用个性化")
    @SerializedName("personalized")
    private Boolean personalized;

    @ApiModelProperty(value = "是否超出个性化的使用阈值")
    @SerializedName("over_personalized_threshold")
    private Boolean over_personalized_threshold;

    @ApiModelProperty(value = "算法路径")
    @SerializedName("route")
    private Route route;

    @ApiModelProperty(value = "版本号")
    @SerializedName("api_version")
    private String api_version;

    @SerializedName("result")
    private List<BigDataComprehensiveData> result;

    @SerializedName("msg")
    private Route msg;

    @SerializedName("cost_time")
    private BigDecimal cost_time;

    @SerializedName("timestamp")
    private Long timestamp;

    @SerializedName("total")
    private Integer total;

    @Data
    public static class Route {
        @SerializedName("ss")
        private String ss;
    }

    @SerializedName("trace_id")
    private String trace_id;
}
