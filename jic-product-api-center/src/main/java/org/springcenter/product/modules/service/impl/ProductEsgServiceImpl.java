package org.springcenter.product.modules.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.product.api.dto.EsgInfoEs;
import org.springcenter.product.api.dto.EsgRespInfo;
import org.springcenter.product.modules.entity.SearchEsgParam;
import org.springcenter.product.modules.mapper.product.EsgNetDiskMainMapper;
import org.springcenter.product.modules.model.EsgNetDiskDetail;
import org.springcenter.product.modules.service.ProductEsgService;
import org.springcenter.product.modules.util.EsUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2025/3/20 20:35
 */
@Slf4j
@Service
@RefreshScope
public class ProductEsgServiceImpl implements ProductEsgService {


    @Value("${esg.info.index}")
    private String esgInfoIndex;

    @Autowired
    private EsUtil esUtil;

    @Autowired
    private EsgNetDiskMainMapper esgNetDiskMainMapper;


    @Override
    public EsgRespInfo searchEsgInfo(String skcCode) {

        // 根据skc查询当前商品信息
        if (StringUtils.isBlank(skcCode)) {
            return null;
        }
        SearchEsgParam param = new SearchEsgParam();
        param.setSkcCode(skcCode);
        List<EsgInfoEs> infoEs = searchInfoInEs(param);
        if (CollectionUtils.isEmpty(infoEs)) {
            return null;
        }

        // 获取当前skc的相关图片 系列
        Boolean isNotSeries = false;
        String mainId = esgNetDiskMainMapper
                .selectWpPicsByFile1name(infoEs.get(0).getGood_season() == null ? "" : infoEs.get(0).getGood_season(),
                        infoEs.get(0).getFile_label_name() == null ? "" : infoEs.get(0).getFile_label_name(),
                        infoEs.get(0).getTymx_label_name() == null ? "" : infoEs.get(0).getTymx_label_name(),
                        infoEs.get(0).getArc_brand() == null ? "" : infoEs.get(0).getArc_brand());
        if (StringUtils.isBlank(mainId)) {
            isNotSeries = true;
            mainId = esgNetDiskMainMapper
                    .selectWpPicsByFile1name(infoEs.get(0).getGood_season() == null ? "" : infoEs.get(0).getGood_season(),
                            infoEs.get(0).getPro_small_season() == null ? "" : infoEs.get(0).getPro_small_season(),
                            "形象", infoEs.get(0).getArc_brand() == null ? "" : infoEs.get(0).getArc_brand());
        }

        if (StringUtils.isBlank(mainId)) {
            log.info("未查询到当前skc的相关图片 mainId:{}", mainId);
            return null;
        }
        List<EsgNetDiskDetail> pics = esgNetDiskMainMapper.selectPicsByMainId(mainId);

        SearchEsgParam param1 = new SearchEsgParam();
        param1.setGoodSeason(infoEs.get(0).getGood_season());
        param1.setBrand(infoEs.get(0).getArc_brand());
        if (isNotSeries) {
            param1.setSeason(infoEs.get(0).getPro_small_season());
        } else {
            param1.setFile1Name(infoEs.get(0).getFile_label_name());
            param1.setTymx(infoEs.get(0).getTymx_label_name());
        }
        List<EsgInfoEs> infoEs2 = searchInfoInEs(param1);


        // 组装参数
        EsgRespInfo esgRespInfo = new EsgRespInfo();
        BeanUtils.copyProperties(infoEs.get(0), esgRespInfo);

        esgRespInfo.setCarousalPics(CollectionUtils.isNotEmpty(pics) ?
                pics.stream().filter(v -> Objects.equals(v.getType(), 0)).map(EsgNetDiskDetail::getQiniuPath).collect(Collectors.toList()) :
                new ArrayList<>());

        esgRespInfo.setSeries(CollectionUtils.isNotEmpty(pics) ?
                pics.stream().filter(v -> Objects.equals(v.getType(), 2)).map(EsgNetDiskDetail::getQiniuPath).collect(Collectors.toList()) :
                new ArrayList<>());


        esgRespInfo.setVideo(CollectionUtils.isNotEmpty(pics) ?
                pics.stream().filter(v -> Objects.equals(v.getType(), 1)).map(EsgNetDiskDetail::getQiniuPath).collect(Collectors.toList()) :
                new ArrayList<>());

        if (CollectionUtils.isNotEmpty(infoEs2)) {
            List<EsgRespInfo.ProductData> pas = new ArrayList<>();
            infoEs2.forEach(v -> {
                if (v.getMall_id() == null) {
                    return;
                }
                EsgRespInfo.ProductData data = new EsgRespInfo.ProductData();
                data.setPrice(v.getPrice_list());
                data.setMallImg(v.getMall_image());
                data.setMallTitle(v.getMall_title());
                data.setMallId(v.getMall_id());
                data.setIsNew(v.getIs_new());
                pas.add(data);
            });
            if (CollectionUtils.isNotEmpty(pas)) {
                List<EsgRespInfo.ProductData> data = pas.stream().distinct().collect(Collectors.toList());
                esgRespInfo.setProductsList(data);
            }

        }

        return esgRespInfo;
    }



    private List<EsgInfoEs> searchInfoInEs(SearchEsgParam param) {
        SearchRequest request = new SearchRequest();
        request.indices(esgInfoIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        buildQuery(queryBuilder, param);

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(20);
        sourceBuilder.sort("_id", SortOrder.DESC);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品ESG {}", request.source().toString());
        List<EsgInfoEs> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }

            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                EsgInfoEs entity = EsgInfoEs.fromJson(hits[0].getSourceAsString(), EsgInfoEs.class);
                entity.buildServeFunctionData();
                entity.buildWashInfo();
                entity.setPatternUrl(Arrays.stream(StringUtils.split(entity.getUrl(), ";")).collect(Collectors.toList()));
                entity.buildDetailImgsList();
                entity.setWash_info(StringUtils.isBlank(entity.getWash_info()) ? "" : entity.getWash_info().replace("@@", "\n"));
                entities.add(entity);
            }

        } catch (IOException e) {
            log.error("查询商品FAB_FOR_AUTO_NAME异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }

    private void buildQuery(BoolQueryBuilder queryBuilder, SearchEsgParam param) {

        if (StringUtils.isNotBlank(param.getSkcCode())) {
            queryBuilder.must(QueryBuilders.termQuery("_id", param.getSkcCode()));
        }

        if (StringUtils.isNotBlank(param.getTymx())) {
            queryBuilder.must(QueryBuilders.termsQuery("tymx_label_name", param.getTymx()));
        }

        if (StringUtils.isNotBlank(param.getSeason())) {
            queryBuilder.must(QueryBuilders.termsQuery("pro_small_season", param.getSeason()));
        }

        if (StringUtils.isNotBlank(param.getFile1Name())) {
            queryBuilder.must(QueryBuilders.termsQuery("file_label_name", param.getFile1Name()));
            queryBuilder.must(QueryBuilders.existsQuery("mall_id"));
        }

        if (StringUtils.isNotBlank(param.getBrand())) {
            queryBuilder.must(QueryBuilders.termsQuery("arc_brand", param.getBrand()));
        }

        if (StringUtils.isNotBlank(param.getGoodSeason())) {
            queryBuilder.must(QueryBuilders.termsQuery("good_season", param.getGoodSeason()));
        }
    }


}
