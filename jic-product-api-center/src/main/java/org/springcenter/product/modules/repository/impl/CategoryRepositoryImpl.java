package org.springcenter.product.modules.repository.impl;

import org.springcenter.product.modules.mapper.product.DimMapper;
import org.springcenter.product.modules.model.Dim;
import org.springcenter.product.modules.model.MColor;
import org.springcenter.product.modules.repository.ICategoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/29/21 11:17 AM
 */
@Repository
public class CategoryRepositoryImpl implements ICategoryRepository {

    @Autowired
    private DimMapper dimMapper;

    @Override
    public List<Dim> findDims(String dimFlag) {
        Dim dim = new Dim();
        dim.setDimflag(dimFlag);
        return dimMapper.selectListBySelective(dim);
    }

    @Override
    public List<Dim> findAll() {
        Dim dim = new Dim();
        return dimMapper.selectListBySelective(dim);
    }

    @Override
    public List<MColor> selectColorByValue(List<String> list) {
        if (list.isEmpty()) return new ArrayList<>();
        return dimMapper.selectColorByValue(list);
    }
}
