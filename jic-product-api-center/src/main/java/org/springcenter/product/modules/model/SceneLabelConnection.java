package org.springcenter.product.modules.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/5/19 9:20
 */
@Data
@TableName(value = "SCENE_LABEL_CONNECTION")
public class SceneLabelConnection {

    @TableId
    private String id;

    @TableField(value = "LABEL_CODE")
    private String labelCode;

    @TableField(value = "SCENE_ID")
    private String sceneId;

    @TableField(value = "SORT")
    private Integer sort;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    @TableField(value = "OPERATOR")
    private String operator;

}
