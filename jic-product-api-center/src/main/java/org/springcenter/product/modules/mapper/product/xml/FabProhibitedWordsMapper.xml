<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.FabProhibitedWordsMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.FabProhibitedWords">
        <id column="ID" property="id" />
        <result column="WORD" property="word" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>


</mapper>
