package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.constant.RedisKeyConstant;
import org.springcenter.product.api.lenovo.LenovoFileReq;
import org.springcenter.product.api.lenovo.LenovoFileResp;
import org.springcenter.product.api.lenovo.LianXiangTokenDto;
import org.springcenter.product.modules.service.LianXiangYunFilezService;
import org.springcenter.product.modules.util.HttpUtils;
import org.springcenter.product.modules.util.RedisService;
import org.springcenter.product.modules.util.RedissonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
@RefreshScope
public class LianXiangYunFilezServiceImpl implements LianXiangYunFilezService {

    @Autowired
    private RedissonUtil redissonUtil;
    @Autowired
    private RedisService redisService;

    @Value("${lianxiang.appkey}")
    private String appkey;

    @Value("${lianxiang.appSecret}")
    private String appSecret;

    @Value("${lianxiang.token.url}")
    private String tokenUrl;


    @Value("${lianxiang.sync.url}")
    private String syncUrl;

    @Value("${lianxiang.view.url}")
    private String viewUrl;


    @Value("${lianxiang.download.url}")
    private String downloadUrl;


    @Override
    public String getAccessToken() {
        Object accessToken = redisService.get(RedisKeyConstant.REDIS_LIANXIANG_ACCESSTOKEN);
        if(accessToken != null){
            return accessToken.toString();
        }
        // 加锁
        boolean flag = redissonUtil.tryLock(RedisKeyConstant.REDIS_LIANXIANG_LOCK);
        if(!flag){
            throw new RuntimeException("未能获取联想云的锁");
        }
        try {
            String headerApp = appkey + ":" + appSecret;
            byte[] encodedBytes = Base64.getEncoder().encode(headerApp.getBytes(StandardCharsets.UTF_8));
            String encodedString = new String(encodedBytes, StandardCharsets.UTF_8);

            // 拼装参数
            Map<String,String> params = new HashMap<>();
            params.put("grant_type","client_with_su");
            params.put("scope","all");
            params.put("slug","admin");

            Headers headers = new Headers.Builder()
                    .add("Content-Type","application/x-www-form-urlencoded")
                    .add("Authorization","Basic "+encodedString).build();

            String json = HttpUtils.postForm(tokenUrl, params, headers);
            if(StringUtils.isNotBlank(json)){
                // 获取到数据
                LianXiangTokenDto lianXiangTokenDto = JSONObject.parseObject(json, LianXiangTokenDto.class);
                if(StringUtils.isNotBlank(lianXiangTokenDto.getAccess_token())){
                    String expires_in = lianXiangTokenDto.getExpires_in();
                    String value = lianXiangTokenDto.getToken_type() + " " + lianXiangTokenDto.getAccess_token();
                    redisService.set(RedisKeyConstant.REDIS_LIANXIANG_ACCESSTOKEN,
                            value,
                            Long.parseLong(expires_in));
                    return value;
                }
            }
        } finally {
            redissonUtil.unlock(RedisKeyConstant.REDIS_LIANXIANG_LOCK);
        }
        return null;
    }

    @Override
    public LenovoFileResp getLenovoFile(LenovoFileReq lenovoFileReq) {
        try {
            String jsonString = JSONObject.toJSONString(lenovoFileReq);
            Map<String,String> params = JSONObject.parseObject(jsonString, Map.class);

            Headers headers = new Headers.Builder()
                    .add("Authorization",getAccessToken()).build();

            String json = HttpUtils.postForm(syncUrl, params, headers);
            if(StringUtils.isNotBlank(json)){
                LenovoFileResp lenovoFileResp = JSONObject.parseObject(json, LenovoFileResp.class);
                return lenovoFileResp;
            }
        }catch (Exception e){
            log.info("getLenovoFile e = {}",e);
             return null;
        }
        return null;
    }

    @Override
    public String getPdfView(String neid, String nsid) {
        try {
            Map<String,String> params = new HashMap<>();
            params.put("nsid",nsid);

            Headers headers = new Headers.Builder()
                    .add("Authorization",getAccessToken()).build();

            String finalUrl = viewUrl + "/" + neid;

            String json = HttpUtils.postForm(finalUrl, params, headers);
            if(StringUtils.isNotBlank(json)){
                Map<String,String> lenovoFileResp = JSONObject.parseObject(json, Map.class);
                return lenovoFileResp.get("previewUrl");
            }
        }catch (Exception e){
            log.info("getPdfView e = {}",e);
            return null;
        }
        return null;
    }

    @Override
    public String download(String neid, String nsid) {
        try {
            Map<String,String> params = new HashMap<>();
            params.put("nsid",nsid);
            params.put("neid",neid);

            Headers headers = new Headers.Builder()
                    .add("Authorization",getAccessToken()).build();

            String finalUrl = HttpUtils.postFormDownLoad(downloadUrl, params, headers);

//            ByteArrayOutputStream baos = new ByteArrayOutputStream();
//            int len ;
//            byte[] buffer = new byte[1024];        //缓冲区
//            while((len = inputStream.read(buffer))!=-1) {   //将接受的数据写入缓冲区数组buffer
//                baos.write(buffer,0,len);          //将缓冲区buffer写入byte数组输出流
//            }
//            inputStream.close();
//            baos.close();

            return finalUrl;  //将以byte数组返回输出流中的有效内容
        }catch (Exception e){
            log.info("download e = {}",e);
            return null;
        }
    }
}
