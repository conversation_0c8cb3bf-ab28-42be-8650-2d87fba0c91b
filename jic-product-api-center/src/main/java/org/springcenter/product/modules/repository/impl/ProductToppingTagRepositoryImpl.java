package org.springcenter.product.modules.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.collections.CollectionUtils;
import org.springcenter.product.api.dto.QueryToppingTagListReq;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.modules.mapper.product.ProductToppingTagLogMapper;
import org.springcenter.product.modules.mapper.product.ProductToppingTagMapper;
import org.springcenter.product.modules.model.ProductToppingTag;
import org.springcenter.product.modules.model.ProductToppingTagLog;
import org.springcenter.product.modules.repository.IProductToppingTagRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/5/15 10:24
 */
@Repository
public class ProductToppingTagRepositoryImpl implements IProductToppingTagRepository {

    @Autowired
    private ProductToppingTagMapper productToppingTagMapper;

    @Autowired
    private ProductToppingTagLogMapper productToppingTagLogMapper;

    @Override
    public List<String> selectBySpuList(List<String> spuList) {
        QueryWrapper<ProductToppingTag> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        queryWrapper.in("SPU", spuList);
        List<ProductToppingTag> tags = productToppingTagMapper.selectList(queryWrapper);
        return CollectionUtils.isEmpty(tags) ? Collections.emptyList() :
                tags.stream().map(ProductToppingTag::getSpu).collect(Collectors.toList());
    }

    @Override
    public void saveProductToppingTag(List<ProductToppingTag> productToppingTags) {
        productToppingTagMapper.batchInsert(productToppingTags);
    }

    @Override
    public void saveProductToppingTagLog(List<ProductToppingTagLog> productToppingTagLogs) {
        productToppingTagLogMapper.batchInsert(productToppingTagLogs);
    }

    @Override
    public ProductToppingTag selectBySpu(String spu) {
        QueryWrapper<ProductToppingTag> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("SPU", spu);
        return productToppingTagMapper.selectOne(queryWrapper);
    }

    @Override
    public void updateProductToppingTag(ProductToppingTag tag) {
        productToppingTagMapper.updateById(tag);
    }

    @Override
    public ProductToppingTag searchToppingTag(String id) {
        return productToppingTagMapper.selectById(id);
    }

    @Override
    public List<ProductToppingTag> queryToppingTagList(QueryToppingTagListReq requestData) {
        return productToppingTagMapper.selectByParam(requestData.getSpu(), requestData.getType(), requestData.getStatus(), new Date());
    }

    @Override
    public List<ProductToppingTag> selectNotStartToppingTag() {
        return productToppingTagMapper.selectListByNotStart(new Date());
    }

    @Override
    public void batchUpdateToppingTag(List<ProductToppingTag> productToppingTags) {
        productToppingTagMapper.batchUpdate(productToppingTags);
    }

    @Override
    public List<ProductToppingTag> selectFinishedToppingTagAndNotFlow() {
        return productToppingTagMapper.selectFinishedToppingTagAndNotFlow(new Date());
    }

    @Override
    public List<ProductToppingTag> selectFinishedToppingTagAndFlow() {
        return productToppingTagMapper.selectFinishedToppingTagAndFlow(new Date());
    }


}
