package org.springcenter.product.modules.mapper.product;

import org.springcenter.product.modules.model.CArcbrand;

import java.util.List;

public interface CArcbrandMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CArcbrand record);

    int insertSelective(CArcbrand record);

    CArcbrand selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CArcbrand record);

    int updateByPrimaryKey(CArcbrand record);

    List<CArcbrand> selectActiveList();
}