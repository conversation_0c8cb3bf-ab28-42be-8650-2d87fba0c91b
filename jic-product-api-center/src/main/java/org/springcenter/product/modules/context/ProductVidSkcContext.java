package org.springcenter.product.modules.context;

import lombok.Data;
import org.springcenter.product.modules.model.zt.WeiMoBStorage;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/8/11 20:18
 */
@Data
public class ProductVidSkcContext {

    private String vid;

    private Long productId;

    private Long skuId;

    private Long id;

    private Integer sumQty;

    private Integer skuQty;

    private List<WeiMoBStorage> sumWmStorages;

    public static ProductVidSkcContext build(String vid, long productId, long skuId, int sumQty,
                                             List<WeiMoBStorage> sumWmStorages, int skuQty, long id) {
        ProductVidSkcContext context = new ProductVidSkcContext();
        context.setProductId(productId);
        context.setVid(vid);
        context.setId(id);
        context.setSkuQty(skuQty);
        context.setSumQty(sumQty);
        context.setSkuId(skuId);
        context.setSumWmStorages(sumWmStorages);
        return context;
    }
}
