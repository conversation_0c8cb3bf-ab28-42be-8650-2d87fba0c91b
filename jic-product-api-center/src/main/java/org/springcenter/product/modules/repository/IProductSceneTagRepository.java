package org.springcenter.product.modules.repository;

import org.springcenter.product.api.dto.DisassociateSceneLabelsReq;
import org.springcenter.product.api.dto.UpdateLabelSceneReq;
import org.springcenter.product.modules.model.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/19 10:56
 */
public interface IProductSceneTagRepository {
    /**
     * 保存场景标签关系
     * @param connection 关系
     */
    void saveLabelSceneConnections(List<SceneLabelConnection> connection);

    /**
     * 查询渠道配置参数
     * @return 返回
     */
    List<SceneChannelConfig> querySceneChannelConfig();

    /**
     * 通过渠道id查询场景的渠道
     * @param id 渠道id
     * @return 返回
     */
    SceneChannelConfig querySceneChannelConfigById(Integer id);

    /**
     * 根据渠道id查询相关场景
     * @param channelId 渠道id
     * @return 返回
     */
    List<ProductSceneLabel> queryLabelSceneByChannelId(Integer channelId);

    /**
     * 保存应用场景
     * @param sceneLabel 应用场景实体
     */
    void saveProductSceneLabel(ProductSceneLabel sceneLabel);

    /**
     * 保存应用场景日志
     * @param sceneLabelLog 应用场景日志
     */
    void saveProductSceneLabelLog(ProductSceneLabelLog sceneLabelLog);

    /**
     * 返回信息
     * @param id 场景id
     * @return 返回信息
     */
    ProductSceneLabel queryProductSceneLabel(String id);

    /**
     * 更新场景
     * @param productLabelScene 场景
     */
    void updateProductSceneLabel(ProductSceneLabel productLabelScene);

    /**
     * 根据场景id查询关联的标签
     * @param id 场景id
     * @return 返回关联的列表
     */
    List<SceneLabelConnection> querySceneLabelConnectionBySceneId(String id);

    /**
     * 根据场景标签id查询场景标签
     * @param id id
     * @return 返回
     */
    SceneLabelConnection querySceneLabelConnectionById(String id);

    /**
     * 场景标签关联关系
     * @param connection 参数
     * @return 返回
     */
    void updateSceneConfigConnection(String name, List<DisassociateSceneLabelsReq> list);

    /**
     * 根据参数查询场景
     * @param sceneName 模糊查询参数
     * @param ownChannelId 渠道id
     * @return 返回
     */
    List<ProductSceneLabel> selectSceneListByParam(String sceneName, Integer ownChannelId);

    /**
     * 根据标签code 将父级code解除关联
     * @param labelCode 入参
     */
    void updateDisRelLabelCodeByLabelCode(String labelCode, String name);

    /**
     * 根据labelcode 查询相关标签
     * @param labelCode
     * @return
     */
    List<SceneLabelConnection> selectSceneByLabelCode(String labelCode);

    /**
     * 根据场频id查询场景数据
     * @param sceneIds
     */
    List<ProductSceneLabelEntity> selectSceneLabelById(List<String> sceneIds);

    /**
     * 更新数据
     * @param updateList 更新数据
     * @param finalName 名称
     */
    void updateLabelSceneConnections(List<UpdateLabelSceneReq.updateDataEntity> updateList, String finalName);

    /**
     * 根据key查询标签
     * @param id 场景key
     * @return 返回
     */
    List<SceneLabelConnection> querySceneLabelConnectionBySceneKey(String id);
}
