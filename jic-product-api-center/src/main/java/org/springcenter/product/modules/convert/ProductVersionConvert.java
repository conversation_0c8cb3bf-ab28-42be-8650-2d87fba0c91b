package org.springcenter.product.modules.convert;


import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.api.enums.MQueriesTabEnum;
import org.springcenter.product.modules.model.*;
import org.springcenter.product.modules.repository.IMQueriesTabRepository;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2022/12/2 13:36
 */
public class ProductVersionConvert {
    public static ProductVersionQueriesTabResp build(List<MQueriesTab> brandList, List<MQueriesTab> categoryList,
                                                     List<MQueriesTab> attributeList) {
        ProductVersionQueriesTabResp resp = new ProductVersionQueriesTabResp();
        if (CollectionUtils.isEmpty(brandList) || CollectionUtils.isEmpty(categoryList) || CollectionUtils.isEmpty(attributeList)) {
            return resp;
        }
        //组装品牌
        List<ProductVersionQueriesTabResp.BaseModel> brandListRet = new ArrayList<>();
        brandList.forEach(v -> {
            ProductVersionQueriesTabResp.BaseModel data = new ProductVersionQueriesTabResp.BaseModel();
            data.setId(Objects.toString(v.getId()));
            data.setCode(v.getCode());
            data.setName(v.getName());
            data.setPid(Objects.toString(v.getPid()));
            brandListRet.add(data);
        });
        resp.setBrandList(brandListRet);

        //组装类目和属性信息
        List<ProductVersionQueriesTabResp.BaseModel> categoryListRet = assembleCategory(categoryList, attributeList);
        resp.setCategoryList(categoryListRet);
        return resp;
    }

    private static Map<String, List<ProductVersionQueriesTabResp.BaseModel>> assembleAttribute(
                                                    List<MQueriesTab> attributeList, List<MQueriesTab> mainCategoryList) {
        List<ProductVersionQueriesTabResp.BaseModel> attributeListRet = new ArrayList<>();
        List<MQueriesTab> mainAttributes = new ArrayList<>();
        List<Long> ids = mainCategoryList.stream().map(MQueriesTab::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        attributeList.forEach(v -> {
            if (ids.contains(v.getPid())) {
                mainAttributes.add(v);
            }
        });
        if (CollectionUtils.isEmpty(mainAttributes)) {
            return null;
        }
        //所有子属性
        List<MQueriesTab> allMainAttributes = new ArrayList<>();
        /*mainAttributes.forEach(v -> {
            if (StringUtils.isNotBlank(v.getPTypeSubId())) {
                List<String> list = Arrays.asList(v.getPTypeSubId().split(","));
                if (CollectionUtils.isEmpty(list)) {
                    return;
                }
                list.forEach(x -> {
                    MQueriesTab mQueriesTab = new MQueriesTab();
                    BeanUtils.copyProperties(v, mQueriesTab);
                    mQueriesTab.setPid(Integer.valueOf(x));
                    allMainAttributes.add(mQueriesTab);
                });
            }
        });*/
        if (CollectionUtils.isEmpty(allMainAttributes)) {
            return null;
        }
        allMainAttributes.forEach(v -> {
            ProductVersionQueriesTabResp.BaseModel data = new ProductVersionQueriesTabResp.BaseModel();
            data.setId(Objects.toString(v.getId()));
            data.setCode(v.getCode());
            data.setName(v.getName());
            data.setPid(Objects.toString(v.getPid()));
            //查找子级
            List<MQueriesTab> subAttributesList = attributeList.stream()
                    .filter(x -> {
                        boolean b = (x.getPid() != null && Objects.equals(x.getPid(), v.getId()));
                        return b;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subAttributesList)) {
                attributeListRet.add(data);
            } else {
                List<ProductVersionQueriesTabResp.BaseModel> attributeChildList = new ArrayList<>();
                subAttributesList.forEach(x -> {
                    ProductVersionQueriesTabResp.BaseModel childData = new ProductVersionQueriesTabResp.BaseModel();
                    childData.setId(Objects.toString(x.getId()));
                    childData.setCode(x.getCode());
                    childData.setName(x.getName());
                    childData.setPid(Objects.toString(x.getPid()));
                    attributeChildList.add(childData);
                });
                data.setChildren(attributeChildList);
            }
            attributeListRet.add(data);
        });
        if (CollectionUtils.isEmpty(attributeListRet)) {
            return null;
        }
        return attributeListRet.stream().collect(Collectors.groupingBy(ProductVersionQueriesTabResp.BaseModel::getPid));
    }

    private static List<ProductVersionQueriesTabResp.BaseModel> assembleCategory(List<MQueriesTab> categoryList,
                                                                                 List<MQueriesTab> attributeList) {
        List<ProductVersionQueriesTabResp.BaseModel> categoryListRet = new ArrayList<>();
        //过滤主类目
        List<MQueriesTab> mainCategoryList = categoryList.stream().filter(v -> v.getPid() == null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainCategoryList)) {
            return categoryListRet;
        }
        //处理属性值
        Map<String, List<ProductVersionQueriesTabResp.BaseModel>> map = assembleAttribute(attributeList, mainCategoryList);

        mainCategoryList.forEach(v -> {
            ProductVersionQueriesTabResp.BaseModel data = new ProductVersionQueriesTabResp.BaseModel();
            data.setId(Objects.toString(v.getId()));
            data.setCode(v.getCode());
            data.setName(v.getName());
            data.setPid(Objects.toString(v.getPid()));
            //查找子级
            List<MQueriesTab> subCategoryList = categoryList.stream()
                    .filter(x -> {
                        boolean b = x.getPid() != null && Objects.equals(x.getPid(), v.getId());
                        return b;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subCategoryList)) {
                categoryListRet.add(data);
            } else {
                List<ProductVersionQueriesTabResp.BaseModel> categoryChildList = new ArrayList<>();
                subCategoryList.forEach(x -> {
                    ProductVersionQueriesTabResp.BaseModel childData = new ProductVersionQueriesTabResp.BaseModel();
                    childData.setId(Objects.toString(x.getId()));
                    childData.setCode(x.getCode());
                    childData.setName(x.getName());
                    childData.setPid(Objects.toString(x.getPid()));
                    if (map != null && !map.isEmpty()) {
                        List<ProductVersionQueriesTabResp.BaseModel> attribute = map.get(Objects.toString(x.getId()));
                        //childData.setAttributeList(attribute);
                    }
                    categoryChildList.add(childData);
                });
                data.setChildren(categoryChildList);
            }
            categoryListRet.add(data);
        });
        return categoryListRet;
    }

    public static ProductVersionQueriesTabResp buildRet(List<MQueriesTab> brandList, List<MQueriesTab> categoryList,
                                                        List<MQueriesTab> attributeList,  List<MQueriesTab> filterList,
                                                        List<String> brands) {
        ProductVersionQueriesTabResp resp = new ProductVersionQueriesTabResp();
        //组织品牌信息
        List<ProductVersionQueriesTabResp.BaseModel> brandListRet = new ArrayList<>();
        brandList.forEach(v -> {
            ProductVersionQueriesTabResp.BaseModel data = new ProductVersionQueriesTabResp.BaseModel();
            data.setId(Objects.toString(v.getId()));
            data.setCode(v.getCode());
            data.setName(v.getName());
            data.setPid(Objects.toString(v.getPid()));
            if (CollectionUtils.isNotEmpty(brands)) {
                if (brands.contains(v.getCode())) {
                    brandListRet.add(data);
                }
            } else {
                brandListRet.add(data);
            }

        });
        resp.setBrandList(brandListRet);

        //组织类目信息
        List<ProductVersionQueriesTabResp.BaseModel> categoryRet = commonDeal(categoryList);
        resp.setCategoryList(categoryRet);
        //组织属性信息
        List<ProductVersionQueriesTabResp.BaseModel> attributeRet = commonDeal(attributeList);

        //组织过滤信息
        List<ProductVersionQueriesTabResp.BaseModel> filterRet = new ArrayList<>();
        if (CollectionUtils.isEmpty(filterList)) {
            resp.setFilterList(new ArrayList<>());
        }
        filterList.forEach(v -> {
            ProductVersionQueriesTabResp.BaseModel data = new ProductVersionQueriesTabResp.BaseModel();
            data.setId(Objects.toString(v.getId()));
            data.setCode(v.getCode());
            data.setName(v.getName());
            data.setPid(Objects.toString(v.getPid()));
            filterRet.add(data);
        });
        resp.setBrandList(brandListRet);
        resp.setAttributeList(attributeRet);
        resp.setFilterList(filterRet);
        return resp;
    }

    private static List<ProductVersionQueriesTabResp.BaseModel> commonDeal(List<MQueriesTab> commonList) {
        List<ProductVersionQueriesTabResp.BaseModel> commonListRet = new ArrayList<>();
        //过滤主
        List<MQueriesTab> mainCommonList = commonList.stream().filter(v -> v.getPid() == null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainCommonList)) {
            return commonListRet;
        }

        mainCommonList.forEach(v -> {
            ProductVersionQueriesTabResp.BaseModel data = new ProductVersionQueriesTabResp.BaseModel();
            data.setId(Objects.toString(v.getId()));
            data.setCode(v.getCode());
            data.setName(v.getName());
            data.setPid(Objects.toString(v.getPid()));
            //查找子级
            List<MQueriesTab> subCategoryList = commonList.stream()
                    .filter(x -> {
                        boolean b = x.getPid() != null && Objects.equals(x.getPid(), v.getId());
                        return b;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subCategoryList)) {
                commonListRet.add(data);
            } else {
                List<ProductVersionQueriesTabResp.BaseModel> commonChildList = new ArrayList<>();
                subCategoryList.forEach(x -> {
                    ProductVersionQueriesTabResp.BaseModel childData = new ProductVersionQueriesTabResp.BaseModel();
                    childData.setId(Objects.toString(x.getId()));
                    childData.setCode(x.getCode());
                    childData.setName(x.getName());
                    childData.setPid(Objects.toString(x.getPid()));
                    commonChildList.add(childData);
                });
                data.setChildren(commonChildList);
                commonListRet.add(data);
            }

        });
        return commonListRet;
    }

    public static List<ProductVersionListDataResp> buildListRet(List<ProductVersionEntity> rets,
                                                                Triple<List<MQueriesTab>, List<MQueriesTab>, List<MQueriesTab>> tripList,
                                                                List<MProductVersionPic> mProductVersionPics,
                                                                HashMap<String, SampleProductSkcResp> picMap,
                                                                HashMap<String, GoodSpuResp> goodPicMap,
                                                                List<MPQueAttrValueConnect> connects) {
        List<ProductVersionListDataResp> returnList = new ArrayList<>();
        if (CollectionUtils.isEmpty(rets)) {
            return returnList;
        }

        Map<String, String> attrMap = null;
        if (CollectionUtils.isNotEmpty(connects)) {
            attrMap = connects.stream().collect(HashMap::new, (k, v) -> k.put(v.getMvProductId(), v.getValueName()), HashMap::putAll);
        }

        Map<String, String> finalAttrMap = attrMap;
        rets.forEach(v -> {
            ProductVersionListDataResp resp = new ProductVersionListDataResp();
            BeanUtils.copyProperties(v, resp);
            SampleProductSkcResp sampleProductSkcResp = null;
            GoodSpuResp goodSpuResp = null;
            if (StringUtils.isNotBlank(v.getSampleNo()) && picMap != null) {
                sampleProductSkcResp = picMap.get(v.getSampleNo());
            }
            if (StringUtils.isNotBlank(v.getSampleNo()) && goodPicMap != null) {
                goodSpuResp = goodPicMap.get(v.getSampleNo());
            }
            if (sampleProductSkcResp != null) {
                resp.setBrand(sampleProductSkcResp.getBrand());
                resp.setSkc_id(sampleProductSkcResp.getSkc_id());
            }
            if (goodSpuResp != null) {
                resp.setPic(StringUtils.isBlank(goodSpuResp.getName()) ? "" : goodSpuResp.getCover_imgs());
            }
            //处理类目名称和合体度名称
            MQueriesTab category =  tripList.getMiddle().stream()
                    .filter(x -> Objects.equals(x.getCode(), v.getCategory()))
                    .findFirst()
                    .orElse(null);
            resp.setCategory(category == null ? "" : category.getName());
            if (finalAttrMap == null || StringUtils.isBlank(finalAttrMap.get(v.getId()))) {
                resp.setDegree("");
            } else {
                resp.setDegree(finalAttrMap.get(v.getId()));
            }

            returnList.add(resp);
        });
        return returnList;
    }




    public static ProductVersionDetailResp buildDetail(MProductVersion mProductVersion,
                                                       List<MQueriesAttrConEntity> attrs, List<GoodSpuResp> goodSpuResps,
                                                       MQueriesTab category,
                                                       Integer citations, Integer salesNumber,
                                                       List<SampleProductSkcResp> sampleProductSkcResp,
                                                       Triple<Map<String, String>, Map<String, String>, Map<String, List<String>>> triple) {
        ProductVersionDetailResp ret = new ProductVersionDetailResp();
        ret.setCitationNumber(Objects.toString(citations));
        ret.setSoldNumber(Objects.toString(salesNumber));
        ret.setBrandCode(mProductVersion.getBrandCode());
        ret.setBranCodeName(mProductVersion.getBrand());
        ret.setModelNumber(mProductVersion.getModelNumber());
        ret.setSampleNo(mProductVersion.getSampleNo());
        ret.setId(mProductVersion.getId());
        ret.setIsClosed(mProductVersion.getIsClose());
        ret.setSmallName(mProductVersion.getSmallName());
        ret.setCategoryCode(category.getCode());
        ret.setCategoryName(category.getName());
        ret.setMainIngredientName(mProductVersion.getMainIngredient());
        ret.setMainIngredientCode(mProductVersion.getMainIngredientNumber());


        //大货号
        if (CollectionUtils.isNotEmpty(goodSpuResps)) {
            if (triple != null) {
                if (MapUtils.isNotEmpty(triple.getLeft()) && StringUtils.isNotBlank(triple.getLeft().get(mProductVersion.getSampleNo()))) {
                    if (MapUtils.isNotEmpty(triple.getMiddle()) &&
                            StringUtils.isNotBlank(triple.getMiddle().get(triple.getLeft().get(mProductVersion.getSampleNo())))) {
                        ret.setMainPic(triple.getMiddle().get(triple.getLeft().get(mProductVersion.getSampleNo())));
                    }
                }

                if (MapUtils.isNotEmpty(triple.getRight()) && CollectionUtils.isNotEmpty(triple.getRight().get(mProductVersion.getSampleNo()))) {
                    if (MapUtils.isNotEmpty(triple.getMiddle())) {
                        List<String> urls = new ArrayList<>();
                        triple.getRight().get(mProductVersion.getSampleNo()).forEach(v -> {
                            String s = triple.getMiddle().get(v);
                            if (StringUtils.isNotBlank(s)) {
                                urls.add(s);
                            }
                        });
                        if (CollectionUtils.isNotEmpty(urls)) {
                            ret.setPics(urls.stream().collect(Collectors.joining(",")));
                        }
                    }
                }

            } else {
                ret.setMainPic(StringUtils.isBlank(ret.getSpu()) ? "" : goodSpuResps.get(0).getCover_imgs());
                ret.setPics(StringUtils.isBlank(ret.getSpu()) ? "" : goodSpuResps.get(0).getBanner_imgs());
            }

            ret.setSpu(Objects.toString(goodSpuResps.get(0).getName()));
            ret.setProductId(StringUtils.isBlank(ret.getSpu()) ? "" : Objects.toString(goodSpuResps.get(0).getId()));
            ret.setDetailImgs(StringUtils.isBlank(ret.getSpu()) ? "" : goodSpuResps.get(0).getDetail_imgs());
        }

        //存放图片
        if (CollectionUtils.isNotEmpty(sampleProductSkcResp)) {
            ret.setBrand(sampleProductSkcResp.get(0).getBrand());
            ret.setSkc_id(sampleProductSkcResp.get(0).getSkc_id());
        }

        // 组装属性
        List<ProductVersionDetailResp.BaseInfo> attrInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attrs)) {
            List<MQueriesAttrConEntity> firstAttr = attrs.stream().filter(v -> Objects.equals(v.getType(), 1))
                    .collect(Collectors.toList());
            List<MQueriesAttrConEntity> sceAttr = attrs.stream().filter(v -> Objects.equals(v.getType(), 2))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(firstAttr)) {
                return ret;
            }
            Map<Long, List<MQueriesAttrConEntity>> map = null;
            if (CollectionUtils.isNotEmpty(sceAttr)) {
                map = sceAttr.stream().collect(Collectors.groupingBy(MQueriesAttrConEntity::getAttributeId));
            }
            Map<Long, List<MQueriesAttrConEntity>> finalMap = map;
            firstAttr.forEach(v -> {
                ProductVersionDetailResp.BaseInfo data = new ProductVersionDetailResp.BaseInfo();
                data.setAttrName(v.getAttributeName());
                data.setAttrCode(v.getAttributeCode());
                data.setValueCode(v.getValue());
                data.setValueName(v.getValueName());
                data.setId(v.getId());
                List<MQueriesAttrConEntity> entities = finalMap.get(v.getAttributeId());
                List<ProductVersionDetailResp.BaseInfo> children = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(entities)) {
                    entities.forEach(x -> {
                        ProductVersionDetailResp.BaseInfo childData = new ProductVersionDetailResp.BaseInfo();
                        childData.setId(x.getId());
                        childData.setAttrCode(x.getConnectAttributeCode());
                        childData.setAttrName(x.getConnectAttributeName());
                        childData.setValueCode(x.getValue());
                        childData.setValueName(x.getValueName());
                        children.add(childData);
                    });
                }
                data.setChild(children);
                attrInfos.add(data);
            });
        }
        ret.setAttrs(attrInfos);
        return ret;
    }

    public static ProductVersionSalesResp buildSalesResp(List<MProductVersionSales> mProductVersionSales,
                                                         com.github.pagehelper.Page<Object> hPage, Page page,
                                                         Pair<BigDecimal, BigDecimal> pairs,
                                                         HashMap<String, GoodSpuResp> spuMap) {
        ProductVersionSalesResp resp = new ProductVersionSalesResp();
        List<ProductVersionSalesResp.SalesEntity> retList = new ArrayList<>();
        if (CollectionUtils.isEmpty(mProductVersionSales)) {
            resp.setSumSalesNumber("0");
            resp.setSumSalesPrice("0");
            resp.setSalesEntities(retList);
        }
        mProductVersionSales.forEach(v -> {
            ProductVersionSalesResp.SalesEntity data = new ProductVersionSalesResp.SalesEntity();
            data.setSpu(v.getSpu());
            data.setPic(v.getPic());
            data.setSalesPrice(Objects.toString(v.getSalesPrice()));
            data.setSalesNumber(Objects.toString(v.getSalesNumber()));
            data.setCategoryName(v.getCategoryName());
            data.setProductId(v.getProductId());
            GoodSpuResp goodSpuResp = spuMap.get(v.getSpu());
            data.setPrice(goodSpuResp == null ? Objects.toString(v.getPrice()) : Objects.toString(goodSpuResp.getPrice()));
            retList.add(data);
        });
        resp.setSalesEntities(retList);

        resp.setSumSalesNumber(Objects.toString(pairs.getLeft()));

        resp.setSumSalesPrice(Objects.toString(pairs.getRight()));

        PageInfo<ProductVersionSalesResp.SalesEntity> pageInfo = new PageInfo(hPage);
        pageInfo.setList(retList);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return resp;
    }

    public static ProductVersionSalesQueriesTabResp buildSalesQueries(List<MQueriesTab> salesListFilter, List<MProductVersion> mProductVersionSales) {
        ProductVersionSalesQueriesTabResp ret = new ProductVersionSalesQueriesTabResp();
        List<String> modelNumberList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mProductVersionSales)) {
            modelNumberList = mProductVersionSales.stream()
                    .map(MProductVersion::getModelNumber).distinct().collect(Collectors.toList());
        }
        ret.setModelNumberList(modelNumberList);

        if (CollectionUtils.isEmpty(salesListFilter)) {
            ret.setFilterList(new ArrayList<>());
            return ret;
        }

        List<ProductVersionSalesQueriesTabResp.Base> salesFilter = new ArrayList<>();
        salesListFilter.forEach(v -> {
            ProductVersionSalesQueriesTabResp.Base data = new ProductVersionSalesQueriesTabResp.Base();
            data.setCode(v.getCode());
            data.setName(v.getName());
            salesFilter.add(data);
        });
        ret.setFilterList(salesFilter);
        return ret;
    }

    public static ProductVersionEditDicResp buildProductEditDic(List<MQueriesTab> brandList,
                                                                List<MQueriesTab> attributeList,
                                                                List<MQueriesTab> subAttributeList,
                                                                List<MQueriesAttrConnection> connects) {
        ProductVersionEditDicResp resp = new ProductVersionEditDicResp();
        //组织品牌信息
        List<ProductVersionEditDicResp.BaseModel> brandListRet = new ArrayList<>();
        brandList.forEach(v -> {
            ProductVersionEditDicResp.BaseModel data = new ProductVersionEditDicResp.BaseModel();
            data.setId(Objects.toString(v.getId()));
            data.setCode(v.getCode());
            data.setName(v.getName());
            data.setPid(Objects.toString(v.getPid()));
            brandListRet.add(data);
        });
        resp.setBrandList(brandListRet);

        //处理主属性
        if (CollectionUtils.isEmpty(connects)) {
            return resp;
        }
        List<MQueriesAttrConnection> mainAttr = connects.stream().filter(v -> Objects.equals(v.getType(), 1))
                .collect(Collectors.toList());
        List<MQueriesAttrConnection> sceAttr = connects.stream().filter(v -> Objects.equals(v.getType(), 2))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainAttr) || CollectionUtils.isEmpty(attributeList)) {
            return resp;
        }
        HashMap<String, MQueriesTab> map = attributeList.stream().filter(v -> Objects.isNull(v.getPid()))
                .collect(HashMap::new, (k, v) -> k.put(v.getCode(), v), HashMap::putAll);
        Map<String, List<MQueriesAttrConnection>> sceMap = null;
        if (CollectionUtils.isNotEmpty(sceAttr)) {
            sceMap = sceAttr.stream().collect(Collectors.groupingBy(MQueriesAttrConnection::getAttributeCode));
        }
        List<ProductVersionEditDicResp.BaseModel> mainAttrList = new ArrayList<>();
        Map<String, List<MQueriesAttrConnection>> finalSceMap = sceMap;

        mainAttr.forEach(v -> {
            if (map.isEmpty() || map.get(v.getAttributeCode()) == null) {
                return;
            }
            MQueriesTab mQueriesTab = map.get(v.getAttributeCode());
            if (mQueriesTab == null) {
                return;
            }
            ProductVersionEditDicResp.BaseModel mainAttrData = new ProductVersionEditDicResp.BaseModel();
            mainAttrData.setName(mQueriesTab.getName());
            mainAttrData.setCode(mQueriesTab.getCode());
            mainAttrData.setId(Objects.toString(mQueriesTab.getId()));
            mainAttrData.setPid(Objects.toString(mQueriesTab.getPid()));
            mainAttrData.setField(mQueriesTab.getAttrField());

            // 获取值
            List<MQueriesTab> list = attributeList.stream()
                    .filter(x -> Objects.equals(x.getPid(), mQueriesTab.getId())).collect(Collectors.toList());
            List<ProductVersionEditDicResp.BaseModel> childList = new ArrayList<>();
            list.forEach(u -> {
                ProductVersionEditDicResp.BaseModel childData = new ProductVersionEditDicResp.BaseModel();
                childData.setPid(Objects.toString(u.getPid()));
                childData.setName(u.getName());
                childData.setCode(u.getCode());
                childData.setId(Objects.toString(u.getId()));
                childList.add(childData);
            });

            mainAttrData.setChildList(childList);

            // 二级属性
            List<ProductVersionEditDicResp.BaseModel> subAttrList = new ArrayList<>();
            if (finalSceMap == null || CollectionUtils.isEmpty(finalSceMap.get(v.getAttributeCode()))) {
                if (mainAttrList.stream().anyMatch(x -> Objects.equals(x.getCode(), mainAttrData.getCode()))) {
                    return;
                }
                mainAttrList.add(mainAttrData);
                return;
            }
            finalSceMap.get(v.getAttributeCode()).forEach(x -> {
                MQueriesTab tab = subAttributeList.stream().filter(o -> Objects.equals(o.getId(), x.getConnectAttributeId())).findFirst().orElse(null);
                if (tab == null) {
                    return;
                }
                ProductVersionEditDicResp.BaseModel subAttrData = new ProductVersionEditDicResp.BaseModel();
                subAttrData.setPid(Objects.toString(tab.getPid()));
                subAttrData.setName(tab.getName());
                subAttrData.setCode(tab.getCode());
                subAttrData.setField(tab.getAttrField());
                subAttrData.setId(Objects.toString(tab.getId()));
                subAttrList.add(subAttrData);
            });


            mainAttrData.setSubAttributeList(subAttrList);
            mainAttrList.add(mainAttrData);
        });


        resp.setAttributeList(mainAttrList);
        return resp;
    }

    public static List<PVQueryStatsResp> buildProductVersionQueryStatsResp(List<MQueriesTab> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return Collections.emptyList();
        }
        List<PVQueryStatsResp> rets = new ArrayList<>();
        categoryList.forEach(v -> {
            PVQueryStatsResp resp = new PVQueryStatsResp();
            resp.setDesc(v.getName());
            resp.setId(v.getId());
            rets.add(resp);
        });
        return rets;
    }

    public static List<PVQueryStatsListResp> buildStatsList(List<MQueriesAttrConnection> list,
                                                            List<MQueriesAttrConnection> attributeInfo,
                                                            IMQueriesTabRepository mQueriesTabRepository) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        Map<Long, List<MQueriesAttrConnection>> categoryMap = null;
        if (CollectionUtils.isNotEmpty(attributeInfo)) {
            categoryMap = attributeInfo.stream()
                    .filter(x -> Objects.equals(x.getType(), 1))
                    .collect(Collectors.groupingBy(MQueriesAttrConnection::getAttributeId));
        }


        List<PVQueryStatsListResp> rets = new ArrayList<>();
        Map<Long, List<MQueriesAttrConnection>> finalCategoryMap = categoryMap;
        list.forEach(v -> {
            PVQueryStatsListResp listResp = new PVQueryStatsListResp();
            listResp.setFirstAttribute(v.getAttributeName());
            listResp.setAttributeId(v.getAttributeId());
            listResp.setIsDeleted(v.getIsDelete());
            StringBuffer category = new StringBuffer();
            if (finalCategoryMap != null) {
                List<MQueriesAttrConnection> connections = finalCategoryMap.get(v.getAttributeId());
                if (CollectionUtils.isNotEmpty(connections)) {
                    connections.stream().collect(Collectors.groupingBy(MQueriesAttrConnection::getBigCategoryName))
                            .entrySet().forEach(x -> {
                                if (StringUtils.isBlank(category)) {
                                    category.append(x.getKey() + ":" + x.getValue().stream()
                                            .map(MQueriesAttrConnection::getSmallCategoryName)
                                            .collect(Collectors.joining("、")));
                                } else {
                                    category.append("；" + x.getKey() + ":" + x.getValue().stream()
                                            .map(MQueriesAttrConnection::getSmallCategoryName)
                                            .collect(Collectors.joining("、")));
                                }
                            });

                }
            }

            listResp.setCategory(Objects.toString(category));

            List<MQueriesTab> attrValueList = mQueriesTabRepository.getAttributeListByPidIncludeDeleted(MQueriesTabEnum.ATTRIBUTE, v.getAttributeId());

            String firstAttrName = "";
            if (CollectionUtils.isNotEmpty(attrValueList)) {
                firstAttrName = attrValueList.stream().filter(x -> Objects.equals(x.getIsDelete(), IsDeleteEnum.NORMAL.getCode()))
                        .map(MQueriesTab::getName)
                        .collect(Collectors.joining(";"));
            }
            String secondAttrName = "";
            List<MQueriesTab> mQueriesTabs1 = mQueriesTabRepository.getSubAttrListByPidIncludeDeleted(MQueriesTabEnum.ATTRIBUTE_SUB, v.getAttributeId());
            if (CollectionUtils.isNotEmpty(mQueriesTabs1)) {
                secondAttrName = mQueriesTabs1.stream().filter(x -> Objects.equals(x.getIsDelete(), IsDeleteEnum.NORMAL.getCode()))
                        .map(MQueriesTab::getName)
                        .collect(Collectors.joining(";"));
            }

            listResp.setFirstAttributeValue(firstAttrName);
            listResp.setSecondAttributeValue(secondAttrName);
            rets.add(listResp);
        });
        return rets;
    }

    public static PVQueryStatsDetailResp buildStatsDetail(List<MQueriesTab> firstAttrValue,
                                                          List<MQueriesTab> secondAttrValue,
                                                          List<MQueriesTab> cateGoryList,
                                                          List<MQueriesAttrConnection> connections,
                                                          Long attributeId,
                                                          MQueriesTab attr) {
        PVQueryStatsDetailResp detailResp = new PVQueryStatsDetailResp();
        // 1、一级属性值
        List<PVQueryStatsDetailResp.AttrData> attrValue = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(firstAttrValue)) {
            firstAttrValue.forEach(v -> {
                PVQueryStatsDetailResp.AttrData data = new PVQueryStatsDetailResp.AttrData();
                data.setId(v.getId());
                data.setCode(v.getCode());
                data.setDesc(v.getName());
                data.setIsSelected(Objects.equals(v.getIsDelete(), IsDeleteEnum.NORMAL.getCode()) ? 1 : 0);
                attrValue.add(data);
            });
        }

        if (attr != null) {
            detailResp.setAttributeId(attr.getId());
            detailResp.setAttributeCode(attr.getCode());
            detailResp.setAttributeName(attr.getName());
        }

        Map<Long, List<MQueriesAttrConnection>> smallCateConnectMap = null;
        if (CollectionUtils.isNotEmpty(connections)) {
            smallCateConnectMap = connections.stream().filter(v -> Objects.nonNull(v.getSmallCategoryId()))
                    .collect(Collectors.groupingBy(MQueriesAttrConnection::getSmallCategoryId));
        }

        List<MQueriesAttrConnection> secondAttrConnects = new ArrayList<>();
        List<MQueriesAttrConnection> firstAttrConnects = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(connections)) {
            secondAttrConnects = connections.stream().filter(v -> Objects.equals(v.getType(), 2)).collect(Collectors.toList());
            firstAttrConnects = connections.stream().filter(v -> Objects.equals(v.getType(), 1)).collect(Collectors.toList());
        }


        // 获取大类
        List<MQueriesTab> bigCategoryList = new ArrayList<>();
        List<MQueriesTab> smallCategoryList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cateGoryList)) {
            bigCategoryList = cateGoryList.stream().filter(v -> Objects.isNull(v.getPid())).collect(Collectors.toList());
            smallCategoryList = cateGoryList.stream().filter(v -> Objects.nonNull(v.getPid())).collect(Collectors.toList());
        }

        Map<Long, List<MQueriesTab>> smallCateMap = null;
        if (CollectionUtils.isNotEmpty(smallCategoryList)) {
            smallCateMap = smallCategoryList.stream().collect(Collectors.groupingBy(MQueriesTab::getPid));
        }

        // 2、二级属性值
        List<PVQueryStatsDetailResp.AttrData> secondAttrValueRet = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(secondAttrValue)) {
            secondAttrValue.forEach(v -> {
                PVQueryStatsDetailResp.AttrData data = new PVQueryStatsDetailResp.AttrData();
                data.setId(v.getId());
                data.setCode(v.getCode());
                data.setDesc(v.getName());
                data.setIsSelected(Objects.equals(v.getIsDelete(), IsDeleteEnum.NORMAL.getCode()) ? 1 : 0);
                secondAttrValueRet.add(data);
            });
        }

        detailResp.setAttrValue(attrValue);
        detailResp.setSecondAttrValue(secondAttrValueRet);
        if (CollectionUtils.isEmpty(bigCategoryList)) {
            detailResp.setMainCategoriesConnect(Collections.emptyList());
            return detailResp;
        }

        List<PVQueryStatsDetailResp.CategoryData> mainCategories = new ArrayList<>();
        List<PVQueryStatsDetailResp.AttrValueData> attrValueDataList = new ArrayList<>();
        Map<Long, List<MQueriesTab>> finalSmallCateMap = smallCateMap;
        Map<Long, List<MQueriesAttrConnection>> finalSmallCateConnectMap = smallCateConnectMap;
        // 3、类目部分
        bigCategoryList.forEach(v -> {
            PVQueryStatsDetailResp.CategoryData data = new PVQueryStatsDetailResp.CategoryData();
            data.setId(v.getId());
            data.setCode(v.getCode());
            data.setDesc(v.getName());
            if (finalSmallCateMap == null) {
                mainCategories.add(data);
                return;
            }
            List<MQueriesTab> mQueriesTabs = finalSmallCateMap.get(v.getId());
            if (CollectionUtils.isEmpty(mQueriesTabs)) {
                mainCategories.add(data);
                return;
            }
            List<PVQueryStatsDetailResp.CategoryData> smallCategories = new ArrayList<>();
            mQueriesTabs.forEach(x -> {

                PVQueryStatsDetailResp.CategoryData smallData = new PVQueryStatsDetailResp.CategoryData();
                smallData.setId(x.getId());
                smallData.setCode(x.getCode());
                smallData.setDesc(x.getName());
                List<MQueriesAttrConnection> connections1 = new ArrayList<>();
                // 判断是否有关联
                if (finalSmallCateConnectMap == null) {
                    smallData.setIsSelected(0);
                } else {
                    connections1 = finalSmallCateConnectMap.get(x.getId());
                }

                if (CollectionUtils.isEmpty(connections1)) {
                    smallData.setIsSelected(0);
                } else {
                    List<MQueriesAttrConnection> filterDataList = connections1.stream()
                            .filter(o -> (Objects.equals(o.getAttributeId(), attributeId)
                                    && Objects.equals(o.getBigCategoryId(), x.getPid())
                                    && Objects.equals(o.getSmallCategoryId(), x.getId())
                                    && Objects.equals(o.getType(), 1))).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(filterDataList)) {
                        smallData.setIsSelected(1);
                        smallData.setConnectId(filterDataList.get(0).getId());
                    } else {
                        smallData.setIsSelected(0);
                    }
                }
                smallCategories.add(smallData);
                data.setSubCategories(smallCategories);
            });
            mainCategories.add(data);
        });

        // 4、关联二级属性部分
        if (CollectionUtils.isNotEmpty(firstAttrConnects) && CollectionUtils.isNotEmpty(connections)) {
            firstAttrConnects.forEach(v -> {
                PVQueryStatsDetailResp.AttrValueData data = new PVQueryStatsDetailResp.AttrValueData();
                BeanUtils.copyProperties(v, data);
                data.setBigCategoryDesc(v.getBigCategoryName());
                data.setSmallCategoryDesc(v.getSmallCategoryName());
                List<PVQueryStatsDetailResp.ConnectAttrData> list = new ArrayList<>();
                List<MQueriesAttrConnection> secAttr = connections.stream()
                        .filter(x -> (Objects.equals(x.getType(), 2) && Objects.equals(x.getSmallCategoryId(), v.getSmallCategoryId())))
                        .collect(Collectors.toList());
                Map<Long, MQueriesAttrConnection> sceAttrMap = null;
                if (CollectionUtils.isNotEmpty(secAttr)) {
                    sceAttrMap = secAttr.stream().collect(HashMap::new, (k, va) -> k.put(va.getConnectAttributeId(), va), HashMap::putAll);
                }

                Map<Long, MQueriesAttrConnection> finalSceAttrMap = sceAttrMap;
                secondAttrValue.stream().filter(x -> Objects.equals(x.getPid(), v.getAttributeId())).forEach(
                        o -> {
                            PVQueryStatsDetailResp.ConnectAttrData attrData = new PVQueryStatsDetailResp.ConnectAttrData();
                            attrData.setId(o.getId());
                            attrData.setCode(o.getCode());
                            attrData.setDesc(o.getName());
                            attrData.setIsDeleted(o.getIsDelete());
                            if (finalSceAttrMap == null) {
                                attrData.setIsSelected(0);
                            } else {
                                if (finalSceAttrMap.get(o.getId()) != null) {
                                    attrData.setIsSelected(1);
                                    attrData.setConnectId(finalSceAttrMap.get(o.getId()).getId());
                                } else {
                                    attrData.setIsSelected(0);
                                }
                            }
                            list.add(attrData);
                        }
                );
                data.setAttrDataList(list);
                attrValueDataList.add(data);
            });

        }

        detailResp.setMainCategoriesConnect(mainCategories);
        detailResp.setAttrValueConnectDataList(attrValueDataList);
        return detailResp;
    }
}
