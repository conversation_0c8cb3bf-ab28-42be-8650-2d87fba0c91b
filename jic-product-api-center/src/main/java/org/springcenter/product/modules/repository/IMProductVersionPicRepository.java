package org.springcenter.product.modules.repository;

import org.springcenter.product.modules.model.MProductVersionPic;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【M_PRODUCT_VERSION_PIC(版型库照片)】的数据库操作Service
* @createDate 2022-12-05 09:43:42
*/
public interface IMProductVersionPicRepository {

    List<MProductVersionPic> queryPic(String id);

    List<MProductVersionPic> queryMainPicByIds(List<String> mProductVersionIds);
}
