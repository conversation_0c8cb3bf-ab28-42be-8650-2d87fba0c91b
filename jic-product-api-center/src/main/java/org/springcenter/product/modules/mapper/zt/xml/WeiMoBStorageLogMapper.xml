<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.zt.WeiMoBStorageLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.zt.WeiMoBStorageLog">
        <result column="ID" property="id" />
        <result column="TYPE" property="type" />
        <result column="C_STORE_ID" property="cStoreId" />
        <result column="M_PRODUCT_ID" property="mProductId" />
        <result column="M_PRODUCTALIAS_ID" property="mProductAliasId" />
        <result column="QTY" property="qty" />
        <result column="WEID" property="weId" />
        <result column="WEIMOBSTOREID" property="weiMoBStoreId" />
        <result column="WEIMOBSKUID" property="weiMoBSkuId" />
        <result column="LOGDATE" property="logDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, "TYPE", C_STORE_ID, M_PRODUCT_ID, M_PRODUCTALIAS_ID, QTY, WEID,
        WEIMOBSTOREID,  WEIMOBSKUID,  LOGDATE
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
            from WEIMOBSTORAGELOG
        where
            <if test="startTime != null">
                and LOGDATE <![CDATA[ >= ]]> #{LOGDATE}
            </if>
            <if test="id != null">
                and ID > #{id}
            </if>
            <if test="endTime != null">
                and LOGDATE <![CDATA[ <= ]]> #{endTime}
            </if>
    </select>


</mapper>
