package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.ChatGptEnum;
import org.springcenter.product.modules.mapper.product.AiAssistantStyleMapper;
import org.springcenter.product.modules.model.AiAssistantStyle;
import org.springcenter.product.modules.remote.JicBaseResp;
import org.springcenter.product.modules.remote.entity.AiAssistantReqEntity;
import org.springcenter.product.modules.remote.entity.AiAssistantRespEntity;
import org.springcenter.product.modules.remote.service.IAiAssistantService;
import org.springcenter.product.modules.service.IProductAiAssistantService;
import org.springcenter.product.modules.service.IProductFabService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/9/19 14:01
 */

@Service
@Slf4j
@RefreshScope
public class ProductAiAssistantServiceImpl implements IProductAiAssistantService {

    @Autowired
    private IAiAssistantService aiAssistantService;

    @Autowired
    private AiAssistantStyleMapper aiAssistantStyleMapper;

    @Autowired
    private IProductFabService fabService;

    @Value("${fab.big.category.order.str}")
    private String fabBigCategoryOrderStr;

    @Override
    public String invokeAiAssistant(AiAssistantReq requestData) {
        // 根据类型获取ai content、字段、title
        QueryWrapper query = new QueryWrapper();
        query.eq("IS_DELETED", 0);
        query.eq("ID", requestData.getType());
        List<AiAssistantStyle> list = aiAssistantStyleMapper.selectList(query);
        if (CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("该场景不存在！");
        }
        String introduction = list.get(0).getInputInfo();

        // 查询es数据
        ProductFabInfoReq req = new ProductFabInfoReq();
        req.setProductId(requestData.getProductId());
        ProductSpuFabResp fabResp = fabService.queryFabInfo(req);
        if (fabResp == null) {
            throw new RuntimeException("未找到当前商品的fab信息！");
        }

        // 组装参数
        HashMap<String, String> map = buildFabInfo(fabResp, requestData.getStyleAndSceneTags());
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String str = "${" + entry.getKey() + "}";
            introduction = introduction.replace(str, entry.getValue());
        }

        // 调用ai助手
        AiAssistantReqEntity entity = JSONObject.parseObject(introduction, AiAssistantReqEntity.class);
        entity.setRequestSource(ChatGptEnum.PRODUCT_CENTER_FAB.getEnglishDesc());
        AiAssistantRespEntity resp = aiAssistantService.invokeAiAssistant(entity);
        return resp.getResult();
    }

    private HashMap<String, String> buildFabInfo(ProductSpuFabResp fabResp, String productStyleReq) {
        HashMap<String, String> map = new HashMap<>();
        // 面料特性、进口产地、产品弹力、硬挺度、特殊工艺
        List<String> productElementList = new ArrayList<>();
        productElementList.add(fabResp.getSc_quality());
        productElementList.add(fabResp.getBom_area());
        productElementList.add(fabResp.getSc_elastic());
        productElementList.add(fabResp.getSc_stiffness());
        productElementList.add(fabResp.getTyxl() + fabResp.getTymx());


        productElementList.add(fabResp.getFiller());// 填充物
        productElementList.add(fabResp.getFill_content());// 填充量
        String productElement = productElementList.stream().filter(v -> StringUtils.isNotBlank(v) && !Objects.equals(v, null)
                        & !Objects.equals(v, "null") & !Objects.equals(v, "nullnull"))
                .collect(Collectors.joining(","));

        // 体型、合体度、工艺细节
        List<String> productVersionList = new ArrayList<>();
        productVersionList.add(fabResp.getTixing());
        productVersionList.add(fabResp.getWeidu());
        productVersionList.add(fabResp.getGyxj());
        productVersionList.add(fabResp.getCpdl());// 细分类
        productVersionList.add(fabResp.getScxfl()); // 产品细分
        productVersionList.add(fabResp.getDesign_name()); // 企划品名
        productVersionList.add(fabResp.getScdl());// 产品大类
        productVersionList.add(fabResp.getSc_fzyt()); // 服用功能
        productVersionList.add(fabResp.getSc_popularize()); // 面料推广资料
        String productVersion = productVersionList.stream().filter(v -> StringUtils.isNotBlank(v))
                .collect(Collectors.joining(","));

        // 第三部分
        List<String> productStyleList = new ArrayList<>();
        productStyleList.add(CollectionUtils.isEmpty(fabResp.getColorNames()) ? "" :
                fabResp.getColorNames().stream().map(ProductSpuFabEsResp.ColorName::getStyle_name).distinct()
                        .collect(Collectors.joining(",")));// 自动风格
        productStyleList.add(fabResp.getPattern_type());// 图案大类
        productStyleList.add(fabResp.getPattern_name());// 图案名称
        productStyleList.add(productStyleReq);
        String productStyle = productStyleList.stream().filter(v -> StringUtils.isNotBlank(v))
                .collect(Collectors.joining(","));

        map.put("productStyle", productStyle);
        map.put("productElement", productElement);
        map.put("productVersion", productVersion);
        return map;
    }

    @Override
    public List<AiStyleTypeResp> getAiStyleType() {
        QueryWrapper query = new QueryWrapper();
        query.eq("IS_DELETED", 0);
        query.ge("ID", 1);
        // 1是商品的AI话术 2是Look的AI话术
        query.eq("TYPE", 1);
        List<AiAssistantStyle> list = aiAssistantStyleMapper.selectList(query);
        return this.build(list);
    }

    @Override
    public List<AiStyleTypeResp> getLookAiStyleType() {
        QueryWrapper query = new QueryWrapper();
        query.eq("IS_DELETED", 0);
        query.ge("ID", 4);
        // 1是商品的AI话术 2是Look的AI话术
        query.eq("TYPE", 2);
        query.le("ID", 6);
        List<AiAssistantStyle> list = aiAssistantStyleMapper.selectList(query);
        return this.build(list);
    }

    @Override
    public String invokeLookAiAssistant(AiLookAssistantReq requestData) {
        // 判断当前是否有商品 请先选择搭配的商品
        if (CollectionUtils.isEmpty(requestData.getProductDataList())) {
            return "未选择商品或者商品FAB为空";
        }

        // 查询场景
        QueryWrapper query = new QueryWrapper();
        query.eq("IS_DELETED", 0);
        if (Objects.equals(requestData.getType(), "3")) {
            query.eq("ID", getRandomType());
        } else {
            query.eq("ID", requestData.getType());
        }
        List<AiAssistantStyle> list = aiAssistantStyleMapper.selectList(query);
        if (CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("该场景不存在！");
        }
        String introduction = list.get(0).getInputInfo();

        // 查询fab信息
        List<ProductSpuFabResp> fabResps = fabService.batchQueryFabInfo(requestData.getProductDataList()
                .stream().map(AiLookAssistantReq.ProductData::getProductId).collect(Collectors.toList()), "11");
        if (CollectionUtils.isEmpty(fabResps)) {
            return "未选择商品或者商品FAB为空";
        }

        // 组装参数
        HashMap<String, String> map = buildLookFabInfo(fabResps, requestData);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String str = "${" + entry.getKey() + "}";
            introduction = introduction.replace(str, entry.getValue());
        }

        // 调用ai助手
        AiAssistantReqEntity entity = JSONObject.parseObject(introduction, AiAssistantReqEntity.class);
        entity.setRequestSource(ChatGptEnum.LOOK_FAB.getEnglishDesc());
        AiAssistantRespEntity resp = aiAssistantService.invokeAiAssistant(entity);
        return resp.getResult();
    }

    @Value("${look.type.number}")
    private String lookTypeNumber;

    private String getRandomType() {
        String[] numbers = lookTypeNumber.split(",");
        Random random = new Random();
        int index = random.nextInt(numbers.length);

        // 获取随机选择的数字
        String randomNumber = numbers[index];

        // 输出结果
        log.info("随机选择的数字是: " + randomNumber);
        return randomNumber;
    }



    @Override
    public Boolean invokeLookExistFab(AiLookExistReq requestData) {
        if (CollectionUtils.isEmpty(requestData.getProductDataList())) {
            throw new RuntimeException("未选择商品或者商品FAB为空");
        }

        List<ProductSpuFabResp> fabResps = fabService.batchQueryFabInfo(requestData.getProductDataList()
                .stream().map(AiLookExistReq.ProductData::getProductId).collect(Collectors.toList()), "11");
        if (CollectionUtils.isEmpty(fabResps)) {
            throw new RuntimeException("未选择商品或者商品FAB为空");
        }
        return true;
    }

    private HashMap<String, String> buildLookFabInfo(List<ProductSpuFabResp> fabResps, AiLookAssistantReq req) {
        Map<String, String> orderMap = JSONObject.parseObject(fabBigCategoryOrderStr, Map.class);
        HashMap<String, String> map = new HashMap<>();
        HashMap<String, String> colorMap = req.getProductDataList().stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getProductId(), v.getColorName()), HashMap::putAll);
        Map<String, List<ProductSpuFabResp>> bigCategoryMap = fabResps.stream().collect(Collectors.groupingBy(ProductSpuFabEsResp::getBig_class));
        List<AiLookAssistantEntity> entities = new ArrayList<>();
        List<AiLookAssistantEntity> finalEntities = entities;
        bigCategoryMap.entrySet().forEach(entity -> {
            if (CollectionUtils.isNotEmpty(entity.getValue())) {
                AiLookAssistantEntity assistant = new AiLookAssistantEntity();
                assistant.setFab(entity.getValue().get(0).getFab());
                assistant.setValue(entity.getValue().get(0).getValue());
                assistant.setBigClassName(entity.getValue().get(0).getBig_class());
                assistant.setColorName(colorMap.get(entity.getValue().get(0).getId()));
                String number = orderMap.get(entity.getValue().get(0).getBig_class());
                assistant.setSort(StringUtils.isBlank(number) ? 99 : Integer.valueOf(number));
                finalEntities.add(assistant);
            }
        });

        entities = entities.stream().sorted(Comparator.comparing(AiLookAssistantEntity::getSort)).collect(Collectors.toList());
        StringBuilder productInfo = new StringBuilder();
        entities.forEach(v -> {
            productInfo.append("《").append(v.getColorName()).append(" ")
                    .append(v.getValue()).append("》")
                    .append(v.getFab())
                    .append("/n");
        });


        map.put("theme", req.getTheme());
        map.put("productInfo", productInfo.toString());
        return map;
    }

    private List<AiStyleTypeResp> build(List<AiAssistantStyle> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<AiStyleTypeResp> rets = new ArrayList<>();
        list.forEach(v -> {
            AiStyleTypeResp resp = new AiStyleTypeResp();
            resp.setType(v.getId());
            resp.setStyleTitle(v.getStyleTitle());
            resp.setStyleIntroduction(v.getStyleIntroduction());
            rets.add(resp);
        });
        return rets;
    }
}
