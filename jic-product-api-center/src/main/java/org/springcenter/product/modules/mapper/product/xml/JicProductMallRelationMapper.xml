<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.JicProductMallRelationMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.JicProductMallRelation">
        <id property="id" column="ID" jdbcType="DECIMAL"/>
        <result property="weid" column="WEID" jdbcType="VARCHAR"/>
        <result property="productNo" column="PRODUCT_NO" jdbcType="VARCHAR"/>
        <result property="productCode" column="PRODUCT_CODE" jdbcType="VARCHAR"/>
        <result property="weimoGoodid" column="WEIMO_GOODID" jdbcType="VARCHAR"/>
        <result property="weimoSkuid" column="WEIMO_SKUID" jdbcType="VARCHAR"/>
        <result property="weimoIsputaway" column="WEIMO_ISPUTAWAY" jdbcType="CHAR"/>
        <result property="weimoPutawayTime" column="WEIMO_PUTAWAY_TIME" jdbcType="TIMESTAMP"/>
        <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="ProductCodeMap" type="org.springcenter.product.modules.model.ProductByWeiMenInfoDto">
        <result property="weId" column="WEID" jdbcType="VARCHAR"/>
        <result property="productCode" column="PRODUCT_CODE" jdbcType="VARCHAR"/>
        <result property="weiMenGoodId" column="WEIMO_GOODID" jdbcType="VARCHAR"/>
        <result property="weiMenSkuId" column="WEIMO_SKUID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,WEID,PRODUCT_NO,
        PRODUCT_CODE,WEIMO_GOODID,WEIMO_SKUID,
        WEIMO_ISPUTAWAY,WEIMO_PUTAWAY_TIME,REMARK,
        CREATE_TIME,UPDATE_TIME
    </sql>
    <select id="selectSpuByWeiMenGoodId" resultType="java.lang.String">
        select distinct PRODUCT_NO from userwx.view_JIC_PRODUCT_MALL_RELATION where WEIMO_GOODID = #{weiMenGoodId}
    </select>
    <select id="selectByWeimoInfo" resultMap="ProductCodeMap">
        <foreach collection="skus" item="list" open="(" close=")" separator="union all">
            select
            max(PRODUCT_CODE) as PRODUCT_CODE, WEID, WEIMO_GOODID, WEIMO_SKUID
            from userwx.view_JIC_PRODUCT_MALL_RELATION
            where
            WEID = #{list.weId}
            and WEIMO_GOODID = #{list.weiMenGoodId}
            and WEIMO_SKUID = #{list.weiMenSkuId}
            group by WEID, WEIMO_GOODID, WEIMO_SKUID
        </foreach>
    </select>
    <select id="selectWeimoGoodIdAndBJSkuId"
            resultType="org.springcenter.product.api.dto.ExchangeByProductIdResp">
        select WEIMO_GOODID AS weiMOGoodId, MAX(b.Id) AS skuId, PRODUCT_ID AS productId
        from userwx.JIC_PRODUCT_MALL_RELATION a left join
             userwx.JIC_M_PRODUCT_ATTR b
             on a.PRODUCT_CODE = b.PRODUCT_CODE
             left join jnby.B_BOX_M_PRODUCT c on b.id = c.codeid
        where a.WEID = #{weId} and
              b.PRODUCT_ID in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
              and c.codeid is not null
        GROUP BY WEIMO_GOODID,  PRODUCT_ID
    </select>

    <select id="exchangeBojunIdAndName" resultType="org.springcenter.product.modules.context.BojunProductInfo">
        select b.PRODUCT_ID as id, a.product_no as name, a.WEIMO_GOODID as wmGoodId
        from userwx.JIC_PRODUCT_MALL_RELATION a left join
             userwx.JIC_M_PRODUCT_ATTR b
             on a.product_no = b.product_no
        where a.WEIMO_GOODID in
        <foreach collection="wmGoodIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by  b.PRODUCT_ID, a.product_no, a.WEIMO_GOODID
    </select>

</mapper>
