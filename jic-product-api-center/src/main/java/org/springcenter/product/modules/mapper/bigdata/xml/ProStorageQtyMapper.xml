<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.bigdata.ProFaStorageQtyMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.bigdata.ProFaStorageQty">
        <result column="C_STORE_ID" jdbcType="DECIMAL" property="cStoreId" />
        <result column="M_PRODUCT_ID" jdbcType="DECIMAL" property="mProductId" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="QTY" jdbcType="DECIMAL" property="qty" />
        <result column="MALL_QTY" jdbcType="DECIMAL" property="mallQty" />
        <result column="EB_QTY" jdbcType="DECIMAL" property="ebQty" />
    </resultMap>

    <sql id="Base_Column_List">
        C_STORE_ID, M_PRODUCT_ID, NAME, QTY, MALL_QTY, EB_QTY
    </sql>

    <select id="getEbAndWscSpuStorages" resultType="org.springcenter.product.modules.entity.EbAndWscSpuStockEntity">
        select
        M_PRODUCT_ID as productId,
        sum(MALL_QTY) as mallQty,
        sum(EB_QTY) as ebQty,
        sum(QTY) as qty
        from PRO_FA_STORAGE_QTY
        where c_store_id in
        <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and M_PRODUCT_ID in
        <foreach collection="productIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by M_PRODUCT_ID
    </select>

</mapper>
