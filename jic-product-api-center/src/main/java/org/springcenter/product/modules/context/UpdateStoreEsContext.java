package org.springcenter.product.modules.context;

import org.springcenter.product.modules.entity.EbAndWscSpuStockEntity;
import org.springcenter.product.modules.entity.SpuStockEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 17:46
 */
@Data
public class UpdateStoreEsContext {

    @ApiModelProperty(value = "门店id")
    private long storeId;

    @ApiModelProperty(value = "商品id")
    private long productId;

    @ApiModelProperty(value = "导购库存")
    private List<SpuStockEntity> spuStock;
    private String key;

    @ApiModelProperty(value = "内淘微商城库存")
    private List<EbAndWscSpuStockEntity> ebAndWscSpuStockEntities;

    @ApiModelProperty(value = "索引")
    private String storeGoodIndex;

    public static UpdateStoreEsContext build(long storeId, long productId, List<SpuStockEntity> spuStock, String key,
                               List<EbAndWscSpuStockEntity> ebAndWscSpuStockEntities, String index) {
        UpdateStoreEsContext context = new UpdateStoreEsContext();
        context.setStoreId(storeId);
        context.setKey(key);
        context.setProductId(productId);
        context.setSpuStock(spuStock);
        context.setEbAndWscSpuStockEntities(ebAndWscSpuStockEntities);
        context.setStoreGoodIndex(index);
        return context;
    }
}
