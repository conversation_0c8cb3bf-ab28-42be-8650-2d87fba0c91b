package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.FabInfo;

import java.util.List;


public interface FabInfoMapper extends BaseMapper<FabInfo> {


    FabInfo selectByProductId(@Param("productId") String productId);

    void batchInsert(@Param("list") List<FabInfo> dealFabInfos);

    void batchUpdate(@Param("list") List<FabInfo> dealFabInfos);

    void updateByName(@Param("name") String name);
}
