package org.springcenter.product.modules.remote.service;

import org.springcenter.product.modules.remote.entity.GetListByWeimoGoodIdsReq;
import org.springcenter.product.modules.remote.entity.GetListByWeimoGoodIdsResp;
import org.springcenter.product.modules.remote.entity.WeimoGoodShareLinkReq;
import org.springcenter.product.modules.remote.entity.WeimoGoodShareLinkResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/9/20 9:58
 */
public interface IWeiMoService {

    public List<GetListByWeimoGoodIdsResp> getWeimoGoodIdsInfo(GetListByWeimoGoodIdsReq getListByWeimoGoodIdsReq);

    public List<WeimoGoodShareLinkResp> getWeimoGoodShareLink(WeimoGoodShareLinkReq weimoGoodShareLinkReq);
}
