<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.FabInfoLogMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.FabInfoLog">
        <id column="ID" property="id" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="NAME" property="name" />
        <result column="FAB" property="fab" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="OPERATORS" property="operators" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="FAB_INFO_ID" property="fabInfoId" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, PRODUCT_ID, NAME, FAB, CREATE_TIME, UPDATE_TIME, OPERATORS, IS_DELETED, FAB_INFO_ID
    </sql>
    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO FAB_INFO_LOG
            (ID, PRODUCT_ID, NAME, FAB, OPERATORS, CREATE_TIME, UPDATE_TIME, FAB_INFO_ID) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.productId,jdbcType=VARCHAR}, #{item.name},
            #{item.fab}, #{item.operators}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.fabInfoId}  )
        </foreach>
        SELECT 1 FROM DUAL
    </insert>
</mapper>
