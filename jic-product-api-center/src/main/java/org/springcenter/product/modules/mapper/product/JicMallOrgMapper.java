package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.api.dto.ExchangeProductIdByWeiMoResp;
import org.springcenter.product.modules.model.JicMallOrg;

import java.util.List;

/**
 * @Description: 消息模板
 * @Author: jeecg-boot
 * @Date:  2019-04-09
 * @Version: V1.0
 */
@Mapper
public interface JicMallOrgMapper extends BaseMapper<JicMallOrg> {

    String selectWeIdByVid(@Param("vid") String storeId);

    List<ExchangeProductIdByWeiMoResp> selectBojunInfoByWeimo(@Param("vid") String vid, @Param("list") List<String> weiMoSkuIds);

    List<JicMallOrg> selectVidByInsideId(@Param("insideId") long storeId);

    List<JicMallOrg> selectByVidCodeAndType(@Param("vidCodes") List<String> vidCodes, @Param("weId") String weId);

    List<String> selectVidByWeidAndOnline(@Param("brandId") Long brandId);

    String selectCouponRuleByRuleId(@Param("ruleId") String couponRuleId);
}
