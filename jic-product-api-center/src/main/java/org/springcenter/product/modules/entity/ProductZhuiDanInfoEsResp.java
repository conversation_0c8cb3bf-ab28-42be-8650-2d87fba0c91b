package org.springcenter.product.modules.entity;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/6/20 10:00
 */
@Data
public class ProductZhuiDanInfoEsResp extends RemotingSerializable implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "被查询关联的商品id")
    private Long search_id;

    @ApiModelProperty(value = "关联商品id")
    private Long rela_id;

    @ApiModelProperty(value = "款号")
    private String name;

    @ApiModelProperty(value = "名称")
    private String value;

    @ApiModelProperty(value = "封面图")
    private String cover_imgs;

    @ApiModelProperty(value = "品牌")
    private Long c_arcbrand_id;

    @ApiModelProperty(value = "是否追单")
    private Integer is_zhuidan;

    @ApiModelProperty(value = "是否原款翻单")
    private Integer is_origin_spu;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "skus信息")
    private String sku_list;

    @ApiModelProperty(value = "skus信息")
    private List<Sku> skus;


    @Data
    public static class Sku extends RemotingSerializable implements Serializable{
        @ApiModelProperty(value = "SKUID")
        private String id;

        @ApiModelProperty(value = "条码")
        private String no;

        @ApiModelProperty(value = "国际码")
        private String gbcode;

        @ApiModelProperty(value = "尺码号")
        private String sizeno;

        @ApiModelProperty(value = "尺码名称")
        private String size_name;

        @ApiModelProperty(value = "SKC图片地址")
        private String imgurl;
        //扣完的图片
        @ApiModelProperty(value = "SKC白底图")
        private String matted_url;

        @ApiModelProperty(value = "SKU库存数量")
        private int stock;

        private String stylepartsize_model;

        @ApiModelProperty(value = "色号")
        private String colorno;

        @ApiModelProperty(value = "色名称")
        private String color_name;
    }

    public void buildSku(){
        if (this.getSku_list() == null || "".equals(this.getSku_list())){
            return;
        }
        this.skus = Sku.decodeArr(this.getSku_list(), Sku.class);
    }
}
