package org.springcenter.product.modules.mapper.product;

import org.springcenter.product.modules.context.ProductApiContext;
import org.springcenter.product.modules.entity.ProductApiEntity;
import org.springcenter.product.modules.model.BoxMProduct;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BoxMProductMapper {

    BoxMProduct selectByPrimaryKey(Long codeid);


    BoxMProduct findOneByCondition(BoxMProduct record);

    List<BoxMProduct> findByCondition(BoxMProduct record);

    List<BoxMProduct> findByName(@Param("name") String name);

    List<BoxMProduct> findByNoList(@Param("noList") List<String> noList);

    List<BoxMProduct> findBatchProductBaseInfoByCondition(@Param("nameList") List<String> nameList);

    List<BoxMProduct> selectListBySkuIds(@Param("skuIds") List<Long> skuIds);

    List<ProductApiEntity> selectProductApi(ProductApiContext context);

    List<BoxMProduct> selectListByNames(@Param("names") List<String> names);

    List<BoxMProduct> selectListByGbCodes(@Param("gbCodes") List<String> gbCodes);

    List<Long> selectListBySkcCodesGroupId(@Param("names") List<String> skcCodes);

    List<BoxMProduct> selectByProductIdAndColorNo(@Param("productId") String productId, @Param("colorNo") String productColor);

    List<BoxMProduct> selectListByIds(@Param("ids") List<String> ids);

    BoxMProduct selectListByNo(@Param("no") String no);


}
