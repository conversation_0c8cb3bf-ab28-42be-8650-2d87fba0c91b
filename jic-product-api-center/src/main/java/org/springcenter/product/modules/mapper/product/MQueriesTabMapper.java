package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.MQueriesTab;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【M_QUERIES_TAB(查询标签)】的数据库操作Mapper
* @createDate 2022-11-30 17:17:15
* @Entity generator.domain.MQueriesTab
*/

public interface MQueriesTabMapper extends BaseMapper<MQueriesTab> {

    Long getMQueriesId();

    Long getAttrId();

    Long getAttrValueId();

    Long getSecAttrId();

    void batchInsert(@Param("list") List<MQueriesTab> saveAttrValueList);

    void batchUpdate(@Param("list") List<MQueriesTab> updateAttrValueList);
}
