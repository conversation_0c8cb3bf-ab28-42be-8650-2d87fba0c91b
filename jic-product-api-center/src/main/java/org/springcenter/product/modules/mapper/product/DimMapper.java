package org.springcenter.product.modules.mapper.product;

import org.springcenter.product.modules.model.Dim;
import org.springcenter.product.modules.model.MColor;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DimMapper {
    Dim selectByPrimaryKey(Long id);

    List<Dim> selectListBySelective(Dim record);

    List<String> getCustomerTopClass(String unionId);

    List<MColor> selectColorByValue(@Param("list") List<String> list);

}
