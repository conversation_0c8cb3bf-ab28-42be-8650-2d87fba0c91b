<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.FabAutoNameSettingMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.FabAutoNameSetting">
        <id column="ID" property="id" />
        <result column="FIELD_NAME" property="fieldName" />
        <result column="FIELD" property="field" />
        <result column="ASSEMBLE_PART_ORDER" property="assemblePartOrder" />
        <result column="ASSEMBLE_WHOLE_ORDER" property="assembleWholeOrder" />
        <result column="TYPE" property="type" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, FIELD_NAME, FIELD, ASSEMBLE_PART_ORDER, TYPE, ASSEMBLE_WHOLE_ORDER,
            CREATE_TIME, UPDATE_TIME, IS_DELETED
    </sql>

</mapper>
