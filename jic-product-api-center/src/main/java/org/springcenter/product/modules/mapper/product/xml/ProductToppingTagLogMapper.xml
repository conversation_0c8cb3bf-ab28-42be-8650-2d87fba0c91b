<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.ProductToppingTagLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.ProductToppingTagLog">
        <id column="ID" property="id" />
        <result column="PRODUCT_TOPPING_TAG_ID" property="productToppingTagId"/>
        <result column="TYPE" property="type" />
        <result column="CHANNEL" property="channel" />
        <result column="SPU" property="spu" />
        <result column="SOURCE" property="source" />
        <result column="START_TIME" property="startTime" />
        <result column="END_TIME" property="endTime" />
        <result column="DURING_DAY" property="duringDay" />
        <result column="IS_FLOW" property="isFlow" />
        <result column="STATUS" property="status" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATOR" property="creator"/>
        <result column="UPDATER" property="updater"/>
        <result column="IS_DELETED" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, TYPE, CHANNEL, START_TIME, END_TIME, DURING_DAY, IS_FLOW, STATUS, CREATE_TIME, UPDATE_TIME, CREATOR, UPDATER,
          IS_DELETED, SPU, SOURCE, PRODUCT_TOPPING_TAG_ID
    </sql>

    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO PRODUCT_TOPPING_TAG_LOG
            (ID, TYPE, CHANNEL, START_TIME, END_TIME, DURING_DAY, IS_FLOW, STATUS, CREATE_TIME, UPDATE_TIME, CREATOR, UPDATER,
            SPU, SOURCE, PRODUCT_TOPPING_TAG_ID) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.type,jdbcType=DECIMAL}, #{item.channel,jdbcType=VARCHAR},
            #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP}, #{item.duringDay,jdbcType=DECIMAL},
            #{item.isFlow,jdbcType=DECIMAL}, #{item.status,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.updater,jdbcType=VARCHAR},
            #{item.spu,jdbcType=VARCHAR}, #{item.source,jdbcType=DECIMAL}, #{item.productToppingTagId,jdbcType=VARCHAR})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

</mapper>