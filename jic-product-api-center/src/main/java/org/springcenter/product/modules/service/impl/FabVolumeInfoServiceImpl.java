package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.FabVolumeTypeEnum;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.config.exception.ProductException;
import org.springcenter.product.modules.context.FabSearchContext;
import org.springcenter.product.modules.entity.FabCategorySkcData;
import org.springcenter.product.modules.entity.FabGoodInfoData;
import org.springcenter.product.modules.entity.FabLookSkcData;
import org.springcenter.product.modules.entity.FabSkusData;
import org.springcenter.product.modules.mapper.bojun.CStoreMapper;
import org.springcenter.product.modules.mapper.product.FabProhibitedWordsMapper;
import org.springcenter.product.modules.mapper.product.FabVolumeInfoMapper;
import org.springcenter.product.modules.model.FabProhibitedWords;
import org.springcenter.product.modules.model.FabVolumeInfo;
import org.springcenter.product.modules.model.ProductVersionEntity;
import org.springcenter.product.modules.model.bojun.CStore;
import org.springcenter.product.modules.service.CommonDataRuleService;
import org.springcenter.product.modules.service.FabVolumeInfoService;
import org.springcenter.product.modules.service.IProductFabService;
import org.springcenter.product.util.DateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/11/14 10:03
 */
@Service
@Slf4j
public class FabVolumeInfoServiceImpl implements FabVolumeInfoService {

    @Autowired
    private FabVolumeInfoMapper fabVolumeInfoMapper;

    @Value("${fab.wear.tag.id}")
    private String fabWearTagId;

    @Value("${band.order.str}")
    private String bandOrderStr;

    @Autowired
    private CommonDataRuleService commonDataRuleService;

    @Autowired
    private CStoreMapper cStoreMapper;

    @Autowired
    private IProductFabService productFabService;

    @Override
    public Boolean saveFabVolumeInfo(AddFabVolumeInfoReq requestData) {
        // 1、判断当前年份波段品牌是否已存在
        List<FabVolumeInfo> fabVolumeInfos = getFabVolumeInfos(requestData.getBrandCode(),
                requestData.getBrandName(), requestData.getBandId(), requestData.getType(), requestData.getYear());
        if (CollectionUtils.isNotEmpty(fabVolumeInfos)) {
            throw new RuntimeException("当前波段已有产品册保存，请编辑已保存产品册");
        }

        // 2、进行保存
        FabVolumeInfo fabVolumeInfo = new FabVolumeInfo();
        BeanUtils.copyProperties(requestData, fabVolumeInfo);
        fabVolumeInfo.setId(IdLeaf.getId(fabWearTagId));
        fabVolumeInfo.setCreateTime(new Date());
        fabVolumeInfo.setUpdateTime(new Date());

        // 处理波段
        Map<String, String> orderMap = JSONObject.parseObject(bandOrderStr, Map.class);
        Integer sort = StringUtils.isBlank(orderMap.get(requestData.getBandName())) ?
                37 : Integer.valueOf(orderMap.get(requestData.getBandName()));
        fabVolumeInfo.setBandOrder(sort);
        fabVolumeInfoMapper.insert(fabVolumeInfo);
        if (Objects.equals(requestData.getIsSingleFilter(), 0)) {
            FabVolumeInfo fabVolumeInfoOther = new FabVolumeInfo();
            BeanUtils.copyProperties(fabVolumeInfo, fabVolumeInfoOther);
            fabVolumeInfoOther.setId(IdLeaf.getId(fabWearTagId));
            fabVolumeInfoOther.setType(1);
            fabVolumeInfoMapper.insert(fabVolumeInfoOther);
        }
        return true;
    }

    private List<FabVolumeInfo> getFabVolumeInfos(Long brandCode, String brandName, Long bandId, Integer type, String year) {
        QueryWrapper<FabVolumeInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("BRAND_CODE", brandCode);
        queryWrapper.eq("BRAND_NAME", brandName);
        queryWrapper.eq("BAND_ID", bandId);
        if (type != null) {
            queryWrapper.eq("TYPE", type);
        }
        queryWrapper.eq("YEAR", year);
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        List<FabVolumeInfo> fabVolumeInfos = fabVolumeInfoMapper.selectList(queryWrapper);
        return fabVolumeInfos;
    }

    @Override
    public List<QueryFabVolumeInfoResp> fabVolumeInfoList(QueryFabVolumeInfoReq requestData, Page page, String component) {
        // 获取数据权限
        List<String> brands= new ArrayList<>();
        if (StringUtils.isNotBlank(component)) {
            brands = commonDataRuleService.getPeopleBrandDataRule(component);
        }
        if (CollectionUtils.isNotEmpty(brands)) {
            if (CollectionUtils.isEmpty(requestData.getBrandIds())) {
                requestData.setBrandIds(brands.stream().map(v -> Long.valueOf(v)).collect(Collectors.toList()));
            } else {
                List<String> finalBrands = brands;
                List<Long> filter = requestData.getBrandIds().stream()
                        .filter(v -> finalBrands.contains(Objects.toString(v))).collect(Collectors.toList());
                requestData.setBrandIds(filter);
            }
        }

        if (CollectionUtils.isNotEmpty(requestData.getBrandIds()) && requestData.getBrandIds().contains(4L)
                && CollectionUtils.isEmpty(requestData.getBrandNames())) {
            throw new RuntimeException("当品牌架构id为4时，品牌名称不能为空");
        }

        com.github.pagehelper.Page<ProductVersionEntity> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());

        List<FabVolumeInfo> reps = fabVolumeInfoMapper.selectByParam(FabSearchContext.build(requestData, ""));

        List<QueryFabVolumeInfoResp> rets = this.buildFabVolumeInfo(reps);
        PageInfo<QueryFabVolumeInfoResp> pageInfo = new PageInfo(hPage);
        pageInfo.setList(rets);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    @Override
    public FabVolumeInfoDetailResp searchFabVolumeInfo(String requestData) {
        FabVolumeInfo info = judgeFabInfo(requestData);

        // 判断已展示后不可以编辑  展示时间有值，已到展示时间 为了c端展示 校验去除 前端判断
        /*if (info.getDisplayTime() != null && (info.getDisplayTime().before(new Date())
        || info.getDisplayTime().equals(new Date()))) {
            throw new RuntimeException("已展示后不可以编辑");
        }*/

        FabVolumeInfoDetailResp resp = new FabVolumeInfoDetailResp();
        BeanUtils.copyProperties(info, resp);
        if (Integer.valueOf(resp.getYear()) >= 2024 && StringUtils.isNotBlank(resp.getClobJson())) {
             buildFabPdfImg(resp);
        }

        return resp;
    }


    //后时间充足 前端去渲染该部分
    private void buildFabPdfImg(FabVolumeInfoDetailResp resp) {
        JSONArray array = JSONArray.parseArray(resp.getClobJson());
        List<String> names = new ArrayList<>();
        List<String> lookNames = new ArrayList<>();
        List<String> patternNames = new ArrayList<>();
        List<String> detailNames = new ArrayList<>();
        List<String> fabricNames = new ArrayList<>();
        HashMap<String, List<String>> skcMap = new HashMap<>();
        skcMap.put("PatternList", patternNames);
        skcMap.put("Fabric", fabricNames);
        HashMap<String, List<String>> skcContentMap = new HashMap<>();
        skcContentMap.put("Particular", detailNames);
        skcContentMap.put("MainLook", lookNames);
        array.forEach(v -> {
            Map<String, String> map = JSONObject.parseObject(JSONObject.toJSONString(v), Map.class);
            if (StringUtils.isBlank(map.get("assembly"))) {
                return;
            }

            // 处理面料、重点细节、图案的skc图
            if (Objects.equals(map.get("assembly"), "PatternList") ||
                    Objects.equals(map.get("assembly"), "Fabric")) {

                if (StringUtils.isBlank(JSONObject.toJSONString(map.get("children")))) {
                    return;
                }
                String children = JSONObject.toJSONString(map.get("children"));
                List<FabSkusData> fabSkusData = JSONArray.parseArray(children, FabSkusData.class);
                if (CollectionUtils.isEmpty(fabSkusData)) {
                    return;
                }
                fabSkusData.forEach(u -> {
                    if (CollectionUtils.isEmpty(u.getSkuList())) {
                        return;
                    }
                    u.getSkuList().forEach(x -> {
                        /*if (StringUtils.isNotBlank(x.getImg()) && StringUtils.contains(x.getImg(), "weimob")) {
                            return;
                        }*/
                        names.add(x.getName());
                        skcMap.get(map.get("assembly")).add(x.getName() + x.getColorCode());
                    });
                });
            }

            // 处理主推look的skc图
            if (Objects.equals(map.get("assembly"), "MainLook") ||
                    Objects.equals(map.get("assembly"), "Particular")) {
                if (StringUtils.isBlank(JSONObject.toJSONString(map.get("children")))) {
                    return;
                }
                String children = JSONObject.toJSONString(map.get("children"));
                List<FabLookSkcData> lookSkcData = JSONArray.parseArray(children, FabLookSkcData.class);
                if (CollectionUtils.isEmpty(lookSkcData)) {
                    return;
                }
                lookSkcData.forEach(u -> {
                    if (CollectionUtils.isEmpty(u.getContentSku())) {
                        return;
                    }
                    u.getContentSku().forEach(x -> {
                        /*if (StringUtils.isNotBlank(x.getImg()) && StringUtils.contains(x.getImg(), "weimob")) {
                            return;
                        }*/
                        names.add(x.getName());
                        skcContentMap.get(map.get("assembly")).add(x.getSkcCode());
                    });
                });
            }

            // 处理一览图
            if (Objects.equals(map.get("assembly"), "Goods")) {
                if (StringUtils.isBlank(JSONObject.toJSONString(map.get("children")))) {
                    return;
                }
                String children = JSONObject.toJSONString(map.get("children"));
                List<FabGoodInfoData> goodInfoData = JSONArray.parseArray(children, FabGoodInfoData.class);
                if (CollectionUtils.isEmpty(goodInfoData)) {
                    return;
                }
                goodInfoData.forEach(u -> {
                    if (CollectionUtils.isEmpty(u.getGoodList())) {
                        return;
                    }
                    u.getGoodList().forEach(x-> {
                        if (CollectionUtils.isEmpty(x.getGoodsList())) {
                            return;
                        }
                        x.getGoodsList().forEach(z -> {
                            names.add(z.getProductInfo().getName());
                        });
                    });
                });
            }

        });

        // 查询所有款号预览图
        if (CollectionUtils.isNotEmpty(names)) {
            Map<String, BuildFabImgInfo> infoMap = productFabService.buildFabImgInfo(names, false, true);
            if (MapUtils.isEmpty(infoMap)) {
                return;
            }

            List<Object> list = new ArrayList<>();
            array.forEach(v -> {
                Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(v), Map.class);
                if (map.get("assembly") == null) {
                    return;
                }

                // 处理面料、重点细节、图案的skc图
                if (Objects.equals(map.get("assembly"), "PatternList") ||
                        Objects.equals(map.get("assembly"), "Fabric")) {
                    if (StringUtils.isBlank(JSONObject.toJSONString(map.get("children")))) {
                        return;
                    }
                    String children = JSONObject.toJSONString(map.get("children"));
                    List<FabSkusData> fabSkusData = JSONArray.parseArray(children, FabSkusData.class);
                    if (CollectionUtils.isEmpty(fabSkusData)) {
                        return;
                    }
                    fabSkusData.forEach(u -> {
                        if (CollectionUtils.isEmpty(u.getSkuList())) {
                            return;
                        }
                        u.getSkuList().forEach(x -> {
                            /*if (StringUtils.isNotBlank(x.getImg()) && StringUtils.contains(x.getImg(), "weimob")) {
                                return;
                            }*/
                            BuildFabImgInfo buildFabImgInfo = infoMap.get(x.getName());
                            if (buildFabImgInfo == null ||MapUtils.isEmpty(buildFabImgInfo.getSkcImgMap()) ||
                                StringUtils.isBlank(x.getColorCode()) ||
                                    StringUtils.isBlank(buildFabImgInfo.getSkcImgMap().get(x.getColorCode()))) {
                                return;
                            }
                            x.setImg(buildFabImgInfo.getSkcImgMap().get(x.getColorCode()));
                        });
                    });
                    map.put("children", fabSkusData);

                }

                // 处理主推look的skc图 和 细节
                if (Objects.equals(map.get("assembly"), "MainLook") ||
                        Objects.equals(map.get("assembly"), "Particular")) {
                    if (StringUtils.isBlank(JSONObject.toJSONString(map.get("children")))) {
                        return;
                    }
                    String children = JSONObject.toJSONString(map.get("children"));
                    List<FabLookSkcData> lookSkcData = JSONArray.parseArray(children, FabLookSkcData.class);
                    if (CollectionUtils.isEmpty(lookSkcData)) {
                        return;
                    }
                    lookSkcData.forEach(u -> {
                        if (CollectionUtils.isEmpty(u.getContentSku())) {
                            return;
                        }
                        u.getContentSku().forEach(x -> {
                            /*if (StringUtils.isNotBlank(x.getImg()) && StringUtils.contains(x.getImg(), "weimob")) {
                                return;
                            }*/
                            BuildFabImgInfo buildFabImgInfo = infoMap.get(x.getName());
                            if (buildFabImgInfo == null ||MapUtils.isEmpty(buildFabImgInfo.getSkcImgMap()) ||
                                    StringUtils.isBlank(x.getColorCode()) ||
                                    StringUtils.isBlank(buildFabImgInfo.getSkcImgMap().get(x.getColorCode()))) {
                                return;
                            }
                            x.setImg(buildFabImgInfo.getSkcImgMap().get(x.getColorCode()));
                        });
                    });
                    map.put("children", lookSkcData);
                }

                // 处理一览图
                if (Objects.equals(map.get("assembly"), "Goods")) {
                    if (StringUtils.isBlank(JSONObject.toJSONString(map.get("children")))) {
                        return;
                    }
                    String children = JSONObject.toJSONString(map.get("children"));
                    List<FabGoodInfoData> goodInfoData = JSONArray.parseArray(children, FabGoodInfoData.class);
                    if (CollectionUtils.isEmpty(goodInfoData)) {
                        return;
                    }
                    goodInfoData.forEach(u -> {
                        if (CollectionUtils.isEmpty(u.getGoodList())) {
                            return;
                        }
                        u.getGoodList().forEach(x-> {
                            if (CollectionUtils.isEmpty(x.getGoodsList())) {
                                return;
                            }
                            x.getGoodsList().forEach(z -> {
                                if (z.getProductInfo() == null || StringUtils.isBlank(z.getProductInfo().getName())
                                    || CollectionUtils.isEmpty(z.getProductInfo().getColorNames())) {
                                    return;
                                }
                                BuildFabImgInfo buildFabImgInfo = infoMap.get(z.getProductInfo().getName());
                                if (buildFabImgInfo == null ||MapUtils.isEmpty(buildFabImgInfo.getMattedImgMap())) {
                                    return;
                                }

                                z.getProductInfo().getColorNames().forEach(h ->{
                                    if (StringUtils.isNotBlank(h.getMatted_img()) && StringUtils.contains(h.getMatted_img(), "weimob")) {
                                        return;
                                    }

                                    if (StringUtils.isNotBlank(buildFabImgInfo.getMattedImgMap().get(h.getColor_code()))) {
                                        h.setMatted_img(buildFabImgInfo.getMattedImgMap().get(h.getColor_code()));
                                    }
                                });
                            });
                        });
                    });
                    map.put("children", goodInfoData);
                }

                // 品类图
                if (Objects.equals(map.get("assembly"), "Category")) {
                    if (StringUtils.isBlank(JSONObject.toJSONString(map.get("children")))) {
                        return;
                    }
                    String children = JSONObject.toJSONString(map.get("children"));
                    List<FabCategorySkcData> fabCategorySkcData = JSONArray.parseArray(children, FabCategorySkcData.class);
                    if (CollectionUtils.isEmpty(fabCategorySkcData)) {
                        return;
                    }
                    fabCategorySkcData.forEach(i -> {
                        List<FabCategorySkcData.Children> children1 = i.getChildren();
                        if (CollectionUtils.isEmpty(children1)) {
                            return;
                        }
                        children1.forEach(d -> {
                            if (d.getProductInfo() == null || StringUtils.isBlank(d.getProductInfo().getName())
                                    || CollectionUtils.isEmpty(d.getProductInfo().getColorNames())) {
                                d.getProductInfo().setCopyList(Collections.emptyList());
                                d.getProductInfo().setColorNames(Collections.emptyList());
                                return;
                            }
                            BuildFabImgInfo buildFabImgInfo = infoMap.get(d.getProductInfo().getName());
                            if (buildFabImgInfo != null && MapUtils.isNotEmpty(buildFabImgInfo.getSkcImgMap())) {
                                d.getProductInfo().getColorNames().forEach(x -> {
                                    if (StringUtils.isNotBlank(x.getImg()) &&
                                            (StringUtils.contains(x.getImg(), "weimob") || StringUtils.contains(x.getImg(), "qiniu"))) {
                                        return;
                                    }
                                    if (StringUtils.isNotBlank(buildFabImgInfo.getSkcImgMap().get(x.getColor_code()))) {
                                        x.setImg(buildFabImgInfo.getSkcImgMap().get(x.getColor_code()));
                                    }
                                });
                            }


                            // 处理copyList
                            if (buildFabImgInfo != null && MapUtils.isNotEmpty(buildFabImgInfo.getSkcImgMap()) && CollectionUtils.isNotEmpty(d.getProductInfo().getCopyList())) {
                                d.getProductInfo().getCopyList().forEach(g -> {
                                    if (StringUtils.isNotBlank(g.getImg()) && StringUtils.contains(g.getImg(), "qiniu")) {
                                        return;
                                    }
                                    if (StringUtils.isNotBlank(buildFabImgInfo.getSkcImgMap().get(g.getColor_code()))) {
                                        g.setImg(buildFabImgInfo.getSkcImgMap().get(g.getColor_code()));
                                    }

                                });
                            }


                            if (buildFabImgInfo == null || CollectionUtils.isEmpty(buildFabImgInfo.getMatchPicExpends())) {
                                d.getProductInfo().setMatchPics(CollectionUtils.isNotEmpty(d.getProductInfo().getMatchPics()) ?
                                        d.getProductInfo().getMatchPics() : Collections.emptyList());
                                return;
                            }

                            if (d.getProductInfo().getMatchPics() == null) {
                                d.getProductInfo().setMatchPics(new ArrayList<>());
                            }

                            List<ProductSpuFabEsResp.MatchPic> pics = new ArrayList<>();
                            d.getProductInfo().getMatchPics().forEach(f -> {
                                if (StringUtils.isNotBlank(f.getUrl()) &&
                                        (StringUtils.contains(f.getUrl(), "https://retail.jnby.com/"))
                                        || StringUtils.contains(f.getUrl(), "https://img.bzhz.jnbygroup.com/")) {
                                    ProductSpuFabEsResp.MatchPic pic = new ProductSpuFabEsResp.MatchPic();
                                    pic.setUrl(f.getUrl());
                                    pic.setValue("01");
                                    pics.add(pic);
                                }
                            });

                            if (CollectionUtils.isNotEmpty(pics)) {
                                d.getProductInfo().setMatchPics(pics);

                            } else {
                                buildFabImgInfo.getMatchPicExpends().forEach(g -> {
                                    ProductSpuFabEsResp.MatchPic pic = new ProductSpuFabEsResp.MatchPic();
                                    pic.setUrl(g.getUrl());
                                    pic.setValue("01");
                                    pics.add(pic);
                                });
                                d.getProductInfo().setMatchPics(pics);
                            }


                        });
                    });
                    map.put("children", fabCategorySkcData);
                }
                list.add(map);
            });

            resp.setClobJson(JSONObject.toJSONString(list));
        }
    }

    @Override
    public Boolean updateFabVolumeInfo(UpdateFabVolumeInfoReq requestData) {
        FabVolumeInfo info = judgeFabInfo(requestData.getId());

        // 判断已展示后不可以编辑  展示时间有值，已到展示时间
//        if (info.getDisplayTime() != null && (info.getDisplayTime().before(new Date())
//                || info.getDisplayTime().equals(new Date()))) {
//            throw new RuntimeException("已展示后不可以编辑");
//        }

        FabVolumeInfo fabVolumeInfo = new FabVolumeInfo();
        BeanUtils.copyProperties(requestData, fabVolumeInfo);
        fabVolumeInfoMapper.updateParamsById(fabVolumeInfo);

        return true;
    }

    private FabVolumeInfo judgeFabInfo(String id) {
        QueryWrapper<FabVolumeInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ID", id);
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        FabVolumeInfo info = fabVolumeInfoMapper.selectOne(queryWrapper);
        if (info == null) {
            throw new RuntimeException("未找到该id的商品数据");
        }

        return info;

    }

    @Override
    public Boolean setFabVolumeInfoDisplayTime(SetFabVolumeInfoDisplayTimeReq requestData) {
        if (requestData.getDisplayTime() == null) {
            throw new ProductException("设置的时间不能为空");
        }
        if (requestData.getDisplayTime().before(new Date())) {
            throw new ProductException("只能设置未来时间");
        }

        FabVolumeInfo info = judgeFabInfo(requestData.getId());
        // 判断已展示后不可以编辑  展示时间有值，已到展示时间
        if (info.getDisplayTime() != null && (info.getDisplayTime().before(new Date())
                || info.getDisplayTime().equals(new Date()))) {
            throw new ProductException("已展示的不允许修改");
        }

        FabVolumeInfo fabVolumeInfo = new FabVolumeInfo();
        BeanUtils.copyProperties(requestData, fabVolumeInfo);
        fabVolumeInfoMapper.updateParamsById(fabVolumeInfo);

        return true;
    }

    @Override
    public Boolean setFabVolumeInfoPublish(SetFabVolumeInfoDisplayTimeReq requestData) {
        judgeFabInfo(requestData.getId());

        FabVolumeInfo fabVolumeInfo = new FabVolumeInfo();
        BeanUtils.copyProperties(requestData, fabVolumeInfo);
        fabVolumeInfo.setDisplayTime(new Date());
        fabVolumeInfoMapper.updateParamsById(fabVolumeInfo);

        return true;
    }

    @Override
    public Boolean deleteFabVolumeInfo(SetFabVolumeInfoDisplayTimeReq requestData) {
        judgeFabInfo(requestData.getId());

        FabVolumeInfo fabVolumeInfo = new FabVolumeInfo();
        BeanUtils.copyProperties(requestData, fabVolumeInfo);
        fabVolumeInfo.setIsDeleted(IsDeleteEnum.IS_DELETED.getCode());
        fabVolumeInfoMapper.updateParamsById(fabVolumeInfo);

        return true;
    }

    @Override
    public List<FabVolumeInfoListForC> queryFabVolumeInfoForC(QueryFabVolumeInfoReq requestData, Page page) {
        if (CollectionUtils.isNotEmpty(requestData.getBrandIds()) && requestData.getBrandIds().contains(4L)
                && CollectionUtils.isEmpty(requestData.getBrandNames())) {
            throw new RuntimeException("当品牌架构id为4时，品牌名称不能为空");
        }

        if (requestData.getStoreId() == null) {
            throw new RuntimeException("当前门店信息不能为空");
        }
        CStore store = Preconditions.checkNotNull(cStoreMapper.selectById(requestData.getStoreId()), "当前门店id未找到对应门店信息");
        requestData.setFabType(Objects.equals(store.getCCustomerId(), 176L) ? FabVolumeTypeEnum.ZHI_YING.getCode() :
                FabVolumeTypeEnum.DISTRIBUTION.getCode());

        com.github.pagehelper.Page<FabVolumeInfo> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<FabVolumeInfo> reps = fabVolumeInfoMapper.selectByParam(FabSearchContext.build(requestData, "queryFabVolumeInfoForC"));

        List<FabVolumeInfoListForC> rets = this.buildFabVolumeInfoListForC(reps);
        PageInfo<FabVolumeInfoListForC> pageInfo = new PageInfo(hPage);
        pageInfo.setList(rets);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    private List<FabVolumeInfoListForC> buildFabVolumeInfoListForC(List<FabVolumeInfo> reps) {
        //未解锁（对应后台状态：待展示，已保存-未编辑时间）未解锁产品册无法点击；
        //已浏览（对应后台状态：已展示+导购已浏览）；已浏览产品册置灰；
        //解锁-未浏览（对应后台状态：已展示

        if (CollectionUtils.isEmpty(reps)) {
            return Collections.emptyList();
        }

        List<FabVolumeInfoListForC> rets = new ArrayList<>();
        List<FabVolumeInfoListForC> finalRets = rets;
        reps.forEach(v -> {
            FabVolumeInfoListForC forC = new FabVolumeInfoListForC();
            BeanUtils.copyProperties(v, forC);
            forC.setStatus(v.getDisplayTime().before(new Date()) ? 1 : 0);
            finalRets.add(forC);
        });
        return rets;
    }

    private List<QueryFabVolumeInfoResp> buildFabVolumeInfo(List<FabVolumeInfo> reps) {
        if (CollectionUtils.isEmpty(reps)) {
            return Collections.emptyList();
        }

        List<QueryFabVolumeInfoResp> rets = new ArrayList<>();
        reps.forEach(v -> {
            QueryFabVolumeInfoResp infoResp = new QueryFabVolumeInfoResp();
            BeanUtils.copyProperties(v, infoResp);
            // 未设置时间：FAB已经保存，展示时间为空；
            // 待展示：FAB已保存，展示时间有值，未到展示时间；
            // 已展示：FAB已保存，展示时间有值，已到展示时间
            if (v.getDisplayTime() == null) {
                infoResp.setStatus(0);
            } else if (v.getDisplayTime().before(new Date()) || v.getDisplayTime().equals(new Date())) {
                infoResp.setStatus(2);
            } else {
                infoResp.setStatus(1);
            }

            // 时间处理
            infoResp.setUpdateTime(DateUtil.parseDate(v.getUpdateTime(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMM));
            if (v.getDisplayTime() != null) {
                infoResp.setDisplayTime(DateUtil.parseDate(v.getDisplayTime(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMM));
            }
            rets.add(infoResp);
        });

        return rets;
    }


    @Override
    public List<FabVolumeInfoParam> queryFabVolumeParams() {
        List<FabVolumeInfoParam> rets = new ArrayList<>();
        Arrays.stream(FabVolumeTypeEnum.values()).forEach(v -> {
            FabVolumeInfoParam fabVolumeInfoParam = new FabVolumeInfoParam();
            BeanUtils.copyProperties(v, fabVolumeInfoParam);
            rets.add(fabVolumeInfoParam);
        });
        return rets;
    }


    @Override
    public Boolean judgeCreateFab(JudgeCreateFabReq req) {
        if (Objects.equals(req.getIsSingleFilter(), 1)) {
            List<FabVolumeInfo> fabVolumeInfos = getFabVolumeInfos(req.getBrandId(), req.getBrandName(), req.getBandId(), req.getType(), req.getYear());
            return CollectionUtils.isEmpty(fabVolumeInfos) ? true : false;
        } else {
            List<FabVolumeInfo> fabVolumeInfos = getFabVolumeInfos(req.getBrandId(), req.getBrandName(), req.getBandId(), null, req.getYear());
            return CollectionUtils.isEmpty(fabVolumeInfos) ? true : false;
        }

    }

    @Override
    public List<FabVolumeInfoListForC> queryFabVolumeInfoForTask(QueryFabVolumeInfoReq requestData, Page page) {
        if (CollectionUtils.isNotEmpty(requestData.getBrandIds()) && requestData.getBrandIds().contains(4L)
                && CollectionUtils.isEmpty(requestData.getBrandNames())) {
            throw new RuntimeException("当品牌架构id为4时，品牌名称不能为空");
        }

        com.github.pagehelper.Page<FabVolumeInfo> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<FabVolumeInfo> reps = fabVolumeInfoMapper.selectByParam(FabSearchContext.build(requestData, "queryFabVolumeInfoForC"));

        List<FabVolumeInfoListForC> rets = this.buildFabVolumeInfoListForTask(reps);
        PageInfo<FabVolumeInfoListForC> pageInfo = new PageInfo(hPage);
        pageInfo.setList(rets);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }


    private List<FabVolumeInfoListForC> buildFabVolumeInfoListForTask(List<FabVolumeInfo> reps) {
        //未解锁（对应后台状态：待展示，已保存-未编辑时间）未解锁产品册无法点击；
        //已浏览（对应后台状态：已展示+导购已浏览）；已浏览产品册置灰；
        //解锁-未浏览（对应后台状态：已展示

        if (CollectionUtils.isEmpty(reps)) {
            return Collections.emptyList();
        }

        List<FabVolumeInfoListForC> rets = new ArrayList<>();
        List<FabVolumeInfoListForC> finalRets = rets;
        reps.forEach(v -> {
            FabVolumeInfoListForC forC = new FabVolumeInfoListForC();
            BeanUtils.copyProperties(v, forC);
            forC.setStatus(v.getDisplayTime().before(new Date()) ? 1 : 0);
            forC.setFabName(FabVolumeTypeEnum.getByCode(v.getType()) + "_" + StringUtils.trim(v.getFabName()));
            finalRets.add(forC);
        });
        return rets;
    }

    //-------------------------------------------违禁词----------------------------------------------------------

    @Autowired
    private FabProhibitedWordsMapper fabProhibitedWordsMapper;

    @Override
    public List<String> queryFabProhibitedWords() {
        QueryWrapper<FabProhibitedWords> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        List<FabProhibitedWords> fabProhibitedWords = fabProhibitedWordsMapper.selectList(queryWrapper);
        return CollectionUtils.isEmpty(fabProhibitedWords) ? Collections.emptyList() :
                fabProhibitedWords.stream().map(FabProhibitedWords::getWord).collect(Collectors.toList());
    }

}
