<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.DimMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.Dim">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="M_DIMDEF_ID" jdbcType="DECIMAL" property="mDimdefId" />
    <result column="ATTRIBCODE" jdbcType="VARCHAR" property="attribcode" />
    <result column="ATTRIBNAME" jdbcType="VARCHAR" property="attribname" />
    <result column="DIMFLAG" jdbcType="VARCHAR" property="dimflag" />
    <result column="AD_CLIENT_ID" jdbcType="DECIMAL" property="adClientId" />
    <result column="AD_ORG_ID" jdbcType="DECIMAL" property="adOrgId" />
    <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
    <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
    <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
    <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
    <result column="MODIFIERID" jdbcType="DECIMAL" property="modifierid" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="C_CORP_ID" jdbcType="DECIMAL" property="cCorpId" />
    <result column="ORDER_CAP" jdbcType="DECIMAL" property="orderCap" />
    <result column="CCKU_QTY" jdbcType="DECIMAL" property="cckuQty" />
    <result column="ATTRIB_ENAME" jdbcType="VARCHAR" property="attribEname" />
    <result column="M_DIMDL_ID" jdbcType="DECIMAL" property="mDimdlId" />
    <result column="RATE" jdbcType="DECIMAL" property="rate" />
    <result column="IS_TO_BPOS" jdbcType="CHAR" property="isToBpos" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, M_DIMDEF_ID, ATTRIBCODE, ATTRIBNAME, DIMFLAG, AD_CLIENT_ID, AD_ORG_ID, ISACTIVE,
    CREATIONDATE, OWNERID, MODIFIEDDATE, MODIFIERID, DESCRIPTION, C_CORP_ID, ORDER_CAP,
    CCKU_QTY, ATTRIB_ENAME, M_DIMDL_ID, RATE, IS_TO_BPOS
  </sql>

  <!-- 根据色号批量获取颜色 -->
  <select id="selectColorByValue" resultType="org.springcenter.product.modules.model.MColor">
    select value,name
    from m_color
    where value in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from M_DIM
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <select id="selectListBySelective" parameterType="org.springcenter.product.modules.model.Dim" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from M_DIM
    where 1=1 and ISACTIVE = 'Y'
    <if test="mDimdefId != null">
     and M_DIMDEF_ID = #{mDimdefId,jdbcType=DECIMAL}
    </if>
    <if test="attribcode != null">
      and ATTRIBCODE = #{attribcode,jdbcType=VARCHAR}
    </if>
    <if test="attribname != null">
      and ATTRIBNAME = #{attribname,jdbcType=VARCHAR}
    </if>
    <if test="dimflag != null">
      and DIMFLAG = #{dimflag,jdbcType=VARCHAR}
    </if>
    <if test="adClientId != null">
      and AD_CLIENT_ID = #{adClientId,jdbcType=DECIMAL}
    </if>
    <if test="adOrgId != null">
      and AD_ORG_ID = #{adOrgId,jdbcType=DECIMAL}
    </if>
    <if test="creationdate != null">
      and CREATIONDATE = #{creationdate,jdbcType=TIMESTAMP}
    </if>
    <if test="ownerid != null">
      and OWNERID = #{ownerid,jdbcType=DECIMAL}
    </if>
    <if test="modifieddate != null">
      and MODIFIEDDATE = #{modifieddate,jdbcType=TIMESTAMP}
    </if>
    <if test="modifierid != null">
      and MODIFIERID = #{modifierid,jdbcType=DECIMAL}
    </if>
    <if test="cCorpId != null">
      and C_CORP_ID = #{cCorpId,jdbcType=DECIMAL}
    </if>
    <if test="orderCap != null">
      and ORDER_CAP = #{orderCap,jdbcType=DECIMAL}
    </if>
    <if test="cckuQty != null">
      and CCKU_QTY = #{cckuQty,jdbcType=DECIMAL}
    </if>
    <if test="attribEname != null">
      and ATTRIB_ENAME = #{attribEname,jdbcType=VARCHAR}
    </if>
    <if test="mDimdlId != null">
      and M_DIMDL_ID = #{mDimdlId,jdbcType=DECIMAL}
    </if>
    <if test="rate != null">
      and RATE = #{rate,jdbcType=DECIMAL}
    </if>
    <if test="isToBpos != null">
      and IS_TO_BPOS = #{isToBpos,jdbcType=CHAR}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from M_DIM
    where ID = #{id,jdbcType=DECIMAL}
  </delete>

  <select id="getCustomerTopClass" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT name
      FROM (select k.attribname name,count(*) num
      from m_retail a
      inner join m_retailitem b on a.id = b.m_retail_id
      inner join c_client_vip c on a.c_vip_id = c.id
      inner join m_product d on b.M_PRODUCT_ID = d.id
      left join (select * from m_dim where dimflag = 'DIM7') k on k.id =
      d.M_DIM7_ID
      where a.status = 2
      and a.isActive = 'Y'
      and a.is_virtual = 'N'
      and c.unionid = #{unionId}
      and a.tot_amt_actual > 0
      group by k.attribname
      order by num desc)
    where ROWNUM &lt;= 6
  </select>
</mapper>
