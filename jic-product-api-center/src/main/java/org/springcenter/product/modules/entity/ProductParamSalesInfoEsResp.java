package org.springcenter.product.modules.entity;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springcenter.product.api.dto.background.fab.ProductWashInfoEsResp;
import strman.Strman;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/10/17 10:11
 */
@Data
public class ProductParamSalesInfoEsResp extends RemotingSerializable implements Serializable {

    @ApiModelProperty(value = "商品id")
    private Long id;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "款号")
    private String style_id;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "成衣弹力")
    private String cloth_elastic;

    @ApiModelProperty(value = "合体度")
    private String weidu;

    @ApiModelProperty(value = "长度分类")
    private String kunxing;

    @ApiModelProperty(value = "体型")
    private String tixing;

    @ApiModelProperty(value = "袖型")
    private String xiu;

    @ApiModelProperty(value = "是否有里")
    private String youli;

    @ApiModelProperty(value = "下摆设计")
    private String xiabai;

    @ApiModelProperty(value = "开衩方式")
    private String kaicha;

    @ApiModelProperty(value = "衣门襟")
    private String yimenjin;

    @ApiModelProperty(value = "裤门襟")
    private String kumenjin;

    @ApiModelProperty(value = "裤脚口")
    private String kujiaokou;

    @ApiModelProperty(value = "外观")
    private String facade;

    @ApiModelProperty(value = "厚薄")
    private String thick;

    @ApiModelProperty(value = "羽绒填充进口")
    private String yr_import_area;

    @ApiModelProperty(value = "硬挺度")
    private String sc_stiffness;

    @ApiModelProperty(value = "布面效果")
    private String sc_effect;

    @ApiModelProperty(value = "进口产地")
    private String bomarea;

    @ApiModelProperty(value = "原料产地")
    private String sc_environmental;

    @ApiModelProperty(value = "填充物")
    private String filler;

    @ApiModelProperty(value = "填充量")
    private String fillcontent;

    @ApiModelProperty(value = "工艺细节")
    private String gyxj;

    @ApiModelProperty(value = "成分面料")
    private String sjcf;

    @ApiModelProperty(value = "袖长")
    private String xiuxing;

    @ApiModelProperty(value = "领型")
    private String lingxing;

    @ApiModelProperty(value = "面料特性")
    private String fabric_label_list;

    @ApiModelProperty(value = "服务功能")
    private String serve_function_list;

    @ApiModelProperty(value = "克重信息")
    private String fabric_kz_list;

    @ApiModelProperty(value = "面料推广资料")
    private String sc_popularize;

    @ApiModelProperty(value = "辅料推广资料 (对外)")
    private String fl_sc_popularize;

    private List<String> labels;

    private List<String> label_levels;

    @ApiModelProperty(value = "小类")
    private String small_class;

    @ApiModelProperty(value = "洗涤说明")
    private String wash_name;

    @ApiModelProperty(value = "洗涤图片")
    private String wash_no;

    @ApiModelProperty(value = "波段")
    private String band;


    @ApiModelProperty(value = "洗涤信息")
    private List<ProductWashInfoEsResp.WashInfo> washInfoList;

    public void buildWashInfo() {
        List<String> washNameList = Arrays.stream(this.wash_name.split(",")).collect(Collectors.toList());
        List<String> washNoList = Arrays.stream(this.wash_no.split(",")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(washNameList) && CollectionUtils.isNotEmpty(washNoList)) {
            this.washInfoList = washNameList.stream()
                    .map(v -> {
                        ProductWashInfoEsResp.WashInfo info = new ProductWashInfoEsResp.WashInfo();
                        info.setWashName(v);
                        info.setWashNo(washNoList.get(washNameList.indexOf(v)));
                        return info;
                    }).collect(Collectors.toList());
        }
    }

    @Data
    public static class WashInfo extends RemotingSerializable implements Serializable {
        @ApiModelProperty(value = "洗涤说明")
        private String washName;

        @ApiModelProperty(value = "洗涤图片")
        private String washNo;
    }

    private List<String> labelsList;

    private List<String> labelLevels;

    private List<FabricKzData> fabricKzDataList;

    private List<FabricIconData> fabricMaterialDataList;

    private List<FabricIconData> fabricServerFunctionDataList;

    @Data
    public static class FabricKzData extends RemotingSerializable implements Serializable {
        @ApiModelProperty(value = "面料")
        private String color_code;

        @ApiModelProperty(value = "克重")
        private String color_name;

        @ApiModelProperty(value = "克重")
        private String fabric_kz;

        @ApiModelProperty(value = "面料号")
        private String bom_code;
    }


    @Data
    public static class FabricIconData extends RemotingSerializable implements Serializable {
        @ApiModelProperty(value = "标签code")
        private String label_code;

        @ApiModelProperty(value = "标签icon")
        private String mc_pic;

        @ApiModelProperty(value = "标签名称")
        private String label_name;

    }

    public void buildFabricKzData(){
        if (this.getFabric_kz_list() == null || "".equals(this.getFabric_kz_list())){
            return;
        }
        this.fabricKzDataList = FabricKzData.decodeArr(this.getFabric_kz_list(),
                FabricKzData.class);
    }

    public void buildMaterialData(){
        if (this.getFabric_label_list() == null || "".equals(this.getFabric_label_list())){
            return;
        }
        this.fabricMaterialDataList = FabricIconData.decodeArr(this.getFabric_label_list(),
                FabricIconData.class);
    }

    public void buildServeFunctionData(){
        if (this.getServe_function_list() == null || "".equals(this.getServe_function_list())){
            return;
        }
        this.fabricServerFunctionDataList = FabricIconData.decodeArr(this.getServe_function_list(),
                FabricIconData.class);
    }

    public void buildLabels(){
        if (this.getLabels() == null || "".equals(this.getLabels())){
            return;
        }

        this.labelsList = this.getLabels()
                .stream()
                .map(v -> Strman.replace(v.trim(), "_", "-", true))
                .collect(Collectors.toList());
    }

    public void buildLabelLevels(){
        if (this.getLabel_levels() == null || "".equals(this.getLabel_levels())){
            return;
        }

        this.labelLevels = this.getLabel_levels().stream()
                .map(v -> Strman.replace(v.trim(), "_", "-", true))
                .collect(Collectors.toList());
    }

}
