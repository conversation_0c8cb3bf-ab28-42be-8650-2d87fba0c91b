package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.MPQueAttrValueConnect;
import org.springcenter.product.modules.model.MQueriesAttrConEntity;
import org.springcenter.product.modules.model.MQueriesAttrConnection;
import org.springcenter.product.modules.model.ProductVersionEntity;

import java.util.List;



public interface MPQueAttrValueConnectMapper extends BaseMapper<MPQueAttrValueConnect> {


    void batchInsert(@Param("list") List<MPQueAttrValueConnect> mpQueAttrValueConnects);

    void delAllMPValueConnectByMpIds(@Param("list") List<String> ids);

    List<MQueriesAttrConEntity> selectByMpId(@Param("mpId") String mpId);

    void batchUpdateValue(@Param("list") List<MPQueAttrValueConnect> valueConnects);

    List<MPQueAttrValueConnect> selectByProductId(@Param("list") List<ProductVersionEntity> rets);
}
