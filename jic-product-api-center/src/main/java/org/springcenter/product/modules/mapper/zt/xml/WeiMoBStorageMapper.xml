<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.zt.WeiMoBStorageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.zt.WeiMoBStorage">
        <result column="TYPE" property="type" />
        <result column="C_STORE_ID" property="cStoreId" />
        <result column="M_PRODUCT_ID" property="mProductId" />
        <result column="M_PRODUCTALIAS_ID" property="mProductAliasId" />
        <result column="QTY" property="qty" />
        <result column="WEID" property="weId" />
        <result column="WEIMOBSTOREID" property="weiMoBStoreId" />
        <result column="WEIMOBSKUID" property="weiMoBSkuId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        "TYPE", C_STORE_ID, M_PRODUCT_ID, M_PRODUCTALIAS_ID, QTY, WEID, WEIMOBSTOREID, WEIMOBSKUID
    </sql>

    <select id="selectByTypesAndProductIdAndBrandId" resultMap="BaseResultMap">
        SELECT M_PRODUCT_ID, M_PRODUCTALIAS_ID, SUM(QTY) as QTY
        FROM WEIMOBSTORAGE
        WHERE WEID = #{brandId} AND M_PRODUCT_ID = #{productId} and
        "TYPE" in
        <foreach collection="list" item="type" open="(" close=")" separator=",">
            #{type}
        </foreach>
        GROUP BY M_PRODUCT_ID, M_PRODUCTALIAS_ID
    </select>

    <select id="selectByStoreIdAndProductIdAndBrandId" resultMap="BaseResultMap">
        select  M_PRODUCT_ID, M_PRODUCTALIAS_ID, SUM(QTY) as QTY
        FROM WEIMOBSTORAGE
        WHERE C_STORE_ID = #{storeId} AND M_PRODUCT_ID = #{productId} AND WEID = #{brandId}
        GROUP BY M_PRODUCT_ID, M_PRODUCTALIAS_ID
    </select>


</mapper>
