package org.springcenter.product.modules.remote.entity;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/8/8 10:26
 */
@Data
public class EgSearchRespEntity implements Serializable {

    @ApiModelProperty(value = "识别的图片")
    @SerializedName("img")
    private String img;

    @ApiModelProperty(value = "目标识别-子图品类")
    @SerializedName("class_name")
    private String class_name;

    @ApiModelProperty(value = "目标识别-品类展示等级,越小的展示在越前面")
    @SerializedName("class_rank")
    private Integer class_rank;

    @ApiModelProperty(value = "品牌集合")
    @SerializedName("list")
    private List<BrandInfo> list;

    @Data
    public static class BrandInfo {
        @ApiModelProperty(value = "商品所属品牌")
        @SerializedName("brand")
        private String brand;

        @ApiModelProperty(value = "品牌展示等级")
        @SerializedName("brand_rank")
        private Integer brand_rank;

        @ApiModelProperty(value = "产品详情集合")
        @SerializedName("list")
        private List<ProductInfo> list;

    }

    @Data
    public static class ProductInfo {
        @ApiModelProperty(value = "商品id")
        @SerializedName("m_product_id")
        private String m_product_id;

        @ApiModelProperty(value = "款号")
        @SerializedName("name")
        private String name;

        @ApiModelProperty(value = "spu展示等级，越小的展示在越前面")
        @SerializedName("rank")
        private Integer rank;

        @ApiModelProperty(value = "目标图片与当前spu的距离，越小表示越相似")
        @SerializedName("res_distance")
        private Double res_distance;

        @ApiModelProperty(value = "商品图片")
        @SerializedName("img_url")
        private String img_url;
    }
}
