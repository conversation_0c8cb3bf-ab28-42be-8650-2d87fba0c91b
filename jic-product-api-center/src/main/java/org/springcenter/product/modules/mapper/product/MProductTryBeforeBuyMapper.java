package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.api.dto.ProductIsTryBeforeBuyResp;
import org.springcenter.product.modules.model.MProductTryBeforeBuy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【M_PRODUCT_TRY_BEFORE_BUY(先试后买商品)】的数据库操作Mapper
 * @createDate 2023-02-02 17:54:14
 * @Entity generator.domain.MProductTryBeforeBuy
 */
@Mapper
public interface MProductTryBeforeBuyMapper extends BaseMapper<MProductTryBeforeBuy> {


    List<ProductIsTryBeforeBuyResp> searchProductIsTryBeforeBuy(@Param("weiMoGoods") List<String> weiMoGoodIds);

    List<MProductTryBeforeBuy> selectAllNormalList();
}
