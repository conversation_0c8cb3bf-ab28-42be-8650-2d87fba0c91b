package org.springcenter.product.modules.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.checkerframework.common.value.qual.StaticallyExecutable;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/10/11 15:27
 */
@Data
@TableName(value = "LABEL_RULE_DETAIL")
public class LabelRuleDetail implements Serializable {

    @TableId(value = "ID")
    private String id;

    @TableField(value = "LABEL_NAME")
    private String labelName;

    @TableField(value = "LABEL_NAME_CODE")
    private String labelNameCode;

    @TableField(value = "LABEL_NAME_TYPE")
    private Integer labelNameType;

    @TableField(value = "NAME_VALUE_RELATION")
    private Integer nameValueRelation;

    @TableField(value = "LABEL_VALUE")
    private String labelValue;

    @TableField(value = "LABEL_VALUE_CODE")
    private String labelValueCode;

    @TableField(value = "CALC_RELATION")
    private Integer calcRelation;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    @TableField(value = "RULE_MAIN_ID")
    private String ruleMainId;
}
