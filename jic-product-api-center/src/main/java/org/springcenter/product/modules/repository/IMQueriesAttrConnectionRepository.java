package org.springcenter.product.modules.repository;

import org.springcenter.product.api.dto.PVQueryStatsListReq;
import org.springcenter.product.modules.model.MPQueAttrValueConnect;
import org.springcenter.product.modules.model.MQueriesAttrConnection;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/2 13:15
 */
public interface IMQueriesAttrConnectionRepository {


    /**
     * 根据参数获取配置列表
     * @param requestData 入参
     * @return 返回
     */
    List<MQueriesAttrConnection> selectStatsListByParams(PVQueryStatsListReq requestData);

    /**
     * 根据attribute id和code 查询所有信息组装
     * @param list 入参
     * @return 返回
     */
    List<MQueriesAttrConnection> selectByAttributeInfo(List<MQueriesAttrConnection> list);

    /**
     * 批量保存关联关系
     * @param saveConnections 关联关系
     */
    void batchSaveConnections(List<MQueriesAttrConnection> saveConnections);

    /**
     * 批量更心关联关系
     * @param updateConnections 关联关系
     */
    void batchUpdateConnections(List<MQueriesAttrConnection> updateConnections);

    /**
     * 获取全部
     * @return
     */
    List<MQueriesAttrConnection> queryAllNormalConnections();


    void batchUpdateBySceAttrIds(List<Long> deletedSecAttr);

    /**
     * 获取该小类所有的关联
     * @param categoryCode 小类
     * @return 返回
     */
    List<MQueriesAttrConnection> selectBySmallCateCode(String categoryCode);


    /**
     * 根据属性id禁用
     * @param attrId 属性id
     */
    void updateByAttrId(Long attrId);

    /**
     * 根据属性id查询关联id
     * @param attrId 属性id
     * @return 返回
     */
    List<String> selectByAttrId(Long attrId);

    /**
     * 根据id将数据还原
     * @param ids id
     */
    void updateValidByIds(List<String> ids);

    /**
     * 查询当前的关联属性
     * @param attributeId 属性id
     * @return 主属性id
     */
    List<MQueriesAttrConnection> selectConnectByAttrIds(Long attributeId);

    /**
     * 根据小类目id查询全关联属性
     * @param id 数据
     * @return 返回
     */
    List<MQueriesAttrConnection> selectAllConnectBySmallCategoryId(Long id);
}
