package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.modules.mapper.bigdata.ProFaStorageQtyMapper;
import org.springcenter.product.modules.mapper.bojun.CStoreMapper;
import org.springcenter.product.modules.mapper.product.FabInfoMapper;
import org.springcenter.product.modules.mapper.zt.WeiMoBStorageMapper;
import org.springcenter.product.modules.model.FabInfo;
import org.springcenter.product.modules.model.bigdata.ProFaStorageQty;
import org.springcenter.product.modules.model.bojun.CStore;
import org.springcenter.product.modules.model.zt.WeiMoBStorage;
import org.springcenter.product.modules.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date:2024/3/13 13:29
 */
@Service
@Slf4j
public class TestServiceImpl implements TestService {

    @Autowired
    private ProFaStorageQtyMapper proFaStorageQtyMapper;

    @Autowired
    private CStoreMapper cStoreMapper;

    @Autowired
    private FabInfoMapper fabInfoMapper;

    @Autowired
    private WeiMoBStorageMapper weiMoBStorageMapper;


    @Override
    public void testDataBase() {
        log.info("===================大数据库存数据========================");
        QueryWrapper<ProFaStorageQty> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("C_STORE_ID", 24113L);
        queryWrapper1.eq("M_PRODUCT_ID", 596214L);
        ProFaStorageQty ret1 = proFaStorageQtyMapper.selectOne(queryWrapper1);
        log.info("===================大数据库存数据库存ret========================{}", JSONObject.toJSONString(ret1));

        log.info("===================伯俊数据========================");
        CStore ret2 = cStoreMapper.selectById(24113);
        log.info("===================伯俊数据ret========================{}", JSONObject.toJSONString(ret2));

        log.info("===================商品库数据========================");
        FabInfo ret3 = fabInfoMapper.selectById("102");
        log.info("===================商品库数据ret========================{}", JSONObject.toJSONString(ret3));


        /*log.info("===================zt数据========================");
        WeiMoBStorage ret4 = weiMoBStorageMapper.selectById("102");
        log.info("===================zt数据ret========================{}", JSONObject.toJSONString(ret4));*/
    }
}
