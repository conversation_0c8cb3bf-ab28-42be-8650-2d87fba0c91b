package org.springcenter.product.modules.model;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Author: yuanxiaozhong
 * @Date: 2023-02-08 16:43:02
 * @Description: gio页面浏览事件数据
 */
@TableName("GIO_ACTION_EVT_STAT")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="GioActionEvtStat对象", description="gio页面浏览事件数据")
public class GioActionEvtStat implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "访问用户ID，BOX导购为工号、小程序为unionid")
    @TableField("VISIT_USER_ID")
    private String visitUserId;
    @ApiModelProperty(value = "登录用户")
    @TableField("SESSION_ID")
    private String sessionId;
    @ApiModelProperty(value = "根据请求的类型不同，可能值为：clck(click), chng(change)，sbmt(submit)")
    @TableField("REQUEST_TYPE")
    @ExcelProperty("requestType")
    private String requestType;
    @ApiModelProperty(value = "应用分类；")
    @TableField(value = "\"DOMAIN\"")
    private String domain;
    @ApiModelProperty(value = "访问的路径")
    @TableField("PAGE")
    @ExcelProperty("page")
    private String page;
    @ApiModelProperty(value = "签内的跳转连接。")
    @TableField("HREF")
    @ExcelProperty("href")
    private String href;
    @ApiModelProperty(value = "该消息的值，例如标签的value。。")
    @TableField("REQUEST_VALUE")
    @ExcelProperty("requestValue")
    private String requestValue;
    @TableField(value = "\"INDEX\"")
    @ExcelProperty("index")
    private String index;
    @ApiModelProperty(value = "请求在用户端发生的时间戳。")
    @TableField(value = "\"TIME\"")
    @ExcelProperty("time")
    private Long time;
    @ApiModelProperty(value = "请求在SDK发送的时间戳。")
    @TableField("SEND_TIME")
    @ExcelProperty("sendTime")
    private Long sendTime;
    @ApiModelProperty(value = "页面请求来源ID")
    @TableField("PAGE_REQUEST_ID")
    @ExcelProperty("pageRequestId")
    private String pageRequestId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_DATE")
    private Date createDate;
    @ApiModelProperty(value = "action请求来源ID")
    @TableField("ACTION_REQUEST_ID")
    @ExcelProperty("actionRequestId")
    private String actionRequestId;

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getVisitUserId() {
        return visitUserId;
    }

    public GioActionEvtStat setVisitUserId(String visitUserId) {
        this.visitUserId = visitUserId;
        return this;
    }

    public String getSessionId() {
        return sessionId;
    }

    public GioActionEvtStat setSessionId(String sessionId) {
        this.sessionId = sessionId;
        return this;
    }

    public String getRequestType() {
        return requestType;
    }

    public GioActionEvtStat setRequestType(String requestType) {
        this.requestType = requestType;
        return this;
    }

    public String getDomain() {
        return domain;
    }

    public GioActionEvtStat setDomain(String domain) {
        this.domain = domain;
        return this;
    }

    public String getPage() {
        return page;
    }

    public GioActionEvtStat setPage(String page) {
        this.page = page;
        return this;
    }

    public String getHref() {
        return href;
    }

    public GioActionEvtStat setHref(String href) {
        this.href = href;
        return this;
    }

    public String getRequestValue() {
        return requestValue;
    }

    public GioActionEvtStat setRequestValue(String requestValue) {
        this.requestValue = requestValue;
        return this;
    }

    public String getIndex() {
        return index;
    }

    public GioActionEvtStat setIndex(String index) {
        this.index = index;
        return this;
    }

    public Long getTime() {
        return time;
    }

    public GioActionEvtStat setTime(Long time) {
        this.time = time;
        return this;
    }

    public Long getSendTime() {
        return sendTime;
    }

    public GioActionEvtStat setSendTime(Long sendTime) {
        this.sendTime = sendTime;
        return this;
    }

    public String getPageRequestId() {
        return pageRequestId;
    }

    public GioActionEvtStat setPageRequestId(String pageRequestId) {
        this.pageRequestId = pageRequestId;
        return this;
    }

    public String getActionRequestId() {
        return actionRequestId;
    }

    public void setActionRequestId(String actionRequestId) {
        this.actionRequestId = actionRequestId;
    }

    @Override
    public String toString() {
        return "GioActionEvtStatModel{" +
                "visitUserId=" + visitUserId +
                ", sessionId=" + sessionId +
                ", requestType=" + requestType +
                ", domain=" + domain +
                ", page=" + page +
                ", href=" + href +
                ", requestValue=" + requestValue +
                ", index=" + index +
                ", time=" + time +
                ", sendTime=" + sendTime +
                ", pageRequestId=" + pageRequestId +
                "}";
    }
}