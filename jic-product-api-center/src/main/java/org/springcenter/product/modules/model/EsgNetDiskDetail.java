package org.springcenter.product.modules.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName ESG_NET_DISK_DETAIL
 */
public class EsgNetDiskDetail implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * ESG主表id
     */
    private String esgNetDiskMainId;

    /**
     * 七牛云url
     */
    private String qiniuPath;

    /**
     * neid
     */
    private String neid;

    /**
     * nsid
     */
    private String nsid;

    /**
     * 0 未删除  1 删除
     */
    private Integer isDel;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 0  轮播  1   视频    2  系列说明
     */
    private Integer type;

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * ESG主表id
     */
    public String getEsgNetDiskMainId() {
        return esgNetDiskMainId;
    }

    /**
     * ESG主表id
     */
    public void setEsgNetDiskMainId(String esgNetDiskMainId) {
        this.esgNetDiskMainId = esgNetDiskMainId;
    }

    /**
     * 七牛云url
     */
    public String getQiniuPath() {
        return qiniuPath;
    }

    /**
     * 七牛云url
     */
    public void setQiniuPath(String qiniuPath) {
        this.qiniuPath = qiniuPath;
    }

    /**
     * neid
     */
    public String getNeid() {
        return neid;
    }

    /**
     * neid
     */
    public void setNeid(String neid) {
        this.neid = neid;
    }

    /**
     * nsid
     */
    public String getNsid() {
        return nsid;
    }

    /**
     * nsid
     */
    public void setNsid(String nsid) {
        this.nsid = nsid;
    }

    /**
     * 0 未删除  1 删除
     */
    public Integer getIsDel() {
        return isDel;
    }

    /**
     * 0 未删除  1 删除
     */
    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 0  轮播  1   视频    2  系列说明
     */
    public Integer getType() {
        return type;
    }

    /**
     * 0  轮播  1   视频    2  系列说明
     */
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        EsgNetDiskDetail other = (EsgNetDiskDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getEsgNetDiskMainId() == null ? other.getEsgNetDiskMainId() == null : this.getEsgNetDiskMainId().equals(other.getEsgNetDiskMainId()))
            && (this.getQiniuPath() == null ? other.getQiniuPath() == null : this.getQiniuPath().equals(other.getQiniuPath()))
            && (this.getNeid() == null ? other.getNeid() == null : this.getNeid().equals(other.getNeid()))
            && (this.getNsid() == null ? other.getNsid() == null : this.getNsid().equals(other.getNsid()))
            && (this.getIsDel() == null ? other.getIsDel() == null : this.getIsDel().equals(other.getIsDel()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getEsgNetDiskMainId() == null) ? 0 : getEsgNetDiskMainId().hashCode());
        result = prime * result + ((getQiniuPath() == null) ? 0 : getQiniuPath().hashCode());
        result = prime * result + ((getNeid() == null) ? 0 : getNeid().hashCode());
        result = prime * result + ((getNsid() == null) ? 0 : getNsid().hashCode());
        result = prime * result + ((getIsDel() == null) ? 0 : getIsDel().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", esgNetDiskMainId=").append(esgNetDiskMainId);
        sb.append(", qiniuPath=").append(qiniuPath);
        sb.append(", neid=").append(neid);
        sb.append(", nsid=").append(nsid);
        sb.append(", isDel=").append(isDel);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", type=").append(type);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}