package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springcenter.product.modules.entity.BatchProductLabel;
import org.springcenter.product.modules.model.SysProductLabel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysProductLabelMapper extends BaseMapper<SysProductLabel> {
    int insert(SysProductLabel record);

    int insertSelective(SysProductLabel record);

    void delByProductIds(@Param("productIds") List<String> productIds);

    void delByProductIdsAndColorNos(@Param("list") List<BatchProductLabel> list);

    List<String> selectByDistinctSkcCode();
}
