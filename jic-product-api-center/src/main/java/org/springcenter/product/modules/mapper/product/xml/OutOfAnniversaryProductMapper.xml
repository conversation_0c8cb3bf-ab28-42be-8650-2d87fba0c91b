<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.OutOfAnniversaryProductMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.OutOfAnniversaryProduct">
    <id column="ID"  property="id" />
    <result column="SPU" property="spu"/>
    <result column="TYPE" property="type"/>
    <result column="IS_DELETED" property="isDeleted"/>
    <result column="CREATE_TIME" property="createTime"/>
    <result column="UPDATE_TIME" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    ID, SPU, IS_DELETED, "TYPE", CREATE_TIME, UPDATE_TIME
  </sql>
  <select id="selectSpus" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
        from OUT_OF_ANNIVERSARY_PRODUCT
    where IS_DELETED = 0 and
          SPU in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>


</mapper>
