package org.springcenter.product.modules.service;

import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.modules.remote.entity.WeimoGoodShareLinkReq;
import org.springcenter.product.modules.remote.entity.WeimoGoodShareLinkResp;

import java.util.List;

public interface IProductStoreService {

    /**
     * 查询门店下SKC数据
     * @param reg
     * @param page
     * @return
     */
    List<ProductSkcResp> queryStoreGoodSkc(QueryGoodsSkcListReq reg, Page page);

    /**
     * 查询门店下商品
     * @param context
     * @param page
     * @return
     */
    List<StoreGoodSpuResp> searchGoodsByStore(StoreGoodsReq context, Page page);

    /**
     * 查询微商城下的SKC列表
     * @param goodsReq
     * @param page
     * @return
     */
    List<ProductSkcResp> searchMallProductSkc(QueryGoodsSkcListReq goodsReq, Page page);

    /**
     * 查询商品spu维度的列表
     * @param requestData 入参
     * @param page 页码
     * @return 返回
     */
    List<ProductSkcResp> searchMallProductSpu(QueryGoodsSkcListReq requestData, Page page);

    /**
     * 查询云店vid下的商品skc
     * @param requestData 入参
     * @param page 分页
     * @return 返回
     */
    List<ProductVidSkcResp> queryVidMallGoodSkc(QueryGoodsSkcListReq requestData, Page page);


    /**
     * 修改vid的库存
     * @param storeId 门店id
     * @param productId 商品id
     * @param id 主键
     */
    void changeStoreGoodsByListenerVidStorage(Long storeId, Long productId, Long id, String type, String weId, Long skuId);

    /**
     * 导入不同品牌的门店商品数据
     * @param requestData 入参
     * @return 返回
     */
    Boolean exportVidProduct(ExportVidProductReq requestData);

    /**
     * 修改商品是否可售逻辑
     * @param isCanSellId 是否可售id
     * @param brandId 品牌
     * @param params 参数
     */
    void changeVidProductIsCanSell(Long isCanSellId, String brandId, String params);

    /**
     * 修改商品是否上下架逻辑
     * @param isPutaway id
     * @param brandId 品牌
     * @param params 参数
     */
    void changeVidProductIsPutaway(Long isPutaway, String brandId, String params, String event);

    /**
     * 获取微盟分享链接
     * @param request 入参
     * @return 返回
     */
    List<WeimoGoodShareLinkResp> getProductWeimoShareLink(CommonRequest<WeimoGoodShareLinkReq> request);


    /**
     * 根据券id查询券商品列表
     * @param requestData 入参
     * @param page 页码
     * @return 返回
     */
    List<StoreGoodSpuResp> getCouponGoodsListByStore(StoreCouponGoodsReq requestData, Page page);
}
