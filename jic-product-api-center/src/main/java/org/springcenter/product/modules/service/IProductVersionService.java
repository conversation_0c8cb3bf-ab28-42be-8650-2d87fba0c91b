package org.springcenter.product.modules.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import org.springcenter.product.modules.model.MProductVersion;
import org.springcenter.product.api.dto.*;

import java.io.IOException;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【M_QUERIES_TAB(查询标签)】的数据库操作Service
* @createDate 2022-11-30 17:17:15
*/
public interface IProductVersionService extends IService<MProductVersion> {

    /**
     * 获取版型库管理-查询参数
     * @return 返回数据
     */
    ProductVersionQueriesTabResp getProductVersionQueriesTab(CommonRequest request);

    /**
     * excel解析导入的版型商品
     * @param url 链接
     * @param keys redis的key
     */
    void downFileForProductVersion(String url, String keys) throws IOException;


    List<ProductVersionListDataResp> queryList(ProductVersionListDataReq requestData, Page page, String component);

    /**
     * 获取商品信息
     * @param requestData 请求参数
     * @return 返回信息
     */
    ProductVersionDetailResp queryDetail(ProductVersionDetailReq requestData);

    /**
     * 编辑商品
     * @param requestData 请求参数
     * @return 返回信息
     */
    Boolean editDetail(ProductVersionEditDetailReq requestData);

    /**
     * 查询
     * @param requestData
     * @return
     */
    ProductVersionSalesResp querySales(ProductVersionSalesReq requestData, Page page);

    ProductVersionSalesQueriesTabResp querySalesQueries(ProductVersionSalesQueriesReq req);

    ProductVersionEditDicResp queryDetailDic(ProductVersionDetailDicReq requestData);

    /**
     * 版型库属性配置的筛选项
     * @param requestData 入参
     * @return 返回
     */
    List<PVQueryStatsResp> queryStatsParams(PVQueryStatsReq requestData);

    /**
     * 分页查询属性配置列表
     * @param requestData 入参
     * @param page 页码
     * @return 返回
     */
    List<PVQueryStatsListResp> queryStatsList(PVQueryStatsListReq requestData, Page page);

    /**
     * 查询配置详情
     * @param requestData 属性id
     * @return 返回
     */
    PVQueryStatsDetailResp queryStatsDetail(PVQueryStatsDetailReq requestData);

    /**
     * 编辑详情
     * @param requestData 保存
     * @return 返回
     */
    Boolean saveStatsDetail(PVSaveQueryStatsDetailReq requestData);

    /**
     * 更新excel
     * @param url 链接
     * @param keys redis的key
     */
    void downFileForUpdateProductVersion(String url, String keys) throws IOException;

    /**
     * 起禁用属性
     * @param requestData
     * @return
     */
    Boolean enableDisable(PVEnableDisableAttrReq requestData);
}
