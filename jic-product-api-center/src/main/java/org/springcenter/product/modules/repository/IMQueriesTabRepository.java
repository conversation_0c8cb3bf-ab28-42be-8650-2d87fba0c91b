package org.springcenter.product.modules.repository;

import org.springcenter.product.modules.model.MQueriesTab;
import org.springcenter.product.api.enums.MQueriesTabEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/2 13:15
 */
public interface IMQueriesTabRepository {
    /**
     * 查询品牌
     * @param brand 枚举
     * @return 返回品牌信息
     */
    List<MQueriesTab> getBrandList(MQueriesTabEnum brand);

    /**
     * 获取类目
     * @param category 枚举
     * @return 返回类目列表
     */
    List<MQueriesTab> getCateGoryList(MQueriesTabEnum category);

    /**
     * 获取属性
     * @param attribute 枚举
     * @return 返回属性列表
     */
    List<MQueriesTab> getAttributeList(MQueriesTabEnum attribute);

    /**
     * 获取子属性
     * @param attributeSub 入参
     * @return 出参
     */
    List<MQueriesTab> getSubAttributeList(MQueriesTabEnum attributeSub);

    /**
     * 获取版型库列表的条件
     * @param listFilter 入参
     * @return 出参
     */
    List<MQueriesTab> getProductVersionListFilter(MQueriesTabEnum listFilter);

    List<MQueriesTab> getSalesFilterList(MQueriesTabEnum salesListFilter);

    /**
     * 获取所有主类目
     * @param category 枚举
     * @return 返回
     */
    List<MQueriesTab> getFirstCateGoryList(MQueriesTabEnum category);

    /**
     * 根据父级获取第二级父类
     * @param category 枚举
     * @param categoryId pid
     * @return 返回
     */
    List<MQueriesTab> getCateGoryListByPid(MQueriesTabEnum category, Integer categoryId);


    /**
     * 根据父级属性获取属性值
     * @param attributeId 属性id
     * @return 返回
     */
    List<MQueriesTab> getAttributeListByPidIncludeDeleted(MQueriesTabEnum category, Long attributeId);

    /**
     * 根据父类属性获取二级属性值
     * @param attributeSub 属性枚举
     * @param attributeId 属性id
     * @return 返回
     */
    List<MQueriesTab> getSubAttrListByPidIncludeDeleted(MQueriesTabEnum attributeSub, Long attributeId);

    /**
     * 批量保存属性
     * @param saveAttrValueList 属性
     */
    void batchSaveAttrValue(List<MQueriesTab> saveAttrValueList);

    /**
     * 批量更新属性
     * @param updateAttrValueList 属性
     */
    void batchUpdateAttrValue(List<MQueriesTab> updateAttrValueList);

    /**
     * 根据id查询属性
     * @param attributeId id
     * @return 返回
     */
    MQueriesTab getAttrById(Long attributeId);
}
