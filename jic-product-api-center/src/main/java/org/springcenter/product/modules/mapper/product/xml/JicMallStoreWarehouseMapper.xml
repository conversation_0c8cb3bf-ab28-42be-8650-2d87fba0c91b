<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.JicMallStoreWarehouseMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.JicMallStoreWarehouse">
        <id column="ID" property="id" />
        <result column="MALL_STORE_ID" property="mallStoreId" />
        <result column="WAREHOUSE_CODE" property="warehouseCode" />
        <result column="STORE_CODE" property="storeCode" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="BRAND_ID" property="brandId" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, MALL_STORE_ID, STORE_CODE, WAREHOUSE_CODE, CREATE_TIME, BRAND_ID
    </sql>

    <select id="selectByStoreCodeAndBrandId" resultMap="BaseResultMap">
        select ID, MALL_STORE_ID, STORE_CODE,  CREATE_TIME, BRAND_ID,
        case when WAREHOUSE_CODE = 'NTGX0000'  then 'NT'
             when WAREHOUSE_CODE = 'NTGX0001'  then 'QUYU'
        else 'CD' end as WAREHOUSE_CODE
            FROM userwx.JIC_MALL_STORE_WAREHOUSE
        where WAREHOUSE_CODE IN ( 'NTGX0000', 'NTGX0001', 'CD01' )
        and BRAND_ID = #{brandId} and STORE_CODE = #{vidCode}
    </select>

    <select id="selectByTypeAndBrandId" resultType="java.lang.String">
        select INSIDE_ID from (
        select <include refid="Base_Column_List"/> from userwx.jic_mall_store_warehouse
                where brand_id = #{weId}  and WAREHOUSE_CODE = #{type})  b
            left join userwx.JIC_MALL_ORG c on b.STORE_CODE = c.VID_CODE
        where c.VID_TYPE = 10
    </select>


</mapper>
