package org.springcenter.product.modules.service;

import org.springcenter.product.api.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/10/11 16:09
 */
public interface IProductLabelRuleService {

    /**
     * 添加标签规则
     * @param requestData 入参
     * @return 返回
     */
    Boolean addLabelRule(AddLabelRuleReq requestData);

    /**
     * 查询标签规则
     * @param requestData 标签id
     * @return 返回
     */
    SearchLabelRuleResp searchLabelRule(SearchLabelRuleReq requestData);


    /**
     * 修改标签规则
     * @param requestData 数据
     * @return 返回
     */
    Boolean updateLabelRule(UpdateLabelRuleReq requestData);


    /**
     * 查询属性
     * @param requestData 入参
     * @return 返回
     */
    List<QueryLabelRuleSettingResp> queryLabelRuleSetting(QueryLabelRuleSettingReq requestData);


    void dealLabelRuleToCreate();


    /**
     * 根据规则生成标签
     * @param requestData 若为空 生成所有商品的关于规则的标签；如果不为空 返回单个商品的标签
     * @return 返回
     */
    List<CreateCalcLabelResp> createLabel(List<String> requestData);

    /**
     * 判断当前节点是否能添加数据
     * @param requestData
     * @return
     */
    Boolean judgeIsCanCreate(String requestData);

    /**
     * 获取skc的属性和标签信息
     * @param requestData
     * @return
     */
    LabelProductInfoEsResp getLabelProductInfo(String requestData);

    /**
     * 根据lablecode删除规则
     * @param requestData labelCode
     * @return 返回
     */
    Boolean beforeDelLabel(String requestData);

    /**
     * 定期拉取带麻带丝的数据
     */
    void pullOutsideNameJob();
}
