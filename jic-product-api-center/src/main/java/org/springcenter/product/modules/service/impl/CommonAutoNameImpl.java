package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.product.api.dto.FabSpuForAutoNameEsResp;
import org.springcenter.product.api.dto.FabSpuForAutoNameResp;
import org.springcenter.product.modules.entity.CommonAutoNameGenerateEntity;
import org.springcenter.product.modules.entity.CommonAutoNameSettingEntity;
import org.springcenter.product.modules.entity.CommonSearchByPageAndIdEntity;
import org.springcenter.product.modules.mapper.product.FabAnDynamicFieldSettingMapper;
import org.springcenter.product.modules.mapper.product.FabAutoNameSettingMapper;
import org.springcenter.product.modules.model.FabAnDynamicFieldSetting;
import org.springcenter.product.modules.model.FabAutoNameSetting;
import org.springcenter.product.modules.service.CommonAutoName;
import org.springcenter.product.modules.service.FabVolumeInfoService;
import org.springcenter.product.modules.util.EsUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/1/17 14:53
 */
@Service
@Slf4j
public class CommonAutoNameImpl implements CommonAutoName {

    @Autowired
    private FabAutoNameSettingMapper fabAutoNameSettingMapper;

    @Autowired
    private FabAnDynamicFieldSettingMapper fabAnDynamicFieldSettingMapper;

    @Autowired
    private FabVolumeInfoService fabVolumeInfoService;

    @Autowired
    private EsUtil esUtil;

    @Override
    public CommonAutoNameSettingEntity getAutoNameSettingInfo() {
        CommonAutoNameSettingEntity commonAutoNameSettingEntity = new CommonAutoNameSettingEntity();
        QueryWrapper<FabAutoNameSetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", 0);
        List<FabAutoNameSetting> fabAutoNameSettings = fabAutoNameSettingMapper.selectList(queryWrapper);
        List<FabAutoNameSetting> partSortedList = fabAutoNameSettings.stream()
                .sorted(Comparator.comparing(FabAutoNameSetting::getAssemblePartOrder)).collect(Collectors.toList());

        QueryWrapper<FabAnDynamicFieldSetting> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("IS_DELETED", 0);
        List<FabAnDynamicFieldSetting> fabAnDynamicFieldSettings = fabAnDynamicFieldSettingMapper.selectList(queryWrapper1);

        // 违禁词查询
        List<String> forbiddenList = fabVolumeInfoService.queryFabProhibitedWords();

        commonAutoNameSettingEntity.setFabAnDynamicFieldSettings(fabAnDynamicFieldSettings);
        commonAutoNameSettingEntity.setForbiddenList(forbiddenList);
        commonAutoNameSettingEntity.setPartSortedList(partSortedList);
        return commonAutoNameSettingEntity;
    }

    @Override
    public void getAutoGenerate(CommonAutoNameGenerateEntity entity, String tag) {
        entity.getPartSortedList().forEach(x -> {
            // 样衣自动品名特殊处理 去除系列、品牌、货季  大货自动品名需要
            if (StringUtils.isNotBlank(tag) &&
                    (Objects.equals("file1_name", x.getField()) ||
                            Objects.equals("d_pp", x.getField()) ||
                            Objects.equals("good_season", x.getField()))) {
                return;
            }
            String fieldValue = getFieldValue(x.getField(), entity.getResp());
            if (StringUtils.isBlank(fieldValue)) {
                return;
            }

            // 判断当前是动态字段还是静态字段
            List<FabSpuForAutoNameResp> dynamicOrStaticList = new ArrayList<>();
            if (Objects.equals(x.getType(), 1)) {
                List<FabAnDynamicFieldSetting> fabAnDynamicFieldSettingList = entity.getFabAnDynamicFieldSettings()
                        .stream()
                        .filter(u -> Objects.equals(u.getFabAutoNameId(), x.getId()))
                        .filter(u -> Objects.equals(u.getBelongAttr(), fieldValue))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(fabAnDynamicFieldSettingList) && !Objects.equals(fieldValue, "空")) {
                    log.error("==============未找到该字段的动态字段：{}, 值为：{}", JSONObject.toJSONString(x), fieldValue);
                    return;
                }

                fabAnDynamicFieldSettingList.forEach(u -> {
                    if (Objects.equals(u.getType(), 1)) {
                        String fieldValue1 = getFieldValue(u.getField(), entity.getResp());
                        if (StringUtils.isBlank(fieldValue1)) {
                            return;
                        }
                        FabSpuForAutoNameResp fabSpuForAutoNameResp = new FabSpuForAutoNameResp();
                        BeanUtils.copyProperties(x, fabSpuForAutoNameResp);
                        fabSpuForAutoNameResp.setDataValue(fieldValue1);
                        fabSpuForAutoNameResp.setAssembleWholeOrder(u.getWholeOrder());
                        fabSpuForAutoNameResp.setAssemblePartOrder(u.getPartOrder());
                        fabSpuForAutoNameResp.setField(u.getField());
                        dynamicOrStaticList.add(fabSpuForAutoNameResp);
                    } else {
                        FabSpuForAutoNameResp fabSpuForAutoNameResp = new FabSpuForAutoNameResp();
                        BeanUtils.copyProperties(x, fabSpuForAutoNameResp);
                        fabSpuForAutoNameResp.setDataValue(u.getFieldName());
                        fabSpuForAutoNameResp.setAssembleWholeOrder(u.getWholeOrder());
                        fabSpuForAutoNameResp.setAssemblePartOrder(u.getPartOrder());
                        dynamicOrStaticList.add(fabSpuForAutoNameResp);
                    }
                });

            } else {
                FabSpuForAutoNameResp fabSpuForAutoNameResp = new FabSpuForAutoNameResp();
                BeanUtils.copyProperties(x, fabSpuForAutoNameResp);
                fabSpuForAutoNameResp.setDataValue(fieldValue);
                fabSpuForAutoNameResp.setAssembleWholeOrder(x.getAssembleWholeOrder());
                fabSpuForAutoNameResp.setAssemblePartOrder(x.getAssemblePartOrder());
                fabSpuForAutoNameResp.setField(x.getField());
                dynamicOrStaticList.add(fabSpuForAutoNameResp);
            }

            if (CollectionUtils.isEmpty(dynamicOrStaticList)) {
                return;
            }


            dynamicOrStaticList.forEach(u -> {
                // 图案（属于动态字段组合）和品牌字段值重合，体型（属于动态字段组合）和品名字段值重合
                if (Objects.equals(u.getField(), "d_pp")) {
                    entity.getDpp().set(u.getDataValue());
                }
                if (Objects.equals(u.getField(), "adjust_name")) {
                    entity.getDesignName().set(u.getDataValue());
                }

                if (Objects.equals(u.getField(), "pattern_name") && StringUtils.isNotBlank(entity.getDpp().get())
                        && entity.getDpp().get().contains(u.getDataValue())) {
                    return;
                }
                if (Objects.equals(u.getField(), "tixing") && StringUtils.isNotBlank(entity.getDesignName().get())
                        && entity.getDesignName().get().contains(u.getDataValue())) {
                    return;
                }

                // 违禁词 处理
                if (entity.getForbiddenList().contains(u.getDataValue())) {
                    return;
                }
                if (StringUtils.isBlank(u.getDataValue()) || Objects.equals("null", u.getDataValue())) {
                    return;
                }

                int fieldChar = u.getDataValue().length();
                if (entity.getCharLen()[0] + fieldChar <= 35) {
                    entity.getPartList().add(u);
                    entity.getWholeList().add(u);
                    entity.getCharLen()[0] = entity.getCharLen()[0] + fieldChar;
                } else {
                    entity.getWholeList().add(u);
                }
            });
        });
    }

    private static String getFieldValue(String fieldName, FabSpuForAutoNameEsResp info) {
        try {
            Field field = info.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return Objects.toString(field.get(info));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }



    public Integer searchMinAndMaxNumInEs(SortOrder sortOrder, String index) {
        SearchRequest request = new SearchRequest();
        request.indices(index);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(1);
        sourceBuilder.sort("id", sortOrder);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品searchMinNumInEs {}", request.source().toString());
        Integer minNum = 0;
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return 0;
            }
            SearchHit[] hits = response.getHits().getHits();
            FabSpuForAutoNameEsResp entity = FabSpuForAutoNameEsResp.fromJson(hits[0].getSourceAsString(), FabSpuForAutoNameEsResp.class);
            minNum = Integer.valueOf(entity.getId());
        } catch (IOException e) {
            log.error("查询商品searchMinNumInEs异常e = {}", e.getMessage());
            return 0;
        }
        return minNum;
    }

    @Override
    public List<FabSpuForAutoNameEsResp> searchDataForAutoNameInEsForNum(CommonSearchByPageAndIdEntity entity) {
        SearchRequest request = new SearchRequest();
        request.indices(entity.getIndex());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        int minPage = entity.getMinNum();
        int maxPage = entity.getMinNum() + entity.getPageSize();
        if (entity.getI() != 0) {
            minPage = entity.getMinNum() + entity.getI() * entity.getPageSize();
            maxPage = minPage + entity.getPageSize();
            if (maxPage == entity.getMaxNum()) {
                maxPage = entity.getMaxNum() + 1;
            }
        }
        queryBuilder.must(QueryBuilders.rangeQuery("id").gte(minPage).lt(maxPage));
        sourceBuilder.sort("id", SortOrder.ASC);
        sourceBuilder.size(10000);
        sourceBuilder.query(queryBuilder);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品FAB_FOR_AUTO_NAME:{}", request.source().toString());
        List<FabSpuForAutoNameEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                FabSpuForAutoNameEsResp resp = FabSpuForAutoNameEsResp.fromJson(hit.getSourceAsString(), FabSpuForAutoNameEsResp.class);
                resp.setId(Integer.valueOf(hit.getId()));
                entities.add(resp);
            }
        } catch (IOException e) {
            log.error("查询商品FAB_FOR_AUTO_NAME异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }
}
