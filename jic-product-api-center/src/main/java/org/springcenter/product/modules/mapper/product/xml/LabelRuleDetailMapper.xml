<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.LabelRuleDetailMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.LabelRuleDetail">
    <result column="ID" property="id" />
    <result column="LABEL_NAME" property="labelName" />
    <result column="LABEL_NAME_CODE" property="labelNameCode" />
    <result column="LABEL_NAME_TYPE" property="labelNameType" />
    <result column="NAME_VALUE_RELATION" property="nameValueRelation" />
    <result column="LABEL_VALUE" property="labelValue" />
    <result column="LABEL_VALUE_CODE" property="labelValueCode" />
    <result column="CALC_RELATION" property="calcRelation" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="IS_DELETED" property="isDeleted" />
    <result column="RULE_MAIN_ID" property="ruleMainId" />
  </resultMap>

  <insert id="insertBatch">
    INSERT ALL
    <foreach item="item" index="index" collection="list">
      INTO LABEL_RULE_DETAIL
      (ID, LABEL_NAME, LABEL_NAME_CODE, LABEL_NAME_TYPE, NAME_VALUE_RELATION, LABEL_VALUE, LABEL_VALUE_CODE,
      CALC_RELATION, CREATE_TIME, UPDATE_TIME, RULE_MAIN_ID) VALUES
      (#{item.id,jdbcType=VARCHAR}, #{item.labelName,jdbcType=VARCHAR}, #{item.labelNameCode,jdbcType=VARCHAR},
      #{item.labelNameType,jdbcType=DECIMAL}, #{item.nameValueRelation,jdbcType=DECIMAL}, #{item.labelValue,jdbcType=VARCHAR},
      #{item.labelValueCode,jdbcType=VARCHAR}, #{item.calcRelation,jdbcType=DECIMAL},#{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP}, #{item.ruleMainId,jdbcType=VARCHAR})
    </foreach>
    SELECT 1 FROM DUAL
  </insert>

  <update id="batchUpdate">
    <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
      update LABEL_RULE_DETAIL
      set
      LABEL_NAME = #{item.labelName},
      <if test="item.labelNameCode != null and item.labelNameCode != ''">
        LABEL_NAME_CODE = #{item.labelNameCode},
      </if>
      LABEL_NAME_TYPE = #{item.labelNameType},
      NAME_VALUE_RELATION = #{item.nameValueRelation},
      LABEL_VALUE = #{item.labelValue},
      LABEL_VALUE_CODE = #{item.labelValueCode,jdbcType=VARCHAR},
      CALC_RELATION = #{item.calcRelation},
      RULE_MAIN_ID = #{item.ruleMainId},
      IS_DELETED = #{item.isDeleted},
      UPDATE_TIME = sysdate
      where ID = #{item.id}
    </foreach>
  </update>


</mapper>