package org.springcenter.product.modules.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.BufferedSource;

import java.io.InputStream;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
public class HttpUtils {

    private HttpUtils() {
    }

    public static OkHttpClient getInstance() {
        return HttpUtils.Singleton.INSTANCE.getInstance();
    }

    public static String get(String url) {
        Request request = (new Request.Builder()).url(url).get().build();

        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            return responseBody.string();
        } catch (Exception var3) {
            return null;
        }
    }

    public static String get(String url, Headers headers) {
        Request request = (new Request.Builder()).url(url).headers(headers).get().build();

        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            return responseBody.string();
        } catch (Exception var4) {
            return null;
        }
    }

    public static String post(String url, Map<String, Object> params) {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody requestBody = FormBody.create(mediaType, JSON.toJSONString(params));
        Request request = (new Request.Builder()).url(url).post(requestBody).build();

        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            return responseBody.string();
        } catch (Exception var6) {
            return null;
        }
    }

    public static String postForm(String url, Map<String, String> params,Headers headers) {
        log.info("postForm req url = {}",url);
        log.info("postForm req params = {}" , JSONObject.toJSONString(params));
        log.info("postForm req header = {}",headers.toString());

        FormBody.Builder builder = new FormBody.Builder();
        for (String key : params.keySet()) {
            builder.add(key,params.get(key))
                    .build();
        }
        FormBody formBody = builder.build();
        Request request = (new Request.Builder()).url(url).post(formBody).headers(headers).build();
        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            String string = responseBody.string();
            log.info("postForm resp = {}",string);
            return string;
        } catch (Exception var6) {
            return null;
        }
    }



    public static String post(String url, String body) {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody requestBody = FormBody.create(mediaType, body);
        Request request = (new Request.Builder()).url(url).post(requestBody).build();

        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            return responseBody.string();
        } catch (Exception var6) {
            return null;
        }
    }

    public static void main(String[] args) {
        System.out.println(get("https://bzhz.jnbygroup.com/api/segment/get/leaf-segment-test"));
    }

    public static String postFormDownLoad(String url, Map<String, String> params, Headers headers) {
        log.info("postFormDownLoad req url = {}",url);
        log.info("postFormDownLoad req params = {}" , JSONObject.toJSONString(params));
        log.info("postFormDownLoad req header = {}",headers.toString());

        FormBody.Builder builder = new FormBody.Builder();
        for (String key : params.keySet()) {
            builder.add(key,params.get(key))
                    .build();
        }
        FormBody formBody = builder.build();
        Request request = (new Request.Builder()).url(url).post(formBody).headers(headers).build();
        try {
            Response execute = getInstance().newCall(request).execute();
            HttpUrl finalUrl = execute.request().url();
            ResponseBody responseBody = execute.body();
            BufferedSource source = responseBody.source();
            log.info("postFormDownLoad resp = {}",source);
            return finalUrl.url().toString();
        } catch (Exception var6) {
            return null;
        }

    }

    private static enum Singleton {
        INSTANCE;

        private OkHttpClient singleton;

        private Singleton() {
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            builder.connectTimeout(6L, TimeUnit.SECONDS);
            builder.readTimeout(6L, TimeUnit.SECONDS);
            builder.writeTimeout(6L, TimeUnit.SECONDS);
            int ioWorkerCount = Math.max(Runtime.getRuntime().availableProcessors() * 6, 4);
            ConnectionPool connectionPool = new ConnectionPool(ioWorkerCount, 4L, TimeUnit.SECONDS);
            builder.connectionPool(connectionPool);
            this.singleton = builder.build();
        }

        public OkHttpClient getInstance() {
            return this.singleton;
        }
    }
}
