<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.SampleCloFabInfoMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.SampleCloFabInfo">
        <id column="ID" property="id" />
        <result column="BRAND" property="brand" />
        <result column="BAND" property="band" />
        <result column="YEAR" property="year" />
        <result column="SAMPLE_CODE" property="sampleCode" />
        <result column="FAB" property="fab" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="GOOD_SEASON" property="goodSeason" />
        <result column="COLOR_NO" property="colorNo" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, BRAND, BAND, YEAR, SAMPLE_CODE, FAB, CREATE_TIME, UPDATE_TIME, IS_DELETED, GOOD_SEASON, COLOR_NO
    </sql>

    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO SAMPLE_CLO_FAB_INFO
            (ID, BRAND, BAND, YEAR, SAMPLE_CODE, FAB, CREATE_TIME, UPDATE_TIME, GOOD_SEASON, COLOR_NO) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR}, #{item.band,jdbcType=VARCHAR},
            #{item.year,jdbcType=VARCHAR}, #{item.sampleCode,jdbcType=VARCHAR},#{item.fab,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.goodSeason,jdbcType=VARCHAR}, #{item.colorNo,jdbcType=VARCHAR})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <update id="updateByParam">
        UPDATE SAMPLE_CLO_FAB_INFO
        SET IS_DELETED = 1
        WHERE IS_DELETED = 0
        <if test="data.year != null and data.year != ''">
            AND YEAR = #{data.year}
        </if>
        <if test="data.bandStr != null and data.bandStr != ''">
            AND BAND = #{data.bandStr}
        </if>
        <if test="data.goodSeason != null and data.goodSeason != ''">
            AND GOOD_SEASON = #{data.goodSeason}
        </if>
        <if test="data.brands != null and data.brands.size() > 0">
            AND BRAND IN
            <foreach collection="data.brands" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="data.sampleCodes != null and data.sampleCodes.size() > 0">
            AND SAMPLE_CODE IN
            <foreach collection="data.sampleCodes" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>

    <select id="selectBySampleCodes" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"></include>
            FROM SAMPLE_CLO_FAB_INFO
        WHERE IS_DELETED = 0 AND SAMPLE_CODE IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>


</mapper>
