package org.springcenter.product.modules.model.zt;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/6/13 13:36
 */
@Data
@TableName(value = "WEIMOBSTORAGELOG")
public class WeiMoBStorageLog {
    @TableField(value = "ID")
    private Long id;

    @TableField(value = "TYPE")
    private String type;

    @TableField(value = "C_STORE_ID")
    private Long cStoreId;

    @TableField(value = "M_PRODUCT_ID")
    private Long mProductId;

    @TableField(value = "M_PRODUCTALIAS_ID")
    private Integer mProductAliasId;

    @TableField(value = "QTY")
    private Long qty;

    @TableField(value = "WEID")
    private Long weId;

    @TableField(value = "WEIMOBSTOREID")
    private Long weiMoBStoreId;

    @TableField(value = "WEIMOBSKUID")
    private Long weiMoBSkuId;

    @TableField(value = "LOGDATE")
    private Date logDate;
}
