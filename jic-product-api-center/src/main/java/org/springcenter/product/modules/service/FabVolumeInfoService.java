package org.springcenter.product.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/11/14 10:03
 */
public interface FabVolumeInfoService {
    /**
     * 保存fab产品册信息
     * @param requestData 请求
     * @return 返回
     */
    Boolean saveFabVolumeInfo(AddFabVolumeInfoReq requestData);


    /**
     * fab产品册信息列表
     * @param requestData 请求
     * @return 返回
     */
    List<QueryFabVolumeInfoResp> fabVolumeInfoList(QueryFabVolumeInfoReq requestData, Page page, String component);


    /**
     * 根据id查询详情
     * @param requestData id
     * @return 返回
     */
    FabVolumeInfoDetailResp searchFabVolumeInfo(String requestData);


    /**
     * 修改fab信息
     * @param requestData 入参
     * @return 返回
     */
    Boolean updateFabVolumeInfo(UpdateFabVolumeInfoReq requestData);

    /**
     * 设置展示时间
     * @param requestData 入参
     * @return 返回
     */
    Boolean setFabVolumeInfoDisplayTime(SetFabVolumeInfoDisplayTimeReq requestData);


    /**
     * 立即发布
     * @param requestData 入参
     * @return 返回
     */
    Boolean setFabVolumeInfoPublish(SetFabVolumeInfoDisplayTimeReq requestData);


    /**
     * 删除
     * @param requestData 入参
     * @return 返回
     */
    Boolean deleteFabVolumeInfo(SetFabVolumeInfoDisplayTimeReq requestData);


    /**
     * pos端查询产品册信息
     * @param requestData 入参
     * @return 返回
     */
    List<FabVolumeInfoListForC> queryFabVolumeInfoForC(QueryFabVolumeInfoReq requestData, Page page);


    /**
     * fab违禁词查询
     * @return 返回违禁词列表
     */
    List<String> queryFabProhibitedWords();

    /**
     * 返回产品册的筛选参数
     * @return 返回
     */
    List<FabVolumeInfoParam> queryFabVolumeParams();

    /**
     * 查询当前是否能创建产品册 true可以 false不可以
     * @return true可以 false不可以
     */
    Boolean judgeCreateFab(JudgeCreateFabReq req);

    /**
     * 任务中心-根据筛选条件查询产品册信息
     * @param requestData 入参
     * @param page 分页
     * @return 返回
     */
    List<FabVolumeInfoListForC> queryFabVolumeInfoForTask(QueryFabVolumeInfoReq requestData, Page page);
}
