package org.springcenter.product.modules.service;

import org.springcenter.product.api.dto.ProductZhuiDanInfoResp;
import org.springcenter.product.api.dto.ParentChildInfoReq;
import org.springcenter.product.api.dto.ParentChildInfoResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/6/14 15:44
 */
public interface IProductRelationInfoService {

    /**
     * 根据商品id找追单信息
     * @param requestData 商品id
     * @return 找对应id的追单或原单信息
     */
    List<ProductZhuiDanInfoResp> queryProductZhuiDanInfo(List<Long> requestData);
    /**
     * 查询商品的亲子款
     * @param requestData 商品id
     * @return 返回
     */
    List<ParentChildInfoResp> queryProductParentChildInfo(ParentChildInfoReq requestData);
}
