package org.springcenter.product.modules.service;

import org.springcenter.product.api.dto.PreSaleProdCoverPicsResp;
import org.springcenter.product.api.dto.PreSaleProductDetailReq;
import org.springcenter.product.api.dto.PreSaleProductDetailResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/8/17 10:59
 */
public interface IProductPreSaleService {


    /**
     * 查询预售商品详情
     * @param requestData 入参
     * @return 返回
     */
    PreSaleProductDetailResp searchPreSaleProductDetail(PreSaleProductDetailReq requestData);

    /**
     *  * 导入模特图
     * @param path 文件路径
     * @return 返回导入成功||失败
     */
    Boolean importModelPics(String path);

    /**
     * 根据款号批量查询封面图
     * @param requestData 款号
     * @return 返回
     */
    List<PreSaleProdCoverPicsResp> searchPreSaleCoverPics(List<String> requestData);
}
