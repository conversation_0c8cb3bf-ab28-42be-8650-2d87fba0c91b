package org.springcenter.product.modules.mapper.bojun;

import org.springcenter.product.modules.model.bojun.CStore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 10:49
 */

public interface CStoreMapper {
    List<Long> getAgentStoreByStoreId(@Param("storeId") Long storeId);

    List<CStore> getByCUnionstoreIdList(@Param("unionStoreList") List<Long> unionStoreIdList);

    CStore selectById(@Param("storeId") long storeId);

    List<CStore> selectByCCustomerId(@Param("cCustomerId") Long storeIdL);
}
