package org.springcenter.product.modules.repository;

import org.springcenter.product.api.dto.QueryToppingTagListReq;
import org.springcenter.product.modules.model.ProductToppingTag;
import org.springcenter.product.modules.model.ProductToppingTagLog;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/15 10:24
 */
public interface IProductToppingTagRepository {
    /**
     * 根据spuList查询是否有spu
     * @param spuList 款号
     * @return 返回
     */
    List<String> selectBySpuList(List<String> spuList);

    /**
     * 批量保存置顶标签
     * @param productToppingTags 置顶标签
     */
    void saveProductToppingTag(List<ProductToppingTag> productToppingTags);

    /**
     * 批量保存置顶标签日志
     * @param productToppingTagLogs 置顶标签日志
     */
    void saveProductToppingTagLog(List<ProductToppingTagLog> productToppingTagLogs);

    /**
     * 查询数据
     * @param spu 根据spu查询数据
     * @return 返回数据
     */
    ProductToppingTag selectBySpu(String spu);

    /**
     * 更新置顶标签
     * @param key 标签
     */
    void updateProductToppingTag(ProductToppingTag key);

    /**
     * 根据id查询置顶标签
     * @param id 唯一标识
     * @return 返回
     */
    ProductToppingTag searchToppingTag(String id);

    /**
     * 根据参数查询列表
     * @param requestData 入参
     * @return 返回
     */
    List<ProductToppingTag> queryToppingTagList(QueryToppingTagListReq requestData);

    /**
     * 查询没有开始的置顶标签 时间是小于当前时间
     * @return 返回
     */
    List<ProductToppingTag> selectNotStartToppingTag();

    /**
     * 批量更新置顶标签
     * @param productToppingTags 置顶标签
     */
    void batchUpdateToppingTag(List<ProductToppingTag> productToppingTags);

    /**
     * 结束时间是小于当前时间，状态是进行中状态的标签,不流转
     */
    List<ProductToppingTag> selectFinishedToppingTagAndNotFlow();

    /**
     * 结束时间是小于当前时间，状态是进行中状态的标签,流转
     */
    List<ProductToppingTag> selectFinishedToppingTagAndFlow();
}
