<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.ProductToppingTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.ProductToppingTag">
        <id column="ID" property="id" />
        <result column="TYPE" property="type" />
        <result column="CHANNEL" property="channel" />
        <result column="SPU" property="spu" />
        <result column="SOURCE" property="source" />
        <result column="START_TIME" property="startTime" />
        <result column="END_TIME" property="endTime" />
        <result column="DURING_DAY" property="duringDay" />
        <result column="IS_FLOW" property="isFlow" />
        <result column="STATUS" property="status" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATOR" property="creator"/>
        <result column="UPDATER" property="updater"/>
        <result column="IS_DELETED" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, TYPE, CHANNEL, START_TIME, END_TIME, DURING_DAY, IS_FLOW, STATUS, CREATE_TIME, UPDATE_TIME, CREATOR, UPDATER,
          IS_DELETED, SPU, SOURCE
    </sql>
    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO PRODUCT_TOPPING_TAG
            (ID, TYPE, CHANNEL, START_TIME, END_TIME, DURING_DAY, IS_FLOW, STATUS, CREATE_TIME, UPDATE_TIME, CREATOR, UPDATER,
            SPU, SOURCE) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.type,jdbcType=DECIMAL}, #{item.channel,jdbcType=VARCHAR},
            #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP}, #{item.duringDay,jdbcType=DECIMAL},
            #{item.isFlow,jdbcType=DECIMAL}, #{item.status,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.updater,jdbcType=VARCHAR},
            #{item.spu,jdbcType=VARCHAR}, #{item.source,jdbcType=DECIMAL})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <select id="selectByParam" resultMap="BaseResultMap">
       select <include refid="Base_Column_List"/>
           from PRODUCT_TOPPING_TAG
        where IS_DELETED = 0
        <if test="spu != null and spu !=''">
            and SPU = #{spu}
        </if>
        <if test="type != null">
            and TYPE = #{type}
        </if>
        <if test="status != null ">
            and STATUS = #{status}
          <if test="status == 0">
            and END_TIME  <![CDATA[ >= ]]> #{nowDay}
          </if>
          <if test="status == 1">
            and START_TIME <![CDATA[ <= ]]> #{nowDay}
            and END_TIME  <![CDATA[ >= ]]> #{nowDay}
          </if>
          <if test="status == 2">
            and END_TIME  <![CDATA[ < ]]> #{nowDay}
          </if>
        </if>
        order by CREATE_TIME desc
    </select>
    <select id="selectListByNotStart" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from PRODUCT_TOPPING_TAG
        where IS_DELETED = 0 and STATUS = 0
        and START_TIME <![CDATA[ <= ]]> #{nowDay}
        and END_TIME  <![CDATA[ >= ]]> #{nowDay}
    </select
    >
    <select id="selectFinishedToppingTagAndNotFlow" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from PRODUCT_TOPPING_TAG
        where IS_DELETED = 0 and STATUS = 1 and IS_FLOW = 0
        and END_TIME  <![CDATA[ <= ]]> #{nowDay}
    </select>

    <select id="selectFinishedToppingTagAndFlow" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from PRODUCT_TOPPING_TAG
        where IS_DELETED = 0 and STATUS = 1 and IS_FLOW = 1
        and END_TIME  <![CDATA[ <= ]]> #{nowDay}
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update PRODUCT_TOPPING_TAG
            set
            STATUS = #{item.status},
            START_TIME = #{item.startTime},
            END_TIME = #{item.endTime},
            IS_FLOW = #{item.isFlow},
            TYPE = #{item.type},
            SOURCE = #{item.source}
            where ID = #{item.id}
        </foreach>
    </update>

</mapper>