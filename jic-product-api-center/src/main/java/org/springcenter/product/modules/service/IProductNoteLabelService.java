package org.springcenter.product.modules.service;

import org.springcenter.product.api.dto.LabelNoteReq;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/3/25 9:49
 */
public interface IProductNoteLabelService {

    /**
     * 更新标签注释
     * @param requestData 入参
     */
    void updateLabelNote(LabelNoteReq requestData);

    /**
     * 增加标签注释
     * @param requestData 入参
     */
    void addLabelNote(LabelNoteReq requestData);

    /**
     * 根据labelCode查询当前的注释
     * @param requestData 标签code
     * @return 返回
     */
    List<LabelNoteReq> searchLabelNote(List<String> requestData);
}
