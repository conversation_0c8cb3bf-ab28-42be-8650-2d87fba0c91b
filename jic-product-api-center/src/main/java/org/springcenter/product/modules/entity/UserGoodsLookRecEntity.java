package org.springcenter.product.modules.entity;

import com.jnby.common.RemotingSerializable;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户look推荐实体
 * <AUTHOR>
 * @Date 2021/7/2 3:54 下午
 * @Version 1.0
 */
@Data
public class UserGoodsLookRecEntity extends RemotingSerializable implements Serializable {
    private String unionid;
    private String look_id;
    private long m_product_id;
    private long m_productalias_id;
    private float score;
    private float total_score;
}
