<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.MProductVersionSalesMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.MProductVersionSales">
            <result property="pic" column="PIC" jdbcType="VARCHAR"/>
            <result property="categoryName" column="CATEGORY_NAME" jdbcType="VARCHAR"/>
            <result property="spu" column="SPU" jdbcType="VARCHAR"/>
            <result property="price" column="PRICE" jdbcType="DECIMAL"/>
            <result property="salesNumber" column="SALES_NUMBER" jdbcType="DECIMAL"/>
            <result property="salesPrice" column="SALES_PRICE" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="modelNumber" column="MODEL_NUMBER" jdbcType="VARCHAR"/>
            <result property="productId" column="PRODUCT_ID" jdbcType="VARCHAR"/>
            <result property="sampleCode" column="SAMPLE_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        PIC,CATEGORY_NAME,
        SPU,PRICE,SALES_NUMBER,
        SALES_PRICE,CREATE_TIME,MODEL_NUMBER,PRODUCT_ID,SAMPLE_CODE
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap">
        select a.SPU,
        max(a.PIC) as pic,
        max(a.CATEGORY_NAME) as CATEGORY_NAME,
        max(a.PRICE) as price,
        max(a.PRODUCT_ID) as PRODUCT_ID,
        b.MODEL_NUMBER,
        sum(a.SALES_PRICE)        as SALES_PRICE,
        sum(a.SALES_NUMBER)  as SALES_NUMBER
        from M_PRODUCT_VERSION_SALES a
        inner join M_PRODUCT_VERSION b
        on a.MODEL_NUMBER = b.MODEL_NUMBER
        or a.MODEL_NUMBER = b.SAMPLE_NO
        or a.SAMPLE_CODE = b.SAMPLE_NO
        <where>
            <if test="modelNumber != null">
                and a.MODEL_NUMBER in  (#{modelNumber}, #{sampleCode})
            </if>
            <if test="sampleCode != null">
                or a.SAMPLE_CODE  = #{sampleCode, jdbcType=VARCHAR}
            </if>
            <if test="sort != null">
                <if test="sort == 0">
                    and a.CREATE_TIME > add_months(sysdate, -1)
                </if>
                <if test="sort == 1">
                    and a.CREATE_TIME > add_months(sysdate, -3)
                </if>
                <if test="sort == 2">
                    and a.CREATE_TIME > add_months(sysdate, -6)
                </if>
                <if test="sort == 3">
                    and a.CREATE_TIME > add_months(sysdate, -12)
                </if>
            </if>
        </where>
       group by a.SPU, b.MODEL_NUMBER
    </select>

    <select id="selectSumByParam" resultMap="BaseResultMap">
        select sum(a.SALES_NUMBER) as SALES_NUMBER, sum(a.SALES_PRICE) as SALES_PRICE
        from M_PRODUCT_VERSION_SALES a
        inner join M_PRODUCT_VERSION b
        on a.MODEL_NUMBER = b.MODEL_NUMBER
        or a.MODEL_NUMBER = b.SAMPLE_NO
        or a.SAMPLE_CODE = b.SAMPLE_NO
        <where>
            <if test="modelNumber != null">
                and a.MODEL_NUMBER in  (#{modelNumber}, #{sampleCode})
            </if>
            <if test="sampleCode != null">
                or a.SAMPLE_CODE  = #{sampleCode, jdbcType=VARCHAR}
            </if>
            <if test="sort != null">
                <if test="sort == 0">
                    and a.CREATE_TIME > add_months(sysdate, -1)
                </if>
                <if test="sort == 1">
                    and a.CREATE_TIME > add_months(sysdate, -3)
                </if>
                <if test="sort == 2">
                    and a.CREATE_TIME > add_months(sysdate, -6)
                </if>
                <if test="sort == 3">
                    and a.CREATE_TIME > add_months(sysdate, -12)
                </if>
            </if>
        </where>

    </select>

    <select id="selectSumByParamAndSampleCode" resultMap="BaseResultMap">
        select
            a.SPU as spu,
            a.MODEL_NUMBER as MODEL_NUMBER,
            sum(a.SALES_NUMBER)  as SALES_NUMBER
        from M_PRODUCT_VERSION_SALES a
        where
        <if test="modelNumber != null">
             a.MODEL_NUMBER in  (#{modelNumber}, #{sampleNo})
        </if>
        <if test="sampleNo != null">
            or a.SAMPLE_CODE  = #{sampleNo, jdbcType=VARCHAR}
        </if>
          and a.CREATE_TIME > add_months(sysdate, -12)
        group by a.SPU, a.MODEL_NUMBER
    </select>
</mapper>
