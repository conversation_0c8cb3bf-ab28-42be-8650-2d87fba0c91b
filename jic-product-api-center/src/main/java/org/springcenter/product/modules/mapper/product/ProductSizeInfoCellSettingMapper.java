package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.ProductSizeInfoCellSetting;

import java.util.List;


public interface ProductSizeInfoCellSettingMapper extends BaseMapper<ProductSizeInfoCellSetting> {


    List<ProductSizeInfoCellSetting> selectByBandAndSmallClassIds(@Param("brandSettingId") Integer brandSettingId,
                                                                  @Param("smallClassSettingIds") List<Integer> smallClassSettingId);
}
