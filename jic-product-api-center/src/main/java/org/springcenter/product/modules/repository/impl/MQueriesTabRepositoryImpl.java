package org.springcenter.product.modules.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springcenter.product.modules.mapper.product.MQueriesTabMapper;
import org.springcenter.product.modules.model.MQueriesTab;
import org.springcenter.product.modules.repository.IMQueriesTabRepository;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.api.enums.MQueriesTabEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/2 13:15
 */
@Service
public class MQueriesTabRepositoryImpl implements IMQueriesTabRepository {

    @Autowired
    private MQueriesTabMapper mQueriesTabMapper;

    @Override
    public List<MQueriesTab> getBrandList(MQueriesTabEnum brand) {
        QueryWrapper<MQueriesTab> query = new QueryWrapper<>();
        query.eq("NODE", brand.getNode());
        query.eq("SUB_NODE", brand.getSubNode());
        query.eq("TYPE", brand.getCode());
        query.eq("IS_DELETE", IsDeleteEnum.NORMAL.getCode());
        return mQueriesTabMapper.selectList(query);
    }

    @Override
    public List<MQueriesTab> getCateGoryList(MQueriesTabEnum category) {
        QueryWrapper<MQueriesTab> categoryQuery = new QueryWrapper<>();
        categoryQuery.eq("NODE", category.getNode());
        categoryQuery.eq("SUB_NODE", category.getSubNode());
        categoryQuery.eq("TYPE", category.getCode());
        categoryQuery.eq("IS_DELETE", IsDeleteEnum.NORMAL.getCode());
        return mQueriesTabMapper.selectList(categoryQuery);
    }

    @Override
    public List<MQueriesTab> getAttributeList(MQueriesTabEnum attribute) {
        QueryWrapper<MQueriesTab> attributeQuery = new QueryWrapper<>();
        attributeQuery.eq("NODE", attribute.getNode());
        attributeQuery.eq("SUB_NODE", attribute.getSubNode());
        attributeQuery.eq("TYPE", attribute.getCode());
        attributeQuery.eq("IS_DELETE", IsDeleteEnum.NORMAL.getCode());
        return mQueriesTabMapper.selectList(attributeQuery);
    }

    @Override
    public List<MQueriesTab> getSubAttributeList(MQueriesTabEnum attributeSub) {
        QueryWrapper<MQueriesTab> attributeQuery = new QueryWrapper<>();
        attributeQuery.eq("NODE", attributeSub.getNode());
        attributeQuery.eq("SUB_NODE", attributeSub.getSubNode());
        attributeQuery.eq("TYPE", attributeSub.getCode());
        attributeQuery.eq("IS_DELETE", IsDeleteEnum.NORMAL.getCode());
        return mQueriesTabMapper.selectList(attributeQuery);
    }

    @Override
    public List<MQueriesTab> getProductVersionListFilter(MQueriesTabEnum listFilter) {
        QueryWrapper<MQueriesTab> attributeQuery = new QueryWrapper<>();
        attributeQuery.eq("NODE", listFilter.getNode());
        attributeQuery.eq("SUB_NODE", listFilter.getSubNode());
        attributeQuery.eq("TYPE", listFilter.getCode());
        attributeQuery.eq("IS_DELETE", IsDeleteEnum.NORMAL.getCode());
        return mQueriesTabMapper.selectList(attributeQuery);
    }

    @Override
    public List<MQueriesTab> getSalesFilterList(MQueriesTabEnum salesListFilter) {
        QueryWrapper<MQueriesTab> attributeQuery = new QueryWrapper<>();
        attributeQuery.eq("NODE", salesListFilter.getNode());
        attributeQuery.eq("SUB_NODE", salesListFilter.getSubNode());
        attributeQuery.eq("TYPE", salesListFilter.getCode());
        attributeQuery.eq("IS_DELETE", IsDeleteEnum.NORMAL.getCode());
        return mQueriesTabMapper.selectList(attributeQuery);
    }

    @Override
    public List<MQueriesTab> getFirstCateGoryList(MQueriesTabEnum category) {
        QueryWrapper<MQueriesTab> attributeQuery = new QueryWrapper<>();
        attributeQuery.eq("NODE", category.getNode());
        attributeQuery.eq("SUB_NODE", category.getSubNode());
        attributeQuery.eq("TYPE", category.getCode());
        attributeQuery.isNull("PID");
        attributeQuery.eq("IS_DELETE", IsDeleteEnum.NORMAL.getCode());
        return mQueriesTabMapper.selectList(attributeQuery);
    }

    @Override
    public List<MQueriesTab> getCateGoryListByPid(MQueriesTabEnum category, Integer categoryId) {
        QueryWrapper<MQueriesTab> attributeQuery = new QueryWrapper<>();
        attributeQuery.eq("NODE", category.getNode());
        attributeQuery.eq("SUB_NODE", category.getSubNode());
        attributeQuery.eq("TYPE", category.getCode());
        attributeQuery.eq("PID", categoryId);
        attributeQuery.eq("IS_DELETE", IsDeleteEnum.NORMAL.getCode());
        return mQueriesTabMapper.selectList(attributeQuery);
    }

    @Override
    public List<MQueriesTab> getAttributeListByPidIncludeDeleted(MQueriesTabEnum category, Long attributeId) {
        QueryWrapper<MQueriesTab> attributeQuery = new QueryWrapper<>();
        attributeQuery.eq("NODE", category.getNode());
        attributeQuery.eq("SUB_NODE", category.getSubNode());
        attributeQuery.eq("TYPE", category.getCode());
        attributeQuery.eq("PID", attributeId);
        return mQueriesTabMapper.selectList(attributeQuery);
    }

    @Override
    public List<MQueriesTab> getSubAttrListByPidIncludeDeleted(MQueriesTabEnum attributeSub, Long attributeId) {
        QueryWrapper<MQueriesTab> attributeQuery = new QueryWrapper<>();
        attributeQuery.eq("NODE", attributeSub.getNode());
        attributeQuery.eq("SUB_NODE", attributeSub.getSubNode());
        attributeQuery.eq("TYPE", attributeSub.getCode());
        attributeQuery.eq("PID", attributeId);
        return mQueriesTabMapper.selectList(attributeQuery);
    }

    @Override
    public void batchSaveAttrValue(List<MQueriesTab> saveAttrValueList) {
        mQueriesTabMapper.batchInsert(saveAttrValueList);
    }

    @Override
    public void batchUpdateAttrValue(List<MQueriesTab> updateAttrValueList) {
        mQueriesTabMapper.batchUpdate(updateAttrValueList);
    }

    @Override
    public MQueriesTab getAttrById(Long attributeId) {
        return mQueriesTabMapper.selectById(attributeId);
    }
}
