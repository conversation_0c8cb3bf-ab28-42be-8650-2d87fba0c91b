package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.MQueriesAttrConnection;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【M_QUERIES_TAB(查询标签)】的数据库操作Mapper
* @createDate 2022-11-30 17:17:15
* @Entity generator.domain.MQueriesTab
*/

public interface MQueriesAttrConnectionMapper extends BaseMapper<MQueriesAttrConnection> {


    List<MQueriesAttrConnection> selectStatsListByParams(@Param("bigCategoryId") Integer bigCategoryId,
                                                     @Param("smallCategoryId") Integer smallCategoryId,
                                                         @Param("isDisabled") Integer isDisabled);

    List<MQueriesAttrConnection> selectStatsListByAttributeList(@Param("attributeIds") List<Long> attributeIds,
                                                                @Param("attributeCodes") List<String> attributeCodes);

    void batchInsert(@Param("list") List<MQueriesAttrConnection> saveConnections);

    void batchUpdate(@Param("list") List<MQueriesAttrConnection> updateConnections);

    void batchUpdateBySceAttrIds(@Param("list") List<Long> deletedSecAttr);

    void updateByAttrIds(@Param("id") Long attrId);

    List<MQueriesAttrConnection> selectBySmallCateCode(@Param("smallCategoryCode") String categoryCode);

    List<String> selectByAttrId(@Param("attrId") Long attrId);

    void updateValidByIds(@Param("list") List<String> ids);

    List<MQueriesAttrConnection> selectConnectByAttrIds(@Param("attrId") Long attributeId);


    List<MQueriesAttrConnection> selectBySmallCategory(@Param("id") Long smallAttrId);
}
