package org.springcenter.product.modules.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.modules.mapper.product.JicMProductAttrMapper;
import org.springcenter.product.modules.mapper.product.JicProductMallRelationMapper;
import org.springcenter.product.modules.mapper.product.MProductTryBeforeBuyMapper;
import org.springcenter.product.modules.model.MProductTryBeforeBuy;
import org.springcenter.product.modules.model.ProductByWeiMenInfoDto;
import org.springcenter.product.modules.repository.IJicMProductRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR>
 * @Date:2023/5/17 15:51
 */
@Repository
public class IJicMProductRepositoryImpl implements IJicMProductRepository {

    @Autowired
    private JicMProductAttrMapper jicMProductAttrMapper;

    @Autowired
    private MProductTryBeforeBuyMapper mProductTryBeforeBuyMapper;

    @Autowired
    private JicProductMallRelationMapper jicProductMallRelationMapper;

    @Autowired
    private RedisPoolUtil redisPoolUtil;
    private final static String WEI_MO_GOOD_IDS_KEY = "PRODUCT_TRY_BEFORE_BUY:WEI_MO_GOOD_ID";


    @Override
    public List<ProductSkuIdByWeiMenInfoResp> selectSkuIdByWeiMenInfo(List<ProductByWeiMenInfoReq> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            return Collections.emptyList();
        }
        //查询单条数据 主要是测试环境数据存在多条
        List<ProductByWeiMenInfoDto> retQuestData = jicProductMallRelationMapper.selectByWeimoInfo(requestData);
        return jicMProductAttrMapper.selectSkuIdByWeiMenInfo(retQuestData);
    }

    @Override
    public List<ProductByWeiMenInfoResp> searchProductByWeiMenInfo(List<ProductByWeiMenInfoReq> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            return Collections.emptyList();
        }
        //查询单条数据 主要是测试环境数据存在多条
        List<ProductByWeiMenInfoDto> retQuestData = jicProductMallRelationMapper.selectByWeimoInfo(requestData);
        if (CollectionUtils.isEmpty(retQuestData)) {
            return Collections.emptyList();
        }
        return jicMProductAttrMapper.searchProductByWeiMenInfo(retQuestData);
    }

    @Override
    public List<ProductIsTryBeforeBuyResp> searchProductIsTryBeforeBuy(ProductIsTryBeforeBuyReq requestData) {
        // 查询redis
        Map<String, String> map = RedisTemplateUtil.hGetAll(redisPoolUtil, WEI_MO_GOOD_IDS_KEY);
        List<ProductIsTryBeforeBuyResp> ret = new ArrayList<>();
        requestData.getWeiMenGoodIds().forEach(v -> {
            String s = map.get(v);
            if (StringUtils.isNotBlank(s)) {
                ret.add(JSONObject.parseObject(s, ProductIsTryBeforeBuyResp.class));
            } else {
                ProductIsTryBeforeBuyResp resp = new ProductIsTryBeforeBuyResp();
                resp.setWeiMoGoodId(v);
                resp.setType(1);
                ret.add(resp);
            }
        });
        return ret;
    }

    @Override
    public String refreshTryBeforeBuy() {
        List<MProductTryBeforeBuy> mProductTryBeforeBuyDatas = mProductTryBeforeBuyMapper.selectAllNormalList();
        if (CollectionUtils.isEmpty(mProductTryBeforeBuyDatas)) {
            return "success";
        }
        HashMap<String, Object> map = mProductTryBeforeBuyDatas.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getWeiMoGoodId(), v), HashMap::putAll);
        RedisTemplateUtil.hmSet(redisPoolUtil, WEI_MO_GOOD_IDS_KEY, map);
        return "success";
    }

    @Override
    public List<ProductByWeiMenInfoResp> searchProductByWeiMenInfoNoType(List<ProductByWeiMenInfoReq> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            return Collections.emptyList();
        }
        //查询单条数据 主要是测试环境数据存在多条
        List<ProductByWeiMenInfoDto> retQuestData = jicProductMallRelationMapper.selectByWeimoInfo(requestData);
        if (CollectionUtils.isEmpty(retQuestData)) {
            return Collections.emptyList();
        }
        return jicMProductAttrMapper.searchProductByWeiMenInfoNoType(retQuestData);
    }
}
