package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.JicProductMallInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/17 15:53
 */
@Mapper
public interface JicProductMallInfoMapper extends BaseMapper<JicProductMallInfo> {

    List<JicProductMallInfo> selectByNameAndWeId(@Param("name") String name, @Param("weid") Long weid);


    List<JicProductMallInfo> selectByProductIdAndWeId(@Param("list") List<Long> productIds, @Param("weid") Long weid);
}
