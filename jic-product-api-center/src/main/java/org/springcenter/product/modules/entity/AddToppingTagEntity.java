package org.springcenter.product.modules.entity;

import com.jnby.authority.api.ISysBaseAPI;
import lombok.Data;
import org.springcenter.product.api.dto.AddToppingTagReq;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/15 13:38
 */
@Data
public class AddToppingTagEntity {
    private ISysBaseAPI sysBaseAPI;

    private String tag;

    private List<String> spuList;

    private AddToppingTagReq req;

    private Boolean isToday;

    public static AddToppingTagEntity build(List<String> spus, AddToppingTagReq req, String productToppingLeafTag,
                                            ISysBaseAPI sysBaseAPI, Boolean isToday) {
        AddToppingTagEntity entity = new AddToppingTagEntity();
        entity.setTag(productToppingLeafTag);
        entity.setReq(req);
        entity.setIsToday(isToday);
        entity.setSpuList(spus);
        entity.setSysBaseAPI(sysBaseAPI);
        return entity;
    }
}
