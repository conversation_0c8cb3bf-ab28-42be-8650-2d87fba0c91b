<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.ProductSceneLabelLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.ProductSceneLabelLog">
        <id column="ID" property="id" />
        <id column="SCENE_LABEL_ID" property="sceneLabelId" />
        <result column="SCENE_NAME" property="sceneName" />
        <result column="OWN_CHANNEL_ID" property="ownChannelId" />
        <result column="REMARK" property="remark" />
        <result column="SCENE_KEY" property="sceneKey" />
        <result column="CREATOR" property="creator" />
        <result column="UPDATER" property="updater" />
        <result column="STATUS" property="status" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SCENE_NAME, OWN_CHANNEL_ID, REMARK, SCENE_KEY, CREATOR, UPDATER, STATUS, CREATE_TIME, UPDATE_TIME, IS_DELETED, SCENE_LABEL_ID
    </sql>


</mapper>
