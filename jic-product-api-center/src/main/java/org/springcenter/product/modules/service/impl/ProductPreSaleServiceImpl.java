package org.springcenter.product.modules.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.QiniuUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.constant.ImageConstant;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.modules.mapper.product.PreSaleProductModelPicMapper;
import org.springcenter.product.modules.model.FabInfo;
import org.springcenter.product.modules.model.PreSaleProductModelPic;
import org.springcenter.product.modules.service.FabInfoService;
import org.springcenter.product.modules.service.IProductPreSaleService;
import org.springcenter.product.modules.service.IProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;

/**
 * <AUTHOR>
 * @Date:2023/8/17 10:59
 */
@Service
@Slf4j
@RefreshScope
public class ProductPreSaleServiceImpl implements IProductPreSaleService {

    @Autowired
    private IProductService productService;

    @Autowired
    private PreSaleProductModelPicMapper preSaleProductModelPicMapper;

    @Autowired
    private QiniuUtil qiniuUtil;

    @Value(value = "${pre.sale.product.url.tag}")
    private String preSaleProductUrlTag;

    @Value("${import.prefix.url}")
    private String prefixUrl;

    @Value("${prefix.single.spu.url}")
    private String prefixSingleSpuUrl;

    @Value("${prefix.single.skc.url}")
    private String prefixSingleSkcUrl;

    @Autowired
    private FabInfoService fabInfoService;

    @Override
    public PreSaleProductDetailResp searchPreSaleProductDetail(PreSaleProductDetailReq requestData) {
        // 1、查询商品信息
        GoodSpuDetailEntity entity = productService.searchProductDetail(requestData.getProductId());
        if (entity == null) {
            return null;
        }
        FabInfo fabInfo = fabInfoService.selectFabInfoByProductIdAndName(Objects.toString(entity.getId()), entity.getName());
        if (fabInfo != null && StringUtils.isNotBlank(fabInfo.getFab())) {
            entity.setFab(fabInfo.getFab());
        }

        // 2、查询商品相关模特图信息
        List<String> modelPics = new ArrayList<>();
        if (StringUtils.isNotBlank(entity.getName())) {
            modelPics = preSaleProductModelPicMapper.selectModelUrlByModelNo(Lists.newArrayList(entity.getName()));
        }


        return PreSaleProductDetailResp.build(entity, modelPics, prefixSingleSkcUrl);
    }

    @Override
    public Boolean importModelPics(String path) {
        // 读取文件夹里面的文件
        File fileFolder = new File(path);
        List<PreSaleProductModelPic> pics = new ArrayList<>();
        if (fileFolder.isDirectory()) {
            // 款号层文件夹
            File[] secFileList = fileFolder.listFiles();

            // 文件夹路径
            for (int i = 0; i < secFileList.length; i++) {

                if (secFileList[i].isDirectory()) {
                    String fileName = secFileList[i].getName();
                    File[] picDirList = secFileList[i].listFiles();
                    for (int k = 0; k < picDirList.length; k++) {
                        // 读取文件夹
                        // 上传七牛
                        // 上传七牛的路径
                        String wholeName = fileName;
                        wholeName = wholeName + ImageConstant.ONE_HE + picDirList[k].getName();
                        File[] files = picDirList[k].listFiles();
                        if (files == null) {
                            continue;
                        }
                        for (int j = 0; j < files.length; j++) {
                            String modelUrl = qiniuUtil.upload(files[j].getPath(), ImageConstant.PRE_SALE_FOLDER + wholeName + ImageConstant.ONE_HE + files[j].getName());
                            // 图片名称
                            String nameUrl = wholeName + ImageConstant.ONE_HE + StringUtils.split(files[j].getName(), ImageConstant.POINT)[0];
                            // 图片后缀
                            String nameUrlSuffix = StringUtils.split(files[j].getName(), ImageConstant.POINT)[1];
                            // 去除域名后在七牛图片的名称
                            String notHaveUrl = modelUrl.split(prefixUrl)[1];

                            // 上传七牛压缩图
                            String largeSubUrl = ImageConstant.PRE_SALE_FOLDER + nameUrl + ImageConstant.COMPRESS_LARGE + nameUrlSuffix;
                            qiniuUtil.transCoding(notHaveUrl, largeSubUrl, ImageConstant.PIC_PARAM);
                            String compressUrl = prefixUrl + largeSubUrl;

                            // 组装
                            PreSaleProductModelPic modelPic = new PreSaleProductModelPic();
                            modelPic.setId(IdLeaf.getDateId(preSaleProductUrlTag));
                            modelPic.setSpu(fileName);
                            modelPic.setModelUrl(modelUrl);
                            modelPic.setAbbreviateModelUrl(compressUrl);
                            modelPic.setCreateTime(new Date());
                            modelPic.setUpdateTime(new Date());
                            modelPic.setFolderName(wholeName);
                            pics.add(modelPic);
                        }
                    }
            }
          }
        }

        // 保存数据库
        if (CollectionUtils.isNotEmpty(pics)) {
            preSaleProductModelPicMapper.batchInsert(pics);
        }
        return true;
    }

    @Override
    public List<PreSaleProdCoverPicsResp> searchPreSaleCoverPics(List<String> requestData) {
        QueryGoodsReq queryGoodsReq = new QueryGoodsReq();
        queryGoodsReq.setNames(requestData);
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(100);
        List<ProductSkcResp> goodSpuResps = productService.searchGoodsSkc(queryGoodsReq, page, "");
        HashMap<String, ProductSkcResp> map = goodSpuResps.stream().collect(HashMap::new, (k, v) -> k.put(v.getName(), v), HashMap::putAll);
        List<PreSaleProdCoverPicsResp> rets = new ArrayList<>();
        if (CollectionUtils.isEmpty(goodSpuResps)) {
            requestData.forEach(v -> {
                PreSaleProdCoverPicsResp picsResp = new PreSaleProdCoverPicsResp();
                picsResp.setName(v);
                picsResp.setPics(prefixSingleSpuUrl + v + ImageConstant.JPG);
                picsResp.setLookPic(prefixSingleSpuUrl + v + ImageConstant.JPG);
                picsResp.setSampleCode("");
                rets.add(picsResp);
            });
        } else {
            requestData.forEach(v -> {
                PreSaleProdCoverPicsResp picsResp = new PreSaleProdCoverPicsResp();
                picsResp.setName(v);
                ProductSkcResp productSkcResp = map.get(v);
                picsResp.setPics(productSkcResp == null ? prefixSingleSpuUrl + v + ImageConstant.JPG : (prefixSingleSkcUrl + v + productSkcResp.getColorno() + ImageConstant.JPG));
                picsResp.setLookPic(prefixSingleSpuUrl + v + ImageConstant.JPG);
                picsResp.setSampleCode(productSkcResp == null ? "" : productSkcResp.getSample_code());
                rets.add(picsResp);
            });
        }
        return rets;
    }
}
