package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.LocalDate;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.ToppingTagFlowEnum;
import org.springcenter.product.api.enums.ToppingTagSourceEnum;
import org.springcenter.product.api.enums.ToppingTagStatusEnum;
import org.springcenter.product.api.enums.ToppingTagTypeEnum;
import org.springcenter.product.modules.convert.ProductToppingTagConvert;
import org.springcenter.product.modules.entity.AddToppingTagEntity;
import org.springcenter.product.modules.model.ProductToppingTag;
import org.springcenter.product.modules.model.ProductToppingTagLog;
import org.springcenter.product.modules.model.ProductVersionEntity;
import org.springcenter.product.modules.repository.IProductToppingTagRepository;
import org.springcenter.product.modules.service.IProductToppingEsScriptService;
import org.springcenter.product.modules.service.IProductToppingTagService;
import org.springcenter.product.util.DateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/5/15 10:07
 */
@Service
@Slf4j
public class ProductToppingTagServiceImpl implements IProductToppingTagService {

    @Autowired
    private IProductToppingTagRepository productToppingTagRepository;

    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Value("${product.topping.leaf.tag}")
    private String productToppingLeafTag;



    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Autowired
    private IProductToppingEsScriptService productToppingEsScriptService;


    @Override
    public List<String> addToppingTag(AddToppingTagReq req) {
        // 1、判断时间是否符合要求，返回开始时间是否是今日
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("1、判断时间是否符合要求，返回开始时间是否是今日");
        Boolean isToday = judgeAddParam(req);
        stopWatch.stop();


        // 2、判断款号是否已存在表中
        stopWatch.start("2、判断款号是否已存在表中");
        List<String> spus = new ArrayList<>();
        try {
            String str = req.getSpu().replace("，", ",");
            spus = Arrays.stream(str.split(",")).map(v -> StringUtils.trim(v)).collect(Collectors.toList());
            Boolean isHasRepeat = judgeIsHasRepeat(spus);
            if (isHasRepeat) {
                throw new RuntimeException("传入的款号中有重复款号，请核对！");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (spus.size() > 50) {
            throw new RuntimeException("传入的款号不能多于50个，请重新传入！");
        }
        List<String> repeatList = judgeSpuInTags(spus);
        if (CollectionUtils.isNotEmpty(repeatList)) {
            return repeatList;
        }
        stopWatch.stop();

        // 3、组装实体
        stopWatch.start("3、整个组装实体");
        AddToppingTagEntity entity = AddToppingTagEntity.build(spus, req, productToppingLeafTag, sysBaseAPI, isToday);
        Pair<List<ProductToppingTag>, List<ProductToppingTagLog>> pair = ProductToppingTag.buildAddEntity(entity);
        stopWatch.stop();

        stopWatch.start("4、保存数据");
        template.execute(action -> {
            // 保存实体
            productToppingTagRepository.saveProductToppingTag(pair.getKey());

            // 保存日志
            productToppingTagRepository.saveProductToppingTagLog(pair.getValue());

            // 如果是今天更改索引
            if (isToday) {
                try {
                    productToppingEsScriptService.batchUpdateIndexInfoByAdd(ProductToppingTag.buildAddEntity(entity).getKey());
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            return true;
        });
        stopWatch.stop();
        log.info("==============" + stopWatch.prettyPrint());
        return Collections.emptyList();
    }

    // 判断是否有重复数据
    private Boolean judgeIsHasRepeat(List<String> spus) {
        Set<Object> tempSet = new HashSet<Object>();
        spus.forEach(v -> {
            tempSet.add(v);
        });

        if(spus.size() == tempSet.size()){
            return false;
        }else{
            return true;
        }
    }

    @Override
    public Boolean updateToppingTag(AddToppingTagReq req) {
        // 1、判断时间是否符合要求，返回开始时间是否是今日
        Boolean isToday = judgeAddParam(req);

        // 2、查询当前数据
        ProductToppingTag productToppingTag = Preconditions.checkNotNull(productToppingTagRepository.selectBySpu(req.getSpu()), "当前spu数据不存在");

        // 3、组装实体
        AddToppingTagEntity entity = AddToppingTagEntity.build(new ArrayList<>(), req, productToppingLeafTag, sysBaseAPI, isToday);

        template.execute(action -> {
            Pair<ProductToppingTag, ProductToppingTagLog> tagPair = ProductToppingTag.buildUpdateEntity(entity, productToppingTag);
            ProductToppingTag toppingTag = tagPair.getKey();
            ProductToppingTagLog tagLog = tagPair.getValue();
            // 保存实体
            productToppingTagRepository.updateProductToppingTag(toppingTag);

            // 保存日志
            productToppingTagRepository.saveProductToppingTagLog(Lists.newArrayList(tagLog));

            // 如果是今天更改索引 不是去出索引
            if (isToday) {
                try {
                    productToppingEsScriptService.batchUpdateIndexInfoByAdd(Lists.newArrayList(toppingTag));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            } else {
                productToppingEsScriptService.updateIndexInfoByRemoveEle(toppingTag);
            }
            return true;
        });
        return true;
    }

    @Override
    public QueryToppingTagResp queryToppingTag(QueryToppingTagReq requestData) {
        ProductToppingTag productToppingTag =
                Preconditions.checkNotNull(productToppingTagRepository.searchToppingTag(requestData.getId()), "根据当前id查询，数据不存在");
        return ProductToppingTagConvert.buildToppingTag(productToppingTag);
    }

    @Override
    public List<QueryToppingTagListResp> queryToppingTagList(QueryToppingTagListReq requestData, Page page) {
        com.github.pagehelper.Page<ProductVersionEntity> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<ProductToppingTag> productToppingTags = productToppingTagRepository.queryToppingTagList(requestData);
        List<QueryToppingTagListResp> queryToppingTagListResps = ProductToppingTagConvert.buildToppingTagList(productToppingTags);
        PageInfo<QueryToppingTagListResp> pageInfo = new PageInfo(hPage);
        pageInfo.setList(queryToppingTagListResps);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        page.setPageNo(page.getPageNo());
        page.setPageSize(page.getPageSize());
        return pageInfo.getList();
    }

    @Override
    public Boolean switchToppingTag(SwitchToppingTagReq requestData) {
        // 1、查询该数据
        ProductToppingTag productToppingTag =
                Preconditions.checkNotNull(productToppingTagRepository.searchToppingTag(requestData.getId()), "根据当前id查询，数据不存在");

        // 2、判断操作参数
        judgeSwitchParam(requestData, productToppingTag);

        // 3、组装数据更新
        Pair<ProductToppingTag, ProductToppingTagLog> tagPair = ProductToppingTag.buildSwitchEntity(requestData, productToppingTag, sysBaseAPI,
                productToppingLeafTag);
        template.execute(action -> {
            // 保存实体
            productToppingTagRepository.updateProductToppingTag( tagPair.getKey());

            // 保存日志
            productToppingTagRepository.saveProductToppingTagLog(Lists.newArrayList(tagPair.getValue()));

            // 更改索引
            try {
                ProductToppingTag toppingTag = ProductToppingTag.buildSwitchEntity(requestData, productToppingTag, sysBaseAPI,
                        productToppingLeafTag).getKey();
                if (Objects.equals(requestData.getIsOpen(), 0)) {
                    productToppingEsScriptService.updateIndexInfoByRemoveEle(toppingTag);
                } else {
                    productToppingEsScriptService.batchUpdateIndexInfoByAdd(Lists.newArrayList(toppingTag));
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            return true;
        });
        return true;
    }



    @Override
    public void switchOpenStatus() {
        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        // 查询开始时间小于当前时间 且状态是未开始状态 获取总数
        productToppingTagRepository.selectNotStartToppingTag();
        PageInfo<ProductToppingTag> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();

        long pageTotal = 0;
        int pageSize = 100;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }

        for(int i = 1 ; i <= pageTotal; i++){
            com.github.pagehelper.Page<Object> page = PageHelper.startPage(1, 200);
            // 查询开始时间小于当前时间 且状态是未开始状态
            List<ProductToppingTag> productToppingTags = productToppingTagRepository.selectNotStartToppingTag();
            if (CollectionUtils.isNotEmpty(productToppingTags)) {
                log.info("productToppingTags：{}", JSONObject.toJSONString(productToppingTags));
                // 处理数据
                productToppingTags.forEach(v -> v.setStatus(ToppingTagStatusEnum.PROCESSING.getCode()));
                List<ProductToppingTagLog> productToppingTagLogs = productToppingTags.stream().map(v -> {
                    ProductToppingTagLog tagLog = new ProductToppingTagLog();
                    BeanUtils.copyProperties(v, tagLog);
                    tagLog.setProductToppingTagId(v.getId());
                    tagLog.setId(IdLeaf.getDateId(productToppingLeafTag));
                    tagLog.setCreateTime(new Date());
                    tagLog.setUpdateTime(new Date());
                    tagLog.setCreator("系统");
                    tagLog.setUpdater("系统");
                    return tagLog;
                }).collect(Collectors.toList());
                template.execute(action -> {
                    // 批量更新置顶标签
                    productToppingTagRepository.batchUpdateToppingTag(productToppingTags);
                    // 插入日志
                    productToppingTagRepository.saveProductToppingTagLog(productToppingTagLogs);
                    return true;
                });
            }
        }
    }

    @Override
    public void switchShutDownStatus() {
        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        // 查询结束时间大于当前时间 且状态是进行中状态 不能流转 获取总数
        productToppingTagRepository.selectFinishedToppingTagAndNotFlow();
        PageInfo<ProductToppingTag> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();

        long pageTotal = 0;
        int pageSize = 100;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }

        for(int i = 1 ; i <= pageTotal; i++){
            com.github.pagehelper.Page<Object> page = PageHelper.startPage(1, 200);
            // 查询结束时间大于当前时间 且状态是进行中状态 不能流转 获取总数
            List<ProductToppingTag> productToppingTags = productToppingTagRepository.selectFinishedToppingTagAndNotFlow();
            if (CollectionUtils.isNotEmpty(productToppingTags)) {
                log.info("productToppingTags：{}", JSONObject.toJSONString(productToppingTags));
                // 处理数据
                productToppingTags.forEach(v -> v.setStatus(ToppingTagStatusEnum.FINISHED.getCode()));
                List<ProductToppingTagLog> productToppingTagLogs = productToppingTags.stream().map(v -> {
                    ProductToppingTagLog tagLog = new ProductToppingTagLog();
                    BeanUtils.copyProperties(v, tagLog);
                    tagLog.setProductToppingTagId(v.getId());
                    tagLog.setId(IdLeaf.getDateId(productToppingLeafTag));
                    tagLog.setCreateTime(new Date());
                    tagLog.setUpdateTime(new Date());
                    tagLog.setCreator("系统");
                    tagLog.setUpdater("系统");
                    return tagLog;
                }).collect(Collectors.toList());
                template.execute(action -> {
                    // 批量更新置顶标签
                    productToppingTagRepository.batchUpdateToppingTag(productToppingTags);
                    // 插入日志
                    productToppingTagRepository.saveProductToppingTagLog(productToppingTagLogs);
                    return true;
                });
                try {
                    log.info("==============switchShutDownStatus处理es");
                    productToppingTags.forEach(v -> {
                        productToppingEsScriptService.updateIndexInfoByRemoveEle(v);
                    });
                } catch (Exception e) {
                    log.error("==================switchShutDownStatus{}", JSONObject.toJSONString(productToppingTags));
                }
            }
        }
    }

    @Override
    public void flowProductToppingTag() {
        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        // 查询结束时间大于当前时间 且状态是进行中状态 且能流转 获取总数
        productToppingTagRepository.selectFinishedToppingTagAndFlow();
        PageInfo<ProductToppingTag> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();

        long pageTotal = 0;
        int pageSize = 100;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }

        for(int i = 1 ; i <= pageTotal; i++){
            com.github.pagehelper.Page<Object> page = PageHelper.startPage(1, 200);
            // 查询结束时间大于当前时间 且状态是进行中状态 且能流转 获取总数
            List<ProductToppingTag> productToppingTags = productToppingTagRepository.selectFinishedToppingTagAndFlow();
            if (CollectionUtils.isNotEmpty(productToppingTags)) {
                log.info("productToppingTags：{}", JSONObject.toJSONString(productToppingTags));
                // 处理数据
                // 判断流转状态 判断是否结束
                String now = DateUtil.parseDate(LocalDate.now().toDate(), DateUtil.DATEFORMATE_YYYY_MM_DD);
                productToppingTags.forEach(v -> {
                    if (Objects.equals(v.getType(), ToppingTagTypeEnum.HOT.getCode())) {
                        v.setStatus(ToppingTagStatusEnum.FINISHED.getCode());
                        return;
                    }
                    // 判断状态
                    if (Objects.equals(v.getType(), ToppingTagTypeEnum.PRE_SALE.getCode())) {
                        v.setType(ToppingTagTypeEnum.NEW.getCode());
                    } else if (Objects.equals(v.getType(), ToppingTagTypeEnum.NEW.getCode())) {
                        v.setType(ToppingTagTypeEnum.HOT.getCode());
                        v.setIsFlow(ToppingTagFlowEnum.NOT_FLOW.getCode());
                    }
                    v.setStartTime(DateUtil.formatToDate( now + " 00:00:00", DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS));
                    LocalDate localDate = LocalDate.now().plusDays(v.getDuringDay() - 1);
                    String end = DateUtil.parseDate(localDate.toDate(), DateUtil.DATEFORMATE_YYYY_MM_DD);
                    v.setEndTime(DateUtil.formatToDate( end + " 23:59:59", DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS));
                    v.setSource(ToppingTagSourceEnum.FLOW.getCode());
                });
                List<ProductToppingTagLog> productToppingTagLogs = productToppingTags.stream().map(v -> {
                    ProductToppingTagLog tagLog = new ProductToppingTagLog();
                    BeanUtils.copyProperties(v, tagLog);
                    tagLog.setProductToppingTagId(v.getId());
                    tagLog.setId(IdLeaf.getDateId(productToppingLeafTag));
                    tagLog.setCreateTime(new Date());
                    tagLog.setUpdateTime(new Date());
                    tagLog.setCreator("系统");
                    tagLog.setUpdater("系统");
                    return tagLog;
                }).collect(Collectors.toList());
                template.execute(action -> {
                    // 批量更新置顶标签
                    productToppingTagRepository.batchUpdateToppingTag(productToppingTags);
                    // 插入日志
                    productToppingTagRepository.saveProductToppingTagLog(productToppingTagLogs);
                    return true;
                });
                try {
                    log.info("==============flowProductToppingTag处理es");
                    productToppingTags.forEach(v -> {
                        productToppingEsScriptService.updateIndexInfoByRemoveEle(v);
                    });
                } catch (Exception e) {
                    log.error("==================flowProductToppingTag{}", JSONObject.toJSONString(productToppingTags));
                }

            }
        }
    }

    @Override
    public List<ToppingTagTypeResp> queryToppingTagType() {
        List<ToppingTagTypeResp> rets = new ArrayList<>();

        Arrays.stream(ToppingTagTypeEnum.values()).forEach(
            v -> {
                ToppingTagTypeResp typeResp = new ToppingTagTypeResp();
                BeanUtils.copyProperties(v, typeResp);
                rets.add(typeResp);
            }
        );
        return rets;
    }


    private void judgeSwitchParam(SwitchToppingTagReq requestData, ProductToppingTag productToppingTag) {
        if (Objects.equals(productToppingTag.getStatus(), ToppingTagStatusEnum.TO_BE_STARTED.getCode())) {
            if (Objects.equals(requestData.getIsOpen(), 0)) {
                throw new RuntimeException("待开始状态的不能进行关闭");
            }

            // 判断结束时间是否小于当前开始时间
            Date nowDate = DateUtil.parseDatetoDate(new Date(), DateUtil.DATE_FORMAT_YMDHM);
            if (productToppingTag.getEndTime().before(nowDate)) {
                throw new RuntimeException("结束时间需大于当前时间，不能进行关闭");
            }
        }

        if (Objects.equals(productToppingTag.getStatus(), ToppingTagStatusEnum.PROCESSING.getCode())) {
            if (Objects.equals(requestData.getIsOpen(), 1)) {
                throw new RuntimeException("进行中的状态的不能进行开启");
            }

        }

        if (Objects.equals(productToppingTag.getStatus(), ToppingTagStatusEnum.FINISHED.getCode())) {
            if (Objects.equals(requestData.getIsOpen(), 0)) {
                throw new RuntimeException("已结束的状态的不能进行关闭");
            }

            // 判断结束时间是否小于当前开始时间
            Date nowDate = DateUtil.parseDatetoDate(new Date(), DateUtil.DATE_FORMAT_YMDHM);
            if (productToppingTag.getEndTime().before(nowDate)) {
                throw new RuntimeException("置顶时间已过期，请编辑后重新保存");
            }
        }

    }



    // 判断spu是否已在表忠
    private List<String> judgeSpuInTags(List<String> spuList) {
        return productToppingTagRepository.selectBySpuList(spuList);
    }

    // 参数校验
    private Boolean judgeAddParam(AddToppingTagReq req) {
        Date startDate = DateUtil.parseDate(req.getStartTime(), DateUtil.DATEFORMATE_YYYY_MM_DD);
        Date endDate = DateUtil.parseDate(req.getEndTime(), DateUtil.DATEFORMATE_YYYY_MM_DD);
        Date date = LocalDate.now().toDate();
        Date nowDay = DateUtil.parseDatetoDate(date, DateUtil.DATEFORMATE_YYYY_MM_DD);
        if (startDate.before(nowDay)) {
            throw new RuntimeException("开始时间需要是当前和未来时间");
        }

        if (endDate.before(nowDay)) {
            throw new RuntimeException("结束时间需要是当前和未来时间");
        }

        if (endDate.before(startDate)) {
            throw new RuntimeException("结束时间不能早于开始时间");
        }

        if (Objects.equals(req.getIsFlow(), ToppingTagFlowEnum.FLOW.getCode()) &&
                (req.getDuringTime() == null || req.getDuringTime() == 0)) {
            throw new RuntimeException("流转状态下，新标识有效期必须大于0");
        }
        if (Objects.equals(req.getIsFlow(), ToppingTagFlowEnum.NOT_FLOW.getCode())) {
            req.setDuringTime(0);
        }

        if (startDate.equals(nowDay)) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

}
