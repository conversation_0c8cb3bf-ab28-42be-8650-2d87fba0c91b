package org.springcenter.product.modules.repository.bojun;

import org.springcenter.product.modules.entity.EbAndWscSpuStockEntity;
import org.springcenter.product.modules.entity.ProductStockEntity;
import org.springcenter.product.modules.entity.SkcStockEntity;
import org.springcenter.product.modules.entity.SpuStockEntity;
import org.springcenter.product.modules.model.bojun.FaStorage;
import org.springcenter.product.api.dto.ProductAgentStockResp;
import org.springcenter.product.api.dto.SameSpuProductResp;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 13:15
 */
public interface StockRepository {
    /**
     * 根据门店获取经销商门店
     * @param storeId 门店id
     * @return 返回
     */
    List<Long> getAgentStoreByStoreId(Long storeId);

    /**
     * 根据skuID和storeId获取库存
     * @param skuIds 规格id
     * @param storeIds 门店id
     * @return 返回
     */
    List<ProductAgentStockResp> getStorages(List<Long> skuIds, List<Long> storeIds);

    /**
     * 查看同款商品
     * @param spu 款号
     * @return 返回
     */
    List<SameSpuProductResp> getSameSpuProduct(String spu);

    /**
     * 判断门店是否有该商品
     * @param productIds 商品id
     * @param storeIds 门店信息
     * @return 返回
     */
    List<SpuStockEntity> selectBySpuAndStoreIds(List<Long> productIds, List<Long> storeIds);

    /**
     * 根据款号和门店查询内淘库存
     * @param productIds 商品id
     * @param storeIds 门店
     * @return 返回信息
     */
    List<SpuStockEntity> selectEbStorageBySpuAndStoreIds(List<Long> productIds, List<Long> storeIds);

    /**
     * 查询SPU库存
     * @param productId
     * @param storeIds
     * @return
     */
    List<SpuStockEntity> getSpuStorages(List<Long> productId, List<Long> storeIds);


    /**
     * 查询是否包含江南布衣+BOx+LJ门店
     * @param storeId 门店
     * @return 返回
     */
    boolean containsBLJnbypStore(Long storeId);

    /**
     * 根据id获取
     * @param faStoreId
     * @return
     */
    FaStorage getStorageById(Long faStoreId);


    /**
     * 通过条码ID查询门店库存
     * @param mProductAliasId skuId
     * @return 返回
     */
    long getStorage(Long mProductAliasId, List<Long> asList);

    /**
     * 查询门店SKC库存
     * @param productId 商品id
     * @param storeId 门店id
     * @return 返回
     */
    List<SkcStockEntity> querySkcStorageList(long productId, long storeId);

    /**
     * 查询LJ+BOX仓库存
     * @return 返回
     */
    List<SkcStockEntity> queryLjBoxSkcStorage(long productId);

    /**
     * 获取江南布衣+BOx+LJ下门店SKC库存
     * @param productId 商品id
     * @return 返回
     */
    List<SkcStockEntity> queryBLJnbypSkcStorageList(long productId);

    /**
     * 获取江南布衣+BOx+LJ下门店SKU库存
     * @param mProductAliasIds skuIds
     * @return 返回
     */
    List<ProductStockEntity> getJNBYGatherStock(List<Long> mProductAliasIds);

    /**
     * 查询Eb和微商城库存
     * @param productIds 商品ids
     * @param storeIds 门店ids
     * @return 返回
     */
    List<EbAndWscSpuStockEntity> getEbAndWscSpuStorages(List<Long> productIds, List<Long> storeIds);
}
