<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.CalcLabelOutsideNameMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.CalcLabelOutsideName">
        <id column="ID" property="id" />
        <result column="SC_OUTSIDE_NAME" property="scOutsideName" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, SC_OUTSIDE_NAME
    </sql>
    <select id="selectOutsideNameList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM CALC_LABEL_OUTSIDE_NAME
        ORDER BY ID DESC
    </select>


    <select id="getLabelSettingValueId" resultType="java.lang.Long" useCache="false" flushCache="true">
        select seq_LABEL_OUTSIDE_NAME.nextval from dual
    </select>
</mapper>
