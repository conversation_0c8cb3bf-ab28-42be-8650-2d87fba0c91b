package org.springcenter.product.modules.remote.service.impl;

import org.springcenter.product.modules.remote.IStoreCenterRemoteApi;
import org.springcenter.product.modules.remote.JicBaseResp;
import org.springcenter.product.modules.remote.entity.StoreDetailReqEntity;
import org.springcenter.product.modules.remote.entity.StoreDetailRespEntity;
import org.springcenter.product.modules.remote.service.IStoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;

@Service
@Slf4j
public class StoreServiceImpl implements IStoreService {

    @Autowired
    private IStoreCenterRemoteApi iStoreCenterRemoteApi;

    @Override
    public JicBaseResp<StoreDetailRespEntity> detail(String storeId) {
        StoreDetailReqEntity req = new StoreDetailReqEntity();
        req.setStoreId(Integer.valueOf(storeId));
        try {
            Response<JicBaseResp<StoreDetailRespEntity>> response = iStoreCenterRemoteApi.detail(req).execute();
            if (response.isSuccessful()){
                return response.body();
            }
            throw new RuntimeException("获取门店详情异常storeId = " + storeId);
        } catch (IOException e) {
            log.error("获取门店详情异常 params:{}  e:{} message:{}", storeId, e, e.getMessage());
            throw new RuntimeException("获取门店详情异常");
        }
    }
}
