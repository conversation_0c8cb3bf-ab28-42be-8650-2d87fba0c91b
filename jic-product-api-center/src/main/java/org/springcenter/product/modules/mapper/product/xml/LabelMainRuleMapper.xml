<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.LabelMainRuleMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.LabelMainRule">
    <result column="ID" property="id" />
    <result column="LABEL_ID" property="labelId" />
    <result column="LABEL_CODE" property="labelCode" />
    <result column="LABEL_NAME" property="labelName" />
    <result column="CALC_RELATION" property="calcRelation" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="IS_DELETED" property="isDeleted" />
    <result column="OPERATOR" property="operator" />
    <result column="PARENT_LABEL" property="parentLabel" />
    <result column="SORT" property="sort" />
  </resultMap>
  <insert id="insertBatch">
    INSERT ALL
    <foreach item="item" index="index" collection="list">
      INTO LABEL_MAIN_RULE
      (ID, LABEL_ID, LABEL_CODE, LABEL_NAME, CALC_RELATION, CREATE_TIME, UPDATE_TIME, OPERATOR, PARENT_LABEL,
        SORT) VALUES
      (#{item.id,jdbcType=VARCHAR}, #{item.labelId,jdbcType=VARCHAR}, #{item.labelCode,jdbcType=VARCHAR},
      #{item.labelName,jdbcType=DECIMAL}, #{item.calcRelation,jdbcType=DECIMAL},#{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP}, #{item.operator,jdbcType=VARCHAR}, #{item.parentLabel,jdbcType=VARCHAR},
       #{item.sort,jdbcType=DECIMAL})
    </foreach>
    SELECT 1 FROM DUAL
  </insert>

  <update id="batchUpdate">
    <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
      update LABEL_MAIN_RULE
      set
      LABEL_ID = #{item.labelId},
      LABEL_CODE = #{item.labelCode},
      LABEL_NAME = #{item.labelName},
      CALC_RELATION = #{item.calcRelation},
      OPERATOR = #{item.operator},
      IS_DELETED = #{item.isDeleted},
      SORT = #{item.sort},
      UPDATE_TIME = sysdate
      where ID = #{item.id}
    </foreach>
  </update>

  <resultMap type="org.springcenter.product.modules.model.RuleResp" id="ResultMap">
    <result column="ID" property="id" />
    <result column="LABEL_ID" property="labelId" />
    <result column="LABEL_CODE" property="labelCode" />
    <result column="LABEL_NAME" property="labelName" />
    <result column="CALC_RELATION" property="calcRelation" />
    <result column="SORT" property="sort" />
    <result column="PARENT_LABEL" property="parentLabel" />
    <collection property="labelRuleDetails" resultMap="RuleDetailResultMap"/>
  </resultMap>

  <resultMap type="org.springcenter.product.modules.model.LabelRuleDetailResp" id="RuleDetailResultMap">
    <result column="ID" property="detailId" />
    <result column="LABEL_NAME" property="valueLabelName" />
    <result column="LABEL_NAME_CODE" property="valueLabelNameCode" />
    <result column="LABEL_NAME_TYPE" property="valueLabelNameType" />
    <result column="NAME_VALUE_RELATION" property="nameValueRelation" />
    <result column="LABEL_VALUE" property="labelValue" />
    <result column="LABEL_VALUE_CODE" property="labelValueCode" />
    <result column="CALC_RELATION" property="valueCalcRelation" />
  </resultMap>

  <sql id="BathList_Column_List">
    de.ID as detailId, de.LABEL_NAME as valueLabelName, de.LABEL_NAME_CODE as valueLabelNameCode,
    de.LABEL_NAME_TYPE as valueLabelNameType, de.NAME_VALUE_RELATION as nameValueRelation,
    de.LABEL_VALUE as labelValue, de.LABEL_VALUE_CODE as labelValueCode, de.CALC_RELATION as valueCalcRelation
  </sql>

  <select id="findAll" resultMap="ResultMap">
    select
    ma.ID, ma.LABEL_ID as labelId, ma.LABEL_CODE as labelCode, ma.LABEL_NAME as labelName, ma.CALC_RELATION as calcRelation,
    ma.SORT as sort, ma.PARENT_LABEL as parentLabel,
    <include refid="BathList_Column_List"/>
    from LABEL_MAIN_RULE ma
    left join LABEL_RULE_DETAIL de
    on ma.ID = de.RULE_MAIN_ID
    where ma.IS_DELETED = 0 and de.IS_DELETED = 0
    --GROUP BY ma.LABEL_ID, ma.LABEL_CODE, ma.LABEL_NAME, ma.CALC_RELATION, ma.SORT, ma.PARENT_LABEL
    order by ma.SORT asc
  </select>


</mapper>