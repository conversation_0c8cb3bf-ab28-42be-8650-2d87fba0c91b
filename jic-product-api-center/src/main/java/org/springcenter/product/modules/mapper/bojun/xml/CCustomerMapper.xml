<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.bojun.CCustomerMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.bojun.CCustomer">
        <id column="ID" jdbcType="DECIMAL" property="id" />
        <result column="AD_CLIENT_ID" jdbcType="DECIMAL" property="adClientId" />
        <result column="AD_ORG_ID" jdbcType="DECIMAL" property="adOrgId" />
        <result column="MODIFIERID" jdbcType="DECIMAL" property="modifierid" />
        <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
        <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
        <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
        <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
        <result column="ENTERDATE" jdbcType="DECIMAL" property="enterdate" />
        <result column="ISSTOP" jdbcType="CHAR" property="isstop" />
        <result column="CONTACTER" jdbcType="VARCHAR" property="contacter" />
        <result column="PHONE" jdbcType="VARCHAR" property="phone" />
        <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
        <result column="POST" jdbcType="VARCHAR" property="post" />
        <result column="ACCOUNT" jdbcType="VARCHAR" property="account" />
        <result column="EMAIL" jdbcType="VARCHAR" property="email" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="TAXNO" jdbcType="VARCHAR" property="taxno" />
        <result column="FEEREMAIN" jdbcType="DECIMAL" property="feeremain" />
        <result column="FEESALE" jdbcType="DECIMAL" property="feesale" />
        <result column="SALEDIS" jdbcType="DECIMAL" property="saledis" />
        <result column="SALERETDIS" jdbcType="DECIMAL" property="saleretdis" />
        <result column="C_CUSRANK_ID" jdbcType="DECIMAL" property="cCusrankId" />
        <result column="C_CUSTOMERUP_ID" jdbcType="DECIMAL" property="cCustomerupId" />
        <result column="FIRSALEDIS" jdbcType="DECIMAL" property="firsaledis" />
        <result column="FEELSALE" jdbcType="DECIMAL" property="feelsale" />
        <result column="FEELTAKE" jdbcType="DECIMAL" property="feeltake" />
        <result column="ISPERIOD" jdbcType="CHAR" property="isperiod" />
        <result column="AREAMNG_ID" jdbcType="DECIMAL" property="areamngId" />
        <result column="C_CITY_ID" jdbcType="DECIMAL" property="cCityId" />
        <result column="ISAUTOIN" jdbcType="CHAR" property="isautoin" />
        <result column="ORDERRATE" jdbcType="DECIMAL" property="orderrate" />
        <result column="ISMTRLSTEP" jdbcType="CHAR" property="ismtrlstep" />
        <result column="RET_RATE" jdbcType="DECIMAL" property="retRate" />
        <result column="BIGAREAMNG_ID" jdbcType="DECIMAL" property="bigareamngId" />
        <result column="ISGROUP" jdbcType="CHAR" property="isgroup" />
        <result column="ISACCOUNT" jdbcType="CHAR" property="isaccount" />
        <result column="C_DEPARTMENT_ID" jdbcType="DECIMAL" property="cDepartmentId" />
        <result column="C_CLASSCODE_ID" jdbcType="DECIMAL" property="cClasscodeId" />
        <result column="CODE" jdbcType="VARCHAR" property="code" />
        <result column="UF_CODE" jdbcType="VARCHAR" property="ufCode" />
        <result column="C_AREA_ID" jdbcType="DECIMAL" property="cAreaId" />
        <result column="PRIORITY" jdbcType="DECIMAL" property="priority" />
        <result column="M_DIMCUS1_ID" jdbcType="DECIMAL" property="mDimcus1Id" />
        <result column="M_DIMCUS2_ID" jdbcType="DECIMAL" property="mDimcus2Id" />
        <result column="M_DIMCUS3_ID" jdbcType="DECIMAL" property="mDimcus3Id" />
        <result column="M_DIMCUS4_ID" jdbcType="DECIMAL" property="mDimcus4Id" />
        <result column="M_DIMCUS5_ID" jdbcType="DECIMAL" property="mDimcus5Id" />
        <result column="M_DIMCUS6_ID" jdbcType="DECIMAL" property="mDimcus6Id" />
        <result column="M_DIMCUS7_ID" jdbcType="DECIMAL" property="mDimcus7Id" />
        <result column="M_DIMCUS8_ID" jdbcType="DECIMAL" property="mDimcus8Id" />
        <result column="M_DIMCUS9_ID" jdbcType="DECIMAL" property="mDimcus9Id" />
        <result column="M_DIMCUS10_ID" jdbcType="DECIMAL" property="mDimcus10Id" />
        <result column="BUTSALEDIS" jdbcType="DECIMAL" property="butsaledis" />
        <result column="AGTSALEDIS" jdbcType="DECIMAL" property="agtsaledis" />
        <result column="FIRSALERETDIS" jdbcType="DECIMAL" property="firsaleretdis" />
        <result column="AGTSALERETDIS" jdbcType="DECIMAL" property="agtsaleretdis" />
        <result column="TRANDIS" jdbcType="DECIMAL" property="trandis" />
        <result column="C_CUSATTRIB1_ID" jdbcType="DECIMAL" property="cCusattrib1Id" />
        <result column="C_CUSATTRIB2_ID" jdbcType="DECIMAL" property="cCusattrib2Id" />
        <result column="C_CUSATTRIB3_ID" jdbcType="DECIMAL" property="cCusattrib3Id" />
        <result column="C_CUSATTRIB4_ID" jdbcType="DECIMAL" property="cCusattrib4Id" />
        <result column="C_CUSATTRIB5_ID" jdbcType="DECIMAL" property="cCusattrib5Id" />
        <result column="C_CUSATTRIB6_ID" jdbcType="DECIMAL" property="cCusattrib6Id" />
        <result column="C_CUSATTRIB7_ID" jdbcType="DECIMAL" property="cCusattrib7Id" />
        <result column="C_CUSATTRIB8_ID" jdbcType="DECIMAL" property="cCusattrib8Id" />
        <result column="C_CUSATTRIB9_ID" jdbcType="DECIMAL" property="cCusattrib9Id" />
        <result column="C_CUSATTRIB10_ID" jdbcType="DECIMAL" property="cCusattrib10Id" />
        <result column="C_CUSATTRIB11_ID" jdbcType="DECIMAL" property="cCusattrib11Id" />
        <result column="C_CUSATTRIB12_ID" jdbcType="DECIMAL" property="cCusattrib12Id" />
        <result column="C_CUSATTRIB13_ID" jdbcType="DECIMAL" property="cCusattrib13Id" />
        <result column="C_CUSATTRIB14_ID" jdbcType="DECIMAL" property="cCusattrib14Id" />
        <result column="C_CUSATTRIB15_ID" jdbcType="DECIMAL" property="cCusattrib15Id" />
        <result column="C_CUSATTRIB16_ID" jdbcType="DECIMAL" property="cCusattrib16Id" />
        <result column="C_CUSATTRIB17_ID" jdbcType="DECIMAL" property="cCusattrib17Id" />
        <result column="C_CUSATTRIB18_ID" jdbcType="DECIMAL" property="cCusattrib18Id" />
        <result column="C_CUSATTRIB19_ID" jdbcType="DECIMAL" property="cCusattrib19Id" />
        <result column="C_CUSATTRIB20_ID" jdbcType="DECIMAL" property="cCusattrib20Id" />
        <result column="CAN_BUTSALE" jdbcType="CHAR" property="canButsale" />
        <result column="CAN_NOTBUTSALE" jdbcType="CHAR" property="canNotbutsale" />
        <result column="CAN_AGTSALE" jdbcType="CHAR" property="canAgtsale" />
        <result column="C_CORPSALE_ID1" jdbcType="DECIMAL" property="cCorpsaleId1" />
        <result column="C_CORPSALE_ID2" jdbcType="DECIMAL" property="cCorpsaleId2" />
        <result column="C_CORPSALE_ID3" jdbcType="DECIMAL" property="cCorpsaleId3" />
        <result column="C_CORPRET_ID1" jdbcType="DECIMAL" property="cCorpretId1" />
        <result column="C_CORPRET_ID2" jdbcType="DECIMAL" property="cCorpretId2" />
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
        <result column="C_CORPRET_ID3" jdbcType="DECIMAL" property="cCorpretId3" />
        <result column="YUAN_CUSTOMER" jdbcType="VARCHAR" property="yuanCustomer" />
        <result column="IS_TRUNCOM_RET" jdbcType="CHAR" property="isTruncomRet" />
        <result column="IS_TRUNCOM_SAL" jdbcType="CHAR" property="isTruncomSal" />
        <result column="IS_SALE_PCK" jdbcType="CHAR" property="isSalePck" />
        <result column="FRAMWORK_AREA_ID" jdbcType="DECIMAL" property="framworkAreaId" />
        <result column="IS_PASSWORDCHECK" jdbcType="CHAR" property="isPasswordcheck" />
        <result column="IS_COMPLETE" jdbcType="CHAR" property="isComplete" />
        <result column="PTR4SALERETDIS" jdbcType="DECIMAL" property="ptr4saleretdis" />
        <result column="FEELTAKE_EBSO" jdbcType="DECIMAL" property="feeltakeEbso" />
        <result column="TERMINATEDATE" jdbcType="DECIMAL" property="terminatedate" />
        <result column="CONBEGDATE" jdbcType="DECIMAL" property="conbegdate" />
        <result column="CONBEGEND" jdbcType="DECIMAL" property="conbegend" />
        <result column="C_CONSUMEAREA_ID" jdbcType="DECIMAL" property="cConsumeareaId" />
        <result column="BANK" jdbcType="VARCHAR" property="bank" />
        <result column="IS_AUTOCONFIRM" jdbcType="CHAR" property="isAutoconfirm" />
        <result column="THIRD_SF" jdbcType="VARCHAR" property="thirdSf" />
        <result column="IS_CREDIT" jdbcType="CHAR" property="isCredit" />
        <result column="IS_AUTONTB" jdbcType="CHAR" property="isAutontb" />
        <result column="IS_CONTROLRET" jdbcType="CHAR" property="isControlret" />
        <result column="C_ARCBRAND_ID" jdbcType="DECIMAL" property="cArcbrandId" />
    </resultMap>
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, MODIFIERID, CREATIONDATE, MODIFIEDDATE, OWNERID, ISACTIVE,
        NAME, DESCRIPTION, ENTERDATE, ISSTOP, CONTACTER, PHONE, ADDRESS, POST, ACCOUNT, EMAIL,
        REMARK, TAXNO, FEEREMAIN, FEESALE, SALEDIS, SALERETDIS, C_CUSRANK_ID, C_CUSTOMERUP_ID,
        FIRSALEDIS, FEELSALE, FEELTAKE, ISPERIOD, AREAMNG_ID, C_CITY_ID, ISAUTOIN, ORDERRATE,
        ISMTRLSTEP, RET_RATE, BIGAREAMNG_ID, ISGROUP, ISACCOUNT, C_DEPARTMENT_ID, C_CLASSCODE_ID,
        CODE, UF_CODE, C_AREA_ID, PRIORITY, M_DIMCUS1_ID, M_DIMCUS2_ID, M_DIMCUS3_ID, M_DIMCUS4_ID,
        M_DIMCUS5_ID, M_DIMCUS6_ID, M_DIMCUS7_ID, M_DIMCUS8_ID, M_DIMCUS9_ID, M_DIMCUS10_ID,
        BUTSALEDIS, AGTSALEDIS, FIRSALERETDIS, AGTSALERETDIS, TRANDIS, C_CUSATTRIB1_ID, C_CUSATTRIB2_ID,
        C_CUSATTRIB3_ID, C_CUSATTRIB4_ID, C_CUSATTRIB5_ID, C_CUSATTRIB6_ID, C_CUSATTRIB7_ID,
        C_CUSATTRIB8_ID, C_CUSATTRIB9_ID, C_CUSATTRIB10_ID, C_CUSATTRIB11_ID, C_CUSATTRIB12_ID,
        C_CUSATTRIB13_ID, C_CUSATTRIB14_ID, C_CUSATTRIB15_ID, C_CUSATTRIB16_ID, C_CUSATTRIB17_ID,
        C_CUSATTRIB18_ID, C_CUSATTRIB19_ID, C_CUSATTRIB20_ID, CAN_BUTSALE, CAN_NOTBUTSALE,
        CAN_AGTSALE, C_CORPSALE_ID1, C_CORPSALE_ID2, C_CORPSALE_ID3, C_CORPRET_ID1, C_CORPRET_ID2,
        MOBILE, C_CORPRET_ID3, YUAN_CUSTOMER, IS_TRUNCOM_RET, IS_TRUNCOM_SAL, IS_SALE_PCK,
        FRAMWORK_AREA_ID, IS_PASSWORDCHECK, IS_COMPLETE, PTR4SALERETDIS, FEELTAKE_EBSO, TERMINATEDATE,
        CONBEGDATE, CONBEGEND, C_CONSUMEAREA_ID, BANK, IS_AUTOCONFIRM, THIRD_SF, IS_CREDIT,
        IS_AUTONTB, IS_CONTROLRET, C_ARCBRAND_ID
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            from
            C_CUSTOMER
        where ID = #{id}
    </select>


</mapper>
