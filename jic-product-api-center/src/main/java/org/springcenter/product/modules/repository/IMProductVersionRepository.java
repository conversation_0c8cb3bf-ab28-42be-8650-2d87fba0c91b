package org.springcenter.product.modules.repository;

import org.springcenter.product.modules.entity.ParseProductVersionEntity;
import org.springcenter.product.modules.model.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【M_PRODUCT_VERSION(版型库表)】的数据库操作Service
* @createDate 2022-12-05 09:43:30
*/
public interface IMProductVersionRepository {

    /**
     * 获取全量参数
     * @return 返回
     */
    List<ProductVersionEntity> queryList();

    /**
     * 根据参数查询
     * @param sampleNos 样衣号
     * @param entries 属性参数
     * @param brandList 品牌
     * @param categoryList 类目
     * @param sort 排序
     * @param name 参数搜索
     * @return 返回参数
     */
    List<ProductVersionEntity> queryListByParams(List<String> sampleNos,
                                                 List<String> attrs,
                                                 List<String> brandList,
                                                 List<String> categoryList,
                                                 String sort,
                                                 String name,
                                                 Integer isClosed);

    MProductVersion queryDetail(String id);

    Boolean updateProductVersion(MProductVersion mProductVersion);

    /**
     * 根据版型搜索版型数据
     * @param modelNumber 版型模糊搜索
     * @return 返回数据
     */
    List<MProductVersion> queryByParams(String modelNumber);

    List<MProductVersion> queryAll();

    List<MProductVersion> queryByModelNumber(String modelNumber);

    /**
     * 保存全属性关联值
     * @param mpQueAttrValueConnects 入参
     */
    void batchSaveValueConnects(List<MPQueAttrValueConnect> mpQueAttrValueConnects);

    /**
     * 删除对应mpid全属性关联值
     */
    void delAllMPValueConnectByMpIds(List<String> ids);

    List<MQueriesAttrConEntity> queryMpAttrByMPId(String id);

    /**
     * 批量更新值
     * @param valueConnects 值
     */
    void batchUpdateValue(List<MPQueAttrValueConnect> valueConnects);

    /**
     * 根据商品id和小类查询合体度
     * @param rets 入参
     * @return 返回
     */
    List<MPQueAttrValueConnect> selectValueByProductId(List<ProductVersionEntity> rets);
}
