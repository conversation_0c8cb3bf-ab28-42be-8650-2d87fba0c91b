package org.springcenter.product.modules.service.impl;

import brave.Tracer;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsSetQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springcenter.product.api.IProductDetailsImgApi;
import org.springcenter.product.api.SingleProductRecommendationReq;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.ProductCenterSearchCenterEnum;
import org.springcenter.product.modules.context.BojunProductInfo;
import org.springcenter.product.modules.context.GoodsAiSingleContext;
import org.springcenter.product.modules.context.ProductApiContext;
import org.springcenter.product.modules.context.UpdateStoreEsContext;
import org.springcenter.product.modules.entity.*;
import org.springcenter.product.modules.enums.BigDataBjBrandEnum;
import org.springcenter.product.modules.enums.BigDataSearchChannelEnum;
import org.springcenter.product.modules.enums.BrandBrandEnum;
import org.springcenter.product.modules.mapper.bojun.*;
import org.springcenter.product.modules.mapper.product.*;
import org.springcenter.product.modules.model.BoxMProduct;
import org.springcenter.product.modules.model.*;
import org.springcenter.product.modules.model.bojun.*;
import org.springcenter.product.modules.remote.entity.BigDataSearchReqEntity;
import org.springcenter.product.modules.remote.entity.BigDataSearchRespEntity;
import org.springcenter.product.modules.remote.entity.BigDataSingleReconReq;
import org.springcenter.product.modules.remote.service.IBigDataService;
import org.springcenter.product.modules.repository.IBoxMProductRepository;
import org.springcenter.product.modules.repository.IProductAttributesRepository;
import org.springcenter.product.modules.repository.bojun.StockRepository;
import org.springcenter.product.modules.service.CommonDataRuleService;
import org.springcenter.product.modules.service.IProductService;
import org.springcenter.product.modules.service.IProductStoreService;
import org.springcenter.product.modules.util.EsUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import strman.Strman;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/15/21 11:19 AM
 */
@Service
@RefreshScope
public class ProductService implements IProductService {
    private static final Logger logger = LoggerFactory.getLogger(ProductService.class);
    private static final String SINGLE_TOP50_INDEX = "index_jnby_rec_res_model_top50";
    private static final String LOOK_AI_INDEX = "index_jnby_rec_res_box_look";

    @Autowired
    private IProductAttributesRepository iProductAttributesRepository;

    @Autowired
    private IBoxMProductRepository iBoxMProductRepository;

    @Autowired
    private StockRepository stockRepository;

    @Value("${es.index.goods}")
    private String goodsIndex;

    @Value("${es.index.store.goods.skc}")
    private String storeProductSkcIndex;

    @Autowired
    private EsUtil esUtil;

    @Value("${bandIds.config}")
    private String bandIdsConfig;

    @Value("${es.index.similar.goods}")
    private String similarProductIndex;

    @Value("${es.index.store.goods}")
    private String storeGoodsIndex;

    @Value("${bands.str}")
    private String bandIdsStr;

    @Value("${brands.str}")
    private String brandsStr;

    @Value("${sample.product.skc.index}")
    private String SAMPLE_PRODUCT_SKC_INDEX;

    @Value("${product.skc.index}")
    private String PRODUCT_SKC_INDEX;

    @Autowired
    private MProductExtendMapper mProductExtendMapper;

    @Value("${search.product.attr}")
    private String productSizeNos;

    @Value("${new.tag}")
    private Long newTag;

    @Value("${product.aolai.radius}")
    private String productAolaiRadius;

    @Autowired
    private CommonDataRuleService commonDataRuleService;

    @Autowired
    private JicProductMallRelationMapper jicProductMallRelationMapper;

    @Autowired
    private JicMallOrgMapper jicMallOrgMapper;

    @Autowired
    private JicMProductAttrMapper jicMProductAttrMapper;

    @Autowired
    private BoxMProductMapper boxMProductMapper;

    @Autowired
    private MProductMapper mProductMapper;

    @Autowired
    private MProductListMapper mProductListMapper;

    @Autowired
    private MProductaliasAliasAgainMapper mProductaliasAliasAgainMapper;

    @Autowired
    private MdimMapper mdimMapper;

    @Value("${es.index.split.store.goods}")
    private String storeSplitGoodsIndex;

    @Autowired
    private CStoreMapper cStoreMapper;

    @Value("${es.index.split.store.goods.mode}")
    private Long storeSplitGoodsIndexMode;

    @Autowired
    private BBoxMProductMapper bBoxMProductMapper;


    @Autowired
    private IProductDetailsImgApi productDetailsImgApi;
    
    @Autowired
    private IBigDataService bigDataService;

    @Value("${yingtong.brand}")
    private String yingTongBrandId;

    @Autowired
    private FabInfoMapper fabInfoMapper;

    @Autowired
    private Tracer tracer;

    @Autowired
    private JicProductMallInfoMapper jicProductMallInfoMapper;

    @Autowired
    private JicProductPutwayMapper jicProductPutwayMapper;

    @Override
    public MAttributesetinstance findProductAttr(String color, String size) {
        MAttributesetinstance mAttributesetinstance = new MAttributesetinstance();
        mAttributesetinstance.setValue1(color);
        mAttributesetinstance.setValue2(size);
        List<MAttributesetinstance> list = Optional.ofNullable(iProductAttributesRepository.selectListBySelective(mAttributesetinstance)).orElseGet(null);
        return list == null ? null : list.get(0);
    }

    @Override
    public BoxMProduct findOneByCondition(BoxMProduct product){
        return iBoxMProductRepository.findOneByCondition(product);
    }

    @NewSpan
    @Override
    public List<BoxMProduct> selectGoodsListBySkuIds(List<Long> skuIds) {
        logger.info("selectGoodsListBySkuIds接口调用输入");
        List<BoxMProduct> boxMProducts = iBoxMProductRepository.selectListBySkuIds(skuIds);
        logger.info("selectGoodsListBySkuIds接口调用输出");
        return boxMProducts;
    }

    @Override
    public List<GoodsAiRecEntity> searchAiBySingleGoods(GoodsAiSingleContext context, Page page) {
        SearchRequest request = new SearchRequest();
        request.indices(SINGLE_TOP50_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        //query create
        if (StringUtils.isNotBlank(context.getUnionId())){
            queryBuilder.must(QueryBuilders.termQuery("unionid", context.getUnionId()));
        }
        if (context.getCategoryId() != null && context.getCategoryId() > 0L){
            queryBuilder.must(QueryBuilders.termQuery("m_big_category_id", context.getCategoryId()));
        }
        if (context.getBrandId() != null && context.getBrandId() > 0L){
            queryBuilder.must(QueryBuilders.termQuery("m_brand_id", context.getBrandId()));
        }
        if (context.getSelectedAliasId() != null && context.getSelectedAliasId().size() > 0){
            queryBuilder.must(QueryBuilders.termsQuery("m_productalias_id", Arrays.asList(context.getSelectedAliasId())));
        }
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1)*page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        sourceBuilder.sort("score", SortOrder.DESC);
        request.source(sourceBuilder);

        List<GoodsAiRecEntity> list = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                list.add(GoodsAiRecEntity.fromJson(hits[i].getSourceAsString(), GoodsAiRecEntity.class));
            }
        } catch (IOException e) {
            logger.error("查询单品推荐异常 unionid = {}, e = {}", context.getUnionId(), e.getMessage());
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<UserGoodsLookRecEntity> searchAiLook(String unionId) {
        SearchRequest request = new SearchRequest();
        request.indices(LOOK_AI_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("unionid", unionId));

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(100);
        sourceBuilder.sort("total_score", SortOrder.DESC);
        request.source(sourceBuilder);
        List<UserGoodsLookRecEntity> list = new ArrayList<>();

        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }

            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                list.add(UserGoodsLookRecEntity.fromJson(hits[i].getSourceAsString(), UserGoodsLookRecEntity.class));
            }
        } catch (IOException e) {
            logger.error("查询单品推荐异常 unionid = {}, e = {}", unionId, e.getMessage());
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<GoodSpuResp> searchGoods(QueryGoodsReq context, Page page) {
        logger.info("查询过滤参数 params = {}", context.toString());
        SearchRequest request = new SearchRequest();
        request.indices(goodsIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        buildQueryGoodsIndex(queryBuilder, context);
        List<String> includeSource = Stream.of("name","value","price","id", "sales_num","cover_imgs","labels",
                "label_levels","c_arcbrand_id", "sample_code", "banner_imgs", "detail_imgs",
                "year","m_band_id","small_season_id","m_big_category_id","m_small_category_id","m_band",
                "m_big_category", "small_season", "brand", "m_small_category", "mark_style", "fab", "mall_title",
                "lifestyle_tag").collect(Collectors.toList());
        if (context.isReturnSku()){
            includeSource.add("sku_list");
        }
        sourceBuilder.fetchSource(includeSource.toArray(new String[]{}), new String[]{});
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1)*page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        if (context.getSorted() == 2){
            sourceBuilder.sort("sales_num", SortOrder.DESC);
        }
        if (context.getSorted() == 1){
            sourceBuilder.sort("year", SortOrder.DESC).sort("m_band_id", SortOrder.DESC);
        }
        if (context.getSorted() == 3){
            sourceBuilder.sort("id", SortOrder.DESC);
        }
        request.source(sourceBuilder);
        List<GoodSpuResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                GoodSpuResp entity = GoodSpuResp.fromJson(hits[i].getSourceAsString(), GoodSpuResp.class);
                if (context.isReturnSku()){
                    entity.buildSku();
                }
                entity.setM_product_id(entity.getId());
                entities.add(entity);
            }
        } catch (IOException e) {
            logger.error("查询商品异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }

    private void buildQueryGoodsIndex(BoolQueryBuilder queryBuilder, QueryGoodsReq context) {

        if (context.getProductId() != null){
            queryBuilder.must(QueryBuilders.termQuery("id", context.getProductId()));
        }
        if (CollectionUtils.isNotEmpty(context.getProductIds())){
            queryBuilder.must(QueryBuilders.termsQuery("id", context.getProductIds()));
        }
        if (StringUtils.isNotBlank(context.getName())){
            queryBuilder.must(QueryBuilders.matchPhraseQuery("name",context.getName()));
        }
        if (context.getBandId() != null){
            queryBuilder.must(QueryBuilders.termQuery("m_band_id", context.getBandId()));
        }
        if (context.getSeasonId() != null){
            queryBuilder.must(QueryBuilders.termQuery("big_season_id", context.getSeasonId()));
        }
        if (!context.getBigCategoryIds().isEmpty()){
            queryBuilder.must(QueryBuilders.termsQuery("m_big_category_id", context.getBigCategoryIds()));
        }
        if (!context.getSmallCategoryIds().isEmpty()){
            queryBuilder.must(QueryBuilders.termsQuery("m_small_category_id", context.getSmallCategoryIds()));
        }
        if (context.getBrandId() != null){
            queryBuilder.must(QueryBuilders.termQuery("c_arcbrand_id", context.getBrandId()));
        }
        if (context.getStoreId() != null){
            queryBuilder.must(QueryBuilders.termQuery("store_id", context.getStoreId()));
        }
        if (context.getYear() != null){
            queryBuilder.must(QueryBuilders.termQuery("year", context.getYear()));
        }
        if (context.getIsStockNotEmpty() == 1){
            queryBuilder.must(QueryBuilders.termQuery("qty", 1));
        }
        if (context.getLPrice() != null || context.getGPrice() != null){
            if (context.getLPrice() != null && context.getGPrice() != null){
                queryBuilder.must(QueryBuilders.rangeQuery("price").gte(context.getLPrice()).lte(context.getGPrice()));
            }

            if (context.getLPrice() != null && context.getGPrice() == null){
                queryBuilder.must(QueryBuilders.rangeQuery("price").gte(context.getLPrice()));
            }

            if (context.getLPrice() == null && context.getGPrice() != null){
                queryBuilder.must(QueryBuilders.rangeQuery("price").lte(context.getGPrice()));
            }
        }
        if (!ObjectUtils.isEmpty(context.getMustLabels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("labels", context.getMustLabels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(context.getMustLabelLevels().size())));
            queryBuilder.must(termsSetQueryBuilder);
        }

        if (!ObjectUtils.isEmpty(context.getMustLabelLevels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("label_levels", context.getMustLabelLevels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(context.getMustLabelLevels().size())));
            queryBuilder.must(termsSetQueryBuilder);
        }

        if (!ObjectUtils.isEmpty(context.getMustNotLabels())){
            queryBuilder.mustNot(QueryBuilders.termsQuery("labels", context.getMustNotLabels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList())));
        }
        if (CollectionUtils.isNotEmpty(context.getBigCategoryIds())){
            queryBuilder.must(QueryBuilders.termsQuery("m_big_category_id", context.getBigCategoryIds()));
        }
        if (CollectionUtils.isNotEmpty(context.getSmallCategoryIds())){
            queryBuilder.must(QueryBuilders.termsQuery("m_small_category_id", context.getSmallCategoryIds()));
        }
        if (CollectionUtils.isNotEmpty(context.getYears())){
            queryBuilder.must(QueryBuilders.termsQuery("year", context.getYears()));
        }
        if (CollectionUtils.isNotEmpty(context.getBrandIds())){
            queryBuilder.must(QueryBuilders.termsQuery("c_arcbrand_id", context.getBrandIds()));
        }
        if (CollectionUtils.isNotEmpty(context.getBandIds())){
            queryBuilder.must(QueryBuilders.termsQuery("m_band_id", context.getBandIds()));
        }
        if (CollectionUtils.isNotEmpty(context.getSampleCodes())){
            queryBuilder.must(QueryBuilders.termsQuery("sample_code", context.getSampleCodes()));
        }
        if (CollectionUtils.isNotEmpty(context.getNames())){
            queryBuilder.must(QueryBuilders.termsQuery("name", context.getNames()));
        }

        if (StringUtils.isNotBlank(context.getValue())) {
            queryBuilder.must(QueryBuilders.matchQuery("value", context.getValue()));
        }

        if (CollectionUtils.isNotEmpty(context.getExcludeBigCategoryIds())){
            queryBuilder.mustNot(QueryBuilders.termsQuery("m_big_category_id", context.getExcludeBigCategoryIds()));
        }
        if (CollectionUtils.isNotEmpty(context.getExcludeSmallCategoryIds())){
            queryBuilder.mustNot(QueryBuilders.termsQuery("m_small_category_id", context.getExcludeSmallCategoryIds()));
        }

        if (StringUtils.isNotBlank(context.getSearchParam())) {
            queryBuilder.must(QueryBuilders.wildcardQuery("value", "*" + context.getSearchParam() + "*"));
        }
    }

    @Override
    public List<ProductSkcResp> searchGoodsSkc(QueryGoodsReq context, Page page, String component) {
        // 对入参品牌进行处理进行处理 婴童和童装需要传品牌名称
        if (CollectionUtils.isNotEmpty(context.getBrandIds())) {
            List<BrandBrandEnum> brandInfoByCode = BrandBrandEnum.getBrandInfoByCode(context.getBrandIds());
            if (CollectionUtils.isNotEmpty(brandInfoByCode)) {
                List<String> names = new ArrayList<>();
                brandInfoByCode.stream().forEach(v -> {
                    names.addAll(v.getDesc());
                });
                context.setBrandNames(names);
                context.setBrandIds(brandInfoByCode.stream().map(v -> v.getInsideCode()).collect(Collectors.toList()));
            }
        }

        // 获取数据权限
        List<String> brands= new ArrayList<>();
        if (StringUtils.isNotBlank(component)) {
            brands = commonDataRuleService.getPeopleBrandDataRule(component);
        }
        if (CollectionUtils.isNotEmpty(brands)) {
            List<Long> brandIdsL = brands.stream().map(v -> Long.valueOf(v)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(context.getBrandIds())) {
                context.setBrandIds(brandIdsL);
            } else {
                List<Long> filter = context.getBrandIds().stream().filter(v -> brandIdsL.contains(v)).collect(Collectors.toList());
                context.setBrandIds(filter);
            }
        }

        // 查看是否有搜索参数
        if (StringUtils.isNotBlank(context.getSearchParam()) && context.getSearchType() == 0) {
            List<String> productIds = getInBigData(context.getBrandIds(), context);
            if (CollectionUtils.isEmpty(productIds)) {
                return Collections.emptyList();
            }
            context.setProductIds(productIds);
        }

        SearchRequest request = new SearchRequest();
        logger.info("========searchGoodsSkc, 过滤的品牌信息:{}", JSONObject.toJSONString(context.getBrandIds()));
        request.indices(PRODUCT_SKC_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        buildQuery(queryBuilder, context);
        sourceBuilder.fetchSource(new String[]{"name","value","price","skc_code", "sales_num","colorno",
                "color_name","imgurl","cover_imgs","product_id","brand","c_arcbrand_id","m_band","m_band_id","year","small_season_id","m_big_category_id",
        "m_small_category_id","small_season","m_small_category","m_big_category"}, new String[]{});
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1)*page.getPageSize());
        if (CollectionUtils.isNotEmpty(context.getProductIds())) {
            sourceBuilder.size(1000);
        } else {
            sourceBuilder.size(page.getPageSize());
        }

/*//        if (context.getSorted() == 2){
            sourceBuilder.sort("product_id", SortOrder.DESC);
//        }
        if (context.getSorted() == 1){
            sourceBuilder.sort("year", SortOrder.DESC).sort("m_band_id", SortOrder.DESC);
        }*/
        sourceBuilder.sort("date_list", SortOrder.DESC)
                     .sort("year", SortOrder.DESC)
                     .sort("m_band_id", SortOrder.DESC)
                     .sort("c_arcbrand_id", SortOrder.DESC);
        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        logger.info("查询商品SKC-ES {}", request.source().toString());
        List<ProductSkcResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductSkcResp entity = ProductSkcResp.fromJson(hits[i].getSourceAsString(), ProductSkcResp.class);
                entity.setId(hits[i].getId());
                entity.setPrimaryKey(hits[i].getId());
                entities.add(entity);
            }
            //相同SKC排在一起
            entities = entities.stream().sorted(Comparator.comparing(ProductSkcResp::getName).reversed()).collect(Collectors.toList());
        } catch (IOException e) {
            logger.error("查询商品SKC异常e = {}", e.getMessage());
            return new ArrayList<>();
        }


        // 处理skc图片
        // 24年前的先取微盟再取杰哥服务器 24年后先取微盟再取网盘再取杰哥服务器
        List<ProductSkcResp> picRets = new ArrayList<>();
        entities.forEach(entity -> {
            if (StringUtils.isBlank(entity.getYear()) || Objects.equals(entity.getYear(), "无年份")) {
                return;
            }
            // 没有图片 || 不是微盟图片的数据 都查网盘图
            if ((Integer.valueOf(entity.getYear()) >= 2024 && StringUtils.isBlank(entity.getImgurl())) ||
                    ((Integer.valueOf(entity.getYear()) >= 2024 && !StringUtils.contains(entity.getImgurl(), "weimob")))) {
                picRets.add(entity);
            }
        });
        if (CollectionUtils.isNotEmpty(picRets)) {
            CommonRequest<FindProductDetailImgReq> req = new CommonRequest<>();
            FindProductDetailImgReq imgReq = new FindProductDetailImgReq();
            imgReq.setProductCodes(Lists.newArrayList(picRets.stream().map(ProductSkcResp::getName).collect(Collectors.toList())));
            req.setRequestData(imgReq);
            ResponseResult<List<FindProductDetailImgResp>> detailImg = productDetailsImgApi.findProductDetailImg(req);
            if (CollectionUtils.isEmpty(detailImg.getData())) {
                return entities;
            }

            List<BatchGetViewUrlReq.LianxiangData> reqs = new ArrayList<>();
            HashMap<String, List<FindProductDetailImgResp.ImgData>> map = detailImg.getData().stream()
                    .collect(HashMap::new, (k, v) -> k.put(v.getProductCode(), v.getMainImgs()), HashMap::putAll);
            Map<String, String> retsMap = new HashMap<>();
            picRets.forEach(v -> {
                if (org.apache.commons.collections4.MapUtils.isNotEmpty(map) && CollectionUtils.isNotEmpty(map.get(v.getName()))) {
                    FindProductDetailImgResp.ImgData data = map.get(v.getName()).stream()
                            .filter(x -> Objects.equals(x.getColorNo(), v.getColorno())).findFirst().orElse(null);
                    if (data != null) {
                        BatchGetViewUrlReq.LianxiangData file = new BatchGetViewUrlReq.LianxiangData();
                        file.setNeid(data.getNeid());
                        file.setNsid(data.getNsid());

                        reqs.add(file);
                        retsMap.put(v.getId(), data.getNeid());
                    }
                }
            });

            // 查询对应图片
            if (CollectionUtils.isEmpty(reqs)) {
                return entities;
            }
            CommonRequest<LianxiangBatchGetViewReq> commonRequest = new CommonRequest<>();
            LianxiangBatchGetViewReq viewReq = new LianxiangBatchGetViewReq();
            viewReq.setFile_array(reqs);
            commonRequest.setRequestData(viewReq);
            ResponseResult<List<LianxiangBatchGetViewResp>> result = productDetailsImgApi.batchGetViewUrl(commonRequest);
            if (CollectionUtils.isEmpty(result.getData()) || org.apache.commons.collections4.MapUtils.isEmpty(retsMap)) {
                return entities;
            }

            HashMap<String, String> urlMap = result.getData().stream()
                    .filter(v -> StringUtils.isNotBlank(v.getPreviewUrl()))
                    .collect(HashMap::new, (k, v) -> k.put(v.getNeid(), v.getPreviewUrl()), HashMap::putAll);
            entities.forEach(v -> {
                if (StringUtils.isNotBlank(retsMap.get(v.getId()))) {
                    String neId = retsMap.get(v.getId());
                    if (StringUtils.isBlank(urlMap.get(neId))) {
                        return;
                    }
                    v.setImgurl(urlMap.get(neId));
                }
            });
        }

        return entities;
    }

    private List<String> getInBigData(List<Long> brandIds, QueryGoodsReq context) {
        BigDataSearchReqEntity reqEntity = new BigDataSearchReqEntity();
        reqEntity.setBrand(CollectionUtils.isEmpty(BigDataBjBrandEnum.getByCode(brandIds)) ?
                Arrays.stream(BigDataBjBrandEnum.values()).map(BigDataBjBrandEnum::getDesc).collect(Collectors.toList()) :
                BigDataBjBrandEnum.getByCode(brandIds));
        reqEntity.setChannel_dict(BigDataSearchChannelEnum.PRODUCT_CENTER.getCode());
        reqEntity.setQuery(context.getSearchParam());
        reqEntity.setRequestId(BigDataSearchChannelEnum.PRODUCT_CENTER.getDesc() + System.currentTimeMillis());
        reqEntity.setTraceId(tracer.currentSpan().context().traceIdString());
        // 默认就取800条
        reqEntity.setList_size(800);
        reqEntity.setList_start(0);

        List<BigDataSearchRespEntity> entities = bigDataService.searchByText(reqEntity);

        return entities.stream().map(v -> Objects.toString(v.getSpu_id())).collect(Collectors.toList());
    }

    @Override
    public List<SampleProductSkcResp> searchSampleProductSkc(SampleProductReq context, Page page, String component) {
        // 获取数据权限
        List<String> brands= new ArrayList<>();
        if (StringUtils.isNotBlank(component)) {
            brands = commonDataRuleService.getPeopleBrandDataRule(component);
        }
        if (CollectionUtils.isNotEmpty(brands)) {
            if (CollectionUtils.isEmpty(context.getBrandIds())) {
                context.setBrandIds(brands);
            } else {
                List<String> finalBrands = brands;
                List<String> filter = context.getBrandIds().stream().filter(v -> finalBrands.contains(v)).collect(Collectors.toList());
                context.setBrandIds(filter);
            }
        }

        SearchRequest request = new SearchRequest();
        logger.info("==============searchSampleProductSkc, 过滤的品牌信息:{}", JSONObject.toJSONString(context.getBrandIds()));
        request.indices(SAMPLE_PRODUCT_SKC_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (Objects.nonNull(context.getName()) && !"".equals(context.getName())){
            queryBuilder.must(QueryBuilders.termQuery("sample_code", context.getName()));
        }

        if (CollectionUtils.isNotEmpty(context.getNameLists())) {
            queryBuilder.must(QueryBuilders.termsQuery("sample_code", context.getNameLists()));
        }

        if (CollectionUtils.isNotEmpty(context.getBrandIds())){
            // 转换品牌
            List<BrandConvertEntity> brandConvertEntities = JSONObject.parseArray(brandsStr, BrandConvertEntity.class);
            List<String> brandsName = new ArrayList<>();
            context.getBrandIds().forEach(v -> {
                BrandConvertEntity entity = brandConvertEntities.stream().filter(x -> Objects.equals(x.getCode(), v))
                        .findFirst().orElse(null);
                if (entity != null) {
                    brandsName.add(entity.getName());
                }
            });
            queryBuilder.must(QueryBuilders.termsQuery("brand", brandsName));
        }

        if (CollectionUtils.isNotEmpty(context.getYears())){
            queryBuilder.must(QueryBuilders.termsQuery("year", context.getYears()));
        }

        if (CollectionUtils.isNotEmpty(context.getSeasonIds())){
            queryBuilder.must(QueryBuilders.termsQuery("small_season_id", context.getSeasonIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getBandIds())){
            // 转换波段
            List<AttrResp> attrEntities = JSONObject.parseArray(bandIdsStr, AttrResp.class);
            if (context.getBandIds().size() == 1 && Objects.equals(context.getBandIds().get(0), 2000L)) {
                queryBuilder.mustNot(QueryBuilders.termsQuery("m_band", 
                        attrEntities.stream().map(AttrResp::getAttribname).collect(Collectors.toList())));
            } else if (context.getBandIds().contains(2000L)) {
                BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                BoolQueryBuilder subBool1 = new BoolQueryBuilder();
                List<String> bandNames = new ArrayList<>();
                context.getBandIds().forEach(v -> {
                    AttrResp attrResp = attrEntities.stream().filter(x -> Objects.equals(v, x.getId())).findFirst().orElse(null);
                    if (attrResp != null) {
                        bandNames.add(attrResp.getAttribname());
                    }
                });
                subBool1.must(QueryBuilders.termsQuery("m_band", bandNames));

                BoolQueryBuilder subBool = new BoolQueryBuilder();
                subBool.mustNot(QueryBuilders.termsQuery("m_band",
                        attrEntities.stream().map(AttrResp::getAttribname).collect(Collectors.toList())));
                boolQueryBuilder.should(subBool1);
                boolQueryBuilder.should(subBool);
                queryBuilder.must(boolQueryBuilder);
            } else {
                List<String> bandNames = new ArrayList<>();
                context.getBandIds().forEach(v -> {
                    AttrResp attrResp = attrEntities.stream().filter(x -> Objects.equals(v, x.getId())).findFirst().orElse(null);
                    if (attrResp != null) {
                        bandNames.add(attrResp.getAttribname());
                    }
                });
                queryBuilder.must(QueryBuilders.termsQuery("m_band", bandNames));
            }
        }

        if (CollectionUtils.isNotEmpty(context.getSmallCategoryIds())){
            queryBuilder.must(QueryBuilders.termsQuery("small_category_id", context.getSmallCategoryIds()));
        }

        if (!ObjectUtils.isEmpty(context.getMustLabels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("labels.keyword", context.getMustLabels().stream().map(item -> item.toLowerCase()).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(context.getMustLabelLevels().size())));
            queryBuilder.must(termsSetQueryBuilder);
        }

        if (!ObjectUtils.isEmpty(context.getMustLabelLevels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("label_levels", context.getMustLabelLevels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(context.getMustLabelLevels().size())));
            queryBuilder.must(termsSetQueryBuilder);
        }

//        if (!ObjectUtils.isEmpty(context.getMustLabels())){
//            List matchList = new ArrayList<>();
//            context.setMustLabels(context.getMustLabels().stream().map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
//            for(String label : context.getMustLabels()){
//                matchList.add(QueryBuilders.matchPhraseQuery("labels", label));
//            }
//            queryBuilder.should().addAll(matchList);
//            queryBuilder.minimumShouldMatch(1);
//        }
        if (!ObjectUtils.isEmpty(context.getMustNotLabels())){
            context.setMustNotLabels(context.getMustNotLabels().stream().map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            queryBuilder.mustNot(QueryBuilders.termsQuery("labels", context.getMustNotLabels()));
        }

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1)*page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        request.source(sourceBuilder);

        List<SampleProductSkcResp> entities = new ArrayList<>();
        try {
            logger.info("SAMPLE_PRODUCT_SKC_INDEX 索引入参sourceBuilder：{}", sourceBuilder);
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                SampleProductSkcResp entity = SampleProductSkcResp.fromJson(hits[i].getSourceAsString(), SampleProductSkcResp.class);
                if (Objects.nonNull(entity.getLabels())){
                    entity.setLabels(entity.getLabels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                    entity.setLabel_levels(entity.getLabel_levels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                }
                entities.add(entity);
            }
        } catch (IOException e) {
            logger.error("查询商品SKC异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }

    @Override
    public SampleProductFilterOptionResp querySampleProductFilter() {
        SearchRequest request = new SearchRequest();
        SampleProductFilterOptionResp resp = new SampleProductFilterOptionResp();
        request.indices(SAMPLE_PRODUCT_SKC_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder
                .aggregation(AggregationBuilders.terms("brand_group").field("brand").size(20))
                .aggregation(AggregationBuilders.terms("year_group").field("year").size(20))
                .aggregation(AggregationBuilders.terms("band_group").field("m_band").size(20))
                .aggregation(AggregationBuilders.terms("season_group").field("big_season").size(20));
        sourceBuilder.size(0);
        request.source(sourceBuilder);

        try {
            SearchResponse response = esUtil.search(request);
            Aggregations aggregations = response.getAggregations();
            Map<String, Aggregation> aggregationMap = aggregations.getAsMap();
            ParsedStringTerms brandTerms = (ParsedStringTerms) aggregationMap.get("brand_group");
            List<String> brands = new ArrayList<>();
            brandTerms.getBuckets().forEach(item -> brands.add(item.getKeyAsString()));
            resp.setBrand(brands);

            ParsedStringTerms yearTerms = (ParsedStringTerms) aggregationMap.get("year_group");
            List<String> years = new ArrayList<>();
            yearTerms.getBuckets().forEach(item -> years.add(item.getKeyAsString()));
            resp.setYears(years);

            ParsedStringTerms bandTerms = (ParsedStringTerms) aggregationMap.get("band_group");
            List<String> bands = new ArrayList<>();
            bandTerms.getBuckets().forEach(item -> bands.add(item.getKeyAsString()));
            resp.setBands(bands);

            ParsedStringTerms seasonTerms = (ParsedStringTerms) aggregationMap.get("season_group");
            List<String> seasons = new ArrayList<>();
            seasonTerms.getBuckets().forEach(item -> seasons.add(item.getKeyAsString()));
            resp.setSeasons(seasons);
        } catch (IOException e) {
            logger.error("查询商品SKC异常e = {}", e.getMessage());
        }
        return resp;
    }

    private void buildQuery(BoolQueryBuilder queryBuilder, QueryGoodsReq context){
        if (CollectionUtils.isNotEmpty(context.getNames())){
            queryBuilder.must(QueryBuilders.termsQuery("name",context.getNames()));
        }
        if (StringUtils.isNotBlank(context.getName())){
            // 处理 如果是九位且没有中文 走款号搜索
            //          走模糊匹配
            if (!isChinese(context.getName())) {
                queryBuilder.must(QueryBuilders.termQuery("name",context.getName()));
            } else {
                queryBuilder.must(QueryBuilders.matchQuery("bom_text",context.getName()));
            }
        }
        if (context.getBandId() != null){
            queryBuilder.must(QueryBuilders.termQuery("m_band_id", context.getBandId()));
        }
        if (CollectionUtils.isNotEmpty(context.getSeasonIds())){
            queryBuilder.must(QueryBuilders.termsQuery("small_season_id", context.getSeasonIds()));
        }
        if (context.getSeasonId() != null){
            queryBuilder.must(QueryBuilders.termQuery("big_season_id", context.getSeasonId()));
        }
        if (!context.getBigCategoryIds().isEmpty()){
            queryBuilder.must(QueryBuilders.termsQuery("m_big_category_id", context.getBigCategoryIds()));
        }
        if (!context.getSmallCategoryIds().isEmpty()){
            queryBuilder.must(QueryBuilders.termsQuery("m_small_category_id", context.getSmallCategoryIds()));
        }
        if (context.getBrandId() != null){
            queryBuilder.must(QueryBuilders.termQuery("c_arcbrand_id", context.getBrandId()));
        }
        if (context.getStoreId() != null){
            queryBuilder.must(QueryBuilders.termQuery("store_id", context.getStoreId()));
        }
        if (context.getYear() != null){
            queryBuilder.must(QueryBuilders.termQuery("year", context.getYear()));
        }
        if (context.getIsStockNotEmpty() == 1){
            queryBuilder.must(QueryBuilders.termQuery("qty", 1));
        }
        if (context.getLPrice() != null || context.getGPrice() != null){
            if (context.getLPrice() != null && context.getGPrice() != null){
                queryBuilder.must(QueryBuilders.rangeQuery("price").gte(context.getLPrice()).lte(context.getGPrice()));
            }

            if (context.getLPrice() != null && context.getGPrice() == null){
                queryBuilder.must(QueryBuilders.rangeQuery("price").gte(context.getLPrice()));
            }

            if (context.getLPrice() == null && context.getGPrice() != null){
                queryBuilder.must(QueryBuilders.rangeQuery("price").lte(context.getGPrice()));
            }
        }
        if(CollectionUtils.isNotEmpty(context.getSkcIds())){
            queryBuilder.must(QueryBuilders.termsQuery("_id",context.getSkcIds()));
        }

        if (!ObjectUtils.isEmpty(context.getMustLabels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("labels", context.getMustLabels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(context.getMustLabelLevels().size())));
            queryBuilder.must(termsSetQueryBuilder);
        }

        if (!ObjectUtils.isEmpty(context.getMustLabelLevels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("label_levels", context.getMustLabelLevels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(context.getMustLabelLevels().size())));
            queryBuilder.must(termsSetQueryBuilder);
        }

        if (!ObjectUtils.isEmpty(context.getMustNotLabels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("label_levels", context.getMustNotLabels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(context.getMustNotLabels().size())));
            queryBuilder.mustNot(termsSetQueryBuilder);
        }
        if (context.getProductId() != null){
            queryBuilder.must(QueryBuilders.termQuery("product_id",context.getProductId()));
        }

        if (context.getProductIds() != null){
            queryBuilder.must(QueryBuilders.termsQuery("product_id",context.getProductIds()));
        }

        if (!context.getBigCategoryIds().isEmpty()){
            queryBuilder.must(QueryBuilders.termsQuery("m_big_category_id", context.getBigCategoryIds()));
        }
        if (!context.getSmallCategoryIds().isEmpty()){
            queryBuilder.must(QueryBuilders.termsQuery("m_small_category_id", context.getSmallCategoryIds()));
        }
        if (!context.getYears().isEmpty()){
            queryBuilder.must(QueryBuilders.termsQuery("year", context.getYears()));
        }
        if (!context.getBrandIds().isEmpty()){
            queryBuilder.must(QueryBuilders.termsQuery("c_arcbrand_id", context.getBrandIds()));
        }
        if (!context.getBandIds().isEmpty()){
            List<Long> other = new ArrayList<>();
            if(context.getBandIds().contains(2000L)){
                List<AttrResp> attrEntities = JSONObject.parseArray(bandIdsConfig, AttrResp.class);
                AttrResp attrResp = attrEntities.get(attrEntities.size() - 1);
                List<AttrResp> children = attrResp.getChildren();
                other = children.stream().map(r -> r.getId()).collect(Collectors.toList());
            }
            other.addAll(context.getBandIds());
            queryBuilder.must(QueryBuilders.termsQuery("m_band_id", other));
        }

        if (CollectionUtils.isNotEmpty(context.getSeasonGoods())){
            queryBuilder.must(QueryBuilders.termsQuery("good_season.keyword", context.getSeasonGoods()));
        }

        if (StringUtils.isNotBlank(context.getBrandName())) {
            queryBuilder.must(QueryBuilders.termQuery("brand", context.getBrandName()));
        }

        if (CollectionUtils.isNotEmpty(context.getBrandNames())) {
            queryBuilder.must(QueryBuilders.termsQuery("brand", context.getBrandNames()));
        }

        if (context.getIsZhuiDan() != null) {
            queryBuilder.must(QueryBuilders.termQuery("is_zhuidan", context.getIsZhuiDan()));
        }

        if (Objects.equals(context.getHasFab(), 1)) {
            queryBuilder.must(QueryBuilders.termQuery("has_fab", 1));
        } else if (Objects.equals(context.getHasFab(), 0)) {
            queryBuilder.must(QueryBuilders.termQuery("has_fab", 0));
        }

        if (Objects.equals(context.getHasReport(), 1)) {
            queryBuilder.must(QueryBuilders.termQuery("report_tag", 1));
        } else if (Objects.equals(context.getHasReport(), 0)) {
            queryBuilder.must(QueryBuilders.termQuery("report_tag", 0));
        }

        if (StringUtils.isNotBlank(context.getGys())) {
            queryBuilder.must(QueryBuilders.termQuery("gys", context.getGys()));
        }

        if (StringUtils.isNotBlank(context.getDesigner())) {
            queryBuilder.must(QueryBuilders.termQuery("designer", context.getDesigner()));
        }

        if (context.getIsPattern() != null) {
            queryBuilder.must(QueryBuilders.termQuery("is_pattern", context.getIsPattern()));
        }

        // 增加筛选项目
        if (StringUtils.isNotEmpty(context.getSearchParam()) && context.getSearchType() != null && context.getSearchType() != 0) {
            String field = ProductCenterSearchCenterEnum.getField(context.getSearchType());
            queryBuilder.must(QueryBuilders.wildcardQuery(field, "*" + context.getSearchParam() + "*"));
        }
    }

    @Override
    public void changeStoreGoodsByListenerStorage(long storeId, long productId) {
        logger.info("自动刷新并添加门店下的款号 storeId = {}, productId = {}", storeId, productId);
//        changeStoreGoodsByListenerStorage(storeId, productId, null);
    }
    @Override
    public List<GoodSpuResp.Sku> findSkuListByProductId(String productId) {
        GetRequest request = new GetRequest(goodsIndex, productId);
        List<GoodSpuResp.Sku> skus = new ArrayList<>();
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                GoodSpuResp entity = GoodSpuResp.fromJson(response.getSourceAsString(), GoodSpuResp.class);
                entity.buildSku();
                skus = entity.getSkus();
            }
            return skus;
        } catch (IOException e) {
            logger.error("查询商品异常e = {}", e.getMessage());
        }
        return skus;
    }


    @Override
    public GoodSpuResp findGoodByProductId(String productId) {
        GetRequest request = new GetRequest(goodsIndex, productId);
        GoodSpuResp entity =null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                entity = GoodSpuResp.fromJson(response.getSourceAsString(), GoodSpuResp.class);
                entity.buildSku();
            }
            return entity;
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("查询商品异常e = {}", e.getMessage());
        }
        return entity;
    }

    @Override
    public List<ProductSkcResp> findGoodsSkcDetailByIds(List<String> ids) {
        SearchRequest request = new SearchRequest();
        request.indices(PRODUCT_SKC_INDEX);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        queryBuilder.must(QueryBuilders.termsQuery("_id", ids));
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(ids.size());
        request.source(sourceBuilder);

        List<ProductSkcResp> resps = new ArrayList<>();
        try {
            logger.info("findGoodsSkcDetailByIds request = {}", request.source().toString());
            SearchResponse search = esUtil.search(request);
            if (search.getHits().getTotalHits().value == 0){
                return Collections.emptyList();
            }
            SearchHit[] hits = search.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductSkcResp entity = ProductSkcResp.fromJson(hits[i].getSourceAsString(), ProductSkcResp.class);
                if (CollectionUtils.isNotEmpty(entity.getLabels())){
                    entity.setLabels(entity.getLabels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                    entity.setLabel_levels(entity.getLabel_levels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                }
                entity.setId(hits[i].getId());
                resps.add(entity);
            }
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("查询商品异常e = {}", e.getMessage());
        }
        return resps;
    }

    @Override
    public GoodSpuEntity findGoodByProductCode(String productCode) {
        SearchRequest request = new SearchRequest();
        request.indices(goodsIndex);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        queryBuilder.must(QueryBuilders.termQuery("name", productCode));
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(20);
        request.source(sourceBuilder);
        GoodSpuEntity entity = null;
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return entity;
            }
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                entity = GoodSpuEntity.fromJson(hits[i].getSourceAsString(), GoodSpuEntity.class);
                entity.buildSku();
            }
        } catch (IOException e) {
            logger.error("查询商品异常e = {}", e.getMessage());
            return entity;
        }
        return entity;
    }

    @Override
    public List<BoxMProduct> findByNoList(List<String> skuList) {
        return iBoxMProductRepository.findByNoList(skuList);
    }

    @Override
    public List<ProductApiEntity> getProductApiList(Page page, ProductApiContext context) {
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        iBoxMProductRepository.getProductApiList(context);
        PageInfo<ProductApiEntity> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    @Override
    public List<BoxMProduct> findByCondition(BoxMProduct record) {
        return iBoxMProductRepository.findByCondition(record);
    }

    @Override
    public List<ProductSkcResp> getEsDataByProductIdAndColorNo(String productId, String colorNo) {
        SearchRequest request = new SearchRequest();
        request.indices(storeProductSkcIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("m_product_id",productId));
        queryBuilder.must(QueryBuilders.termQuery("colorno",colorNo));
        sourceBuilder.fetchSource(new String[]{"name","qty","value","price","id", "sales_num","colorno", "color_name",
                "store_id","imgurl","cover_imgs","detail_imgs","m_product_id","brand", "skus",
                "small_season_id","small_season","name_text","skc_code","skc_code1"}, new String[]{});
        sourceBuilder.query(queryBuilder);
        request.source(sourceBuilder);
        List<ProductSkcResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductSkcResp entity = ProductSkcResp.fromJson(hits[i].getSourceAsString(), ProductSkcResp.class);
                entity.buildSku();
                entity.setId(String.valueOf(entity.getM_product_id()));
                entity.setPrimaryKey(hits[i].getId());
                entities.add(entity);
            }
        } catch (IOException e) {
            logger.error("getEsDataBySkcCode   e  = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }



    public List<GoodSpuEntity> getEsDataByProductIdAndColorNo(List<String> productIds, List<String> colorNos) {

        //l
        SearchRequest request = new SearchRequest();
        request.indices(goodsIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("_id",productIds));
        sourceBuilder.from(0);
        sourceBuilder.size(1000);
        sourceBuilder.query(queryBuilder);
        request.source(sourceBuilder);
        List<GoodSpuEntity> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                GoodSpuEntity entity = GoodSpuEntity.fromJson(hits[i].getSourceAsString(), GoodSpuEntity.class);
                entity.buildSku();
                entities.add(entity);
            }
        } catch (IOException e) {
            logger.error("getEsDataBySkcCode   e  = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }


    @Override
    public List<BoxMProduct> batchGetProductByNames(List<String> productCodes) {
        // 查询数据
        List<BoxMProduct> list = iBoxMProductRepository.selectListByNames(productCodes);
        return list;
    }

    @Override
    public List<BoxMProduct> batchGetProductByGbCode(List<String> gbCode) {
        return iBoxMProductRepository.selectListByGbCodes(gbCode);
    }

    @Override
    public List<SimilarProductResp> searchSimilarGoods(SimilarProductReq similarProductReq) {
        SearchRequest request = new SearchRequest();
        request.indices(similarProductIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("source_name", similarProductReq.getSpu()));
        sourceBuilder.fetchSource(new String[]{"rec_name"}, new String[]{});
        sourceBuilder.query(queryBuilder);
        request.source(sourceBuilder);
        List<SimilarProductResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                SimilarProductResp entity = ProductSkcResp.fromJson(hits[i].getSourceAsString(), SimilarProductResp.class);
                entities.add(entity);
            }
        } catch (IOException e) {
            logger.error("getEsDataBySkcCode   e  = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }

    @Override
    public HashMap<Long, ProductAgentStockResp> searchSkuAgentStorageByIds(ProductAgentStockReq requestData) {
        // 查询经销商门店
        List<Long> storeIds = stockRepository.getAgentStoreByStoreId(requestData.getStoreId().get(0));
        if (org.apache.commons.collections.CollectionUtils.isEmpty(storeIds)) {
            throw new RuntimeException("未找到任何门店信息");
        }
        // 查询库存信息
        return stockRepository.getStorages(requestData.getSkuIds(), storeIds)
                .stream().collect(HashMap::new, (k, v) -> k.put(v.getSkuId(), v), HashMap::putAll);
    }

    @Override
    public List<SameSpuProductResp> searchSameSpuProduct(SameSpuProductReq req) {
        // 查询同款
        List<SameSpuProductResp> firstRets = new ArrayList<>();
        List<SameSpuProductResp> rets = new ArrayList<>();
        List<SameSpuProductResp> sameSpuProductEntities = stockRepository.getSameSpuProduct(req.getSpu());
        // 判断是否在同店
        if (CollectionUtils.isEmpty(sameSpuProductEntities)) {
            return sameSpuProductEntities;
        }
        List<SpuStockEntity> spuList = stockRepository.selectBySpuAndStoreIds(
                sameSpuProductEntities.stream().map(SameSpuProductResp::getProductId).collect(Collectors.toList()), req.getStoreIds());
        if (CollectionUtils.isEmpty(spuList)) {
            return Collections.emptyList();
        }
        HashMap<Long, SameSpuProductResp> map = sameSpuProductEntities.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getProductId(), v), HashMap::putAll);
        // pos+ 0库存也展示 去除库存为0不展示逻辑 .filter(v -> v.getQty() > 0)
        spuList.stream().forEach(v -> {
            Long productId = v.getProductId();
            SameSpuProductResp entity = null;
            if (productId != null) {
                entity = map.get(productId);
            }
            if (entity != null) {
                SameSpuProductResp product = new SameSpuProductResp();
                BeanUtils.copyProperties(entity, product);
                product.setStoreQty(v.getQty());
                firstRets.add(product);
            }
        });
        // 判断是否要查EB库存
        if (!req.getIsEB() || CollectionUtils.isEmpty(firstRets)) {
            return firstRets;
        }
        List<SpuStockEntity> ebList = stockRepository.selectEbStorageBySpuAndStoreIds(
                sameSpuProductEntities.stream().map(SameSpuProductResp::getProductId).collect(Collectors.toList()), req.getStoreIds());
        ebList.forEach(v -> {
            Long key = v.getProductId();
            SameSpuProductResp entity = firstRets.stream().filter(x -> Objects.equals(key, x.getProductId())).findFirst().orElse(null);
            if (entity != null) {
                SameSpuProductResp product = new SameSpuProductResp();
                BeanUtils.copyProperties(entity, product);
                product.setEbQty(v.getQty());
                rets.add(product);
            }
        });
        return rets;
    }

    @Override
    public void changeStoreGoodsByListenerStorage(long storeId, long productId, Long faStoreId) {
        // 导购
        List<SpuStockEntity> spuStock = stockRepository.getSpuStorages(Arrays.asList(productId), Arrays.asList(storeId));
        // 查询内淘和微商城库存
        List<EbAndWscSpuStockEntity> ebAndWscSpuStockEntities = stockRepository.getEbAndWscSpuStorages(Arrays.asList(productId), Arrays.asList(storeId));
        String key = storeId + "-" + productId;
        //更新门店SPU库存标识 ID唯一键位product_id+store_id
        String storeGoodIndex = allocationIndex(storeId);
        saveOrUpdateByStoreEs(UpdateStoreEsContext.build(storeId, productId, spuStock, key, ebAndWscSpuStockEntities, storeGoodIndex));
        // 搭配师
        //判断是否为BOX或LJ仓ID&jnbp唯一ID为款号+色号+门店标识
        if (stockRepository.containsBLJnbypStore(Long.valueOf(storeId))){
            // 根据skusId 查询数据 fa-   1 有库存   0 无库存
            FaStorage storage = faStoreId == null ? null : stockRepository.getStorageById(faStoreId);
            if (storeId == 417608L){//box仓
                long qty = stockRepository.getStorage(storage.getmProductaliasId(), Arrays.asList(417608L));
                updateStoreSkcStorage(storeId, productId,qty > 0 ? (int) qty : 0,storage.getmProductaliasId(), storeGoodIndex);
            }
            long qty = stockRepository.getStorage(storage.getmProductaliasId(), Arrays.asList(417608L,31066L));
            updateLjBoxSkcStorage(productId,qty > 0 ? 1 : 0,storage.getmProductaliasId(), storeGoodIndex);

            //jnbyp更新
            updateJnbypLjBoxSkcStorage(productId, storage.getmProductaliasId(), storeGoodIndex);
        }
    }

    private String allocationIndex(long storeId) {
        // 根据门店判断index的查询
        if (Objects.equals(storeId, "")) {
            throw new RuntimeException("门店信息不能为空");
        }
        CStore cStore = Preconditions.checkNotNull(cStoreMapper.selectById(storeId),"未找到当前门店");

        Long yuShu = null;
        if (cStore.getCUnionstoreId() == null) {
            yuShu = cStore.getId() % storeSplitGoodsIndexMode;
        } else {
            yuShu = cStore.getCUnionstoreId() % storeSplitGoodsIndexMode;
        }
        if (yuShu == null) {
            throw new RuntimeException("未计算到余数");
        }

        return StringUtils.replace(storeSplitGoodsIndex, "mode", Objects.toString(yuShu));
    }

    @Override
    public GoodSpuDetailEntity searchProductDetail(String productId) {
        if (StringUtils.isBlank(productId)) {
            return null;
        }
        GetRequest request = new GetRequest(goodsIndex, productId);
        GoodSpuDetailEntity entity =null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                entity = GoodSpuDetailEntity.fromJson(response.getSourceAsString(), GoodSpuDetailEntity.class);
                entity.buildSku();
                // 查询销售话术 修改成取商品中台fab
                entity.setSalesTalk(entity.getFab());
                if (CollectionUtils.isNotEmpty(entity.getLabels())){
                    entity.setLabels(entity.getLabels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                    entity.setLabel_levels(entity.getLabel_levels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                }
            }
            // 查询销售话术 修改成取商品中台fab
           /* MProductExtend mProductExtend = mProductExtendMapper.selectInfoByProductId(productId);
            if (mProductExtend != null) {
                entity.setSalesTalk(mProductExtend.getSalesTalk());
            }*/
            // 查询商品是否为新品和上市时间 0-4周为新品
            entity.setIsNew(Objects.equals(entity.getLifestyle_tag(), newTag) ? 1 : 0);
            MProductList mProductList = mProductListMapper.selectByProductId(productId);
            if (mProductList != null) {
                entity.setMarketTime(mProductList.getDateList());
            }


            // 判断当前是否有最新fab 覆盖
            FabInfo fabInfo = fabInfoMapper.selectByProductId(productId);
            if (fabInfo != null && StringUtils.isNotBlank(fabInfo.getFab())) {
                entity.setFab(fabInfo.getFab());
                entity.setSalesTalk(fabInfo.getFab());
            }

            return entity;
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("查询商品异常e = {}", e.getMessage());
        }
        return entity;
    }

    private final static String BRAND = "品牌";


    @Override
    public JSONArray getSizeNos(CommonRequest request) {
        List<SizeNoParamResp> data = JSONObject.parseArray(productSizeNos, SizeNoParamResp.class);

        if (StringUtils.isNotBlank(request.getComponent())) {
            List<String> brands = commonDataRuleService.getPeopleBrandDataRule(request.getComponent());
            if (CollectionUtils.isEmpty(brands)) {
                return JSONArray.parseArray(JSON.toJSONString(data));
            }
            List<SizeNoParamResp> filterBrand = data.stream().filter(v -> Objects.equals(v.getName(), BRAND))
                    .map(SizeNoParamResp::getChildren).findFirst().orElse(Collections.emptyList())
                    .stream().filter(v -> brands.contains(v.getCode())).collect(Collectors.toList());

            data.stream().filter(v -> Objects.equals(v.getName(), BRAND)).findFirst().orElse(null).setChildren(filterBrand);
            return JSONArray.parseArray(JSON.toJSONString(data));
        }

        return JSONArray.parseArray(JSON.toJSONString(data));
    }

    @Override
    public List<SearchWeiMoGoodInfoResp> selectWeiMoGoodIdByNames(List<SearchWeiMoGoodInfoReq> req) {
        List<Long> weIds = req.stream().map(SearchWeiMoGoodInfoReq::getWeId).collect(Collectors.toList());
        List<String> names = req.stream().map(SearchWeiMoGoodInfoReq::getName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(weIds) || CollectionUtils.isEmpty(names)) {
            throw new RuntimeException("品牌 or 款号信息不能为空");
        }

        // 将款号转为原款号 暂时先做九位处理
        List<String> dealNames = new ArrayList<>();
        HashMap map = new HashMap<>();
        names.forEach(v -> {
            String s = StringUtils.substring(v, 0, 9);
            dealNames.add(s);
            map.put(s, v);});


        QueryWrapper<JicProductMallRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("WEID", weIds);
        queryWrapper.in("PRODUCT_NO", dealNames);
        List<JicProductMallRelation> relations = jicProductMallRelationMapper.selectList(queryWrapper);
        List<SearchWeiMoGoodInfoResp> rets = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(relations)) {
            relations.forEach(v -> {
                SearchWeiMoGoodInfoResp infoResp = new SearchWeiMoGoodInfoResp();
                if (map == null || StringUtils.isBlank((String) map.get(v.getProductNo()))) {
                    return;
                }
                infoResp.setName((String) map.get(v.getProductNo()));
                infoResp.setWeiMoGoodId(v.getWeimoGoodid());
                infoResp.setWeId(v.getWeid());
                rets.add(infoResp);
            });
        }
        return rets.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<ExchangeProductIdByWeiMoResp> exchangeProductIdByWeiMo(ExchangeProductIdByWeiMoReq requestData) {
        return jicMallOrgMapper.selectBojunInfoByWeimo(requestData.getVid(), requestData.getWeiMoSkuIds());
    }

    @Override
    public List<SearchWeiMoGoodInfoResp> selectWeiMoInfoByNames(List<SearchWeiMoGoodInfoReq> requestData) {
        logger.info("============exchangeWeiMoProductIdAndSkuId 转换成微盟productId和skuId 入参：{}", JSONObject.toJSONString(requestData));
        List<String> names = requestData.stream().map(SearchWeiMoGoodInfoReq::getName).collect(Collectors.toList());
        List<Long> weIds = requestData.stream().map(SearchWeiMoGoodInfoReq::getWeId).collect(Collectors.toList());
        List<Long> skuIds = requestData.stream().map(SearchWeiMoGoodInfoReq::getSkuId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(weIds) || CollectionUtils.isEmpty(names)) {
            throw new RuntimeException("品牌 or 款号 or 规格id 信息不能为空");
        }

        QueryWrapper<JicMProductAttr> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.in("ID", skuIds);
        List<JicMProductAttr> jicMProductAttrs = jicMProductAttrMapper.selectList(queryWrapper1);
        logger.info("============exchangeWeiMoProductIdAndSkuId 查询attr 返回：{}", JSONObject.toJSONString(jicMProductAttrs));

        if (CollectionUtils.isEmpty(jicMProductAttrs)) {
            return Collections.emptyList();
        }
        List<String> productCodes = jicMProductAttrs.stream().map(JicMProductAttr::getProductCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodes)) {
            return Collections.emptyList();
        }

        QueryWrapper<JicProductMallRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("WEID", weIds);
        queryWrapper.in("PRODUCT_NO", names);
        queryWrapper.in("PRODUCT_CODE", productCodes);
        List<JicProductMallRelation> relations = jicProductMallRelationMapper.selectList(queryWrapper);
        logger.info("============exchangeWeiMoProductIdAndSkuId 查询mallRelation 返回：{}", JSONObject.toJSONString(relations));

        if (CollectionUtils.isEmpty(relations)) {
            return Collections.emptyList();
        }

        List<SearchWeiMoGoodInfoResp> rets = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(relations)) {
            relations.forEach(v -> {
                SearchWeiMoGoodInfoResp infoResp = new SearchWeiMoGoodInfoResp();
                infoResp.setName(v.getProductNo());
                infoResp.setWeiMoGoodId(v.getWeimoGoodid());
                infoResp.setWeId(v.getWeid());
                infoResp.setWeiMoSkuId(v.getWeimoSkuid());
                rets.add(infoResp);
            });
        }
        logger.info("============exchangeWeiMoProductIdAndSkuId 最终返回：{}", JSONObject.toJSONString(rets));
        return rets;
    }

    @Override
    public List<ExchangeByProductIdResp> getWeiMoGoodIdAndBJSkuId(ExchangeByProductIdReq requestData) {
        return jicProductMallRelationMapper.selectWeimoGoodIdAndBJSkuId(requestData.getWeId(), requestData.getProductId());
    }

    @Override
    public SkuProductInfoResp getProductInfoBySku(CommonRequest<String> request) {
        if (StringUtils.isBlank(request.getRequestData())) {
            throw new RuntimeException("该sku信息不能空！");
        }

        SearchRequest searchRequest = new SearchRequest();
        searchRequest.indices(goodsIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.wildcardQuery("name", StringUtils.substring(request.getRequestData(), 0, 9) + "*"));
        sourceBuilder.query(queryBuilder);
        searchRequest.source(sourceBuilder);
        List<GoodSpuResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(searchRequest);
            if (response.getHits().getTotalHits().value == 0){
                return null;
            }
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                GoodSpuResp entity = GoodSpuResp.fromJson(hits[i].getSourceAsString(), GoodSpuResp.class);
                entity.buildSku();
                entities.add(entity);
            }
        } catch (IOException e) {
            logger.error("getProductInfoBySku   e  = {}", e.getMessage());
            return null;
        }

        // 判断匹配的条数
        return SkuProductInfoResp.build(entities, request.getRequestData());
    }

    @Override
    public Boolean judgeIsZhuiDan(String request) {
        if (StringUtils.isBlank(request)) {
            throw new RuntimeException("传入的款号不能为空");
        }
        // 判断当前商品的m_dim13_id的数据 若在m_dim中是'拆分款', '快反追单','N追单','N+1追单' 则是追单款
        QueryWrapper<MProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("NAME", request);
        queryWrapper.eq("ISACTIVE", "Y");
        List<MProduct> mProducts = mProductMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(mProducts)) {
            return false;
        }

        Long mDim13Id = mProducts.get(0).getMDim13Id();
        if (mDim13Id == null) {
            return false;
        }

        // 查询当前id 对应的attribname数据
        Mdim mdim = mdimMapper.selectById(mDim13Id);
        if (mdim == null) {
            return false;
        }

        if (Objects.equals(mdim.getAttribname(), "拆分款") ||
                Objects.equals(mdim.getAttribname(), "快反追单") ||
                Objects.equals(mdim.getAttribname(), "N追单") ||
                Objects.equals(mdim.getAttribname(), "N+1追单")) {
            return true;
        }

        return false;
    }

    @Override
    public GoodSpuDetailEntity getProductDetailByZhuiDan(ZhuiDanProductInfo requestData) {
        if (StringUtils.isBlank(requestData.getName()) || StringUtils.isBlank(requestData.getColorNo())) {
            throw new RuntimeException("传入的款号和规格id不能为空");
        }
        // 获取当前商品的原款商品
        Long listProduct = getOrigProductId(requestData.getName());
        if (listProduct == null) {
            throw new RuntimeException("当前商品的原单不存在");
        }

        GoodSpuDetailEntity entity = searchProductDetail(Objects.toString(listProduct));
        if (entity != null && CollectionUtils.isNotEmpty(entity.getSkus())) {
            List<GoodSpuDetailEntity.Sku> skus = entity.getSkus();
            if (skus != null){
                entity.setSkus(skus.stream().filter(item -> Objects.equals(item.getColorno(), requestData.getColorNo())).collect(Collectors.toList()));
            }
        }
        return entity;
    }

    private Long getOrigProductId(String requestData) {
        QueryWrapper<MProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("NAME", requestData);
        queryWrapper.eq("ISACTIVE", "Y");
        List<MProduct> mProducts = mProductMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(mProducts)) {
            return null;
        }

        // 查看当前商品的原款数据
        Long listProduct = mProducts.get(0).getListProduct();
        if (listProduct == null) {
            // home款特殊处理 需要去其他表查
            List<BoxMProduct> boxMProducts = boxMProductMapper.findByName(requestData);
            if (CollectionUtils.isEmpty(boxMProducts)) {
                return null;
            }
            listProduct = mProductaliasAliasAgainMapper.selectOrigProductIdByZhuiDanAliasId(boxMProducts.get(0).getCodeid());
            if (listProduct == null) {
                return null;
            }
        }
        return listProduct;
    }

    @Override
    public Boolean judgeIsExistZhuiDan(String request) {
        if (StringUtils.isBlank(request)) {
            throw new RuntimeException("传入的产品款号不能为空");
        }
        Long origProductId = getOrigProductId(request);
        return origProductId == null ? false : true;
    }

    @Override
    public List<String> queryGoodsIds(QueryGoodsReq goodsReq) {
        logger.info("queryGoodsIds查询过滤参数 params = {}", goodsReq.toString());
        SearchRequest request = new SearchRequest();
        request.indices(goodsIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        buildQueryGoodsIndex(queryBuilder, goodsReq);
        List<String> includeSource = Stream.of("id" ).collect(Collectors.toList());
        sourceBuilder.fetchSource(includeSource.toArray(new String[]{}), new String[]{});
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(10000);
        sourceBuilder.sort("id", SortOrder.DESC);
        request.source(sourceBuilder);
        List<String> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                String entity = hits[i].getId();
                entities.add(entity);
            }
        } catch (IOException e) {
            logger.error("查询商品异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }

    @Override
    public List<BJProductIdByWMGoodIdResp> queryBJProductIdByWMGoodId(List<Long> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            throw new RuntimeException("入参不能为空");
        }
        List<BojunProductInfo> productInfos = jicProductMallRelationMapper.exchangeBojunIdAndName(requestData);
        return buildBJProductIdByWMGoodIdResp(productInfos);
    }

    @Override
    public List<SkuInfoByProductCodeResp> querySkuIdByBjProductCode(List<String> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            throw new RuntimeException("当前参数不能为空");
        }
        List<org.springcenter.product.modules.model.bojun.BoxMProduct> boxMProducts = bBoxMProductMapper.selectListByNos(requestData);
        HashMap<String, org.springcenter.product.modules.model.bojun.BoxMProduct> map = boxMProducts.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getNo(), v), HashMap::putAll);
        List<SkuInfoByProductCodeResp> rets = new ArrayList<>();
        requestData.forEach(v -> {
            SkuInfoByProductCodeResp codeResp = new SkuInfoByProductCodeResp();
            if (MapUtils.isEmpty(map) || map.get(v) == null) {
                codeResp.setProductCode(v);
            } else {
                codeResp.setProductCode(v);
                codeResp.setSkuId(map.get(v).getCodeid());
                codeResp.setSizeNo(map.get(v).getSizeno());
                codeResp.setColorNo(map.get(v).getColorno());
            }
            rets.add(codeResp);
        });
        return rets;
    }

    @Value("${product.life.style}")
    private String productLifeStyle;

    @Override
    public List<FashionerProductIsCanSellResp> queryFashionerIsCanSell(List<Long> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            return Collections.emptyList();
        }
        List<FashionerProductIsCanSellResp> reps = new ArrayList<>();
        QueryWrapper<MProduct> wrapper = new QueryWrapper<>();
        wrapper.in("ID", requestData);
        List<MProduct> mProducts = mProductMapper.selectList(wrapper);

        List<Long> inAllows = Arrays.stream(StringUtils.split(productLifeStyle, ";"))
                .map(v -> Long.valueOf(v)).collect(Collectors.toList());
        HashMap<Long, Long> map = mProducts.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getId(), v.getMDim63Id()), HashMap::putAll);
        if (MapUtils.isEmpty(map) || CollectionUtils.isEmpty(mProducts)) {
            requestData.forEach(v -> {
                FashionerProductIsCanSellResp sellResp = new FashionerProductIsCanSellResp();
                sellResp.setProductId(v);
                sellResp.setIsCanSell(0);
                reps.add(sellResp);
            });
            return reps;
        }

        requestData.forEach(v -> {
            Long aLong = map.get(v);
            FashionerProductIsCanSellResp sellResp = new FashionerProductIsCanSellResp();
            sellResp.setProductId(v);
            sellResp.setIsCanSell(aLong == null || !inAllows.contains(aLong) ? 0 : 1);
            reps.add(sellResp);
        });
        return reps;
    }

    @Override
    public List<ProductInfoByNo> querySpuNoPrice(List<String> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            return Collections.emptyList();
        }

        return mProductMapper.selectInfoByNames(requestData);
    }

   /* @Autowired
    private ProductDetailInfoService productDetailInfoService;*/

    @Value("${product.sales.info.index}")
    private String productSalesInfoIndex;

    @Value(value = "${fabric.icon.img}")
    private String fabricIcon;

    @Value(value = "${fabric.kz.dw}")
    private String dw;

    @Value(value = "${params.order}")
    private String paramsOrder;

    @Override
    public ProductSalesInfoResp searchProductSalesInfo(String requestData) {
        if (StringUtils.isBlank(requestData)) {
            throw new RuntimeException("传入的商品id不能为空");
        }
        // productDetailInfoService.
        ProductParamSalesInfoEsResp resp = searchProductSalesInfo(requestData,
                productSalesInfoIndex);

        ProductSalesInfoResp infoResp = new ProductSalesInfoResp();
        if (resp != null) {
            // 面料信息
            infoResp.setFabricPromotionMaterials(resp.getSc_popularize());
            infoResp.setExcipientPromotionMaterials(resp.getFl_sc_popularize());

            // 标签信息
            if (StringUtils.isNotBlank(resp.getSjcf())) {
                List<ProductSalesInfoResp.IconDataInfo> fabricIconInfoList = new ArrayList<>();
                ProductSalesInfoResp.IconDataInfo fabricIconInfo = new ProductSalesInfoResp.IconDataInfo();
                fabricIconInfo.setName(resp.getSjcf());
                fabricIconInfo.setIcon(fabricIcon);
                fabricIconInfoList.add(fabricIconInfo);
                infoResp.setFabricIconInfo(fabricIconInfoList);
            }


            if (CollectionUtils.isNotEmpty(resp.getFabricMaterialDataList())) {
                List<ProductSalesInfoResp.IconDataInfo> materialIconInfoList = new ArrayList<>();
                resp.getFabricMaterialDataList().forEach(v -> {
                    ProductSalesInfoResp.IconDataInfo materialIconInfo = new ProductSalesInfoResp.IconDataInfo();
                    materialIconInfo.setName(v.getLabel_name());
                    materialIconInfo.setIcon(v.getMc_pic());
                    materialIconInfoList.add(materialIconInfo);
                });
                infoResp.setMaterialIconInfo(materialIconInfoList);
            }

            if (CollectionUtils.isNotEmpty(resp.getFabricServerFunctionDataList())) {
                List<ProductSalesInfoResp.IconDataInfo> serveFunctionInfoList = new ArrayList<>();
                resp.getFabricServerFunctionDataList().forEach(v -> {
                    ProductSalesInfoResp.IconDataInfo serveFunctionInfo = new ProductSalesInfoResp.IconDataInfo();
                    serveFunctionInfo.setName(v.getLabel_name());
                    serveFunctionInfo.setIcon(v.getMc_pic());
                    serveFunctionInfoList.add(serveFunctionInfo);
                });
                infoResp.setServeFunctionInfo(serveFunctionInfoList);
            }

            if (CollectionUtils.isNotEmpty(resp.getWashInfoList())) {
                List<ProductSalesInfoResp.WashInfo> washInfos = new ArrayList<>();
                resp.getWashInfoList().forEach(v -> {
                    ProductSalesInfoResp.WashInfo washInfo = new ProductSalesInfoResp.WashInfo();
                    washInfo.setWashName(v.getWashName());
                    washInfo.setWashNo(v.getWashNo());
                    washInfos.add(washInfo);
                });
                infoResp.setWashingInfo(washInfos);
            }

            // 克重信息
            String yrfKz = "";
            if (CollectionUtils.isNotEmpty(resp.getFabricKzDataList())) {
                List<String> kzList = resp.getFabricKzDataList().stream()
                        .map(ProductParamSalesInfoEsResp.FabricKzData::getFabric_kz).distinct().collect(Collectors.toList());
                if (kzList.size() == 1) {
                    yrfKz = StringUtils.isBlank(kzList.get(0)) ? "" : kzList.get(0) + dw;
                } else {
                    List<String> kzName = new ArrayList<>();
                    resp.getFabricKzDataList().forEach(v -> {
                        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(v.getFabric_kz())) {
                            return;
                        }
                        String kz = v.getColor_name() + ":" + v.getFabric_kz() + dw;
                        kzName.add(kz);
                    });
                    yrfKz = kzName.stream().collect(Collectors.joining(";"));
                }
            }

            // 品牌、年份、品类、图案、羽绒填充进口、克重（如果不同skc不同，前面拼skc名称）、布面效果、进口产地、原料产地、填充物、填充量
            List<ProductSalesInfoResp.DataInfo> salesData = new ArrayList<>();
            String finalYrfKz = yrfKz;
            Arrays.stream(paramsOrder.split(",")).forEach(v -> {
                String[] split = StringUtils.split(v, ":");
                ProductSalesInfoResp.DataInfo data = new ProductSalesInfoResp.DataInfo();
                data.setKey(split[0]);
                if (Objects.equals(split[0], "克重")) {
                    if (StringUtils.isNotBlank(finalYrfKz)) {
                        data.setValue(finalYrfKz);
                        salesData.add(data);
                        return;
                    } else {
                        return;
                    }
                }

                // ReflectUtil.
                String fieldValue = getFieldValue(Objects.toString(split[1]), resp);
                if (StringUtils.isNotBlank(fieldValue)) {
                    data.setValue(fieldValue);
                    salesData.add(data);
                }
            });
            infoResp.setParams(salesData);
            infoResp.setLabelsList(resp.getLabelsList());
            infoResp.setLabelLevels(resp.getLabelLevels());
        }

        return infoResp;
    }

    @Override
    public List<ZhuiDanSkuInfoResp> getSkuInfoByZdSkuId(List<Long> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            throw new RuntimeException("当前参数不能为空");
        }
        return mProductaliasAliasAgainMapper.getSkuInfoByZdSkuId(requestData);
    }

    @Override
    public List<ProductWscStatusInfoResp> getProductWscStatusInfo(ProductWscStatusInfoReq requestData) {
        List<JicProductMallInfo> mallInfos = jicProductMallInfoMapper.selectByProductIdAndWeId(requestData.getProductIds(),
                Long.valueOf(requestData.getWeId()));
        List<JicProductPutaway> putaways = jicProductPutwayMapper.selectByProductIdAndWeidAndVid(requestData.getProductIds(), requestData.getWeId(),
                requestData.getVid());
        HashMap<Long, Integer> sellMap = mallInfos.stream().collect(HashMap::new, (k, v) -> k.put(v.getProductId(), v.getMallIsCanSell()), HashMap::putAll);
        HashMap<Long, Integer> puwayMap = putaways.stream().collect(HashMap::new, (k, v) -> k.put(v.getProductId(), v.getPutway()), HashMap::putAll);
        List<ProductWscStatusInfoResp> rets = new ArrayList<>();
        requestData.getProductIds().forEach(v -> {
            ProductWscStatusInfoResp infoResp = new ProductWscStatusInfoResp();
            infoResp.setProductId(v);
            if (MapUtils.isNotEmpty(puwayMap) && puwayMap.get(v) != null) {
                infoResp.setIsInfactPutway(puwayMap.get(v) == 0 ? 1 : 0);
            } else {
                infoResp.setIsInfactPutway(0);
            }
            if (sellMap.get(v) != null) {
                infoResp.setIsCanSell(sellMap.get(v));
            } else {
                infoResp.setIsCanSell(0);
            }

            if (Objects.equals(infoResp.getIsCanSell(), 1) && Objects.equals(infoResp.getIsInfactPutway(), 1)) {
                infoResp.setIsPutway(1);
            } else {
                infoResp.setIsPutway(0);
            }

            rets.add(infoResp);
        });
        return rets;
    }

    @Autowired
    private IProductStoreService productStoreService;

    @Override
    public List<GoodSpuResp> getSingleProductRecommendation(SingleProductRecommendationReq requestData) {
        if (CollectionUtils.isEmpty(requestData.getStoreList()) || requestData.getStoreList().get(0) == null || 
                StringUtils.isBlank(requestData.getStoreList().get(0).getStoreId())) {
            throw new RuntimeException("当品牌架构id为4时，品牌名称不能为空");
        }
        
        CStore cStore = Preconditions.checkNotNull(cStoreMapper.selectById(Long.valueOf(requestData.getStoreList().get(0).getStoreId())),
                "未找到当前门店");
        List<Long> aolaiList = Arrays.stream(productAolaiRadius.split(","))
                .map(v -> Long.valueOf(v)).collect(Collectors.toList());
        // 根据商品id查询大数据
        List<Integer> productIds = bigDataService.searchSingleRecommendation(BigDataSingleReconReq.build(requestData,
                cStore, tracer.currentSpan().context().traceIdString(), aolaiList));
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>();
        }
        List<String> productInfos = productIds.stream().map(v -> Objects.toString(v)).collect(Collectors.toList());

        // 去商品列表过滤门店-商品
        StoreGoodsReq req = new StoreGoodsReq();
        req.setStoreId(requestData.getStoreList().stream().map(SingleProductRecommendationReq.Store::getStoreId).collect(Collectors.toList()));
        req.setProductIds(productInfos);
        req.setQty(true);
        req.setMallQty(true);
        req.setEbQty(true);
        Page page1 = new Page();
        page1.setPageNo(1);
        page1.setPageSize(productInfos.size());
        List<StoreGoodSpuResp> goodSpuResps = productStoreService.searchGoodsByStore(req, page1);

        if (CollectionUtils.isEmpty(goodSpuResps)) {
            return new ArrayList<>();
        }

        List<String> pIds = goodSpuResps.stream().map(v -> Objects.toString(v.getM_product_id())).collect(Collectors.toList());
        QueryGoodsReq goodsReq = new QueryGoodsReq();
        goodsReq.setProductIds(pIds);
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(10);
        List<GoodSpuResp> resps = searchGoods(goodsReq, page);
        return resps;
    }

    public static String getFieldValue(String fieldName, ProductParamSalesInfoEsResp info) {
        try {
            Field field = info.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(info) == null ? "" : Objects.toString(field.get(info));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public ProductParamSalesInfoEsResp searchProductSalesInfo(String requestData, String index) {

        GetRequest request = new GetRequest(index, requestData);
        ProductParamSalesInfoEsResp resp = null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                resp = ProductParamSalesInfoEsResp.fromJson(response.getSourceAsString(), ProductParamSalesInfoEsResp.class);
                resp.buildFabricKzData();
                resp.buildMaterialData();
                resp.buildServeFunctionData();
                resp.buildLabels();
                resp.buildLabelLevels();
                resp.buildWashInfo();
            }
        } catch (IOException e) {
            e.printStackTrace();
            //log.error("查询商品详情的信息异常e = {}", e.getMessage());
        }
        return resp;
    }

    private List<BJProductIdByWMGoodIdResp> buildBJProductIdByWMGoodIdResp(List<BojunProductInfo> productInfos) {
        if (CollectionUtils.isEmpty(productInfos)) {
            return Collections.emptyList();
        }

        List<BJProductIdByWMGoodIdResp> rets = new ArrayList<>();
        productInfos.forEach(v -> {
            BJProductIdByWMGoodIdResp wmGoodIdResp = new BJProductIdByWMGoodIdResp();
            wmGoodIdResp.setProductId(v.getId());
            wmGoodIdResp.setWeiMoGoodId(v.getWmGoodId());
            rets.add(wmGoodIdResp);
        });
        return rets;
    }

    /**
     * 门店库存更新，不存在索引则新建，否则为更新
     */
    private void saveOrUpdateByStoreEs(UpdateStoreEsContext context){
        GetRequest request = new GetRequest(context.getStoreGoodIndex(), context.getKey());
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                //如果一致，则不更新
                StoreGoodSpuEntity entity = StoreGoodSpuEntity.fromJson(response.getSourceAsString(), StoreGoodSpuEntity.class);
                UpdateRequest update = new UpdateRequest(context.getStoreGoodIndex(), context.getKey()).retryOnConflict(2);
                Map<String, Object> objectMap = new HashMap<>();
                if (CollectionUtils.isEmpty(context.getSpuStock())) {
                    //有可能删除了，这里直接将其下架
                    objectMap.put("qty", 0);
                } else {
                    objectMap.put("qty", context.getSpuStock().get(0).getQty());
                }

                if (CollectionUtils.isEmpty(context.getEbAndWscSpuStockEntities())) {
                    objectMap.put("mall_qty", 0);
                    objectMap.put("eb_qty", 0);
                } else {
                    objectMap.put("mall_qty", context.getEbAndWscSpuStockEntities().get(0).getMallQty());
                    objectMap.put("eb_qty", context.getEbAndWscSpuStockEntities().get(0).getEbQty());
                }

                if (CollectionUtils.isNotEmpty(context.getSpuStock()) || CollectionUtils.isNotEmpty(context.getEbAndWscSpuStockEntities())){
                    update.doc(objectMap);
                    esUtil.update(update);
                }
            }else {
                //新建
                queryAndCreateStoreProductIndex(context);
            }
        } catch (IOException e) {
            logger.error("监听到门店数据库存变更,ES修改异常 storeId = {}, productId = {}, e = {}", context.getStoreId(), context.getProductId(), e);
        }
    }

    /**
     * 查询并创建门店商品
     * @param context 新增信息
     */
    private void queryAndCreateStoreProductIndex(UpdateStoreEsContext context){
        SearchRequest request = new SearchRequest();
        request.indices(context.getStoreGoodIndex());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        queryBuilder.must(QueryBuilders.termQuery("m_product_id", context.getProductId()));
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(2);
        sourceBuilder.sort("sales_num", SortOrder.DESC);
        request.source(sourceBuilder);
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return;
            }

            SearchHit[] hits = response.getHits().getHits();
            StoreGoodSpuEntity entity = StoreGoodSpuEntity.fromJson(hits[0].getSourceAsString(), StoreGoodSpuEntity.class);
            entity.setStore_product_id(context.getStoreId() + "-" + context.getProductId());
            entity.setC_store_id(context.getStoreId());
            if (CollectionUtils.isEmpty(context.getSpuStock())) {
                entity.setQty(0);
            } else {
                entity.setQty(context.getSpuStock().get(0).getQty());
            }

            if (CollectionUtils.isEmpty(context.getEbAndWscSpuStockEntities())) {
                entity.setMall_qty(0);
                entity.setEb_qty(0);
            } else {
                entity.setMall_qty(context.getEbAndWscSpuStockEntities().get(0).getMallQty());
                entity.setEb_qty(context.getEbAndWscSpuStockEntities().get(0).getEbQty());
            }

            IndexRequest indexRequest = new IndexRequest(context.getStoreGoodIndex());
            indexRequest.source(JSON.toJSONString(entity), XContentType.JSON);
            indexRequest.id(entity.getStore_product_id());
            esUtil.index(indexRequest);
        } catch (IOException e) {
            logger.error("门店商品新建索引异常 e = {}", e.getMessage(), e);
            return ;
        }
    }

    private void updateStoreSkcStorage(long storeId, long productId, Integer qty, Long skuId, String storeGoodsIndex) {
        List<SkcStockEntity> list = stockRepository.querySkcStorageList(productId, storeId);
        updateStorageForEs(list, String.valueOf(storeId), productId, qty, skuId, storeGoodsIndex);
    }

    /**
     * 通过商品ID和门店ID查询所有的SKC库存
     * @param list
     * @param storeId
     * @param productId
     * @param qty
     * @param skuId
     */
    private void updateStorageForEs(List<SkcStockEntity> list, String storeId, long productId, Integer qty, Long skuId,
                                    String index) {
        //按照SKC分组 如果条件不符合 查询不出所有的SKC对应的库存数据,应该需要循环所有ES存在的SKC商品
        Map<String, List<SkcStockEntity>> groupSkc = list.stream().collect(Collectors.groupingBy(d -> fetchGroupKey(d)));
        //取出修改时间最近的SKC进行更新
        List<SkcStockEntity> skcStockEntities = iteratorMigrate(groupSkc);
        skcStockEntities.forEach(item -> {
            try {
                // 查询数据 唯一ID款号+色号
                GetRequest request = new GetRequest(index, item.getName() + item.getColorno() + "-" + storeId);
                GetResponse response = esUtil.getIndex(request);
                if (!response.isExists()) {
                    return;
                }

                GoodsSkcResp entity = GoodsSkcResp.fromJson(response.getSourceAsString(), GoodsSkcResp.class);
                List<GoodsSkcResp.Sku> skus = entity.getSkus();
                List<Map<String,Object>> updateList = new ArrayList<>(skus.size());
                boolean isUpdate = false;
                boolean isUpdateQty = false;
                for (GoodsSkcResp.Sku sku : skus) {
                    boolean equals = sku.getId().equals(String.valueOf(skuId));
                    if(equals){
                        isUpdate = true;
                        int updateSkuQty = qty <= 0 ? 0 : 1;//安全库存校验
                        if (!String.valueOf(updateSkuQty).equals(sku.getQty())){
                            isUpdateQty = true;
                            sku.setQty(String.valueOf(updateSkuQty));
                        }
                    }
                    Map<String,Object> map = JSONObject.parseObject(JSONObject.toJSONString(sku), Map.class);
                    updateList.add(map);
                }

                if (entity.getQty() != item.getQty() || (isUpdate && isUpdateQty)){
                    UpdateRequest update = new UpdateRequest(index, item.getName() + item.getColorno() + "-" + storeId).retryOnConflict(2);
                    Map<String, Object> objectMap = new HashMap<>();
                    objectMap.put("qty", item.getQty());
                    // 更新 sku
                    if(isUpdate && isUpdateQty){
                        objectMap.put("skus", updateList);
                    }
                    update.doc(objectMap);
                    try {
                        esUtil.update(update);
                    } catch (IOException e) {
                        logger.error("监听到门店C数据库存变更,SKC修改异常 storeId = {}, productId = {}, e = {}", storeId, productId, e);
                    }
                }
            } catch (IOException e) {
                logger.error("监听到门店数据库存变更,SKUS修改异常 storeId = {}, productId = {}, e = {} , faStoreId = {}", storeId, productId, e,skuId);
            }

        });

    }
    private void updateLjBoxSkcStorage(long productId, Integer qty, Long skuId, String storeGoodIndex) {
        List<SkcStockEntity> list = stockRepository.queryLjBoxSkcStorage(productId);
        updateStorageForEs(list, "417608-31066", productId, qty, skuId, storeGoodIndex);
    }

    private void updateJnbypLjBoxSkcStorage(long productId, Long skuId, String index){
        List<SkcStockEntity> list = stockRepository.queryBLJnbypSkcStorageList(productId);
        List<ProductStockEntity> productStockEntities = stockRepository.getJNBYGatherStock(Arrays.asList(skuId));
        long qty = (Objects.isNull(productStockEntities) || productStockEntities.isEmpty()) ? 0 : productStockEntities.get(0).getQty();
        updateStorageForEs(list, "JNBYP", productId,Integer.valueOf(qty+""), skuId, index);
    }

    //组装多属性key
    private String fetchGroupKey(SkcStockEntity skc){
        return skc.getName() + skc.getColorno();
    }

    /**
     * 迭代组装，按照款色取出最近修改时间的库存,合并仓
     * @param groupSkc
     * @return
     */
    private List<SkcStockEntity> iteratorMigrate(Map<String, List<SkcStockEntity>> groupSkc){
        List<SkcStockEntity> skcList = new ArrayList<>();
        Iterator<Map.Entry<String, List<SkcStockEntity>>> it = groupSkc.entrySet().iterator();
        while (it.hasNext()){
            Map.Entry<String, List<SkcStockEntity>> entry = it.next();
            List<SkcStockEntity> entryValue = entry.getValue();
            SkcStockEntity entity = entryValue.stream().filter(item -> item.getQty() == 1l)
                    .findFirst().orElse(entryValue.get(0));
            skcList.add(entity);
        }
        return skcList;
    }


    /**
     * 判断字符串是否为中文
     *
     * @param input
     * @return
     */
    private static boolean isChinese(String input) {
        if(StringUtils.isBlank(input)){
            return false;
        }
        int length = input.length();
        for(int i = 0 ; i < length ; i++){
            String code = input.charAt(i)+"";
            boolean matches = code.matches("^[\u4e00-\u9fa5]+$");
            if(matches){
                return true;
            }
        }
        return false;
    }
}
