<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.SysProductCalcLabelMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.SysProductCalcLabel">
    <result column="ID"  property="id" />
    <result column="PRODUCT_ID"  property="productId" />
    <result column="LABEL_NAME"  property="labelName" />
    <result column="LABEL_CODE"  property="labelCode" />
    <result column="IS_DELETED"  property="isDeleted" />
    <result column="SKC_CODE"  property="skcCode" />
    <result column="CREATE_TIME"  property="createTime" />
    <result column="UPDATE_TIME"  property="updateTime" />
  </resultMap>

  <insert id="insertBatch">
    INSERT ALL
    <foreach item="item" index="index" collection="list">
      INTO SYS_PRODUCT_CALC_LABEL
      (ID, PRODUCT_ID, LABEL_NAME, LABEL_CODE, SKC_CODE, CREATE_TIME, UPDATE_TIME) VALUES
      (#{item.id,jdbcType=VARCHAR}, #{item.productId,jdbcType=VARCHAR}, #{item.labelName,jdbcType=DECIMAL},
      #{item.labelCode,jdbcType=VARCHAR}, #{item.skcCode,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
    SELECT 1 FROM DUAL
  </insert>
  <update id="updateAll">
    update SYS_PRODUCT_CALC_LABEL set IS_DELETED = 1,
                                      update_time = sysdate
    where IS_DELETED = 0
  </update>


</mapper>