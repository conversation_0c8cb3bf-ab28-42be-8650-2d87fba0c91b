package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.JicProductPutaway;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/17 15:53
 */
@Mapper
public interface JicProductPutwayMapper extends BaseMapper<JicProductPutaway> {

    List<JicProductPutaway> selectByWeIdAndNameAndStore(@Param("weid") Long weid, @Param("name") String name, @Param("vid") String storeId);

    List<JicProductPutaway>  selectByProductIdAndWeidAndVid(@Param("list") List<Long> productIds, @Param("weid") String weId, @Param("vid") Long vid);
}
