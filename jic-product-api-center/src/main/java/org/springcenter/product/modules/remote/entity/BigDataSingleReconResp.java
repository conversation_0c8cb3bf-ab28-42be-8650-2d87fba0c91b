package org.springcenter.product.modules.remote.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/5/15 15:30
 */
@Data
public class BigDataSingleReconResp implements Serializable {
    private String trace_id;
    private String request_id;
    private int code;
    private boolean use_prec;
    private boolean over_prec_threshold;
    private Route route;
    private String api_version;
    private int total;
    private List<Result> result;
    private Object msg; // 可以替换为具体类
    private double cost_time;
    private long timestamp;


    @Data
    public static class Route {
        private String recall;


    }

    @Data
    public static class Result {
        private int idx;
        private int m_product_id;
        private double score;
        private boolean top;
        private boolean sink;
    }

}
