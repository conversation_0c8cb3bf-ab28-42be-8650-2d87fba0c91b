<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.MAttributesetinstanceMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.MAttributesetinstance">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="AD_CLIENT_ID" jdbcType="DECIMAL" property="adClientId" />
    <result column="AD_ORG_ID" jdbcType="DECIMAL" property="adOrgId" />
    <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
    <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
    <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
    <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
    <result column="MODIFIERID" jdbcType="DECIMAL" property="modifierid" />
    <result column="M_ATTRIBUTESET_ID" jdbcType="DECIMAL" property="mAttributesetId" />
    <result column="GUARANTEEDATE" jdbcType="DECIMAL" property="guaranteedate" />
    <result column="M_LOT_ID" jdbcType="DECIMAL" property="mLotId" />
    <result column="SERNO" jdbcType="VARCHAR" property="serno" />
    <result column="LOT" jdbcType="VARCHAR" property="lot" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="VALUE1" jdbcType="VARCHAR" property="value1" />
    <result column="VALUE2" jdbcType="VARCHAR" property="value2" />
    <result column="VALUE3" jdbcType="VARCHAR" property="value3" />
    <result column="VALUE4" jdbcType="VARCHAR" property="value4" />
    <result column="VALUE5" jdbcType="VARCHAR" property="value5" />
    <result column="VALUE2_ID" jdbcType="DECIMAL" property="value2Id" />
    <result column="VALUE1_ID" jdbcType="DECIMAL" property="value1Id" />
    <result column="VALUE1_CODE" jdbcType="VARCHAR" property="value1Code" />
    <result column="VALUE2_CODE" jdbcType="VARCHAR" property="value2Code" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, AD_CLIENT_ID, AD_ORG_ID, ISACTIVE, CREATIONDATE, OWNERID, MODIFIEDDATE, MODIFIERID,
    M_ATTRIBUTESET_ID, GUARANTEEDATE, M_LOT_ID, SERNO, LOT, DESCRIPTION, VALUE1, VALUE2,
    VALUE3, VALUE4, VALUE5, VALUE2_ID, VALUE1_ID, VALUE1_CODE, VALUE2_CODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from M_ATTRIBUTESETINSTANCE
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="selectListBySelective" parameterType="org.springcenter.product.modules.model.MAttributesetinstance" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from M_ATTRIBUTESETINSTANCE
    where 1=1
    <if test="adClientId != null">
      and AD_CLIENT_ID = #{adClientId,jdbcType=DECIMAL}
    </if>
    <if test="adOrgId != null">
      and AD_ORG_ID = #{adOrgId,jdbcType=DECIMAL}
    </if>
    <if test="isactive != null">
      and ISACTIVE = #{isactive,jdbcType=CHAR}
    </if>
    <if test="creationdate != null">
      and CREATIONDATE = #{creationdate,jdbcType=TIMESTAMP}
    </if>
    <if test="ownerid != null">
      and OWNERID = #{ownerid,jdbcType=DECIMAL}
    </if>
    <if test="modifieddate != null">
      and MODIFIEDDATE = #{modifieddate,jdbcType=TIMESTAMP}
    </if>
    <if test="modifierid != null">
      and MODIFIERID = #{modifierid,jdbcType=DECIMAL}
    </if>
    <if test="mAttributesetId != null">
      and M_ATTRIBUTESET_ID = #{mAttributesetId,jdbcType=DECIMAL}
    </if>
    <if test="guaranteedate != null">
      and GUARANTEEDATE = #{guaranteedate,jdbcType=DECIMAL}
    </if>
    <if test="mLotId != null">
      and M_LOT_ID = #{mLotId,jdbcType=DECIMAL}
    </if>
    <if test="serno != null">
      and SERNO = #{serno,jdbcType=VARCHAR}
    </if>
    <if test="lot != null">
      and LOT = #{lot,jdbcType=VARCHAR}
    </if>
    <if test="description != null">
      and DESCRIPTION = #{description,jdbcType=VARCHAR}
    </if>
    <if test="value1 != null">
      and VALUE1 = #{value1,jdbcType=VARCHAR}
    </if>
    <if test="value2 != null">
      and VALUE2 = #{value2,jdbcType=VARCHAR}
    </if>
    <if test="value3 != null">
      and VALUE3 = #{value3,jdbcType=VARCHAR}
    </if>
    <if test="value4 != null">
      and VALUE4 = #{value4,jdbcType=VARCHAR}
    </if>
    <if test="value5 != null">
      and VALUE5 = #{value5,jdbcType=VARCHAR}
    </if>
    <if test="value2Id != null">
      and VALUE2_ID = #{value2Id,jdbcType=DECIMAL}
    </if>
    <if test="value1Id != null">
      and VALUE1_ID = #{value1Id,jdbcType=DECIMAL}
    </if>
    <if test="value1Code != null">
      and VALUE1_CODE = #{value1Code,jdbcType=VARCHAR}
    </if>
    <if test="value2Code != null">
      and VALUE2_CODE = #{value2Code,jdbcType=VARCHAR}
    </if>
  </select>
</mapper>
