package org.springcenter.product.modules.service;

import org.springcenter.product.api.dto.ExportSizeInfoDataReq;
import org.springcenter.product.api.dto.ProductFabInfoReq;
import org.springcenter.product.api.dto.ProductSizeInfoResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/12/21 16:59
 */
public interface IProductSizeInfoService {
    /**
     * 返回
     * @param requestData 商品id
     * @return 返回map
     */
    List<ProductSizeInfoResp> queryFabSizeInfo(ProductFabInfoReq requestData);

    /**
     * 返回导入的excel
     * @param requestData 入参
     * @return 返回
     */
    void exportSizeInfoData(String keys, ExportSizeInfoDataReq requestData);
}
