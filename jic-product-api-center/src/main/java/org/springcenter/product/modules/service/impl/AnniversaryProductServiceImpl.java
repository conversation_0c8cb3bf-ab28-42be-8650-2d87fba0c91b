package org.springcenter.product.modules.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.api.dto.IsTakePartInAnniActReq;
import org.springcenter.product.api.dto.IsTakePartInAnniActResp;
import org.springcenter.product.modules.mapper.product.OutOfAnniversaryProductMapper;
import org.springcenter.product.modules.model.OutOfAnniversaryProduct;
import org.springcenter.product.modules.service.AnniversaryProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/8/18 16:06
 */
@Service
@Slf4j
public class AnniversaryProductServiceImpl implements AnniversaryProductService {

    @Autowired
    private OutOfAnniversaryProductMapper outOfAnniversaryProductMapper;

    @Override
    public List<IsTakePartInAnniActResp> isTakePartIn(IsTakePartInAnniActReq requestData) {
        // 参与六折部分 1、直营导购||搭配师 展示标签;  2、经销导购 不展示标签
        // 查询所有参与款
        List<OutOfAnniversaryProduct> existSpu = outOfAnniversaryProductMapper.selectSpus(requestData.getSpus());
        List<String> reOrderSpus = existSpu.stream().filter(v -> Objects.equals(v.getType(), 0))
                .map(OutOfAnniversaryProduct::getSpu).collect(Collectors.toList());
        List<String> sixDisSpus = existSpu.stream().filter(v -> Objects.equals(v.getType(), 1))
                .map(OutOfAnniversaryProduct::getSpu).collect(Collectors.toList());
        List<IsTakePartInAnniActResp> rets = new ArrayList<>();
        requestData.getSpus().forEach(v -> {
            if (CollectionUtils.isEmpty(existSpu)) {
                IsTakePartInAnniActResp actResp = new IsTakePartInAnniActResp();
                actResp.setIsTakePart(1);
                actResp.setIsSixDiscount(0);
                actResp.setSpu(v);
                rets.add(actResp);
            } else {
                IsTakePartInAnniActResp actResp = new IsTakePartInAnniActResp();
                actResp.setSpu(v);
                actResp.setIsTakePart(CollectionUtils.isNotEmpty(reOrderSpus) && reOrderSpus.contains(v) ? 0 : 1);
                actResp.setIsSixDiscount(CollectionUtils.isNotEmpty(sixDisSpus) && sixDisSpus.contains(v) ? 1 : 0);
                rets.add(actResp);
            }
        });

        return rets;
    }
}
