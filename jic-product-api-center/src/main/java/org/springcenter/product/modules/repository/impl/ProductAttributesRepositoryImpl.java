package org.springcenter.product.modules.repository.impl;

import org.springcenter.product.modules.mapper.product.MAttributesetinstanceMapper;
import org.springcenter.product.modules.model.MAttributesetinstance;
import org.springcenter.product.modules.repository.IProductAttributesRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/15/21 11:02 AM
 */
@Repository
public class ProductAttributesRepositoryImpl implements IProductAttributesRepository {

    @Autowired
    MAttributesetinstanceMapper mAttributesetinstanceMapper;

    @Override
    public List<MAttributesetinstance> selectListBySelective(MAttributesetinstance mAttributesetinstance) {
        return mAttributesetinstanceMapper.selectListBySelective(mAttributesetinstance);
    }
}
