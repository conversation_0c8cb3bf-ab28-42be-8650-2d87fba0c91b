<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.bojun.MProductaliasAliasAgainMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.bojun.MProductaliasAliasAgain">
        <id column="ID" property="id" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="M_PRODUCTALIAS_ID" property="mProductaliasId" />
        <result column="ZHUIDAN_ID" property="zhuidanId" />
        <result column="IS_MERGE" property="isMerge" />
        <result column="OWNERID" property="ownerid" />
        <result column="MODIFIERID" property="modifierid" />
        <result column="CREATIONDATE" property="creationdate" />
        <result column="MODIFIEDDATE" property="modifieddate" />
        <result column="ISACTIVE" property="isactive" />

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, M_PRODUCTALIAS_ID, ZHUIDAN_ID, IS_MERGE,  OWNERID, MODIFIERID, CREATIONDATE, MODIFIEDDATE, ISACTIVE
    </sql>
    <select id="selectOrigProductIdByZhuiDanAliasId" resultType="java.lang.Long">
        select b.ID from
            M_PRODUCTALIAS_ALIAS_AGAIN a
        LEFT JOIN BOX_M_PRODUCT b on a.M_PRODUCTALIAS_ID = b.CODEID
        WHERE a.ZHUIDAN_ID = #{zhuiDanId}
    </select>

    <select id="getSkuInfoByZdSkuId" resultType="org.springcenter.product.api.dto.ZhuiDanSkuInfoResp">
        select
            a.ZHUIDAN_ID as zdSkuId,
            a.M_PRODUCTALIAS_ID as mProductAliasId,
            b.NO as skuNo
        from
            M_PRODUCTALIAS_ALIAS_AGAIN a
        LEFT JOIN BOX_M_PRODUCT b on a.M_PRODUCTALIAS_ID = b.CODEID
        WHERE
        a.ZHUIDAN_ID in
            <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                 #{item}
            </foreach>
        AND a.ISACTIVE = 'Y'
    </select>


</mapper>
