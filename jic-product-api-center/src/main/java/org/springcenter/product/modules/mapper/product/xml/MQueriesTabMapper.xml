<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.MQueriesTabMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.MQueriesTab">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="node" column="NODE" jdbcType="VARCHAR"/>
            <result property="subNode" column="SUB_NODE" jdbcType="VARCHAR"/>
            <result property="code" column="CODE" jdbcType="VARCHAR"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="pid" column="PID" jdbcType="DECIMAL"/>
            <result property="type" column="TYPE" jdbcType="DECIMAL"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="isDelete" column="IS_DELETE" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="attrField" column="ATTR_FIELD" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NODE,SUB_NODE,
        CODE,NAME,PID,
        TYPE,
        REMARK,IS_DELETE,CREATE_TIME,
        UPDATE_TIME, ATTR_FIELD
    </sql>
    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO M_QUERIES_TAB
            (ID, NODE, SUB_NODE, CODE, NAME, PID, TYPE, CREATE_TIME, UPDATE_TIME) VALUES
            (#{item.id,jdbcType=DECIMAL}, #{item.node,jdbcType=VARCHAR}, #{item.subNode,jdbcType=VARCHAR},
            #{item.code,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.pid,jdbcType=DECIMAL},
            #{item.type,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update M_QUERIES_TAB
            set
            IS_DELETE = #{item.isDelete},
            NAME = #{item.name,jdbcType=VARCHAR},
            UPDATE_TIME = sysdate
            where ID = #{item.id}
        </foreach>
    </update>


    <select id="getMQueriesId" resultType="java.lang.Long" useCache="false" flushCache="true">
        select seq_M_QUERIES_ID.nextval from dual
    </select>

    <select id="getAttrId" resultType="java.lang.Long" useCache="false" flushCache="true">
        select seq_ATTR_ID.nextval from dual
    </select>

    <select id="getAttrValueId" resultType="java.lang.Long" useCache="false" flushCache="true">
        select seq_ATTR_VALUE_ID.nextval from dual
    </select>

    <select id="getSecAttrId" resultType="java.lang.Long" useCache="false" flushCache="true">
        select seq_SEC_ATTR_ID.nextval from dual
    </select>

</mapper>
