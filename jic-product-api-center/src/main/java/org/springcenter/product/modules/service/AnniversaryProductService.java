package org.springcenter.product.modules.service;

import org.springcenter.product.api.dto.IsTakePartInAnniActReq;
import org.springcenter.product.api.dto.IsTakePartInAnniActResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/8/18 16:06
 */
public interface AnniversaryProductService {

    /**
     * 获取款号是否参与活动
     * @param requestData 入参
     * @return 返回
     */
    List<IsTakePartInAnniActResp> isTakePartIn(IsTakePartInAnniActReq requestData);
}
