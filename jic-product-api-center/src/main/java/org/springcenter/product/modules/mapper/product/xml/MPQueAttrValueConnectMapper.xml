<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.MPQueAttrValueConnectMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.MPQueAttrValueConnect">
            <id property="id" column="ID" />
            <result property="mvProductId" column="MV_PRODUCT_ID" />
            <result property="queConnectId" column="QUE_CONNECT_ID" />
            <result property="value" column="VALUE" />
            <result property="valueName" column="VALUE_NAME" />
            <result property="isDelete" column="IS_DELETE" />
            <result property="createTime" column="CREATE_TIME" />
            <result property="updateTime" column="UPDATE_TIME" />
    </resultMap>

    <sql id="Base_Column_List">
        a.ID, a.MV_PRODUCT_ID, a.QUE_CONNECT_ID, a."VALUE", a.VALUE_NAME, a.IS_DELETE, a.CREATE_TIME, a.UPDATE_TIME
    </sql>
    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO MP_QUE_ATTR_VALUE_CONNECT
            (ID, MV_PRODUCT_ID, QUE_CONNECT_ID, "VALUE", VALUE_NAME, CREATE_TIME, UPDATE_TIME) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.mvProductId,jdbcType=VARCHAR}, #{item.queConnectId,jdbcType=VARCHAR},
            #{item.value,jdbcType=VARCHAR}, #{item.valueName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>
    <update id="delAllMPValueConnectByMpIds">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update MP_QUE_ATTR_VALUE_CONNECT
            set
            IS_DELETE = 1
            where MV_PRODUCT_ID = #{item}
        </foreach>
    </update>

    <update id="batchUpdateValue">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update MP_QUE_ATTR_VALUE_CONNECT
            set
            <if test="item.value != null and item.value !=''">
            "VALUE" = #{item.value,jdbcType=VARCHAR},
            </if>
            <if test="item.valueName != null and item.valueName !=''">
            VALUE_NAME = #{item.valueName,jdbcType=VARCHAR},
            </if>
            UPDATE_TIME = sysdate
            where ID = #{item.id}
        </foreach>
    </update>


    <resultMap id="EntityResultMap" type="org.springcenter.product.modules.model.MQueriesAttrConEntity">
        <id property="id" column="ID" />
        <result property="value" column="VALUE" />
        <result property="valueName" column="VALUE_NAME" />
        <result property="bigCategoryId" column="BIG_CATEGORY_ID" />
        <result property="bigCategoryCode" column="BIG_CATEGORY_CODE" />
        <result property="smallCategoryId" column="SMALL_CATEGORY_ID" />
        <result property="smallCategoryCode" column="SMALL_CATEGORY_CODE" />
        <result property="attributeId" column="ATTRIBUTE_ID" />
        <result property="attributeCode" column="ATTRIBUTE_CODE" />
        <result property="type" column="TYPE" />
        <result property="bigCategoryName" column="BIG_CATEGORY_NAME" />
        <result property="smallCategoryName" column="SMALL_CATEGORY_NAME" />
        <result property="attributeName" column="ATTRIBUTE_NAME" />
        <result property="connectAttributeId" column="CONNECT_ATTRIBUTE_ID" />
        <result property="connectAttributeCode" column="CONNECT_ATTRIBUTE_CODE" />
        <result property="connectAttributeName" column="CONNECT_ATTRIBUTE_NAME" />
    </resultMap>

    <select id="selectByMpId" resultMap="EntityResultMap">
        SELECT b.ID, a.BIG_CATEGORY_ID, a.BIG_CATEGORY_CODE,
               a.SMALL_CATEGORY_ID, a.SMALL_CATEGORY_CODE,
               a.ATTRIBUTE_ID, a.ATTRIBUTE_CODE, a.TYPE,
               a.BIG_CATEGORY_NAME, a.SMALL_CATEGORY_NAME,
               a.ATTRIBUTE_NAME, a.CONNECT_ATTRIBUTE_ID,
               a.CONNECT_ATTRIBUTE_CODE, a.CONNECT_ATTRIBUTE_NAME,
               "VALUE",
               case when c.type = 3  then c.NAME
                    else  b.VALUE_NAME end as VALUE_NAME
        FROM M_QUERIES_ATTR_CONNECTION a
                 left join MP_QUE_ATTR_VALUE_CONNECT b on a.ID = b.QUE_CONNECT_ID
                 left join M_QUERIES_TAB c on b."VALUE" = c.CODE
        where
            b.MV_PRODUCT_ID = #{mpId} and a.IS_DELETE = 0 and b.IS_DELETE = 0
        order by a.ATTRIBUTE_CODE asc
    </select>

    <select id="selectByProductId" resultMap="BaseResultMap">
       select <include refid="Base_Column_List"></include> from
        MP_QUE_ATTR_VALUE_CONNECT a
        left join M_QUERIES_ATTR_CONNECTION b
        on a.QUE_CONNECT_ID = b.ID
        where a.IS_DELETE = 0 and b.IS_DELETE = 0 and b.TYPE = 1 and b.ATTRIBUTE_ID = 32
        and MV_PRODUCT_ID in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
    </select>


</mapper>
