package org.springcenter.product.modules.service;

import org.springcenter.product.api.dto.ProductLifestyleInfoReq;
import org.springcenter.product.api.dto.ProductLifestyleInfoResp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date:2024/7/1 10:59
 */
public interface IProductInfoService {
    /**
     * 根据商品id查询商品生命周期
     * @param req 入参
     * @return 返回
     */
    List<ProductLifestyleInfoResp> queryProductLifestyle(ProductLifestyleInfoReq req);

    /**
     * 根据商品id获取新品标
     * @param productIds 商品id
     * @return 返回 <商品id， 是否新品[0不是新品，1是新品]>
     */
    Map<Long, Integer> queryNewProduct(List<Long> productIds);
}
