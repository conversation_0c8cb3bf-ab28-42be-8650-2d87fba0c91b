package org.springcenter.product.modules.repository;



import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.context.ProductApiContext;
import org.springcenter.product.modules.entity.ProductApiEntity;
import org.springcenter.product.modules.model.BoxMProduct;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
public interface IBoxMProductRepository {

    BoxMProduct findOneByCondition(BoxMProduct boxMProduct);

    List<BoxMProduct> findByName(String name);

    List<BoxMProduct> findByNoList(List<String> noList);

    List<BoxMProduct> selectListBySkuIds(List<Long> skuIds);

    List<ProductApiEntity> getProductApiList(ProductApiContext context);

    List<BoxMProduct> findBatchProductBaseInfoByCondition(List<String> nameList);

    List<BoxMProduct> findByCondition(BoxMProduct record);

    List<BoxMProduct> selectListByNames(List<String> names);

    List<BoxMProduct> selectListByGbCodes(List<String> gbCodes);

    List<Long> getNotEffectProductCodes(List<String> notEffectProductCodesList);

    BoxMProduct selectBoxMProductByNo(String no);
}
