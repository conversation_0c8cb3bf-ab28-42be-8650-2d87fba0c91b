package org.springcenter.product.modules.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springcenter.product.modules.mapper.product.MProductVersionPicMapper;
import org.springcenter.product.modules.model.MProductVersionPic;
import org.springcenter.product.modules.repository.IMProductVersionPicRepository;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.api.enums.ProductVersionPicEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【M_PRODUCT_VERSION_PIC(版型库照片)】的数据库操作Service实现
* @createDate 2022-12-05 09:43:42
*/
@Service
public class MProductVersionPicRepositoryImpl implements IMProductVersionPicRepository {
    
    @Autowired
    private MProductVersionPicMapper mProductVersionPicMapper;

    @Override
    public List<MProductVersionPic> queryPic(String id) {
        QueryWrapper<MProductVersionPic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("PRODUCT_VERSION_ID", id);
        queryWrapper.eq("IS_DELETE", IsDeleteEnum.NORMAL.getCode());
        return mProductVersionPicMapper.selectList(queryWrapper);
    }

    @Override
    public List<MProductVersionPic> queryMainPicByIds(List<String> mProductVersionIds) {
        QueryWrapper<MProductVersionPic> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("PRODUCT_VERSION_ID", mProductVersionIds);
        queryWrapper.in("TYPE", ProductVersionPicEnum.FRONT.getCode());
        queryWrapper.eq("IS_DELETE", IsDeleteEnum.NORMAL.getCode());
        return mProductVersionPicMapper.selectList(queryWrapper);
    }
}
