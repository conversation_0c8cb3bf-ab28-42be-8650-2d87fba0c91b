<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.JicProductPutwayMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.JicProductPutaway">
        <id property="id" column="ID" />
        <result property="weId" column="WEID" />
        <result property="productId" column="PRODUCT_ID"/>
        <result property="productNo" column="PRODUCT_NO"/>
        <result property="mallProductId" column="MALL_PRODUCT_ID"/>
        <result property="storeId" column="STOREID"/>
        <result property="mallStoreId" column="MALL_STOREID"/>
        <result property="putway" column="PUTWAY"/>
        <result property="createTime" column="CREATE_TIME"/>
    </resultMap>



    <sql id="Base_Column_List">
        ID, WEID, PRODUCT_ID, PRODUCT_NO,
        MALL_PRODUCT_ID, STOREID, MALL_STOREID,
        PUTWAY, CREATE_TIME
    </sql>
    <select id="selectByWeIdAndNameAndStore" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM USERWX.JIC_PRODUCT_PUTAWAY
        WHERE WEID = #{weid} AND PRODUCT_NO = #{name} AND MALL_STOREID = #{vid}
    </select>
    <select id="selectByProductIdAndWeidAndVid" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM USERWX.JIC_PRODUCT_PUTAWAY
        WHERE PRODUCT_ID IN
        <foreach item="item" collection="list" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND WEID = #{weid}
        AND MALL_STOREID = #{vid}
    </select>


</mapper>
