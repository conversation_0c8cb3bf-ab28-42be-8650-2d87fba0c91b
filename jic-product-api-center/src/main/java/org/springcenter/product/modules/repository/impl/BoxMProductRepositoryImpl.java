package org.springcenter.product.modules.repository.impl;

import org.springcenter.product.modules.context.ProductApiContext;
import org.springcenter.product.modules.entity.ProductApiEntity;
import org.springcenter.product.modules.mapper.product.BoxMProductMapper;
import org.springcenter.product.modules.model.BoxMProduct;
import org.springcenter.product.modules.repository.IBoxMProductRepository;
import org.springcenter.product.modules.util.StrUtil;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/23
 */
@Repository
public class BoxMProductRepositoryImpl implements IBoxMProductRepository {

    @Resource
    BoxMProductMapper boxMProductMapper;

    @Override
    public BoxMProduct findOneByCondition(BoxMProduct boxMProduct) {
        return boxMProductMapper.findOneByCondition(boxMProduct);
    }

    @Override
    public List<BoxMProduct> findByName(String name) {
        return boxMProductMapper.findByName(name);
    }

    @Override
    public List<BoxMProduct> findByNoList(List<String> noList) {
        return boxMProductMapper.findByNoList(noList);
    }

    @Override
    public List<BoxMProduct> selectListBySkuIds(List<Long> skuIds) {
        return boxMProductMapper.selectListBySkuIds(skuIds);
    }

    @Override
    public List<ProductApiEntity> getProductApiList(ProductApiContext context) {
        return boxMProductMapper.selectProductApi(context);
    }

    @Override
    public List<BoxMProduct> findBatchProductBaseInfoByCondition(List<String> nameList) {
        if (nameList.isEmpty()) return new ArrayList<>();
        return boxMProductMapper.findBatchProductBaseInfoByCondition(nameList);
    }

    @Override
    public List<BoxMProduct> findByCondition(BoxMProduct record) {
        return boxMProductMapper.findByCondition(record);
    }

    @Override
    public List<BoxMProduct> selectListByNames(List<String> names) {
        return boxMProductMapper.selectListByNames(names);
    }

    @Override
    public List<BoxMProduct> selectListByGbCodes(List<String> gbCodes) {
        return boxMProductMapper.selectListByGbCodes(gbCodes);
    }

    @Override
    public List<Long> getNotEffectProductCodes(List<String> notEffectProductCodesList) {
        List<Long> notEffectProductIds = new ArrayList<>();
        if(notEffectProductCodesList.size() > 900){
            List<List<String>> subLists = StrUtil.getSubLists(notEffectProductCodesList,900);
            for (List<String> subList : subLists) {
                notEffectProductIds.addAll(boxMProductMapper.selectListBySkcCodesGroupId(subList));
            }
        }else{
            notEffectProductIds.addAll(boxMProductMapper.selectListBySkcCodesGroupId(notEffectProductCodesList));
        }
        return notEffectProductIds;
    }

    @Override
    public BoxMProduct selectBoxMProductByNo(String no) {
        return boxMProductMapper.selectListByNo(no);
    }
}
