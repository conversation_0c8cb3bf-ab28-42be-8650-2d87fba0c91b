package org.springcenter.product.modules.repository.impl;

import org.springcenter.product.modules.repository.IMProductVersionAttributeRepository;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【M_PRODUCT_VERSION_ATTRIBUTE(版型库属性表)】的数据库操作Service实现
* @createDate 2022-12-05 09:43:37
*/
@Service
public class MProductVersionAttributeRepositoryImpl implements IMProductVersionAttributeRepository {

}
