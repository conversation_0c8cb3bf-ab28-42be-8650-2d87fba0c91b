package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.LabelMainRule;
import org.springcenter.product.modules.model.LabelRuleSqlStr;

import java.util.List;

public interface LabelRuleSqlStrMapper extends BaseMapper<LabelRuleSqlStr> {

    void insertBatch(@Param("list") List<LabelRuleSqlStr> mainRuleList);

    void batchUpdate(@Param("list") List<LabelRuleSqlStr> updateList);
}
