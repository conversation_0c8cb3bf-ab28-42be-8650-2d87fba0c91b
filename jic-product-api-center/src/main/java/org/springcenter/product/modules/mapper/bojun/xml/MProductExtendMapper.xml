<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.bojun.MProductExtendMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.bojun.MProductExtend">
        <id column="ID" jdbcType="DECIMAL" property="id" />
        <result column="AD_CLIENT_ID" jdbcType="DECIMAL" property="adClientId" />
        <result column="AD_ORG_ID" jdbcType="DECIMAL" property="adOrgId" />
        <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
        <result column="MODIFIERID" jdbcType="DECIMAL" property="modifierid" />
        <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
        <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
        <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
        <result column="M_PRODUCT_ID" jdbcType="DECIMAL" property="mProductId" />
        <result column="SALES_TALK" jdbcType="VARCHAR" property="salesTalk" />
        <result column="USER_DEFINED1" jdbcType="VARCHAR" property="userDefined1" />
        <result column="USER_DEFINED2" jdbcType="VARCHAR" property="userDefined2" />
        <result column="USER_DEFINED3" jdbcType="VARCHAR" property="userDefined3" />
        <result column="USER_DEFINED4" jdbcType="VARCHAR" property="userDefined4" />
        <result column="USER_DEFINED5" jdbcType="VARCHAR" property="userDefined5" />
    </resultMap>
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, OWNERID, MODIFIERID, CREATIONDATE, MODIFIEDDATE, ISACTIVE,
    M_PRODUCT_ID, SALES_TALK, USER_DEFINED1, USER_DEFINED2, USER_DEFINED3, USER_DEFINED4, USER_DEFINED5
    </sql>
    <select id="selectInfoByProductId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
            FROM M_PRODUCT_EXTEND
        where ISACTIVE = 'Y' and M_PRODUCT_ID = #{productId}
    </select>


</mapper>
