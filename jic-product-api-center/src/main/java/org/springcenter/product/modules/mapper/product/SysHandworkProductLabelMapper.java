package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.entity.BatchProductLabel;
import org.springcenter.product.modules.model.SysHandworkProductLabel;
import org.springcenter.product.modules.model.SysProductLabel;

import java.util.List;

public interface SysHandworkProductLabelMapper extends BaseMapper<SysHandworkProductLabel> {
    int insert(SysHandworkProductLabel record);

    int insertSelective(SysHandworkProductLabel record);

    void delByProductIds(@Param("productIds") List<String> productIds);

    void delByProductIdsAndColorNos(@Param("list") List<BatchProductLabel> list);

    List<String> selectByDistinctSkcCode();

    void delByProductIdAndSkcCode(@Param("productId") String productId, @Param("skcCode") String skcCode);

    void batchInsert(@Param("list") List<SysProductLabel> labelList);
}
