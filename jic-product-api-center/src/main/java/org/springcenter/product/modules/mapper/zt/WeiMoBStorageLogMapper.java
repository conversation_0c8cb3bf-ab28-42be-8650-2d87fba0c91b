package org.springcenter.product.modules.mapper.zt;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.zt.WeiMoBStorageLog;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/6/13 13:41
 */
public interface WeiMoBStorageLogMapper extends BaseMapper<WeiMoBStorageLog> {


    List<WeiMoBStorageLog> selectByParams(@Param("startTime") Date startTime, @Param("id") Long id, @Param("endTime") Date endTime);
}
