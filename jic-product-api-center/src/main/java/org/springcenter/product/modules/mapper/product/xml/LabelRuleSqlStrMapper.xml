<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.LabelRuleSqlStrMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.LabelRuleSqlStr">
    <result column="ID" property="id" />
    <result column="LABEL_CODE" property="labelCode" />
    <result column="LABEL_NAME" property="labelName" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="IS_DELETED" property="isDeleted" />
    <result column="PARENT_LABEL" property="parentLabel" />
    <result column="SQL_STR" property="sqlStr" />
    <result column="SORT" property="sort" />
  </resultMap>
  <insert id="insertBatch">
    INSERT ALL
    <foreach item="item" index="index" collection="list">
      INTO LABEL_RULE_SQL_STR
      (ID, LABEL_CODE, LABEL_NAME, CREATE_TIME, UPDATE_TIME, PARENT_LABEL, SQL_STR, SORT) VALUES
      (#{item.id,jdbcType=VARCHAR}, #{item.labelCode,jdbcType=VARCHAR},
      #{item.labelName,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP}, #{item.parentLabel,jdbcType=VARCHAR},
      #{item.sqlStr,jdbcType=VARCHAR}, #{item.sort,jdbcType=VARCHAR})
    </foreach>
    SELECT 1 FROM DUAL
  </insert>

  <update id="batchUpdate">
    <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
      update LABEL_RULE_SQL_STR
      set
      SORT = #{item.sort},
      IS_DELETED = #{item.isDeleted},
      SQL_STR = #{item.sqlStr},
      UPDATE_TIME = sysdate
      where LABEL_CODE = #{item.labelCode}
      and  LABEL_NAME = #{item.labelName}
      and PARENT_LABEL = #{item.parentLabel}
    </foreach>
  </update>


</mapper>