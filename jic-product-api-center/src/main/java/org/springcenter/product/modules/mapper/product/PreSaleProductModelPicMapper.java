package org.springcenter.product.modules.mapper.product;

import org.apache.ibatis.annotations.Param;
import org.springcenter.product.api.dto.ModelDataDto;
import org.springcenter.product.modules.model.PreSaleProductModelPic;

import java.util.List;

public interface PreSaleProductModelPicMapper {


    List<String> selectModelUrlByModelNo(@Param("list") List<String> modelNos);

    PreSaleProductModelPic selectByModelNo(@Param("spu") String name);

    void batchInsert(@Param("list") List<PreSaleProductModelPic> pics);

    void batchUpdate(@Param("list") List<PreSaleProductModelPic> pics);
}
