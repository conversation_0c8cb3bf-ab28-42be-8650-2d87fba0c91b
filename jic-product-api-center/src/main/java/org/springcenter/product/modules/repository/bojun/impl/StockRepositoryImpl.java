package org.springcenter.product.modules.repository.bojun.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springcenter.product.modules.entity.EbAndWscSpuStockEntity;
import org.springcenter.product.modules.entity.ProductStockEntity;
import org.springcenter.product.modules.entity.SkcStockEntity;
import org.springcenter.product.modules.entity.SpuStockEntity;
import org.springcenter.product.modules.mapper.bigdata.ProFaStorageQtyMapper;
import org.springcenter.product.modules.mapper.bojun.BBoxMProductMapper;
import org.springcenter.product.modules.mapper.product.BEbStoreMapper;
import org.springcenter.product.modules.mapper.bojun.CStoreMapper;
import org.springcenter.product.modules.mapper.bojun.FaStorageMapper;
import org.springcenter.product.modules.model.BEbStore;
import org.springcenter.product.modules.model.bojun.CStore;
import org.springcenter.product.modules.model.bojun.FaStorage;
import org.springcenter.product.modules.repository.bojun.StockRepository;
import org.springcenter.product.api.dto.ProductAgentStockResp;
import org.springcenter.product.api.dto.SameSpuProductResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/3/10 13:15
 */
@Repository
public class StockRepositoryImpl implements StockRepository {

    @Autowired
    private FaStorageMapper faStorageMapper;

    @Autowired
    private CStoreMapper cStoreMapper;

    @Autowired
    private BBoxMProductMapper boxMProductMapper;

    @Autowired
    private BEbStoreMapper bEbStoreMapper;

    @Autowired
    private ProFaStorageQtyMapper proStorageQtyMapper;

    public static final long BOX_STOREID = 417608L;
    public static final long LJ_STOREID = 31066L;

    @Override
    public List<Long> getAgentStoreByStoreId(Long storeId) {
        return cStoreMapper.getAgentStoreByStoreId(storeId);
    }

    @Override
    public List<ProductAgentStockResp> getStorages(List<Long> skuIds, List<Long> storeIds) {
        return faStorageMapper.getStorageList(skuIds, storeIds, 0);
    }

    @Override
    public List<SameSpuProductResp> getSameSpuProduct(String spu) {
        return boxMProductMapper.getSameSpuProduct(spu);
    }

    @Override
    public List<SpuStockEntity> selectBySpuAndStoreIds(List<Long> productIds, List<Long> storeIds) {
        return faStorageMapper.getInfoBySpuAndStoreIds(productIds, storeIds);
    }

    @Override
    public List<SpuStockEntity> selectEbStorageBySpuAndStoreIds(List<Long> productIds, List<Long> storeIds) {
        return faStorageMapper.getEbStorageBySpuAndStoreIds(productIds, storeIds);
    }

    @Override
    public List<SpuStockEntity> getSpuStorages(List<Long> productId, List<Long> storeIds) {
        return faStorageMapper.getSpuStorageList(productId, storeIds);
    }

    @Override
    public boolean containsBLJnbypStore(Long storeId) {
        List<BEbStore> storeList = bEbStoreMapper.selectListByStatus(1);
        List<Long> unionStoreIdList = storeList.stream().map(BEbStore::getId).collect(Collectors.toList());
        // 获取所有指定江南布衣+门店下的品牌店
        List<CStore> storeAllList = cStoreMapper.getByCUnionstoreIdList(unionStoreIdList);
        List<Long> storeIdList = storeAllList.stream().map(CStore::getId).collect(Collectors.toList());
        storeIdList.addAll(Arrays.asList(BOX_STOREID, LJ_STOREID));
        return storeIdList.contains(storeId);
    }

    @Override
    public FaStorage getStorageById(Long faStoreId) {
        return faStorageMapper.selectByPrimaryKey(faStoreId);
    }

    @Override
    public long getStorage(Long mProductAliasId, List<Long> storeIds) {
        return faStorageMapper.getStorage(mProductAliasId, storeIds);
    }

    @Override
    public List<SkcStockEntity> querySkcStorageList(long productId, long storeId) {
        return faStorageMapper.querySkcStorageList(productId, storeId);
    }

    @Override
    public List<SkcStockEntity> queryLjBoxSkcStorage(long productId) {
        return faStorageMapper.queryLjBoxSkcStorage(productId);
    }

    @Override
    public List<SkcStockEntity> queryBLJnbypSkcStorageList(long productId) {
        List<BEbStore> storeList = bEbStoreMapper.selectListByStatus(1);
        List<Long> unionStoreIdList = storeList.stream().map(BEbStore::getId).collect(Collectors.toList());
        // 获取所有指定江南布衣+门店下的品牌店
        List<CStore> storeAllList = cStoreMapper.getByCUnionstoreIdList(unionStoreIdList);
        List<Long> storeIdList = storeAllList.stream().map(CStore::getId).collect(Collectors.toList());
        return faStorageMapper.queryBLJnbypSkcStorageList(productId, storeIdList);
    }

    @Override
    public List<ProductStockEntity> getJNBYGatherStock(List<Long> mProductAliasIds) {
        List<BEbStore> storeList = bEbStoreMapper.selectListByStatus(1);
        List<Long> unionStoreIdList = storeList.stream().map(BEbStore::getId).collect(Collectors.toList());
        // 获取所有指定江南布衣+门店下的品牌店
        List<CStore> storeAllList = cStoreMapper.getByCUnionstoreIdList(unionStoreIdList);
        List<Long> storeIdList = storeAllList.stream().map(CStore::getId).collect(Collectors.toList());
        return faStorageMapper.getJNBYGatherStock(mProductAliasIds, storeIdList);
    }

    @Override
    public List<EbAndWscSpuStockEntity> getEbAndWscSpuStorages(List<Long> productIds, List<Long> storeIds) {
        return proStorageQtyMapper.getEbAndWscSpuStorages(productIds, storeIds);
    }
}
