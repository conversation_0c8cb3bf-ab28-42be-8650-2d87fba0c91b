package org.springcenter.product.modules.remote.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/2/29 9:46
 */
@Data
public class JnbyAuthSysCateResp implements Serializable {
    private List<Records> records;

    private int total;

    private int size;

    private int current;

    private List<String> orders;

    private boolean optimizeCountSql;

    private boolean hitCount;

    private String countId;

    private String maxLimit;

    private boolean searchCount;

    private int pages;

    @Data
    public static class Records {
        private int isMuilt;

        private String code;

        private String icon;

        private String type_dictText;

        private String pid;

        private Date updateTime;

        private String remark;

        private int type;

        private String sourceType_dictText;

        private String createBy;

        private int sorted;

        private Date createTime;

        private String updateBy;

        private String sourceType;

        private String hasChild;

        private String name;

        private String sysOrgCode;

        private String outId;

        private String id;

        private String categoryServiceType;

        private int status;
    }
}
