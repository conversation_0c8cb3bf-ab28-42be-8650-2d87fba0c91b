package org.springcenter.product.modules.entity;

import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/6/14 15:49
 */
@Data
public class ParentSubQinZiEsResp extends RemotingSerializable implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "对应父的样衣号")
    private String one_sample_code;

    @ApiModelProperty(value = "对应父的商品id")
    private Long one_id;

    @ApiModelProperty(value = "对应父的款号")
    private String one_name;

    @ApiModelProperty(value = "对应父的品名")
    private String one_value;

    @ApiModelProperty(value = "对应父的图片")
    private String one_cover_imgs;

    @ApiModelProperty(value = "对应父的品牌")
    private Long one_c_arcbrand_id;

    @ApiModelProperty(value = "对应父的价格")
    private BigDecimal one_price;

    @ApiModelProperty(value = "对应父的sku")
    private String one_sku_list;

    @ApiModelProperty(value = "对应子的商品id")
    private Long two_id;

    @ApiModelProperty(value = "对应子的款号")
    private String two_name;

    @ApiModelProperty(value = "对应子的品名")
    private String two_value;

    @ApiModelProperty(value = "对应子的图片")
    private String two_cover_imgs;

    @ApiModelProperty(value = "对应子的品牌")
    private Long two_c_arcbrand_id;

    @ApiModelProperty(value = "对应子的价格")
    private BigDecimal two_price;

    @ApiModelProperty(value = "对应子的sku")
    private String two_sku_list;

    @ApiModelProperty(value = "对应子的样衣")
    private String two_sample_code;

    @ApiModelProperty(value = "对应子的样衣")
    private List<Sku> skus;


    @Data
    public static class Sku extends RemotingSerializable implements Serializable{
        @ApiModelProperty(value = "SKUID")
        private String id;

        @ApiModelProperty(value = "条码")
        private String no;

        @ApiModelProperty(value = "国际码")
        private String gbcode;

        @ApiModelProperty(value = "尺码号")
        private String sizeno;

        @ApiModelProperty(value = "尺码名称")
        private String size_name;

        @ApiModelProperty(value = "SKC图片地址")
        private String imgurl;
        //扣完的图片
        @ApiModelProperty(value = "SKC白底图")
        private String matted_url;

        @ApiModelProperty(value = "SKU库存数量")
        private int stock;

        private String stylepartsize_model;

        @ApiModelProperty(value = "色号")
        private String colorno;

        @ApiModelProperty(value = "色名称")
        private String color_name;
    }

    public void buildSku(){
        if (this.getTwo_sku_list() == null || "".equals(this.getTwo_sku_list())){
            return;
        }
        this.skus = Sku.decodeArr(this.getTwo_sku_list(), Sku.class);
    }
}
