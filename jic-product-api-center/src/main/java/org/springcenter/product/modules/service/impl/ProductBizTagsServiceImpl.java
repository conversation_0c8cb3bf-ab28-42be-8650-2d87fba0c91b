package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.common.system.vo.DictModel;
import org.springcenter.product.modules.model.ProductBizTags;
import org.springcenter.product.modules.mapper.product.ProductBizTagsMapper;
import org.springcenter.product.modules.service.IProductBizTagsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcenter.product.api.dto.ProductBizTagResp;
import org.springcenter.product.api.dto.QueryProductBizTags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @auther yuanxiaozhong
 * @create 2022-12-01 16:17:30
 * @describe 服务实现类
 */
@Service
@Slf4j
public class ProductBizTagsServiceImpl extends ServiceImpl<ProductBizTagsMapper, ProductBizTags> implements IProductBizTagsService {
    private static final String BIZ_TYPE_CODE = "biz_dict_type";

    @Resource
    private ISysBaseAPI iSysBaseAPI;

    @Override
    public List<ProductBizTagResp> queryProductBizTags(QueryProductBizTags queryProductBizTags) {
        QueryWrapper<ProductBizTags> wrapper = new QueryWrapper<>();
        if (Objects.nonNull(queryProductBizTags.getProductCodes()) && queryProductBizTags.getProductCodes().size() > 0){
            wrapper.in("PRODUCT_CODE", queryProductBizTags.getProductCodes());
        }
        if (Objects.nonNull(queryProductBizTags.getBizCode())){
            wrapper.eq("TAG_DICT_CODE", queryProductBizTags.getBizCode());
        }
        if (Objects.nonNull(queryProductBizTags.getBrandIds()) && !queryProductBizTags.getBrandIds().isEmpty()){
            wrapper.in("ARC_BRAND_ID", queryProductBizTags.getBrandIds());
        }
        if (Objects.nonNull(queryProductBizTags.getSmallCategoryIds()) && !queryProductBizTags.getSmallCategoryIds().isEmpty()){
            wrapper.in("SMALL_CATEGORY_ID", queryProductBizTags.getSmallCategoryIds().stream().map(item -> Long.valueOf(item)).collect(Collectors.toList()));
        }
        if (Objects.nonNull(queryProductBizTags.getSeasonIds()) && !queryProductBizTags.getSeasonIds().isEmpty()){
            wrapper.in("SMALL_SEASION_ID", queryProductBizTags.getSeasonIds());
        }
        if (Objects.nonNull(queryProductBizTags.getYears()) && !queryProductBizTags.getYears().isEmpty()){
            wrapper.in("YEAR", queryProductBizTags.getYears());
        }
        wrapper.eq("IS_DEL", 0);
        wrapper.eq("BIZ_TYPE", queryProductBizTags.getBizType());
        wrapper.gt("END_TIME", new Date()).lt("BEGIN_TIME", new Date());
        List<ProductBizTags> bizTags = this.baseMapper.selectList(wrapper);
        if (bizTags.isEmpty()) return new ArrayList<>();

        Map<String, List<ProductBizTags>> productGroupBy = bizTags.stream().collect(Collectors.groupingBy(ProductBizTags::getProductCode));
        //获取业务下的商品标签
        List<DictModel> bizProductDicts = iSysBaseAPI.queryDictItemsByCode(queryProductBizTags.getBizType());
        log.info("获取业务字典数组 dict = {}", JSON.toJSONString(bizProductDicts));
        return productGroupBy.keySet().stream().map(item -> {
            List<ProductBizTags> values = productGroupBy.get(item);
            ProductBizTags productBizTags = values.get(0);
            ProductBizTagResp resp = new ProductBizTagResp();
            BeanUtils.copyProperties(productBizTags, resp);
            resp.setProductCode(item);
            resp.setProductId(productBizTags.getProductId().longValue());
            List<ProductBizTagResp.Tag> tags = new ArrayList<>();
            values.forEach(itemBizTags -> {
                ProductBizTagResp.Tag tag = new ProductBizTagResp.Tag();
                DictModel model = bizProductDicts.stream().filter(dictModel -> dictModel.getValue().equals(itemBizTags.getTagDictCode())).findFirst().orElse(null);
                if (Objects.nonNull(model)){
                    BeanUtils.copyProperties(model, tag);
                    tags.add(tag);
                }
            });
            resp.setTags(tags);
            return resp;
        }).collect(Collectors.toList());
    }
}
