package org.springcenter.product.modules.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/9/19 14:11
 */
@Data
public class AiAssistantReqEntity {

    @ApiModelProperty(value = "大数据区分来源")
    private String requestSource;

    @ApiModelProperty(value = "complete:内容生成 chat:对话")
    private String mode;

    @ApiModelProperty(value = "输入内容")
    private List<InputContent> input;

    @Data
    public static class InputContent {
        @ApiModelProperty(value = "角色 'system', 'user', 'assistant'")
        private String role;

        @ApiModelProperty(value = "内容")
        private String content;
    }
}
