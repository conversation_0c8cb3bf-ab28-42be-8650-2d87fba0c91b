package org.springcenter.product.modules.remote;


import org.springcenter.product.modules.remote.entity.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;


/**
 * <AUTHOR>
 * @Date:2024/5/14 10:15
 * 调用大数据接口
 */
public interface IBigDataHttpApi {

    /**
     * 计算价格
     * @param bigDataSearchReqEntity
     * @return
     */
    @POST("/text_search/text_search")
    Call<BigDataCommonResponse<List<BigDataSearchFirstRespEntity>>> searchByText(@Body BigDataSearchReqEntity bigDataSearchReqEntity);


    /**
     * 综合排序  包含的商品进行综合排序
     * @param bigDataComprehensiveReq 入参
     * @return 返回
     */
    @POST("/ProdPersonalized/v1/jnby/app")
    Call<BigDataComprehensiveResponse> searchComprehensiveList(@Body BigDataComprehensiveReq bigDataComprehensiveReq);



    /**
     * 单品推荐
     * @param bigDataComprehensiveReq 入参
     * @return 返回
     */
    @POST("/ProdSim/v1/jnby")
    Call<BigDataSingleReconResp> searchSingleRecommendation(@Body BigDataSingleReconReq bigDataComprehensiveReq);
}
