package org.springcenter.product.modules.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springcenter.product.api.dto.AfterSalesEsResp;
import org.springcenter.product.api.dto.AfterSalesResp;
import org.springcenter.product.modules.service.IProductAfterSaleInfoService;
import org.springcenter.product.modules.util.EsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/6/26 9:55
 */
@Service
@Slf4j
@RefreshScope
public class ProductAfterSaleInfoServiceImpl implements IProductAfterSaleInfoService {


    @Value("${after.sale.index}")
    private String afterSalesIndex;

    @Autowired
    private EsUtil esUtil;

    @Override
    public List<AfterSalesResp.ButtonInfo> queryButtonInfo(String requestData) {
        if (StringUtils.isBlank(requestData)) {
            throw new RuntimeException("入参款号信息不能为空");
        }

        // 根据款号查询es
        SearchRequest request = new SearchRequest();
        request.indices(afterSalesIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("product_id", requestData));
        queryBuilder.must(QueryBuilders.termQuery("is_button", 1));
        queryBuilder.must(QueryBuilders.termQuery("is_invalid", 1));

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(100);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品售后信息-ES {}", request.source().toString());
        List<AfterSalesEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return null;
            }

            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                AfterSalesEsResp entity = AfterSalesEsResp.fromJson(hit.getSourceAsString(), AfterSalesEsResp.class);
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品售后信息ES异常e = {}", e.getMessage());
            return null;
        }

        Map<String, List<AfterSalesEsResp>> map = entities.stream().collect(Collectors.groupingBy(AfterSalesEsResp::getSty_color_id));
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyList();
        }

        List<AfterSalesEsResp> salesEsResps = map.get(entities.get(0).getSty_color_id());
        List<AfterSalesResp.ButtonInfo> rets = new ArrayList<>();
        salesEsResps.forEach(entity -> {
            AfterSalesResp.ButtonInfo buttonInfo = new AfterSalesResp.ButtonInfo();
            buttonInfo.setClassifyCode(entity.getClassify_small());
            buttonInfo.setButtonNum(new BigDecimal(entity.getDh()).intValue());
            buttonInfo.setBomName(entity.getButton_name());
            buttonInfo.setBomCode(entity.getBomcode());
            rets.add(buttonInfo);
        });
        return rets.stream().distinct().collect(Collectors.toList());
    }
}
