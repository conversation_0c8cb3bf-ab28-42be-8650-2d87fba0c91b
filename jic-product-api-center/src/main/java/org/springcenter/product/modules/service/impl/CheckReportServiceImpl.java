package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.common.util.excel.IWriteDataExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.constant.IdConstant;
import org.springcenter.product.api.dto.CheckReportDataResp;
import org.springcenter.product.api.dto.CheckReportReq;
import org.springcenter.product.api.dto.CheckReportResp;
import org.springcenter.product.api.dto.ImportCheckReportExcelDto;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.api.lenovo.LenovoFileReq;
import org.springcenter.product.api.lenovo.LenovoFileResp;
import org.springcenter.product.modules.entity.ParseProductVersionEntity;
import org.springcenter.product.modules.mapper.product.CheckReportMapper;
import org.springcenter.product.modules.mapper.product.CheckReportProductMapper;
import org.springcenter.product.modules.model.CheckReport;
import org.springcenter.product.modules.model.CheckReportProduct;
import org.springcenter.product.modules.service.CheckReportService;
import org.springcenter.product.modules.service.LianXiangYunFilezService;
import org.springcenter.product.modules.util.FileParseUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RefreshScope
@Slf4j
public class CheckReportServiceImpl implements CheckReportService {

    @Autowired
    private LianXiangYunFilezService lianXiangYunFilezService;


    @Autowired
    private CheckReportMapper checkReportMapper;

    @Autowired
    private CheckReportProductMapper checkReportProductMapper;

    @Value("${lianxiang.file.path}")
    private String filePath;

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Autowired
    private QiniuUtil qiniuUtil;

    @Override
    public void syncFileInfo() {
        LenovoFileReq lenovoFileReq = new LenovoFileReq();
        lenovoFileReq.setPath(filePath);
        LenovoFileResp lenovoFile = lianXiangYunFilezService.getLenovoFile(lenovoFileReq);
        if(lenovoFile == null || CollectionUtils.isEmpty(lenovoFile.getFileModelList())){
            return ;
        }
        // 开始处理数据
        Boolean firstFlag = true;
        int i = 0;
        out:while (firstFlag){
            LenovoFileResp resp = getLenovoFileResp(filePath, i);
            if(resp == null || CollectionUtils.isEmpty(resp.getFileModelList())){
                break out;
            }
            // /不合格/成衣/22SS  一层  不合格
            List<LenovoFileResp.LenovoFileModel> fileModelList = resp.getFileModelList();
            for (LenovoFileResp.LenovoFileModel lenovoFileModel : fileModelList) {
                // 一层 不合格
                Boolean dir = lenovoFileModel.getDir();
                // 查询是否库中已经同步过了
                String oneDirName = lenovoFileModel.getPath().substring(lenovoFileModel.getPath().lastIndexOf("/") + 1);
                if(dir){
                    // 一层  不合格
                    Boolean twoFlag = true;
                    int two = 0;
                    innerOne:while (twoFlag) {
                        LenovoFileResp pdfResp = getLenovoFileResp(lenovoFileModel.getPath(), two);
                        List<LenovoFileResp.LenovoFileModel> pdfRespFileModelList = pdfResp.getFileModelList();
                        if(pdfResp == null || CollectionUtils.isEmpty(pdfResp.getFileModelList())){
                            break innerOne;
                        }
                        for (LenovoFileResp.LenovoFileModel fileModel : pdfRespFileModelList) {
                            // 二层  成衣 or 面料
                            Boolean threeDir = fileModel.getDir();
                            String twoDirName = fileModel.getPath().substring(fileModel.getPath().lastIndexOf("/") + 1);
                            if(threeDir){
                                Boolean threeFlag = true;
                                int three = 0;
                                innerTwo:while (threeFlag) {
                                    LenovoFileResp pdfRespThree = getLenovoFileResp(fileModel.getPath(), three);
                                    List<LenovoFileResp.LenovoFileModel> pdfRespFileModelListThree = pdfRespThree.getFileModelList();
                                    if(pdfRespThree == null || CollectionUtils.isEmpty(pdfRespThree.getFileModelList())){
                                        break innerTwo;
                                    }
                                    for (LenovoFileResp.LenovoFileModel model : pdfRespFileModelListThree) {
                                        //三层
                                        Boolean fourDir = model.getDir();
                                        String fourDirName = model.getPath().substring(model.getPath().lastIndexOf("/") + 1);
                                        // 3层  货季
                                        if(fourDir){
                                            Boolean fourFlag = true;
                                            int four = 0;
                                            innerThree:while (fourFlag) {
                                                LenovoFileResp pdfRespFour = getLenovoFileResp(model.getPath(), four);
                                                List<LenovoFileResp.LenovoFileModel> pdfRespFileModelListFour = pdfRespFour.getFileModelList();
                                                if(pdfRespFour == null || CollectionUtils.isEmpty(pdfRespFour.getFileModelList())){
                                                    break innerThree;
                                                }
                                                // 获取  面料还是成衣
                                                String reportTypeStr = twoDirName;
                                                // 获取  货季
                                                String reportFreightSeason = fourDirName;
                                                // 获取  合格不合格
                                                String reportStatusStr = oneDirName;

                                                String dirName = reportStatusStr +"-"+reportTypeStr+"-"+reportFreightSeason;

                                                Integer reportType = 0;
                                                if(reportTypeStr.equals("面料")){
                                                    reportType = 0;
                                                }else {
                                                    reportType = 1;
                                                }

                                                Integer reportStatus = 0;
                                                if(reportStatusStr.equals("合格")){
                                                    reportStatus = 1;
                                                }else {
                                                    reportStatus = 0;
                                                }

                                                // 处理数据 依次创建表记录
                                                List<LenovoFileResp.LenovoFileModel> pdfRespFileModelListFive = pdfRespFour.getFileModelList();
                                                Integer finalReportType = reportType;
                                                Integer finalReportStatus = reportStatus;

                                                // 查询这个列表没有的数据
                                                List<String> reportCodes = pdfRespFileModelListFive.stream().map(r -> {
                                                    String path = r.getPath();
                                                    String reportCode = path.substring(path.lastIndexOf("/") + 1);
                                                    if (reportCode.contains(".")) {
                                                        // 那么截取
                                                        reportCode = reportCode.substring(0, reportCode.indexOf("."));
                                                    }
                                                    return reportCode;
                                                }).collect(Collectors.toList());

                                                if(CollectionUtils.isNotEmpty(reportCodes)){
                                                    // 查询数据 根据reportCode  已有reportCodes
                                                    List<String> alreadyHaveReportCodes = checkReportMapper.selectByReportCodes(reportCodes,dirName);
                                                    // 已有的去除 查看没有的
                                                    if(CollectionUtils.isNotEmpty(alreadyHaveReportCodes)){
                                                        reportCodes.removeAll(alreadyHaveReportCodes);
                                                    }
                                                }

                                                template.execute(action->{
                                                    List<CheckReport> list = new ArrayList<>();

                                                    for (LenovoFileResp.LenovoFileModel fileModelFive : pdfRespFileModelListFour) {

                                                        String path = fileModelFive.getPath();
                                                        // 文件名称  检测编号
                                                        String reportCode = path.substring(path.lastIndexOf("/") + 1);
                                                        if(reportCode.contains(".")){
                                                            // 那么截取
                                                            reportCode = reportCode.substring(0,reportCode.indexOf("."));
                                                        }
                                                        if(CollectionUtils.isNotEmpty(reportCodes)){
                                                            if(reportCodes.contains(reportCode)){
                                                                String nsid = fileModelFive.getNsid();
                                                                String neid = fileModelFive.getNeid();
                                                                CheckReport checkReport = buildAndInsertData(reportCode, dirName, finalReportType, finalReportStatus, reportFreightSeason,
                                                                        fileModelFive.getDeliveryCode(), nsid, neid);
                                                                list.add(checkReport);
                                                            }
                                                        }
                                                    }

                                                    if(CollectionUtils.isNotEmpty(list)){
                                                        checkReportMapper.batchInsert(list);
                                                    }
                                                    return action;
                                                });


                                                four++;
                                            }
                                        }
                                    }
                                    three++;
                                }
                            }
                        }
                        two++;
                    }
                }
            }
            i++;
        }
    }

    private LenovoFileResp getLenovoFileResp(String path,int pageNum) {
        LenovoFileReq params = new LenovoFileReq();
        params.setPath(path);
        params.setPage_num(pageNum+"");
        LenovoFileResp resp = lianXiangYunFilezService.getLenovoFile(params);
        return resp;
    }


    @Override
    public List<CheckReport> list(CheckReportReq requestData,Page page) {
        com.github.pagehelper.Page<CheckReport> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        // 报告编号  成衣号  可以多个 英文逗号分隔
        String reportCode = requestData.getReportCode();
        String fabirc = requestData.getFabirc();
        Integer reportStatus = requestData.getReportStatus();
        Integer isDel = requestData.getIsDel();
        String reportFreightSeason = requestData.getReportFreightSeason();
        Integer notImport = requestData.getNotImport();
        Integer reportType = requestData.getReportType();

        List<String> reportCodes = new ArrayList<>();
        if(StringUtils.isNotBlank(reportCode)){
            String[] split = reportCode.split(",");
            for (String code : split) {
                reportCodes.add(code.toUpperCase());
            }
        }

        List<String> fabircs = new ArrayList<>();
        if(StringUtils.isNotBlank(fabirc)){
            String[] split = fabirc.split(",");
            for (String code : split) {
                fabircs.add(code.toUpperCase());
            }
        }
//        if(StringUtils.isNotBlank(reportCode)){
//            reportCode = reportCode.toUpperCase();
//        }
//        if(StringUtils.isNotBlank(fabirc)){
//            fabirc = fabirc.toUpperCase();
//        }

        checkReportMapper.findList(reportCodes,fabircs,reportStatus,isDel,reportFreightSeason,notImport,reportType,reportCode,fabirc);
        if(CollectionUtils.isNotEmpty(fabircs)){
            PageInfo<CheckReport> pageInfo = new PageInfo(hPage);
            List<CheckReport> list = pageInfo.getList();
            // 处理额外数据  根据查询出来的数据 在去查一遍面料号  筛选出来成衣的
            List<String> collect = list.stream().filter(r -> r.getReportType().equals(1)).map(r->r.getFabirc()).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect)){
                // 成衣号  查询到面料号
                List<CheckReport> checkReports = checkReportMapper.selectByProductCodes(collect);
                list.addAll(checkReports);
            }
            return list;
        }else{
            PageInfo<CheckReport> pageInfo = new PageInfo(hPage);
            page.setCount(hPage.getTotal());
            page.setPages(pageInfo.getPages());
            return pageInfo.getList();
        }
    }

    @Override
    public Boolean update(CheckReportReq requestData) {
        if(requestData == null || StringUtils.isBlank(requestData.getId())){
            return false;
        }
        CheckReport update = new CheckReport();
        BeanUtils.copyProperties(requestData,update);

        template.execute(action->{
            checkReportMapper.updateByPrimaryKeySelective(update);
            return action;
        });

        return true;
    }

    @Override
    public String importCheckReport(String url) {

        List<Map<String,String>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {

            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,String> result = new HashMap<>();
                    result.put("reportCode",integerStringMap.get(0));
                    result.put("fabirc",integerStringMap.get(1));
                    result.put("type",integerStringMap.get(2));
                    result.put("productCodes",integerStringMap.get(3));
                    result.put("dirName",integerStringMap.get(4));
                    importData.add(result);
                }
            }
        });
        // 失败的数据
        List<ImportCheckReportExcelDto> list = buildAndUpdate(importData);

        if (CollectionUtils.isNotEmpty(list)) {
            String fileName = System.currentTimeMillis() + ".xlsx";
            EasyExcelUtil.write(fileName, ImportCheckReportExcelDto.class, new IWriteDataExcel<ImportCheckReportExcelDto>() {
                @Override
                public List<ImportCheckReportExcelDto> getData() {
                    return list.stream().map(item -> {
                        ImportCheckReportExcelDto data = new ImportCheckReportExcelDto();
                        data.setReportCode(item.getReportCode());
                        data.setType(item.getType());
                        data.setProductCodes(item.getProductCodes());
                        data.setFabirc(item.getFabirc());
                        data.setDirName(item.getDirName());
                        return data;
                    }).collect(Collectors.toList());
                }
            });
            File file = new File(fileName);
            String param = qiniuUtil.upload(file.getPath(), "异常检测报告编号"+System.currentTimeMillis() + ".xlsx");
            Integer success = importData.size() - list.size();
            Integer fail = list.size();
            Integer sum = success + fail;
            param = param + ";" + sum + ";" + success + ";" + fail;
            file.delete();
            log.info("redis的key{}", param);
            return param;
        }
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
        return "";
    }

    @Override
    public CheckReportDataResp getReportCode(String productCode, String reportSeason, String fabircParams) {
        CheckReportDataResp checkReportDataResp = new CheckReportDataResp();

        // 查询面料的  查询面料 横机  面料  横机  需要根据款号+ 货季去查询
        List<CheckReport> fabirc = checkReportMapper.selectByProductCode(productCode,reportSeason);
        if(CollectionUtils.isNotEmpty(fabirc)){
            List<CheckReportResp> checkReportResps = JSONObject.parseArray(JSONObject.toJSONString(fabirc), CheckReportResp.class);
            checkReportDataResp.setFabirc(checkReportResps);
        }else{
            // 根据面料号查询  面料  非横机    根据面料号去查询
            List<CheckReport> list=  new ArrayList<>();
            if(StringUtils.isNotBlank(fabircParams)){
                list = checkReportMapper.selectByFabircFlatType(Arrays.asList(fabircParams.split(",")), reportSeason,productCode);
            }
            List<CheckReportResp> checkReportResps = JSONObject.parseArray(JSONObject.toJSONString(list), CheckReportResp.class);
            checkReportDataResp.setFabirc(checkReportResps);
        }
        List<CheckReport> dress = checkReportMapper.selectByFabirc(productCode,reportSeason);
        List<CheckReportResp> dressCheck = JSONObject.parseArray(JSONObject.toJSONString(dress), CheckReportResp.class);
        checkReportDataResp.setDress(dressCheck);
        return checkReportDataResp;
    }

    @Override
    public String getPdfViewUrl(String id) {
        CheckReport checkReport = checkReportMapper.selectByPrimaryKey(id);
        String neid = checkReport.getNeid();
        String nsid = checkReport.getNsid();

        String pdfView = lianXiangYunFilezService.getPdfView(neid,nsid);
        return pdfView;
    }

    @Override
    public String download(String id) {
        CheckReport checkReport = checkReportMapper.selectByPrimaryKey(id);
        String neid = checkReport.getNeid();
        String nsid = checkReport.getNsid();
        String download = lianXiangYunFilezService.download(neid, nsid);
        return download;
    }

    private List<ImportCheckReportExcelDto> buildAndUpdate(List<Map<String, String>> importData) {
        List<ImportCheckReportExcelDto> errorList = new ArrayList<>();

        for (Map<String, String> importDatum : importData) {
            String reportCode = importDatum.get("reportCode");
            String fabirc = importDatum.get("fabirc");
            String type = importDatum.get("type");
            String productCodes = importDatum.get("productCodes");
            String dirName = importDatum.get("dirName");

            if(StringUtils.isBlank(reportCode) || StringUtils.isBlank(fabirc) || StringUtils.isBlank(type) || StringUtils.isBlank(dirName)){
                ImportCheckReportExcelDto importCheckReportExcelDto = new ImportCheckReportExcelDto();
                importCheckReportExcelDto.setType(type);
                importCheckReportExcelDto.setFabirc(fabirc);
                importCheckReportExcelDto.setProductCodes(productCodes);
                importCheckReportExcelDto.setDirName(dirName);
                importCheckReportExcelDto.setReportCode(reportCode);
                errorList.add(importCheckReportExcelDto);
                log.info("必填项为空= {}",JSONObject.toJSONString(importDatum));
                continue;
            }

            if((StringUtils.isNotBlank(type) && type.equals("横机"))  && StringUtils.isBlank(productCodes)){
                ImportCheckReportExcelDto importCheckReportExcelDto = new ImportCheckReportExcelDto();
                importCheckReportExcelDto.setType(type);
                importCheckReportExcelDto.setFabirc(fabirc);
                importCheckReportExcelDto.setProductCodes(productCodes);
                importCheckReportExcelDto.setDirName(dirName);
                importCheckReportExcelDto.setReportCode(reportCode);
                errorList.add(importCheckReportExcelDto);
                log.info("横机并且没有传递productCode= {}",JSONObject.toJSONString(importDatum));
                continue;
            }


            CheckReport params = new CheckReport();
            params.setIsDel(IsDeleteEnum.NORMAL.getCode());
            if(reportCode.length() > 11){
                params.setReportCode(reportCode.substring(0,11));
            }else{
                params.setReportCode(reportCode);
            }
            params.setSyncDir(dirName);
            List<CheckReport> list = checkReportMapper.selectByLikeReportCode(params);
            if(CollectionUtils.isEmpty(list)){

                ImportCheckReportExcelDto importCheckReportExcelDto = new ImportCheckReportExcelDto();
                importCheckReportExcelDto.setType(type);
                importCheckReportExcelDto.setFabirc(fabirc.toUpperCase());
                importCheckReportExcelDto.setProductCodes(productCodes);
                importCheckReportExcelDto.setDirName(dirName);
                importCheckReportExcelDto.setReportCode(reportCode);
                errorList.add(importCheckReportExcelDto);
                log.info("buildAndUpdate失败= {}",JSONObject.toJSONString(importDatum));
                continue;
            }

            for (CheckReport checkReport : list) {
                CheckReport update = new CheckReport();
                if(type.equals("横机")){
                    update.setFlatType(1);
                }else{
                    update.setFlatType(0);
                }
                update.setId(checkReport.getId());
                update.setFabirc(fabirc.toUpperCase());
                update.setUpdateTime(new Date());
                checkReportMapper.updateByPrimaryKeySelective(update);
            }

            //处理数据
            if(StringUtils.isNotBlank(productCodes)){
                // 删除之前所有的
                for (CheckReport checkReport : list) {
                    checkReportProductMapper.updateByCheckReportId(checkReport.getId());
                }

                String[] productSplit = productCodes.split(",");
                // 插入数据
                List<CheckReportProduct> insertList = new ArrayList<>();
                for (String productCode : productSplit) {
                    for (CheckReport checkReport : list) {
                        CheckReportProduct checkReportProduct = new CheckReportProduct();
                        checkReportProduct.setId(IdLeaf.getId(IdConstant.CHECK_REPORT_PRODUCT));
                        checkReportProduct.setProductCode(productCode);
                        checkReportProduct.setCheckReportId(checkReport.getId());
                        checkReportProduct.setCreateTime(new Date());
                        checkReportProduct.setUpdateTime(new Date());
                        checkReportProduct.setIsDel(IsDeleteEnum.NORMAL.getCode());
                        insertList.add(checkReportProduct);
                    }
                }
                checkReportProductMapper.batchInsert(insertList);
            }
        }
        return errorList;
    }

    private CheckReport buildAndInsertData(String reportCode, String dirName, Integer reportType, Integer reportStatus, String reportFreightSeason,String deliveryCode,String nsid, String neid) {
        CheckReport checkReport = new CheckReport();
        checkReport.setReportType(reportType);
        checkReport.setReportStatus(reportStatus);
        checkReport.setReportFreightSeason(reportFreightSeason);
        if(reportType.equals(1)){
            try {
                checkReport.setFabirc(reportCode.substring(0,9));
            }catch (Exception e){
                log.info("截取错误");
            }
        }
        checkReport.setReportCode(reportCode);
        checkReport.setCreateTime(new Date());
        checkReport.setUpdateTime(new Date());
        checkReport.setIsDel(IsDeleteEnum.NORMAL.getCode());
        checkReport.setSyncDir(dirName);
        checkReport.setId(IdLeaf.getId(IdConstant.CHECK_REPORT));
        checkReport.setDeliveryCode(deliveryCode);
        checkReport.setNsid(nsid);
        checkReport.setNeid(neid);
        return checkReport;
    }
}
