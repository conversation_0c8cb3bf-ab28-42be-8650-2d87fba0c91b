<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.bojun.FaStorageMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.bojun.FaStorage">
        <id column="ID" jdbcType="DECIMAL" property="id" />
        <result column="AD_CLIENT_ID" jdbcType="DECIMAL" property="adClientId" />
        <result column="AD_ORG_ID" jdbcType="DECIMAL" property="adOrgId" />
        <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
        <result column="MODIFIERID" jdbcType="DECIMAL" property="modifierid" />
        <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
        <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
        <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
        <result column="C_STORE_ID" jdbcType="DECIMAL" property="cStoreId" />
        <result column="M_PRODUCT_ID" jdbcType="DECIMAL" property="mProductId" />
        <result column="M_ATTRIBUTESETINSTANCE_ID" jdbcType="DECIMAL" property="mAttributesetinstanceId" />
        <result column="QTY" jdbcType="DECIMAL" property="qty" />
        <result column="QTYPREOUT" jdbcType="DECIMAL" property="qtypreout" />
        <result column="QTYPREIN" jdbcType="DECIMAL" property="qtyprein" />
        <result column="QTYVALID" jdbcType="DECIMAL" property="qtyvalid" />
        <result column="M_PRODUCTALIAS_ID" jdbcType="DECIMAL" property="mProductaliasId" />
        <result column="QTY_OMS" jdbcType="DECIMAL" property="qtyOms" />
    </resultMap>
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, OWNERID, MODIFIERID, CREATIONDATE, MODIFIEDDATE, ISACTIVE,
    C_STORE_ID, M_PRODUCT_ID, M_ATTRIBUTESETINSTANCE_ID, QTY, QTYPREOUT, QTYPREIN, QTYVALID,
    M_PRODUCTALIAS_ID, QTY_OMS
    </sql>


    <resultMap id="SpuStockResultMap" type="org.springcenter.product.modules.entity.SpuStockEntity">
        <result column="QTY" jdbcType="DECIMAL" property="qty" />
        <result column="M_PRODUCT_ID" jdbcType="DECIMAL" property="productId" />
    </resultMap>

    <resultMap id="ProductStockResultMap" type="org.springcenter.product.api.dto.ProductAgentStockResp">
        <result column="QTY" jdbcType="DECIMAL" property="qty" />
        <result column="SKUID" jdbcType="DECIMAL" property="skuId" />
    </resultMap>
    <select id="getStorageList" resultMap="ProductStockResultMap">
        select nvl(sum(qty - greatest(qtypreout,0)),0) as QTY,m_productalias_id  as SKUID
        from v_fa_storage
        where c_store_id in
        <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and m_productalias_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND nvl(qty - greatest(qtypreout,0),0) > #{safeStock}
        group by m_productalias_id
    </select>

    <select id="getInfoBySpuAndStoreIds" resultMap="SpuStockResultMap">
        select M_PRODUCT_ID, sum(qtycan) as qty
        from v_fa_storage
        where isactive = 'Y'  and
        M_PRODUCT_ID in
        <foreach collection="productList" item="productId" open="(" close=")" separator=",">
            #{productId}
        </foreach>
        and c_store_id in
        <foreach collection="storeList" item="storeId" open="(" close=")" separator=",">
            #{storeId}
        </foreach>
        group by M_PRODUCT_ID
    </select>

    <select id="getEbStorageBySpuAndStoreIds" resultMap="SpuStockResultMap">
        select M_PRODUCT_ID, sum(qty)  as qty
        from
        BOX_EB_WXMALL_STORAGE a
        where M_PRODUCT_ID in
        <foreach collection="productList" item="productId" open="(" close=")" separator=",">
            #{productId}
        </foreach>
        group by M_PRODUCT_ID
    </select>
    <select id="getSpuStorageList" resultMap="SpuStockResultMap">
        select
        M_PRODUCT_ID,
        (case
        when sum(QTY-greatest(QTYPREOUT,0)) &lt;=0
        then 0
        else sum(QTY-greatest(QTYPREOUT,0)) end) as QTY  from v_FA_STORAGE
        where c_store_id in
        <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and M_PRODUCT_ID in
        <foreach collection="productIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by M_PRODUCT_ID
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from FA_STORAGE
        where ID = #{id,jdbcType=DECIMAL}
    </select>
    <!-- 查询库存 -->
    <select id="getStorage" resultType="java.lang.Long">
        select nvl(sum(qty - greatest(qtypreout,0)),0) as qty
        from fa_storage
        where c_store_id in
        <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and M_PRODUCTALIAS_ID = #{mProductaliasId,jdbcType=DECIMAL} AND QTY > 0
    </select>

    <resultMap id="SkcStockResultMap" type="org.springcenter.product.modules.entity.SkcStockEntity">
        <result column="QTY" jdbcType="DECIMAL" property="qty" />
        <result column="M_PRODUCT_ID" jdbcType="DECIMAL" property="productId" />
        <result column="COLORNO" jdbcType="VARCHAR" property="colorno" />
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
    </resultMap>
    <select id="querySkcStorageList" resultMap="SkcStockResultMap">
        select a.M_PRODUCT_ID,b.NAME,b.COLORNO,
               (case
                    when sum(a.QTY-greatest(a.QTYPREOUT,0)) &lt;=0
                        then 0
                    else 1 end) as QTY from v_FA_STORAGE a
                                                left join box_m_product b
                                                          on a.M_PRODUCT_ID = b.ID and a.M_PRODUCTALIAS_ID = b.CODEID
        where b.ID=#{productId,jdbcType=DECIMAL}
          and a.C_STORE_ID = #{storeId,jdbcType=DECIMAL} AND QTY > 0
        group by a.M_PRODUCT_ID,b.NAME,b.COLORNO
    </select>

    <select id="queryLjBoxSkcStorage" resultMap="SkcStockResultMap">
        select a.M_PRODUCT_ID,b.NAME,b.COLORNO,
               (case
                    when sum(a.QTY-greatest(a.QTYPREOUT,0)) &lt;=0
                        then 0
                    else 1 end) as QTY from v_FA_STORAGE a
                                                left join box_m_product b
                                                          on a.M_PRODUCT_ID = b.ID and a.M_PRODUCTALIAS_ID = b.CODEID
        where b.ID=#{productId,jdbcType=DECIMAL} and a.C_STORE_ID in (417608) AND QTY > 0
        group by a.M_PRODUCT_ID,b.NAME,b.COLORNO
        UNION
        select a.M_PRODUCT_ID,b.NAME,b.COLORNO,
               (case
                    when sum(a.QTY-greatest(a.QTYPREOUT,0)) &lt;=0
                        then 0
                    else 1 end) as QTY from v_FA_STORAGE a
                                                left join box_m_product b
                                                          on a.M_PRODUCT_ID = b.ID and a.M_PRODUCTALIAS_ID = b.CODEID
        where b.ID=#{productId,jdbcType=DECIMAL} and a.C_STORE_ID in (31066) AND QTY > 0
        group by a.M_PRODUCT_ID,b.NAME,b.COLORNO
    </select>

    <select id="queryBLJnbypSkcStorageList" resultMap="SkcStockResultMap">
        select a.M_PRODUCT_ID,b.NAME,b.COLORNO,
        (case
        when sum(a.QTY-greatest(a.QTYPREOUT,0)) &lt;=0
        then 0
        else 1 end) as QTY
        from v_FA_STORAGE a
        left join box_m_product b
        on a.M_PRODUCT_ID = b.ID and a.M_PRODUCTALIAS_ID = b.CODEID
        where b.ID=#{productId,jdbcType=DECIMAL}
        and a.C_STORE_ID in
        <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND a.QTY > 3
        group by a.M_PRODUCT_ID,b.NAME,b.COLORNO
        union
        select a.M_PRODUCT_ID,b.NAME,b.COLORNO,
        (case
        when sum(a.QTY-greatest(a.QTYPREOUT,0)) &lt;=0
        then 0
        else 1 end) as QTY
        from v_FA_STORAGE a
        left join box_m_product b
        on a.M_PRODUCT_ID = b.ID and a.M_PRODUCTALIAS_ID = b.CODEID
        where b.ID=#{productId,jdbcType=DECIMAL}
        and a.C_STORE_ID in(417608,31066)
        AND a.QTY > 0
        group by a.M_PRODUCT_ID,b.NAME,b.COLORNO
    </select>

    <select id="getJNBYGatherStock" resultType="org.springcenter.product.modules.entity.ProductStockEntity">
        select     (case
        when sum(QTY-greatest(QTYPREOUT,0)) &lt;=0
        then 0
        else 1 end) as QTY,m_productalias_id  as SKUID
        from v_fa_storage
        where c_store_id in
        <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and m_productalias_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND QTY > 3
        group by m_productalias_id
        union
        select     (case
        when sum(QTY-greatest(QTYPREOUT,0)) &lt;=0
        then 0
        else 1 end) as QTY,m_productalias_id  as SKUID
        from v_fa_storage
        where c_store_id in (417608,31066)
        and m_productalias_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND QTY > 0
        group by m_productalias_id
    </select>
</mapper>
