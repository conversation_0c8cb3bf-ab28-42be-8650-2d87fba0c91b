<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.MPQAttrConnectionLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.MPQAttrConnectionLog">
        <id column="ID" property="id" />
        <result column="M_QUERIES_TAB_ID" property="mQueriesTabId" />
        <result column="MPQ_ATTR_CONNECT_IDS" property="mPQAttrConnectIds" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETE" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, M_QUERIES_TAB_ID, MPQ_ATTR_CONNECT_IDS, CREATE_TIME, UPDATE_TIME, IS_DELETE
    </sql>
    <select id="selectByAttrId" resultType="java.lang.String">
        select MPQ_ATTR_CONNECT_IDS from (
        select MPQ_ATTR_CONNECT_IDS from MPQ_ATTR_CONNECTION_LOG
        where M_QUERIES_TAB_ID = #{attrId} order by create_time desc)
        where rownum = 1
    </select>


</mapper>
