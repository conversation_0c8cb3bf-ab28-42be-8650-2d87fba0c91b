package org.springcenter.product.modules.service;

import org.springcenter.product.modules.model.ProductToppingTag;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/6/7 16:47
 */
public interface IProductToppingEsScriptService {

    void updateIndexInfoByRemoveEle(ProductToppingTag tag);

    void batchUpdateIndexInfoByAdd(List<ProductToppingTag> productToppingTags) throws IOException;
}
