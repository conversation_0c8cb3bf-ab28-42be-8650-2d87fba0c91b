package org.springcenter.product.modules.remote.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springcenter.product.modules.remote.BigDataCommonResponse;
import org.springcenter.product.modules.remote.IBigDataHttpApi;
import org.springcenter.product.modules.remote.entity.*;
import org.springcenter.product.modules.remote.service.IBigDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/4/28 9:23
 */
@Service
@Slf4j
public class BigDataServiceImpl implements IBigDataService {

    @Autowired
    private IBigDataHttpApi bigDataHttpApi;

    @Override
    public List<BigDataSearchRespEntity> searchByText(BigDataSearchReqEntity bigDataSearchReqEntity) {
        log.info("======================请求大数据搜索接口：{}", JSONObject.toJSONString(bigDataSearchReqEntity));
        List<BigDataSearchRespEntity> entities = new ArrayList<>();
        try {
            Response<BigDataCommonResponse<List<BigDataSearchFirstRespEntity>>> responseCall = bigDataHttpApi.searchByText(bigDataSearchReqEntity).execute();
            log.info("=======================返回参数：{}", JSONObject.toJSONString(responseCall));
            if (!responseCall.isSuccessful()) {
                log.error("=============================请求大数据接口报错，第一步：{}", JSONObject.toJSONString(responseCall));
                return entities;
            }
            if (!Objects.equals(responseCall.body().getCode(), 200)) {
                log.error("=============================请求大数据接口报错，第二步", JSONObject.toJSONString(responseCall));
                return entities;
            }
            BigDataSearchFirstRespEntity bigDataSearchFirstRespEntity = responseCall.body().getData().get(0);
            entities = bigDataSearchFirstRespEntity.getList();
        } catch (Exception e) {
            log.error("============================请求大数据接口报错，e:{}", e);
            return entities;
        }
        return entities;
    }


    @Override
    public Pair<List<BigDataComprehensiveData>, Integer> searchComprehensiveList(BigDataComprehensiveReq bigDataComprehensiveReq) {
        log.info("======================请求大数据综合排序接口：{}", JSONObject.toJSONString(bigDataComprehensiveReq));
        List<BigDataComprehensiveData> entities = new ArrayList<>();
        try {
            Response<BigDataComprehensiveResponse> responseCall = bigDataHttpApi.searchComprehensiveList(bigDataComprehensiveReq).execute();
            log.info("=======================返回参数：{}", JSONObject.toJSONString(responseCall));
            if (!responseCall.isSuccessful()) {
                log.error("=============================请求大数据综合排序接口报错，第一步：{}", JSONObject.toJSONString(responseCall));
                return Pair.of(entities, 0);
            }
            if (!Objects.equals(responseCall.body().getCode(), 200)) {
                log.error("=============================请求大数据综合排序接口报错，第二步", JSONObject.toJSONString(responseCall));
                return Pair.of(entities, 0);
            }
            return Pair.of(responseCall.body().getResult(), responseCall.body().getTotal());
        } catch (Exception e) {
            log.error("============================请求大数据接口报错，e:{}", e);
            return Pair.of(entities, 0);
        }
    }


    @Override
    public List<Integer> searchSingleRecommendation(BigDataSingleReconReq bigDataSingleReconReq) {
        log.info("======================请求大数据单品推荐接口：{}", JSONObject.toJSONString(bigDataSingleReconReq));
        List<Integer> productIds = new ArrayList<>();
        try {
            Response<BigDataSingleReconResp> responseCall = bigDataHttpApi.searchSingleRecommendation(bigDataSingleReconReq).execute();
            log.info("=======================返回参数：{}", JSONObject.toJSONString(responseCall));
            if (!responseCall.isSuccessful()) {
                log.error("=============================请求大数据单品推荐接口报错，第一步：{}", JSONObject.toJSONString(responseCall));

            }
            if (!Objects.equals(responseCall.body().getCode(), 200)) {
                log.error("=============================请求大数据单品推荐接口报错，第二步", JSONObject.toJSONString(responseCall));

            }
            BigDataSingleReconResp body = responseCall.body();
            productIds = body.getResult().stream().map(BigDataSingleReconResp.Result::getM_product_id).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("============================请求大数据接口报错，e:{}", e);

        }
        return productIds;
    }
}
