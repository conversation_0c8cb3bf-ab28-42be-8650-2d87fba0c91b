package org.springcenter.product.modules.mapper.product;

import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.JicMallStoreWarehouse;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/8/9 15:14
 */
public interface JicMallStoreWarehouseMapper {
    List<JicMallStoreWarehouse> selectByStoreCodeAndBrandId(@Param("vidCode") String vidCode, @Param("brandId") String brandId);

    List<String> selectByTypeAndBrandId(@Param("type") String type, @Param("weId") String weId);
}
