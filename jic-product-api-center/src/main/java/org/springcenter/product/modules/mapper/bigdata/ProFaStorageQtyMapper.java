package org.springcenter.product.modules.mapper.bigdata;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springcenter.product.modules.entity.EbAndWscSpuStockEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.bigdata.ProFaStorageQty;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 10:37
 */

public interface ProFaStorageQtyMapper extends BaseMapper<ProFaStorageQty> {
    List<EbAndWscSpuStockEntity> getEbAndWscSpuStorages(@Param("productIds") List<Long> productIds, @Param("storeIds") List<Long> storeIds);
}
