<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.CheckReportMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.CheckReport">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="REPORT_CODE" jdbcType="VARCHAR" property="reportCode" />
    <result column="REPORT_TYPE" jdbcType="DECIMAL" property="reportType" />
    <result column="FABIRC" jdbcType="VARCHAR" property="fabirc" />
    <result column="REPORT_FREIGHT_SEASON" jdbcType="VARCHAR" property="reportFreightSeason" />
    <result column="DELIVERY_CODE" jdbcType="VARCHAR" property="deliveryCode" />
    <result column="REPORT_STATUS" jdbcType="DECIMAL" property="reportStatus" />
    <result column="SYNC_DIR" jdbcType="VARCHAR" property="syncDir" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="IS_DEL" jdbcType="DECIMAL" property="isDel" />

    <result column="NEID" jdbcType="VARCHAR" property="neid" />
    <result column="NSID" jdbcType="VARCHAR" property="nsid" />
    <result column="FLAT_TYPE" jdbcType="VARCHAR" property="flatType" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, REPORT_CODE, REPORT_TYPE, FABIRC, REPORT_FREIGHT_SEASON, DELIVERY_CODE, REPORT_STATUS, 
    SYNC_DIR, CREATE_TIME, UPDATE_TIME, IS_DEL,NEID,NSID,FLAT_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CHECK_REPORT
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectBySyncDir" resultType="java.lang.Integer">
        select count(*) from CHECK_REPORT where SYNC_DIR = #{syncDir}
  </select>
  <select id="selectBySelective" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from CHECK_REPORT
    <where>
      <if test="id != null">
        and ID = #{id}
      </if>
      <if test="reportCode != null">
        and REPORT_CODE = #{reportCode,jdbcType=VARCHAR}
      </if>
      <if test="reportType != null">
        and REPORT_TYPE = #{reportType,jdbcType=DECIMAL}
      </if>
      <if test="fabirc != null">
        and FABIRC = #{fabirc,jdbcType=VARCHAR}
      </if>
      <if test="reportFreightSeason != null">
        and REPORT_FREIGHT_SEASON = #{reportFreightSeason,jdbcType=VARCHAR}
      </if>
      <if test="deliveryCode != null">
        and DELIVERY_CODE = #{deliveryCode,jdbcType=VARCHAR}
      </if>
      <if test="reportStatus != null">
        and REPORT_STATUS = #{reportStatus,jdbcType=DECIMAL}
      </if>
      <if test="syncDir != null">
        and SYNC_DIR = #{syncDir,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="isDel != null">
        and IS_DEL = #{isDel,jdbcType=DECIMAL}
      </if>
      <if test="flatType != null">
        and FLAT_TYPE = #{flatType,jdbcType=DECIMAL}
      </if>
    </where>
        order by create_time desc
  </select>
  <select id="selectByProductCode" resultMap="BaseResultMap">

    select * from (
        select
          cr.ID, cr.REPORT_CODE, cr.REPORT_TYPE, cr.FABIRC, cr.REPORT_FREIGHT_SEASON, cr.DELIVERY_CODE, cr.REPORT_STATUS,
          cr.SYNC_DIR, cr.CREATE_TIME, cr.UPDATE_TIME, cr.IS_DEL,cr.NEID, cr.NSID,cr.FLAT_TYPE
        from CHECK_REPORT cr left join CHECK_REPORT_PRODUCT crp on cr.id = crp.CHECK_REPORT_ID
        where crp.PRODUCT_CODE like  concat(#{productCode},'%')
        <if test="reportSeason != null and reportSeason != ''">
          and cr.REPORT_FREIGHT_SEASON = #{reportSeason}
        </if>
          and cr.report_Status = 1  and cr.flat_type = 1 and crp.is_del = 0 and cr.is_del = 0 order by cr.create_time desc
    ) where 4 >= rownum

  </select>
  <select id="selectByFabirc" resultMap="BaseResultMap">
    select * from (
    select <include refid="Base_Column_List"></include> from CHECK_REPORT
    where FABIRC like  concat(#{productCode},'%')
      <if test="reportSeason != null and reportSeason != ''">
        and REPORT_FREIGHT_SEASON = #{reportSeason}
      </if>
      and is_del = 0 and report_Status = 1 order by create_time desc
    ) where 4 >= rownum

  </select>

  <select id="selectByFabircFlatType" resultMap="BaseResultMap">
    select * from (
    select     cr.ID, cr.REPORT_CODE, cr.REPORT_TYPE, cr.FABIRC, cr.REPORT_FREIGHT_SEASON, cr.DELIVERY_CODE, cr.REPORT_STATUS,
    cr.SYNC_DIR, cr.CREATE_TIME, cr.UPDATE_TIME, cr.IS_DEL,cr.NEID, cr.NSID,cr.FLAT_TYPE
    from CHECK_REPORT cr left join CHECK_REPORT_PRODUCT crp on cr.id = crp.CHECK_REPORT_ID
    where cr.FABIRC  in
    <foreach collection="fabircs" item="item" separator="," close=")" open="(">
      #{item}
    </foreach>
    <if test="reportSeason != null and reportSeason != ''">
      and cr.REPORT_FREIGHT_SEASON = #{reportSeason}
    </if>
      <if test="productCode != null and productCode != '' ">
        and crp.PRODUCT_CODE like  #{productCode}
      </if>
    and cr.report_Status = 1  and cr.flat_type = 0 and crp.is_del = 0 and cr.is_del = 0 order by cr.create_time desc
    ) where 4 >= rownum

  </select>

    <select id="findList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from  CHECK_REPORT
        <where>
<!--            <if test="reportCodes != null and reportCodes.size() > 0">-->
<!--                and REPORT_CODE  in-->
<!--                <foreach collection="reportCodes" separator="," open="(" close=")" item="item">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->

<!--          <if test="fabircs != null and fabircs.size() > 0">-->
<!--            and FABIRC  in-->
<!--            <foreach collection="fabircs" separator="," open="(" close=")" item="item">-->
<!--              #{item}-->
<!--            </foreach>-->
<!--          </if>-->

          <if test="reportStatus != null">
            and REPORT_STATUS   = #{reportStatus}
          </if>
          <if test="reportCode != null and reportCode != '' ">
            and REPORT_CODE   like concat(concat('%',#{reportCode}),'%')
          </if>
          <if test="fabirc != null and fabirc != '' ">
            and FABIRC   like concat(concat('%',#{fabirc}),'%')
          </if>

          <if test="isDel != null">
            and IS_DEL  = #{isDel}
          </if>
          <if test="reportType != null">
            and REPORT_TYPE  = #{reportType}
          </if>
          <if test="reportFreightSeason != null">
            and REPORT_FREIGHT_SEASON  = #{reportFreightSeason}
          </if>

          <if test="notImport  == 1 ">
            and FABIRC is null
          </if>

        </where>



    </select>
  <select id="selectByReportCodes" resultType="java.lang.String">
        select     REPORT_CODE from CHECK_REPORT where is_del = 0 and REPORT_CODE in
                                                     <foreach collection="reportCodes" item="item" close=")" open="(" separator=",">
                                                       #{item}
                                                     </foreach>
                                                     and SYNC_DIR = #{dirName}
  </select>
  <select id="selectByLikeReportCode" resultMap="BaseResultMap"
          parameterType="org.springcenter.product.modules.model.CheckReport">


    select <include refid="Base_Column_List"></include> from CHECK_REPORT
    <where>
      <if test="reportCode != null">
        and REPORT_CODE like concat(#{reportCode,jdbcType=VARCHAR},'%')
      </if>
      <if test="syncDir != null">
        and SYNC_DIR = #{syncDir,jdbcType=VARCHAR}
      </if>
      <if test="isDel != null">
        and IS_DEL = #{isDel,jdbcType=DECIMAL}
      </if>
    </where>
    order by create_time desc


  </select>
  <select id="selectByProductCodes" resultMap="BaseResultMap">

    select
    cr.ID, cr.REPORT_CODE, cr.REPORT_TYPE, cr.FABIRC, cr.REPORT_FREIGHT_SEASON, cr.DELIVERY_CODE, cr.REPORT_STATUS,
    cr.SYNC_DIR, cr.CREATE_TIME, cr.UPDATE_TIME, cr.IS_DEL,cr.NEID, cr.NSID,cr.FLAT_TYPE
    from CHECK_REPORT cr left join CHECK_REPORT_PRODUCT crp on cr.id = crp.CHECK_REPORT_ID
    where crp.PRODUCT_CODE in
        <foreach collection="productCodes" item="item" separator="," close=")" open="(">
          #{item}
        </foreach>
    and  crp.is_del = 0 and cr.is_del = 0 order by cr.create_time desc

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CHECK_REPORT
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="org.springcenter.product.modules.model.CheckReport" useGeneratedKeys="true">
    insert into CHECK_REPORT (REPORT_CODE, REPORT_TYPE, FABIRC, 
      REPORT_FREIGHT_SEASON, DELIVERY_CODE, REPORT_STATUS, 
      SYNC_DIR, CREATE_TIME, UPDATE_TIME, 
      IS_DEL)
    values (#{reportCode,jdbcType=VARCHAR}, #{reportType,jdbcType=DECIMAL}, #{fabirc,jdbcType=VARCHAR}, 
      #{reportFreightSeason,jdbcType=VARCHAR}, #{deliveryCode,jdbcType=VARCHAR}, #{reportStatus,jdbcType=DECIMAL}, 
      #{syncDir,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isDel,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective"  parameterType="org.springcenter.product.modules.model.CheckReport" >
    insert into CHECK_REPORT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="reportCode != null">
        REPORT_CODE,
      </if>
      <if test="reportType != null">
        REPORT_TYPE,
      </if>
      <if test="fabirc != null">
        FABIRC,
      </if>
      <if test="reportFreightSeason != null">
        REPORT_FREIGHT_SEASON,
      </if>
      <if test="deliveryCode != null">
        DELIVERY_CODE,
      </if>
      <if test="reportStatus != null">
        REPORT_STATUS,
      </if>
      <if test="syncDir != null">
        SYNC_DIR,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="neid != null">
        NEID,
      </if>
      <if test="nsid != null">
        NSID,
      </if>
      <if test="flatType != null">
         FLAT_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="reportCode != null">
        #{reportCode,jdbcType=VARCHAR},
      </if>
      <if test="reportType != null">
        #{reportType,jdbcType=DECIMAL},
      </if>
      <if test="fabirc != null">
        #{fabirc,jdbcType=VARCHAR},
      </if>
      <if test="reportFreightSeason != null">
        #{reportFreightSeason,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCode != null">
        #{deliveryCode,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=DECIMAL},
      </if>
      <if test="syncDir != null">
        #{syncDir,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="neid != null">
        #{neid},
      </if>
      <if test="nsid != null">
        #{nsid},
      </if>
      <if test="flatType != null">
        #{flatType,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="false">
      INSERT ALL
      <foreach item="item" index="index" collection="list">
        into CHECK_REPORT
        <trim prefix="(" suffix=")" suffixOverrides=",">
          <if test="item.id != null">
            ID,
          </if>
          <if test="item.reportCode != null">
            REPORT_CODE,
          </if>
          <if test="item.reportType != null">
            REPORT_TYPE,
          </if>
          <if test="item.fabirc != null">
            FABIRC,
          </if>
          <if test="item.reportFreightSeason != null">
            REPORT_FREIGHT_SEASON,
          </if>
          <if test="item.deliveryCode != null">
            DELIVERY_CODE,
          </if>
          <if test="item.reportStatus != null">
            REPORT_STATUS,
          </if>
          <if test="item.syncDir != null">
            SYNC_DIR,
          </if>
          <if test="item.createTime != null">
            CREATE_TIME,
          </if>
          <if test="item.updateTime != null">
            UPDATE_TIME,
          </if>
          <if test="item.isDel != null">
            IS_DEL,
          </if>
          <if test="item.neid != null">
            NEID,
          </if>
          <if test="item.nsid != null">
            NSID,
          </if>
          <if test="item.flatType != null">
            FLAT_TYPE,
          </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
          <if test="item.id != null">
            #{item.id},
          </if>
          <if test="item.reportCode != null">
            #{item.reportCode,jdbcType=VARCHAR},
          </if>
          <if test="item.reportType != null">
            #{item.reportType,jdbcType=DECIMAL},
          </if>
          <if test="item.fabirc != null">
            #{item.fabirc,jdbcType=VARCHAR},
          </if>
          <if test="item.reportFreightSeason != null">
            #{item.reportFreightSeason,jdbcType=VARCHAR},
          </if>
          <if test="item.deliveryCode != null">
            #{item.deliveryCode,jdbcType=VARCHAR},
          </if>
          <if test="item.reportStatus != null">
            #{item.reportStatus,jdbcType=DECIMAL},
          </if>
          <if test="item.syncDir != null">
            #{item.syncDir,jdbcType=VARCHAR},
          </if>
          <if test="item.createTime != null">
            #{item.createTime,jdbcType=TIMESTAMP},
          </if>
          <if test="item.updateTime != null">
            #{item.updateTime,jdbcType=TIMESTAMP},
          </if>
          <if test="item.isDel != null">
            #{item.isDel,jdbcType=DECIMAL},
          </if>
          <if test="item.neid != null">
            #{item.neid},
          </if>
          <if test="item.nsid != null">
            #{item.nsid},
          </if>
          <if test="item.flatType != null">
            #{item.flatType,jdbcType=DECIMAL},
          </if>
        </trim>
      </foreach>
      SELECT 1 FROM DUAL
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.springcenter.product.modules.model.CheckReport">
    update CHECK_REPORT
    <set>
      <if test="reportCode != null">
        REPORT_CODE = #{reportCode,jdbcType=VARCHAR},
      </if>
      <if test="reportType != null">
        REPORT_TYPE = #{reportType,jdbcType=DECIMAL},
      </if>
      <if test="fabirc != null">
        FABIRC = #{fabirc,jdbcType=VARCHAR},
      </if>
      <if test="reportFreightSeason != null">
        REPORT_FREIGHT_SEASON = #{reportFreightSeason,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCode != null">
        DELIVERY_CODE = #{deliveryCode,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        REPORT_STATUS = #{reportStatus,jdbcType=DECIMAL},
      </if>
      <if test="syncDir != null">
        SYNC_DIR = #{syncDir,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="neid != null">
        NEID = #{neid},
      </if>
      <if test="nsid != null">
        NSID = #{nsid},
      </if>
      <if test="flatType != null">
        FLAT_TYPE = #{flatType},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.springcenter.product.modules.model.CheckReport">
    update CHECK_REPORT
    set REPORT_CODE = #{reportCode,jdbcType=VARCHAR},
      REPORT_TYPE = #{reportType,jdbcType=DECIMAL},
      FABIRC = #{fabirc,jdbcType=VARCHAR},
      REPORT_FREIGHT_SEASON = #{reportFreightSeason,jdbcType=VARCHAR},
      DELIVERY_CODE = #{deliveryCode,jdbcType=VARCHAR},
      REPORT_STATUS = #{reportStatus,jdbcType=DECIMAL},
      SYNC_DIR = #{syncDir,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      IS_DEL = #{isDel,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>