package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.StringUtil;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.common.system.vo.LoginUser;
import com.jnby.authority.common.system.vo.SysPermissionDataRuleModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springcenter.product.modules.service.CommonDataRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/8/2 13:22
 */
@Service
@Slf4j
public class CommonDataRuleServiceImpl implements CommonDataRuleService {


    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Override
    public List<String> getPeopleBrandDataRule(String component) {
        if (StringUtils.isBlank(component)) {
            return Collections.emptyList();
        }

        List<SysPermissionDataRuleModel> ruleModels = new ArrayList<>();
        try {
            LoginUser userInfo = sysBaseAPI.getCurrentLoginUserInfo();
            ruleModels = sysBaseAPI.queryPermissionDataRule(component, "", userInfo.getUsername());
            log.info("===============规则:{}", JSONObject.toJSONString(ruleModels));
        } catch (Exception e) {
            log.error("===============规则:{}", e);
        }

        if (CollectionUtils.isEmpty(ruleModels)) {
            return Collections.emptyList();
        }
        return ruleModels.stream().map(SysPermissionDataRuleModel::getRuleValue).collect(Collectors.toList());
    }
}
