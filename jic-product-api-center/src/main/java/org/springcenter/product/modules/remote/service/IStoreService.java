package org.springcenter.product.modules.remote.service;

import org.springcenter.product.modules.remote.JicBaseResp;
import org.springcenter.product.modules.remote.entity.StoreDetailReqEntity;
import org.springcenter.product.modules.remote.entity.StoreDetailRespEntity;
import retrofit2.Call;
import retrofit2.http.Body;

public interface IStoreService {

    /**
     * 获取门店详情
     * @param storeId
     * @return
     */
    JicBaseResp<StoreDetailRespEntity> detail(String storeId);
}
