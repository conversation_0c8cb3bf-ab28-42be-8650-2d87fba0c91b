package org.springcenter.product.modules.remote.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.modules.remote.IWeiMoRemoteApi;
import org.springcenter.product.modules.remote.JicBaseResp;
import org.springcenter.product.modules.remote.entity.GetListByWeimoGoodIdsReq;
import org.springcenter.product.modules.remote.entity.GetListByWeimoGoodIdsResp;
import org.springcenter.product.modules.remote.entity.WeimoGoodShareLinkReq;
import org.springcenter.product.modules.remote.entity.WeimoGoodShareLinkResp;
import org.springcenter.product.modules.remote.service.IWeiMoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/9/20 9:58
 */
@Service
@Slf4j
public class WeiMoServiceImpl implements IWeiMoService {

    @Autowired
    private IWeiMoRemoteApi weiMoRemoteApi;

    @Override
    public List<GetListByWeimoGoodIdsResp> getWeimoGoodIdsInfo(GetListByWeimoGoodIdsReq getListByWeimoGoodIdsReq) {
        log.info("weimo获取商品信息是否可售:{}",getListByWeimoGoodIdsReq);
        try {
            Response<JicBaseResp<List<GetListByWeimoGoodIdsResp>>> result = weiMoRemoteApi.weimoGoodIdsInfo(getListByWeimoGoodIdsReq).execute();
            if (!result.isSuccessful()) {
                throw new RuntimeException("weimo获取商品信息是否可售格失败接口异常" + result.errorBody());
            }
            JicBaseResp<List<GetListByWeimoGoodIdsResp>> response = result.body();
            if(!response.isSuccess()){
                throw new RuntimeException("weimo获取商品信息是否可售失败接口异常" + response.getMsg());
            }
            log.info("weimo获取商品信息是否可售接口，param={}, response={}", JSON.toJSONString(getListByWeimoGoodIdsReq),JSON.toJSONString(response.getData()));
            return response.getData();
        } catch (Exception e) {
            log.error("weimo获取商品信息是否可售失败接口异常，param={}", JSON.toJSONString(getListByWeimoGoodIdsReq), e);
            throw new RuntimeException("weimo获取商品信息是否可售格失败接口异常 e:" + e);
        }
    }


    @Override
    public List<WeimoGoodShareLinkResp> getWeimoGoodShareLink(WeimoGoodShareLinkReq weimoGoodShareLinkReq) {
        log.info("weimo获取商品信息h5链接:{}", weimoGoodShareLinkReq);
        try {
            Response<JicBaseResp<List<WeimoGoodShareLinkResp>>> result = weiMoRemoteApi.weimoGoodShareLink(weimoGoodShareLinkReq).execute();
            if (!result.isSuccessful()) {
                throw new RuntimeException("weimo获取商品信息h5链接失败接口异常" + result.errorBody());
            }
            JicBaseResp<List<WeimoGoodShareLinkResp>> response = result.body();
            if(!response.isSuccess()){
                throw new RuntimeException("weimo获取商品信息h5链接失败接口异常" + response.getMsg());
            }
            log.info("weimo获取商品信息h5链接接口，param={}, response={}", JSON.toJSONString(weimoGoodShareLinkReq),JSON.toJSONString(response.getData()));
            return response.getData();
        } catch (Exception e) {
            log.error("weimo获取商品信息h5链接失败接口异常，param={}", JSON.toJSONString(weimoGoodShareLinkReq), e);
            throw new RuntimeException("weimo获取商品信息h5链接失败接口异常 e:" + e);
        }
    }
}
