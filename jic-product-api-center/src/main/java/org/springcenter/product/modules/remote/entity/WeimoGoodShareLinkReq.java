package org.springcenter.product.modules.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/12/7 18:00
 */
@Data
public class WeimoGoodShareLinkReq {

    @ApiModelProperty(value = "品牌ID")
    private String brandId;

    @ApiModelProperty(value = "商品 ID列表，列表长度最大不得超过 20")
    private List<Long> goodsIdList;

    @ApiModelProperty(value = "是否生成小程序跳转页面携带参数。Y-生成；N-不生成, 默认Y")
    private String isQueryScene;

    @ApiModelProperty(value = "页面所属的组织ID")
    private Long vid;
}
