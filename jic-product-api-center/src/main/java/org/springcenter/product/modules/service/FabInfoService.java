package org.springcenter.product.modules.service;

import org.springcenter.product.api.dto.AddProductFabInfoReq;
import org.springcenter.product.modules.model.FabInfo;
import org.springcenter.product.modules.model.FabMatchingSugInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/9/19 13:18
 */
public interface FabInfoService {
    /**
     * 保存fab信息
     * @param requestData 入参
     * @return 返回
     */
    Boolean saveFabInfo(AddProductFabInfoReq requestData);


    /**
     * 根据商品id和款号查询穿着方式图片
     * @param productId 商品id
     * @param name 款号
     * @return 返沪穿着方式图片
     */
    FabInfo selectFabInfoByProductIdAndName(String productId, String name);


    /**
     * 根据商品id和款号批量获取fab信息
     * @param productIds 商品id
     * @return 返回
     */
    List<FabInfo> selectFabInfoByIdsAndNames(List<String> productIds);


    /**
     * 保存搭配建议
     * @param requestData 入参
     * @return 返回
     */
    Boolean saveMatching(AddProductFabInfoReq requestData);


    /**
     * 根据商品id和款号查询搭配建议
     * @param productId 商品id
     * @param name 款号
     * @return 返沪搭配建议
     */
    FabMatchingSugInfo selectFabMatchingByProductIdAndName(String productId, String name);
}
