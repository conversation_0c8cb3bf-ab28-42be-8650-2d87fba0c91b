package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.LabelMainRule;
import org.springcenter.product.modules.model.RuleResp;

import java.util.List;

public interface LabelMainRuleMapper extends BaseMapper<LabelMainRule> {

    void insertBatch(@Param("list") List<LabelMainRule> mainRuleList);

    void batchUpdate(@Param("list") List<LabelMainRule> updateList);

    List<RuleResp> findAll();
}
