package org.springcenter.product.modules.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.product.api.dto.ParentChildInfoReq;
import org.springcenter.product.api.dto.ParentChildInfoResp;
import org.springcenter.product.api.dto.ProductZhuiDanInfoResp;
import org.springcenter.product.modules.entity.ParentSubQinZiEsResp;
import org.springcenter.product.modules.entity.ProductZhuiDanInfoEsResp;
import org.springcenter.product.modules.service.IProductRelationInfoService;
import org.springcenter.product.modules.util.EsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/6/14 15:45
 */
@Service
@Slf4j
@RefreshScope
public class ProductRelationInfoServiceImpl implements IProductRelationInfoService {

    @Autowired
    private EsUtil esUtil;

    @Value("${zhuidan.index}")
    private String zhuidanIndex;

    @Value("${qinzi.index}")
    private String qinZiIndex;


    @Override
    public List<ProductZhuiDanInfoResp> queryProductZhuiDanInfo(List<Long> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            log.error("=================queryProductZhuiDanInfo入参为空");
        }
        SearchRequest request = new SearchRequest();
        request.indices(zhuidanIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("search_id", requestData));

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(100);
        sourceBuilder.sort("rela_id", SortOrder.DESC);

        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品追单or原单-ES {}", request.source().toString());
        List<ProductZhuiDanInfoEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }

            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductZhuiDanInfoEsResp entity = ProductZhuiDanInfoEsResp.fromJson(hits[i].getSourceAsString(), ProductZhuiDanInfoEsResp.class);
                entity.buildSku();
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品追单or原单-ES异常e = {}", e.getMessage());
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(entities)) {
            return new ArrayList<>();
        } else {
            return entities.stream().map(entity -> {
                ProductZhuiDanInfoResp infoResp = new ProductZhuiDanInfoResp();
                infoResp.setProductId(entity.getRela_id());
                infoResp.setName(entity.getName());
                infoResp.setValue(entity.getValue());
                infoResp.setPrice(entity.getPrice());
                infoResp.setImg(entity.getCover_imgs());
                infoResp.setIsOriginSpu(entity.getIs_origin_spu());
                if (CollectionUtils.isNotEmpty(entity.getSkus())) {
                    List<ProductZhuiDanInfoResp.Sku> skus = new ArrayList<>();
                    entity.getSkus().forEach(v -> {
                        ProductZhuiDanInfoResp.Sku sku = new ProductZhuiDanInfoResp.Sku();
                        sku.setNo(v.getNo());
                        sku.setId(v.getId());
                        sku.setColorName(v.getColor_name());
                        sku.setColorNo(v.getColorno());
                        sku.setImgUrl(v.getImgurl());
                        sku.setGbCode(v.getGbcode());
                        sku.setStylePartSizeModel(v.getStylepartsize_model());
                        sku.setMattedUrl(v.getMatted_url());
                        sku.setSizeNo(v.getSizeno());
                        sku.setSizeName(v.getSize_name());
                        skus.add(sku);
                    });
                    infoResp.setSkus(skus);
                }
                return infoResp;
            }).collect(Collectors.toList());
        }
    }

    @Override
    public List<ParentChildInfoResp> queryProductParentChildInfo(ParentChildInfoReq requestData) {
        SearchRequest request = new SearchRequest();
        request.indices(qinZiIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("one_id", requestData.getProductId()));

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(100);
        sourceBuilder.sort("_id", SortOrder.ASC);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品亲子款-ES {}", request.source().toString());
        List<ParentSubQinZiEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }

            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ParentSubQinZiEsResp entity = ParentSubQinZiEsResp.fromJson(hits[i].getSourceAsString(), ParentSubQinZiEsResp.class);
                entity.buildSku();
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品亲子款-ES异常e = {}", e.getMessage());
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(entities)){
            return new ArrayList<>();
        } else {
            return entities.stream().map(entity -> {
                ParentChildInfoResp infoResp = new ParentChildInfoResp();
                infoResp.setProductId(entity.getTwo_id());
                infoResp.setName(entity.getTwo_name());
                infoResp.setValue(entity.getTwo_value());
                infoResp.setPrice(entity.getTwo_price());
                infoResp.setCoverImg(entity.getTwo_cover_imgs());
                infoResp.setCArcbrandId(entity.getTwo_c_arcbrand_id());
                if (CollectionUtils.isNotEmpty(entity.getSkus())) {
                    List<ParentChildInfoResp.Sku> skus = new ArrayList<>();
                    entity.getSkus().forEach(v -> {
                        ParentChildInfoResp.Sku sku = new ParentChildInfoResp.Sku();
                        sku.setNo(v.getNo());
                        sku.setId(v.getId());
                        sku.setColorName(v.getColor_name());
                        sku.setColorNo(v.getColorno());
                        sku.setImgUrl(v.getImgurl());
                        sku.setGbCode(v.getGbcode());
                        sku.setStylePartSizeModel(v.getStylepartsize_model());
                        sku.setMattedUrl(v.getMatted_url());
                        sku.setSizeNo(v.getSizeno());
                        sku.setSizeName(v.getSize_name());
                        skus.add(sku);
                    });
                    infoResp.setSkus(skus);
                }
                return infoResp;
            }).collect(Collectors.toList());
        }
    }
}
