package org.springcenter.product.modules.mapper.product;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.api.dto.DisassociateSceneLabelsReq;
import org.springcenter.product.api.dto.UpdateLabelSceneReq;
import org.springcenter.product.modules.model.BEbStore;
import org.springcenter.product.modules.model.SceneLabelConnection;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 16:47
 */
@Mapper
public interface SceneLabelConnectionMapper extends BaseMapper<SceneLabelConnection> {

    void updateDisRelLabelCodeByLabelCode(@Param("labelCode") String labelCode, @Param("name") String name);

    void batchInsert(@Param("list") List<SceneLabelConnection> connections);

    List<SceneLabelConnection> selectSceneByLabelCode(@Param("labelCode") String labelCode);

    void batchUpdate(@Param("list") List<UpdateLabelSceneReq.updateDataEntity> connections, @Param("name") String name);

    void batchUpdateById(@Param("name") String name, @Param("list") List<DisassociateSceneLabelsReq> list);

    List<SceneLabelConnection> selectListBySceneIs(@Param("sceneId") String id);

    List<SceneLabelConnection> querySceneLabelConnectionBySceneKey(@Param("key") String key);
}
