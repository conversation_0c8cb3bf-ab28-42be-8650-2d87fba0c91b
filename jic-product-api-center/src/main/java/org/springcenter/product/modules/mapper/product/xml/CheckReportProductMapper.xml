<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.CheckReportProductMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.CheckReportProduct">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CHECK_REPORT_ID" jdbcType="VARCHAR" property="checkReportId" />
    <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode" />
    <result column="IS_DEL" jdbcType="DECIMAL" property="isDel" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, CHECK_REPORT_ID, PRODUCT_CODE, IS_DEL, CREATE_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CHECK_REPORT_PRODUCT
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CHECK_REPORT_PRODUCT
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="org.springcenter.product.modules.model.CheckReportProduct" useGeneratedKeys="true">
    insert into CHECK_REPORT_PRODUCT (CHECK_REPORT_ID, PRODUCT_CODE, IS_DEL, 
      CREATE_TIME, UPDATE_TIME)
    values (#{checkReportId,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, #{isDel,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="org.springcenter.product.modules.model.CheckReportProduct" useGeneratedKeys="true">
    insert into CHECK_REPORT_PRODUCT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="checkReportId != null">
        CHECK_REPORT_ID,
      </if>
      <if test="productCode != null">
        PRODUCT_CODE,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="checkReportId != null">
        #{checkReportId,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
    <insert id="batchInsert">
      INSERT ALL
      <foreach item="item" index="index" collection="list">
        INTO CHECK_REPORT_PRODUCT
        (ID, CHECK_REPORT_ID, PRODUCT_CODE, IS_DEL, CREATE_TIME, UPDATE_TIME) VALUES
        (#{item.id,jdbcType=VARCHAR}, #{item.checkReportId,jdbcType=VARCHAR}, #{item.productCode},
        #{item.isDel},  #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
      </foreach>
      SELECT 1 FROM DUAL

  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.springcenter.product.modules.model.CheckReportProduct">
    update CHECK_REPORT_PRODUCT
    <set>
      <if test="checkReportId != null">
        CHECK_REPORT_ID = #{checkReportId,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        PRODUCT_CODE = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.springcenter.product.modules.model.CheckReportProduct">
    update CHECK_REPORT_PRODUCT
    set CHECK_REPORT_ID = #{checkReportId,jdbcType=VARCHAR},
      PRODUCT_CODE = #{productCode,jdbcType=VARCHAR},
      IS_DEL = #{isDel,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByCheckReportId">
    update  CHECK_REPORT_PRODUCT set is_del  = 1 where CHECK_REPORT_ID = #{checkReportId}
  </update>
</mapper>