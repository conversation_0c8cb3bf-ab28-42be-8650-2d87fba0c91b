package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.SysProductCalcLabel;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/10/18 9:48
 */
public interface SysProductCalcLabelMapper extends BaseMapper<SysProductCalcLabel> {
    void insertBatch(@Param("list") List<SysProductCalcLabel> productCalcLabels);

    void updateAll();
}
