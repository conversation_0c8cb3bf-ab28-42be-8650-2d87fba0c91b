<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.PreSaleProductModelPicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.PreSaleProductModelPic">
        <id column="ID" property="id" />
        <result column="SPU" property="spu" />
        <result column="FOLDER_NAME" property="folderName" />
        <result column="MODEL_URL" property="modelUrl" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="ABBREVIATE_MODEL_URL" property="abbreviateModelUrl" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SPU, FOLDER_NAME, MODEL_URL, IS_DELETED, CREATE_TIME, UPDATE_TIME, ABBREVIATE_MODEL_URL
    </sql>



    <select id="selectByModelNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM PRE_SALE_PRODUCT_MODEL_PIC
        where IS_DELETED = 0 and
        SPU = #{spu}
    </select>
    <select id="selectModelUrlByModelNo" resultType="java.lang.String">
        SELECT ABBREVIATE_MODEL_URL
        FROM PRE_SALE_PRODUCT_MODEL_PIC
        where IS_DELETED = 0 and
        SPU in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by CREATE_TIME desc
    </select>

    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO PRE_SALE_PRODUCT_MODEL_PIC
            (ID, SPU, FOLDER_NAME, MODEL_URL, CREATE_TIME, UPDATE_TIME, ABBREVIATE_MODEL_URL) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.spu,jdbcType=VARCHAR}, #{item.folderName,jdbcType=VARCHAR}, #{item.modelUrl,jdbcType=VARCHAR},
             #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.abbreviateModelUrl,jdbcType=VARCHAR})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update PRE_SALE_PRODUCT_MODEL_PIC
            set
            MODEL_URL = #{item.modelUrl},
            ABBREVIATE_MODEL_URL = #{item.abbreviateModelUrl},
            UPDATE_TIME = #{item.updateTime}
            where ID = #{item.id}
        </foreach>
    </update>


</mapper>
