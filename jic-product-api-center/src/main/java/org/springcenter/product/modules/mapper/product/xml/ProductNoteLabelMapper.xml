<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.ProductNoteLabelMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.LabelNoteInfo">
    <result column="ID" property="id" />
    <result column="LABEL_ID" property="labelId" />
    <result column="LABEL_NAME" property="labelName" />
    <result column="LABEL_CODE" property="labelCode" />
    <result column="LABEL_NOTE" property="labelNote" />
    <result column="OPERATOR" property="operator" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="IS_DELETED" property="isDeleted" />
  </resultMap>



</mapper>