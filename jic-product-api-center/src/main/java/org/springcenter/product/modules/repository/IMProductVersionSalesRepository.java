package org.springcenter.product.modules.repository;

import org.springcenter.product.modules.model.MProductVersion;
import org.springcenter.product.modules.model.MProductVersionSales;
import org.springcenter.product.api.dto.ProductVersionSalesReq;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/10 15:45
 */
public interface IMProductVersionSalesRepository {
    List<MProductVersionSales> selectByParams(ProductVersionSalesReq requestData, String sampleCode);

    Pair<BigDecimal, BigDecimal> selectSumByParam(ProductVersionSalesReq requestData, String sampleCode);

    Integer querySalesByModelNumberOrSampleCode(String modelNumber, String sampleNo);
}
