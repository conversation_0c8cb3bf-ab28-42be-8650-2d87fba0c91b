<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.MProductVersionMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.MProductVersion">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="brandCode" column="BRAND_CODE" jdbcType="VARCHAR"/>
        <result property="brand" column="BRAND" jdbcType="VARCHAR"/>
        <result property="sampleNo" column="SAMPLE_NO" jdbcType="VARCHAR"/>
        <result property="categoryCode" column="CATEGORY_CODE" jdbcType="VARCHAR"/>
        <result property="category" column="CATEGORY" jdbcType="VARCHAR"/>
        <result property="degreeFitCode" column="DEGREE_FIT_CODE" jdbcType="VARCHAR"/>
        <result property="modelNumber" column="MODEL_NUMBER" jdbcType="VARCHAR"/>
        <result property="mainIngredient" column="MAIN_INGREDIENT" jdbcType="VARCHAR"/>
        <result property="mainIngredientNumber" column="MAIN_INGREDIENT_NUMBER" jdbcType="VARCHAR"/>
        <result property="isClose" column="IS_CLOSE" jdbcType="DECIMAL"/>
        <result property="isDelete" column="IS_DELETE" jdbcType="DECIMAL"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="smallName" column="SMALL_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,BRAND_CODE,BRAND,
        SAMPLE_NO,CATEGORY_CODE,CATEGORY,
        DEGREE_FIT_CODE,MODEL_NUMBER,MAIN_INGREDIENT,
        MAIN_INGREDIENT_NUMBER,IS_CLOSE,IS_DELETE,
        CREATE_TIME,UPDATE_TIME,CATEGORY_NUMBER,
        DEGREE,BODY_SIZE,COLLAR,
        SHOULDER,SLEEVE,SLEEVE_LENGTH,
        CLOTH_LENGTH,PANTS_TYPE,PANTS_LENGTH,
        SKIRTS_TYPE,SKIRTS_LENGTH,WAIST_SHAPE1,
        WAIST_SHAPE2,CHEST,WAIST,
        PLACE_AROUND,HIT,NECK_LINE,
        NECK_HORIZONTAL,NECK_FRONT,SHOULDER_BREATH,
        CLAMP_RING,SUIT_BICEP,SUIT_BACK_MIDDLE,
        LENGTH_SHOULDER_BREATH,PANTS_SIZE,PANTS_OF_FOOT,
        LENGTH_OF_SKIRT,SKIRT_PLACE_AROUND,WAIST_TYPE, SMALL_NAME
    </sql>

    <resultMap id="queryListResultMap" type="org.springcenter.product.modules.model.ProductVersionEntity">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="sampleNo" column="SAMPLE_NO" jdbcType="VARCHAR"/>
        <result property="degree" column="degree" jdbcType="VARCHAR"/>
        <result property="modelNumber" column="MODEL_NUMBER" jdbcType="VARCHAR"/>
        <result property="citationNumber" column="CITATIONS" jdbcType="DECIMAL"/>
        <result property="category" column="CATEGORY" jdbcType="VARCHAR"/>
        <result property="picUrl" column="picUrl" jdbcType="VARCHAR"/>
        <result property="smallName" column="SMALL_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectQueryList" resultMap="queryListResultMap">
        select a.id, a.sample_no, a.degree, a.model_number, a.category, c.CITATIONS, a.SMALL_NAME
        from M_PRODUCT_VERSION a
            left join M_PRODUCT_VERSION_CITATIONS c on a.MODEL_NUMBER = c.MODEL_NUMBER
    </select>
    <select id="selectQueryListByParams" resultMap="queryListResultMap">
        select a.id, a.sample_no, a.model_number, a.category, c.CITATIONS as CITATIONS,
                a.SMALL_NAME
        from M_PRODUCT_VERSION a left join M_PRODUCT_VERSION_CITATIONS c on a.sample_no = c.MODEL_NUMBER
        left join MP_QUE_ATTR_VALUE_CONNECT c on a.ID = c.MV_PRODUCT_ID and c.IS_DELETE = 0
        left join M_QUERIES_ATTR_CONNECTION d on d.ID = c.QUE_CONNECT_ID and d.IS_DELETE = 0
        <where>
            <if test="samples!= null and samples.size()>0">
                and a.sample_no in
                <foreach collection="samples" item="sampleNO" open="(" close=")" separator=",">
                    #{sampleNO}
                </foreach>
            </if>
            <if test="name != null and name != ''">
                or ( a.sample_no like concat(concat('%',#{name}),'%')
                          or a.model_number like concat(concat('%',#{name}),'%')
                          or a.MAIN_INGREDIENT_NUMBER like concat(concat('%',#{name}),'%')
                          or a.SMALL_NAME like concat(concat('%',#{name}),'%'))
            </if>
            <if test="brands!= null and brands.size()>0">
                and a.BRAND_CODE in
                <foreach collection="brands" item="brand" open="(" close=")" separator=",">
                    #{brand}
                </foreach>
            </if>
            <if test="categories!= null and categories.size()>0">
                and a.category in
                <foreach collection="categories" item="category" open="(" close=")" separator=",">
                    #{category}
                </foreach>
            </if>
            <if test="attrs != null and attrs.size()>0">
            and c.VALUE in
                <foreach collection="attrs" item="attrs" open="(" close=")" separator=",">
                    #{attrs}
                </foreach>
            </if>
            <if test="isClosed != null">
                and a.IS_CLOSE = #{isClosed}
            </if>
        </where>
        group by
        a.id,
        a.sample_no,
        a.model_number,
        a.category,
        a.small_name,
        c.CITATIONS
        <if test="sort == 0">
            order by
            SUBSTR(a.model_number, 1, REGEXP_INSTR(a.model_number,'[[:digit:]]') - 1) ,
            TO_NUMBER(SUBSTR(a.model_number, REGEXP_INSTR(a.model_number,'[[:digit:]]'), instr(a.model_number,'-') - (REGEXP_INSTR(a.model_number,'[[:digit:]]')) )) ,
            TO_NUMBER(SUBSTR(a.model_number, instr(a.model_number,'-') + 1, 10))
        </if>
        <if test="sort == 1">
            order by c.CITATIONS desc nulls last, a.id
        </if>
    </select>

    <update id="updateByEntityId" parameterType="org.springcenter.product.modules.model.MProductVersion">
        update M_PRODUCT_VERSION
        set BRAND_CODE = #{brandCode,jdbcType=VARCHAR},
            BRAND = #{brand,jdbcType=VARCHAR},
            MAIN_INGREDIENT = #{mainIngredient,jdbcType=VARCHAR},
            MAIN_INGREDIENT_NUMBER = #{mainIngredientNumber,jdbcType=VARCHAR},
            IS_CLOSE = #{isClose,jdbcType=DECIMAL},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            SMALL_NAME = #{smallName, jdbcType=VARCHAR}
        where ID = #{id,jdbcType=VARCHAR}
    </update>

    <update id="batchUpdateMp">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update M_PRODUCT_VERSION
            set
            BRAND_CODE = #{item.brandCode,jdbcType=VARCHAR},
            BRAND = #{item.brand,jdbcType=VARCHAR},
            SAMPLE_NO = #{item.sampleNo,jdbcType=VARCHAR},
            CATEGORY_CODE = #{item.categoryCode,jdbcType=VARCHAR},
            CATEGORY = #{item.category,jdbcType=VARCHAR},
            DEGREE_FIT_CODE = #{item.degreeFitCode,jdbcType=VARCHAR},
            MODEL_NUMBER = #{item.modelNumber,jdbcType=VARCHAR},
            MAIN_INGREDIENT = #{item.mainIngredient,jdbcType=VARCHAR},
            MAIN_INGREDIENT_NUMBER = #{item.mainIngredientNumber,jdbcType=VARCHAR},
            IS_CLOSE = #{item.isClose,jdbcType=DECIMAL},
            UPDATE_TIME = #{item.updateTime,jdbcType=TIMESTAMP},
            SMALL_NAME = #{item.smallName,jdbcType=VARCHAR}
            where ID = #{item.id}
        </foreach>
    </update>



</mapper>
