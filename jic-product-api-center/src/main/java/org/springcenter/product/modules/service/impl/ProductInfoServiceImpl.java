package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.dto.ProductLifestyleInfoReq;
import org.springcenter.product.api.dto.ProductLifestyleInfoResp;
import org.springcenter.product.modules.mapper.bojun.MProductMapper;
import org.springcenter.product.modules.model.bojun.MProduct;
import org.springcenter.product.modules.service.IProductInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Date:2024/7/1 10:59
 */
@Service
@Slf4j
@RefreshScope
public class ProductInfoServiceImpl implements IProductInfoService {

    @Autowired
    private MProductMapper mProductMapper;

    @Value("${lifestyle.str.tag}")
    private String lifestyleStrTag;

    @Value("${new.tag}")
    private Long newTag;

    @Override
    public List<ProductLifestyleInfoResp> queryProductLifestyle(ProductLifestyleInfoReq req) {
        QueryWrapper<MProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("ID", req.getProductIds());
        List<MProduct> list = mProductMapper.selectList(queryWrapper);

        Map<String, String> lifeMap = JSONObject.parseObject(lifestyleStrTag, Map.class);
        List<ProductLifestyleInfoResp> rets = new ArrayList<>();
        HashMap<Long, Long> map = list.stream().collect(HashMap::new, (k, v) -> k.put(v.getId(), v.getMDim63Id()), HashMap::putAll);
        req.getProductIds().stream().distinct().forEach(id -> {
            ProductLifestyleInfoResp resp = new ProductLifestyleInfoResp();
            resp.setProductId(id);
            if (MapUtils.isEmpty(map) || MapUtils.isEmpty(lifeMap)) {
                resp.setProductTag("");
                resp.setProductStatus(0L);
                resp.setProductAttr(null);
            } else {
                Long mDim63 = map.get(id);
                if (mDim63 == null) {
                    resp.setProductTag("");
                    resp.setProductStatus(0L);
                    resp.setProductAttr(null);
                } else {
                    String life = lifeMap.get(Objects.toString(mDim63));
                    resp.setProductTag(StringUtils.split(life, ';')[0]);
                    resp.setProductStatus(mDim63);
                    resp.setProductAttr(Integer.valueOf(StringUtils.split(life, ';')[1]));
                }
            }
            rets.add(resp);
        });

        return rets;
    }

    @Override
    public Map<Long, Integer> queryNewProduct(List<Long> productIds) {
        QueryWrapper<MProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("ID", productIds);
        List<MProduct> list = mProductMapper.selectList(queryWrapper);

        Map<Long, Integer> map = new HashMap<>();
        HashMap<Long, Long> productMap = list.stream().collect(HashMap::new, (k, v) -> k.put(v.getId(), v.getMDim63Id()), HashMap::putAll);
        productIds.forEach(v -> {
            if (MapUtils.isEmpty(productMap)) {
                // 0代表不是新品 1代表是新品
                map.put(v, 0);
            } else {
                if (productMap.get(v) == null) {
                    // 0代表不是新品 1代表是新品
                    map.put(v, 0);
                } else {
                    map.put(v, Objects.equals(productMap.get(v), newTag) ? 1 : 0);
                }
            }
        });
        return map;
    }
}
