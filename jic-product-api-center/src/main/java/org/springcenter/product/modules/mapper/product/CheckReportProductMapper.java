package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.CheckReportProduct;
import org.springcenter.product.modules.model.FabInfo;

import java.util.List;

public interface CheckReportProductMapper extends BaseMapper<CheckReportProduct> {
    int deleteByPrimaryKey(String id);

    int insert(CheckReportProduct record);

    int insertSelective(CheckReportProduct record);

    CheckReportProduct selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CheckReportProduct record);

    int updateByPrimaryKey(CheckReportProduct record);

    void batchInsert(@Param("list") List<CheckReportProduct> list);

    void updateByCheckReportId(@Param("checkReportId") String checkReportId);
}