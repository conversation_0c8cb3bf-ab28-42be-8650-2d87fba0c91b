<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.bojun.MdimMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.bojun.Mdim">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="M_DIMDEF_ID" jdbcType="DECIMAL" property="mDimdefId" />
    <result column="ATTRIBCODE" jdbcType="VARCHAR" property="attribcode" />
    <result column="ATTRIBNAME" jdbcType="VARCHAR" property="attribname" />
    <result column="DIMFLAG" jdbcType="VARCHAR" property="dimflag" />
    <result column="AD_CLIENT_ID" jdbcType="DECIMAL" property="adClientId" />
    <result column="AD_ORG_ID" jdbcType="DECIMAL" property="adOrgId" />
    <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
    <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
    <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
    <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
    <result column="MODIFIERID" jdbcType="DECIMAL" property="modifierid" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="C_CORP_ID" jdbcType="DECIMAL" property="cCorpId" />
    <result column="ORDER_CAP" jdbcType="DECIMAL" property="orderCap" />
    <result column="CCKU_QTY" jdbcType="DECIMAL" property="cckuQty" />
    <result column="ATTRIB_ENAME" jdbcType="VARCHAR" property="attribEname" />
    <result column="M_DIMDL_ID" jdbcType="DECIMAL" property="mDimdlId" />
    <result column="RATE" jdbcType="DECIMAL" property="rate" />
    <result column="IS_TO_BPOS" jdbcType="CHAR" property="isToBpos" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, M_DIMDEF_ID, ATTRIBCODE, ATTRIBNAME, DIMFLAG, AD_CLIENT_ID, AD_ORG_ID, ISACTIVE,
    CREATIONDATE, OWNERID, MODIFIEDDATE, MODIFIERID, DESCRIPTION, C_CORP_ID, ORDER_CAP,
    CCKU_QTY, ATTRIB_ENAME, M_DIMDL_ID, RATE, IS_TO_BPOS
  </sql>



  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from M_DIM
    where ID = #{id,jdbcType=DECIMAL} AND ISACTIVE = 'Y'
  </select>



</mapper>
