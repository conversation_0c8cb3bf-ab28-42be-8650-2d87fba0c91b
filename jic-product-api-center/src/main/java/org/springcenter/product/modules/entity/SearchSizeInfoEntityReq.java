package org.springcenter.product.modules.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/12/25 10:09
 */
@Data
public class SearchSizeInfoEntityReq {
    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "年份")
    private List<String> years;

    @ApiModelProperty(value = "波段id")
    private List<String> bandIds;

    @ApiModelProperty(value = "品牌id")
    private List<Long> brandIds;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "品类")
    private List<String> smallCategoryIds;

    @ApiModelProperty(value = "标签")
    private List<String> mustLabels;

    @ApiModelProperty(value = "小季节")
    private List<String> seasonIds;

    @ApiModelProperty(value = "款号")
    private String name;

    @ApiModelProperty(value = "品牌名称")
    private List<String> brandNames;
}
