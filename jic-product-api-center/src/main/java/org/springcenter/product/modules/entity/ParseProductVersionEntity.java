package org.springcenter.product.modules.entity;

import org.springcenter.product.modules.model.MPQueAttrValueConnect;
import org.springcenter.product.modules.model.MProductVersion;
import org.springcenter.product.modules.model.MProductVersionPic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/5 9:41
 */
@Data
public class ParseProductVersionEntity {

    private List<MProductVersion> baseDataList;

    private List<ErrorData> errorDataList;

    private List<MProductVersionPic> pics;

    private List<MPQueAttrValueConnect> mpQueAttrValueConnects;

    @Data
    public static class ErrorData {

        private String modelNumber;

        private String brand;

        private String sampleNo;

        private String msg;

        public ErrorData(String modelNumber, String brand, String sampleNo, String msg) {
            this.modelNumber = modelNumber;
            this.brand = brand;
            this.sampleNo = sampleNo;
            this.msg = msg;
        }

        public ErrorData() {
        }
    }
}
