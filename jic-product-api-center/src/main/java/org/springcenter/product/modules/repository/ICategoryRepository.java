package org.springcenter.product.modules.repository;

import org.springcenter.product.modules.model.Dim;
import org.springcenter.product.modules.model.MColor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/29/21 11:15 AM
 */
public interface ICategoryRepository {

    /**
     * 通用类目属性查询
     * @param dimFlag
     * @return
     */
    List<Dim> findDims(String dimFlag);

    /**
     * 查询所有的属性分类
     * @return
     */
    List<Dim> findAll();

    /**
     *  根据色号批量获取颜色
     * @return
     */
    List<MColor> selectColorByValue(List<String> list);

}
