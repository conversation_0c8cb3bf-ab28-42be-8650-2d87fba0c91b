<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.bojun.MProductMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.bojun.MProduct">
        <id column="ID"  property="id" />
        <result column="M_PRODUCT_ORIG" property="mProductOrig" />
    </resultMap>
    <sql id="Base_Column_List">
        ID, M_PRODUCT_ORIG
    </sql>

    <select id="selectByNames" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM m_product
        WHERE M_PRODUCT_ORIG = #{name}
    </select>


    <select id="selectNameByOrigName" resultType="java.lang.String">
        select name
        from m_product
        where M_PRODUCT_ORIG in
        <foreach item="item" index="index" collection="origNames" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectInfoByNames" resultType="org.springcenter.product.api.dto.ProductInfoByNo">
        select name as productNo, id as productId, PRICELIST as priceList
        from m_product
        where NAME in
        <foreach item="item" index="index" collection="names" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="selectCouponRuleByRuleId" resultType="java.lang.String">
        select get_fitler_sql(product_filter) from voucher_rule where ruleid = #{id}
    </select>


    <select id="selectByCouponRuleAndOtherParams" resultType="java.lang.String">
        select a.Id as ID , a.M_PRODUCT_ORIG as origName from m_product a
        LEFT JOIN M_PRODUCT_LIST b
        ON a.ID = b.M_PRODUCT_ID
        where
        b.DATE_LIST <![CDATA[ <= ]]> #{nowDate}
        <if test="lSql != null and lSql !=''">
            and a.id ${lSql}
        </if>
        order by a.M_DIM63_ID asc, a.id desc
    </select>

</mapper>
