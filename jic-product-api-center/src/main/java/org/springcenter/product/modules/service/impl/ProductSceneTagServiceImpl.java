package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.common.system.vo.SysCategoryModel;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.SceneLabelEnum;
import org.springcenter.product.job.gio.RandomKeyUtil;
import org.springcenter.product.modules.convert.ProductSceneLabelConvert;
import org.springcenter.product.modules.model.*;
import org.springcenter.product.modules.repository.IProductSceneTagRepository;
import org.springcenter.product.modules.service.IProductSceneTagService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/5/19 10:55
 */
@Service
@Slf4j
public class ProductSceneTagServiceImpl implements IProductSceneTagService {

    @Autowired
    private IProductSceneTagRepository productSceneTagRepository;

    @Value("${product.label.scene.leaf.tag}")
    private String labelSceneLeafTag;

    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Value("${first.label.order.str}")
    private String firstLabelOrderStr;

    @Override
    public Boolean addLabelScene(AddLabelSceneReq requestData) {
        judgeSameScene(requestData.getSceneDataList().stream().map(AddLabelSceneReq.SceneData::getSceneId).collect(Collectors.toList()));

        // 判断是否要添加  倒数第二层才添加场景值
        Boolean isPenultimateLevel = judgeIsPenultimateLevel(requestData.getLabelCode());

        if (!isPenultimateLevel) {
            return true;
        }

        // 获取当前操作人
        String name = "";
        try {
            name = sysBaseAPI.getCurrentLoginUserInfo() == null ? "系统" : sysBaseAPI.getCurrentLoginUserInfo().getRealname();
        } catch (Exception e) {
            log.error("获取当前登录用户信息有误");
        }

        // 组装参数
        String finalName = name;
        List<SceneLabelConnection> saveList = new ArrayList<>();

        requestData.getSceneDataList().forEach(v -> {
            SceneLabelConnection connection = new SceneLabelConnection();
            connection.setSceneId(v.getSceneId());
            connection.setId(IdLeaf.getDateId(labelSceneLeafTag));
            connection.setCreateTime(new Date());
            connection.setLabelCode(requestData.getLabelCode());
            connection.setSort(v.getSort());
            connection.setUpdateTime(new Date());
            connection.setOperator(finalName);
            saveList.add(connection);
        });

        productSceneTagRepository.saveLabelSceneConnections(saveList);
        return true;
    }

    private Boolean judgeIsPenultimateLevel(String labelCode) {
        List<SysCategoryModel> sysCategoryModels = sysBaseAPI.queryAllDSysCategoryByCode(labelCode);
        if (CollectionUtils.isEmpty(sysCategoryModels)) {
            return false;
        }
        if (sysCategoryModels.get(0).isLeaf()) {
            return true;
        }

        return false;
    }


    @Override
    public List<QueryValidApplySceneResp> queryValidApplyScene(QueryValidApplySceneReq requestData) {
        List<QueryValidApplySceneResp> rets = new ArrayList<>();
        if (StringUtils.isBlank(requestData.getChannelId())) {
            // 查询渠道信息
            List<SceneChannelConfig> channelConfigs = productSceneTagRepository.querySceneChannelConfig();
            rets = ProductSceneLabelConvert.buildChannelConfigRet(channelConfigs);
        } else {
            // 根据渠道查询场景信息
            Preconditions.checkNotNull(productSceneTagRepository
                    .querySceneChannelConfigById(Integer.valueOf(requestData.getChannelId())), "未找到当前渠道id");
            List<ProductSceneLabel> productSceneLabels  = productSceneTagRepository
                    .queryLabelSceneByChannelId(Integer.valueOf(requestData.getChannelId()));
            rets = ProductSceneLabelConvert.buildProductSceneLabelRet(productSceneLabels);
        }
        return rets;
    }

    @Override
    public Boolean addScene(AddSceneReq requestData) {
        // 获取当前操作人
        String name = "";
        try {
            name = sysBaseAPI.getCurrentLoginUserInfo() == null ? "系统" : sysBaseAPI.getCurrentLoginUserInfo().getRealname();
        } catch (Exception e) {
            log.error("获取当前登录用户信息有误");
        }
        // 创建对象
        ProductSceneLabel sceneLabel = new ProductSceneLabel();
        BeanUtils.copyProperties(requestData, sceneLabel);
        sceneLabel.setId(IdLeaf.getDateId(labelSceneLeafTag));
        sceneLabel.setSceneKey(RandomKeyUtil.getRandomKey());
        sceneLabel.setCreator(name);
        sceneLabel.setUpdater(name);
        sceneLabel.setCreateTime(new Date());
        sceneLabel.setUpdateTime(new Date());

        // 创建log
        ProductSceneLabelLog sceneLabelLog = new ProductSceneLabelLog();
        BeanUtils.copyProperties(sceneLabel, sceneLabelLog);
        sceneLabelLog.setSceneLabelId(sceneLabel.getId());
        sceneLabelLog.setId(IdLeaf.getDateId(labelSceneLeafTag));

        template.execute(action -> {
            productSceneTagRepository.saveProductSceneLabel(sceneLabel);
            productSceneTagRepository.saveProductSceneLabelLog(sceneLabelLog);
            return action;
        });
        return true;
    }

    @Override
    public QuerySceneResp queryScene(QuerySceneReq requestData) {
        ProductSceneLabel productLabelScene = Preconditions.checkNotNull(productSceneTagRepository
                .queryProductSceneLabel(requestData.getId()), "当前id的数据不存在");
        SceneChannelConfig channelConfig = Preconditions.checkNotNull(productSceneTagRepository.
                querySceneChannelConfigById(productLabelScene.getOwnChannelId()), "当前场景的渠道不存在");
        return ProductSceneLabelConvert.buildQueryScene(productLabelScene, channelConfig);
    }

    @Override
    public Boolean updateScene(UpdateSceneReq requestData) {
        // 校验数据正确性
        ProductSceneLabel productLabelScene = Preconditions.checkNotNull(productSceneTagRepository
                .queryProductSceneLabel(requestData.getId()), "当前id的数据不存在");
        SceneChannelConfig channelConfig = Preconditions.checkNotNull(productSceneTagRepository.
                querySceneChannelConfigById(productLabelScene.getOwnChannelId()), "当前场景的渠道不存在");

        // 获取当前操作人
        String name = "";
        try {
            name = sysBaseAPI.getCurrentLoginUserInfo() == null ? "系统" : sysBaseAPI.getCurrentLoginUserInfo().getRealname();
        } catch (Exception e) {
            log.error("获取当前登录用户信息有误");
        }

       // 组装数据
        BeanUtils.copyProperties(requestData, productLabelScene);
        productLabelScene.setUpdateTime(new Date());
        productLabelScene.setUpdater(name);

        ProductSceneLabelLog sceneLabelLog = new ProductSceneLabelLog();
        BeanUtils.copyProperties(productLabelScene, sceneLabelLog);
        sceneLabelLog.setId(IdLeaf.getDateId(labelSceneLeafTag));
        sceneLabelLog.setSceneLabelId(productLabelScene.getId());

        template.execute(action -> {
            productSceneTagRepository.updateProductSceneLabel(productLabelScene);
            productSceneTagRepository.saveProductSceneLabelLog(sceneLabelLog);
            return action;
        });

        return true;
    }

    @Override
    public Boolean switchScene(SwitchSceneReq requestData) {
        // 校验数据正确性
        ProductSceneLabel productLabelScene = Preconditions.checkNotNull(productSceneTagRepository
                .queryProductSceneLabel(requestData.getId()), "当前id的数据不存在");

        // 获取当前操作人
        String name = "";
        try {
            name = sysBaseAPI.getCurrentLoginUserInfo() == null ? "系统" : sysBaseAPI.getCurrentLoginUserInfo().getRealname();
        } catch (Exception e) {
            log.error("获取当前登录用户信息有误");
        }

        checkSceneStatus(productLabelScene, requestData.getIsDisabled());

        // 组装数据
        productLabelScene.setStatus(requestData.getIsDisabled());
        productLabelScene.setUpdateTime(new Date());
        productLabelScene.setUpdater(name);

        ProductSceneLabelLog sceneLabelLog = new ProductSceneLabelLog();
        BeanUtils.copyProperties(productLabelScene, sceneLabelLog);
        sceneLabelLog.setId(IdLeaf.getDateId(labelSceneLeafTag));
        sceneLabelLog.setSceneLabelId(productLabelScene.getId());

        template.execute(action -> {
            productSceneTagRepository.updateProductSceneLabel(productLabelScene);
            productSceneTagRepository.saveProductSceneLabelLog(sceneLabelLog);
            return action;
        });

        return true;
    }

    @Override
    public List<QuerySceneLabelResp> querySceneLabelList(QuerySceneReq requestData) {
        // 查询场景标签关联
        List<SceneLabelConnection> sceneLabelConnections = productSceneTagRepository
                .querySceneLabelConnectionBySceneId(requestData.getId());
        if (CollectionUtils.isEmpty(sceneLabelConnections)) {
            return Collections.emptyList();
        }

        sceneLabelConnections = sceneLabelConnections.stream().filter(v -> StringUtils.isNotBlank(v.getLabelCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sceneLabelConnections)) {
            return Collections.emptyList();
        }

        // 获取标签信息
        List<SysCategoryModel> sysCategoryModels = sysBaseAPI.queryDSysCateByCodes(
                sceneLabelConnections.stream().map(SceneLabelConnection::getLabelCode).collect(Collectors.toList()));

        return ProductSceneLabelConvert.buildSceneLabelList(sceneLabelConnections, sysCategoryModels);
    }

    @Override
    public Boolean disassociateSceneLabels(List<DisassociateSceneLabelsReq> requestData) {
        // 获取当前操作人
        String name = "";
        try {
            name = sysBaseAPI.getCurrentLoginUserInfo() == null ? "系统" : sysBaseAPI.getCurrentLoginUserInfo().getRealname();
        } catch (Exception e) {
            log.error("获取当前登录用户信息有误");
        }

        String finalName = name;
        template.execute(action -> {
            productSceneTagRepository.updateSceneConfigConnection(finalName, requestData);

            return action;
        });
        return true;
    }

    @Override
    public List<QuerySceneListResp> querySceneList(QuerySceneListReq requestData, Page page) {
        com.github.pagehelper.Page<ProductSceneLabel> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<ProductSceneLabel> list = productSceneTagRepository.selectSceneListByParam(requestData.getSceneName(), requestData.getOwnChannelId());
        List<SceneChannelConfig> channelConfigs = productSceneTagRepository.querySceneChannelConfig();
        PageInfo<QuerySceneListResp> pageInfo = new PageInfo(hPage);
        pageInfo.setList(ProductSceneLabelConvert.buildSceneList(list, channelConfigs));
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    @Override
    public List<SearchLabelSceneResp> searchLabelScene(String labelCode) {
        if (StringUtils.isBlank(labelCode)) {
            throw new RuntimeException("标签不能为空");
        }

        // 根据code查询场景
        List<SceneLabelConnection> sceneLabelConnections = productSceneTagRepository.selectSceneByLabelCode(labelCode);
        if (CollectionUtils.isEmpty(sceneLabelConnections)) {
            return Collections.emptyList();
        }
        // 根据sceneId查询场景信息
        List<ProductSceneLabelEntity> productSceneLabelEntities = new ArrayList<>();
        List<String> sceneIds = sceneLabelConnections.stream().map(SceneLabelConnection::getSceneId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sceneIds)) {
            productSceneLabelEntities = productSceneTagRepository.selectSceneLabelById(sceneIds);
        }
        return ProductSceneLabelConvert.buildSearchLabelSceneRet(sceneLabelConnections, productSceneLabelEntities);
    }

    @Override
    public Boolean updateLabelScene(UpdateLabelSceneReq requestData) {
        // 判断传入的是否有相同的场景
        List<String> sceneIds = requestData.getUpdateDataEntityList().stream().map(UpdateLabelSceneReq.updateDataEntity::getSceneId).collect(Collectors.toList());
        judgeSameScene(sceneIds);

        if (CollectionUtils.isEmpty(requestData.getUpdateDataEntityList())) {
            throw new RuntimeException("需要更改的数据不能为空");
        }

        // 判断是否要添加  倒数第二层才添加场景值
        Boolean isPenultimateLevel = judgeIsPenultimateLevel(requestData.getCode());

        if (!isPenultimateLevel) {
            return true;
        }

        // 获取当前操作人
        String name = "";
        try {
            name = sysBaseAPI.getCurrentLoginUserInfo() == null ? "系统" : sysBaseAPI.getCurrentLoginUserInfo().getRealname();
        } catch (Exception e) {
            log.error("获取当前登录用户信息有误");
        }

        // 获取更新的数据
        List<UpdateLabelSceneReq.updateDataEntity> updateList = requestData.getUpdateDataEntityList().stream().filter(v -> StringUtils.isNotBlank(v.getId()))
                                                .collect(Collectors.toList());

        // 获取需要插入的数据
        List<UpdateLabelSceneReq.updateDataEntity> needSaveList = requestData.getUpdateDataEntityList().stream().filter(v -> StringUtils.isBlank(v.getId())).collect(Collectors.toList());
        List<SceneLabelConnection> saveList = new ArrayList<>();
        String finalName = name;
        needSaveList.forEach(v -> {
            SceneLabelConnection connection = new SceneLabelConnection();
            connection.setSceneId(v.getSceneId());
            connection.setId(IdLeaf.getDateId(labelSceneLeafTag));
            connection.setCreateTime(new Date());
            connection.setLabelCode(v.getLabelCode());
            connection.setSort(v.getSort());
            connection.setUpdateTime(new Date());
            connection.setOperator(finalName);
            saveList.add(connection);
        });

        template.execute(action -> {
            if (CollectionUtils.isNotEmpty(saveList)) {
                productSceneTagRepository.saveLabelSceneConnections(saveList);
            }

            if (CollectionUtils.isNotEmpty(updateList)) {
                productSceneTagRepository.updateLabelSceneConnections(updateList, finalName);
            }

            return action;
        });

        return true;
    }

    @Override
    public List<QuerySceneLabelByKeyResp> querySceneLabelsByKey(QuerySceneReq requestData) {
        // 查询场景标签关联
        List<SceneLabelConnection> sceneLabelConnections = productSceneTagRepository
                .querySceneLabelConnectionBySceneKey(requestData.getId());
        if (CollectionUtils.isEmpty(sceneLabelConnections)) {
            return Collections.emptyList();
        }

        // 查询标签的子标签
        List<QuerySceneLabelByKeyResp> rets = new ArrayList<>();
        sceneLabelConnections.forEach(v -> {
            List<SysCategoryModel> sysCategoryModels = sysBaseAPI.queryAllDSysCategoryByCode(v.getLabelCode());
            if (CollectionUtils.isEmpty(sysCategoryModels)) {
                return;
            }
            QuerySceneLabelByKeyResp resp = new QuerySceneLabelByKeyResp();
            resp.setLabelCode(v.getLabelCode());
            resp.setLabelName(sysCategoryModels.get(0).getPidName());
            List<QuerySceneLabelByKeyResp> subResps = new ArrayList<>();
            sysCategoryModels.forEach(x -> {
                QuerySceneLabelByKeyResp subResp = new QuerySceneLabelByKeyResp();
                subResp.setLabelCode(x.getCode());
                subResp.setLabelName(x.getName());
                subResps.add(subResp);
            });
            resp.setSubCodes(subResps);
            rets.add(resp);
        });

        return rets;
    }

    //-------------------------------提供给各个C端使用------------------------------

    @Override
    public List<QuerySceneLabelResp> queryChannelSysCateByKey(String requestData) {
        List<SceneLabelConnection> sceneLabelConnections = getSceneLabelConnections(requestData);

        // 获取标签信息
        List<SysCategoryModel> sysCategoryModels = sysBaseAPI.queryDSysCateByCodes(
                sceneLabelConnections.stream().map(SceneLabelConnection::getLabelCode).collect(Collectors.toList()));

        List<QuerySceneLabelResp> resps = ProductSceneLabelConvert.buildSceneLabelList(sceneLabelConnections, sysCategoryModels);

        return resps;
    }

    private List<SceneLabelConnection> getSceneLabelConnections(String requestData) {
        if (StringUtils.isBlank(requestData)) {
            throw new RuntimeException("场景标签值不能为空");
        }

        // 查询场景标签关联
        List<SceneLabelConnection> sceneLabelConnections = productSceneTagRepository
                .querySceneLabelConnectionBySceneKey(requestData);
        if (CollectionUtils.isEmpty(sceneLabelConnections)) {
            throw new RuntimeException("当前场景key不存在");
        }
        return sceneLabelConnections;
    }

    @Override
    public List<QueryListSceneLabelResp> queryChannelListSysCateByKey(String requestData) {
        List<SceneLabelConnection> sceneLabelConnections = getSceneLabelConnections(requestData);

        // 处理一级标签
        List<String> parentsLabel = new ArrayList<>();
        List<String> finalParentsLabel = parentsLabel;
        sceneLabelConnections.stream().forEach(v -> {
            String[] split = v.getLabelCode().split("-");
            finalParentsLabel.add(split[0] + "-" + split[1]);
        });

        parentsLabel = parentsLabel.stream().distinct().collect(Collectors.toList());

        List<String> labelSigns = sceneLabelConnections.stream().map(SceneLabelConnection::getLabelCode).collect(Collectors.toList());
        labelSigns.addAll(parentsLabel);

        List<SysCategoryModel> sysCategoryModels = sysBaseAPI.queryDSysCateByCodes(labelSigns);
        Map<String, String> map = JSONObject.parseObject(firstLabelOrderStr, Map.class);

        return ProductSceneLabelConvert.buildListLabelResp(parentsLabel, map, sysCategoryModels, sceneLabelConnections);
    }

    private void judgeSameScene(List<String> sceneIds) {
        HashSet<String> set = new HashSet<>();
        sceneIds.forEach(v -> {
            set.add(v);
        });
        if (set.size() != sceneIds.size()) {
            throw new RuntimeException("存在相同的场景，请检查后再保存!");
        }
    }

    private void checkSceneStatus(ProductSceneLabel productLabelScene, Integer isOpen) {
        if (Objects.equals(SceneLabelEnum.OPEN.getCode(), productLabelScene.getStatus())
        && Objects.equals(SceneLabelEnum.OPEN.getCode(), isOpen)) {
            throw new RuntimeException("当前状态已经开启，无需开启");
        }

        if (Objects.equals(SceneLabelEnum.SHUT_DOWN.getCode(), productLabelScene.getStatus())
        && Objects.equals(SceneLabelEnum.SHUT_DOWN.getCode(), isOpen)) {
            throw new RuntimeException("当前状态已经关闭，无需关闭");
        }
    }
}
