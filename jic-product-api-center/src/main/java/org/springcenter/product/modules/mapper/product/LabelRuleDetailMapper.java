package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.LabelRuleDetail;

import java.util.List;

public interface LabelRuleDetailMapper extends BaseMapper<LabelRuleDetail> {

    void insertBatch(@Param("list") List<LabelRuleDetail> labelRuleDetailList);


    void batchUpdate(@Param("list") List<LabelRuleDetail> updateDetailList);
}
