package org.springcenter.product.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.DisplayProductResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/1/31 13:26
 */
public interface IProductDisplayOrderService {
    /**
     * 根据门店code查询门店商品陈列数据
     * @param requestData 门店code
     * @param page 页码
     * @return 返回
     */
    List<DisplayProductResp> queryDisGoodsByStoreCode(String requestData, Page page);
}
