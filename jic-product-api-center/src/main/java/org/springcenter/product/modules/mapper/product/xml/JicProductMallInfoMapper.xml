<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.JicProductMallInfoMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.JicProductMallInfo">
        <id property="id" column="ID" />
        <result property="weId" column="WEID"/>
        <result property="productId" column="PRODUCT_ID"/>
        <result property="productNo" column="PRODUCT_NO"/>
        <result property="mallId" column="MALL_ID" />
        <result property="mallTag" column="MALL_TAG" />
        <result property="mallClassify" column="MALL_CLASSIFY" />
        <result property="mallTitle" column="MALL_TITLE"/>
        <result property="mallIsCanSell" column="MALL_ISCANSELL"/>
        <result property="mallIsPutWay" column="MALL_ISPUTWAY"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="mallImage" column="MALL_IMAGE"/>
        <result property="mallBanner" column="MALL_BANNER"/>
        <result property="mallDetailImage" column="MALL_DETAIL_IMAGE"/>
        <result property="sellModelType" column="SELL_MODEL_TYPE"/>
        <result property="canSellTime" column="CANSELL_TIME"/>
    </resultMap>


    <sql id="Base_Column_List">
        ID, WEID, PRODUCT_ID, PRODUCT_NO, MALL_ID, MALL_TAG, MALL_CLASSIFY, MALL_TITLE, MALL_ISCANSELL,
            MALL_ISPUTWAY, CREATE_TIME, UPDATE_TIME, MALL_IMAGE, MALL_BANNER, MALL_DETAIL_IMAGE,
            SELL_MODEL_TYPE, CANSELL_TIME
    </sql>
    <select id="selectByNameAndWeId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM USERWX.JIC_PRODUCT_MALL_INFO
        WHERE WEID = #{weid} AND PRODUCT_NO = #{name}
    </select>


    <select id="selectByProductIdAndWeId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM USERWX.JIC_PRODUCT_MALL_INFO
        WHERE WEID = #{weid} AND
              PRODUCT_ID IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>


</mapper>
