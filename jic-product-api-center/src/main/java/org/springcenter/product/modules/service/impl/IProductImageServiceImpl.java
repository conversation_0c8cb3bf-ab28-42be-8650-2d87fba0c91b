package org.springcenter.product.modules.service.impl;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springcenter.product.api.dto.IdentifyImageReq;
import org.springcenter.product.api.dto.IdentifyImageResp;
import org.springcenter.product.modules.remote.entity.EgSearchReqEntity;
import org.springcenter.product.modules.remote.entity.EgSearchRespEntity;
import org.springcenter.product.modules.remote.service.IEgSearchService;
import org.springcenter.product.modules.service.IProductImageService;
import org.springcenter.product.util.Base64Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/8/8 10:19
 */
@Slf4j
@Service
public class IProductImageServiceImpl implements IProductImageService {

    @Autowired
    private IEgSearchService iEgSearchService;

    @Override
    public IdentifyImageResp identifyPicByPic(IdentifyImageReq requestData) throws IOException {

        String base64Date = Base64Util.getBase64Date(requestData.getUrl());

        // 获取所有数据
        EgSearchReqEntity reqEntity = EgSearchReqEntity.builder().img(base64Date).brand("ALL").source("pos").build();
        List<EgSearchRespEntity> searchResponse = iEgSearchService.searchAllBrandAndProduct(reqEntity);

        IdentifyImageResp resp = new IdentifyImageResp();
        resp.setOriginImg(requestData.getUrl());
        List<String> allProductIds = new ArrayList<>();
        List<IdentifyImageResp.PartProduct> partProducts = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(searchResponse)) {
            searchResponse.forEach(v -> {
                IdentifyImageResp.PartProduct partProduct = new IdentifyImageResp.PartProduct();
                partProduct.setClassName(v.getClass_name());
                partProduct.setImage(v.getImg());
                List<String> productIds = new ArrayList<>();
                v.getList().forEach(x -> {
                    x.getList().forEach(y -> {
                        productIds.add(y.getM_product_id());
                    });
                });

                allProductIds.addAll(productIds);
                partProduct.setProductIds(productIds);
                partProducts.add(partProduct);
            });
        }

        resp.setProductIds(allProductIds);
        resp.setPartProducts(partProducts);

        return resp;
    }
}
