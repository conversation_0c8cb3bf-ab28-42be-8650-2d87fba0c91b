package org.springcenter.product.modules.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.common.system.vo.LoginUser;
import com.jnby.authority.common.system.vo.SysPermissionDataRuleModel;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.common.util.excel.IWriteDataExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.assertj.core.util.Lists;
import org.springcenter.product.api.IProductDetailsImgApi;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.api.enums.MQueriesTabEnum;
import org.springcenter.product.api.enums.ProductVersionExcelEnum;
import org.springcenter.product.api.enums.ProductVersionPicEnum;
import org.springcenter.product.config.exception.ProductException;
import org.springcenter.product.modules.context.SysPermissionDataRuleModelContext;
import org.springcenter.product.modules.convert.ProductVersionConvert;
import org.springcenter.product.modules.entity.ParseProductVersionEntity;
import org.springcenter.product.modules.mapper.product.MPQAttrConnectionLogMapper;
import org.springcenter.product.modules.mapper.product.MProductVersionMapper;
import org.springcenter.product.modules.mapper.product.MProductVersionPicMapper;
import org.springcenter.product.modules.mapper.product.MQueriesTabMapper;
import org.springcenter.product.modules.model.*;
import org.springcenter.product.modules.repository.*;
import org.springcenter.product.modules.service.CommonDataRuleService;
import org.springcenter.product.modules.service.IProductService;
import org.springcenter.product.modules.service.IProductVersionPicService;
import org.springcenter.product.modules.service.IProductVersionService;
import org.springcenter.product.modules.util.FileParseUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.io.IOException;
import java.io.Reader;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;


/**
* <AUTHOR>
* @description 针对表【M_QUERIES_TAB(查询标签)】的数据库操作Service实现
* @createDate 2022-11-30 17:17:15
*/
@Service
@Slf4j
@RefreshScope
public class ProductVersionServiceImpl extends ServiceImpl<MProductVersionMapper, MProductVersion> implements IProductVersionService {

    @Autowired
    private IMQueriesTabRepository mQueriesTabRepository;

    @Autowired
    private RedisPoolUtil redisPoolUtil;

    @Autowired
    private QiniuUtil qiniuUtil;

    @Autowired
    private IProductVersionPicService productVersionPicService;

    @Autowired
    private IMProductVersionRepository productVersionRepository;

    @Autowired
    private IMProductVersionPicRepository productVersionPicRepository;

    @Autowired
    private IMProductVersionSalesRepository productVersionSalesRepository;

    @Autowired
    private MProductVersionMapper mProductVersionMapper;

    @Autowired
    private MProductVersionPicMapper mProductVersionPicMapper;

    @Autowired
    private IProductService productService;

    @Value("${product.version.leaf.tag}")
    private String productVersionLeafTag;

    @Value("${queries.attr.code}")
    private String queriesAttrCode;

    @Value("${queries.attr.value.code}")
    private String queriesAttrValueCode;

    @Value("${queries.sec.attr.code}")
    private String queriesSecAttrCode;

    @Value("${pv.queries.connect.leaf.tag}")
    private String pvQueriesConnectLeafTag;

    @Value("${mpv.queries.attr.connect.leaf.tag}")
    private String mpvQueriesAttrConnectLeafTag;

    @Autowired
    private IMProductVersionCitationsRepository mProductVersionCitationsRepository;

    @Autowired
    private IMQueriesAttrConnectionRepository mQueriesAttrConnectionRepository;

    @Autowired
    private MQueriesTabMapper mQueriesTabMapper;

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Autowired
    private MPQAttrConnectionLogMapper mpqAttrConnectionLogMapper;

    @Autowired
    private CommonDataRuleService commonDataRuleService;

    @Autowired
    private IProductDetailsImgApi productDetailsImgApi;

    @Override
    public ProductVersionQueriesTabResp getProductVersionQueriesTab(CommonRequest request) {
        // 查询当前用户的权限
        List<String> brands = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getComponent())) {
            brands = commonDataRuleService.getPeopleBrandDataRule(request.getComponent());
        }

        Triple<List<MQueriesTab>, List<MQueriesTab>, List<MQueriesTab>> queryFromDataBaseRet = queryFromDataBase();

        //查询筛选条件
        List<MQueriesTab> filter = mQueriesTabRepository.getProductVersionListFilter(MQueriesTabEnum.LIST_FILTER);

        //组装返回参数
        return ProductVersionConvert.buildRet(queryFromDataBaseRet.getLeft(), queryFromDataBaseRet.getMiddle(),
                                             queryFromDataBaseRet.getRight(), filter, brands);
    }


    private Triple<List<MQueriesTab>, List<MQueriesTab>, List<MQueriesTab>> queryFromDataBase() {
        //查询品牌列表
        List<MQueriesTab> brandList = mQueriesTabRepository.getBrandList(MQueriesTabEnum.BRAND);

        //查询类目列表
        List<MQueriesTab> categoryList = mQueriesTabRepository.getCateGoryList(MQueriesTabEnum.CATEGORY);

        //查询属性列表
        List<MQueriesTab> attributeList = mQueriesTabRepository.getAttributeList(MQueriesTabEnum.ATTRIBUTE);

        return Triple.of(brandList, categoryList, attributeList);
    }

    private Triple<List<MQueriesTab>, List<MQueriesTab>, List<MQueriesTab>> queryFromDataBaseCategoryAndAttr() {

        //查询类目列表
        List<MQueriesTab> categoryList = mQueriesTabRepository.getCateGoryList(MQueriesTabEnum.CATEGORY);

        //查询属性列表
        List<MQueriesTab> attributeList = mQueriesTabRepository.getAttributeList(MQueriesTabEnum.ATTRIBUTE);

        //查询子属性
        List<MQueriesTab> attributeSubList = mQueriesTabRepository.getSubAttributeList(MQueriesTabEnum.ATTRIBUTE_SUB);

        return Triple.of(categoryList, attributeList, attributeSubList);
    }

    @Override
    public void downFileForProductVersion(String url, String keys) throws IOException {
        log.info("==================版型库新增");
        String filePath = FileParseUtil.downLoadExcel(url);
        // 查询所有属性
        Triple<List<MQueriesTab>, List<MQueriesTab>, List<MQueriesTab>> queryFromDataBaseRet = queryFromDataBase();
        // 查询类目全属性
        Map<String, List<MQueriesAttrConnection>> smallCategoryAttrConnectMap = queryAttrConnect();

        // 正常数据 和 错误数据
        List<MProductVersion> normalDataList = new ArrayList<>();
        List<ParseProductVersionEntity.ErrorData> errorDataList  = new ArrayList<>();

        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {
            @Override
            protected void saveData() {
                ParseProductVersionEntity productVersionEntity = assembleDataForInsert(this.cachedDataList, queryFromDataBaseRet, smallCategoryAttrConnectMap, 0);
                //保存数据
                dealData(productVersionEntity);

                // 保存数据
                if (CollectionUtils.isNotEmpty(productVersionEntity.getBaseDataList())) {
                    normalDataList.addAll(productVersionEntity.getBaseDataList());
                }

                if(CollectionUtils.isNotEmpty(productVersionEntity.getErrorDataList())){
                    errorDataList.addAll(productVersionEntity.getErrorDataList());
                }
            }
        });

        // 导出错误数据
        if (CollectionUtils.isNotEmpty(errorDataList)) {
            String fileName = System.currentTimeMillis() + ".xlsx";
            EasyExcelUtil.write(fileName, ParseProductVersionEntity.ErrorData.class, new IWriteDataExcel<ParseProductVersionEntity.ErrorData>() {
                @Override
                public List<ParseProductVersionEntity.ErrorData> getData() {
                    return errorDataList.stream().map(item -> {
                        ParseProductVersionEntity.ErrorData data = new ParseProductVersionEntity.ErrorData();
                        data.setModelNumber(item.getModelNumber());
                        data.setSampleNo(item.getSampleNo());
                        data.setBrand(item.getBrand());
                        data.setMsg(item.getMsg());
                        return data;
                    }).collect(Collectors.toList());
                }
            });
            File file = new File(fileName);
            String param = qiniuUtil.upload(file.getPath(), "异常版型商品"+System.currentTimeMillis() + ".xlsx");
            Integer success = CollectionUtils.isEmpty(normalDataList) ? 0 : normalDataList.size();
            Integer fail = CollectionUtils.isEmpty(errorDataList) ? 0 : errorDataList.size();
            Integer sum = success + fail;
            param = param + ";" + sum + ";" + success + ";" + fail;
            file.delete();
            log.info("redis的key{}", param);
            RedisTemplateUtil.setex(redisPoolUtil,keys, param,60);
        }

        Files.deleteIfExists(Paths.get(filePath));
    }

    private Map<String, List<MQueriesAttrConnection>> queryAttrConnect() {
        List<MQueriesAttrConnection> list = mQueriesAttrConnectionRepository.queryAllNormalConnections();
        return list.stream().collect(Collectors.groupingBy(MQueriesAttrConnection::getSmallCategoryCode));
    }

    private ParseProductVersionEntity assembleDataForInsert(List<Map<Integer, String>> cachedDataList,
                                                            Triple<List<MQueriesTab>, List<MQueriesTab>,
                                                                    List<MQueriesTab>> tripleData,
                                                            Map<String, List<MQueriesAttrConnection>> smallCategoryAttrConnectMap,
                                                            int i) {
        ParseProductVersionEntity ret = new ParseProductVersionEntity();
        List<MProductVersion> list = new ArrayList<>();
        List<ParseProductVersionEntity.ErrorData> errorList = new ArrayList<>();
        List<MProductVersionPic> pics = new ArrayList<>();
        List<MPQueAttrValueConnect> valueConnectList = new ArrayList<>();
        for(Map<Integer,String> data: cachedDataList) {
            // 获取基础商品数据

            // 1、校验传入的属性[新增属性是否已增加过]
            if (StringUtils.isBlank(data.get(0))) {
                ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                        .ErrorData(data.get(0), data.get(1), data.get(2), "版型字段不能为空");
                errorList.add(errorData);
                continue;
            }

            List<MProductVersion> productVersionEntities = productVersionRepository.queryByModelNumber(data.get(0));
            if (CollectionUtils.isNotEmpty(productVersionEntities)) {
                ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                        .ErrorData(data.get(0), data.get(1), data.get(2), "该版型库已重复");
                errorList.add(errorData);
                continue;
            }
            MProductVersion normalData = new MProductVersion();
            normalData = getMProductVersion(tripleData, list, errorList, data, normalData);
            if (normalData == null) continue;
            normalData.setId(IdLeaf.getDateId(productVersionLeafTag));
            normalData.setCreateTime(new Date());

            // 2、查询对应类目的全属性进行组装数据
            dealAttrValue(data, normalData, smallCategoryAttrConnectMap,
                    tripleData, errorList, valueConnectList);

            // 3、组装图片数据
            if (StringUtils.isNotBlank(data.get(38))) {
                dealExportPic(data.get(38), pics, normalData, 38);
            }
            if (StringUtils.isNotBlank(data.get(39))) {
                dealExportPic(data.get(39), pics, normalData, 39);
            }
            if (StringUtils.isNotBlank(data.get(40))) {
                dealExportPic(data.get(40), pics, normalData, 40);
            }
            if (StringUtils.isNotBlank(data.get(41))) {
                dealExportPic(data.get(41), pics, normalData, 41);
            }
            if (StringUtils.isNotBlank(data.get(42))) {
                dealExportPic(data.get(42), pics, normalData, 42);
            }

        }

        // 3、保存数据
        ret.setBaseDataList(list);
        ret.setErrorDataList(errorList);
        ret.setPics(pics);
        ret.setMpQueAttrValueConnects(valueConnectList);
        return ret;

    }

    private MProductVersion getMProductVersion(Triple<List<MQueriesTab>, List<MQueriesTab>,
            List<MQueriesTab>> tripleData, List<MProductVersion> list,
                                               List<ParseProductVersionEntity.ErrorData> errorList, Map<Integer, String> data,
                                               MProductVersion normalData) {


        //必填校验
        if (StringUtils.isBlank(data.get(1))) {
            ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                    .ErrorData(data.get(0), data.get(1), data.get(2), "品牌字段不能为空");
            errorList.add(errorData);
            return null;
        }

        if (StringUtils.isBlank(data.get(2))) {
            ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                    .ErrorData(data.get(0), data.get(1), data.get(2), "样衣号字段不能为空");
            errorList.add(errorData);
            return null;
        }

        if (StringUtils.isBlank(data.get(3))) {
            ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                    .ErrorData(data.get(0), data.get(1), data.get(2), "品类代码字段不能为空");
            errorList.add(errorData);
            return null;
        }

        if (StringUtils.isBlank(data.get(4))) {
            ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                    .ErrorData(data.get(0), data.get(1), data.get(2), "品类字段不能为空");
            errorList.add(errorData);
            return null;
        }

        if (StringUtils.isBlank(data.get(5))) {
            ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                    .ErrorData(data.get(0), data.get(1), data.get(2), "合体度代码字段不能为空");
            errorList.add(errorData);
            return null;
        }

        if (StringUtils.isBlank(data.get(6))) {
            ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                    .ErrorData(data.get(0), data.get(1), data.get(2), "合体度字段不能为空");
            errorList.add(errorData);
            return null;
        }

        if (CollectionUtils.isNotEmpty(list)) {
            if (list.stream().map(MProductVersion::getModelNumber).collect(Collectors.toList()).contains(data.get(0))) {
                ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                        .ErrorData(data.get(0), data.get(1), data.get(2), "该版型库已重复");
                errorList.add(errorData);
                return null;
            }
        }


        //首先校验品牌
        MQueriesTab brandMQueriesTab = tripleData.getLeft().stream()
                .filter(v -> Objects.equals(v.getName(), data.get(1))).findFirst().orElse(null);
        if (brandMQueriesTab == null) {
            ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                    .ErrorData(data.get(0), data.get(1), data.get(2), "当前品牌并不存在，请查看是否格式或品牌有误");
            errorList.add(errorData);
            return null;
        }

        //品类进行校验
        MQueriesTab category = tripleData.getMiddle().stream()
                .filter(v -> Objects.equals(v.getName(), data.get(4))).findFirst().orElse(null);
        if (category == null) {
            ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                    .ErrorData(data.get(0), data.get(1), data.get(2), "当前填写的品类不存在，请查看是否填写有误");
            errorList.add(errorData);
            return null;
        }

        //对合体度和合体度代码进行校验
        // todo 后台增加合体度属性值后会有问题 - 认为暂不添加
            /*DegreeFitCodeEnum degreeFitCode = DegreeFitCodeEnum.getByCode(data.get(5));
            if (degreeFitCode == null) {
                ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                        .ErrorData(data.get(0), data.get(1), data.get(2), "当前填写的合体度代码不存在，请查看是否填写有误");
                errorList.add(errorData);
                continue;
            }

            if (!Objects.equals(degreeFitCode.getDesc(), data.get(6))) {
                ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                        .ErrorData(data.get(0), data.get(1), data.get(2), "当前填写的合体度有误，请查看是否填写有误");
                errorList.add(errorData);
                continue;
            }*/


        //主属性表
        normalData.setBrandCode(brandMQueriesTab.getCode());
        normalData.setBrand(brandMQueriesTab.getName());
        normalData.setSampleNo(data.get(2));
        normalData.setCategoryCode(data.get(3));
        normalData.setCategory(category.getCode());
        normalData.setDegreeFitCode(data.get(5));
        normalData.setModelNumber(data.get(0));
        normalData.setMainIngredient(data.get(36));
        normalData.setMainIngredientNumber(data.get(37));
        normalData.setUpdateTime(new Date());
        //小品名
        if (StringUtils.isNotBlank(data.get(43))) {
            normalData.setSmallName(data.get(43));
        }
        list.add(normalData);
        return normalData;
    }

    private  void dealAttrValue(Map<Integer, String> data, MProductVersion normalData,
                                          Map<String, List<MQueriesAttrConnection>> smallCategoryAttrConnectMap,
                                          Triple<List<MQueriesTab>, List<MQueriesTab>, List<MQueriesTab>> tripleData,
                                                       List<ParseProductVersionEntity.ErrorData> errorList, List<MPQueAttrValueConnect> valueConnectList) {
        // 1、当前类目的全属性
        List<MQueriesAttrConnection> connections = smallCategoryAttrConnectMap.get(normalData.getCategory());
        if (CollectionUtils.isEmpty(connections)) {
            return;
        }


        // 2、关联的全属性来挂值
        if (!CollectionUtils.isEmpty(connections)) {
            connections.forEach(v -> {
                String value = "";
                String valueName = "";
                if (Objects.equals(v.getType(), 1)) {
                    // 校验一级属性值
                    ProductVersionExcelEnum versionExcelEnum = ProductVersionExcelEnum.getByName(v.getAttributeName());
                    if (versionExcelEnum == null) {
                        return;
                    }
                    // 获取表格对应index数据
                    valueName = data.get(versionExcelEnum.getCode());
                    if (StringUtils.isBlank(valueName)) {
                        return;
                    }

                    // 校验对应的数据正确性
                    List<MQueriesTab> mQueriesTabs = tripleData.getRight().stream()
                            .filter(x -> Objects.equals(x.getPid(), v.getAttributeId())).collect(Collectors.toList());
                    String msg = "当前填写的" + versionExcelEnum.getDesc() + "有误，和" + versionExcelEnum.getDesc() + "代码无法匹配";
                    if (CollectionUtils.isEmpty(mQueriesTabs)) {
                        ParseProductVersionEntity.ErrorData errorData =
                                new ParseProductVersionEntity.ErrorData(data.get(0), data.get(1), data.get(2), msg);
                        errorList.add(errorData);
                        return;
                    }

                    String finalValueName = valueName;
                    MQueriesTab mQueriesTab = mQueriesTabs.stream().filter(o -> Objects.equals(o.getName(), finalValueName)).findFirst().orElse(null);
                    if (mQueriesTab == null) {
                        ParseProductVersionEntity.ErrorData errorData =
                                new ParseProductVersionEntity.ErrorData(data.get(0), data.get(1), data.get(2), msg);
                        errorList.add(errorData);
                        return;
                    }
                    value = mQueriesTab.getCode();

                } else {
                    // 处理二级属性
                    ProductVersionExcelEnum versionExcelEnum = ProductVersionExcelEnum.getByName(v.getConnectAttributeName());
                    if (versionExcelEnum == null) {
                        return;
                    }
                    // 获取表格对应index数据
                    valueName = data.get(versionExcelEnum.getCode());
                }


                MPQueAttrValueConnect valueConnect = new MPQueAttrValueConnect();
                valueConnect.setId(IdLeaf.getId(mpvQueriesAttrConnectLeafTag));
                valueConnect.setCreateTime(new Date());
                valueConnect.setUpdateTime(new Date());
                valueConnect.setMvProductId(normalData.getId());
                valueConnect.setQueConnectId(v.getId());
                valueConnect.setValue(value);
                valueConnect.setValueName(valueName);
                valueConnectList.add(valueConnect);
            });
        }

    }

    @Override
    public List<ProductVersionListDataResp> queryList(ProductVersionListDataReq productVersionListDataReq, Page page, String component) {
        // 获取数据权限
        List<String> brands= new ArrayList<>();
        if (StringUtils.isNotBlank(component)) {
            brands = commonDataRuleService.getPeopleBrandDataRule(component);
        }
        if (CollectionUtils.isNotEmpty(brands)) {
            if (CollectionUtils.isEmpty(productVersionListDataReq.getBrandList())) {
                productVersionListDataReq.setBrandList(brands);
            } else {
                List<String> finalBrands = brands;
                List<String> filter = productVersionListDataReq.getBrandList().stream().filter(v -> finalBrands.contains(v)).collect(Collectors.toList());
                productVersionListDataReq.setBrandList(filter);
            }
        }
        log.info("==============版型库queryList，过滤品牌信息 = {}", JSONObject.toJSONString(productVersionListDataReq.getBrandList()));

        List<ProductVersionEntity> rets = new ArrayList<>();
        List<String> sampleNos = new ArrayList<>();
        com.github.pagehelper.Page<ProductVersionEntity> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        if (productVersionListDataReq == null) {
            rets = productVersionRepository.queryList();
        } else {
            if (StringUtils.isNotBlank(productVersionListDataReq.getName())) {
                QueryGoodsReq queryGoodsReq = new QueryGoodsReq();
                queryGoodsReq.setName(productVersionListDataReq.getName());
                Page pageES = new Page();
                pageES.setPageNo(1);
                pageES.setPageSize(1000);
                List<GoodSpuResp> goodSpuResps = productService.searchGoods(queryGoodsReq, pageES);
                if (CollectionUtils.isNotEmpty(goodSpuResps)) {
                    sampleNos = goodSpuResps.stream().map(GoodSpuResp::getSample_code).collect(Collectors.toList());
                }
            }

            rets = productVersionRepository.queryListByParams(sampleNos, productVersionListDataReq.getAttributeList(), productVersionListDataReq.getBrandList(),
                    productVersionListDataReq.getCategoryList(), productVersionListDataReq.getSort(),
                    productVersionListDataReq.getName(), productVersionListDataReq.getIsClose());
        }

        //根据数据去查照片
        HashMap<String, SampleProductSkcResp> map = null;
        HashMap<String, GoodSpuResp> picMap = null;
        List<MProductVersionPic> mProductVersionPics = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rets)) {
            List<String> mProductVersionIds = rets.stream().map(ProductVersionEntity::getId).collect(Collectors.toList());
            mProductVersionPics = productVersionPicRepository.queryMainPicByIds(mProductVersionIds);

            List<String> sampleCodes = rets.stream().map(ProductVersionEntity::getSampleNo).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sampleCodes)) {
                //根据es查询样衣号的品牌和skc_id数据
                SampleProductReq sampleProductReq = new SampleProductReq();
                sampleProductReq.setNameLists(sampleCodes);
                Page pageES = new Page();
                pageES.setPageNo(1);
                pageES.setPageSize(1000);
                List<SampleProductSkcResp> sampleProductSkcResp = productService.searchSampleProductSkc(sampleProductReq, pageES, "");
                if (CollectionUtils.isNotEmpty(sampleProductSkcResp)) {
                    map = sampleProductSkcResp.stream().collect(HashMap::new, (k, v) -> k.put(v.getSample_code(), v), HashMap::putAll);
                }

                // 根据sample_code查询数据
                QueryGoodsReq req = new QueryGoodsReq();
                req.setSampleCodes(sampleCodes);
                List<GoodSpuResp> searchGoods = productService.searchGoods(req, pageES);
                if (CollectionUtils.isNotEmpty(searchGoods)) {
                     picMap = searchGoods.stream().collect(HashMap::new, (k, v) -> k.put(v.getSample_code(), v), HashMap::putAll);
                }
            }

        }
        // 查询对应合体度信息
        List<MPQueAttrValueConnect> connects = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rets)) {
            connects = productVersionRepository.selectValueByProductId(rets);
        }
        PageInfo<ProductVersionListDataResp> pageInfo = new PageInfo(hPage);
        pageInfo.setList(ProductVersionConvert.buildListRet(rets, queryFromDataBase(),
                mProductVersionPics, map, picMap, connects));
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());

        // 查询样衣号的图片
        List<String> samples = rets.stream().map(ProductVersionEntity::getSampleNo).collect(Collectors.toList());
        Triple<Map<String, String>, Map<String, String>, Map<String, List<String>>> triple = buildProductVersion(samples);
        if (triple == null) {
            return pageInfo.getList();
        }

        pageInfo.getList().forEach(v -> {
            if (MapUtils.isEmpty(triple.getLeft()) || StringUtils.isBlank(triple.getLeft().get(v.getSampleNo()))) {
                return;
            }
            String s = triple.getLeft().get(v.getSampleNo());
            if (MapUtils.isEmpty(triple.getMiddle()) || StringUtils.isBlank(triple.getMiddle().get(s))) {
                return;
            }
            v.setPic(triple.getMiddle().get(s));
        });

        return pageInfo.getList();
    }

    // 前者返回neId 后者返回图
    private Triple<Map<String, String>, Map<String, String>, Map<String, List<String>>> buildProductVersion(List<String> samples) {
        CommonRequest<FindSampleClothNetDiskImgReq> request = new CommonRequest<>();
        FindSampleClothNetDiskImgReq imgReq = new FindSampleClothNetDiskImgReq();
        imgReq.setSampleCloths(samples);
        request.setRequestData(imgReq);
        ResponseResult<List<SampleProductDetailImg>> sampleClothNetDiskImgs = productDetailsImgApi.findSampleClothNetDiskImgs(request);
        if (CollectionUtils.isEmpty(sampleClothNetDiskImgs.getData())) {
            return null;
        }
        Map<String, String> sampleNeIdMap = new HashMap<>();
        Map<String, List<String>> sampleAllPicsNeIdMap = new HashMap<>();
        List<BatchGetViewUrlReq.LianxiangData> list = new ArrayList<>();

        Map<String, List<SampleProductDetailImg>> listMap = sampleClothNetDiskImgs.getData().stream()
                .collect(Collectors.groupingBy(SampleProductDetailImg::getSampleClothCode));
        listMap.entrySet().forEach(entry -> {
            List<String> imgs = new ArrayList<>();
            entry.getValue().forEach(v -> {
                imgs.add(v.getNeid());
                if (Objects.equals(v.getFileName(), "1")) {
                    sampleNeIdMap.put(v.getSampleClothCode(), v.getNeid());
                }

                BatchGetViewUrlReq.LianxiangData data = new BatchGetViewUrlReq.LianxiangData();
                data.setNsid(v.getNsid());
                data.setNeid(v.getNeid());
                list.add(data);
            });
            sampleAllPicsNeIdMap.put(entry.getKey(), imgs);
        });
        if (MapUtils.isEmpty(sampleNeIdMap) || CollectionUtils.isEmpty(list)) {
            return null;
        }
        CommonRequest<LianxiangBatchGetViewReq> request1 = new CommonRequest<>();
        LianxiangBatchGetViewReq viewReq = new LianxiangBatchGetViewReq();
        viewReq.setFile_array(list);
        request1.setRequestData(viewReq);
        ResponseResult<List<LianxiangBatchGetViewResp>> responseResult = productDetailsImgApi.batchGetViewUrl(request1);
        if (CollectionUtils.isEmpty(responseResult.getData())) {
            return null;
        }
        Map<String, String> wpPicMap = responseResult.getData().stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getNeid(), v.getPreviewUrl()), HashMap::putAll);
        return Triple.of(sampleNeIdMap, wpPicMap, sampleAllPicsNeIdMap);
    }

    @Override
    public ProductVersionDetailResp queryDetail(ProductVersionDetailReq requestData) {
        MProductVersion mProductVersion = productVersionRepository.queryDetail(requestData.getId());
        if (mProductVersion == null || StringUtils.isBlank(mProductVersion.getModelNumber())) {
            throw new RuntimeException("该版型商品不存在");
        }
        // todo不止是否可用
        //List<MProductVersionPic> mProductVersionPics = productVersionPicRepository.queryPic(requestData.getId());
        //获取引用次数
        Integer citations = mProductVersionCitationsRepository.queryCitationsByModelNumber(mProductVersion);
        //根据样衣号获取大货号
        List<GoodSpuResp> goodSpuResps = new ArrayList<>();
        if (StringUtils.isNotBlank(mProductVersion.getSampleNo())) {
            QueryGoodsReq queryGoodsReq = new QueryGoodsReq();
            List<String> sample = new ArrayList<>();
            sample.add(mProductVersion.getSampleNo());
            queryGoodsReq.setSampleCodes(sample);
            Page pageES = new Page();
            pageES.setPageNo(1);
            pageES.setPageSize(1000);
            goodSpuResps = productService.searchGoods(queryGoodsReq, pageES);
        }
        //查询销量
        Integer salesNumber = 0;
        salesNumber = productVersionSalesRepository.querySalesByModelNumberOrSampleCode(mProductVersion.getModelNumber(), mProductVersion.getSampleNo());

        //查询拼图片的相关数据
        //根据es查询样衣号的品牌和skc_id数据
        SampleProductReq sampleProductReq = new SampleProductReq();
        sampleProductReq.setName(mProductVersion.getSampleNo());
        Page pageES = new Page();
        pageES.setPageNo(1);
        pageES.setPageSize(1000);
        List<SampleProductSkcResp> sampleProductSkcResp = productService.searchSampleProductSkc(sampleProductReq, pageES, "");

        // 查询全属性以及关联的值
        List<MQueriesAttrConEntity> attrs = productVersionRepository.queryMpAttrByMPId(mProductVersion.getId());
        Triple<List<MQueriesTab>, List<MQueriesTab>, List<MQueriesTab>> dataList = queryFromDataBaseCategoryAndAttr();
        MQueriesTab category = dataList.getLeft().stream()
                .filter(v -> Objects.equals(v.getCode(), mProductVersion.getCategory()))
                .findFirst().orElse(null);
        //品类
        if (category == null) {
            throw new RuntimeException("该数据有问题" + mProductVersion.getCategory());
        }
        // 查询全属性关联的属性
        List<MQueriesAttrConnection> connections = mQueriesAttrConnectionRepository.selectAllConnectBySmallCategoryId(category.getId());

        // 查询全属性
        List<MQueriesAttrConEntity> allAttrs = new ArrayList<>();
        if (attrs.size() != connections.size()) {
            List<MPQueAttrValueConnect> saveList = new ArrayList<>();
            // 处理一级数据
            saveList = dealSaveAttrValueData(attrs, connections, saveList, mProductVersion, 1);

            // 处理二级数据
            saveList = dealSaveAttrValueData(attrs, connections, saveList, mProductVersion, 2);

            if (CollectionUtils.isNotEmpty(saveList)) {
                productVersionRepository.batchSaveValueConnects(saveList);
            }

            allAttrs = productVersionRepository.queryMpAttrByMPId(mProductVersion.getId());

        } else {
            allAttrs = attrs;
        }

        Triple<Map<String, String>, Map<String, String>, Map<String, List<String>>> triple =
                buildProductVersion(Lists.newArrayList(mProductVersion.getSampleNo()));


        return ProductVersionConvert.buildDetail(mProductVersion, allAttrs, goodSpuResps,
                category, citations, salesNumber, sampleProductSkcResp, triple);
    }

    private List<MPQueAttrValueConnect> dealSaveAttrValueData(List<MQueriesAttrConEntity> attrs, List<MQueriesAttrConnection> connections, List<MPQueAttrValueConnect> saveList, MProductVersion mProductVersion, int i) {
        List<MQueriesAttrConEntity> filterAttrs = attrs.stream().filter(v -> Objects.equals(v.getType(), i)).collect(Collectors.toList());
        List<MQueriesAttrConnection> filterConnects = connections.stream().filter(v -> Objects.equals(v.getType(), i)).collect(Collectors.toList());
        if (filterAttrs.size() == filterConnects.size()) {
            return saveList;
        }
        if (Objects.equals(i, 1)) {
            // 当类型是1时
            HashMap<Long, MQueriesAttrConEntity> map = filterAttrs.stream().collect(HashMap::new, (k, v) -> k.put(v.getAttributeId(), v), HashMap::putAll);
            filterConnects.forEach(v -> {
                MQueriesAttrConEntity entity = map.get(v.getAttributeId());
                if (entity == null) {
                    MPQueAttrValueConnect valueConnect = new MPQueAttrValueConnect();
                    valueConnect.setId(IdLeaf.getId(mpvQueriesAttrConnectLeafTag));
                    valueConnect.setCreateTime(new Date());
                    valueConnect.setUpdateTime(new Date());
                    valueConnect.setMvProductId(mProductVersion.getId());
                    valueConnect.setQueConnectId(v.getId());
                    valueConnect.setValueName("");
                    valueConnect.setValue("");
                    saveList.add(valueConnect);
                }
            });
        } else {
            // 当类型是2时
            HashMap<Long, MQueriesAttrConEntity> map = filterAttrs.stream().collect(HashMap::new, (k, v) -> k.put(v.getConnectAttributeId(), v), HashMap::putAll);
            filterConnects.forEach(v -> {
                MQueriesAttrConEntity entity = map.get(v.getConnectAttributeId());
                if (entity == null) {
                    MPQueAttrValueConnect valueConnect = new MPQueAttrValueConnect();
                    valueConnect.setId(IdLeaf.getId(mpvQueriesAttrConnectLeafTag));
                    valueConnect.setCreateTime(new Date());
                    valueConnect.setUpdateTime(new Date());
                    valueConnect.setMvProductId(mProductVersion.getId());
                    valueConnect.setQueConnectId(v.getId());
                    valueConnect.setValueName("");
                    valueConnect.setValue("");
                    saveList.add(valueConnect);
                }
            });
        }
        return saveList;
    }

    @Override
    public Boolean editDetail(ProductVersionEditDetailReq requestData) {
        //根据id 查询数据
        MProductVersion mProductVersion = productVersionRepository.queryDetail(requestData.getId());
        if (mProductVersion == null) {
            throw new RuntimeException("没有该商品信息，请校验商品id是否传错");
        }
        //校验入参 并组装参数
        Pair<MProductVersion, List<MPQueAttrValueConnect>> pair = checkEditParams(requestData, mProductVersion, requestData.getAttrs());
        template.execute(v -> {
            productVersionRepository.updateProductVersion(pair.getLeft());
            List<MPQueAttrValueConnect> valueConnects = pair.getRight();
            if (CollectionUtils.isNotEmpty(valueConnects)) {
                productVersionRepository.batchUpdateValue(valueConnects);
            }
            return true;
        });
        return true;
    }

    @Override
    public ProductVersionSalesResp querySales(ProductVersionSalesReq requestData, Page page) {
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        String sampleCode = null;
        //如果根据版型号查询，需要将对应版型的样衣号取出 一并查询
        if (StringUtils.isNotBlank(requestData.getModelNumber())) {
            QueryWrapper<MProductVersion> query = new QueryWrapper<>();
            query.eq("MODEL_NUMBER", requestData.getModelNumber());
            query.eq("IS_DELETE", IsDeleteEnum.NORMAL.getCode());
            List<MProductVersion> mProductVersions = mProductVersionMapper.selectList(query);
            if (CollectionUtils.isEmpty(mProductVersions)) {
                return new ProductVersionSalesResp();
            }
            sampleCode = mProductVersions.get(0).getSampleNo();
        }
        List<MProductVersionSales> mProductVersionSales = productVersionSalesRepository.selectByParams(requestData, sampleCode);

        //查询总金额和总销量
        Pair<BigDecimal, BigDecimal> retSum = productVersionSalesRepository.selectSumByParam(requestData, sampleCode);

        //查询当前款号的价格
        HashMap<String, GoodSpuResp> spuMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(mProductVersionSales)) {
            List<String> names = mProductVersionSales.stream().map(MProductVersionSales::getSpu).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(names)) {
                // 根据sample_code查询数据
                Page pageES = new Page();
                pageES.setPageNo(1);
                pageES.setPageSize(1000);
                QueryGoodsReq req = new QueryGoodsReq();
                req.setNames(names);
                List<GoodSpuResp> searchGoods = productService.searchGoods(req, pageES);
                if (CollectionUtils.isNotEmpty(searchGoods)) {
                    spuMap = searchGoods.stream().collect(HashMap::new, (k, v) -> k.put(v.getName(), v), HashMap::putAll);
                }
            }
        }
        return ProductVersionConvert.buildSalesResp(mProductVersionSales, hPage, page, retSum, spuMap);
    }

    @Override
    public ProductVersionSalesQueriesTabResp querySalesQueries(ProductVersionSalesQueriesReq req) {
        List<MProductVersion> mProductVersions = new ArrayList<>();
        if (StringUtils.isNotBlank(req.getModelNumber())) {
            //根据入参模糊匹配
            mProductVersions = productVersionRepository.queryByParams(req.getModelNumber());
        } else {
            //获取所有版型
            mProductVersions = productVersionRepository.queryAll();
        }

        //筛选条件
        List<MQueriesTab> salesListFilter = mQueriesTabRepository.getSalesFilterList(MQueriesTabEnum.SALES_LIST_FILTER);
        return ProductVersionConvert.buildSalesQueries(salesListFilter, mProductVersions);
    }

    @Override
    public ProductVersionEditDicResp queryDetailDic(ProductVersionDetailDicReq requestData) {
        //查询字典
        List<MQueriesTab> brandList = mQueriesTabRepository.getBrandList(MQueriesTabEnum.BRAND);
        List<MQueriesTab> attributeList = mQueriesTabRepository.getAttributeList(MQueriesTabEnum.ATTRIBUTE);
        List<MQueriesTab> subAttributeList = mQueriesTabRepository.getAttributeList(MQueriesTabEnum.ATTRIBUTE_SUB);

        // 获取该小类目所有的一级和二级属性
        List<MQueriesAttrConnection> connects = mQueriesAttrConnectionRepository.selectBySmallCateCode(requestData.getCategoryCode());
        return ProductVersionConvert.buildProductEditDic(brandList, attributeList, subAttributeList, connects);
    }

    @Override
    public List<PVQueryStatsResp> queryStatsParams(PVQueryStatsReq requestData) {
        List<MQueriesTab> categoryList;
        if (requestData.getCategoryId() == null) {
            //查询类目列表
            categoryList = mQueriesTabRepository.getFirstCateGoryList(MQueriesTabEnum.CATEGORY);
        } else {
            categoryList = mQueriesTabRepository.getCateGoryListByPid(MQueriesTabEnum.CATEGORY, requestData.getCategoryId());
        }

        return ProductVersionConvert.buildProductVersionQueryStatsResp(categoryList);
    }

    @Override
    public List<PVQueryStatsListResp> queryStatsList(PVQueryStatsListReq requestData, Page page) {
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<MQueriesAttrConnection> list = mQueriesAttrConnectionRepository.selectStatsListByParams(requestData);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<MQueriesAttrConnection> attributeInfo = mQueriesAttrConnectionRepository.selectByAttributeInfo(list);

        List<PVQueryStatsListResp> rets = ProductVersionConvert.buildStatsList(list, attributeInfo, mQueriesTabRepository);
        PageInfo<PVQueryStatsListResp> pageInfo = new PageInfo(hPage);
        pageInfo.setList(rets);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    @Override
    public PVQueryStatsDetailResp queryStatsDetail(PVQueryStatsDetailReq requestData) {

        // 获取类目及其子类
        List<MQueriesTab> cateGoryList = mQueriesTabRepository.getCateGoryList(MQueriesTabEnum.CATEGORY);

        // 查询该属性值所有的关联数据
        List<MQueriesAttrConnection> connections = new ArrayList<>();
        List<MQueriesTab> firstAttrValue = new ArrayList<>();
        List<MQueriesTab> secondAttrValue = new ArrayList<>();
        MQueriesTab attr = null;
        if (Objects.nonNull(requestData.getAttributeId())) {
            MQueriesAttrConnection connection = new MQueriesAttrConnection();
            connection.setAttributeId(requestData.getAttributeId());
            connections = mQueriesAttrConnectionRepository.selectByAttributeInfo(Lists.newArrayList(connection));

            // 获取一级属性值 包含被删除的
            firstAttrValue = mQueriesTabRepository.getAttributeListByPidIncludeDeleted(MQueriesTabEnum.ATTRIBUTE, requestData.getAttributeId());

            // 获取二级属性 包含被删除的
            secondAttrValue = mQueriesTabRepository.getSubAttrListByPidIncludeDeleted(MQueriesTabEnum.ATTRIBUTE_SUB, requestData.getAttributeId());

            // 查询当前属性
            attr = mQueriesTabRepository.getAttrById(requestData.getAttributeId());
        }


        return ProductVersionConvert.buildStatsDetail(firstAttrValue, secondAttrValue, cateGoryList, connections, requestData.getAttributeId(), attr);
    }

    @Override
    public Boolean saveStatsDetail(PVSaveQueryStatsDetailReq requestData) {
        List<MQueriesTab> saveAttrValueList = new ArrayList<>();
        List<MQueriesTab> updateAttrValueList = new ArrayList<>();
        // 获取当前被删除属性值
        List<Long> deletedSecAttr = requestData.getSecAttrList().stream()
                .filter(v -> Objects.nonNull(v.getAttrId()) && Objects.equals(v.getIsSelected(), 0))
                .map(PVSaveQueryStatsDetailReq.AttrValueData::getAttrId)
                .collect(Collectors.toList());

        // 1、假设无属性id 对属性进行保存
        MQueriesTab mainTab = addStatsAttr(requestData);
        if (mainTab != null) {
            saveAttrValueList.add(mainTab);
        }

        // 1、处理属性值 没有id是新增 isSelected为0是删除
        // 1.1、如果id为空 则是新增 其余为更新
        List<PVSaveQueryStatsDetailReq.AttrValueData> saveAttrValueData = requestData.getAttrValueList().stream()
                .filter(v -> Objects.isNull(v.getAttrId())).collect(Collectors.toList());
        // 更新
        List<PVSaveQueryStatsDetailReq.AttrValueData> updateAttrValueData = requestData.getAttrValueList().stream()
                .filter(v -> Objects.nonNull(v.getAttrId())).collect(Collectors.toList());
        // 重复校验
        List<String> repeatAttrValue = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(saveAttrValueData)) {
            List<MQueriesTab> finalSaveAttrValueList = saveAttrValueList;
            saveAttrValueData.forEach(v -> {
                if (repeatAttrValue.contains(v.getAttrDesc())) {
                    throw new RuntimeException("当前名字存在重复的，请检查是否存在同名");
                }
                repeatAttrValue.add(v.getAttrDesc());
                Long mQueriesId = mQueriesTabMapper.getMQueriesId();
                Long attrId = mQueriesTabMapper.getAttrValueId();
                String attrCode = requestData.getAttributeCode() + "-" + queriesAttrValueCode + Objects.toString(attrId);
                MQueriesTab tab = new MQueriesTab();
                tab.setId(mQueriesId);
                tab.setNode(MQueriesTabEnum.ATTRIBUTE.getNode());
                tab.setSubNode(MQueriesTabEnum.ATTRIBUTE.getSubNode());
                tab.setCode(attrCode);
                tab.setName(v.getAttrDesc());
                tab.setType(MQueriesTabEnum.ATTRIBUTE.getCode());
                tab.setPid(mainTab == null ? requestData.getAttributeId() : mainTab.getId());
                tab.setCreateTime(new Date());
                tab.setUpdateTime(new Date());
                tab.setIsDelete(Objects.equals(v.getIsSelected(), 0) ? 1 : 0);
                finalSaveAttrValueList.add(tab);
            });
        }

        if (CollectionUtils.isNotEmpty(updateAttrValueData)) {
            updateAttrValueData.forEach(v -> {
                if (repeatAttrValue.contains(v.getAttrDesc())) {
                    throw new RuntimeException("当前名字存在重复的，请检查是否存在同名");
                }
                repeatAttrValue.add(v.getAttrDesc());
                MQueriesTab tab = new MQueriesTab();
                tab.setId(Long.valueOf(v.getAttrId()));
                tab.setName(v.getAttrDesc());
                tab.setIsDelete(Objects.equals(v.getIsSelected(), 0) ? 1 : 0);
                updateAttrValueList.add(tab);
            });
        }

        // 2、处理二级属性
        List<PVSaveQueryStatsDetailReq.AttrValueData> saveSecAttrData = requestData.getSecAttrList().stream()
                .filter(v -> Objects.isNull(v.getAttrId())).collect(Collectors.toList());
        // 更新
        List<PVSaveQueryStatsDetailReq.AttrValueData> updateSecAttrData = requestData.getSecAttrList().stream()
                .filter(v -> Objects.nonNull(v.getAttrId())).collect(Collectors.toList());
        List<String> sceAttr = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(saveSecAttrData)) {
            List<MQueriesTab> finalSaveAttrValueList1 = saveAttrValueList;
            saveSecAttrData.forEach(v -> {
                if (sceAttr.contains(v.getAttrDesc())) {
                    throw new RuntimeException("当前名字存在重复的，请检查是否存在同名");
                }
                sceAttr.add(v.getAttrDesc());
                Long mQueriesId = mQueriesTabMapper.getMQueriesId();
                Long attrId = mQueriesTabMapper.getSecAttrId();
                String attrCode = requestData.getAttributeCode() + "-" + queriesSecAttrCode + Objects.toString(attrId);
                MQueriesTab tab = new MQueriesTab();
                tab.setId(mQueriesId);
                tab.setNode(MQueriesTabEnum.ATTRIBUTE_SUB.getNode());
                tab.setSubNode(MQueriesTabEnum.ATTRIBUTE_SUB.getSubNode());
                tab.setCode(attrCode);
                tab.setName(v.getAttrDesc());
                tab.setType(MQueriesTabEnum.ATTRIBUTE_SUB.getCode());
                tab.setCreateTime(new Date());
                tab.setUpdateTime(new Date());
                tab.setPid(mainTab == null ? requestData.getAttributeId() : mainTab.getId());
                tab.setIsDelete(Objects.equals(v.getIsSelected(), 0) ? 1 : 0);
                finalSaveAttrValueList1.add(tab);
            });
        }
        if (CollectionUtils.isNotEmpty(updateSecAttrData)) {
            updateSecAttrData.forEach(v -> {
                if (sceAttr.contains(v.getAttrDesc())) {
                    throw new RuntimeException("当前名字存在重复的，请检查是否存在同名");
                }
                sceAttr.add(v.getAttrDesc());
                MQueriesTab tab = new MQueriesTab();
                tab.setId(Long.valueOf(v.getAttrId()));
                tab.setName(v.getAttrDesc());
                tab.setIsDelete(Objects.equals(v.getIsSelected(), 0) ? 1 : 0);
                updateAttrValueList.add(tab);
            });
        }

        // 查询已有的关联关系
        List<MQueriesAttrConnection> oldConnects = new ArrayList();
        if (requestData.getAttributeId() != null) {
            oldConnects = mQueriesAttrConnectionRepository.selectConnectByAttrIds(requestData.getAttributeId());
        }

        // 3、处理主属性和类目的关联关系
        List<MQueriesAttrConnection> saveConnections = new ArrayList<>();
        List<MQueriesAttrConnection> updateConnections = new ArrayList<>();
        List<PVSaveQueryStatsDetailReq.CategoryData> needSaveCategoryData = requestData.getCategoryList().stream()
                .filter(v -> (Objects.isNull(v.getId()) && Objects.equals(v.getIsSelected(), 1)))
                .collect(Collectors.toList());
        List<PVSaveQueryStatsDetailReq.CategoryData> needUpdateCategoryData = requestData.getCategoryList().stream()
                .filter(v -> Objects.nonNull(v.getId()))
                .collect(Collectors.toList());
        List<MQueriesAttrConnection> finalOldConnects = oldConnects;
        if (CollectionUtils.isNotEmpty(needSaveCategoryData)) {
            needSaveCategoryData.forEach(v -> {
                // 判断是都已存在关联中 若已经有数据但不存在更新数据里，则加进更新
                if (CollectionUtils.isNotEmpty(finalOldConnects)) {
                    MQueriesAttrConnection attrConnection = finalOldConnects.stream().filter(x -> Objects.equals(x.getSmallCategoryName(), v.getSmallCategoryDesc())
                            && Objects.equals(x.getType(), 1)).findFirst().orElse(null);
                    if (attrConnection != null) {
                        MQueriesAttrConnection connection = new MQueriesAttrConnection();
                        connection.setId(attrConnection.getId());
                        connection.setIsDelete(Objects.equals(v.getIsSelected(), 0) ? 1 : 0);
                        updateConnections.add(connection);
                        return;
                    }
                }
                MQueriesAttrConnection connection = new MQueriesAttrConnection();
                connection.setId(IdLeaf.getDateId(pvQueriesConnectLeafTag));
                connection.setType(1);
                connection.setCreateTime(new Date());
                connection.setUpdateTime(new Date());
                connection.setAttributeId(mainTab == null ? requestData.getAttributeId() : mainTab.getId());
                connection.setAttributeCode(requestData.getAttributeCode());
                connection.setAttributeName(requestData.getAttributeName());
                connection.setBigCategoryId(v.getBigCategoryId());
                connection.setBigCategoryName(v.getBigCategoryDesc());
                connection.setBigCategoryCode(v.getBigCategoryCode());
                connection.setSmallCategoryId(v.getSmallCategoryId());
                connection.setSmallCategoryName(v.getSmallCategoryDesc());
                connection.setSmallCategoryCode(v.getSmallCategoryCode());
                saveConnections.add(connection);
            });
        }
        if (CollectionUtils.isNotEmpty(needUpdateCategoryData)) {
            needUpdateCategoryData.forEach(v -> {
                MQueriesAttrConnection connection = new MQueriesAttrConnection();
                connection.setId(v.getId());
                connection.setIsDelete(Objects.equals(v.getIsSelected(), 0) ? 1 : 0);
                updateConnections.add(connection);
            });
        }

        // 4、处理类目和二级属性的关联关系
        List<PVSaveQueryStatsDetailReq.CategoryData> needSaveSecAttrConnectData = requestData.getSecAttrConnectList().stream()
                .filter(v -> (Objects.isNull(v.getId()) && Objects.equals(v.getIsSelected(), 1)))
                .collect(Collectors.toList());
        List<PVSaveQueryStatsDetailReq.CategoryData> needUpdateSecAttrConnectData = requestData.getSecAttrConnectList().stream()
                .filter(v -> Objects.nonNull(v.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needSaveSecAttrConnectData)) {
            List<MQueriesTab> finalSaveAttrValueList3 = saveAttrValueList;
            needSaveSecAttrConnectData.forEach(v -> {
                if (CollectionUtils.isNotEmpty(finalOldConnects)) {
                    MQueriesAttrConnection attrConnection = finalOldConnects.stream().filter(x -> Objects.equals(x.getConnectAttributeName(), v.getAttrDesc())
                            && Objects.equals(x.getType(), 2) && Objects.equals(x.getSmallCategoryName(), v.getSmallCategoryDesc())).findFirst().orElse(null);
                    if (attrConnection != null) {
                        MQueriesAttrConnection connection = new MQueriesAttrConnection();
                        connection.setId(attrConnection.getId());
                        connection.setConnectAttributeName(v.getAttrDesc());
                        connection.setIsDelete(Objects.equals(v.getIsSelected(), 0) ? 1 : 0);
                        updateConnections.add(connection);
                        return;
                    }
                }
                MQueriesAttrConnection connection = new MQueriesAttrConnection();
                connection.setId(IdLeaf.getDateId(pvQueriesConnectLeafTag));
                connection.setType(2);
                connection.setCreateTime(new Date());
                connection.setUpdateTime(new Date());
                connection.setAttributeId(mainTab == null ? requestData.getAttributeId() : mainTab.getId());
                connection.setAttributeCode(requestData.getAttributeCode());
                connection.setAttributeName(requestData.getAttributeName());
                connection.setBigCategoryId(v.getBigCategoryId());
                connection.setBigCategoryName(v.getBigCategoryDesc());
                connection.setBigCategoryCode(v.getBigCategoryCode());
                connection.setSmallCategoryId(v.getSmallCategoryId());
                connection.setSmallCategoryName(v.getSmallCategoryDesc());
                connection.setSmallCategoryCode(v.getSmallCategoryCode());
                if (CollectionUtils.isNotEmpty(saveSecAttrData) &&
                        saveSecAttrData.stream().map(PVSaveQueryStatsDetailReq.AttrValueData::getAttrDesc)
                                .collect(Collectors.toList()).contains(v.getAttrDesc())) {
                    MQueriesTab mQueriesTab = finalSaveAttrValueList3.stream().filter(x -> (Objects.equals(x.getType(), MQueriesTabEnum.ATTRIBUTE_SUB.getCode())
                            && Objects.equals(x.getName(), v.getAttrDesc()))).findFirst().orElse(null);
                    if (mQueriesTab == null) {
                        return;
                    }
                    connection.setConnectAttributeId(mQueriesTab.getId());
                    connection.setConnectAttributeCode(mQueriesTab.getCode());
                } else {
                    connection.setConnectAttributeId(v.getAttrId());
                    connection.setConnectAttributeCode(v.getAttrCode());
                }

                connection.setConnectAttributeName(v.getAttrDesc());
                saveConnections.add(connection);
            });
        }
        if (CollectionUtils.isNotEmpty(needUpdateSecAttrConnectData)) {
            needUpdateSecAttrConnectData.forEach(v -> {
                MQueriesAttrConnection connection = new MQueriesAttrConnection();
                connection.setId(v.getId());
                connection.setConnectAttributeName(v.getAttrDesc());
                connection.setIsDelete(Objects.equals(v.getIsSelected(), 0) ? 1 : 0);
                updateConnections.add(connection);
            });
        }

        List<MQueriesTab> finalSaveAttrValueList2 = saveAttrValueList;
        template.execute(v -> {
            if (CollectionUtils.isNotEmpty(finalSaveAttrValueList2)) {
                mQueriesTabRepository.batchSaveAttrValue(finalSaveAttrValueList2);
            }

            if (CollectionUtils.isNotEmpty(updateAttrValueList)) {
                mQueriesTabRepository.batchUpdateAttrValue(updateAttrValueList);
            }

            if (CollectionUtils.isNotEmpty(saveConnections)) {
                mQueriesAttrConnectionRepository.batchSaveConnections(saveConnections);
            }

            if (CollectionUtils.isNotEmpty(updateConnections)) {
                mQueriesAttrConnectionRepository.batchUpdateConnections(updateConnections);
            }

            if (CollectionUtils.isNotEmpty(deletedSecAttr)) {
                mQueriesAttrConnectionRepository.batchUpdateBySceAttrIds(deletedSecAttr);
            }
            return v;
        });
        return true;
    }

    @Override
    public void downFileForUpdateProductVersion(String url, String keys) throws IOException {
        log.info("==================版型库更新");
        String filePath = FileParseUtil.downLoadExcel(url);
        // 查询所有属性
        Triple<List<MQueriesTab>, List<MQueriesTab>, List<MQueriesTab>> queryFromDataBaseRet = queryFromDataBase();
        // 查询类目全属性
        Map<String, List<MQueriesAttrConnection>> smallCategoryAttrConnectMap = queryAttrConnect();

        // 正常数据 和 错误数据
        List<MProductVersion> normalDataList = new ArrayList<>();
        List<ParseProductVersionEntity.ErrorData> errorDataList  = new ArrayList<>();

        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {
            @Override
            protected void saveData() {
                ParseProductVersionEntity productVersionEntity = assembleDataForUpdate(this.cachedDataList, queryFromDataBaseRet, smallCategoryAttrConnectMap);
                //保存数据
                dealUpdateData(productVersionEntity);
                if (CollectionUtils.isNotEmpty(productVersionEntity.getBaseDataList())) {
                    normalDataList.addAll(productVersionEntity.getBaseDataList());
                }

                if(CollectionUtils.isNotEmpty(productVersionEntity.getErrorDataList())){
                    errorDataList.addAll(productVersionEntity.getErrorDataList());
                }
            }
        });

        if (CollectionUtils.isNotEmpty(errorDataList)) {
            String fileName = System.currentTimeMillis() + ".xlsx";
            EasyExcelUtil.write(fileName, ParseProductVersionEntity.ErrorData.class, new IWriteDataExcel<ParseProductVersionEntity.ErrorData>() {
                @Override
                public List<ParseProductVersionEntity.ErrorData> getData() {
                    return errorDataList.stream().map(item -> {
                        ParseProductVersionEntity.ErrorData data = new ParseProductVersionEntity.ErrorData();
                        data.setModelNumber(item.getModelNumber());
                        data.setSampleNo(item.getSampleNo());
                        data.setBrand(item.getBrand());
                        data.setMsg(item.getMsg());
                        return data;
                    }).collect(Collectors.toList());
                }
            });
            File file = new File(fileName);
            String param = qiniuUtil.upload(file.getPath(), "异常版型商品"+System.currentTimeMillis() + ".xlsx");
            Integer success = CollectionUtils.isEmpty(normalDataList) ? 0 : normalDataList.size();
            Integer fail = CollectionUtils.isEmpty(errorDataList) ? 0 : errorDataList.size();
            Integer sum = success + fail;
            param = param + ";" + sum + ";" + success + ";" + fail;
            file.delete();
            log.info("redis的key{}", param);
            RedisTemplateUtil.setex(redisPoolUtil,keys, param,60);
        }
        Files.deleteIfExists(Paths.get(filePath));
    }

    @Override
    public Boolean enableDisable(PVEnableDisableAttrReq req) {
        // 查询该属性
        MQueriesTab attr = mQueriesTabRepository.getAttrById(req.getAttrId());
        if (attr == null) {
            throw new ProductException("未找到该属性！");
        }
        if (Objects.equals(attr.getName(), "合体度")) {
            throw new ProductException("合体度不允许操作！");
        }
        if (Objects.equals(req.getIsDisabled(), attr.getIsDelete())) {
            throw new ProductException("操作状态和原状态相同，无需操作！");
        }

        // ----------------启用
        if (Objects.equals(req.getIsDisabled(), 0)) {
            MQueriesTab tab = new MQueriesTab();
            tab.setId(req.getAttrId());
            tab.setIsDelete(req.getIsDisabled());
            tab.setName(attr.getName());
            // 恢复数据
            String attrIdConnectIds = mpqAttrConnectionLogMapper.selectByAttrId(req.getAttrId());
            template.execute(ac -> {
                mQueriesTabRepository.batchUpdateAttrValue(Lists.newArrayList(tab));
                if (StringUtils.isNotEmpty(attrIdConnectIds)) {
                    List<String> ids = Arrays.stream(attrIdConnectIds.split(",")).collect(Collectors.toList());
                    mQueriesAttrConnectionRepository.updateValidByIds(ids);
                }
                return ac;
            });
        } else {
        // ----------------禁用
        // 禁用属性
            MQueriesTab tab = new MQueriesTab();
            tab.setId(req.getAttrId());
            tab.setIsDelete(req.getIsDisabled());
            tab.setName(attr.getName());
        // 禁用类目全属性 -- 获取当前的属性快照，启用时需要恢复
            List<String> attrIdConnectIds = mQueriesAttrConnectionRepository.selectByAttrId(req.getAttrId());
            MPQAttrConnectionLog log = packageSnapshotLog(attrIdConnectIds, req.getAttrId());
            template.execute(ac -> {
                mQueriesTabRepository.batchUpdateAttrValue(Lists.newArrayList(tab));
                mQueriesAttrConnectionRepository.updateByAttrId(req.getAttrId());
                if (log != null) {
                    mpqAttrConnectionLogMapper.insert(log);
                }
                return ac;
            });

        }
        return true;
    }

    private MPQAttrConnectionLog packageSnapshotLog(List<String> attrIdConnectIds, Long attrId) {
        if (CollectionUtils.isEmpty(attrIdConnectIds)) {
            return null;
        }
        MPQAttrConnectionLog log = new MPQAttrConnectionLog();
        log.setId(IdLeaf.getId(pvQueriesConnectLeafTag));
        log.setMQueriesTabId(attrId);
        log.setMPQAttrConnectIds(attrIdConnectIds.stream().collect(Collectors.joining(",")));
        log.setUpdateTime(new Date());
        log.setCreateTime(new Date());
        return log;
    }

    private void dealUpdateData(ParseProductVersionEntity productVersionEntity) {
        // 更新商品主数据
        if (CollectionUtils.isEmpty(productVersionEntity.getBaseDataList())) {
            return;
        }
        template.execute(action -> {
            if (CollectionUtils.isNotEmpty(productVersionEntity.getBaseDataList())) {
                mProductVersionMapper.batchUpdateMp(productVersionEntity.getBaseDataList());
            }
            List<String> ids = productVersionEntity.getBaseDataList().stream().map(MProductVersion::getId).collect(Collectors.toList());

            // 更新照片数据
            // 删除原来的图片
            mProductVersionPicMapper.delAllPicsByMpIds(ids);
            if (CollectionUtils.isNotEmpty(productVersionEntity.getPics())) {
                productVersionPicService.saveBatch(productVersionEntity.getPics());
            }

            // 更新属性值数据
            productVersionRepository.delAllMPValueConnectByMpIds(ids);
            if (CollectionUtils.isNotEmpty(productVersionEntity.getMpQueAttrValueConnects())) {
                productVersionRepository.batchSaveValueConnects(productVersionEntity.getMpQueAttrValueConnects());
            }
            return action;
        });

    }

    private ParseProductVersionEntity assembleDataForUpdate(List<Map<Integer, String>> cachedDataList,
                                                            Triple<List<MQueriesTab>, List<MQueriesTab>,
                                                                    List<MQueriesTab>> tripleData,
                                                            Map<String, List<MQueriesAttrConnection>> smallCategoryAttrConnectMap) {
        ParseProductVersionEntity ret = new ParseProductVersionEntity();
        List<MProductVersion> list = new ArrayList<>();
        List<ParseProductVersionEntity.ErrorData> errorList = new ArrayList<>();
        List<MProductVersionPic> pics = new ArrayList<>();
        List<MPQueAttrValueConnect> valueConnectList = new ArrayList<>();
        for(Map<Integer,String> data: cachedDataList) {
            // 获取基础商品数据
            MProductVersion normalData = null;
            Date createTime = null;
            Integer isClose = null;
            String id = "";
            // 1、校验传入的属性[新增属性是否已增加过]
            if (StringUtils.isBlank(data.get(0))) {
                ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                        .ErrorData(data.get(0), data.get(1), data.get(2), "版型字段不能为空");
                errorList.add(errorData);
                continue;
            }
            List<MProductVersion> productVersionEntities = productVersionRepository.queryByModelNumber(data.get(0));
            if (CollectionUtils.isEmpty(productVersionEntities)) {
                ParseProductVersionEntity.ErrorData errorData = new ParseProductVersionEntity
                        .ErrorData(data.get(0), data.get(1), data.get(2), "该版型库不存在");
                errorList.add(errorData);
                continue;
            } else {
                normalData = productVersionEntities.get(0);
                createTime = normalData.getCreateTime();
                isClose = normalData.getIsClose();
                id = normalData.getId();
            }
            normalData = getMProductVersion(tripleData, list, errorList, data, normalData);
            if (normalData == null) {
                continue;
            }
            normalData.setCreateTime(createTime);
            normalData.setIsClose(isClose);
            normalData.setId(id);

            // 2、查询对应类目的全属性进行组装数据
            dealAttrValue(data, normalData, smallCategoryAttrConnectMap,
                    tripleData, errorList, valueConnectList);

            // 3、组装图片数据
            if (StringUtils.isNotBlank(data.get(38))) {
                dealExportPic(data.get(38), pics, normalData, 38);
            }
            if (StringUtils.isNotBlank(data.get(39))) {
                dealExportPic(data.get(39), pics, normalData, 39);
            }
            if (StringUtils.isNotBlank(data.get(40))) {
                dealExportPic(data.get(40), pics, normalData, 40);
            }
            if (StringUtils.isNotBlank(data.get(41))) {
                dealExportPic(data.get(41), pics, normalData, 41);
            }
            if (StringUtils.isNotBlank(data.get(42))) {
                dealExportPic(data.get(42), pics, normalData, 42);
            }

        }

        // 3、保存更新的数据
        ret.setBaseDataList(list);
        ret.setErrorDataList(errorList);
        ret.setPics(pics);
        ret.setMpQueAttrValueConnects(valueConnectList);
        return ret;

    }


    public MQueriesTab addStatsAttr(PVSaveQueryStatsDetailReq requestData) {
        MQueriesTab tab = new MQueriesTab();
        if (requestData.getAttributeId() == null) {
            // 对参数进行校验
            List<MQueriesTab> firstCateGoryList = mQueriesTabRepository.getFirstCateGoryList(MQueriesTabEnum.ATTRIBUTE);
            checkIsRepeat(requestData.getAttributeName(), firstCateGoryList);
            Long mQueriesId = mQueriesTabMapper.getMQueriesId();
            Long attrId = mQueriesTabMapper.getAttrId();
            String attrCode = queriesAttrCode + Objects.toString(attrId);
            requestData.setAttributeCode(attrCode);
            tab.setId(mQueriesId);
            tab.setNode(MQueriesTabEnum.ATTRIBUTE.getNode());
            tab.setSubNode(MQueriesTabEnum.ATTRIBUTE.getSubNode());
            tab.setCode(attrCode);
            tab.setName(requestData.getAttributeName());
            tab.setType(MQueriesTabEnum.ATTRIBUTE.getCode());
            tab.setCreateTime(new Date());
            tab.setUpdateTime(new Date());
            return tab;
        }
        return null;
    }

    private void checkIsRepeat(String name, List<MQueriesTab> firstCateGoryList) {
        if (CollectionUtils.isNotEmpty(firstCateGoryList)) {
            MQueriesTab first = firstCateGoryList.stream()
                    .filter(v -> Objects.equals(v.getName(),name)).findFirst().orElse(null);
            if (first != null) {
                throw new RuntimeException("当前名称已经存在，请校验是否确认增加此名称！");
            }
        }
    }

    private Pair<MProductVersion, List<MPQueAttrValueConnect>> checkEditParams(ProductVersionEditDetailReq requestData, MProductVersion mProductVersion,
                                            List<ProductVersionDetailResp.BaseInfo> attrs) {
        //查询字典
        List<MQueriesTab> brandList = mQueriesTabRepository.getBrandList(MQueriesTabEnum.BRAND);
        List<MQueriesTab> attributeList = mQueriesTabRepository.getAttributeList(MQueriesTabEnum.ATTRIBUTE);
        List<MQueriesTab> subAttributeList = mQueriesTabRepository.getAttributeList(MQueriesTabEnum.ATTRIBUTE_SUB);

        //修改商品主基础数据
        MQueriesTab brand = null;
        if (StringUtils.isNotBlank(requestData.getBrandCode())) {
            brand = brandList.stream()
                    .filter(v -> Objects.equals(v.getCode(), requestData.getBrandCode())).findFirst().orElse(null);
            if (brand == null) {
                throw new RuntimeException("品牌信息有误");
            }
            mProductVersion.setBrandCode(brand.getCode());
            mProductVersion.setBrand(brand.getName());
        }

        BeanUtils.copyProperties(requestData, mProductVersion);
        if (brand != null) {
            mProductVersion.setBrand(brand.getName());
        }
        mProductVersion.setUpdateTime(new Date());

        // 修改商品属性
        List<MPQueAttrValueConnect> updates = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attrs)) {
            // 一级属性校验值
            attrs.forEach(v -> {
                MPQueAttrValueConnect valueConnect = new MPQueAttrValueConnect();
                valueConnect.setId(v.getId());
                MQueriesTab mQueriesTab = attributeList.stream().filter(x -> Objects.equals(x.getCode(), v.getAttrCode())).findFirst().orElse(null);
                if (mQueriesTab == null) {
                    return;
                }
                if (StringUtils.isNotBlank(v.getValueCode())) {
                    MQueriesTab checkMQueriesTabs = attributeList.stream()
                            .filter(x -> (Objects.equals(x.getPid(), mQueriesTab.getId()) && Objects.equals(x.getCode(), v.getValueCode())))
                            .findFirst().orElse(null);
                    if (checkMQueriesTabs == null) {
                        throw new RuntimeException(v.getAttrName() + "的值有误！");
                    }
                    valueConnect.setValue(checkMQueriesTabs.getCode());
                    valueConnect.setValueName(checkMQueriesTabs.getName());
                } else {
                    valueConnect.setValue("");
                    valueConnect.setValueName("");
                }
                updates.add(valueConnect);
                if (CollectionUtils.isNotEmpty(v.getChild())) {
                    v.getChild().forEach(x -> {
                        MPQueAttrValueConnect valueConnectData = new MPQueAttrValueConnect();
                        valueConnectData.setId(x.getId());
                        valueConnectData.setValueName(x.getValueName());
                        updates.add(valueConnectData);
                    });
                }
            });
        }
        return Pair.of(mProductVersion, updates);
    }

    private void dealData(ParseProductVersionEntity productVersionEntity) {
        if (productVersionEntity == null) {
            return;
        }
        template.execute(action -> {
            //保存主数据
            List<MProductVersion> baseDataList = productVersionEntity.getBaseDataList();
            List<MProductVersionPic> pics = productVersionEntity.getPics();
            if (CollectionUtils.isNotEmpty(baseDataList)) {
                saveBatch(baseDataList);
            }
            if (CollectionUtils.isNotEmpty(pics)) {
                productVersionPicService.saveBatch(pics);
            }
            if (CollectionUtils.isNotEmpty(productVersionEntity.getMpQueAttrValueConnects())) {
                productVersionRepository.batchSaveValueConnects(productVersionEntity.getMpQueAttrValueConnects());
            }
            return action;
        });

    }




    private void dealExportPic(String data, List<MProductVersionPic> pics, MProductVersion normalData, int i) {
        String[] split = data.split(",");
        List<String> picSources = Arrays.asList(split);
        if (CollectionUtils.isNotEmpty(picSources)) {
            picSources.forEach(v -> {
                MProductVersionPic pic = new MProductVersionPic();
                ProductVersionPicEnum picEnum = ProductVersionPicEnum.getByExcelPos(i);
                if (picEnum == null) {
                    return;
                }
                pic.setType(picEnum.getCode());
                pic.setPicUrl(v);
                pic.setId(IdLeaf.getId(productVersionLeafTag));
                pic.setProductVersionId(normalData.getId());
                pic.setCreateTime(new Date());
                pic.setUpdateTime(new Date());
                pics.add(pic);
            });
        }
    }




}
