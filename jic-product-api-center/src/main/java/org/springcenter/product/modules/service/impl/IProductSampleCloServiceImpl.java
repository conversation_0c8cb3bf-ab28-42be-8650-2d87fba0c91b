package org.springcenter.product.modules.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.ChatGptEnum;
import org.springcenter.product.modules.entity.BrandConvertEntity;
import org.springcenter.product.modules.entity.CommonAutoNameGenerateEntity;
import org.springcenter.product.modules.entity.CommonAutoNameSettingEntity;
import org.springcenter.product.modules.entity.CommonSearchByPageAndIdEntity;
import org.springcenter.product.modules.mapper.product.AiAssistantStyleMapper;
import org.springcenter.product.modules.mapper.product.SampleCloFabAutoNameInfoMapper;
import org.springcenter.product.modules.mapper.product.SampleCloFabInfoMapper;
import org.springcenter.product.modules.model.AiAssistantStyle;
import org.springcenter.product.modules.model.SampleCloFabAutoNameInfo;
import org.springcenter.product.modules.model.SampleCloFabInfo;
import org.springcenter.product.modules.remote.entity.AiAssistantReqEntity;
import org.springcenter.product.modules.remote.entity.AiAssistantRespEntity;
import org.springcenter.product.modules.remote.service.IAiAssistantService;
import org.springcenter.product.modules.service.CommonAutoName;
import org.springcenter.product.modules.service.CommonDataRuleService;
import org.springcenter.product.modules.service.IProductSampleCloService;
import org.springcenter.product.modules.util.EsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import strman.Strman;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/1/16 15:41
 */
@Service
@Slf4j
@RefreshScope
public class IProductSampleCloServiceImpl implements IProductSampleCloService {

    @Value("${sample.cloth.index}")
    private String sampleClothIndex;

    @Autowired
    private CommonDataRuleService commonDataRuleService;

    @Value("${brands.str}")
    private String brandsStr;

    @Value("${bands.str}")
    private String bandIdsStr;

    @Autowired
    private EsUtil esUtil;

    @Value("${sample.id.str}")
    private String sampleIdStr;

    @Value("${small.category.str}")
    private String smallCategoryStr;

    @Value("${sample.clo.fab.auto.name.info.index}")
    private String sampleCloFabAutoNameInfoIndex;

    @Autowired
    private SampleCloFabAutoNameInfoMapper sampleCloFabAutoNameInfoMapper;

    @Autowired
    private SampleCloFabInfoMapper sampleCloFabInfoMapper;

    @Autowired
    private CommonAutoName commonAutoName;

    @Value("${sample.fab.auto.name.info.tag}")
    private String sampleFabAutoNameInfoTag;

    @Autowired
    private AiAssistantStyleMapper aiAssistantStyleMapper;

    @Autowired
    private IAiAssistantService aiAssistantService;

    @Override
    public List<SampleProductSkcResp> searchSampleCloProductSkc(SampleProductReq context, Page page, String component) {
        // 获取数据权限
        List<String> brands= new ArrayList<>();
        if (StringUtils.isNotBlank(component)) {
            brands = commonDataRuleService.getPeopleBrandDataRule(component);
        }
        if (CollectionUtils.isNotEmpty(brands)) {
            if (CollectionUtils.isEmpty(context.getBrandIds())) {
                context.setBrandIds(brands);
            } else {
                List<String> filter = context.getBrandIds().stream().filter(brands::contains).collect(Collectors.toList());
                context.setBrandIds(filter);
            }
        }

        SearchRequest request = new SearchRequest();
        log.info("==============searchSampleCloProductSkc, 过滤的品牌信息:{}", JSONObject.toJSONString(context.getBrandIds()));
        request.indices(sampleClothIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(context.getName())){
            queryBuilder.must(QueryBuilders.termQuery("sample_code", context.getName()));
        }

        if (CollectionUtils.isNotEmpty(context.getNameLists())) {
            queryBuilder.must(QueryBuilders.termsQuery("sample_code", context.getNameLists()));
        }

        if (CollectionUtils.isNotEmpty(context.getBrandIds())){
            // 转换品牌
            List<BrandConvertEntity> brandConvertEntities = JSONObject.parseArray(brandsStr, BrandConvertEntity.class);
            List<String> brandsName = new ArrayList<>();
            context.getBrandIds().forEach(v -> brandConvertEntities.stream().filter(x -> Objects.equals(x.getCode(), v))
                    .findFirst().ifPresent(entity -> brandsName.add(entity.getName())));
            queryBuilder.must(QueryBuilders.termsQuery("d_pp", brandsName));
        }

        if (CollectionUtils.isNotEmpty(context.getYears())){
            queryBuilder.must(QueryBuilders.termsQuery("year", context.getYears()));
        }

        if (CollectionUtils.isNotEmpty(context.getSeasonIds())){
            List<String> sampleNames = getNames(context.getSeasonIds(), sampleIdStr);
            if (CollectionUtils.isNotEmpty(sampleNames)) {
                queryBuilder.must(QueryBuilders.termsQuery("sample_id", sampleNames));
            }
        }

        if (CollectionUtils.isNotEmpty(context.getBandIds())){
            // 转换波段
            List<AttrResp> attrEntities = JSONObject.parseArray(bandIdsStr, AttrResp.class);
            if (context.getBandIds().size() == 1 && Objects.equals(context.getBandIds().get(0), 2000L)) {
                queryBuilder.mustNot(QueryBuilders.termsQuery("season",
                        attrEntities.stream().map(AttrResp::getAttribname).collect(Collectors.toList())));
            } else if (context.getBandIds().contains(2000L)) {
                BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                BoolQueryBuilder subBool1 = new BoolQueryBuilder();
                List<String> bandNames = new ArrayList<>();
                context.getBandIds().forEach(v -> attrEntities.stream().filter(x -> Objects.equals(v, x.getId())).findFirst().ifPresent(attrResp -> bandNames.add(attrResp.getAttribname())));
                subBool1.must(QueryBuilders.termsQuery("season", bandNames));

                BoolQueryBuilder subBool = new BoolQueryBuilder();
                subBool.mustNot(QueryBuilders.termsQuery("season",
                        attrEntities.stream().map(AttrResp::getAttribname).collect(Collectors.toList())));
                boolQueryBuilder.should(subBool1);
                boolQueryBuilder.should(subBool);
                queryBuilder.must(boolQueryBuilder);
            } else {
                List<String> bandNames = new ArrayList<>();
                context.getBandIds().forEach(v -> attrEntities.stream().filter(x -> Objects.equals(v, x.getId())).findFirst().ifPresent(attrResp -> bandNames.add(attrResp.getAttribname())));
                queryBuilder.must(QueryBuilders.termsQuery("season", bandNames));
            }
        }

        if (CollectionUtils.isNotEmpty(context.getSmallCategoryIds())){
            List<String> smallCategoryIds = getNames(context.getSmallCategoryIds(), smallCategoryStr);
            if (CollectionUtils.isNotEmpty(smallCategoryIds)) {
                queryBuilder.must(QueryBuilders.termsQuery("ccchr4", smallCategoryIds));
            }
        }


        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1)*page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        sourceBuilder.sort("id", SortOrder.ASC);
        request.source(sourceBuilder);

        List<SampleProductSkcResp> entities = new ArrayList<>();
        try {
            log.info("SAMPLE_PRODUCT_SKC_INDEX 索引入参sourceBuilder：{}", sourceBuilder);
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                SampleProductSkcEsResp entity = SampleProductSkcEsResp.fromJson(hit.getSourceAsString(), SampleProductSkcEsResp.class);
                if (Objects.nonNull(entity.getLabels())) {
                    entity.setLabels(entity.getLabels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                    entity.setLabel_levels(entity.getLabel_levels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                }
                entities.add(SampleProductSkcResp.build(entity));
            }
        } catch (IOException e) {
            log.error("查询商品SKC异常e = {}", e.getMessage());
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(entities)) {
            return new ArrayList<>();
        }
        // 查询样衣的自动品名和fab

        Map<String, SampleCloFabInfo> fabInfoMap = sampleCloFabInfoMapper.selectBySampleCodes(
                entities.stream().map(SampleProductSkcResp::getSample_code).collect(Collectors.toList()));

        Map<String, SampleCloFabAutoNameInfo> autoNameInfoMap = sampleCloFabAutoNameInfoMapper.selectBySampleCodes(
                entities.stream().map(SampleProductSkcResp::getSample_code).collect(Collectors.toList()));

        if (CollectionUtils.isNotEmpty(fabInfoMap)) {
            entities.forEach(v -> {
                if (fabInfoMap.get(v.getSample_code()) == null) {
                    return;
                }
                v.setFabInfo(fabInfoMap.get(v.getSample_code()).getFab());
            });
        }

        if (CollectionUtils.isNotEmpty(autoNameInfoMap)) {
            entities.forEach(v -> {
                SampleCloFabAutoNameInfo sampleCloFabAutoNameInfo = autoNameInfoMap.get(v.getSample_code());
                if (sampleCloFabAutoNameInfo == null) {
                    return;
                }
                v.setAutoName(sampleCloFabAutoNameInfo.getAutoNameWhole());
            });
        }

        return entities;
    }

    @Override
    public void sampleCloAutoName(SampleProductAutoNameReq requestData) {
        // 更新数据
        sampleCloFabAutoNameInfoMapper.updateByParam(requestData);

        // 查询数量
        Integer totalNum = searchTotalNumInEs(requestData);
        if (totalNum == 0) {
            return;
        }

        Integer maxNum = 0;
        Integer minNum = 0;
        if (CollectionUtils.isEmpty(requestData.getSampleCodes())) {
            maxNum = commonAutoName.searchMinAndMaxNumInEs(SortOrder.DESC, sampleCloFabAutoNameInfoIndex);
            minNum = commonAutoName.searchMinAndMaxNumInEs(SortOrder.ASC, sampleCloFabAutoNameInfoIndex);
            log.info("===============最小数：{}，最大数：{}", minNum, maxNum);
        }

        if (minNum == 0 && totalNum == 0) {
            return;
        }


        // 获取配置信息
        // 2、获取配置
        CommonAutoNameSettingEntity info = commonAutoName.getAutoNameSettingInfo();

        long pageTotal = 1;
        int pageSize = 5000;
        if(totalNum % pageSize == 0){
            pageTotal = totalNum / pageSize;
        }else{
            pageTotal = totalNum / pageSize + 1;
        }

        for (int i = 0; i < pageTotal; i++) {
            List<FabSpuForAutoNameEsResp> respList = new ArrayList<>();
            if (requestData != null) {
                Page page = new Page(1, 10000);
                respList = searchDataForAutoNameInEsForParam(i, pageSize, requestData);
            } else {
                respList = commonAutoName.searchDataForAutoNameInEsForNum(
                        CommonSearchByPageAndIdEntity.build(minNum, i, maxNum ,pageSize, sampleCloFabAutoNameInfoIndex));
            }

            if (CollectionUtils.isEmpty(respList)) {
                continue;
            }

            List<SampleCloFabAutoNameInfo> partBatchInsert = new ArrayList<>();
            respList.forEach(v -> {
                List<FabSpuForAutoNameResp> partList = new ArrayList<>();
                List<FabSpuForAutoNameResp> wholeList = new ArrayList<>();
                final Integer[] charLen = {0};
                AtomicReference<String> dpp = new AtomicReference<>("");
                AtomicReference<String> designName = new AtomicReference<>("");

                CommonAutoNameGenerateEntity generateEntity = new CommonAutoNameGenerateEntity();
                generateEntity.setDpp(dpp);
                generateEntity.setDesignName(designName);
                generateEntity.setCharLen(charLen);
                generateEntity.setPartList(partList);
                generateEntity.setWholeList(wholeList);
                generateEntity.setForbiddenList(info.getForbiddenList());
                generateEntity.setFabAnDynamicFieldSettings(info.getFabAnDynamicFieldSettings());
                generateEntity.setPartSortedList(info.getPartSortedList());
                generateEntity.setResp(v);

                commonAutoName.getAutoGenerate(generateEntity, "sample");

                // 组装数据插入
                String partAutoName = partList.stream().sorted(Comparator.comparing(FabSpuForAutoNameResp::getAssembleWholeOrder))
                        .map(FabSpuForAutoNameResp::getDataValue).collect(Collectors.joining("")).toString();
                String wholeAutoName = wholeList.stream().sorted(Comparator.comparing(FabSpuForAutoNameResp::getAssembleWholeOrder))
                        .map(FabSpuForAutoNameResp::getDataValue).collect(Collectors.joining("")).toString();

                SampleCloFabAutoNameInfo sampleCloFabAutoNameInfo = new SampleCloFabAutoNameInfo();
                sampleCloFabAutoNameInfo.setSampleCode(v.getSample_code());
                sampleCloFabAutoNameInfo.setBand(v.getCorres_season());
                sampleCloFabAutoNameInfo.setBrand(v.getD_pp());
                sampleCloFabAutoNameInfo.setYear(StringUtils.substring(v.getGood_season(), 0, 4));
                sampleCloFabAutoNameInfo.setAutoNameWhole(wholeAutoName);
                sampleCloFabAutoNameInfo.setAutoNameCalc(partAutoName);
                sampleCloFabAutoNameInfo.setId(IdLeaf.getId(sampleFabAutoNameInfoTag));
                sampleCloFabAutoNameInfo.setUpdateTime(new Date());
                sampleCloFabAutoNameInfo.setCreateTime(new Date());
                sampleCloFabAutoNameInfo.setGoodSeason(v.getGood_season());
                sampleCloFabAutoNameInfo.setColorNo(v.getSty_color_id());
                partBatchInsert.add(sampleCloFabAutoNameInfo);
            });

            // 操作插入语句
            if (CollectionUtils.isEmpty(partBatchInsert)) {
                return;
            }
            List<List<SampleCloFabAutoNameInfo>> partition = com.google.common.collect.Lists.partition(partBatchInsert, 1000);
            partition.forEach(v -> {
                sampleCloFabAutoNameInfoMapper.batchInsert(v);
            });
        }
    }



    @Override
    public void sampleCloFab(SampleProductAutoNameReq requestData) {
        // 更新数据
        sampleCloFabInfoMapper.updateByParam(requestData);

        // 查询数量
        Integer totalNum = searchTotalNumInEs(requestData);
        if (totalNum == 0) {
            return;
        }

        long pageTotal = 1;
        int pageSize = 5000;
        if(totalNum % pageSize == 0){
            pageTotal = totalNum / pageSize;
        }else{
            pageTotal = totalNum / pageSize + 1;
        }

        // 根据类型获取ai content、字段、title
        QueryWrapper query = new QueryWrapper();
        query.eq("IS_DELETED", 0);
        query.eq("TYPE", 3);
        List<AiAssistantStyle> list = aiAssistantStyleMapper.selectList(query);
        if (CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("该场景不存在！");
        }
        AiAssistantStyle style = list.get(0);


        log.info("========================计算总页数为：{}", pageTotal);
        for (int i = 0; i < pageTotal; i++) {
            log.info("========================当前执行的fab的页码为：{}", i);
            List<SampleCloFabInfo> sampleCloFabFabInfos = new ArrayList<>();
            List<FabSpuForAutoNameEsResp> respList = new ArrayList<>();
            respList = searchDataForAutoNameInEsForParam(i, pageSize, requestData);

            if (CollectionUtils.isEmpty(respList)) {
                continue;
            }

            // 处理AI逻辑
            respList.forEach(v -> {
                SampleCloFabInfo sampleCloFabFabInfo = null;
                try {
                    sampleCloFabFabInfo = dealSampleCloAiGenerate(style, v);
                } catch (Exception e) {
                    log.error("==================处理ai逻辑报错, 样衣号为：{}", v.getSample_code());
                    return;
                    // 不抛异常 throw new RuntimeException(e);
                }
                sampleCloFabFabInfos.add(sampleCloFabFabInfo);
            });

            if (CollectionUtils.isNotEmpty(sampleCloFabFabInfos)) {
                List<List<SampleCloFabInfo>> partition = com.google.common.collect.Lists.partition(sampleCloFabFabInfos, 1000);
                partition.forEach(v -> {
                    sampleCloFabInfoMapper.batchInsert(v);
                });
                log.info("========================当前执行的fab第{}插入完成", i);
            }
        }
    }

    private SampleCloFabInfo dealSampleCloAiGenerate(AiAssistantStyle style, FabSpuForAutoNameEsResp v) throws InterruptedException {
        HashMap<String, String> map = buildFabInfo(v);
        String introduction = style.getInputInfo();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String str = "${" + entry.getKey() + "}";
            introduction = introduction.replace(str, entry.getValue());
        }

        log.info("=============商品{}fab展示：{}", v.getSample_code(), introduction);
        // 调用ai助手
        AiAssistantReqEntity entity = JSONObject.parseObject(introduction, AiAssistantReqEntity.class);
        entity.setRequestSource(ChatGptEnum.SAMPLE_CLO_FAB.getEnglishDesc());
        int i = 0;
        String aiStr = callThreeTimes(entity, i);

        SampleCloFabInfo info = new SampleCloFabInfo();
        info.setFab(aiStr);
        info.setBrand(v.getD_pp());
        info.setBand(v.getCorres_season());
        info.setSampleCode(v.getSample_code());
        info.setYear(StringUtils.substring(v.getGood_season(), 0, 4));
        info.setId(IdLeaf.getId(sampleFabAutoNameInfoTag));
        info.setGoodSeason(v.getGood_season());
        info.setUpdateTime(new Date());
        info.setCreateTime(new Date());
        info.setColorNo(v.getSty_color_id());
        return info;
    }

    private String callThreeTimes(AiAssistantReqEntity entity, int i) throws InterruptedException {
        // 因为一分钟不能超过80000或者500 需要一分钟后才能访问 故设置睡眠时间
        Thread.sleep(55000);
        if (i >= 3) {
            return "";
        }
        log.info("==============计算次数，第{}次", i + 1);
        AiAssistantRespEntity resp = null;
        try {
            i = i + 1;
            resp = aiAssistantService.invokeAiAssistant(entity);
            if (!Objects.equals(resp.getCode(), "200")) {
                callThreeTimes(entity, i);
            }
        } catch (Exception e) {
            callThreeTimes(entity, i);
        }
        return resp != null ? resp.getResult() : "";
    }

    private HashMap<String, String> buildFabInfo(FabSpuForAutoNameEsResp v) {
        HashMap<String, String> map = new HashMap<>();
        // 面料特性、原料产地、辅料卖点、产品细分、面料推广资料
        List<String> productElementList = new ArrayList<>();
        productElementList.add(v.getSc_quality());
        productElementList.add(v.getSc_environmental());
        productElementList.add(v.getAcc_point());
        productElementList.add(v.getScxfl());
        productElementList.add(v.getSc_popularize());
        String productElement = productElementList.stream()
                .filter(x -> StringUtils.isNotBlank(x) && !Objects.equals(x, "null"))
                .collect(Collectors.joining(","));

        // 细分类,企划品名,产品大类,特殊工艺明细,特殊工艺小类,体型,合体度,领型,袖型,图案大类,图案名称
        List<String> technologyList = new ArrayList<>();
        technologyList.add(v.getCcchr5());
        technologyList.add(v.getDesign_name());
        technologyList.add(v.getScdl());
        technologyList.add(v.getTymx());
        technologyList.add(v.getGyxj());
        technologyList.add(v.getTixing());
        technologyList.add(v.getWeidu());
        technologyList.add(v.getLingxing());
        technologyList.add(v.getXiu());
        technologyList.add(v.getPattern_type());
        technologyList.add(v.getPattern_name());
        String technology = technologyList.stream()
                .filter(x -> StringUtils.isNotBlank(x) && !Objects.equals(x, "null"))
                .collect(Collectors.joining(","));

        map.put("fabric", productElement);
        map.put("technology", technology);
        return map;
    }

    private List<FabSpuForAutoNameEsResp> searchDataForAutoNameInEsForParam(int i, int pageSize, SampleProductAutoNameReq req) {
        SearchRequest request = new SearchRequest();
        request.indices(sampleCloFabAutoNameInfoIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        sourceBuilder.from(i);
        sourceBuilder.size(pageSize);
        buildQuery(queryBuilder, req);
        sourceBuilder.sort("id", SortOrder.ASC);
        sourceBuilder.query(queryBuilder);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品SAMPLE_CLO_FAB_FOR_AUTO_NAME:{}", request.source().toString());
        List<FabSpuForAutoNameEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                FabSpuForAutoNameEsResp entity = FabSpuForAutoNameEsResp.fromJson(hit.getSourceAsString(), FabSpuForAutoNameEsResp.class);
                entity.setId(Integer.valueOf(hit.getId()));
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品SAMPLE_CLO_FAB_FOR_AUTO_NAME异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }


    private Integer searchTotalNumInEs(SampleProductAutoNameReq requestData) {
        SearchRequest request = new SearchRequest();
        request.indices(sampleCloFabAutoNameInfoIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        if (requestData == null) {
            sourceBuilder.trackTotalHits(true);
        } else {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            buildQuery(queryBuilder, requestData);
            sourceBuilder.query(queryBuilder);
            sourceBuilder.trackTotalHits(true);
        }


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品SAMPLE_CLO_FAB_FOR_AUTO_NAME_TOTAL {}", request.source().toString());
        int entities = 0;
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return 0;
            }

            SearchHits hits = response.getHits();
            entities = Math.toIntExact(hits.getTotalHits().value);

        } catch (IOException e) {
            log.error("查询商品SAMPLE_CLO_FAB_FOR_AUTO_NAME_TOTAL异常e = {}", e.getMessage());
            return 0;
        }
        return entities;
    }

    private void buildQuery(BoolQueryBuilder queryBuilder, SampleProductAutoNameReq requestData) {
        if (StringUtils.isNotBlank(requestData.getBandStr())) {
            // 样衣该字段洗的是波段
            queryBuilder.must(QueryBuilders.termsQuery("corres_season.keyword", requestData.getBandStr()));
        }

        if (StringUtils.isNotBlank(requestData.getYear())) {
            queryBuilder.must(QueryBuilders.matchPhrasePrefixQuery("good_season", requestData.getYear()));
        }

        if (CollectionUtils.isNotEmpty(requestData.getSampleCodes())) {
            queryBuilder.must(QueryBuilders.termsQuery("sample_code.keyword", requestData.getSampleCodes()));
        }

        if (CollectionUtils.isNotEmpty(requestData.getBrands())) {
            queryBuilder.must(QueryBuilders.termsQuery("d_pp.keyword", requestData.getBrands()));
        }

        if (StringUtils.isNotBlank(requestData.getGoodSeason())) {
            queryBuilder.must(QueryBuilders.termsQuery("good_season.keyword", requestData.getGoodSeason()));
        }
    }

    private List<String> getNames(List<String> ids, String str) {
        List<AttrResp> attrEntities = JSONObject.parseArray(str, AttrResp.class);
        return attrEntities.stream().filter(v -> ids.contains(v.getAttribcode()))
                .map(AttrResp::getAttribname).collect(Collectors.toList());
    }
}
