package org.springcenter.product.modules.remote;

import org.springcenter.product.modules.remote.entity.GetListByWeimoGoodIdsReq;
import org.springcenter.product.modules.remote.entity.GetListByWeimoGoodIdsResp;
import org.springcenter.product.modules.remote.entity.WeimoGoodShareLinkReq;
import org.springcenter.product.modules.remote.entity.WeimoGoodShareLinkResp;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/9/20 9:49
 */
public interface IWeiMoRemoteApi {


    @POST("/wxmall/wxmall-center/weimo/v2/product/getListById")
    Call<JicBaseResp<List<GetListByWeimoGoodIdsResp>>> weimoGoodIdsInfo(@Body GetListByWeimoGoodIdsReq getListByWeimoGoodIdsReq);

    @POST("/wxmall/wxmall-center/weimo/goods/url/getList")
    Call<JicBaseResp<List<WeimoGoodShareLinkResp>>> weimoGoodShareLink(@Body WeimoGoodShareLinkReq weimoGoodShareLinkReq);
}
