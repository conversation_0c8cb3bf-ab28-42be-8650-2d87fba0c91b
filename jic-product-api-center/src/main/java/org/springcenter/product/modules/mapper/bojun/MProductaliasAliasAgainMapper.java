package org.springcenter.product.modules.mapper.bojun;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.api.dto.ZhuiDanSkuInfoResp;
import org.springcenter.product.modules.model.bojun.MProductaliasAliasAgain;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/1/10 9:05
 */
public interface MProductaliasAliasAgainMapper extends BaseMapper<MProductaliasAliasAgain> {

    List<ZhuiDanSkuInfoResp> getSkuInfoByZdSkuId(@Param("list") List<Long> requestData);

    Long selectOrigProductIdByZhuiDanAliasId(@Param("zhuiDanId") Long zhuiDanId);

}

