package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.springcenter.product.modules.model.ProductToppingTag;
import org.springcenter.product.modules.service.IProductToppingEsScriptService;
import org.springcenter.product.modules.util.EsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date:2023/6/7 16:48
 */
@Service
@Slf4j
@EnableAsync
@RefreshScope
public class ProductToppingEsScriptServiceImpl implements IProductToppingEsScriptService {

    @Value("${es.index.store.goods.skc}")
    private String storeProductSkcIndex;

    @Autowired
    private EsUtil esUtil;

    @Async
    public void updateIndexInfoByRemoveEle(ProductToppingTag tag) {
        log.info("=========异步操作");
        UpdateByQueryRequest request = new UpdateByQueryRequest(storeProductSkcIndex);
        request.setQuery(new TermQueryBuilder("name", tag.getSpu()));
        Map<String, Object> params = new HashMap<>();
        Map<String, Object> toppingTag = new HashMap<>();
        toppingTag.put("topping_tag", tag.getType());
        toppingTag.put("channel", tag.getChannel());
        params.put("new_tag", toppingTag);
        Script script = new Script("if (ctx._source.topping_tag_list != null) ctx._source.topping_tag_list.removeIf(it -> it.channel == 'BOX')");
        request.setScript(script);
        try {
            esUtil.updateByQuery(request);
        } catch (IOException e) {
            log.error("{}款号更新有问题", JSONObject.toJSONString(tag));
            throw new RuntimeException(e);
        }
    }

    // 批量更新新增索引
    @Async
    public void batchUpdateIndexInfoByAdd(List<ProductToppingTag> productToppingTags) throws IOException {
        log.info("=========异步操作");
        StopWatch stopWatch3 = new StopWatch();
        stopWatch3.start("异步操作");
        if (CollectionUtils.isNotEmpty(productToppingTags)) {
            productToppingTags.forEach(v -> {
                // 查询es 是否已经存在box标签
                UpdateByQueryRequest request = new UpdateByQueryRequest(storeProductSkcIndex);
                request.setQuery(new TermQueryBuilder("name", v.getSpu()));
                Map<String, Object> params = new HashMap<>();
                Map<String, Object> toppingTag = new HashMap<>();
                toppingTag.put("topping_tag", v.getType());
                toppingTag.put("channel", v.getChannel());
                params.put("new_tag", toppingTag);
                Script script = new Script(ScriptType.INLINE, "painless",
                        "if (ctx._source.topping_tag_list == null) {ctx._source.topping_tag_list = []} " +
                                "ctx._source.topping_tag_list.removeIf(it -> it.channel == 'BOX');" +
                                "ctx._source.topping_tag_list.add(params.new_tag)", params);
                request.setScript(script);
                try {
                    esUtil.updateByQuery(request);
                } catch (IOException e) {
                    log.error("{}款号更新有问题", v);
                    throw new RuntimeException(e);
                }
            });
        }
        stopWatch3.stop();
        log.info("============" + stopWatch3.prettyPrint());
    }
}
