package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.common.system.vo.SysCategoryModel;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.modules.mapper.product.*;
import org.springcenter.product.modules.model.*;
import org.springcenter.product.modules.service.IProductLabelRuleService;
import org.springcenter.product.modules.util.EsUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import strman.Strman;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/10/11 16:09
 */
@Service
@Slf4j
@RefreshScope
public class ProductLabelRuleServiceImpl implements IProductLabelRuleService {

    @Autowired
    private LabelRuleDetailMapper labelRuleDetailMapper;

    @Autowired
    private LabelMainRuleMapper labelMainRuleMapper;

    @Value("${label.rule.tag.id}")
    private String labelRuleTagId;


    @Value("${label.product.index}")
    private String labelProductIndex;

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Resource
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    private LabelSettingInfoMapper labelSettingInfoMapper;


    @Autowired
    private LabelSettingInfoValueMapper labelSettingInfoValueMapper;

    @Autowired
    private SysProductLabelMapper sysProductLabelMapper;

    @Autowired
    private EsUtil esUtil;

    @Autowired
    private SysProductCalcLabelMapper sysProductCalcLabelMapper;

    @Autowired
    private CalcLabelOutsideNameMapper calcLabelOutsideNameMapper;

    @Value("${label.sort}")
    private String filterLabelSort;


    @Override
    public Boolean addLabelRule(AddLabelRuleReq requestData) {
        // 判断当前排序是否存在
        String[] split = requestData.getPcode().split("-");
        String parentLabel = split[0] + "-" + split[1] + "-" + split[2];
        /*QueryWrapper<LabelMainRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("SORT", requestData.getSort());
        queryWrapper.eq("PARENT_LABEL", parentLabel);
        List<LabelMainRule> mainRuleList1 = labelMainRuleMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(mainRuleList1)) {
            throw new RuntimeException("当前排序已重复，请重新输入");
        }*/

        // 1、校验该层是否可以增加规则 只有叶子节点可以增加规则
        // 当前标签是否存在 && 当前标签无子级
        List<SysCategoryModel> sysCategoryModels = sysBaseAPI.queryDSysCateByCodes(Lists.newArrayList(requestData.getPcode()));
        if (CollectionUtils.isEmpty(sysCategoryModels)) {
            throw new RuntimeException("该标签不存在");
        }
        List<SysCategoryModel> sysCategoryModels2 = sysBaseAPI.queryAllDSysCategoryByCode(requestData.getPcode());
        if (CollectionUtils.isNotEmpty(sysCategoryModels2)) {
            throw new RuntimeException("当前标签下还有标签值，计算规则只能在叶子结点生效");
        }


        // 2、保存规则
        String pid = requestData.getPid();
        String pName = requestData.getPname();
        String pCode = requestData.getPcode();
        if (CollectionUtils.isEmpty(requestData.getMainRules())) {
            throw new RuntimeException("设置的主条件不能为空");
        }

        List<LabelMainRule> mainRuleList = new ArrayList<>();
        List<LabelRuleDetail> labelRuleDetailList = new ArrayList<>();
        AtomicReference<Integer> mainCalcRelation = new AtomicReference<>();
        requestData.getMainRules().forEach(mainRule -> {
            if (CollectionUtils.isEmpty(mainRule.getRuleDetails())) {
                throw new RuntimeException("设置的条件不能为空");
            }

            // 检验符号
            if (!Objects.equals(mainCalcRelation.get(), 0) && !Objects.equals(mainCalcRelation.get(), 1) ) {
                mainCalcRelation.set(mainRule.getCalcRelation());
            } else {
                if (!Objects.equals(mainCalcRelation.get(), mainRule.getCalcRelation())) {
                    throw new RuntimeException("主条件的运算符号不一致");
                }
            }


            // 1、保存主条件
            LabelMainRule labelMainRule = new LabelMainRule();
            labelMainRule.setLabelId(pid);
            labelMainRule.setLabelCode(pCode);
            labelMainRule.setLabelName(pName);
            labelMainRule.setCalcRelation(mainRule.getCalcRelation());
            labelMainRule.setSort(requestData.getSort());
            labelMainRule.setCreateTime(new Date());
            labelMainRule.setUpdateTime(new Date());
            labelMainRule.setOperator(requestData.getOperator());
            labelMainRule.setId(IdLeaf.getId(labelRuleTagId));




            // 2、保存子条件
            AtomicReference<Integer> subCalcRelation = new AtomicReference<>();
            mainRule.getRuleDetails().forEach(v -> {
                // 检验符号
                if (!Objects.equals(subCalcRelation.get(), 0) && !Objects.equals(subCalcRelation.get(), 1)) {
                    subCalcRelation.set(v.getCalcRelation());
                } else {
                    if (!Objects.equals(subCalcRelation.get(), v.getCalcRelation())) {
                        throw new RuntimeException("主条件的运算符号不一致");
                    }
                }

                LabelRuleDetail labelRuleDetail = new LabelRuleDetail();
                labelRuleDetail.setLabelNameCode(v.getLabelNameCode());
                labelRuleDetail.setLabelName(v.getLabelName());
                labelRuleDetail.setLabelNameType(v.getLabelNameType());
                labelRuleDetail.setLabelValueCode(v.getLabelValueCode());
                labelRuleDetail.setCreateTime(new Date());
                labelRuleDetail.setUpdateTime(new Date());
                labelRuleDetail.setId(IdLeaf.getId(labelRuleTagId));
                labelRuleDetail.setLabelValue(v.getLabelValue());
                labelRuleDetail.setRuleMainId(labelMainRule.getId());
                labelRuleDetail.setCalcRelation(v.getCalcRelation());
                labelRuleDetail.setNameValueRelation(v.getNameValueRelation());
                labelRuleDetailList.add(labelRuleDetail);
            });

            labelMainRule.setParentLabel(parentLabel);
            mainRuleList.add(labelMainRule);
        });

        template.execute(x -> {
            if (CollectionUtils.isNotEmpty(mainRuleList)) {
                labelMainRuleMapper.insertBatch(mainRuleList);
            }

            if (CollectionUtils.isNotEmpty(labelRuleDetailList)) {
                labelRuleDetailMapper.insertBatch(labelRuleDetailList);
            }
            return true;
        });

        return true;
    }



    @Override
    public SearchLabelRuleResp searchLabelRule(SearchLabelRuleReq requestData) {
        // 1、根据标签id查询主条件
        QueryWrapper<LabelMainRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("LABEL_ID", requestData.getLabelId());
        List<LabelMainRule> labelMainRules = labelMainRuleMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(labelMainRules)) {
            return null;
        }

        SearchLabelRuleResp searchLabelRuleResp = new SearchLabelRuleResp();
        List<SearchLabelRuleResp.MainRuleData> mainRuleDataList = new ArrayList<>();
        labelMainRules.forEach(v -> {
            QueryWrapper<LabelRuleDetail> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
            queryWrapper1.eq("RULE_MAIN_ID", v.getId());
            List<LabelRuleDetail> labelRuleDetailList = labelRuleDetailMapper.selectList(queryWrapper1);
            if (CollectionUtils.isEmpty(labelRuleDetailList)) {
                return;
            }

            // 组装参数
            SearchLabelRuleResp.MainRuleData mainData = buildSearchMainRuleData(v, labelRuleDetailList);
            mainRuleDataList.add(mainData);

        });

        searchLabelRuleResp.setPid(labelMainRules.get(0).getLabelId());
        searchLabelRuleResp.setPCode(labelMainRules.get(0).getLabelCode());
        searchLabelRuleResp.setPName(labelMainRules.get(0).getLabelName());
        searchLabelRuleResp.setSort(labelMainRules.get(0).getSort());
        searchLabelRuleResp.setMainRules(mainRuleDataList);
        return searchLabelRuleResp;
    }

    @Override
    public Boolean updateLabelRule(UpdateLabelRuleReq requestData) {
        // 判断当前排序是否存在
        String[] split = requestData.getPcode().split("-");
        String parentLabel = split[0] + "-" + split[1] + "-" + split[2];
        /*QueryWrapper<LabelMainRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("SORT", requestData.getSort());
        queryWrapper.ne("LABEL_CODE", requestData.getPcode());
        queryWrapper.eq("PARENT_LABEL", parentLabel);
        List<LabelMainRule> mainRuleList1 = labelMainRuleMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(mainRuleList1)) {
            throw new RuntimeException("当前排序已重复，请重新输入");
        }*/


        // 1、组装数据
        if (CollectionUtils.isEmpty(requestData.getMainRules())) {
            throw new RuntimeException("设置的主条件不能为空");
        }


        List<LabelMainRule> insertList = new ArrayList<>();
        List<LabelMainRule> updateList = new ArrayList<>();
        List<LabelRuleDetail> insertDetailList = new ArrayList<>();
        List<LabelRuleDetail> updateDetailList = new ArrayList<>();
        AtomicReference<Integer> mainCalcRelation = new AtomicReference<>();
        requestData.getMainRules().forEach(v -> {
            if (CollectionUtils.isEmpty(v.getRuleDetails())) {
                throw new RuntimeException("设置的条件不能为空");
            }
            // 1、保存主条件
            // 检验符号
            if (!Objects.equals(mainCalcRelation.get(), 0) && !Objects.equals(mainCalcRelation.get(), 1)) {
                mainCalcRelation.set(v.getCalcRelation());
            } else {
                if (!Objects.equals(mainCalcRelation.get(), v.getCalcRelation())) {
                    throw new RuntimeException("主条件的运算符号不一致");
                }
            }
            LabelMainRule labelMainRule = new LabelMainRule();
            labelMainRule.setLabelId(requestData.getPid());
            labelMainRule.setLabelCode(requestData.getPcode());
            labelMainRule.setLabelName(requestData.getPname());
            labelMainRule.setCalcRelation(v.getCalcRelation());
            labelMainRule.setSort(requestData.getSort());
            labelMainRule.setUpdateTime(new Date());
            labelMainRule.setIsDeleted(v.getIsDeleted());
            labelMainRule.setOperator(requestData.getOperator());
            labelMainRule.setId(v.getId());

            if (v.getId() == null) {
                labelMainRule.setCreateTime(new Date());
                labelMainRule.setId(IdLeaf.getId(labelRuleTagId));
                if (Objects.equals(v.getIsDeleted(), IsDeleteEnum.NORMAL.getCode())) {
                    labelMainRule.setParentLabel(parentLabel);
                }
                insertList.add(labelMainRule);
            } else {
                if (Objects.equals(v.getIsDeleted(), IsDeleteEnum.NORMAL.getCode())) {
                    labelMainRule.setParentLabel(parentLabel);
                }
                updateList.add(labelMainRule);
            }


            // 2、保存子条件
            AtomicReference<Integer> subCalcRelation = new AtomicReference<>();
            v.getRuleDetails().forEach(v1 -> {
                // 检验符号
                if (!Objects.equals(subCalcRelation.get(), 0) && !Objects.equals(subCalcRelation.get(), 1)) {
                    subCalcRelation.set(v1.getCalcRelation());
                } else {
                    if (!Objects.equals(subCalcRelation.get(), v1.getCalcRelation())) {
                        throw new RuntimeException("主条件的运算符号不一致");
                    }
                }
                // 组装数据
                LabelRuleDetail labelRuleDetail = new LabelRuleDetail();
                labelRuleDetail.setLabelNameCode(v1.getLabelNameCode());
                labelRuleDetail.setLabelName(v1.getLabelName());
                labelRuleDetail.setLabelNameType(v1.getLabelNameType());
                labelRuleDetail.setLabelValueCode(v1.getLabelValueCode());
                labelRuleDetail.setUpdateTime(new Date());
                labelRuleDetail.setId(v1.getId());
                labelRuleDetail.setLabelValue(v1.getLabelValue());
                labelRuleDetail.setRuleMainId(labelMainRule.getId());
                labelRuleDetail.setCalcRelation(v1.getCalcRelation());
                labelRuleDetail.setNameValueRelation(v1.getNameValueRelation());
                labelRuleDetail.setIsDeleted(v1.getIsDeleted());
                if (v1.getId() == null) {
                    labelRuleDetail.setCreateTime(new Date());
                    labelRuleDetail.setId(IdLeaf.getId(labelRuleTagId));
                    labelRuleDetail.setRuleMainId(labelMainRule.getId());
                    insertDetailList.add(labelRuleDetail);
                } else {
                    if (Objects.equals(v.getIsDeleted(), IsDeleteEnum.IS_DELETED.getCode())) {
                        labelRuleDetail.setIsDeleted(IsDeleteEnum.IS_DELETED.getCode());
                    }
                    updateDetailList.add(labelRuleDetail);
                }

            });


        });


        // 2、更新数据
        template.execute(a -> {
            if (CollectionUtils.isNotEmpty(insertList)) {
                labelMainRuleMapper.insertBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                labelMainRuleMapper.batchUpdate(updateList);
            }
            if (CollectionUtils.isNotEmpty(insertDetailList)) {
                labelRuleDetailMapper.insertBatch(insertDetailList);
            }
            if (CollectionUtils.isNotEmpty(updateDetailList)) {
                labelRuleDetailMapper.batchUpdate(updateDetailList);
            }
            return true;
        });
        return true;
    }

    @Override
    public List<QueryLabelRuleSettingResp> queryLabelRuleSetting(QueryLabelRuleSettingReq requestData) {
        List<QueryLabelRuleSettingResp> rets = new ArrayList<>();
        // 如果入参为空，则查询所有名称
        if (requestData == null || (StringUtils.isBlank(requestData.getId()) && requestData.getType() == null
        && StringUtils.isBlank(requestData.getSettingCode()))) {
            QueryWrapper<LabelSettingInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
            if (StringUtils.isNotBlank(requestData.getQueryParam())) {
                queryWrapper.like("LABEL_SETTING_NAME", requestData.getQueryParam());
            }
            List<LabelSettingInfo> infos = labelSettingInfoMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(infos)) {
                return Collections.emptyList();
            }
            infos.forEach(v -> {
                QueryLabelRuleSettingResp resp = new QueryLabelRuleSettingResp();
                resp.setLabelSettingName(v.getLabelSettingName());
                resp.setLabelSettingField(v.getLabelSettingField());
                resp.setLabelSettingType(v.getLabelSettingType());
                resp.setLabelSettingNameCode(v.getLabelSettingNameCode());
                resp.setId(v.getId());
                rets.add(resp);
            });
            return rets;
        }

        // 如果入参不为空，则根据入参查询
        // 不为空，查询值
        if (Objects.equals(requestData.getType(), 0)) {
            if (StringUtils.isBlank(requestData.getSettingCode())) {
                throw new RuntimeException("标签code不能为空");
            }
            // 获取标签
            List<SysCategoryModel> models = sysBaseAPI.queryAllDSysCategoryByCode(requestData.getSettingCode());
            if (CollectionUtils.isEmpty(models)) {
                return Collections.emptyList();
            }
            AtomicInteger code = new AtomicInteger(1);
            models.forEach(v -> {
                if (StringUtils.isNotBlank(requestData.getQueryParam())) {
                    if (v.getName().contains(requestData.getQueryParam())) {
                        QueryLabelRuleSettingResp resp = new QueryLabelRuleSettingResp();
                        resp.setLabelSettingName(v.getName());
                        resp.setLabelSettingNameCode(v.getCode());
                        resp.setId(Objects.toString(code.getAndIncrement()));
                        rets.add(resp);
                    }
                } else {
                    QueryLabelRuleSettingResp resp = new QueryLabelRuleSettingResp();
                    resp.setLabelSettingName(v.getName());
                    resp.setLabelSettingNameCode(v.getCode());
                    resp.setId(Objects.toString(code.getAndIncrement()));
                    rets.add(resp);
                }

            });
        } else {
            if (StringUtils.isBlank(requestData.getId())) {
                throw new RuntimeException("id不能为空");
            }
            // 获取属性
            QueryWrapper<LabelSettingInfoValue> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
            queryWrapper.eq("LABEL_SETTING_INFO_ID", requestData.getId());
            if (StringUtils.isNotBlank(requestData.getQueryParam())) {
                queryWrapper.like("LABEL_SETTING_VALUE", requestData.getQueryParam());
            }
            List<LabelSettingInfoValue> values = labelSettingInfoValueMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(values)) {
                return Collections.emptyList();
            }

            values.forEach(v -> {
                QueryLabelRuleSettingResp resp = new QueryLabelRuleSettingResp();
                resp.setLabelSettingName(v.getLabelSettingValue());
                resp.setId(Objects.toString(v.getId()));
                rets.add(resp);
            });
        }

        return rets;
    }

    @Override
    public void dealLabelRuleToCreate() {
        // 逻辑删除所有数据
        sysProductCalcLabelMapper.updateAll();

        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        //  获取总数
        List<String> list = sysProductLabelMapper.selectByDistinctSkcCode();
        PageInfo<ProductToppingTag> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();

        long pageTotal = 0;
        int pageSize = 200;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }


        // 查询所有规则条件
        List<RuleResp> dealRuleList = getRuleResps();
        if (CollectionUtils.isEmpty(dealRuleList)) return;


        // 保存数据
        for(int i = 1 ; i <= pageTotal; i++){
            List<SysProductCalcLabel> inserts = new ArrayList<>();
            com.github.pagehelper.Page<Object> page = PageHelper.startPage(i, 200);
            // 查询skc
            List<String> skcCodes = sysProductLabelMapper.selectByDistinctSkcCode();
            if (CollectionUtils.isEmpty(skcCodes)) {
                return;
            }

            // 查询es信息
            dealProductCalcLabel(dealRuleList, inserts, skcCodes);

            if (CollectionUtils.isNotEmpty(inserts)) {
                List<List<SysProductCalcLabel>> partition = com.google.common.collect.Lists.partition(inserts, 1800);
                partition.forEach(v -> {
                    sysProductCalcLabelMapper.insertBatch(v);
                });
            }
        }



    }

    private List<RuleResp> getRuleResps() {
        QueryWrapper<LabelMainRule> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        List<LabelMainRule> mainRules = labelMainRuleMapper.selectList(queryWrapper1);
        if (CollectionUtils.isEmpty(mainRules)) {
            return null;
        }

        QueryWrapper<LabelRuleDetail> ruleQueryWrapper = new QueryWrapper<>();
        ruleQueryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        List<LabelRuleDetail> labelRuleDetails = labelRuleDetailMapper.selectList(ruleQueryWrapper);
        if (CollectionUtils.isEmpty(labelRuleDetails)) {
            return null;
        }


        List<RuleResp> dealRuleList = new ArrayList<>();
        mainRules.forEach(map ->{
            RuleResp ruleResp = new RuleResp();
            ruleResp.setId(map.getId());
            ruleResp.setLabelCode(map.getLabelCode());
            ruleResp.setLabelName(map.getLabelName());
            ruleResp.setLabelId(map.getLabelId());
            ruleResp.setSort(map.getSort());
            ruleResp.setParentLabel(map.getParentLabel());
            ruleResp.setCalcRelation(map.getCalcRelation());
            List<LabelRuleDetail> ruleDetails = labelRuleDetails.stream()
                    .filter(da -> Objects.equals(da.getRuleMainId(), map.getId()))
                            .collect(Collectors.toList());
            ruleResp.setLabelRuleDetails(ruleDetails);
            dealRuleList.add(ruleResp);
        });
        return dealRuleList;
    }

    private void dealProductCalcLabel(List<RuleResp> dealRuleList, List<SysProductCalcLabel> inserts, List<String> skcCodes) {
        // 根据skcCode批量查询索引数据
        List<LabelProductInfoEsResp> infoEss = getBatchLabelProductInfo(skcCodes);
        if (CollectionUtils.isEmpty(infoEss)) {
            return;
        }
        Map infoMap = infoEss.stream().collect(HashMap::new, (k, v) -> k.put(v.getSkc(), v), HashMap::putAll);
        skcCodes.forEach(v -> {
            if (CollectionUtils.isEmpty(infoMap) || infoMap.get(v) == null) {
                return;
            }
            LabelProductInfoEsResp info = (LabelProductInfoEsResp) infoMap.get(v);

            // 处理规则 1、先处理主条件
            // 符合条件的假如
            List<RuleDataRelation> mainRuleList = new ArrayList<>();
            dealRuleList.forEach(x -> {
                if (CollectionUtils.isEmpty(x.getLabelRuleDetails())) {
                    return;
                }

                // 处理逻辑
                List<Boolean> detailMatchList = new ArrayList<>();
                AtomicReference<Integer> detailCalc = new AtomicReference<>();
                x.getLabelRuleDetails().forEach(u -> {
                    detailCalc.set(u.getCalcRelation());
                    if (Objects.equals(u.getLabelNameType(), 0)) {
                        // 标签
                        String code = Objects.equals(u.getLabelNameType(), 0) ? u.getLabelValueCode() : u.getLabelValue();
                        if (Objects.equals(u.getNameValueRelation(), 0)) {
                            // in
                            if (isJudge(info.getCode(), Arrays.stream(code.split(";")).collect(Collectors.toList()))) {
                                // 符合条件
                                detailMatchList.add(true);
                            } else {
                                detailMatchList.add(false);
                            }
                        } else {
                            // not in
                            if (isJudge(info.getCode(), Arrays.stream(code.split(";")).collect(Collectors.toList()))) {
                                // 符合条件
                                detailMatchList.add(false);
                            } else {
                                detailMatchList.add(true);
                            }
                        }
                    } else {
                        // 属性
                        // 获取字段名称
                        QueryWrapper<LabelSettingInfo> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
                        queryWrapper.eq("LABEL_SETTING_NAME", u.getLabelName());
                        List<LabelSettingInfo> infos = labelSettingInfoMapper.selectList(queryWrapper);
                        if (CollectionUtils.isEmpty(infos)) {
                            throw new RuntimeException("标签名称不存在" + u.getLabelName());
                        }
                        if (infos.get(0) == null) {
                            return;
                        }
                        if (Objects.equals(u.getNameValueRelation(), 0)) {
                            // in
                            if (Arrays.stream(u.getLabelValue().split(";")).collect(Collectors.toList()).contains(getFieldValue(infos.get(0).getLabelSettingField(), info))) {
                                // 符合条件
                                detailMatchList.add(true);
                            } else {
                                detailMatchList.add(false);
                            }
                        } else {
                            // not in
                            if (Arrays.stream(u.getLabelValue().split(";")).collect(Collectors.toList()).contains(getFieldValue(infos.get(0).getLabelSettingField(), info))) {
                                // 符合条件
                                detailMatchList.add(false);
                            } else {
                                detailMatchList.add(true);
                            }
                        }
                    }


                });

                // 判断当前这个主条件的子条件的最终结果是
                if (CollectionUtils.isEmpty(detailMatchList)) {
                    return;
                }

                RuleDataRelation ruleDataRelation = new RuleDataRelation();
                BeanUtils.copyProperties(x, ruleDataRelation);
                if (Objects.equals(detailCalc.get(), 0)) {
                    // 且 全为true则是大条件为true
                    List<Boolean> list1 = detailMatchList.stream().distinct().collect(Collectors.toList());
                    if (list1.size() == 1 && list1.get(0)) {
                        ruleDataRelation.setFlag(true);
                    } else {
                        ruleDataRelation.setFlag(false);
                    }
                } else {
                    // 或 任意有一个true则是大条件为true
                    List<Boolean> conditions = detailMatchList.stream()
                            .filter(ma -> Objects.equals(ma, true)).collect(Collectors.toList());
                    if (conditions.size() >= 1) {
                        ruleDataRelation.setFlag(true);
                    } else {
                        ruleDataRelation.setFlag(false);
                    }
                }
                mainRuleList.add(ruleDataRelation);
            });

            // 判断当前商品符合的标签
            if (CollectionUtils.isEmpty(mainRuleList)) {
                return;
            }

            Map<String, List<RuleDataRelation>> map = mainRuleList.stream().collect(Collectors.groupingBy(RuleDataRelation::getLabelCode));
            List<RuleDataRelation> finalMapList = new ArrayList<>();
            // 处理同一个标签值的标签
            map.entrySet().forEach(dataMap -> {
                List<Boolean> disList = dataMap.getValue().stream()
                        .map(RuleDataRelation::getFlag).distinct().collect(Collectors.toList());
                List<Boolean> conditions = dataMap.getValue().stream().map(RuleDataRelation::getFlag)
                        .filter(ma -> Objects.equals(ma, true)).collect(Collectors.toList());
                if (Objects.equals(dataMap.getValue().get(0).getCalcRelation(), 0) && disList.size() == 1 && disList.get(0)) {
                    finalMapList.add(dataMap.getValue().get(0));
                } else if (Objects.equals(dataMap.getValue().get(0).getCalcRelation(), 1) && conditions.contains(true)) {
                    finalMapList.add(dataMap.getValue().get(0));
                }
            });


            // 处理同一个二级标签
            if (CollectionUtils.isEmpty(finalMapList)) {
                return;
            }
            List<String> filterLabelSortList = Arrays.stream(filterLabelSort.split(";")).collect(Collectors.toList());
            Map<String, List<RuleDataRelation>> finalBeforeMap = finalMapList.stream().collect(Collectors.groupingBy(RuleDataRelation::getParentLabel));
            Comparator<RuleDataRelation> byScoreDesc = Comparator.comparing(RuleDataRelation::getSort);
            List<RuleDataRelation> finalList = new ArrayList<>();
            finalBeforeMap.forEach((parentLabel, ruleDetailList) -> {
                List<RuleDataRelation> needDealList = new ArrayList<>();
                // 先获取全部是1的 如果没有1 获取排序第一的 【为了兼容配置1都会被留下】
                needDealList = ruleDetailList.stream().filter(x -> Objects.equals(x.getSort(), 1))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(needDealList)) {
                    RuleDataRelation ruleResp = ruleDetailList.stream().sorted(byScoreDesc).findFirst().orElse(null);
                    needDealList.add(ruleResp);
                }

                if (CollectionUtils.isEmpty(needDealList)) {
                    return;
                }

                // 自动风格只会打一个标 重写父级和排序 获取一个标
                needDealList.forEach(ruleResp -> {
                    RuleDataRelation finalRuleResp = new RuleDataRelation();
                    BeanUtils.copyProperties(ruleResp, finalRuleResp);
                    String[] split = finalRuleResp.getParentLabel().split("-");
                    finalRuleResp.setParentLabel(split[0] + "-" + split[1]);
                    if (filterLabelSortList.contains(finalRuleResp.getParentLabel())) {
                        finalRuleResp.setSort(1);
                    } else {
                        finalRuleResp.setSort(Integer.valueOf(split[2].substring(1)));
                    }

                    finalList.add(finalRuleResp);
                });

            });

            // 最后一层数据排序
            Map<String, List<RuleDataRelation>> finalMap = finalList.stream().collect(Collectors.groupingBy(RuleDataRelation::getParentLabel));
            finalMap.forEach((parentLabel, ruleDetailList) -> {
                if (filterLabelSortList.contains(parentLabel)) {
                    // 新风格如果配置就是打多个标
                    ruleDetailList.forEach(ruleResp -> {
                        SysProductCalcLabel label = new SysProductCalcLabel();
                        label.setProductId(Objects.toString(info.getProductId()));
                        label.setLabelCode(ruleResp.getLabelCode());
                        label.setLabelName(ruleResp.getLabelName());
                        label.setSkcCode(v);
                        label.setId(IdLeaf.getId(labelRuleTagId));
                        label.setCreateTime(new Date());
                        label.setUpdateTime(new Date());
                        inserts.add(label);
                    });
                } else {
                    RuleDataRelation ruleResp = ruleDetailList.stream().sorted(byScoreDesc).findFirst().orElse(null);
                    if (Objects.equals(ruleResp, null)) {
                        return;
                    }

                    SysProductCalcLabel label = new SysProductCalcLabel();
                    label.setProductId(Objects.toString(info.getProductId()));
                    label.setLabelCode(ruleResp.getLabelCode());
                    label.setLabelName(ruleResp.getLabelName());
                    label.setSkcCode(v);
                    label.setId(IdLeaf.getId(labelRuleTagId));
                    label.setCreateTime(new Date());
                    label.setUpdateTime(new Date());
                    inserts.add(label);
                }
            });
        });
    }

    private List<LabelProductInfoEsResp> getBatchLabelProductInfo(List<String> skcCodes) {
        SearchRequest request = new SearchRequest();
        request.indices(labelProductIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (CollectionUtils.isEmpty(skcCodes)) {
            return Collections.emptyList();
        }

        if (CollectionUtils.isNotEmpty(skcCodes)) {
            queryBuilder.must(QueryBuilders.termsQuery("skc.keyword", skcCodes));
        }

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(500);
        sourceBuilder.sort("id", SortOrder.DESC);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品计算标签索引： {}", request.source().toString());

        List<LabelProductInfoEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }

            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                LabelProductInfoEsResp entity = LabelProductInfoEsResp.fromJson(hits[i].getSourceAsString(), LabelProductInfoEsResp.class);
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品计算标签索引异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;

    }

    @Override
    public List<CreateCalcLabelResp> createLabel(List<String> requestData) {
        List<CreateCalcLabelResp> rets = new ArrayList<>();
        if (CollectionUtils.isEmpty(requestData)) {
            this.dealLabelRuleToCreate();
        } else {
            requestData = requestData.stream().distinct().collect(Collectors.toList());
            // 查询所有规则条件
            List<RuleResp> dealRuleList = getRuleResps();
            if (CollectionUtils.isEmpty(dealRuleList))
            return Collections.emptyList();

            // 处理具体规则数据
            List<SysProductCalcLabel> inserts = new ArrayList<>();
            dealProductCalcLabel(dealRuleList, inserts, requestData);
            if (CollectionUtils.isEmpty(inserts)) {
                return Collections.emptyList();
            }
            inserts.forEach(v -> {
                CreateCalcLabelResp resp = new CreateCalcLabelResp();
                resp.setSkcCode(v.getSkcCode());
                resp.setLabelName(v.getLabelName());
                resp.setLabelCode(v.getLabelCode());
                rets.add(resp);
            });
        }
        return rets;
    }

    @Override
    public Boolean judgeIsCanCreate(String requestData) {

        List<SysCategoryModel> sysCategoryModels = sysBaseAPI.queryDSysCateByCodes(Lists.newArrayList(requestData));
        if (CollectionUtils.isEmpty(sysCategoryModels)) {
            return false;
        }
        List<SysCategoryModel> sysCategoryModels2 = sysBaseAPI.queryAllDSysCategoryByCode(requestData);
        if (CollectionUtils.isNotEmpty(sysCategoryModels2)) {
            return false;
        }
        return true;
    }


    private static String getFieldValue(String fieldName, LabelProductInfoEsResp info) {
        try {
            Field field = info.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return Objects.toString(field.get(info));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    private static Boolean isJudge(List<String> productList, List<String> ruleList) {
        AtomicReference<Boolean> isInclude = new AtomicReference<>(false);
        if (CollectionUtils.isEmpty(productList)) {
            return isInclude.get();
        }
        productList.stream().map(item -> Strman.replace(item, "_", "-", true))
                .collect(Collectors.toList()).forEach(v -> {
            if (ruleList.contains(v)) {
                isInclude.set(true);
                return;
            }
        });
        return isInclude.get();
    }




    public LabelProductInfoEsResp getLabelProductInfo(String skc) {
        GetRequest request = new GetRequest(labelProductIndex, skc);
        LabelProductInfoEsResp resp = null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                resp = LabelProductInfoEsResp.fromJson(response.getSourceAsString(), LabelProductInfoEsResp.class);
                resp.setId(skc);
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询标签商品信息异常e = {}", e.getMessage());
        }
        return resp;
    }

    @Override
    public Boolean beforeDelLabel(String requestData) {
        // 根据labelCode查询 是否还有需要删除的规则
        QueryWrapper<LabelMainRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", 0);
        queryWrapper.eq("LABEL_CODE", requestData);
        List<LabelMainRule> mainRuleList = labelMainRuleMapper.selectList(queryWrapper);
        return CollectionUtils.isEmpty(mainRuleList) ? true : false;
    }

    @Override
    public void pullOutsideNameJob() {
        // 查询对外名称的表
        com.github.pagehelper.Page<Object> outsideNamePageTotal = PageHelper.startPage(1, 100);
        List<CalcLabelOutsideName> outsideNameTotalList = calcLabelOutsideNameMapper.selectOutsideNameList();

        long total = outsideNamePageTotal.getTotal();
        long pageTotal = 0;
        int pageSize = 500;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }

        // 查询对应的数据在配置中 无则进行插入
        List<String> notInValueList = new ArrayList<>();
        for (int i = 0; i < pageTotal; i++) {
            com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, 500);
            List<CalcLabelOutsideName> outsideNameList = calcLabelOutsideNameMapper.selectOutsideNameList();
            if (CollectionUtils.isEmpty(outsideNameList)) {
                return;
            }

            // 判断存在的
            List<String> outSides = outsideNameList.stream().map(CalcLabelOutsideName::getScOutsideName).collect(Collectors.toList());
            List<LabelSettingInfoValue> labelSettingInfoValues = labelSettingInfoValueMapper.selectOutsideNameList(outSides);

            if (CollectionUtils.isEmpty(labelSettingInfoValues)) {
                notInValueList = outSides;
            } else {
                List<String> settingValues = labelSettingInfoValues.stream().map(LabelSettingInfoValue::getLabelSettingValue)
                        .collect(Collectors.toList());
                Collection result = org.apache.commons.collections4.CollectionUtils.subtract(outSides, settingValues);
                if (CollectionUtils.isEmpty(result)) {
                    notInValueList = notInValueList;
                } else {
                    notInValueList = (List<String>) result.stream().collect(Collectors.toList());
                }
            }

        }


        // 将没有的数据进行组装插入
        if (CollectionUtils.isEmpty(notInValueList)) {
            return;
        }
        List<LabelSettingInfoValue> values = new ArrayList<>();
        notInValueList.forEach(v -> {
            LabelSettingInfoValue value = new LabelSettingInfoValue();
            value.setId(calcLabelOutsideNameMapper.getLabelSettingValueId());
            value.setCreateTime(new Date());
            value.setUpdateTime(new Date());
            value.setIsDeleted(0);
            value.setLabelSettingValue(v);
            value.setLabelSettingInfoId(56L);
            values.add(value);
        });

        if (CollectionUtils.isNotEmpty(values)) {
            List<List<LabelSettingInfoValue>> partition = com.google.common.collect.Lists.partition(values, 1000);
            partition.forEach(v -> {
                labelSettingInfoValueMapper.batchInsert(v);
            });
        }
    }

    private SearchLabelRuleResp.MainRuleData buildSearchMainRuleData(LabelMainRule data, List<LabelRuleDetail> labelRuleDetailList) {
        SearchLabelRuleResp.MainRuleData mainRuleData = new SearchLabelRuleResp.MainRuleData();
        BeanUtils.copyProperties(data, mainRuleData);

        List<SearchLabelRuleResp.RuleDetailData> ruleDetailData = new ArrayList<>();
        labelRuleDetailList.forEach(v -> {
            SearchLabelRuleResp.RuleDetailData ruleDetailData1 = new SearchLabelRuleResp.RuleDetailData();
            BeanUtils.copyProperties(v, ruleDetailData1);
            ruleDetailData.add(ruleDetailData1);
        });

        mainRuleData.setRuleDetails(ruleDetailData);
        return mainRuleData;
    }


    public void testQuery() {
        // 查询当前是否存在
        List<SysCategoryModel> sysCategoryModels = sysBaseAPI.queryDSysCateByCodes(Lists.newArrayList("B05-A07-A01-A01"));
        log.info("==================" + JSONObject.toJSONString(sysCategoryModels));

        List<SysCategoryModel> sysCategoryModels1 = sysBaseAPI.queryDSysCateByCodes(Lists.newArrayList("B05-A24-A08-A01"));
        log.info("==================" + JSONObject.toJSONString(sysCategoryModels1));


        // 查询当前 无数据则是子级
        List<SysCategoryModel> sysCategoryModels2 = sysBaseAPI.queryAllDSysCategoryByCode("B05-A07-A01-A01");
        log.info("==================" + JSONObject.toJSONString(sysCategoryModels2));

        List<SysCategoryModel> sysCategoryModels3 = sysBaseAPI.queryAllDSysCategoryByCode("B05-A24-A08-A01");
        log.info("==================" + JSONObject.toJSONString(sysCategoryModels3));

    }
}
