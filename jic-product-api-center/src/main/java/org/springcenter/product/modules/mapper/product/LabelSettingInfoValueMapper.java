package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.LabelSettingInfo;
import org.springcenter.product.modules.model.LabelSettingInfoValue;

import java.util.List;

public interface LabelSettingInfoValueMapper extends BaseMapper<LabelSettingInfoValue> {

    List<LabelSettingInfoValue> selectOutsideNameList(@Param("list") List<String> outSides);

    void batchInsert(@Param("list") List<LabelSettingInfoValue> v);
}
