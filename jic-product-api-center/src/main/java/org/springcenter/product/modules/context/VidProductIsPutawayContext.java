package org.springcenter.product.modules.context;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/8/15 15:57
 */
@Data
public class VidProductIsPutawayContext {

    private String mid;

    private String brandId;

    private String id;

    private String business_id;

    private String public_account_id;

    private String topic;

    private String event;

    private String msg_body;

    private String version;

    private String bosId;

    private String msgBody;

    @Data
    public static class IsPutawayProductData {
        private String vid;

        private Boolean isOnline;

        private List<Long> goodsIdList;
    }


    @Data
    public static class IsPutawayProductRelationData {
        private List<Long> vidList;

        private Boolean isOnline;

        private List<Long> goodsIdList;

        private Boolean isAssigned;
    }
}
