package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springcenter.product.modules.model.MProductVersionSales;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【M_PRODUCT_VERSION_SALES(版型商品销量)】的数据库操作Mapper
* @createDate 2022-12-09 17:13:42
* @Entity generator.domain.MProductVersionSales
*/

public interface MProductVersionSalesMapper extends BaseMapper<MProductVersionSales> {


    List<MProductVersionSales> selectByParams(@Param("modelNumber") String modelNumber, @Param("sort") Integer sort,
                                              @Param("sampleCode") String sampleCode);

    MProductVersionSales selectSumByParam(@Param("sort") Integer sort, @Param("modelNumber") String modelNumber,
                                          @Param("sampleCode") String sampleCode);

    List<MProductVersionSales> selectSumByParamAndSampleCode(@Param("modelNumber") String modelNumber, @Param("sampleNo") String sampleNo);
}
