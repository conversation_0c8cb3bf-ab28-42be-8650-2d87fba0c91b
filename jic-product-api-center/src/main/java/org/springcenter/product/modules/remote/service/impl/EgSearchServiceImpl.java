package org.springcenter.product.modules.remote.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springcenter.product.config.exception.ProductException;
import org.springcenter.product.modules.remote.IEgSearchRemoteApi;
import org.springcenter.product.modules.remote.JicBaseResp;
import org.springcenter.product.modules.remote.entity.EgSearchReqEntity;
import org.springcenter.product.modules.remote.entity.EgSearchRespEntity;
import org.springcenter.product.modules.remote.service.IEgSearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class EgSearchServiceImpl implements IEgSearchService {

    @Autowired
    private IEgSearchRemoteApi iEgSearchRemoteApi;


    @Override
    public List<EgSearchRespEntity> searchAllBrandAndProduct(EgSearchReqEntity req) {
        try {
            Response<JicBaseResp<List<EgSearchRespEntity>>> response = iEgSearchRemoteApi.search(req).execute();
            if (response.isSuccessful()) {
                JicBaseResp<List<EgSearchRespEntity>> body = response.body();
                List<EgSearchRespEntity> egSearchRespEntities = body.getData();
                if (CollectionUtils.isEmpty(egSearchRespEntities)) {
                    return Lists.newArrayList();
                }


                return egSearchRespEntities;
            }
            log.error("调用以图搜款接口异常 params:{}  e:{} message:{}", JSONObject.toJSONString(req), response.code(), response.message());
            throw new ProductException("无法解析，请换张图片");
        } catch (IOException e) {
            log.error("调用以图搜款接口异常 params:{}  e:{} message:{}", JSONObject.toJSONString(req), e, e.getMessage());
            throw new ProductException("无法解析，请换张图片");
        }
    }
}
