package org.springcenter.product.modules.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.SingleProductRecommendationReq;
import org.springcenter.product.api.dto.StoreGoodSpuResp;
import org.springcenter.product.modules.enums.WscBrandEnum;
import org.springcenter.product.modules.model.bojun.CStore;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/5/15 15:30
 */
@Data
public class BigDataSingleReconReq {

    private int topKGt;

    private int topKLt;

    private String unionId;

    private String weid;

    private String traceId;

    private int m_product_id;

    private String requestSource = "boxSelfSelected";

    public static BigDataSingleReconReq build(SingleProductRecommendationReq req, CStore cStore, String traceId, List<Long> aolaiList) {
        BigDataSingleReconReq bigDataSingleReconReq = new BigDataSingleReconReq();

        bigDataSingleReconReq.setTraceId(traceId);
        if (StringUtils.isBlank(req.getUnionId())) {
            bigDataSingleReconReq.setUnionId("-1");
        } else {
            bigDataSingleReconReq.setUnionId(req.getUnionId());
        }

        bigDataSingleReconReq.setM_product_id(Integer.valueOf(req.getProductId()));
        WscBrandEnum wscBrandEnum = WscBrandEnum.getWscBrandEnum(cStore.getCArcbrandId());
        // 先判断是否奥莱
        if (wscBrandEnum == null) {
            bigDataSingleReconReq.setWeid("-1");
        } else if (cStore.getCCustomerId() == 176L && Objects.equals(cStore.getDefault05(), "是")) {
            bigDataSingleReconReq.setWeid("11");
        } else if (cStore.getCCustomerId() != 176L && aolaiList.contains(cStore.getCStoreAttrib9Id())) {
            bigDataSingleReconReq.setWeid("11");
        } else if (cStore.getCUnionstoreId() != null) {
            // 判断是否多品牌
            bigDataSingleReconReq.setWeid("5");
        } else {
            bigDataSingleReconReq.setWeid(Objects.toString(wscBrandEnum.getWscBrandId()));
        }
        bigDataSingleReconReq.setTopKGt(0);
        bigDataSingleReconReq.setTopKLt(200);

        // 处理单品牌
        return bigDataSingleReconReq;
    }
}
