<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.LabelSettingInfoValueMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.LabelSettingInfoValue">
    <result column="ID" property="id" />
    <result column="LABEL_SETTING_VALUE" property="labelSettingValue" />
    <result column="LABEL_SETTING_INFO_ID" property="labelSettingInfoId" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="IS_DELETED" property="isDeleted" />
  </resultMap>

  <sql id="Base_Column_List">
    ID,LABEL_SETTING_VALUE,LABEL_SETTING_INFO_ID,
        CREATE_TIME,UPDATE_TIME, IS_DELETED
  </sql>
    <insert id="batchInsert">
      INSERT ALL
      <foreach item="item" index="index" collection="list">
        INTO LABEL_SETTING_INFO_VALUE
        (ID, LABEL_SETTING_VALUE, LABEL_SETTING_INFO_ID, IS_DELETED, CREATE_TIME, UPDATE_TIME) VALUES
        (#{item.id}, #{item.labelSettingValue,jdbcType=VARCHAR}, #{item.labelSettingInfoId},
        #{item.isDeleted},  #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
      </foreach>
      SELECT 1 FROM DUAL
    </insert>

    <select id="selectOutsideNameList" resultMap="BaseResultMap">
      SELECT <include refid="Base_Column_List"></include>
          FROM LABEL_SETTING_INFO_VALUE
         WHERE IS_DELETED = 0 and LABEL_SETTING_VALUE in
      <foreach collection="list" item="id" open="(" close=")" separator=",">
          #{id}
      </foreach>
    </select>


</mapper>