package org.springcenter.product.modules.service.impl;

import brave.Tracer;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Preconditions;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.lucene.search.join.ScoreMode;
import org.assertj.core.util.Lists;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.*;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.product.api.IProductDetailsImgApi;
import org.springcenter.product.api.constant.RedisKeyConstant;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.JicStoreTypeEnum;
import org.springcenter.product.modules.context.BojunProductInfo;
import org.springcenter.product.modules.context.ProductVidSkcContext;
import org.springcenter.product.modules.context.VidProductIsCanSellContext;
import org.springcenter.product.modules.context.VidProductIsPutawayContext;
import org.springcenter.product.modules.entity.EbAndWscSpuStockEntity;
import org.springcenter.product.modules.entity.SpuStockEntity;
import org.springcenter.product.modules.enums.BigDataBjBrandEnum;
import org.springcenter.product.modules.enums.BigDataSearchChannelEnum;
import org.springcenter.product.modules.mapper.bigdata.ProFaStorageQtyMapper;
import org.springcenter.product.modules.mapper.bojun.CCustomerMapper;
import org.springcenter.product.modules.mapper.bojun.CStoreMapper;
import org.springcenter.product.modules.mapper.bojun.MProductMapper;
import org.springcenter.product.modules.mapper.product.*;
import org.springcenter.product.modules.mapper.zt.WeiMoBStorageMapper;
import org.springcenter.product.modules.model.*;
import org.springcenter.product.modules.model.bojun.CStore;
import org.springcenter.product.modules.model.bojun.MProduct;
import org.springcenter.product.modules.model.zt.WeiMoBStorage;
import org.springcenter.product.modules.remote.entity.*;
import org.springcenter.product.modules.remote.service.IBigDataService;
import org.springcenter.product.modules.remote.service.IWeiMoService;
import org.springcenter.product.modules.repository.IBoxMProductRepository;
import org.springcenter.product.modules.repository.bojun.StockRepository;
import org.springcenter.product.modules.service.IProductService;
import org.springcenter.product.modules.service.IProductStoreService;
import org.springcenter.product.modules.service.IStoreProductEsScriptService;
import org.springcenter.product.modules.util.EsUtil;
import org.springcenter.product.modules.util.FileParseUtil;
import org.springcenter.product.modules.util.RedisService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import strman.Strman;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class ProductStoreServiceImpl implements IProductStoreService {
    @Value("${es.index.store.goods.skc}")
    private String storeProductSkcIndex;

    @Value("${es.index.store.goods}")
    private String storeGoodsIndex;

    @Value("${bandIds.config}")
    private String bandIdsConfig;
    @Autowired
    private EsUtil esUtil;

    @Autowired
    private IProductService iProductService;

    @Autowired
    private StockRepository stockRepository;

    @Autowired
    private IBoxMProductRepository boxMProductRepository;

    @Autowired
    BoxMProductMapper boxMProductMapper;

    @Value("${es.mall.product.skc.index}")
    private String MALL_PRODUCT_SKC_INDEX;

    @Value("${es.mall.product.spu.index}")
    private String MALL_PRODUCT_SPU_INDEX;

    @Value("${es.index.vid.mall.goods.skc}")
    private String vidMallGoodsSkcIndex;

    @Autowired
    private IProductService productService;

    @Value("${wx.brand.commonConfigs}")
    private String commonConfigs;

    @Autowired
    private JicMallOrgMapper jicMallOrgMapper;

    @Autowired
    private WeiMoBStorageMapper weiMoBStorageMapper;

    @Autowired
    private CCustomerMapper cCustomerMapper;

    @Autowired
    private CStoreMapper cStoreMapper;

    @Autowired
    private JicMallStoreWarehouseMapper jicMallStoreWarehouseMapper;

    @Value("${jic.vid.product.leaf.tag}")
    private String jicVidProductIdLeaf;

    @Autowired
    private JicVidProductIsSellInfoMapper jicVidProductIsSellInfoMapper;

    @Autowired
    private JicProductMallRelationMapper jicProductMallRelationMapper;

    @Autowired
    private IStoreProductEsScriptService storeProductEsScriptService;

    @Autowired
    private IWeiMoService weiMoService;

    @Value("${es.index.split.store.goods}")
    private String storeSplitGoodsIndex;


    @Value("${es.index.split.store.goods.mode}")
    private Long storeSplitGoodsIndexMode;

    @Autowired
    private ProFaStorageQtyMapper proStorageQtyMapper;

    @Value("${vid.index.mode}")
    private String vidIndexMode;

    @Autowired
    private JicProductMallInfoMapper jicProductMallInfoMapper;

    @Autowired
    private JicProductPutwayMapper jicProductPutwayMapper;

    @Autowired
    private IBigDataService bigDataService;

    @Value("${jingxiao.aolai.data}")
    private String jingxiaoAolaiData;

    @Value("${aolai.lifestyle.product}")
    private String aolaiLifestyleProduct;

    @Value("${posive.lifestyle.product}")
    private String posiveLifestyleProduct;

    @Value("${dps.lifestyle.product}")
    private String dpsLifestyleProduct;

    @Autowired
    private MProductMapper mProductMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private Tracer tracer;


    @Override
    public List<ProductSkcResp> queryStoreGoodSkc(QueryGoodsSkcListReq goodsReq, Page page) {
        // 如果搜索不为空 则进行算法搜索
        if (StringUtils.isNotBlank(goodsReq.getName())) {
            List<Long> bigDataProductIds = searchFashionerBigDataSearch(goodsReq, BigDataSearchChannelEnum.BOX);
            page.setCount(bigDataProductIds.size());

            if (CollectionUtils.isEmpty(bigDataProductIds)) {
                return Collections.emptyList();
            }

            if (CollectionUtils.isNotEmpty(goodsReq.getIncludeProductIds())) {
                List<Long> jjList = getJjList(bigDataProductIds, goodsReq.getIncludeProductIds());
                if (CollectionUtils.isEmpty(jjList)) {
                    return Collections.emptyList();
                }
                goodsReq.setIncludeProductIds(jjList);
            } else {
                goodsReq.setIncludeProductIds(bigDataProductIds);
            }
        }

        SearchRequest request = new SearchRequest();
        request.indices(storeProductSkcIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        buildQueryV2(queryBuilder, goodsReq);
        // 生命周期不展示N+3
        List<Long> list1 = Arrays.stream(dpsLifestyleProduct.split(",")).map(s -> Long.parseLong(s)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list1)) {
            queryBuilder.must(QueryBuilders.termsQuery("lifestyle_tag", list1));
        }

        sourceBuilder.fetchSource(new String[]{"name","qty","value","price","id", "sales_num","colorno", "color_name",
                "store_id","imgurl","cover_imgs","detail_imgs","m_product_id","brand", "skus","year",
                "small_season_id","small_season","name_text","skc_code","skc_code1"
                , "c_arcbrand_id","m_big_category_id","quantify_qty",
                "m_small_category_id","small_season","m_small_category","m_big_category","m_band", "topping_tag_list"},
                new String[]{});
        sourceBuilder.query(queryBuilder);
        // 使用主分片查询
        if (CollectionUtils.isNotEmpty(goodsReq.getIncludeProductIds())) {
            sourceBuilder.from(0);
            sourceBuilder.size(page.getPageSize());
        } else {
            if(page.getPageNo() > 1){
                request.preference("primary");
            }
            sourceBuilder.from((page.getPageNo() - 1) * page.getPageSize());
            sourceBuilder.size(page.getPageSize());
        }

        //排序先按照score排序 3是预售
        sourceBuilder.sort("topping_tag_list.topping_tag", SortOrder.ASC);
        if(StringUtils.isNotBlank(goodsReq.getName())){
            sourceBuilder.sort("_score", SortOrder.DESC);
        }
        // 3是啥？
        if (goodsReq.getSorted() == 2 || (StringUtils.isBlank(goodsReq.getName()) && goodsReq.getSorted() == 3)){
            sourceBuilder.sort("sales_num", SortOrder.DESC)
                    .sort("year", SortOrder.DESC)
                    .sort("m_band_id", SortOrder.DESC)
                    .sort("colorno.keyword", SortOrder.ASC)
                    .sort("m_product_id", SortOrder.DESC);
        }
        if (goodsReq.getSorted() == 1){
            sourceBuilder.sort("year", SortOrder.DESC)
                    .sort("m_band_id", SortOrder.DESC)
                    .sort("m_product_id", SortOrder.DESC);;
        }
        if (goodsReq.getSorted() == 4){
            sourceBuilder.sort("quantify_qty", SortOrder.DESC)
                    .sort("year", SortOrder.DESC)
                    .sort("m_band_id", SortOrder.DESC)
                    .sort("m_product_id", SortOrder.DESC);
        }
        request.source(sourceBuilder);

        List<ProductSkcResp> entities = new ArrayList<>();
        searchEsProductSkc(entities, request, goodsReq, page);
        return entities;
    }

    private List<Long> getJjList(List<Long> bigDataProductIds, List<Long> searchProductIds) {
        List<Long> resultList = new ArrayList<>();
        Map<String, Long> map = new HashMap<>();
        bigDataProductIds.forEach(a1->{
            map.put(a1+ "", a1);
        });
        searchProductIds.forEach(a2->{
            Long obj = map.get(a2 + "");
            if (obj != null){
                resultList.add(obj);
            }
        });
        return resultList;
    }


    private List<Long> searchFashionerBigDataSearch(QueryGoodsSkcListReq goodsReq, BigDataSearchChannelEnum channelEnum) {
        BigDataSearchReqEntity reqEntity = new BigDataSearchReqEntity();
        List<Long> cArcBrandIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(goodsReq.getBrandIds())) {
            cArcBrandIds = goodsReq.getBrandIds();
        } else {
            cArcBrandIds = Arrays.stream(BigDataBjBrandEnum.values()).map(BigDataBjBrandEnum::getCode)
                    .collect(Collectors.toList());
        }

        reqEntity.setBrand(BigDataBjBrandEnum.getByCode(cArcBrandIds));
        reqEntity.setChannel_dict(channelEnum.getCode());
        reqEntity.setQuery(goodsReq.getName());
        reqEntity.setRequestId(channelEnum.getDesc() + "HT" + System.currentTimeMillis());
        reqEntity.setTraceId(tracer.currentSpan().context().traceIdString());
        // 默认就取800条
        reqEntity.setList_size(800);
        reqEntity.setList_start(0);

        // reqEntity.setRequestSource(channelEnum == BigDataSearchChannelEnum.BOX ? "box_ht_fashioner" : "wsc_ht_pg_fashioner");

        List<BigDataSearchRespEntity> entities = bigDataService.searchByText(reqEntity);

        return entities.stream().map(v -> v.getSpu_id()).collect(Collectors.toList());
    }

    /**
     * 查询SKC商品
     * @param request
     * @param goodsReq
     * @return
     */
    private void searchEsProductSkc(List<ProductSkcResp> entities, SearchRequest request, QueryGoodsSkcListReq goodsReq, Page page){
        try {
            log.info("查询门店SKC数据ES查询语句 query = {}", request.source().toString());
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return ;
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductSkcResp entity = ProductSkcResp.fromJson(hits[i].getSourceAsString(), ProductSkcResp.class);
                entity.buildSku();
                entity.buildToppingTag();
                entity.setId(entity.getM_product_id() == 0 ? String.valueOf(entity.getProduct_id()) : String.valueOf(entity.getM_product_id()));
                entity.setPrimaryKey(hits[i].getId());
                entity.setM_product_id(Long.valueOf(entity.getId()));
                if(CollectionUtils.isNotEmpty(goodsReq.getProductCodes()) ||  CollectionUtils.isNotEmpty(goodsReq.getProductColorNos())){
                    entity.setIsAddTrolley(1);
                }
                entities.add(entity);
            }
        } catch (IOException e) {
        }
    }

    /**
     * 查询SKC商品
     * @param request
     * @param goodsReq
     * @return
     */
    private void searchEsProductVidSkc(List<ProductVidSkcResp> entities, SearchRequest request, QueryGoodsSkcListReq goodsReq, Page page){
        try {
            log.info("查询门店SKC数据ES查询语句 query = {}", request.source().toString());
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return ;
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductVidSkcResp entity = ProductVidSkcResp.fromJson(hits[i].getSourceAsString(), ProductVidSkcResp.class);
                entity.buildSku();
                entity.setId(entity.getM_product_id() == 0 ? String.valueOf(entity.getProduct_id()) : String.valueOf(entity.getM_product_id()));
                entity.setPrimaryKey(hits[i].getId());
                entity.setM_product_id(Long.valueOf(entity.getId()));
                if(CollectionUtils.isNotEmpty(goodsReq.getProductCodes()) ||  CollectionUtils.isNotEmpty(goodsReq.getProductColorNos())){
                    entity.setIsAddTrolley(1);
                }
                entities.add(entity);
            }
        } catch (IOException e) {
        }
    }


    private void buildQueryV2(BoolQueryBuilder queryBuilder, QueryGoodsSkcListReq context) {
        // 置顶标签：增加渠道筛选
        queryBuilder.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("topping_tag_list")));
        queryBuilder.should(QueryBuilders.termsQuery("topping_tag_list.channel", "BOX"));
        buildCommon(queryBuilder, context);
        if (StringUtils.isNotEmpty(context.getStoreId())){
            queryBuilder.must(QueryBuilders.termQuery("store_id", context.getStoreId()));
        }
        // 直接走大数据 故直接注释
        /*if (StringUtils.isNotBlank(context.getName())){
            context.setName(context.getName().trim());
            if(isChinese(context.getName())){
                queryBuilder.must(QueryBuilders.matchQuery("name_text",context.getName()));
            }else{
                if(context.getName().length() > 12){
                    context.setName(context.getName().substring(0,12));
                }
                queryBuilder.must(QueryBuilders.matchPhrasePrefixQuery("skc_code1",context.getName()).maxExpansions(50));
            }
        }*/
        // 价格处理，列表和精准搜索都不展示
        if (context.getLPrice() != null || context.getGPrice() != null){
            if (context.getLPrice() != null && context.getGPrice() != null && context.getLPrice() != 0 && context.getGPrice() != 0){
                queryBuilder.must(QueryBuilders.rangeQuery("price").gte(context.getLPrice()).lte(context.getGPrice()));
            } else if (context.getLPrice() != null && context.getGPrice() == null && context.getLPrice() != 0){
                queryBuilder.must(QueryBuilders.rangeQuery("price").gte(context.getLPrice()));
            } else if (context.getLPrice() == null && context.getGPrice() != null && context.getGPrice() != 0){
                queryBuilder.must(QueryBuilders.rangeQuery("price").lte(context.getGPrice()));
            } else {
                queryBuilder.must(QueryBuilders.rangeQuery("price").gt(0));
            }
        } else {
            queryBuilder.must(QueryBuilders.rangeQuery("price").gt(0));
        }
        //款号 + 款色  in操作
        if (CollectionUtils.isNotEmpty(context.getProductCodes())){
            queryBuilder.must(QueryBuilders.termsQuery("skc_code1.keyword", context.getProductCodes()));
        }
        // NestedQueryBuilder nestedQuery = createNestedQuery(context);
        // queryBuilder.must(nestedQuery);
        if (CollectionUtils.isNotEmpty(context.getIncludeProductIds())){
            queryBuilder.must(QueryBuilders.termsQuery("m_product_id", context.getIncludeProductIds()));
        }
        if (CollectionUtils.isNotEmpty(context.getExcludeProductIds())){
            queryBuilder.mustNot(QueryBuilders.termsQuery("m_product_id", context.getExcludeProductIds()));
        }
        if (context.getProductId() != null){
            queryBuilder.must(QueryBuilders.termQuery("m_product_id",context.getProductId()));
        }
        if (CollectionUtils.isNotEmpty(context.getExcludeNames())){
            queryBuilder.mustNot(QueryBuilders.termsQuery("name", context.getExcludeNames()));
        }

        // 关于标签搜索
        if (CollectionUtils.isNotEmpty(context.getLabelCodes())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("labels", context.getLabelCodes().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script("1"));
            queryBuilder.must(termsSetQueryBuilder);
        }
    }

    private void buildQueryMall(BoolQueryBuilder queryBuilder, QueryGoodsSkcListReq context) {
        buildCommon(queryBuilder, context);
        if (StringUtils.isNotEmpty(context.getStoreId())){
            queryBuilder.must(QueryBuilders.termQuery("store_id", context.getStoreId()));
        }
        if (StringUtils.isNotBlank(context.getName())){
            context.setName(context.getName().trim());
            if(isChinese(context.getName())){
                queryBuilder.must(QueryBuilders.matchQuery("name_text",context.getName()));
            }else{
                if(context.getName().length() > 12){
                    context.setName(context.getName().substring(0,12));
                }
                queryBuilder.must(QueryBuilders.matchPhrasePrefixQuery("skc_code",context.getName()).maxExpansions(50));
            }
        }
        if (context.getLPrice() != null || context.getGPrice() != null){
            if (context.getLPrice() != null && context.getGPrice() != null){
                queryBuilder.must(QueryBuilders.rangeQuery("price").gte(context.getLPrice()).lte(context.getGPrice()));
            }

            if (context.getLPrice() != null && context.getGPrice() == null){
                queryBuilder.must(QueryBuilders.rangeQuery("price").gte(context.getLPrice()));
            }

            if (context.getLPrice() == null && context.getGPrice() != null){
                queryBuilder.must(QueryBuilders.rangeQuery("price").lte(context.getGPrice()));
            }
        }
        //款号 + 款色  in操作
        if (CollectionUtils.isNotEmpty(context.getProductCodes())){
            queryBuilder.must(QueryBuilders.termsQuery("skc_code1.keyword", context.getProductCodes()));
        }
        NestedQueryBuilder nestedQuery = createNestedQuery(context);
        queryBuilder.must(nestedQuery);
        if (CollectionUtils.isNotEmpty(context.getIncludeProductIds())){
            queryBuilder.must(QueryBuilders.termsQuery("product_id", context.getIncludeProductIds()));
        }
        if (CollectionUtils.isNotEmpty(context.getExcludeProductIds())){
            queryBuilder.mustNot(QueryBuilders.termsQuery("product_id", context.getExcludeProductIds()));
        }
        if (context.getProductId() != null){
            queryBuilder.must(QueryBuilders.termQuery("product_id",context.getProductId()));
        }
    }

    private void buildCommon(BoolQueryBuilder queryBuilder, QueryGoodsSkcListReq context) {
        if (CollectionUtils.isNotEmpty(context.getBandId())){
            List<Long> other = new ArrayList<>();
            if(context.getBandId().contains(2000L)){
                List<AttrResp> attrEntities = JSONObject.parseArray(bandIdsConfig, AttrResp.class);
                AttrResp attrResp = attrEntities.get(attrEntities.size() - 1);
                List<AttrResp> children = attrResp.getChildren();
                other = children.stream().map(r -> r.getId()).collect(Collectors.toList());
            }
            other.addAll(context.getBandId());
            queryBuilder.filter(QueryBuilders.termsQuery("m_band_id", other));
        }

        if (CollectionUtils.isNotEmpty(context.getSeasonIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("small_season_id", context.getSeasonIds()));
        }
        if (CollectionUtils.isNotEmpty(context.getNames())){
            queryBuilder.filter(QueryBuilders.termsQuery("name", context.getNames()));
        }

        if(CollectionUtils.isNotEmpty(context.getSmallCategoryIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("m_small_category_id", context.getSmallCategoryIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getBrandIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("c_arcbrand_id", context.getBrandIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getYears())){
            queryBuilder.filter(QueryBuilders.termsQuery("year", context.getYears()));
        }

        if (context.getIsStockNotEmpty() == 1){
            queryBuilder.filter(QueryBuilders.termQuery("qty", 1));
        }

    }

    /**
     * 判断字符串是否为中文
     *
     * @param input
     * @return
     */
    private static boolean isChinese(String input) {
        if(StringUtils.isBlank(input)){
            return false;
        }
        int length = input.length();
        for(int i = 0 ; i < length ; i++){
            String code = input.charAt(i)+"";
            boolean matches = code.matches("^[\u4e00-\u9fa5]+$");
            if(matches){
                return true;
            }
        }
        return false;
    }

    /**
     * 创建嵌套查询
     * @param context
     */
    private NestedQueryBuilder createNestedQuery(QueryGoodsSkcListReq context) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if(CollectionUtils.isNotEmpty(context.getSizeNos())){
            boolQueryBuilder.should(QueryBuilders.termsQuery("skus.sizeno",context.getSizeNos()));
            boolQueryBuilder.minimumShouldMatch(1);
        }
        if (context.getIsStockNotEmpty() == 1){
            boolQueryBuilder.must(QueryBuilders.termQuery("skus.qty", 1));
        }
        NestedQueryBuilder queryBuilder =
                new NestedQueryBuilder("skus",boolQueryBuilder, ScoreMode.None);
        return queryBuilder;
    }

    public static void main(String[] args) {
        String name = "9M93125004100377273";
        System.out.println(name.substring(0,9));
    }


    @Value("${product.aolai.radius}")
    private String productAolaiRadius;

    @Value("${product.recommend.num}")
    private Integer recommendNum;

    @Override
    public List<StoreGoodSpuResp> searchGoodsByStore(StoreGoodsReq context, Page page) {
        // 兼容box的搜索 前端不动
        if (StringUtils.isBlank(context.getKeywords()) && StringUtils.isNotBlank(context.getProductCode())) {
            context.setKeywords(context.getProductCode());
            context.setProductCode("");
        }
        SearchRequest request = new SearchRequest();

        // 根据门店判断index的查询
        if (CollectionUtils.isEmpty(context.getStoreId())) {
            throw new RuntimeException("门店信息不能为空");
        }
        CStore cStore = Preconditions.checkNotNull(cStoreMapper.selectById(Long.valueOf(context.getStoreId().get(0))),
                "未找到当前门店");

        /*生命周期 // 直营是default05就是奥莱 经销是cStoreattrib9Id为896L, 2756L, 15381L
        List<Long> list = Arrays.stream(jingxiaoAolaiData.split(",")).map(s -> Long.parseLong(s)).collect(Collectors.toList());
        Boolean isAolai = (Objects.equals(cStore.getCCustomerId(), 176L) && Objects.equals(cStore.getDefault05(), "是"))
                || (!Objects.equals(cStore.getCCustomerId(), 176L) && list.contains(cStore.getCStoreattrib9Id()));*/

        // 大数据搜索
        if (StringUtils.isNotBlank(context.getKeywords())) {
            List<String> productIds = searchInBigData(context, cStore);
            // page.setCount(productIds.size());

            if (CollectionUtils.isEmpty(productIds)) {
                return Collections.emptyList();
            } else {
                context.setProductIds(productIds);
            }
        }

        Long yuShu = null;
        if (cStore.getCUnionstoreId() == null) {
            yuShu = cStore.getId() % storeSplitGoodsIndexMode;
        } else {
            yuShu = cStore.getCUnionstoreId() % storeSplitGoodsIndexMode;
        }
        if (yuShu == null) {
            throw new RuntimeException("未计算到余数");
        }

        request.indices(StringUtils.replace(storeSplitGoodsIndex, "mode", Objects.toString(yuShu)));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        //搜索关键字逻辑
        String productCode = context.getProductCode();
        Boolean isTransSpu = false;
        if (StringUtils.isNotBlank(productCode)){
            //条码对应的款号
            if (productCode.startsWith("69")){
                List<BoxMProduct> products = iProductService.batchGetProductByGbCode(Arrays.asList(productCode));
                if (products.isEmpty()) return new ArrayList<>();
                context.setName(products.get(0).getName());
            }else {
                BoxMProduct boxMProduct = boxMProductRepository.selectBoxMProductByNo(productCode);
                if (boxMProduct != null && boxMProduct.getcArcbrandId() == 17L && productCode.length() > 9) {
                    // 当home为九位时
                    context.setName(productCode);
                } else if (boxMProduct != null && StringUtils.isNotBlank(boxMProduct.getName())) {
                    context.setName(boxMProduct.getName());
                    isTransSpu = true;
                } else {
                    context.setName(productCode);
                }
            }
        }
        if (StringUtils.isNotBlank(context.getName())){
            String name = context.getName().toUpperCase();
            if (!isTransSpu && name.length() >= 14) {
                name = name.substring(0,9);
            }
//            queryBuilder.must(QueryBuilders.matchQuery("name", name));
            return querySpuByName(name, context, page);//直接查询数据商品
        }
        if (context.getM_band_id() != null){
            queryBuilder.must(QueryBuilders.termQuery("m_band_id", context.getM_band_id()));
        }

        if (CollectionUtils.isNotEmpty(context.getM_band_ids())){
            queryBuilder.must(QueryBuilders.termsQuery("m_band_id", context.getM_band_ids()));
        }

        if (Objects.nonNull(context.getStoreId())){
            queryBuilder.must(QueryBuilders.termsQuery("c_store_id", context.getStoreId()));
        }

        if (Objects.nonNull(context.getM_big_category_id())){
            queryBuilder.must(QueryBuilders.termQuery("m_big_category_id", context.getM_big_category_id()));
        }
        if (Objects.nonNull(context.getM_small_category_id())){
            queryBuilder.must(QueryBuilders.termQuery("m_small_category_id", context.getM_small_category_id()));
        }

        if (CollectionUtils.isNotEmpty(context.getMBigCategoryIds())){
            queryBuilder.must(QueryBuilders.termsQuery("m_big_category_id", context.getMBigCategoryIds()));
        }
        if (CollectionUtils.isNotEmpty(context.getMSmallCategoryIds())){
            queryBuilder.must(QueryBuilders.termsQuery("m_small_category_id", context.getMSmallCategoryIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getM_brand_id())){
            queryBuilder.must(QueryBuilders.termsQuery("c_arcbrand_id", context.getM_brand_id()));
        }
        if (context.getYear() != null && !context.getYear().isEmpty()){
            queryBuilder.must(QueryBuilders.termsQuery("year", context.getYear()));
        }

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if (context.getIsStockNotEmpty() == 1 || context.isQty()) {
            boolQueryBuilder.should(QueryBuilders.rangeQuery("qty").gt(0));
        }
        if (context.isMallQty()) {
            boolQueryBuilder.should(QueryBuilders.rangeQuery("mall_qty").gt(0));
        }
        if (context.isEbQty()) {
            boolQueryBuilder.should(QueryBuilders.rangeQuery("eb_qty").gt(0));
        }
        if (context.isQwQty()) {
            boolQueryBuilder.should(QueryBuilders.rangeQuery("qw_qty").gt(0));
        }
        if (context.isCityQty()) {
            boolQueryBuilder.should(QueryBuilders.rangeQuery("city_qty").gt(0));
        }
        queryBuilder.must(boolQueryBuilder);



        if(CollectionUtils.isNotEmpty(context.getProductIds())){
            queryBuilder.must(QueryBuilders.termsQuery("m_product_id", context.getProductIds()));
        }

        if(CollectionUtils.isNotEmpty(context.getExcludeProductIds())){
            queryBuilder.mustNot(QueryBuilders.termsQuery("m_product_id", context.getExcludeProductIds()));
        }

        // 处理微商城商品状态
        if (context.getIsCanSell() != null) {
            queryBuilder.must(QueryBuilders.termQuery("is_can_sell", context.getIsCanSell()));
        }
        if (context.getIsOnShell() != null) {
            queryBuilder.must(QueryBuilders.termQuery("is_on_shell", context.getIsOnShell()));
        }

        // 筛选商品标签tag
        if (StringUtils.isNotBlank(context.getProductTag())) {
            queryBuilder.must(QueryBuilders.termsQuery("product_tag_list.keyword", context.getProductTag()));
        }
        //排序,最新排序，获取当前的年份
        if (context.getSorted() == 2){
            sourceBuilder.sort("sales_num", SortOrder.DESC);
        }

        if (context.getSorted() == 1){
            sourceBuilder.sort("year", SortOrder.DESC).sort("m_band_id", SortOrder.DESC);
            queryBuilder.mustNot(QueryBuilders.termQuery("year", "无年份"));
        }

        if (context.getSorted() == 4){
            sourceBuilder.sort("lifestyle_tag", SortOrder.ASC)
                    .sort("qty", SortOrder.DESC)
                    .sort("price", SortOrder.DESC);
        }

        if (context.getSorted() == 5){
            sourceBuilder.sort("lifestyle_tag", SortOrder.ASC)
                    .sort("year", SortOrder.DESC);
        }

        /* 生命周期// 默认按照生命周期排序
        if (context.getSorted() == 0) {
            sourceBuilder.sort("lifestyle_tag", SortOrder.ASC)
                         .sort("year", SortOrder.DESC);
        }*/

        if (CollectionUtils.isNotEmpty(context.getMustLabels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("labels", context.getMustLabels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script("1"));
            queryBuilder.must(termsSetQueryBuilder);
        }

        if (CollectionUtils.isNotEmpty(context.getMustLabelLevels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("label_levels", context.getMustLabelLevels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script("1"));
            queryBuilder.must(termsSetQueryBuilder);
        }
        // 过滤商品重复问题 【微定制出现LESS出现jnby商品】
        queryBuilder.filter(QueryBuilders.termQuery("is_display", 1));

        sourceBuilder.fetchSource(new String[]{"c_arcbrand_id","c_store_id","store_product_id","m_product_id",
                "m_brand_id", "name", "value", "price", "sales_num", "sku_list", "detail_imgs","cover_imgs", "qty",
                "eb_qty", "mall_qty", "product_tag_list"}, new String[]{});
        sourceBuilder.query(queryBuilder);

        if (Objects.equals(context.getSorted(), 5)) {
            sourceBuilder.from(0);
            sourceBuilder.size(recommendNum);
        } else {
            sourceBuilder.from((page.getPageNo() - 1) * page.getPageSize());
            sourceBuilder.size(page.getPageSize());
        }
        request.source(sourceBuilder);
        List<StoreGoodSpuResp> entities = new ArrayList<>();
        try {
            log.info("storeGoodsIndex sourceBuilder：{}", sourceBuilder);
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }

            page.setCount(response.getHits().getTotalHits().value);

            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                StoreGoodSpuResp entity = StoreGoodSpuResp.fromJson(hits[i].getSourceAsString(), StoreGoodSpuResp.class);
                entity.buildSku();
                entity.setId(entity.getM_product_id());
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品异常e = {}", e.getMessage());
            return new ArrayList<>();
        }



        if (Objects.equals(context.getSorted(), 5)) {
            // 走智能推荐排序
            // 查询大数据返回顺序
            log.info("screenRecommendProductList bigdata查询");
            List<Long> aolaiList = Arrays.stream(productAolaiRadius.split(","))
                    .map(v -> Long.valueOf(v)).collect(Collectors.toList());
            Pair<List<BigDataComprehensiveData>, Integer> comprehensiveList = bigDataService.searchComprehensiveList(BigDataComprehensiveReq.build(entities, cStore,
                    context.getUnionId(), tracer.currentSpan().context().traceIdString(), aolaiList));
            log.info("screenRecommendProductList bigdata查询 end");
            if (comprehensiveList.getRight() == 0) {
                page.setCount(entities.size());
                page.setPages(entities.size() % page.getPageSize() == 0 ?
                        entities.size() / page.getPageSize() :
                        entities.size() / page.getPageSize() + 1);
                return entities.stream()
                        .skip((page.getPageNo() - 1) * page.getPageSize())
                        .limit(page.getPageSize())
                        .collect(Collectors.toList());
            }
            HashMap<Long, StoreGoodSpuResp> map = entities.stream()
                    .collect(HashMap::new, (k, v) -> k.put(Long.valueOf(v.getM_product_id()), v), HashMap::putAll);
            List<BigDataComprehensiveData> data = comprehensiveList.getLeft().stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());

            List<StoreGoodSpuResp> rets = new ArrayList<>();
            data.forEach(v -> {
                if (MapUtils.isEmpty(map) || !map.containsKey(v.getM_product_id())) {
                    return;
                }
                StoreGoodSpuResp goodsResp = map.get(v.getM_product_id());
                rets.add(goodsResp);
            });
            page.setCount(comprehensiveList.getLeft().size());
            page.setPages(comprehensiveList.getLeft().size() % page.getPageSize() == 0 ?
                    comprehensiveList.getLeft().size() / page.getPageSize() :
                    comprehensiveList.getLeft().size() / page.getPageSize() + 1);
            return rets;
        } else {
            // 将数据再查询一遍库存信息
            entities = queryStockByInfo(entities, true, context.getStoreId());
            return entities;
        }

    }


    @Autowired
    private ProductListExternalMapper productListExternalMapper;


    private List<String> searchInBigData(StoreGoodsReq context, CStore cStore) {
        BigDataSearchReqEntity reqEntity = new BigDataSearchReqEntity();
        // 1、江南布衣+：全品牌
        // 2、单品牌
        // 3、正常集合店：看集合店下什么品牌
        // 4、奥莱：区分单店||集合店【不一定全品牌】
        // 集合店 + 江南布衣+ + 奥莱 都是传品牌数组 看子店具体是哪些品牌
        List<Long> list = Arrays.stream(jingxiaoAolaiData.split(",")).map(s -> Long.parseLong(s)).collect(Collectors.toList());
        Boolean isAolai = (Objects.equals(cStore.getCCustomerId(), 176L) && Objects.equals(cStore.getDefault05(), "是"))
                || (!Objects.equals(cStore.getCCustomerId(), 176L) && list.contains(cStore.getCStoreAttrib9Id()));

        if (isAolai) {
            reqEntity.setBrand(Lists.newArrayList("samo"));
            if (cStore.getCUnionstoreId() != null) {
                List<CStore> cStoreList = cStoreMapper.getByCUnionstoreIdList(Lists.newArrayList(cStore.getCUnionstoreId()));
                if (CollectionUtils.isEmpty(cStoreList)) {
                    return Collections.emptyList();
                }
                List<Long> cArcBrandIds = cStoreList.stream().map(CStore::getCArcbrandId).collect(Collectors.toList());
                reqEntity.setSubBrand(BigDataBjBrandEnum.getByCode(cArcBrandIds));
            } else {
                reqEntity.setSubBrand(BigDataBjBrandEnum.getByCode(Lists.newArrayList(cStore.getCArcbrandId())));
            }
        } else {
            // 判断是否是江南布衣+ 如果是 传江南布衣加 不是则传单品牌
            List<Long> cArcBrandIds = new ArrayList<>();
            if (cStore.getCUnionstoreId() == null) {
                cArcBrandIds = Lists.newArrayList(cStore.getCArcbrandId());
                reqEntity.setBrand(BigDataBjBrandEnum.getByCode(cArcBrandIds));
            } else {
                List<CStore> cStoreList = cStoreMapper.getByCUnionstoreIdList(Lists.newArrayList(cStore.getCUnionstoreId()));
                if (CollectionUtils.isEmpty(cStoreList)) {
                    return Collections.emptyList();
                }
                CStore mainStore = cStoreList.stream().filter(v -> Objects.equals(v.getId(), cStore.getCUnionstoreId()))
                        .findFirst().orElse(null);
                // 如果是67 则是江南布衣加
                if (Objects.equals(mainStore.getCArcbrandId(), 67L)) {
                    reqEntity.setBrand(Lists.newArrayList("lasumin"));
                } else {
                    cArcBrandIds = cStoreList.stream().map(CStore::getCArcbrandId).collect(Collectors.toList());
                    reqEntity.setBrand(BigDataBjBrandEnum.getByCode(cArcBrandIds));
                }
            }

        }

        // 临时解决宣传物料配错品牌的问题 宣传物料支持无品牌搜索
        Object o = redisService.get(RedisKeyConstant.REDIS_PROMOTIONAL_MATERIALS_KEY);
        List<String> xcNames = new ArrayList<>();
        if (o == null) {
            List<String> validNames = productListExternalMapper.selectValidNames();
            if (CollectionUtils.isNotEmpty(validNames)) {
                String names = validNames.stream().collect(Collectors.joining(","));
                redisService.set(RedisKeyConstant.REDIS_PROMOTIONAL_MATERIALS_KEY, names, 86400);
                xcNames = validNames;
            }

        } else {
            xcNames = Arrays.stream(StringUtils.split(Objects.toString(o), ",")).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(context.getKeywords()) && context.getKeywords().length() >= 10) {
            String name = context.getKeywords().substring(0, 10);
            if (xcNames.contains(name)) {
                reqEntity.setBrand(Lists.newArrayList("all"));
                reqEntity.setSubBrand(new ArrayList<>());
            }
        }


        // 传全品牌 不按品牌门店来
       /* List<Long> cArcBrandIds = Arrays.stream(BigDataBjBrandEnum.values()).map(BigDataBjBrandEnum::getCode).collect(Collectors.toList());

        reqEntity.setBrand(BigDataBjBrandEnum.getByCode(cArcBrandIds));*/
        reqEntity.setChannel_dict(BigDataSearchChannelEnum.POS.getCode());
        reqEntity.setQuery(context.getKeywords());
        reqEntity.setRequestId(BigDataSearchChannelEnum.POS.getDesc() + System.currentTimeMillis());
        reqEntity.setTraceId(tracer.currentSpan().context().traceIdString());
        reqEntity.setWeid(context.getWeid());
        reqEntity.setRequestScenes(context.getRequestScenes());
        if (CollectionUtils.isNotEmpty(context.getSubBrandId())) {
            reqEntity.setSubBrandId(context.getSubBrandId());
        }
        if (StringUtils.isNotBlank(context.getWid())) {
            reqEntity.setWid(context.getWid());
        }
        // 默认就取800条
        reqEntity.setList_size(800);
        reqEntity.setList_start(0);

        List<BigDataSearchRespEntity> entities = bigDataService.searchByText(reqEntity);

        return entities.stream().map(v -> Objects.toString(v.getSpu_id())).collect(Collectors.toList());
    }

    private List<StoreGoodSpuResp> queryStockByInfo(List<StoreGoodSpuResp> entities, Boolean isList, List<String> storeIds) {
          if (CollectionUtils.isEmpty(entities)) {  
               return entities;
          }

          List<EbAndWscSpuStockEntity> ebAndWscSpuStorages = proStorageQtyMapper.getEbAndWscSpuStorages(
          entities.stream().map(StoreGoodSpuResp::getM_product_id).collect(Collectors.toList()),
                  storeIds.stream().map(v -> Long.valueOf(v)).collect(Collectors.toList()));
          if (CollectionUtils.isEmpty(ebAndWscSpuStorages)) { 
              return entities;
          } else {
              List<StoreGoodSpuResp> rets = new ArrayList<>();
              List<String> names = new ArrayList<>();
              HashMap<Long, EbAndWscSpuStockEntity> map = ebAndWscSpuStorages.stream().collect(HashMap::new,
                      (k, v) -> k.put(v.getProductId(), v), HashMap::putAll);
              entities.forEach(v -> {
                  if (map != null && map.get(v.getM_product_id()) != null) {
                      v.setEb_qty(map.get(v.getM_product_id()).getEbQty());
                      v.setMall_qty(map.get(v.getM_product_id()).getMallQty());
                      v.setQty(map.get(v.getM_product_id()).getQty());
                  }
                  if (!names.contains(v.getName())) {
                      rets.add(v);
                      names.add(v.getName());
                  }
                  // 搜索走大数据后 不过滤
                  /*if (isList) {
                      if (!(v.getQty() == 0) || !(v.getEb_qty() == 0) || !(v.getMall_qty() == 0)) {
                          rets.add(v);
                      }
                  } else {
                      rets.add(v);
                  }*/
              });
              return rets;
          }
    }

    private List<StoreGoodSpuResp> querySpuByName(String name, StoreGoodsReq storeGoodsReq, Page page){
        QueryGoodsReq req = new QueryGoodsReq();
        req.setName(name);
        List<GoodSpuResp> spuResps = iProductService.searchGoods(req,new Page(1,100));
        if (spuResps.isEmpty()) return new ArrayList<>();
        Map<String, List<GoodSpuResp>> collect = spuResps.stream().sorted(Comparator.comparing(GoodSpuResp::getId).reversed())
                .collect(Collectors.groupingBy(BaseProductResp::getName));
        if (CollectionUtils.isEmpty(collect)) return new ArrayList<>();

        List<GoodSpuResp> rets = new ArrayList<>();
        collect.entrySet().forEach(v -> {
            rets.add(v.getValue().get(0));
        });


        List<StoreGoodSpuResp> entities = rets.stream().map(item -> {
            StoreGoodSpuResp storeGoodSpuEntity = new StoreGoodSpuResp();
            BeanUtils.copyProperties(item, storeGoodSpuEntity);
            storeGoodSpuEntity.setM_product_id(item.getId());
            if (CollectionUtils.isNotEmpty(storeGoodsReq.getStoreId())) {
                // 存放有库存的商品
                storeGoodsReq.getStoreId().forEach(v -> {
                    List<SpuStockEntity> spuStock = stockRepository.selectBySpuAndStoreIds(Arrays.asList(item.getId()), Arrays.asList(Long.valueOf(v)));
                    if (CollectionUtils.isNotEmpty(spuStock)) {
                        storeGoodSpuEntity.setC_store_id(Long.valueOf(v));
                        return;
                    }
                });
                if (storeGoodSpuEntity.getC_store_id() == 0) {
                    storeGoodSpuEntity.setC_store_id(Long.valueOf(storeGoodsReq.getStoreId().get(0)));
                }
            }
            return storeGoodSpuEntity;
        }).collect(Collectors.toList());

        return queryStockByInfo(entities, false, storeGoodsReq.getStoreId());
    }


    private List<StoreGoodSpuResp> querySpuByMhName(String keyWord, StoreGoodsReq storeGoodsReq, Page page){
        QueryGoodsReq req = new QueryGoodsReq();
        req.setValue(keyWord);
        List<GoodSpuResp> spuResps = iProductService.searchGoods(req,new Page(1,100));
        if (spuResps.isEmpty()) return new ArrayList<>();
        Map<String, List<GoodSpuResp>> collect = spuResps.stream().sorted(Comparator.comparing(GoodSpuResp::getId).reversed())
                .collect(Collectors.groupingBy(BaseProductResp::getName));
        if (CollectionUtils.isEmpty(collect)) return new ArrayList<>();

        List<GoodSpuResp> rets = new ArrayList<>();
        collect.entrySet().forEach(v -> {
            rets.add(v.getValue().get(0));
        });


        List<StoreGoodSpuResp> entities = rets.stream().map(item -> {
            StoreGoodSpuResp storeGoodSpuEntity = new StoreGoodSpuResp();
            BeanUtils.copyProperties(item, storeGoodSpuEntity);
            storeGoodSpuEntity.setM_product_id(item.getId());
            if (CollectionUtils.isNotEmpty(storeGoodsReq.getStoreId())) {
                // 存放有库存的商品
                storeGoodsReq.getStoreId().forEach(v -> {
                    List<SpuStockEntity> spuStock = stockRepository.selectBySpuAndStoreIds(Arrays.asList(item.getId()), Arrays.asList(Long.valueOf(v)));
                    if (CollectionUtils.isNotEmpty(spuStock)) {
                        storeGoodSpuEntity.setC_store_id(Long.valueOf(v));
                        return;
                    }
                });
                if (storeGoodSpuEntity.getC_store_id() == 0) {
                    storeGoodSpuEntity.setC_store_id(Long.valueOf(storeGoodsReq.getStoreId().get(0)));
                }
            }
            return storeGoodSpuEntity;
        }).collect(Collectors.toList());

        return queryStockByInfo(entities, false, storeGoodsReq.getStoreId());
    }

    @Override
    public List<ProductSkcResp> searchMallProductSkc(QueryGoodsSkcListReq goodsReq, Page page) {
        SearchRequest request = new SearchRequest();
        request.indices(MALL_PRODUCT_SKC_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        goodsReq.setStoreId(null);
        buildQueryMall(queryBuilder, goodsReq);
        if (goodsReq.getWeiMenProductId() == 1) {
            queryBuilder.must(QueryBuilders.termQuery("mall_isactive", 1));
        }
        if (goodsReq.getWeid() != null && goodsReq.getWeid() > 0){
            queryBuilder.must(QueryBuilders.termQuery("mall_weid", goodsReq.getWeid()));
        }
        sourceBuilder.fetchSource(new String[]{"name","value","price","id", "sales_num","colorno", "color_name",
                "imgurl","cover_imgs","detail_imgs","product_id","brand", "skus","year",
                "small_season_id","small_season","skc_code"
                , "weimo_product_id", "c_arcbrand_id","element", "product_tag_list"}, new String[]{});
        sourceBuilder.query(queryBuilder);
        // 使用主分片查询
        if(page.getPageNo()>1){
            request.preference("primary");
        }
        sourceBuilder.from((page.getPageNo() - 1)*page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        //排序先按照score排序
        if(StringUtils.isNotBlank(goodsReq.getName())){
            sourceBuilder.sort("_score",SortOrder.DESC);
        }
        if (goodsReq.getSorted() == 2 || (StringUtils.isBlank(goodsReq.getName()) && goodsReq.getSorted() == 3)){
            sourceBuilder.sort("sales_num", SortOrder.DESC);
        }
        if (goodsReq.getSorted() == 1){
            sourceBuilder.sort("year", SortOrder.DESC).sort("m_band_id", SortOrder.DESC);
        }
        request.source(sourceBuilder);
        List<ProductSkcResp> entities = new ArrayList<>();
        searchEsProductSkc(entities, request, goodsReq, page);
        return entities;
    }

    @Override
    public List<ProductSkcResp> searchMallProductSpu(QueryGoodsSkcListReq goodsReq, Page page) {
        SearchRequest request = new SearchRequest();
        request.indices(MALL_PRODUCT_SPU_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        goodsReq.setStoreId(null);
        buildQueryMallSpu(queryBuilder, goodsReq);
        if (goodsReq.getWeiMenProductId() != null && goodsReq.getWeiMenProductId() == 1) {
            queryBuilder.must(QueryBuilders.termQuery("mall_isactive", 1));
        }
        if (goodsReq.getWeid() != null && goodsReq.getWeid() > 0){
            queryBuilder.must(QueryBuilders.termQuery("mall_weid", goodsReq.getWeid()));
        }
        sourceBuilder.fetchSource(new String[]{"name","value","price","id", "sales_num",
                "imgurl","cover_imgs","detail_imgs","product_id","brand","year",
                "small_season_id","small_season", "weimo_product_id", "c_arcbrand_id","element", "m_band_id",
                "product_tag_list"}, new String[]{});
        sourceBuilder.query(queryBuilder);
        // 使用主分片查询
        if(page.getPageNo()>1){
            request.preference("primary");
        }
        sourceBuilder.from((page.getPageNo() - 1)*page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        //排序先按照score排序
        if(StringUtils.isNotBlank(goodsReq.getName())){
            sourceBuilder.sort("_score",SortOrder.DESC);
        }
        if (goodsReq.getSorted() == 2 || (StringUtils.isBlank(goodsReq.getName()) && goodsReq.getSorted() == 3)){
            sourceBuilder.sort("sales_num", SortOrder.DESC);
        }
        if (goodsReq.getSorted() == 1){
            sourceBuilder.sort("year", SortOrder.DESC).sort("m_band_id", SortOrder.DESC);
        }
        request.source(sourceBuilder);
        List<ProductSkcResp> entities = new ArrayList<>();
        searchEsProductSkc(entities, request, goodsReq, page);
        return entities;
    }

    @Override
    public List<ProductVidSkcResp> queryVidMallGoodSkc(QueryGoodsSkcListReq goodsReq, Page page) {
        if (StringUtils.isEmpty(goodsReq.getStoreId())) {
            throw new RuntimeException("店仓信息不能为空");
        }
        // 接入算法
        if (StringUtils.isNotBlank(goodsReq.getName())) {
            // 陪逛搭配师后台也是用box 后续用requestSource区分
            List<Long> bigDataProductIds = searchFashionerBigDataSearch(goodsReq, BigDataSearchChannelEnum.WSC_FASHIONER);
            page.setCount(bigDataProductIds.size());

            if (CollectionUtils.isEmpty(bigDataProductIds)) {
                return Collections.emptyList();
            }

            // 处理商品id 如果是home 补全商品id
            if (goodsReq.getName().length() >= 9) {
                QueryWrapper<MProduct> wrapper = new QueryWrapper<>();
                wrapper.eq("M_PRODUCT_ORIG", goodsReq.getName().substring(0, 9));
                List<MProduct> mProducts = mProductMapper.selectList(wrapper);
                if (CollectionUtils.isNotEmpty(mProducts)) {
                    bigDataProductIds.addAll(mProducts.stream().map(MProduct::getId).collect(Collectors.toList()));
                    bigDataProductIds.stream().distinct().collect(Collectors.toList());
                }
            }
            if (CollectionUtils.isNotEmpty(goodsReq.getIncludeProductIds())) {

                List<Long> jjList = getJjList(bigDataProductIds, goodsReq.getIncludeProductIds());
                if (CollectionUtils.isEmpty(jjList)) {
                    return Collections.emptyList();
                }
                goodsReq.setIncludeProductIds(jjList);
            } else {

                goodsReq.setIncludeProductIds(bigDataProductIds);
            }
        }

        // 查询当前门店属于什么品牌
        String weId = jicMallOrgMapper.selectWeIdByVid(goodsReq.getStoreId());
        Map<String, String> map = JSONObject.parseObject(vidIndexMode, Map.class);
        String index = map.get(weId);
        if (StringUtils.isBlank(index)) {
            throw new RuntimeException("未找到当前门店的品牌");
        }

        SearchRequest request = new SearchRequest();
        request.indices(StringUtils.replace(vidMallGoodsSkcIndex,"mode", index));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        buildVidQuery(queryBuilder, goodsReq);
        sourceBuilder.fetchSource(new String[]{"name","qty","value","price","id", "sales_num","colorno", "color_name",
                        "vid","imgurl","m_product_id","brand", "skus","year",
                        "small_season_id","small_season","name_text","skc_code","skc_code1"
                        , "c_arcbrand_id","m_big_category_id",
                        "m_small_category_id","small_season","m_small_category","m_big_category","m_band"},
                new String[]{});
        sourceBuilder.query(queryBuilder);
        // 使用主分片查询
        if(page.getPageNo() > 1){
            request.preference("primary");
        }
        sourceBuilder.from((page.getPageNo() - 1) * page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        //排序先按照score排序
        if(StringUtils.isNotBlank(goodsReq.getName())){
            sourceBuilder.sort("_score", SortOrder.DESC);
        }
        // 3是啥？
        if (goodsReq.getSorted() == 2 || (StringUtils.isBlank(goodsReq.getName()) && goodsReq.getSorted() == 3)){
            sourceBuilder.sort("sales_num", SortOrder.DESC)
                    .sort("year", SortOrder.DESC)
                    .sort("m_band_id", SortOrder.DESC)
                    .sort("colorno.keyword", SortOrder.ASC)
                    .sort("m_product_id", SortOrder.DESC);
        }
        if (goodsReq.getSorted() == 1){
            sourceBuilder.sort("year", SortOrder.DESC)
                    .sort("m_band_id", SortOrder.DESC)
                    .sort("m_product_id", SortOrder.DESC);;
        }
        request.source(sourceBuilder);

        List<ProductVidSkcResp> entities = new ArrayList<>();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("1 查询ES花费时间");
        searchEsProductVidSkc(entities, request, goodsReq, page);
        stopWatch.stop();

        stopWatch.start("2 查询WEID花费时间");
        goodsReq.setWeid(Long.valueOf(weId));
        stopWatch.stop();
        // 对数据进行处理 塞微盟goodId和微盟skuId
        stopWatch.start("3 塞微盟goodId和微盟skuId时间");
        entities = dealProductExchangeInfo(entities, weId);
        stopWatch.stop();

        // 根据款号查询商品未果处理
        entities = dealNotValidBySearchName(goodsReq, entities, page);

        // 返回数据id的处理
        if (CollectionUtils.isNotEmpty(entities)) {
            entities.forEach(v -> v.setId(Objects.toString(v.getM_product_id())));
        }
        return entities;
    }

    private List<ProductVidSkcResp> dealNotValidBySearchName(QueryGoodsSkcListReq goodsReq, List<ProductVidSkcResp> entities, Page page) {
        if (StringUtils.isNotBlank(goodsReq.getName()) && CollectionUtils.isEmpty(entities)) {
            // 判断当前商品在微盟是否上架且可售
            Boolean isCanSell = judgeIsCanSell(goodsReq.getName(), goodsReq.getStoreId(), goodsReq.getWeid());
            if (!isCanSell) {
                return Collections.emptyList();
            }

            // 查询当前款有的颜色
            List<BoxMProduct> mProducts = boxMProductMapper.findByName(goodsReq.getName());
            if (CollectionUtils.isEmpty(mProducts)) {
                return Collections.emptyList();
            }
            List<String> colorIds = mProducts.stream().map(BoxMProduct::getColorno).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(colorIds)) {
                return Collections.emptyList();
            }

            page.setCount(1);
            List<ProductVidSkcResp> finalEntities = entities;
            colorIds.forEach(v -> {
                // 获取商品的sku信息
                SearchRequest requestSkc = new SearchRequest();
                requestSkc.indices(storeProductSkcIndex);
                SearchSourceBuilder sourceBuilderSkc = new SearchSourceBuilder();
                BoolQueryBuilder queryBuilderSkc = QueryBuilders.boolQuery();
                queryBuilderSkc.filter(QueryBuilders.termQuery("skc_code", goodsReq.getName() + v));

                sourceBuilderSkc.fetchSource(new String[]{"name","qty","value","price","id", "sales_num","colorno", "color_name",
                                "vid","imgurl","m_product_id","brand", "skus","year",
                                "small_season_id","small_season","name_text","skc_code","skc_code1"
                                , "c_arcbrand_id","m_big_category_id", "cover_imgs",
                                "m_small_category_id","small_season","m_small_category","m_big_category","m_band"},
                        new String[]{});
                sourceBuilderSkc.query(queryBuilderSkc);
                // 使用主分片查询
                requestSkc.preference("primary");
                sourceBuilderSkc.from(0);
                sourceBuilderSkc.size(1);
                ProductSkcResp entity = null;
                try {
                    requestSkc.source(sourceBuilderSkc);
                    SearchResponse response = esUtil.search(requestSkc);
                    if (response.getHits().getTotalHits().value == 0){
                        return;
                    }
                    SearchHit[] hits = response.getHits().getHits();
                    entity = ProductSkcResp.fromJson(hits[0].getSourceAsString(), ProductSkcResp.class);
                    entity.setId(hits[0].getId());
                    entity.buildSku();
                } catch (IOException e) {
                    log.error("dealNotInVidSkcEs -查询商品PRODUCT_SKC 异常e = {}", e.getMessage());
                    return;
                }

                // 添加vid-SKu
                if (entity == null) {
                    return;
                }

                ProductVidSkcResp skcResp = new ProductVidSkcResp();
                BeanUtils.copyProperties(entity, skcResp);
                if (CollectionUtils.isNotEmpty(entity.getSkus())) {
                    List<ProductVidSkcResp.Sku> skus = new ArrayList<>();
                    entity.getSkus().forEach(x -> {
                        ProductVidSkcResp.Sku sku = new ProductVidSkcResp.Sku();
                        BeanUtils.copyProperties(x, sku);
                        skus.add(sku);
                    });
                    skcResp.setSkus(skus);
                }
                skcResp.setId(entity.getName() + entity.getColorno() + "-" + goodsReq.getStoreId());
                skcResp.setSkc_code(entity.getName() + entity.getColorno());
                skcResp.setSkc_code1(entity.getName() + entity.getColorno());
                skcResp.setVid(goodsReq.getStoreId());
                skcResp.setIs_display(1);
                if (CollectionUtils.isEmpty(skcResp.getSkus())){
                    return;
                }
                List<String> skus = skcResp.getSkus().stream().map(ProductVidSkcResp.Sku::getId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(skus)) {
                    return;
                }
                // 库存默认给 1
                skcResp.setQty(1);
                skcResp.setWeId(goodsReq.getWeid());
                skcResp.getSkus().forEach(x -> {
                        x.setQty("1");
                });
                finalEntities.add(skcResp);

            });
            storeProductEsScriptService.insertSkcInfoProduct(finalEntities);
        }

        // 转换数据
        entities = dealProductExchangeInfo(entities, Objects.toString(goodsReq.getWeid()));
        return entities;
    }

    private Boolean judgeIsCanSell(String name, String storeId, Long weid) {
        // -------------- 历史逻辑 --------------------
        /*// 获取当前商品的微盟goodId
        SearchWeiMoGoodInfoReq moGoodInfoReq = new SearchWeiMoGoodInfoReq();
        moGoodInfoReq.setName(name);
        moGoodInfoReq.setWeId(weid);
        List<SearchWeiMoGoodInfoResp> resps = productService.selectWeiMoGoodIdByNames(Lists.newArrayList(moGoodInfoReq));
        if (CollectionUtils.isEmpty(resps)) {
            return false;
        }

        // 判断当前商品在微盟是否可售
        GetListByWeimoGoodIdsReq req = new GetListByWeimoGoodIdsReq();
        req.setVid(storeId);
        req.setBrandId(Objects.toString(weid));
        req.setGoodsIdList(Lists.newArrayList(resps.get(0).getWeiMoGoodId()));

        List<GetListByWeimoGoodIdsResp> info = weiMoService.getWeimoGoodIdsInfo(req);
        if (CollectionUtils.isEmpty(info)) {
            return false;
        }

        boolean finalStatus = info.get(0).getIsCanSell() && info.get(0).getIsOnline();
        return finalStatus;*/

        // ----- 根据品牌查询当前商品是否可售----------
        Boolean isCanSell = false;
        List<JicProductMallInfo> jicProductMallInfos = jicProductMallInfoMapper.selectByNameAndWeId(name, weid);
        if (CollectionUtils.isNotEmpty(jicProductMallInfos) &&
                Objects.equals(jicProductMallInfos.get(0).getMallIsCanSell(), 1)) {
            isCanSell = true;
        }

        Boolean isPutaway = false;
        List<JicProductPutaway> jicProductPutways = jicProductPutwayMapper.selectByWeIdAndNameAndStore(weid, name, storeId);
        if (CollectionUtils.isNotEmpty(jicProductPutways) && Objects.equals(jicProductPutways.get(0).getPutway(), 0)) {
            isPutaway = true;
        }
        return isCanSell && isPutaway;
    }

    private List<ProductVidSkcResp> dealProductExchangeInfo(List<ProductVidSkcResp> entities, String weId) {
        if (CollectionUtils.isNotEmpty(entities)) {
            // 批量查询weimengGoodId
            List<SearchWeiMoGoodInfoReq> reqs = new ArrayList<>();
            entities.forEach(v -> {
                SearchWeiMoGoodInfoReq infoReq = new SearchWeiMoGoodInfoReq();
                infoReq.setName(v.getName());
                v.setWeId(Long.valueOf(weId));
                infoReq.setWeId(v.getWeId());
                reqs.add(infoReq);
            });
            if (CollectionUtils.isEmpty(reqs)) {
                return Collections.emptyList();
            }
            /*List<SearchWeiMoGoodInfoResp> resps = productService.selectWeiMoGoodIdByNames(reqs);
            if (CollectionUtils.isEmpty(resps)) {
                return Collections.emptyList();
            }
            HashMap<String, String> map = resps.stream().collect(HashMap::new, (k, v) -> k.put(v.getName(), v.getWeiMoGoodId()), HashMap::putAll);
            entities.stream().map(v -> {
                v.setWei_mo_product_id(map.get(v.getName()) != null ? Long.valueOf(map.get(v.getName())) : 0);
                return v;
            }).filter(x -> x.getWei_mo_product_id()!= 0).collect(Collectors.toList());*/
        }
        return entities;
    }

    @Override
    public void changeStoreGoodsByListenerVidStorage(Long storeId, Long productId, Long id, String type, String weId, Long skuId) {

        // 处理无storeId部分数据
        if (storeId == null || storeId == 0L) {
            JicStoreTypeEnum desc = JicStoreTypeEnum.getDesc(type);
            if (desc == null) {
                return;
            }
            List<String> storeIds = jicMallStoreWarehouseMapper.selectByTypeAndBrandId(desc.getDesc(), weId);
            if (CollectionUtils.isNotEmpty(storeIds)) {
                storeIds.stream().forEach(v -> {
                    if (StringUtils.isBlank(v)) {
                        return;
                    }
                    List<JicMallOrg> jicMallOrg = jicMallOrgMapper.selectVidByInsideId(Long.valueOf(v));
                    if (CollectionUtils.isNotEmpty(jicMallOrg)) {
                        jicMallOrg.forEach(
                                x -> dealVidProdInfo(Long.valueOf(v), productId, skuId, id, x)
                        );
                    }
                });
            }
        } else {
            // 修改有storeId部分的数据
            // --1、查询storeId 对应的vid
            List<JicMallOrg> jicMallOrgs = new ArrayList<>();
            Long storeIdL = 0L;
            if (type == "JX" ) {
                // 经销 需要用经销商的数据处理
                storeIdL = storeId;
                List<CStore> cStores = cStoreMapper.selectByCCustomerId(storeIdL);
                if (CollectionUtils.isEmpty(cStores)) {
                    return;
                }
                jicMallOrgs = jicMallOrgMapper.selectByVidCodeAndType(cStores.stream().map(CStore::getCode).collect(Collectors.toList()), weId);
            } else {
                // 直营 用门店信息的数据处理
                storeIdL = storeId;
                List<JicMallOrg> jicMallOrg = jicMallOrgMapper.selectVidByInsideId(storeId);
                if (CollectionUtils.isNotEmpty(jicMallOrg)) {
                    jicMallOrgs.addAll(jicMallOrg);
                }
            }

            if (CollectionUtils.isNotEmpty(jicMallOrgs)) {
                Long finalStoreIdL = storeIdL;
                jicMallOrgs.forEach(v -> {
                    dealVidProdInfo(finalStoreIdL, productId, skuId, id, v);
                });
            }

        }

    }

    @Override
    public Boolean exportVidProduct(ExportVidProductReq requestData) {
        String filePath = FileParseUtil.downLoadExcel(requestData.getUrl());
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {
            @Override
            protected void saveData() {
                log.info("==============导入门店商品数据");
                List<JicVidProductIsSellInfo> saveList = dealVidProductIsSellInfo(this.cachedDataList, requestData.getBrandId());
                // 如果数据不为空则保存
                if (CollectionUtils.isNotEmpty(saveList)) {
                    List<List<JicVidProductIsSellInfo>> partition = com.google.common.collect.Lists.partition(saveList, 1800);
                    partition.forEach(v -> {
                        jicVidProductIsSellInfoMapper.batchInsert(v);
                    });
                }
            }
        });
        return true;
    }

    @Override
    public void changeVidProductIsCanSell(Long isCanSellId, String brandId, String params) {
        // JSON序列化数据
        VidProductIsCanSellContext vidProductIsCanSellContext = new Gson().fromJson(params, new TypeToken<VidProductIsCanSellContext>() {}.getType());
        if (StringUtils.isBlank(vidProductIsCanSellContext.getMsg_body())) {
            return;
        }
        VidProductIsCanSellContext.IsCanSellProductData isCanSellProductData = new Gson()
                .fromJson(vidProductIsCanSellContext.getMsg_body(),
                        new TypeToken<VidProductIsCanSellContext.IsCanSellProductData>() {}.getType());

        // 转换成bojun商品信息
        List<BojunProductInfo> bojunProductInfos = new ArrayList<>();
        if (isCanSellProductData == null) {
            return;
        }
        if (CollectionUtils.isNotEmpty(isCanSellProductData.getGoodsIdList())) {
            bojunProductInfos = jicProductMallRelationMapper.exchangeBojunIdAndName(isCanSellProductData.getGoodsIdList());
        }
        if (CollectionUtils.isEmpty(bojunProductInfos)) {
            return;
        }

        // 查询当前品牌的vid数据
        List<String> vids = jicMallOrgMapper.selectVidByWeidAndOnline(Long.valueOf(brandId));
        if (CollectionUtils.isEmpty(vids)) {
            return;
        }
        // 更新数据库
        List<String> names = bojunProductInfos.stream().map(BojunProductInfo::getName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(names) || CollectionUtils.isEmpty(vids)) {
            return;
        }

        // 全部用原款号处理
        names = mProductMapper.selectNameByOrigName(names);

        List<JicVidProductIsSellInfo> infos = jicVidProductIsSellInfoMapper.selectByNamesAndBrandIdAndVids(names, Long.valueOf(brandId), vids);
        // 有则更新；无则插入
        List<JicVidProductIsSellInfo> saveList = new ArrayList<>();
        List<JicVidProductIsSellInfo> updateList = new ArrayList<>();
        if (CollectionUtils.isEmpty(infos)) {
            List<String> finalNames = names;
            vids.forEach(v -> {
                finalNames.forEach(x -> {
                    JicVidProductIsSellInfo info = new JicVidProductIsSellInfo();
                    info.setId(IdLeaf.getDateId(jicVidProductIdLeaf));
                    info.setCreateTime(new Date());
                    info.setVid(v);
                    info.setSpu(x);
                    info.setUpdateTime(new Date());
                    info.setBrandId(Long.valueOf(brandId));
                    info.setIsCanSell(Integer.valueOf(isCanSellProductData.getIsCanSell()));
                    info.setIsPutaway(0);
                    saveList.add(info);
                });
            });
        } else {
            List<String> finalNames1 = names;
            vids.forEach(v -> {
                finalNames1.forEach(x -> {
                    JicVidProductIsSellInfo sellInfo = infos.stream()
                            .filter(k -> Objects.equals(v, k.getVid()) && Objects.equals(x, k.getSpu()))
                            .findFirst().orElse(null);
                    if (sellInfo == null) {
                        JicVidProductIsSellInfo info = new JicVidProductIsSellInfo();
                        info.setId(IdLeaf.getDateId(jicVidProductIdLeaf));
                        info.setCreateTime(new Date());
                        info.setVid(v);
                        info.setSpu(x);
                        info.setUpdateTime(new Date());
                        info.setBrandId(Long.valueOf(brandId));
                        info.setIsCanSell(Integer.valueOf(isCanSellProductData.getIsCanSell()));
                        info.setIsPutaway(0);
                        saveList.add(info);
                    } else {
                        sellInfo.setIsCanSell(Integer.valueOf(isCanSellProductData.getIsCanSell()));
                        updateList.add(sellInfo);
                    }
                });
            });
        }
        if (CollectionUtils.isNotEmpty(saveList)) {
            List<List<JicVidProductIsSellInfo>> partition = com.google.common.collect.Lists.partition(saveList, 1000);
            partition.forEach(v -> {
                jicVidProductIsSellInfoMapper.batchInsert(v);
            });

        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<List<JicVidProductIsSellInfo>> partition = com.google.common.collect.Lists.partition(updateList, 1000);
            partition.forEach(v -> {
                jicVidProductIsSellInfoMapper.batchUpdate(v);
            });
        }

        // 如果是下架逻辑则修改es
        if (Objects.equals(isCanSellProductData.getIsCanSell(), "0")) {
            updateVidEsToDown(vids, names, isCanSellId, brandId);
        }
    }

    private void updateVidEsToDown(List<String> vids, List<String> names, Long isCanSellId, String brandId) {
        Map<String, String> map = JSONObject.parseObject(vidIndexMode, Map.class);
        String index = map.get(brandId);
        UpdateByQueryRequest request = new UpdateByQueryRequest(StringUtils.replace(vidMallGoodsSkcIndex,"mode", index));
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(QueryBuilders.termsQuery("vid", vids));
        queryBuilder.must(QueryBuilders.termsQuery("name", names));

        request.setQuery(queryBuilder);
        HashMap<String, Object> params = new HashMap<>(1);
        params.put("down", 0);

        Script script = new Script(ScriptType.INLINE, "painless",
                "ctx._source['is_display'] = params.down", params);
        request.setScript(script);
        try {
            esUtil.updateByQuery(request);
        } catch (IOException e) {
            log.error("=============dealInVidSkcEs更新vid上下架报错:{}", isCanSellId);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void changeVidProductIsPutaway(Long isPutaway, String brandId, String params, String event) {
        // JSON序列化数据
        VidProductIsPutawayContext context = new Gson().fromJson(params, new TypeToken<VidProductIsPutawayContext>() {}.getType());
        if (StringUtils.isBlank(context.getMsgBody())) {
            return;
        }

        // 参数
        Boolean isOnline = false;
        List<Long> goodList = new ArrayList<>();
        List<String> vidList = new ArrayList<>();
        if (Objects.equals(event, "onlineStatusUpdate")) {
            // 上下架逻辑
            VidProductIsPutawayContext.IsPutawayProductData putawayProductData = new Gson()
                    .fromJson(context.getMsgBody(),
                            new TypeToken<VidProductIsPutawayContext.IsPutawayProductData>() {}.getType());
            if (putawayProductData == null) {
                return;
            }
            isOnline = putawayProductData.getIsOnline();
            goodList = putawayProductData.getGoodsIdList();
            vidList = Lists.newArrayList(putawayProductData.getVid());
        } else if (Objects.equals(event, "goodsStoreRelationUpdate")) {
            // 商品上下架逻辑
            VidProductIsPutawayContext.IsPutawayProductRelationData putawayProductRelationData = new Gson()
                    .fromJson(context.getMsgBody(),
                            new TypeToken<VidProductIsPutawayContext.IsPutawayProductRelationData>() {}.getType());
            if (putawayProductRelationData == null) {
                return;
            }
            isOnline = putawayProductRelationData.getIsAssigned() ? putawayProductRelationData.getIsOnline() : false;
            goodList = putawayProductRelationData.getGoodsIdList();
            List<String> finalVidList = vidList;
            if (CollectionUtils.isEmpty(putawayProductRelationData.getVidList())) {
                return;
            }
            putawayProductRelationData.getVidList().forEach(v -> finalVidList.add(Objects.toString(v)));
        }

        List<BojunProductInfo> bojunProductInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(goodList)) {
            bojunProductInfos = jicProductMallRelationMapper.exchangeBojunIdAndName(goodList);
        }
        if (CollectionUtils.isEmpty(bojunProductInfos)) {
            return;
        }

        List<String> names = bojunProductInfos.stream().map(BojunProductInfo::getName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(names) || CollectionUtils.isEmpty(vidList)) {
            return;
        }

        // 全部用原款号处理
        names = mProductMapper.selectNameByOrigName(names);

        List<JicVidProductIsSellInfo> infos = jicVidProductIsSellInfoMapper.selectByNamesAndBrandIdAndVids(names, Long.valueOf(brandId), vidList);
        // 有则更新；无则插入
        List<JicVidProductIsSellInfo> saveList = new ArrayList<>();
        List<JicVidProductIsSellInfo> updateList = new ArrayList<>();
        if (CollectionUtils.isEmpty(infos)) {
            List<String> finalNames = names;
            Boolean finalIsOnline = isOnline;
            vidList.forEach(v -> {
                finalNames.forEach(x -> {
                    JicVidProductIsSellInfo info = new JicVidProductIsSellInfo();
                    info.setId(IdLeaf.getDateId(jicVidProductIdLeaf));
                    info.setCreateTime(new Date());
                    info.setVid(v);
                    info.setSpu(x);
                    info.setUpdateTime(new Date());
                    info.setBrandId(Long.valueOf(brandId));
                    info.setIsPutaway(finalIsOnline ? 1 : 0);
                    info.setIsCanSell(0);
                    saveList.add(info);
                });
            });
        } else {
            List<String> finalNames1 = names;
            Boolean finalIsOnline1 = isOnline;
            vidList.forEach(v -> {
                finalNames1.forEach(x -> {
                    JicVidProductIsSellInfo sellInfo = infos.stream()
                            .filter(k -> Objects.equals(v, k.getVid()) && Objects.equals(x, k.getSpu()))
                            .findFirst().orElse(null);
                    if (sellInfo == null) {
                        JicVidProductIsSellInfo info = new JicVidProductIsSellInfo();
                        info.setId(IdLeaf.getDateId(jicVidProductIdLeaf));
                        info.setCreateTime(new Date());
                        info.setVid(v);
                        info.setSpu(x);
                        info.setUpdateTime(new Date());
                        info.setBrandId(Long.valueOf(brandId));
                        info.setIsPutaway(finalIsOnline1 ? 1 : 0);
                        info.setIsCanSell(0);
                        saveList.add(info);
                    } else {
                        sellInfo.setIsPutaway(finalIsOnline1 ? 1 : 0);
                        updateList.add(sellInfo);
                    }
                });
            });
        }
        if (CollectionUtils.isNotEmpty(saveList)) {
            List<List<JicVidProductIsSellInfo>> partition = com.google.common.collect.Lists.partition(saveList, 1000);
            partition.forEach(v -> {
                jicVidProductIsSellInfoMapper.batchInsert(v);
            });
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<List<JicVidProductIsSellInfo>> partition = com.google.common.collect.Lists.partition(updateList, 1000);
            partition.forEach(v -> {
                jicVidProductIsSellInfoMapper.batchUpdate(v);
            });
        }

        // 如果是下架逻辑则修改es
        if (!isOnline) {
            updateVidEsToDown(vidList, names, isPutaway, brandId);
        }

    }

    @Override
    public List<WeimoGoodShareLinkResp> getProductWeimoShareLink(CommonRequest<WeimoGoodShareLinkReq> request) {
        return weiMoService.getWeimoGoodShareLink(request.getRequestData());
    }

    @Override
    public List<StoreGoodSpuResp> getCouponGoodsListByStore(StoreCouponGoodsReq requestData, Page page) {
        if (StringUtils.isBlank(requestData.getCouponRuleId())) {
            log.error("券列表的券规则Id不能为空");
            throw new RuntimeException("券列表的券规则Id不能为空");
        }
        List<StoreGoodSpuResp> spuResps = dealEsCoupleData(requestData, page, new ArrayList<>(), 1);
        return spuResps;
    }

    // 根据规则id获取商品数据
    private List<String> dealDbCoupleData(String couponRuleId, Integer i) {
        Object o = redisService.get(RedisKeyConstant.REDIS_COUPLE_RULE_ID + couponRuleId);
        String sql = "";
        if (o == null) {
            sql = jicMallOrgMapper.selectCouponRuleByRuleId(couponRuleId);
            redisService.set(RedisKeyConstant.REDIS_COUPLE_RULE_ID + couponRuleId, sql, 86400);
        } else {
            sql = (String) o;
        }

        if (StringUtils.isBlank(sql)) {
            return Collections.emptyList();
        }
        String yyyyMMdd = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String prdListKey = RedisKeyConstant.COUPON_RULE_PAGE_DATA.concat(couponRuleId).concat(yyyyMMdd)
                .concat(Objects.toString(i)).concat("800");
        Object prdList = redisService.get(prdListKey);
        List<String> productIds = new ArrayList<>();
        if (prdList == null) {
            com.github.pagehelper.Page<String> hPage = PageHelper.startPage(i, 800);
            productIds = mProductMapper.selectByCouponRuleAndOtherParams(sql, Long.valueOf(yyyyMMdd));
            redisService.set(prdListKey, JSONObject.toJSONString(productIds), 86400);
        } else {
            productIds = JSONObject.parseArray(Objects.toString(prdList), String.class);
        }

        return productIds;
    }

    private List<StoreGoodSpuResp> dealEsCoupleData(StoreCouponGoodsReq requestData, Page page,
                                                    List<StoreGoodSpuResp> oldRet, Integer i) {
        // 查询当前条件是否有数据
        if (oldRet.size() >= page.getPageNo() * page.getPageSize()) {
            return  oldRet
                    .stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());
        }

        // 如果当前页数超过100页
        log.info("i=:{}", i);
        if (i > 10 || (page.getPages() != 0 && i > page.getPages())) {
            return oldRet
                    .stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());
        }

        List<String> productIds = dealDbCoupleData(requestData.getCouponRuleId(), i);
        if (CollectionUtils.isEmpty(productIds)) {
            return oldRet
                    .stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());
        }

        StoreGoodsReq req = new StoreGoodsReq();
        BeanUtils.copyProperties(requestData, req);
        Page newPage = new Page(1, 800);
        // 通过库存排序
        req.setSorted(4);
        req.setProductIds(productIds);
        List<StoreGoodSpuResp> storeGoodSpuResps = this.searchGoodsByStore(req, newPage);
        if (CollectionUtils.isEmpty(storeGoodSpuResps)) {
            return oldRet
                    .stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());
        } else {
            oldRet.addAll(storeGoodSpuResps);
        }
        return dealEsCoupleData(requestData, page, storeGoodSpuResps, i + 1);
    }


    private List<JicVidProductIsSellInfo> dealVidProductIsSellInfo(List<Map<Integer, String>> cachedDataList, Long brandId) {
        List<JicVidProductIsSellInfo> infos = new ArrayList<>();
        List<String> spus = new ArrayList<>();
        // 1、获取所有的款号
        for (Map<Integer,String> pLabel: cachedDataList) {
            if (StringUtils.isBlank(pLabel.get(0))) {
                break;
            }

            if (StringUtils.isNotBlank(pLabel.get(0))) {
                spus.add(pLabel.get(0));
            }
        }

        if (CollectionUtils.isEmpty(spus)) {
            return Collections.emptyList();
        }
        // 全部用原款号处理
        spus = mProductMapper.selectNameByOrigName(spus);

        // 2、获取所有的该品牌门店
        List<String> vids = jicMallOrgMapper.selectVidByWeidAndOnline(brandId);
        if (CollectionUtils.isEmpty(vids)) {
            return Collections.emptyList();
        }

        // 3、组装数据
        List<String> finalSpus = spus;
        vids.forEach(v -> {
            finalSpus.forEach(x -> {
                JicVidProductIsSellInfo info = new JicVidProductIsSellInfo();
                info.setId(IdLeaf.getDateId(jicVidProductIdLeaf));
                info.setCreateTime(new Date());
                info.setVid(v);
                info.setSpu(x);
                info.setUpdateTime(new Date());
                info.setBrandId(brandId);
                info.setIsCanSell(1);
                info.setIsPutaway(1);
                infos.add(info);
            });
        });
        return infos;
    }

    private void dealVidProdInfo(long storeId, long productId, long skuId, long id, JicMallOrg jicMallOrg) {
        List<WeiMoBStorage> sumWeiMoStorage = new ArrayList<>();
        // --2、查询对应关联的仓 找仓对应的数据
        List<JicMallStoreWarehouse> jicMallStoreWarehouses = jicMallStoreWarehouseMapper.selectByStoreCodeAndBrandId(jicMallOrg.getVidCode(), jicMallOrg.getBrandId());
        List<String> types = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(jicMallStoreWarehouses)) {
            types = jicMallStoreWarehouses.stream().map(JicMallStoreWarehouse::getWarehouseCode).collect(Collectors.toList());
        }
        List<WeiMoBStorage> externalStorage = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(types)) {
            externalStorage = weiMoBStorageMapper.selectByTypesAndProductIdAndBrandId(types, productId, jicMallOrg.getBrandId());
            sumWeiMoStorage.addAll(externalStorage);
        }

        // --3、查询门店改件商品的数据
        List<WeiMoBStorage> internalStorages = weiMoBStorageMapper.selectByStoreIdAndProductIdAndBrandId(productId, jicMallOrg.getBrandId(), storeId);
        sumWeiMoStorage.addAll(internalStorages);

        if (CollectionUtils.isEmpty(sumWeiMoStorage)) {
            return;
        }

        String vid = jicMallOrg.getVid();
        int skuQty = sumWeiMoStorage.stream().filter(v -> Objects.equals(v.getMProductAliasId(), skuId)).mapToInt(WeiMoBStorage::getQty).sum();


        // --4、查询对应vid下的productId去更新es
        // --4.1 查询是否存在该条件下的商品-返回商品的色号 有则更新 无则插入 暂时不考虑新增规格
        List<String> skuIds = searchInEsIsValid(vid, productId, skuId);


        if (CollectionUtils.isNotEmpty(skuIds)) {
            int sumQty = sumWeiMoStorage.stream().filter(v -> skuIds.contains(Objects.toString(v.getMProductAliasId())))
                    .mapToInt(WeiMoBStorage::getQty).sum();
            // 存在es
            dealInVidSkcEs(ProductVidSkcContext.build(vid, productId, skuId, sumQty, sumWeiMoStorage, skuQty, id));
        } else {
            // 不存在es
            dealNotInVidSkcEs(ProductVidSkcContext.build(vid, productId, skuId, 0, sumWeiMoStorage, skuQty, id));
        }

    }

    private void dealNotInVidSkcEs(ProductVidSkcContext context) {
        // 获取商品的sku信息
        SearchRequest request = new SearchRequest();
        request.indices(storeProductSkcIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.termQuery("m_product_id", context.getProductId()));
        queryBuilder.filter(QueryBuilders.nestedQuery("skus", new TermQueryBuilder("skus.id.keyword", context.getSkuId()), ScoreMode.None));

        sourceBuilder.fetchSource(new String[]{"name","qty","value","price","id", "sales_num","colorno", "color_name",
                        "vid","imgurl","m_product_id","brand", "skus","year",
                        "small_season_id","small_season","name_text","skc_code","skc_code1"
                        , "c_arcbrand_id","m_big_category_id",
                        "m_small_category_id","small_season","m_small_category","m_big_category","m_band"},
                new String[]{});
        sourceBuilder.query(queryBuilder);
        // 使用主分片查询
        request.preference("primary");
        sourceBuilder.from(0);
        sourceBuilder.size(1);
        ProductSkcResp entity = null;
        try {
            request.source(sourceBuilder);
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return;
            }
            SearchHit[] hits = response.getHits().getHits();
            entity = ProductSkcResp.fromJson(hits[0].getSourceAsString(), ProductSkcResp.class);
            entity.setId(hits[0].getId());
            entity.buildSku();
        } catch (IOException e) {
            log.error("dealNotInVidSkcEs -查询商品PRODUCT_SKC 异常e = {}", e.getMessage());
            return;
        }


        // 添加vid-SKu
        if (entity == null) {
            return;
        }

        ProductVidSkcResp skcResp = new ProductVidSkcResp();
        BeanUtils.copyProperties(entity, skcResp);
        if (CollectionUtils.isNotEmpty(entity.getSkus())) {
            List<ProductVidSkcResp.Sku> skus = new ArrayList<>();
            entity.getSkus().forEach(v -> {
                ProductVidSkcResp.Sku sku = new ProductVidSkcResp.Sku();
                BeanUtils.copyProperties(v, sku);
                skus.add(sku);
            });
            skcResp.setSkus(skus);
        }
        skcResp.setId(entity.getName() + entity.getColorno() + "-" + context.getVid());
        skcResp.setVid(context.getVid());
        skcResp.setIs_display(1);
        skcResp.setSkc_code(entity.getName() + entity.getColorno());
        skcResp.setSkc_code1(entity.getName() + entity.getColorno());
        if (CollectionUtils.isEmpty(skcResp.getSkus())){
            return;
        }
        List<String> skus = skcResp.getSkus().stream().map(ProductVidSkcResp.Sku::getId).collect(Collectors.toList());
        int sumQty = context.getSumWmStorages().stream().filter(v -> skus.contains(Objects.toString(v.getMProductAliasId())))
                .mapToInt(WeiMoBStorage::getQty).sum();
        skcResp.setQty(sumQty);

        // 如果sku不存在 则返回
        ProductVidSkcResp.Sku sku = skcResp.getSkus().stream().filter(v -> Objects.equals(v.getId(), Objects.toString(context.getSkuId())))
                .findFirst().orElse(null);
        if (sku == null) {
            return;
        }

        skcResp.getSkus().forEach(v -> {
            if (Objects.equals(v.getId(), Objects.toString(context.getSkuId()))) {
                v.setQty(Objects.toString(context.getSkuQty()));
            } else {
                v.setQty("0");
            }
        });

        IndexRequest indexRequest = new IndexRequest();
        indexRequest.index(vidMallGoodsSkcIndex).id(skcResp.getId());
        indexRequest.source(JSONObject.toJSONString(skcResp), XContentType.JSON);
        try {
            esUtil.index(indexRequest);
        }catch (Exception e){
            log.error("dealNotInVidSkcEs  插入es到vid-skc中  出错 ",e);
        }
    }

    private void dealInVidSkcEs(ProductVidSkcContext context) {
        UpdateByQueryRequest request = new UpdateByQueryRequest(vidMallGoodsSkcIndex);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(QueryBuilders.termQuery("vid", context.getVid()));
        queryBuilder.must(QueryBuilders.termQuery("m_product_id", context.getProductId()));
        queryBuilder.must(QueryBuilders.nestedQuery("skus", new TermQueryBuilder("skus.id.keyword", context.getSkuId()), ScoreMode.None));

        request.setQuery(queryBuilder);
        HashMap<String, Object> params = new HashMap<>(3);
        params.put("qty", context.getSumQty());
        params.put("skuQty", Objects.toString(context.getSkuQty()));
        params.put("id", Objects.toString(context.getSkuId()));
        Script script = new Script(ScriptType.INLINE, "painless",
                "ctx._source['qty'] = params.qty; " +
                        "if (ctx._source.skus != null)" +
                        "{for(e in ctx._source.skus)" +
                        "{if (e['id'] == params.id) " +
                        "{e['qty'] = params.skuQty;}}}", params);
        request.setScript(script);
        try {
            esUtil.updateByQuery(request);
        } catch (IOException e) {
            log.error("=============dealInVidSkcEs更新vid库存报错, 数据id" + context.getId());
            throw new RuntimeException(e);
        }
    }

    private List<String> searchInEsIsValid(String vid, long productId, long skuId) {
        SearchRequest request = new SearchRequest();
        request.indices(vidMallGoodsSkcIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.termQuery("vid", vid));
        queryBuilder.filter(QueryBuilders.termQuery("m_product_id", productId));
        queryBuilder.filter(QueryBuilders.nestedQuery("skus", new TermQueryBuilder("skus.id.keyword", skuId), ScoreMode.None));

        sourceBuilder.fetchSource(new String[]{"id", "skus.id"},
                new String[]{});
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(1);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        ProductVidSkcResp entity = null;
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return Collections.emptyList();
            }
            SearchHit[] hits = response.getHits().getHits();
            entity = ProductVidSkcResp.fromJson(hits[0].getSourceAsString(), ProductVidSkcResp.class);
            entity.buildSku();
            entity.setId(hits[0].getId());
        } catch (IOException e) {
            log.error("查询商品SPU-FAB-ES异常e = {}", e.getMessage());
            return Collections.emptyList();
        }
        return entity.getSkus().stream().map(ProductVidSkcResp.Sku::getId).collect(Collectors.toList());
    }

    private void buildVidQuery(BoolQueryBuilder queryBuilder, QueryGoodsSkcListReq context) {
        buildCommon(queryBuilder, context);
        if (StringUtils.isNotEmpty(context.getStoreId())){
            queryBuilder.filter(QueryBuilders.termQuery("vid", context.getStoreId()));
        }
        /*if (StringUtils.isNotBlank(context.getName())){
            context.setName(context.getName().trim());
            if(isChinese(context.getName())){
                queryBuilder.filter(QueryBuilders.matchQuery("name_text",context.getName()));
            }else{
                if(context.getName().length() > 12){
                    context.setName(context.getName().substring(0, 12));
                }
                queryBuilder.filter(QueryBuilders.matchPhrasePrefixQuery("skc_code1",context.getName()).maxExpansions(50));
            }
        }*/
        //款号 + 款色  in操作
        if (CollectionUtils.isNotEmpty(context.getProductCodes())){
            queryBuilder.filter(QueryBuilders.termsQuery("skc_code1.keyword", context.getProductCodes()));
        }
        NestedQueryBuilder nestedQuery = createNestedQuery(context);
        queryBuilder.filter(nestedQuery);
        if (CollectionUtils.isNotEmpty(context.getIncludeProductIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("m_product_id", context.getIncludeProductIds()));
        }
        if (CollectionUtils.isNotEmpty(context.getExcludeProductIds())){
            queryBuilder.mustNot(QueryBuilders.termsQuery("m_product_id", context.getExcludeProductIds()));
        }
        if (context.getProductId() != null){
            queryBuilder.filter(QueryBuilders.termQuery("m_product_id",context.getProductId()));
        }
        if (CollectionUtils.isNotEmpty(context.getExcludeNames())){
            queryBuilder.mustNot(QueryBuilders.termsQuery("name", context.getExcludeNames()));
        }
        queryBuilder.filter(QueryBuilders.rangeQuery("qty").gte(1));
        queryBuilder.filter(QueryBuilders.termQuery("is_display", 1));

        // 关于标签搜索
        if (CollectionUtils.isNotEmpty(context.getLabelCodes())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("labels", context.getLabelCodes().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script("1"));
            queryBuilder.must(termsSetQueryBuilder);
        }
    }

    private void buildQueryMallSpu(BoolQueryBuilder queryBuilder, QueryGoodsSkcListReq context) {
        buildCommon(queryBuilder, context);
        if (StringUtils.isNotEmpty(context.getStoreId())){
            queryBuilder.must(QueryBuilders.termQuery("store_id", context.getStoreId()));
        }
        if (StringUtils.isNotBlank(context.getName())){
            context.setName(context.getName().trim());
            if(isChinese(context.getName())){
                queryBuilder.must(QueryBuilders.matchQuery("name_text",context.getName()));
            }else{
                if(context.getName().length() > 12){
                    context.setName(context.getName().substring(0, 12));
                }
                queryBuilder.must(QueryBuilders.termsQuery("name",context.getName()));
            }
        }
        if (context.getLPrice() != null || context.getGPrice() != null){
            if (context.getLPrice() != null && context.getGPrice() != null){
                queryBuilder.must(QueryBuilders.rangeQuery("price").gte(context.getLPrice()).lte(context.getGPrice()));
            }

            if (context.getLPrice() != null && context.getGPrice() == null){
                queryBuilder.must(QueryBuilders.rangeQuery("price").gte(context.getLPrice()));
            }

            if (context.getLPrice() == null && context.getGPrice() != null){
                queryBuilder.must(QueryBuilders.rangeQuery("price").lte(context.getGPrice()));
            }
        }
        if (CollectionUtils.isNotEmpty(context.getIncludeProductIds())){
            queryBuilder.must(QueryBuilders.termsQuery("product_id", context.getIncludeProductIds()));
        }
        if (CollectionUtils.isNotEmpty(context.getExcludeProductIds())){
            queryBuilder.mustNot(QueryBuilders.termsQuery("product_id", context.getExcludeProductIds()));
        }
        if (context.getProductId() != null){
            queryBuilder.must(QueryBuilders.termQuery("product_id",context.getProductId()));
        }
    }
}
