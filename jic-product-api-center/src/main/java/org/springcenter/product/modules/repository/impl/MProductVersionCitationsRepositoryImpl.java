package org.springcenter.product.modules.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springcenter.product.modules.mapper.product.MProductVersionCitationsMapper;
import org.springcenter.product.modules.model.MProductVersion;
import org.springcenter.product.modules.model.MProductVersionCitations;
import org.springcenter.product.modules.repository.IMProductVersionCitationsRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/12/13 9:23
 */
@Service
public class MProductVersionCitationsRepositoryImpl implements IMProductVersionCitationsRepository {

    @Autowired
    private MProductVersionCitationsMapper mProductVersionCitationsMapper;
    @Override
    public Integer queryCitationsByModelNumber(MProductVersion mProductVersion) {
        QueryWrapper<MProductVersionCitations> query = new QueryWrapper<>();
        query.in("MODEL_NUMBER", mProductVersion.getModelNumber(), mProductVersion.getSampleNo());
        List<MProductVersionCitations> mProductVersionCitations = mProductVersionCitationsMapper.selectList(query);
        return CollectionUtils.isEmpty(mProductVersionCitations) ? 0 :
                mProductVersionCitations.stream().map(MProductVersionCitations::getCitations).reduce(0, Integer::sum);
    }
}
