package org.springcenter.product.modules.service.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.common.system.vo.SysCategoryModel;
import com.jnby.common.Page;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.common.util.excel.IWriteDataExcel;
import io.protostuff.StringSerializer;
import org.apache.commons.collections.MapUtils;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springcenter.product.api.dto.SysProductLabelResp;
import org.springcenter.product.api.dto.background.fab.ProductSpuFabBaseInfoEsResp;
import org.springcenter.product.modules.entity.BatchProductLabel;
import org.springcenter.product.modules.entity.ParseLabelEntity;
import org.springcenter.product.modules.mapper.product.SysHandworkProductLabelMapper;
import org.springcenter.product.modules.mapper.product.SysProductLabelMapper;
import org.springcenter.product.modules.model.SysProductLabel;
import org.springcenter.product.modules.service.IProductLabelService;
import org.springcenter.product.modules.service.IProductService;
import org.springcenter.product.modules.util.EsUtil;
import org.springcenter.product.modules.util.FileParseUtil;
import org.springcenter.product.api.dto.MarkingLabelReq;
import org.springcenter.product.api.dto.ProductSkcResp;
import org.springcenter.product.api.dto.QueryGoodsReq;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.rest.RestStatus;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import strman.Strman;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/8/13 11:09
 */
@Service
@Slf4j
@RefreshScope
public class ProductLabelServiceImpl extends ServiceImpl<SysProductLabelMapper, SysProductLabel> implements IProductLabelService {
    /*private static final String PRODUCT_SKC_INDEX = "index_jnby_res_prod_goods_skc";*/

    @Resource
    private SysProductLabelMapper sysProductLabelMapper;

    @Autowired
    private EsUtil esUtil;

    @Value("${es.index.goods}")
    private String goodsIndex;

    @Autowired
    private IProductService productService;

    @Value("${sys.category.parent.code}")
    private String categoryParentCode;

    @Value("${product.label.leaf.tag}")
    private String labelLeafTag;

    @Resource
    private ISysBaseAPI iSysBaseAPI;

    @Autowired
    private QiniuUtil qiniuUtil;

    @Autowired
    private RedisPoolUtil redisPoolUtil;

    @Value("${product.skc.index}")
    private String PRODUCT_SKC_INDEX;

    @Value("${product.spu.fab.base.info.index}")
    private String productSpuFabBaseInfoIndex;

    @Override
    public void markingProductLabel(MarkingLabelReq reqData, String userId) {
        try{
            // 删除前面数据
            boolean ifDelSpuLabel = delProductLabel(reqData);
            // 保存数据
            boolean ifInsertSpuLabel = insertProductLabel(reqData,userId);
            List<String> syncSkcCode = new ArrayList<>();
            if(ifDelSpuLabel || ifInsertSpuLabel){
                // 查询es
                QueryGoodsReq queryGoodsReq = new QueryGoodsReq();
                queryGoodsReq.setProductId(Long.valueOf(reqData.getProductId()));
                List<ProductSkcResp> skcProduct = productService.searchGoodsSkc(queryGoodsReq, new Page(), "");
                syncSkcCode = skcProduct.stream().map(ProductSkcResp::getId).collect(Collectors.toList());
            }else{
                syncSkcCode.add(reqData.getSkcCode());
            }
            //同步到SPU-es
            syncSpuLabelEs(reqData.getProductId());
            // 同步到SKC-es
            syncSkcLabel2Es(syncSkcCode, reqData.getSkcCode());
        }catch (Exception e){
            log.error("修改商品标签数据失败,productId={}",reqData.getProductId(),e);
        }

    }


    /**
     * 重置传入的skcCode对应的es数据
     * @param skcCode
     * @return
     * @throws IOException
     */
    private boolean syncSkcLabel2Es(List<String> skcCode, String oldSkcCode)throws IOException{
        // 查询数据库获取标签并重组
        QueryWrapper<SysProductLabel> wrapper = new QueryWrapper<>();
        wrapper.in("skc_code",skcCode);
        wrapper.eq("is_del",0L);
        List<SysProductLabel> list = this.list(wrapper);
        Map<String, List<SysProductLabel>> skcLabelMap = list.stream().collect(Collectors.groupingBy(SysProductLabel::getSkcCode));
        List<BatchProductLabel> batchProductLabelList = new ArrayList<>();
        for(String key : skcLabelMap.keySet()){
            List<SysProductLabel> sysProductLabels = skcLabelMap.get(key);
            List<String> labelList = sysProductLabels.stream().map(SysProductLabel::getLabelId).collect(Collectors.toList());
            List<String> labelLevel = new ArrayList<>();
            labelList.forEach(e -> {
                labelLevel.addAll(parseLabel(e));
            });
            BatchProductLabel batchProductLabel = new BatchProductLabel();
            batchProductLabel.setProductId(sysProductLabels.get(0).getProductId());
            batchProductLabel.setSkcCode(sysProductLabels.get(0).getSkcCode());
            batchProductLabel.setLabelList(labelList);
            batchProductLabel.setLabelLevelList(labelLevel.stream().distinct().collect(Collectors.toList()));
            batchProductLabelList.add(batchProductLabel);
        }
        // 批量更新到SKC-es
        return batchUpdateLabel2Es(batchProductLabelList, oldSkcCode);
    }

    /**
     * SPU标签ES同步
     * @param productId
     * @return
     */
    private void syncSpuLabelEs(String productId) throws IOException {
        //取出所有SKC的标签，组合为SPU标签
        QueryWrapper<SysProductLabel> wrapper = new QueryWrapper<>();
        wrapper.in("product_id",productId);
        wrapper.eq("is_del",0L);
        List<SysProductLabel> list = this.list(wrapper);
        if (list.isEmpty()) {
            updateLabel2EsForSpuNoLabels(productId);
        }

        BatchProductLabel batchProductLabel = new BatchProductLabel();
        List<String> labelList = new ArrayList<>();
        List<String> labelLevel = new ArrayList<>();
        list.forEach(item -> {
            labelList.add(item.getLabelId());
            labelLevel.addAll(parseLabel(item.getLabelId()));
        });
        batchProductLabel.setProductId(productId);
        batchProductLabel.setLabelList(labelList.stream().distinct().collect(Collectors.toList()));
        batchProductLabel.setLabelLevelList(labelLevel.stream().distinct().collect(Collectors.toList()));
        updateLabel2EsForSpu(batchProductLabel);
    }

    private void updateLabel2EsForSpuNoLabels(String productId) throws IOException {
        log.info("批量SPU商品打标 size = {}");
        HashMap<String,Object> data=new HashMap<>();
        data.put("labels", new ArrayList<>());
        data.put("label_levels", new ArrayList<>());
        UpdateRequest updateRequest = new UpdateRequest(goodsIndex, productId).doc(data);
        esUtil.update(updateRequest);
    }

    /**
     * 批量刷新标签到es
     * 1、更新ES for skc
     * 2、更新ES for SPU
     */
    private boolean batchUpdateLabel2Es(List<BatchProductLabel> batchProductLabelList, String oldSkcCode) throws IOException{
        BulkRequest bulkRequest = new BulkRequest();
        // 处理取消标签的逻辑
        if (CollectionUtils.isEmpty(batchProductLabelList)) {
            HashMap<String,Object> data=new HashMap<>();
            data.put("labels", new ArrayList<>());
            data.put("label_levels", new ArrayList<>());
            UpdateRequest updateRequest = new UpdateRequest(PRODUCT_SKC_INDEX, oldSkcCode).doc(data);
            bulkRequest.add(updateRequest);
            BulkResponse response = esUtil.bulk(bulkRequest);
            if(RestStatus.OK.equals(response.status())){
                return true;
            }
            return false;
        }

        // 处理更换标签的逻辑
        for(BatchProductLabel productLabel : batchProductLabelList){
            HashMap<String,Object> data=new HashMap<>();
            data.put("labels",productLabel.getLabelList().stream().map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            data.put("label_levels",productLabel.getLabelLevelList().stream().map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            UpdateRequest updateRequest = new UpdateRequest(PRODUCT_SKC_INDEX, productLabel.getSkcCode()).doc(data);
            bulkRequest.add(updateRequest);
        }
        List<String> updateIds = batchProductLabelList.stream().map(e -> e.getProductId()).collect(Collectors.toList());
        if(updateIds.size() > 0){
            BulkResponse response = esUtil.bulk(bulkRequest);
            if(RestStatus.OK.equals(response.status())){
                return true;
            }
        }
        return false;
    }

    /**
     * 更新SPU标签到ES
     * @param productLabel
     * @throws IOException
     */
    private void updateLabel2EsForSpu(BatchProductLabel productLabel) throws IOException{
        log.info("批量SPU商品打标 size = {}", productLabel.getLabelList().size());
        HashMap<String,Object> data=new HashMap<>();
        data.put("labels",productLabel.getLabelList());
        data.put("label_levels",productLabel.getLabelLevelList());
        UpdateRequest updateRequest = new UpdateRequest(goodsIndex, productLabel.getProductId()).doc(data);
        UpdateRequest updateRequestFab = new UpdateRequest(productSpuFabBaseInfoIndex, productLabel.getProductId()).doc(data);
        esUtil.update(updateRequest);
        esUtil.update(updateRequestFab);
    }

    @Override
    @NewSpan
    public void downFileTransferMarkLabel(String fileUrl, String keys) throws RuntimeException, IOException {
        String filePath = FileParseUtil.downLoadExcel(fileUrl);
        final Boolean[] isSetBlank = {true};
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {
            @Override
            protected void saveData() {
                log.info("=============解析完标签");
                ParseLabelEntity labelEntity = parseLabelByName(this.cachedDataList);
                //打标 ，追加方式
                batchLabeling(labelEntity.getList(), true);
                if(labelEntity.getErrorSpu().size() > 0){
                    String fileName = System.currentTimeMillis() + ".xlsx";
                    EasyExcelUtil.write(fileName, ErrorSpu.class, new IWriteDataExcel<ErrorSpu>() {
                        @Override
                        public List<ErrorSpu> getData() {
                            return labelEntity.getErrorSpu().stream().map(item -> {
                                ErrorSpu spu = new ErrorSpu();
                                spu.setSpu(item.getSpu());
                                spu.setErrorMsg(item.getErrorMsg());
                                return spu;
                            }).collect(Collectors.toList());
                        }
                    });
                    File file = new File(fileName);
                    String param = qiniuUtil.upload(file.getPath(), "异常商品"+System.currentTimeMillis() + ".xlsx");
                    log.info("redis的地址{}， keys：{}", param, keys);
                    file.delete();
                    RedisTemplateUtil.setex(redisPoolUtil, keys, param,60);
                    isSetBlank[0] = false;
                }
            }
        });
        if (isSetBlank[0]) {
            RedisTemplateUtil.setex(redisPoolUtil, keys, "",60);
        }
        Files.deleteIfExists(Paths.get(filePath));
    }

    @Data
    class ErrorSpu{
        @ExcelProperty("异常款色号")
        private String spu;

        @ExcelProperty("错误数据")
        private String errorMsg;
    }

    @Override
    public boolean batchLabeling(List<BatchProductLabel> batchProductLabelList, boolean isImport) {
        BulkRequest bulkRequest = new BulkRequest();
        List<SysProductLabel> saveProductLabels = new ArrayList<>();
        List<SysProductLabel> saveHandProductLabels = new ArrayList<>();
        List<String> productIds = new ArrayList<>();
        Map<String, List<String>> productSpuLabelsMap = new HashMap<>();
        Map<String, List<String>> productSpuLabelLevelsMap = new HashMap<>();
        Map<String, Map<String, String>> productOldMap = new HashMap<>();
        for(BatchProductLabel productLabel : batchProductLabelList){
            BatchProductLabel oldProductLabel = new BatchProductLabel();
            oldProductLabel.setSkcCode(productLabel.getSkcCode());
            oldProductLabel.setColorCode(productLabel.getColorCode());
            oldProductLabel.setProductCode(productLabel.getProductCode());
            List<String> newLevels = new ArrayList<>();
            productLabel.getLabelList().forEach(v -> {newLevels.add(v);});
            oldProductLabel.setLabelList(newLevels);
            String skc = productLabel.getProductCode() + productLabel.getColorCode();
            List<ProductSkcResp> goods = productService.findGoodsSkcDetailByIds(Lists.newArrayList(skc));
            if(CollectionUtils.isEmpty(goods)){
                log.error("批量导入商品标签--productCodeColor = {}不存在", productLabel.getProductCode() + productLabel.getColorCode());
                continue;
            }
            productLabel.setProductId(Long.valueOf(goods.get(0).getProduct_id()).toString());
            oldProductLabel.setProductId(Long.valueOf(goods.get(0).getProduct_id()).toString());
            productIds.add(productLabel.getProductId());
            List<String> labelLevelList = new ArrayList<>();
            for(String labels : productLabel.getLabelList()){
                List<String> labelsNew = parseLabel(labels);
                labelLevelList.addAll(labelsNew);
            }

            // 对新的标签进行map处理 为了去除老的相同父级标签
            Map<String, String> labelMap = new HashMap<>();
            labelLevelList.stream().distinct().forEach(v -> {
                String[] split = v.split("-");
                if (split.length < 3) {
                    return;
                }
                labelMap.put(split[0] + "-" + split[1] + "-" + split[2], v);
            });
            productOldMap.put(productLabel.getProductId(), labelMap);

            productLabel.setLabelLevelList(labelLevelList.stream().distinct().collect(Collectors.toList()));
            HashMap<String,Object> data=new HashMap<>();
            List<String> labelLevels = productLabel.getLabelLevelList();
            List<String> labels = productLabel.getLabelList();
            productSpuLabelsMap.put(productLabel.getProductId(), labels);
            productSpuLabelLevelsMap.put(productLabel.getProductId(), labelLevels);

            //如果是导入方式则为追加模式，覆盖重复的
            if (isImport){
                // 处理老标签 将导入的标签的相同父级去除
                List<String> filterLables = new ArrayList<>();
                goods.get(0).getLabels().forEach(v -> {
                    String[] split = v.split("-");
                    if (split.length < 3) {
                        filterLables.add(v);
                        return;
                    }
                    if (MapUtils.isEmpty(labelMap)) {
                        filterLables.add(v);
                        return;
                    }
                    String s = labelMap.get(split[0] + "-" + split[1] + "-" + split[2]);
                    if (StringUtils.isBlank(s)) {
                        filterLables.add(v);
                        return;
                    }
                });

                if (CollectionUtils.isNotEmpty(goods.get(0).getLabel_levels())){
                    labelLevels.addAll(goods.get(0).getLabel_levels());
                }
                if (CollectionUtils.isNotEmpty(goods.get(0).getLabels())){
                    labels.addAll(filterLables);
                }
                productLabel.setLabelList(labels.stream().distinct().collect(Collectors.toList()));
                productLabel.setLabelLevelList(labelLevels.stream().distinct().collect(Collectors.toList()));
            }
            data.put("labels", labels.stream().distinct().map(item -> Strman.replace(item, "-", "_", true))
                    .collect(Collectors.toList()));
            data.put("label_levels", labelLevels.stream().map(item -> Strman.replace(item, "-", "_", true))
                    .distinct().collect(Collectors.toList()));
            UpdateRequest updateRequest = new UpdateRequest(PRODUCT_SKC_INDEX, productLabel.getProductCode() + productLabel.getColorCode()).doc(data);
            bulkRequest.add(updateRequest);

            //重新放所有level进去
            saveProductLabels.addAll(packageProductLabel(productLabel));
            saveHandProductLabels.addAll(packageProductLabel(oldProductLabel));
        }

        // 处理商品中心详情的数据
        BulkRequest bulkBaseInfoRequest = new BulkRequest();
        if (CollectionUtils.isNotEmpty(productIds)) {
            List<ProductSpuFabBaseInfoEsResp> productSpuFabBaseInfos = findProductFabBaseInfoByProductIds(productIds);
            if (CollectionUtils.isNotEmpty(productSpuFabBaseInfos)) {
                productSpuFabBaseInfos.forEach(v -> {
                    List<String> labels = CollectionUtils.isEmpty(productSpuLabelsMap.get(v.getId())) ? new ArrayList<>() : productSpuLabelsMap.get(v.getId());
                    List<String> labelLevels = CollectionUtils.isEmpty(productSpuLabelLevelsMap.get(v.getId())) ? new ArrayList<>() : productSpuLabelLevelsMap.get(v.getId());
                    if (isImport) {
                        if (CollectionUtils.isNotEmpty(v.getLabel_levels())){
                            labelLevels.addAll(v.getLabel_levels());
                        }
                        Map<String, String> oldMap = productOldMap.get(v.getId());
                        if (CollectionUtils.isNotEmpty(v.getLabels())){
                            List<String> newLabelList = new ArrayList<>();
                            v.getLabels().forEach(x -> {
                                String[] split = x.split("-");
                                if (split.length < 3) {
                                    newLabelList.add(x);
                                    return;
                                }
                                if (MapUtils.isEmpty(oldMap)) {
                                    newLabelList.add(x);
                                    return;
                                }
                                String s = oldMap.get(split[0] + "-" + split[1] + "-" + split[2]);
                                if (StringUtils.isBlank(s)) {
                                    newLabelList.add(x);
                                    return;
                                }
                            });
                            labels.addAll(newLabelList);
                        }
                    }
                    HashMap<String,Object> data = new HashMap<>();
                    data.put("labels", labels.stream().distinct().collect(Collectors.toList()));
                    data.put("label_levels", labelLevels.stream().distinct().collect(Collectors.toList()));
                    UpdateRequest updateRequest = new UpdateRequest(productSpuFabBaseInfoIndex, v.getId()).doc(data);
                    bulkBaseInfoRequest.add(updateRequest);
                });
            }
        }

        //查询
        List<String> updateIds = batchProductLabelList.stream().filter(v -> StringUtils.isNotBlank(v.getProductId()))
                .map(e -> e.getProductId()).collect(Collectors.toList());
        log.info("==========updateIds:{}", JSONObject.toJSONString(updateIds));
        if(updateIds.size() > 0){
            sysProductLabelMapper.delByProductIds(updateIds);
            saveBatch(saveProductLabels);
            sysHandworkProductLabelMapper.delByProductIds(updateIds);
            sysHandworkProductLabelMapper.batchInsert(saveHandProductLabels);
            BulkResponse response = null;
            try {
                esUtil.bulk(bulkBaseInfoRequest);
                response = esUtil.bulk(bulkRequest);
            } catch (IOException e) {
                log.error("批量导入标签异常 msg = {}", e.getMessage(), e);
            }
            return RestStatus.OK.equals(response.status());
        }
        return false;
    }

    private List<ProductSpuFabBaseInfoEsResp> findProductFabBaseInfoByProductIds(List<String> productIds) {
        SearchRequest request = new SearchRequest();
        request.indices(productSpuFabBaseInfoIndex);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        queryBuilder.must(QueryBuilders.termsQuery("_id", productIds));
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(productIds.size());
        request.source(sourceBuilder);

        List<ProductSpuFabBaseInfoEsResp> resps = new ArrayList<>();
        try {
            log.info("findProductFabBaseInfoByProductIds request = {}", request.source().toString());
            SearchResponse search = esUtil.search(request);
            if (search.getHits().getTotalHits().value == 0){
                return Collections.emptyList();
            }
            SearchHit[] hits = search.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductSpuFabBaseInfoEsResp entity = ProductSpuFabBaseInfoEsResp.fromJson(hits[i].getSourceAsString(), ProductSpuFabBaseInfoEsResp.class);
                if (CollectionUtils.isNotEmpty(entity.getLabels())){
                    entity.setLabels(entity.getLabels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                    entity.setLabel_levels(entity.getLabel_levels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                }
                entity.setId(hits[i].getId());
                resps.add(entity);
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品异常e = {}", e.getMessage());
        }
        return resps;
    }


    /**
     * 逻辑删除(只删除本平台打的标签)
     * @param req
     */
    private boolean delProductLabel(MarkingLabelReq req){
        SysProductLabel updatePara = new SysProductLabel();
        updatePara.setProductId(req.getProductId());
        updatePara.setSkcCode(req.getSkcCode());
        updatePara.setIsDel(0L);
        // updatePara.setLabelFrom(1);
        List<SysProductLabel> list = this.list(new QueryWrapper<>(updatePara));
        if(list.size() <= 0) {
            return false;
        }
        Map<Integer, List<SysProductLabel>> labelMap = list.stream().collect(Collectors.groupingBy(SysProductLabel::getLabelType));
        // skc维度
        List<SysProductLabel> skcLabel = labelMap.get(1);
        // spu维度
        List<SysProductLabel> spuLabel = labelMap.get(2);
        if(CollectionUtils.isNotEmpty(skcLabel)){
            delSkcLabel(skcLabel);
        }
        if(CollectionUtils.isNotEmpty(spuLabel)){
            delSpuLabel(spuLabel);
            return true;
        }
        // 处理手工标签表的数据
        delHandworkProductLabel(updatePara);
        return false;
    }

    @Autowired
    private SysHandworkProductLabelMapper sysHandworkProductLabelMapper;

    private void delHandworkProductLabel(SysProductLabel updatePara) {
        sysHandworkProductLabelMapper.delByProductIdAndSkcCode(updatePara.getProductId(), updatePara.getSkcCode());
    }

    /**
     * 删除skc标签
     * @param skcLabel
     */
    private void delSkcLabel(List<SysProductLabel> skcLabel){
        List<SysProductLabel> updateList = skcLabel.stream().map(e -> {
            SysProductLabel updateSysPLabel = new SysProductLabel();
            updateSysPLabel.setId(e.getId());
            updateSysPLabel.setIsDel(1L);
            return updateSysPLabel;
        }).collect(Collectors.toList());
        this.updateBatchById(updateList,updateList.size());
    }
    /**
     * 删除spu标签
     * @param spuLabel
     */
    private void delSpuLabel(List<SysProductLabel> spuLabel){
        List<String> labelList = spuLabel.stream().map(SysProductLabel::getLabelId).collect(Collectors.toList());
        LambdaUpdateChainWrapper<SysProductLabel> lambdaUpdateChainWrapper = this.lambdaUpdate();
        lambdaUpdateChainWrapper.eq(SysProductLabel :: getProductId,spuLabel.get(0).getProductId());
        lambdaUpdateChainWrapper.in(SysProductLabel :: getLabelId,labelList);
        lambdaUpdateChainWrapper.eq(SysProductLabel :: getIsDel,0L);
        lambdaUpdateChainWrapper.set(SysProductLabel :: getIsDel,1L);
        lambdaUpdateChainWrapper.update();
    }

    /**
     * 保存
     * @param reqData
     * @param userId
     * @return
     */
    private boolean insertProductLabel(MarkingLabelReq reqData,String userId){
        boolean flag = false;
        List<SysProductLabel> labelList = new ArrayList<>();
        // skc
        for(String label : reqData.getLabelList()){
            SysProductLabel sysProductLabel = new SysProductLabel();
            sysProductLabel.setProductId(reqData.getProductId());
            sysProductLabel.setSkcCode(reqData.getSkcCode());
            sysProductLabel.setLabelId(label);
            sysProductLabel.setLabelLevel(label);
            sysProductLabel.setLabelType(1);
            sysProductLabel.setLabelFrom(3);
            labelList.add(sysProductLabel);
        }
        if(CollectionUtils.isNotEmpty(reqData.getSpuLabelList())) {
            flag = true;
            //spu
            insertSpuLabel(reqData.getSpuLabelList(), labelList, reqData.getProductId());
        }
        Date now = new Date();
        labelList.stream().forEach(e -> {
            e.setId(IdLeaf.getDateId(labelLeafTag));
            e.setIsDel(0L);
            e.setCreateBy(userId);
            e.setUpdateBy(userId);
            e.setCreateTime(now);
            e.setUpdateTime(now);
            // 手工标标识
            e.setLabelFrom(3);
        });
        this.saveBatch(labelList);
        sysHandworkProductLabelMapper.batchInsert(labelList);
        // 返回是否影响其他skc
        return flag;
    }

    private void insertSpuLabel(List<String> spuLabelList,List<SysProductLabel> labelList,String productId){
        List<ProductSkcResp> goodSkcEntities = getSkcByProductId(Integer.valueOf(productId));
        for(ProductSkcResp goodsSkc : goodSkcEntities){
            for(String label : spuLabelList){
                SysProductLabel sysProductLabel = new SysProductLabel();
                sysProductLabel.setProductId(productId);
                sysProductLabel.setSkcCode(goodsSkc.getId());
                sysProductLabel.setLabelId(label);
                sysProductLabel.setLabelLevel(label);
                sysProductLabel.setLabelType(2);
                sysProductLabel.setLabelFrom(3);
                labelList.add(sysProductLabel);
            }
        }
    }

    private List<ProductSkcResp> getSkcByProductId(long productId){
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(10);
        QueryGoodsReq context = new QueryGoodsReq();
        context.setProductId(productId);
        return productService.searchGoodsSkc(context, page, "");
    }

    private List<String> parseLabel(String labelLevel){
        String[] labelArr = labelLevel.split("-");
        List<String> labelsNew = new ArrayList<>();
        for(int i = 0;i< labelArr.length;i++) {
            int j = 0;
            StringBuilder labelBuilder = new StringBuilder();
            while (j <= i) {
                labelBuilder.append(labelArr[j]);
                if (j < i) {
                    labelBuilder.append("-");
                }
                j++;
            }
            labelsNew.add(labelBuilder.toString());
        }
        return labelsNew;
    }

    private List<SysProductLabel> packageProductLabel(BatchProductLabel productLabel){
        List<SysProductLabel> list = new ArrayList<>();
        for(String label : productLabel.getLabelList()){
            SysProductLabel sysProductLabel = new SysProductLabel();
            sysProductLabel.setId(IdLeaf.getDateId(labelLeafTag));
            sysProductLabel.setProductId(productLabel.getProductId());
            sysProductLabel.setSkcCode(productLabel.getProductCode() + productLabel.getColorCode());
            sysProductLabel.setLabelId(Strman.replace(label, "_", "-", true));
            sysProductLabel.setLabelLevel(Strman.replace(label, "_", "-", true));
            sysProductLabel.setIsDel(0L);
            sysProductLabel.setCreateTime(new Date());
            sysProductLabel.setUpdateTime(new Date());
            sysProductLabel.setCreateBy("系统导入");
            sysProductLabel.setUpdateBy("系统导入");
            sysProductLabel.setLabelFrom(3);
            sysProductLabel.setLabelType(1);
            list.add(sysProductLabel);
        }
        return list;
    }

    /**
     * @Description: 根据名称解析标签
     * @Author: brian
     * @Date: 2021/8/27 16:36
     * @params: [dataList][{0=童装, 1=5G450034, 2=001, 3=null, 4=null, 5=长裙, 6=null, 7=null, 8=null, 9=null, 10=null, 11=null, 12=null, 13=null, 14=null, 15=null, 16=null, 17=null, 18=null}]
     * @return: org.springcenter.product.module.product.entity.ParseLabelEntity
     */
    private ParseLabelEntity parseLabelByName(List<Map<Integer, String>> dataList){
        // 获取所有标签信息
        //List<SysCategoryModel> model = iSysBaseAPI.queryAllDSysCategoryByCode(categoryParentCode);
        // 为了自测
        String ss = "[{\"code\":\"B05-A33-A13-A01\",\"id\":\"1897629143950004225\",\"leaf\":true,\"name\":\"运动\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A02\",\"id\":\"1897629176615243778\",\"leaf\":true,\"name\":\"牛仔\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A03\",\"id\":\"1897629219426504706\",\"leaf\":true,\"name\":\"轻礼服\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A04\",\"id\":\"1897629237082640386\",\"leaf\":true,\"name\":\"极寒\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A05\",\"id\":\"1897629259396337666\",\"leaf\":true,\"name\":\"非遗\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A06\",\"id\":\"1897629278838820865\",\"leaf\":true,\"name\":\"30周年\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A07\",\"id\":\"1897629303744598017\",\"leaf\":true,\"name\":\"黑支线 b line\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A08\",\"id\":\"1897629325003640834\",\"leaf\":true,\"name\":\"Samuel Drira\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A09\",\"id\":\"1897629346293927938\",\"leaf\":true,\"name\":\"Neil Barrett\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A10\",\"id\":\"1897629368378822658\",\"leaf\":true,\"name\":\"精工\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A11\",\"id\":\"1897629388096245762\",\"leaf\":true,\"name\":\"XUN\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A12\",\"id\":\"1897629407346216962\",\"leaf\":true,\"name\":\"SAMUEL\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A13\",\"id\":\"1897629425088122882\",\"leaf\":true,\"name\":\"0压\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A14\",\"id\":\"1897629443893071873\",\"leaf\":true,\"name\":\"度假\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A15\",\"id\":\"1897629472204623874\",\"leaf\":true,\"name\":\"新生儿\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A13-A16\",\"id\":\"1897629512100843522\",\"leaf\":true,\"name\":\"户外\",\"pid\":\"1897628550525407233\",\"pidCode\":\"B05-A33-A13\",\"pidName\":\"系列标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A14-A01\",\"id\":\"1897629018859081730\",\"leaf\":true,\"name\":\"外观专利\",\"pid\":\"1897628569261363201\",\"pidCode\":\"B05-A33-A14\",\"pidName\":\"专利标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A14-A02\",\"id\":\"1897629037748342785\",\"leaf\":true,\"name\":\"实用新型\",\"pid\":\"1897628569261363201\",\"pidCode\":\"B05-A33-A14\",\"pidName\":\"专利标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A14-A03\",\"id\":\"1897629059573190657\",\"leaf\":true,\"name\":\"美术著作\",\"pid\":\"1897628569261363201\",\"pidCode\":\"B05-A33-A14\",\"pidName\":\"专利标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A14-A04\",\"id\":\"1897629077310902274\",\"leaf\":true,\"name\":\"发明专利\",\"pid\":\"1897628569261363201\",\"pidCode\":\"B05-A33-A14\",\"pidName\":\"专利标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A15-A01\",\"id\":\"1897628854345895937\",\"leaf\":true,\"name\":\"运动\",\"pid\":\"1897628614670782465\",\"pidCode\":\"B05-A33-A15\",\"pidName\":\"场景标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A15-A02\",\"id\":\"1897628873503592449\",\"leaf\":true,\"name\":\"通勤\",\"pid\":\"1897628614670782465\",\"pidCode\":\"B05-A33-A15\",\"pidName\":\"场景标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A15-A03\",\"id\":\"1897628894022127617\",\"leaf\":true,\"name\":\"聚会\",\"pid\":\"1897628614670782465\",\"pidCode\":\"B05-A33-A15\",\"pidName\":\"场景标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A15-A04\",\"id\":\"1897628922568560641\",\"leaf\":true,\"name\":\"度假\",\"pid\":\"1897628614670782465\",\"pidCode\":\"B05-A33-A15\",\"pidName\":\"场景标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A15-A05\",\"id\":\"1897628957500608513\",\"leaf\":true,\"name\":\"日常\",\"pid\":\"1897628614670782465\",\"pidCode\":\"B05-A33-A15\",\"pidName\":\"场景标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A16-A01\",\"id\":\"1897628717062131713\",\"leaf\":true,\"name\":\"简约\",\"pid\":\"1897628638651928577\",\"pidCode\":\"B05-A33-A16\",\"pidName\":\"时髦度\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A16-A02\",\"id\":\"1897628742628724738\",\"leaf\":true,\"name\":\"时髦\",\"pid\":\"1897628638651928577\",\"pidCode\":\"B05-A33-A16\",\"pidName\":\"时髦度\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A16-A03\",\"id\":\"1897628761628921858\",\"leaf\":true,\"name\":\"先锋\",\"pid\":\"1897628638651928577\",\"pidCode\":\"B05-A33-A16\",\"pidName\":\"时髦度\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A33-A16-A04\",\"id\":\"1897628789866860545\",\"leaf\":true,\"name\":\"浪漫\",\"pid\":\"1897628638651928577\",\"pidCode\":\"B05-A33-A16\",\"pidName\":\"时髦度\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A01\",\"id\":\"1714832098995933185\",\"leaf\":false,\"name\":\"精致都市\",\"pid\":\"1714832063939940353\",\"pidCode\":\"B05-A34\",\"pidName\":\"自动风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A02\",\"id\":\"1714832120848257026\",\"leaf\":false,\"name\":\"优雅\",\"pid\":\"1714832063939940353\",\"pidCode\":\"B05-A34\",\"pidName\":\"自动风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A03\",\"id\":\"1714832150787198978\",\"leaf\":false,\"name\":\"运动户外\",\"pid\":\"1714832063939940353\",\"pidCode\":\"B05-A34\",\"pidName\":\"自动风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A04\",\"id\":\"1714832178792566785\",\"leaf\":false,\"name\":\"日常休闲\",\"pid\":\"1714832063939940353\",\"pidCode\":\"B05-A34\",\"pidName\":\"自动风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A05\",\"id\":\"1714832254407479298\",\"leaf\":false,\"name\":\"极简\",\"pid\":\"1714832063939940353\",\"pidCode\":\"B05-A34\",\"pidName\":\"自动风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A06\",\"id\":\"1714832284262535169\",\"leaf\":false,\"name\":\"前卫摩登\",\"pid\":\"1714832063939940353\",\"pidCode\":\"B05-A34\",\"pidName\":\"自动风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A07\",\"id\":\"1715287004504592385\",\"leaf\":false,\"name\":\"复古\",\"pid\":\"1714832063939940353\",\"pidCode\":\"B05-A34\",\"pidName\":\"自动风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A08\",\"id\":\"1715287050922954754\",\"leaf\":false,\"name\":\"度假\",\"pid\":\"1714832063939940353\",\"pidCode\":\"B05-A34\",\"pidName\":\"自动风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A01-A01\",\"id\":\"1714832343557410818\",\"leaf\":true,\"name\":\"都市\",\"pid\":\"1714832098995933185\",\"pidCode\":\"B05-A34-A01\",\"pidName\":\"精致都市\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A01-A02\",\"id\":\"1714832366995181569\",\"leaf\":true,\"name\":\"性感\",\"pid\":\"1714832098995933185\",\"pidCode\":\"B05-A34-A01\",\"pidName\":\"精致都市\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A01-A03\",\"id\":\"1714832389124329473\",\"leaf\":true,\"name\":\"华丽\",\"pid\":\"1714832098995933185\",\"pidCode\":\"B05-A34-A01\",\"pidName\":\"精致都市\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A01-A04\",\"id\":\"1714832420082487298\",\"leaf\":true,\"name\":\"通勤\",\"pid\":\"1714832098995933185\",\"pidCode\":\"B05-A34-A01\",\"pidName\":\"精致都市\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A01-A05\",\"id\":\"1715284212033458177\",\"leaf\":true,\"name\":\"趣味\",\"pid\":\"1714832098995933185\",\"pidCode\":\"B05-A34-A01\",\"pidName\":\"精致都市\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A02-A01\",\"id\":\"1714832503972761601\",\"leaf\":true,\"name\":\"优雅\",\"pid\":\"1714832120848257026\",\"pidCode\":\"B05-A34-A02\",\"pidName\":\"优雅\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A02-A02\",\"id\":\"1714832543571185666\",\"leaf\":true,\"name\":\"浪漫\",\"pid\":\"1714832120848257026\",\"pidCode\":\"B05-A34-A02\",\"pidName\":\"优雅\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A03-A01\",\"id\":\"1714832578924974081\",\"leaf\":true,\"name\":\"运动\",\"pid\":\"1714832150787198978\",\"pidCode\":\"B05-A34-A03\",\"pidName\":\"运动户外\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A03-A02\",\"id\":\"1714832598227161089\",\"leaf\":true,\"name\":\"户外\",\"pid\":\"1714832150787198978\",\"pidCode\":\"B05-A34-A03\",\"pidName\":\"运动户外\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A04-A01\",\"id\":\"1714832714086420481\",\"leaf\":true,\"name\":\"休闲\",\"pid\":\"1714832178792566785\",\"pidCode\":\"B05-A34-A04\",\"pidName\":\"日常休闲\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A04-A03\",\"id\":\"1714832766561357826\",\"leaf\":true,\"name\":\"工装\",\"pid\":\"1714832178792566785\",\"pidCode\":\"B05-A34-A04\",\"pidName\":\"日常休闲\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A05-A01\",\"id\":\"1714832807329992705\",\"leaf\":true,\"name\":\"极简\",\"pid\":\"1714832254407479298\",\"pidCode\":\"B05-A34-A05\",\"pidName\":\"极简\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A05-A02\",\"id\":\"1714832833116573697\",\"leaf\":true,\"name\":\"通勤\",\"pid\":\"1714832254407479298\",\"pidCode\":\"B05-A34-A05\",\"pidName\":\"极简\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A06-A01\",\"id\":\"1714832862141157378\",\"leaf\":true,\"name\":\"前卫\",\"pid\":\"1714832284262535169\",\"pidCode\":\"B05-A34-A06\",\"pidName\":\"前卫摩登\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A06-A02\",\"id\":\"1714832884131893249\",\"leaf\":true,\"name\":\"街头\",\"pid\":\"1714832284262535169\",\"pidCode\":\"B05-A34-A06\",\"pidName\":\"前卫摩登\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A07-A01\",\"id\":\"1715287264698241026\",\"leaf\":true,\"name\":\"复古\",\"pid\":\"1715287004504592385\",\"pidCode\":\"B05-A34-A07\",\"pidName\":\"复古\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A07-A02\",\"id\":\"1715287295236968450\",\"leaf\":true,\"name\":\"新中式\",\"pid\":\"1715287004504592385\",\"pidCode\":\"B05-A34-A07\",\"pidName\":\"复古\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A34-A08-A01\",\"id\":\"1715287365806133250\",\"leaf\":true,\"name\":\"度假\",\"pid\":\"1715287050922954754\",\"pidCode\":\"B05-A34-A08\",\"pidName\":\"度假\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A35-A01\",\"id\":\"1714885432217899010\",\"leaf\":false,\"name\":\"型\",\"pid\":\"1714885386537734146\",\"pidCode\":\"B05-A35\",\"pidName\":\"元素标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A35-A02\",\"id\":\"1714885445589340161\",\"leaf\":false,\"name\":\"色\",\"pid\":\"1714885386537734146\",\"pidCode\":\"B05-A35\",\"pidName\":\"元素标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A35-A03\",\"id\":\"1714885463910060033\",\"leaf\":false,\"name\":\"质\",\"pid\":\"1714885386537734146\",\"pidCode\":\"B05-A35\",\"pidName\":\"元素标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A35-A01-A01\",\"id\":\"1714885495182790657\",\"leaf\":true,\"name\":\"显身材\",\"pid\":\"1714885432217899010\",\"pidCode\":\"B05-A35-A01\",\"pidName\":\"型\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A35-A01-A02\",\"id\":\"1714885517261606913\",\"leaf\":true,\"name\":\"基础线\",\"pid\":\"1714885432217899010\",\"pidCode\":\"B05-A35-A01\",\"pidName\":\"型\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A35-A01-A03\",\"id\":\"1714885543316623361\",\"leaf\":true,\"name\":\"超廓形\",\"pid\":\"1714885432217899010\",\"pidCode\":\"B05-A35-A01\",\"pidName\":\"型\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A35-A02-A01\",\"id\":\"1714885567450648577\",\"leaf\":true,\"name\":\"百搭色\",\"pid\":\"1714885445589340161\",\"pidCode\":\"B05-A35-A02\",\"pidName\":\"色\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A35-A02-A02\",\"id\":\"1714885588866764802\",\"leaf\":true,\"name\":\"低饱和\",\"pid\":\"1714885445589340161\",\"pidCode\":\"B05-A35-A02\",\"pidName\":\"色\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A35-A03-A01\",\"id\":\"1714885616914075649\",\"leaf\":true,\"name\":\"贵面料\",\"pid\":\"1714885463910060033\",\"pidCode\":\"B05-A35-A03\",\"pidName\":\"质\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A35-A03-A02\",\"id\":\"1714885640930660354\",\"leaf\":true,\"name\":\"软面料\",\"pid\":\"1714885463910060033\",\"pidCode\":\"B05-A35-A03\",\"pidName\":\"质\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A38-A01\",\"id\":\"1880094870443982849\",\"leaf\":false,\"name\":\"服用功能\",\"pid\":\"1880094833093705730\",\"pidCode\":\"B05-A38\",\"pidName\":\"面料标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A38-A02\",\"id\":\"1880094904996659202\",\"leaf\":false,\"name\":\"面料特性\",\"pid\":\"1880094833093705730\",\"pidCode\":\"B05-A38\",\"pidName\":\"面料标签\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A38-A01-A01\",\"id\":\"1880094966669705218\",\"leaf\":true,\"name\":\"防风\",\"pid\":\"1880094870443982849\",\"pidCode\":\"B05-A38-A01\",\"pidName\":\"服用功能\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A38-A01-A02\",\"id\":\"1880094997019688961\",\"leaf\":true,\"name\":\"透湿\",\"pid\":\"1880094870443982849\",\"pidCode\":\"B05-A38-A01\",\"pidName\":\"服用功能\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A38-A01-A03\",\"id\":\"1880095019656347649\",\"leaf\":true,\"name\":\"可机洗\",\"pid\":\"1880094870443982849\",\"pidCode\":\"B05-A38-A01\",\"pidName\":\"服用功能\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A38-A02-A01\",\"id\":\"1880095054154498050\",\"leaf\":true,\"name\":\"反面绒感\",\"pid\":\"1880094904996659202\",\"pidCode\":\"B05-A38-A02\",\"pidName\":\"面料特性\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A38-A02-A02\",\"id\":\"1880095079613923330\",\"leaf\":true,\"name\":\"里布绒感\",\"pid\":\"1880094904996659202\",\"pidCode\":\"B05-A38-A02\",\"pidName\":\"面料特性\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A38-A02-A03\",\"id\":\"1880095098903527426\",\"leaf\":true,\"name\":\"局部反面绒感\",\"pid\":\"1880094904996659202\",\"pidCode\":\"B05-A38-A02\",\"pidName\":\"面料特性\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A01\",\"id\":\"1889197095044841474\",\"leaf\":false,\"name\":\"运动\",\"pid\":\"1889197058806054914\",\"pidCode\":\"B05-A39\",\"pidName\":\"停用\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A02\",\"id\":\"1889197117241098241\",\"leaf\":false,\"name\":\"日常\",\"pid\":\"1889197058806054914\",\"pidCode\":\"B05-A39\",\"pidName\":\"停用\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A03\",\"id\":\"1889197144776704001\",\"leaf\":false,\"name\":\"通勤\",\"pid\":\"1889197058806054914\",\"pidCode\":\"B05-A39\",\"pidName\":\"停用\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A04\",\"id\":\"1889197166012465154\",\"leaf\":false,\"name\":\"聚会\",\"pid\":\"1889197058806054914\",\"pidCode\":\"B05-A39\",\"pidName\":\"停用\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A05\",\"id\":\"1889197194042998786\",\"leaf\":false,\"name\":\"度假\",\"pid\":\"1889197058806054914\",\"pidCode\":\"B05-A39\",\"pidName\":\"停用\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A01-A01\",\"id\":\"1889197229098991618\",\"leaf\":true,\"name\":\"日常运动\",\"pid\":\"1889197095044841474\",\"pidCode\":\"B05-A39-A01\",\"pidName\":\"运动\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A01-A02\",\"id\":\"1889197257251160065\",\"leaf\":true,\"name\":\"专业运动\",\"pid\":\"1889197095044841474\",\"pidCode\":\"B05-A39-A01\",\"pidName\":\"运动\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A02-A01\",\"id\":\"1889197305229803521\",\"leaf\":true,\"name\":\"简约日常\",\"pid\":\"1889197117241098241\",\"pidCode\":\"B05-A39-A02\",\"pidName\":\"日常\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A02-A02\",\"id\":\"1889197330387238913\",\"leaf\":true,\"name\":\"时髦日常\",\"pid\":\"1889197117241098241\",\"pidCode\":\"B05-A39-A02\",\"pidName\":\"日常\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A02-A03\",\"id\":\"1889197356060573698\",\"leaf\":true,\"name\":\"先锋日常\",\"pid\":\"1889197117241098241\",\"pidCode\":\"B05-A39-A02\",\"pidName\":\"日常\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A03-A01\",\"id\":\"1889197406572576770\",\"leaf\":true,\"name\":\"简约通勤\",\"pid\":\"1889197144776704001\",\"pidCode\":\"B05-A39-A03\",\"pidName\":\"通勤\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A03-A02\",\"id\":\"1889197429691580418\",\"leaf\":true,\"name\":\"时髦通勤\",\"pid\":\"1889197144776704001\",\"pidCode\":\"B05-A39-A03\",\"pidName\":\"通勤\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A03-A03\",\"id\":\"1889197459756351490\",\"leaf\":true,\"name\":\"先锋通勤\",\"pid\":\"1889197144776704001\",\"pidCode\":\"B05-A39-A03\",\"pidName\":\"通勤\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A04-A01\",\"id\":\"1889197491888914434\",\"leaf\":true,\"name\":\"简约聚会\",\"pid\":\"1889197166012465154\",\"pidCode\":\"B05-A39-A04\",\"pidName\":\"聚会\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A04-A02\",\"id\":\"1889197514374578177\",\"leaf\":true,\"name\":\"时髦聚会\",\"pid\":\"1889197166012465154\",\"pidCode\":\"B05-A39-A04\",\"pidName\":\"聚会\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A04-A03\",\"id\":\"1889197539871752194\",\"leaf\":true,\"name\":\"先锋聚会\",\"pid\":\"1889197166012465154\",\"pidCode\":\"B05-A39-A04\",\"pidName\":\"聚会\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A05-A01\",\"id\":\"1889197574676086786\",\"leaf\":true,\"name\":\"简约度假\",\"pid\":\"1889197194042998786\",\"pidCode\":\"B05-A39-A05\",\"pidName\":\"度假\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A05-A02\",\"id\":\"1889197606661849090\",\"leaf\":true,\"name\":\"时髦度假\",\"pid\":\"1889197194042998786\",\"pidCode\":\"B05-A39-A05\",\"pidName\":\"度假\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A05-A03\",\"id\":\"1889197650832064514\",\"leaf\":true,\"name\":\"先锋度假\",\"pid\":\"1889197194042998786\",\"pidCode\":\"B05-A39-A05\",\"pidName\":\"度假\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A39-A05-A04\",\"id\":\"1889197684713652225\",\"leaf\":true,\"name\":\"浪漫度假\",\"pid\":\"1889197194042998786\",\"pidCode\":\"B05-A39-A05\",\"pidName\":\"度假\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A01\",\"id\":\"1897630593216253953\",\"leaf\":false,\"name\":\"运动\",\"pid\":\"1897630492720730113\",\"pidCode\":\"B05-A40\",\"pidName\":\"场景风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A02\",\"id\":\"1897630612185206785\",\"leaf\":false,\"name\":\"日常\",\"pid\":\"1897630492720730113\",\"pidCode\":\"B05-A40\",\"pidName\":\"场景风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A03\",\"id\":\"1897630640062435330\",\"leaf\":false,\"name\":\"通勤\",\"pid\":\"1897630492720730113\",\"pidCode\":\"B05-A40\",\"pidName\":\"场景风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A04\",\"id\":\"1897630779336609793\",\"leaf\":false,\"name\":\"聚会\",\"pid\":\"1897630492720730113\",\"pidCode\":\"B05-A40\",\"pidName\":\"场景风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A05\",\"id\":\"1897630809038360577\",\"leaf\":false,\"name\":\"度假\",\"pid\":\"1897630492720730113\",\"pidCode\":\"B05-A40\",\"pidName\":\"场景风格\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A01-A01\",\"id\":\"1897630833419849730\",\"leaf\":true,\"name\":\"日常运动\",\"pid\":\"1897630593216253953\",\"pidCode\":\"B05-A40-A01\",\"pidName\":\"运动\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A01-A02\",\"id\":\"1897630929096118274\",\"leaf\":true,\"name\":\"专业运动\",\"pid\":\"1897630593216253953\",\"pidCode\":\"B05-A40-A01\",\"pidName\":\"运动\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A02-A01\",\"id\":\"1897630968885596161\",\"leaf\":true,\"name\":\"简约日常\",\"pid\":\"1897630612185206785\",\"pidCode\":\"B05-A40-A02\",\"pidName\":\"日常\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A02-A02\",\"id\":\"1897630986275180545\",\"leaf\":true,\"name\":\"时髦日常\",\"pid\":\"1897630612185206785\",\"pidCode\":\"B05-A40-A02\",\"pidName\":\"日常\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A02-A03\",\"id\":\"1897631003689930753\",\"leaf\":true,\"name\":\"先锋日常\",\"pid\":\"1897630612185206785\",\"pidCode\":\"B05-A40-A02\",\"pidName\":\"日常\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A03-A01\",\"id\":\"1897631033286823938\",\"leaf\":true,\"name\":\"简约通勤\",\"pid\":\"1897630640062435330\",\"pidCode\":\"B05-A40-A03\",\"pidName\":\"通勤\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A03-A02\",\"id\":\"1897631056450080770\",\"leaf\":true,\"name\":\"时髦通勤\",\"pid\":\"1897630640062435330\",\"pidCode\":\"B05-A40-A03\",\"pidName\":\"通勤\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A03-A03\",\"id\":\"1897631083830497281\",\"leaf\":true,\"name\":\"先锋通勤\",\"pid\":\"1897630640062435330\",\"pidCode\":\"B05-A40-A03\",\"pidName\":\"通勤\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A04-A01\",\"id\":\"1897631115100917761\",\"leaf\":true,\"name\":\"简约聚会\",\"pid\":\"1897630779336609793\",\"pidCode\":\"B05-A40-A04\",\"pidName\":\"聚会\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A04-A02\",\"id\":\"1897631145387986946\",\"leaf\":true,\"name\":\"时髦聚会\",\"pid\":\"1897630779336609793\",\"pidCode\":\"B05-A40-A04\",\"pidName\":\"聚会\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A04-A03\",\"id\":\"1897631171608891393\",\"leaf\":true,\"name\":\"先锋聚会\",\"pid\":\"1897630779336609793\",\"pidCode\":\"B05-A40-A04\",\"pidName\":\"聚会\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A05-A01\",\"id\":\"1897631201046401026\",\"leaf\":true,\"name\":\"简约度假\",\"pid\":\"1897630809038360577\",\"pidCode\":\"B05-A40-A05\",\"pidName\":\"度假\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A05-A02\",\"id\":\"1897631250308501505\",\"leaf\":true,\"name\":\"时髦度假\",\"pid\":\"1897630809038360577\",\"pidCode\":\"B05-A40-A05\",\"pidName\":\"度假\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A05-A03\",\"id\":\"1897631305352663041\",\"leaf\":true,\"name\":\"先锋度假\",\"pid\":\"1897630809038360577\",\"pidCode\":\"B05-A40-A05\",\"pidName\":\"度假\",\"sorted\":0,\"status\":0},{\"code\":\"B05-A40-A05-A04\",\"id\":\"1897631329643761666\",\"leaf\":true,\"name\":\"浪漫度假\",\"pid\":\"1897630809038360577\",\"pidCode\":\"B05-A40-A05\",\"pidName\":\"度假\",\"sorted\":0,\"status\":0}]";
        List<SysCategoryModel> model = JSONArray.parseArray(ss, SysCategoryModel.class);



        // 根据标签名字进行分组
        Map<String, List<SysCategoryModel>> map = model.stream().collect(Collectors.groupingBy(SysCategoryModel::getCode));
        List<BatchProductLabel> list = new ArrayList<>();
        List<ParseLabelEntity.ErrorData> errorSpuList = new ArrayList<>();

        for(Map<Integer,String> pLabel: dataList){
            log.info("=============开始遍历：{}", JSONObject.toJSONString(pLabel));
            //每行总列数
            int columnSize = pLabel.keySet().size();
            String productCode = pLabel.get(1);
            String colorCode = pLabel.get(2);

            BatchProductLabel productLabel = new BatchProductLabel();
            List<String> labels = new ArrayList<>();

            // 从标签开始解析
            for(int i = 3; i<columnSize; i++){
                String colData = pLabel.get(i);
                if(StringUtils.isBlank(colData) || Objects.equals(colData, "/")){
                    continue;
                }
                List<String> labelList = Arrays.stream(colData.split(",")).map(String::trim).collect(Collectors.toList());
                // 根据标签名称来匹配数据
                labelList.forEach(v -> {
                    List<SysCategoryModel> category = map.get(v);
                    if(ObjectUtils.isEmpty(category)){
                        ParseLabelEntity.ErrorData data = new ParseLabelEntity.ErrorData();
                        data.setSpu(pLabel.get(1) + pLabel.get(2));
                        data.setErrorMsg(v);
                        errorSpuList.add(data);
                        return;
                    }
                    labels.add(category.get(0).getCode());
                });

            }
            if(labels.size() == 0){
                continue;
            }
            productLabel.setProductCode(productCode);
            productLabel.setColorCode(colorCode);
            productLabel.setLabelList(labels);
            list.add(productLabel);
        }
        return new ParseLabelEntity(list, errorSpuList);
    }


    public void parseExportData(Map<String, String> titleData, JSONArray productData){
        // 解析title
        List<SysCategoryModel> model = iSysBaseAPI.queryAllDSysCategoryByCode(categoryParentCode);
        Map<String, List<SysCategoryModel>> codeMap = model.stream().collect(Collectors.groupingBy(SysCategoryModel::getCode));
        // 获取所有父标签用作标题
        List<String> titleList = model.stream().filter(e -> true == e.isLeaf() && !categoryParentCode.equals(e.getPidCode()))
                .map(e -> e.getPidCode()).distinct().collect(Collectors.toList());
        JSONArray addTitle = new JSONArray();
        for(String title : titleList){
            List<SysCategoryModel> category = codeMap.get(title);
            if(category.isEmpty()){
                continue;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code",category.get(0).getCode());
            jsonObject.put("name",category.get(0).getName());
            addTitle.add(jsonObject);
            titleData.put(category.get(0).getCode(),category.get(0).getName());
        }
        // 匹配数据
        for(Object obj : productData){
            JSONObject product = (JSONObject) obj;
            // 匹配标签
            List<String> labelLevels = new ArrayList<>();
            if(com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotEmpty(product.get("label_levels"))){
                labelLevels = JSONArray.parseArray(product.get("label_levels").toString(), String.class);
            }
            for(Object  obj2 : addTitle){
                JSONObject label = (JSONObject) obj2;
                String sb= "";
                for(String pLabel : labelLevels){
                    if(pLabel.indexOf(label.get("code").toString()) > -1){
                        List<SysCategoryModel> categoryModel = codeMap.get(pLabel);
                        if(ObjectUtils.isEmpty(categoryModel) || categoryModel.size() == 0){
                            continue;
                        }
                        sb += categoryModel.get(0).getName()+",";
                    }
                }
                product.put(label.get("code").toString(),"".equals(sb) ? null:sb.substring(0,sb.length()-1));
            }
        }
    }

    @Override
    public void downFileMarkLabelInDataBase(String url, String keys) throws IOException {
        String filePath = FileParseUtil.downLoadExcel(url);
        final String[] param = {""};
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {
            @Override
            protected void saveData() {
                log.info("=============解析商品标签");
                ParseLabelEntity labelEntity = parseLabelByName(this.cachedDataList);
                //打标 ，导入到库
                if (CollectionUtils.isNotEmpty(labelEntity.getList())) {
                    List<SysProductLabel> saveProductLabels = new ArrayList<>();
                    labelEntity.getList().forEach(
                            v -> {
                                String skc = v.getProductCode() + v.getColorCode();
                                List<ProductSkcResp> goods = productService.findGoodsSkcDetailByIds(Lists.newArrayList(skc));
                                if(CollectionUtils.isEmpty(goods)){
                                    log.error("批量导入商品标签--productCodeColor = {}不存在", v.getProductCode() + v.getColorCode());
                                    return;
                                }
                                v.setProductId(Long.valueOf(goods.get(0).getProduct_id()).toString());
                                saveProductLabels.addAll(packageProductLabel(v));
                            }
                    );
                    List<String> updateIds = labelEntity.getList().stream().filter(v -> StringUtils.isNotBlank(v.getProductId()))
                            .map(e -> e.getProductId()).collect(Collectors.toList());
                    log.info("==========updateIds:{}", JSONObject.toJSONString(updateIds));
                    if(updateIds.size() > 0){
                        try {
                            sysProductLabelMapper.delByProductIdsAndColorNos(labelEntity.getList());
                            sysHandworkProductLabelMapper.delByProductIdsAndColorNos(labelEntity.getList());
                        } catch (Exception e) {
                            log.error("====================更新报错:{}", JSONObject.toJSONString(labelEntity.getList()));
                        }
                        saveBatch(saveProductLabels);
                        sysHandworkProductLabelMapper.batchInsert(saveProductLabels);
                    }
                }

                if(labelEntity.getErrorSpu().size() > 0){
                    String fileName = System.currentTimeMillis() + ".xlsx";
                    EasyExcelUtil.write(fileName, ErrorSpu.class, new IWriteDataExcel<ErrorSpu>() {
                        @Override
                        public List<ErrorSpu> getData() {
                            return labelEntity.getErrorSpu().stream().map(item -> {
                                ErrorSpu spu = new ErrorSpu();
                                spu.setSpu(item.getSpu());
                                spu.setErrorMsg(item.getErrorMsg());
                                return spu;
                            }).collect(Collectors.toList());
                        }
                    });
                    File file = new File(fileName);
                    param[0] = qiniuUtil.upload(file.getPath(), "异常商品"+System.currentTimeMillis() + ".xlsx");
                    file.delete();
                }
            }
        });
        RedisTemplateUtil.setex(redisPoolUtil,keys, param[0],60);
        Files.deleteIfExists(Paths.get(filePath));
    }

    @Override
    public List<SysProductLabelResp> getProductLabelBySkcCodes(List<String> skcCodes) {
        LambdaQueryWrapper<SysProductLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysProductLabel::getSkcCode,skcCodes);
        queryWrapper.eq(SysProductLabel::getIsDel,0);
        List<SysProductLabel> list = baseMapper.selectList(queryWrapper);
        return list.stream().map(item -> {
            SysProductLabelResp resp = new SysProductLabelResp();
            BeanUtils.copyProperties(item,resp);
            return resp;
        }).collect(Collectors.toList());
    }


}
