<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.MProductAliasMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.MProductAlias">
        <id column="ID" property="id" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="NO" property="no" />
        <result column="DESCRIPTION" property="description" />
        <result column="M_PRODUCT_ID" property="mProductId" />
        <result column="M_ATTRIBUTESETINSTANCE_ID" property="mAttributesetinstanceId" />
        <result column="INTSCODE" property="intscode" />
        <result column="FORCODE" property="forcode" />
        <result column="COMMENTS" property="comments" />
        <result column="OWNERID" property="ownerid" />
        <result column="MODIFIERID" property="modifierid" />
        <result column="CREATIONDATE" property="creationdate" />
        <result column="MODIFIEDDATE" property="modifieddate" />
        <result column="ISACTIVE" property="isactive" />
        <result column="LASTCODE" property="lastcode" />
        <result column="BIND_PROALIAS01" property="bindProalias01" />
        <result column="BIND_PROALIAS02" property="bindProalias02" />
        <result column="M_RKSJ" property="mRksj" />
        <result column="IS_CANCEL" property="isCancel" />
        <result column="M_DIM20_ID" property="mDim20Id" />
        <result column="FIRSTDATE" property="firstdate" />
        <result column="IS_WINDOWSTYLE" property="isWindowstyle" />
        <result column="IS_TOC17" property="isToc17" />
        <result column="IS_TOWMS" property="isTowms" />
        <result column="PREDISTIME" property="predistime" />
        <result column="IS_TORFID" property="isTorfid" />
        <result column="PRODUCT_NO" property="productNo" />
        <result column="FACTORYID" property="factoryid" />
        <result column="GOODSNO" property="goodsno" />
        <result column="IS_TOJD" property="isTojd" />
        <result column="IS_JJ" property="isJj" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, NO, DESCRIPTION, M_PRODUCT_ID, M_ATTRIBUTESETINSTANCE_ID, INTSCODE, FORCODE, COMMENTS, OWNERID, MODIFIERID, CREATIONDATE, MODIFIEDDATE, ISACTIVE, LASTCODE, BIND_PROALIAS01, BIND_PROALIAS02, M_RKSJ, IS_CANCEL, M_DIM20_ID, FIRSTDATE, IS_WINDOWSTYLE, IS_TOC17, IS_TOWMS, PREDISTIME, IS_TORFID, PRODUCT_NO, FACTORYID, GOODSNO, IS_TOJD, IS_JJ
    </sql>

</mapper>