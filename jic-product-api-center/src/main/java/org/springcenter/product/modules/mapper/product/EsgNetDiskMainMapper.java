package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.EsgNetDiskDetail;
import org.springcenter.product.modules.model.EsgNetDiskMain;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ESG_NET_DISK_MAIN】的数据库操作Mapper
* @createDate 2025-03-20 10:01:29
* @Entity generator.domain.EsgNetDiskMain
*/
public interface EsgNetDiskMainMapper extends BaseMapper<EsgNetDiskMain> {


    String selectWpPicsByFile1name(@Param("goodSeason") String good_season,
                                                   @Param("file1Name") String file1_name,
                                                   @Param("tymx") String tymx,
                                                   @Param("brand") String arc_brand);

    List<EsgNetDiskDetail> selectPicsByMainId(@Param("mainId") String mainId);
}
