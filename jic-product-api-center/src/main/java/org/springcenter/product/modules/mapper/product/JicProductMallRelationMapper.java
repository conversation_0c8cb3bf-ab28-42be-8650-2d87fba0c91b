package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.api.dto.ExchangeByProductIdResp;
import org.springcenter.product.api.dto.ProductByWeiMenInfoReq;
import org.springcenter.product.modules.context.BojunProductInfo;
import org.springcenter.product.modules.model.JicProductMallRelation;
import org.springcenter.product.modules.model.ProductByWeiMenInfoDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/17 16:02
 */
@Mapper
public interface JicProductMallRelationMapper extends BaseMapper<JicProductMallRelation> {
    String selectSpuByWeiMenGoodId(@Param("weiMenGoodId") String weiMenGoodId);

    List<ProductByWeiMenInfoDto> selectByWeimoInfo(@Param("skus") List<ProductByWeiMenInfoReq> requestData);

    List<ExchangeByProductIdResp> selectWeimoGoodIdAndBJSkuId(@Param("weId") String weId, @Param("list") List<String> productId);

    List<BojunProductInfo> exchangeBojunIdAndName(@Param("wmGoodIds") List<Long> goodsIdList);
}
