<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.BEbStoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.BEbStore">
        <id column="ID" property="id" />
        <result column="STORE_NAME" property="storeName" />
        <result column="LONGITUDE" property="longitude" />
        <result column="LATITUDE" property="latitude" />
        <result column="EB_STATUS" property="ebStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, STORE_NAME, LONGITUDE, LATITUDE, EB_STATUS
    </sql>
    <select id="selectListByStatus" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from jnby.B_EB_STORE where EB_STATUS = 1
    </select>

</mapper>
