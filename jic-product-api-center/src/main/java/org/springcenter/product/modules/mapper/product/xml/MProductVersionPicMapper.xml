<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.MProductVersionPicMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.MProductVersionPic">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="productVersionId" column="PRODUCT_VERSION_ID" jdbcType="VARCHAR"/>
            <result property="picUrl" column="PIC_URL" jdbcType="VARCHAR"/>
            <result property="type" column="TYPE" jdbcType="DECIMAL"/>
            <result property="isDelete" column="IS_DELETE" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PRODUCT_VERSION_ID,PIC_URL,
        TYPE,IS_DELETE,CREATE_TIME,
        UPDATE_TIME
    </sql>
    <update id="delAllPicsByMpIds">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update M_PRODUCT_VERSION_PIC
            set
            IS_DELETE = 1
            where PRODUCT_VERSION_ID = #{item}
        </foreach>
    </update>
</mapper>
