package org.springcenter.product.modules.remote.service;

import org.apache.commons.lang3.tuple.Pair;
import org.springcenter.product.modules.remote.entity.*;
import retrofit2.http.Body;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/4/28 9:22
 */
public interface IBigDataService {

    /**
     * 获取搜索查询的数据
     * @param bigDataSearchReqEntity 查询参数
     * @return 返回
     */
    List<BigDataSearchRespEntity> searchByText(@Body BigDataSearchReqEntity bigDataSearchReqEntity);


    /**
     * 查询综合排序
     * @param bigDataComprehensiveReq 入参
     * @return 返回 具体数据 和 总数
     */
    Pair<List<BigDataComprehensiveData>, Integer> searchComprehensiveList(BigDataComprehensiveReq bigDataComprehensiveReq);


    /**
     * 单品推荐
     * @param bigDataSingleReconReq 入参
     * @return 返回
     */
    List<Integer> searchSingleRecommendation(BigDataSingleReconReq bigDataSingleReconReq);
}
