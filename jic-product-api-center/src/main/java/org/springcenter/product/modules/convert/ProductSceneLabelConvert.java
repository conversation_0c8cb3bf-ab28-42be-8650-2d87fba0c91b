package org.springcenter.product.modules.convert;


import com.jnby.authority.common.system.vo.SysCategoryModel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.modules.model.*;
import org.springcenter.product.util.DateUtil;
import org.springframework.beans.BeanUtils;

import javax.xml.crypto.Data;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/5/19 13:23
 */
public class ProductSceneLabelConvert {
    public static List<QueryValidApplySceneResp> buildChannelConfigRet(List<SceneChannelConfig> channelConfigs) {
        if (CollectionUtils.isEmpty(channelConfigs)) {
            return Collections.emptyList();
        }
        List<QueryValidApplySceneResp> rets = new ArrayList<>();
        channelConfigs.forEach(v -> {
            QueryValidApplySceneResp resp = new QueryValidApplySceneResp();
            resp.setId(Objects.toString(v.getId()));
            resp.setDesc(v.getChannelName());
            rets.add(resp);
        });
        return rets;
    }

    public static List<QueryValidApplySceneResp> buildProductSceneLabelRet(List<ProductSceneLabel> productSceneLabels) {
        if (CollectionUtils.isEmpty(productSceneLabels)) {
            return Collections.emptyList();
        }
        List<QueryValidApplySceneResp> rets = new ArrayList<>();
        productSceneLabels.forEach(v -> {
            QueryValidApplySceneResp resp = new QueryValidApplySceneResp();
            resp.setId(v.getId());
            resp.setDesc(v.getSceneName());
            rets.add(resp);
        });
        return rets;
    }

    public static QuerySceneResp buildQueryScene(ProductSceneLabel productLabelScene, SceneChannelConfig channelConfig) {
        QuerySceneResp resp = new QuerySceneResp();
        BeanUtils.copyProperties(productLabelScene, resp);
        resp.setChannelName(channelConfig.getChannelName());
        return resp;
    }

    public static List<QuerySceneLabelResp> buildSceneLabelList(List<SceneLabelConnection> sceneLabelConnections,
                                                                List<SysCategoryModel> sysCategoryModels) {
        if (CollectionUtils.isEmpty(sceneLabelConnections) || CollectionUtils.isEmpty(sysCategoryModels)) {
            return Collections.emptyList();
        }
        HashMap<String, SysCategoryModel> map = sysCategoryModels.stream().collect(HashMap::new, (k, v) -> k.put(v.getCode(), v), HashMap::putAll);
        if (map.isEmpty()) {
            return Collections.emptyList();
        }

        List<QuerySceneLabelResp> rets = new ArrayList<>();
        sceneLabelConnections.forEach(v -> {
            QuerySceneLabelResp resp = new QuerySceneLabelResp();
            SysCategoryModel sysCategoryModel = map.get(v.getLabelCode());
            if (sysCategoryModel == null) {
                return;
            }
            resp.setId(v.getId());
            resp.setLabelCode(v.getLabelCode());
            resp.setLabelName(sysCategoryModel.getName());
            resp.setLabelType(sysCategoryModel.getCategoryServiceType());
            resp.setSort(v.getSort());
            resp.setIsDeleted(v.getIsDeleted());
            resp.setCreateTime(DateUtil.parseDate(v.getCreateTime(), DateUtil.DATEFORMATE_YYYY_MM_DD));
            rets.add(resp);
        });
        return rets;
    }

    public static List<QuerySceneListResp> buildSceneList(List<ProductSceneLabel> list, List<SceneChannelConfig> channelConfigs) {
        if (CollectionUtils.isEmpty(list) && CollectionUtils.isEmpty(channelConfigs)) {
            return Collections.emptyList();
        }
        HashMap<Integer, String> map = channelConfigs.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getId(), v.getChannelName()), HashMap::putAll);
        if (map.isEmpty()) {
            return Collections.emptyList();
        }

        List<QuerySceneListResp> rets = new ArrayList<>();
        list.forEach(v -> {
            QuerySceneListResp resp = new QuerySceneListResp();
            resp.setSceneKey(v.getSceneKey());
            resp.setId(v.getId());
            resp.setCreator(v.getCreator());
            resp.setSceneName(v.getSceneName());
            resp.setStatus(v.getStatus());
            resp.setRemark(v.getRemark());
            resp.setChannelName(map.get(v.getOwnChannelId()));
            rets.add(resp);
        });
        return rets;
    }

    public static List<SearchLabelSceneResp> buildSearchLabelSceneRet(List<SceneLabelConnection> sceneLabelConnections,
                                                                      List<ProductSceneLabelEntity> productSceneLabelEntities) {
        if (CollectionUtils.isEmpty(sceneLabelConnections) || CollectionUtils.isEmpty(productSceneLabelEntities)) {
            return Collections.emptyList();
        }
        HashMap<String, ProductSceneLabelEntity> map =
                productSceneLabelEntities.stream().collect(HashMap::new, (k, v) -> k.put(v.getId(), v), HashMap::putAll);
        List<SearchLabelSceneResp> rets = new ArrayList<>();
        sceneLabelConnections.forEach(v -> {
            SearchLabelSceneResp resp = new SearchLabelSceneResp();
            resp.setId(v.getId());
            resp.setIsDeleted(v.getIsDeleted());
            resp.setLabelCode(v.getLabelCode());
            resp.setSceneId(v.getSceneId());
            resp.setSort(v.getSort());
            ProductSceneLabelEntity productSceneLabelEntity = map.get(v.getSceneId());
            if (productSceneLabelEntity == null) {
                return;
            }
            resp.setChannelId(productSceneLabelEntity.getOwnChannelId());
            resp.setChannelName(productSceneLabelEntity.getOwnChannelName());
            resp.setSceneName(productSceneLabelEntity.getSceneName());
            rets.add(resp);
        });
        return rets;
    }

    public static List<QueryListSceneLabelResp> buildListLabelResp(List<String> parentsLabel, Map<String, String> map,
                                                                   List<SysCategoryModel> sysCategoryModels,
                                                                   List<SceneLabelConnection> sceneLabelConnections) {
        if (map.isEmpty()) {
            return Collections.emptyList();
        }

        HashMap<String, String> labelMap = sysCategoryModels.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getCode(), v.getName()), HashMap::putAll);
        HashMap<String, SceneLabelConnection> subLabelMap = sceneLabelConnections.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getLabelCode(), v), HashMap::putAll);
        if (labelMap.isEmpty() || subLabelMap.isEmpty())  {
            return Collections.emptyList();
        }

        List<QueryListSceneLabelResp> rets = new ArrayList<>();
        parentsLabel.forEach(v -> {
            String labelOrder = map.get(v);
            if (StringUtils.isBlank(labelOrder)) {
                return;
            }
            QueryListSceneLabelResp parentLabel = new QueryListSceneLabelResp();
            parentLabel.setLabelCode(v);
            parentLabel.setId(labelOrder);
            parentLabel.setSort(Integer.valueOf(labelOrder));
            String labelName = labelMap.get(v);
            if (StringUtils.isBlank(labelName)) {
                return;
            }
            parentLabel.setLabelName(labelName);
            parentLabel.setSort(Integer.valueOf(labelOrder));
            if (subLabelMap.isEmpty()) {
                return;
            }
            
            // 记录当前是否有新标签
            List<QueryListSceneLabelResp> subLabelList = new ArrayList<>();
            AtomicReference<Integer> isNew = new AtomicReference<>(0);
            subLabelMap.entrySet().forEach(x -> {
                if (x.getKey().contains(v)) {
                    String s = labelMap.get(x.getKey());
                    SceneLabelConnection labelEntity = (SceneLabelConnection) subLabelMap.get(x.getKey());
                    if (StringUtils.isBlank(s) || labelEntity == null) {
                        return;
                    }
                    QueryListSceneLabelResp subLabel = new QueryListSceneLabelResp();
                    BeanUtils.copyProperties(labelEntity, subLabel);
                    subLabel.setLabelName(s);
                    if (DateUtil.diffDateByDay(new Date(), labelEntity.getCreateTime()) < 6 && isNew.get() == 0) {
                        isNew.set(1);
                    }
                    subLabelList.add(subLabel);
                }
            });

            parentLabel.setIsNew(isNew.get());
            parentLabel.setSubSceneLabels(subLabelList.stream()
                    .sorted(Comparator.comparing(QueryListSceneLabelResp::getSort)).collect(Collectors.toList()));
            rets.add(parentLabel);
        });

        return rets.stream()
                .sorted(Comparator.comparing(QueryListSceneLabelResp::getSort)).collect(Collectors.toList());
    }


    public static void main(String[] args) {
        int i = DateUtil.diffDateByDay(new Date(), DateUtil.parseDate("2024-02-29 16:44:03", DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS));
        System.out.println("===============2024-02-29 16:44:03==" + i);
        int i1 = DateUtil.diffDateByDay(new Date(), DateUtil.parseDate("2024-03-12 00:00:00", DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS));
        System.out.println("===============2024-03-12 00:00:00==" + i1);
        int i2 = DateUtil.diffDateByDay(new Date(), DateUtil.parseDate("2024-03-06 00:00:00", DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS));
        System.out.println("===============2024-03-06 00:00:00==" + i2);
        int i3 = DateUtil.diffDateByDay(new Date(), DateUtil.parseDate("2024-03-05 00:00:00", DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS));
        System.out.println("===============2024-03-05 00:00:00==" + i3);
        int i4 = DateUtil.diffDateByDay(new Date(), DateUtil.parseDate("2024-03-11 00:00:00", DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS));
        System.out.println("===============2024-03-05 00:00:00==" + i4);
    }
}
