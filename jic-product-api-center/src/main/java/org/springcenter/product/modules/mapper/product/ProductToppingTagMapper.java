package org.springcenter.product.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.product.modules.model.ProductToppingTag;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/15 9:47
 */
@Mapper
public interface ProductToppingTagMapper extends BaseMapper<ProductToppingTag> {
    void batchInsert(@Param("list") List<ProductToppingTag> productToppingTags);

    List<ProductToppingTag> selectByParam(@Param("spu") String spu, @Param("type") Integer type, @Param("status") Integer status,
                                          @Param("nowDay") Date now);

    List<ProductToppingTag> selectListByNotStart(@Param("nowDay") Date date);

    void batchUpdate(@Param("list") List<ProductToppingTag> productToppingTags);

    List<ProductToppingTag> selectFinishedToppingTagAndNotFlow(@Param("nowDay") Date date);

    List<ProductToppingTag> selectFinishedToppingTagAndFlow(@Param("nowDay") Date date);
}
