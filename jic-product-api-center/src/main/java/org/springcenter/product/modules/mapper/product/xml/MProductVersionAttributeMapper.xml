<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.MProductVersionAttributeMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.MProductVersionAttribute">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="code" column="CODE" jdbcType="DECIMAL"/>
            <result property="value" column="VALUE" jdbcType="DECIMAL"/>
            <result property="isDelete" column="IS_DELETE" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,CODE,VALUE,
        IS_DELETE,CREATE_TIME,UPDATE_TIME
    </sql>
</mapper>
