package org.springcenter.product.modules.service;

import com.alibaba.fastjson.JSONArray;
import com.jnby.common.CommonRequest;
import org.springcenter.product.api.SingleProductRecommendationReq;
import org.springcenter.product.modules.context.ProductApiContext;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.modules.entity.*;
import com.jnby.common.Page;
import org.springcenter.product.modules.model.BoxMProduct;
import org.springcenter.product.modules.model.MAttributesetinstance;
import org.springcenter.product.modules.context.GoodsAiSingleContext;
import org.springcenter.product.modules.context.GoodsContext;

import java.util.HashMap;
import java.util.List;

/**
 * 产品service
 * <AUTHOR>
 * @version 1.0
 * @date 3/15/21 11:16 AM
 */
public interface IProductService {

    /**
     * 查询产品色号
     * @param color
     * @param size
     * @return
     */
    MAttributesetinstance findProductAttr(String color, String size);


    /**
     * 根据查询条件获取唯一商品
     * @return
     */
    BoxMProduct findOneByCondition(BoxMProduct product);

    /**
     * 批量查询商品数据
     * @param skuIds
     * @return
     */
    List<BoxMProduct> selectGoodsListBySkuIds(List<Long> skuIds);


    /**
     * 搜索ai单品推荐
     * @param context
     * @param page
     * @return
     */
    List<GoodsAiRecEntity> searchAiBySingleGoods(GoodsAiSingleContext context, Page page);

    /**
     * 通过UNIONID 查询look推荐
     * @param unionId
     * @return
     */
    List<UserGoodsLookRecEntity> searchAiLook(String unionId);

    /**
     * 查询商品信息(分页)
     * @param context
     * @return
     */
    List<GoodSpuResp> searchGoods(QueryGoodsReq context, Page page);

    /**
     * 查询SKC商品信息
     * @param context
     * @param page
     * @return
     */
    List<ProductSkcResp> searchGoodsSkc(QueryGoodsReq context, Page page, String component);

    /**
     * 查询打标的样衣数据
     * @param context
     * @param page
     * @return
     */
    List<SampleProductSkcResp> searchSampleProductSkc(SampleProductReq context, Page page, String component);

    /**
     * 查询样衣过滤条件
     * @return
     */
    SampleProductFilterOptionResp querySampleProductFilter();

    /**
     * 监听门店库存变更，修改门店商品标识
     * @param storeId 门店ID
     * @param productId 商品ID
     */
    void changeStoreGoodsByListenerStorage(long storeId, long productId);

    /**
     * 查询SPU下所有的SKU集合
     * @param productId
     * @return
     */
    List<GoodSpuResp.Sku> findSkuListByProductId(String productId);

    /**
     * 根据noList 获取详情数据
     * @param noList 条码no批量获取
     * @return
     */
    List<BoxMProduct> findByNoList(List<String> noList);

    /**
     * 根据商品id获取详情数据
     * @param productId
     * @return
     */
    GoodSpuResp findGoodByProductId(String productId);

    /**
     * 查询商品SKC详情，含有标签
     * @param id name+colorno
     * @return
     */
    List<ProductSkcResp> findGoodsSkcDetailByIds(List<String> id);

    /**
     * 通过款号获取商品
     * @param productCode
     * @return
     */
    GoodSpuEntity findGoodByProductCode(String productCode);


    /**
     * 获取api商品数据
     * @param page
     * @param context
     * @return
     */
    List<ProductApiEntity> getProductApiList(Page page, ProductApiContext context);

    /**
     * @Description: 根据属性查询
     * @Author: brian
     * @Date: 2021/10/26 13:23
     * @params: [record]
     * @return: java.util.List<org.springcenter.product.infrastructure.bojun.model.BoxMProduct>
     */
    List<BoxMProduct> findByCondition(BoxMProduct record);

    List<ProductSkcResp> getEsDataByProductIdAndColorNo(String productid, String colorNo);

    /**
     * 根据skcCode查询  批量查询
     */
    List<BoxMProduct> batchGetProductByNames(List<String> productCodes);

    /**
     * 通过国际码批量获取
     * @param gbCode
     * @return
     */
    List<BoxMProduct> batchGetProductByGbCode(List<String> gbCode);

    /**
     * 查询相似商品
     * @param similarProductReq 查询
     * @return 返回
     */
    List<SimilarProductResp> searchSimilarGoods(SimilarProductReq similarProductReq);

    /**
     * 查询经销库存
     * @param requestData 入参
     * @return 返回
     */
    HashMap<Long, ProductAgentStockResp> searchSkuAgentStorageByIds(ProductAgentStockReq requestData);

    /**
     * 查询同款商品
     * @param requestData 查询参数
     * @return 返回
     */
    List<SameSpuProductResp> searchSameSpuProduct(SameSpuProductReq requestData);

    /**
     * 监听门店库存变更，修改门店商品标识
     * @param storeId 门店ID
     * @param productId 商品ID
     * @param faStoreId  库存ID
     */
    void changeStoreGoodsByListenerStorage(long storeId, long productId, Long faStoreId);

    /**
     * 查询商品详情
     * @param requestData 商品id
     * @return 返回
     */
    GoodSpuDetailEntity searchProductDetail(String requestData);

    /**
     * 获取菜单选择
     * @param request 入参
     * @return 返回
     */
    JSONArray getSizeNos(CommonRequest request);

    /**
     * 根据品牌和商品id 查询微盟goodId
     */
    List<SearchWeiMoGoodInfoResp> selectWeiMoGoodIdByNames(List<SearchWeiMoGoodInfoReq> req);

    /**
     * 根据微盟店铺和skuId转换伯俊skuId
     * @param requestData 入参
     * @return 返回
     */
    List<ExchangeProductIdByWeiMoResp> exchangeProductIdByWeiMo(ExchangeProductIdByWeiMoReq requestData);


    /**
     * 根据品牌和商品id 查询微盟goodId
     */
    List<SearchWeiMoGoodInfoResp> selectWeiMoInfoByNames(List<SearchWeiMoGoodInfoReq> requestData);


    /**
     * 根据品牌和productId过滤weimoGoodId和skuId
     * @param requestData 入参
     * @return 返回
     */
    List<ExchangeByProductIdResp> getWeiMoGoodIdAndBJSkuId(ExchangeByProductIdReq requestData);


    /**
     * 根据sku获取商品信息
     * @param request sku
     * @return 返回所需要的商品信息
     */
    SkuProductInfoResp getProductInfoBySku(CommonRequest<String> request);

    /**
     * 根据款号判断当前商品是否是追单款 是返回true 否返回false
     * @param request 款号
     * @return 返回boolean
     */
    Boolean judgeIsZhuiDan(String request);


    /**
     * 根据追单商品查询原款详情信息
     * @param requestData 追单款号
     * @return 返回
     */
    GoodSpuDetailEntity getProductDetailByZhuiDan(ZhuiDanProductInfo requestData);

    /**
     * 根据款号判断当前商品是否存在追单款 是返回true 否返回false
     * @param request 款号
     * @return 返回boolean
     */
    Boolean judgeIsExistZhuiDan(String request);

    /**
     * 根据条件返回商品ids--提供海外使用
     * @param goodsReq 入参
     * @return 返回
     */
    List<String> queryGoodsIds(QueryGoodsReq goodsReq);


    /**
     * 根据微盟goodId获取伯俊productId
     * @param requestData 微盟商品id
     * @return 伯俊商品id
     */
    List<BJProductIdByWMGoodIdResp> queryBJProductIdByWMGoodId(List<Long> requestData);

    /**
     * 批量获取对应商品码的skuId
     * @param requestData 入参
     * @return 返回
     */
    List<SkuInfoByProductCodeResp> querySkuIdByBjProductCode(List<String> requestData);

    /**
     * 根据商品id判断当前搭配师商品是否可售卖
     * @param requestData 商品id
     * @return 返回是否可售
     */
    List<FashionerProductIsCanSellResp> queryFashionerIsCanSell(List<Long> requestData);

    /**
     * 根据产品款号查询商品价格和id
     * @param requestData 入参
     * @return 返回
     */
    List<ProductInfoByNo> querySpuNoPrice(List<String> requestData);

    /**
     * 根据商品id查询商品卖点信息
     * @param requestData 商品id
     * @return 商品卖点信息
     */
    ProductSalesInfoResp searchProductSalesInfo(String requestData);


    /**
     * 高分局追单skuid查询原单sku信息
     * @param requestData 追单skuId
     * @return 返回
     */
    List<ZhuiDanSkuInfoResp> getSkuInfoByZdSkuId(List<Long> requestData);

    /**
     * 获取商品微盟状态信息
     * @param requestData 入参
     * @return 返回
     */
    List<ProductWscStatusInfoResp> getProductWscStatusInfo(ProductWscStatusInfoReq requestData);

    /**
     * 单品推荐 - for自选盒子
     * @param requestData 入参
     * @return 返回
     */
    List<GoodSpuResp> getSingleProductRecommendation(SingleProductRecommendationReq requestData);
}
