package org.springcenter.product.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/15 10:07
 */
public interface IProductToppingTagService {

    /**
     * 增加指定标签
     * @param req 请求参数
     * @return 返回
     */
    List<String> addToppingTag(AddToppingTagReq req);


    /**
     * 更新置顶标签
     * @param requestData 请求参数
     * @return 返回
     */
    Boolean updateToppingTag(AddToppingTagReq requestData);

    /**
     * 查询置顶标签
     * @param requestData id
     * @return 返回参数
     */
    QueryToppingTagResp queryToppingTag(QueryToppingTagReq requestData);

    /**
     * 获取置顶列表
     * @param requestData 查询参数
     * @return 返回
     */
    List<QueryToppingTagListResp> queryToppingTagList(QueryToppingTagListReq requestData, Page page);

    /**
     * 开启/关闭标签
     * @param requestData 参数
     * @return 返回
     */
    Boolean switchToppingTag(SwitchToppingTagReq requestData);


    /**
     * 更新成开启状态
     */
    void switchOpenStatus();

    /**
     * 更新成关闭状态
     */
    void switchShutDownStatus();

    /**
     * 操作流转定时任务
     */
    void flowProductToppingTag();

    /**
     * 获取标签类型
     * @return
     */
    List<ToppingTagTypeResp> queryToppingTagType();
}
