<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.MProductVersionAttrSubMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.MProductVersionAttrSub">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="productVersionId" column="PRODUCT_VERSION_ID" jdbcType="VARCHAR"/>
            <result property="proVerAttrId" column="PRO_VER_ATTR_ID" jdbcType="VARCHAR"/>
            <result property="code" column="CODE" jdbcType="VARCHAR"/>
            <result property="value" column="VALUE" jdbcType="VARCHAR"/>
            <result property="isDelete" column="IS_DELETE" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PRODUCT_VERSION_ID,PRO_VER_ATTR_ID,
        CODE,VALUE,IS_DELETE,
        CREATE_TIME,UPDATE_TIME
    </sql>
</mapper>
