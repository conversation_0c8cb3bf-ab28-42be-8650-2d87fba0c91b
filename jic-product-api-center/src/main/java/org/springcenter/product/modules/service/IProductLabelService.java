package org.springcenter.product.modules.service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springcenter.product.api.dto.MarkingLabelReq;
import org.springcenter.product.api.dto.SysProductLabelResp;
import org.springcenter.product.modules.entity.BatchProductLabel;
import org.springcenter.product.modules.model.SysHandworkProductLabel;
import org.springcenter.product.modules.model.SysProductLabel;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/8/13 11:09
 */
public interface IProductLabelService extends IService<SysProductLabel> {

    /**
     * @Description: 商品打标
     * @Author: brian
     * @Date: 2021/8/13 14:21
     * @params: [reqData, userId]
     * @return: void
     */
    void markingProductLabel(MarkingLabelReq reqData, String userId);


    /**
     * 根据excel URL解析商品打标数据
     * @param fileUrl
     * @param keys
     * @return
     */
    void downFileTransferMarkLabel(String fileUrl, String keys) throws IOException;

    /**
     * 商品批量打标
     * @param batchProductLabelList
     * @param isImport true,表示需要保留原有的标签，新增导入标签，false表示全部清空已所选标签为主
     * @return
     */
    boolean batchLabeling(List<BatchProductLabel> batchProductLabelList, boolean isImport);

    /**
     * 匹配导出商品标签和标题数据
     * @param titleData
     * @param productData
     */
    void parseExportData(Map<String, String> titleData, JSONArray productData);


    /**
     * 更新商品在数据库的标签
     * @param url 路径
     * @param keys key
     */
    void downFileMarkLabelInDataBase(String url, String keys) throws IOException;

    /**
     * 通过skc批量查询商品标签
     * @param skcCodes
     * @return
     */
    List<SysProductLabelResp> getProductLabelBySkcCodes(List<String> skcCodes);
}
