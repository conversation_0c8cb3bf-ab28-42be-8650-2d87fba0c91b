<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.SysProductLabelMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.SysProductLabel">
    <result column="ID" jdbcType="OTHER" property="id" />
    <result column="PRODUCT_ID" jdbcType="OTHER" property="productId" />
    <result column="LABEL_ID" jdbcType="OTHER" property="labelId" />
    <result column="LABEL_LEVEL" jdbcType="OTHER" property="labelLevel" />
    <result column="IS_DEL" jdbcType="DECIMAL" property="isDel" />
    <result column="CREATE_BY" jdbcType="OTHER" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="OTHER" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="label_from" jdbcType="INTEGER" property="labelFrom" />
    <result column="label_type" jdbcType="INTEGER" property="labelType" />
    <result column="skc_code" jdbcType="OTHER" property="skcCode" />
  </resultMap>

  <insert id="insertSelective" parameterType="org.springcenter.product.modules.model.SysProductLabel">
    insert into SYS_PRODUCT_LABEL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="productId != null">
        PRODUCT_ID,
      </if>
      <if test="labelId != null">
        LABEL_ID,
      </if>
      <if test="labelLevel != null">
        LABEL_LEVEL,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="skcCode != null">
        skc_code,
      </if>
      <if test="labelFrom != null">
        label_from,
      </if>
      <if test="labelType != null">
        label_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=OTHER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=OTHER},
      </if>
      <if test="labelId != null">
        #{labelId,jdbcType=OTHER},
      </if>
      <if test="labelLevel != null">
        #{labelLevel,jdbcType=OTHER},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=OTHER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=OTHER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="skcCode != null">
        #{skcCode,jdbcType=OTHER},
      </if>
      <if test="labelFrom != null">
        #{labelFrom,jdbcType=INTEGER},
      </if>
      <if test="labelType != null">
        #{labelType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <select id="delByProductIds" parameterType="java.util.List">
      update SYS_PRODUCT_LABEL
      set is_del = 1
      where is_del = 0
      <if test="productIds != null and productIds.size() > 0">
        and product_id in
        <foreach collection="productIds" item="id" open="(" close=")" separator=",">
          #{id}
        </foreach>
      </if>
  </select>



    <update id="delByProductIdsAndColorNos">
    <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
      update SYS_PRODUCT_LABEL
      set is_del = 1,
      UPDATE_TIME = sysdate
      where product_id = #{item.productId} and skc_code = concat(#{item.productCode},#{item.colorCode})
    </foreach>
  </update>

  <select id="selectByDistinctSkcCode" resultType="java.lang.String">
      SELECT DISTINCT SKC_CODE
          FROM
            SYS_PRODUCT_LABEL
      WHERE IS_DEL = 0
      ORDER BY SKC_CODE desc
  </select>

</mapper>