<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.FabWearingImgInfoLogMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.FabWearingImgInfoLog">
        <id column="ID" property="id" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="NAME" property="name" />
        <result column="IMAGES" property="images" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="OPERATORS" property="operators" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="FAB_WEARING_IMG_INFO_ID" property="fabWearingImgInfoId" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, PRODUCT_ID, NAME, IMAGES, CREATE_TIME, UPDATE_TIME, OPERATORS, IS_DELETED, FAB_WEARING_IMG_INFO_ID
    </sql>
</mapper>
