package org.springcenter.product.modules.service;


import com.jnby.common.Page;
import org.springcenter.product.api.dto.*;

import java.util.List;
import java.util.Map;

public interface IProductFabService {


    /**
     * 导出对应条件fab的品类信息
     * @param requestData 入参
     * @param page 分页
     * @return 返回
     */
    ProductFabResp exportProductFabInfo(QueryGoodsFabReq requestData, Page page);

    /**
     * 根据款号查询商品的fab信息
     * @param requestData 商品款号
     * @return 返回
     */
    ProductSpuFabResp queryFabInfo(ProductFabInfoReq requestData);

    /**
     * 根据商品id批量查询重点面料
     * @param requestData 入参
     * @return 返回
     */
    List<FabProductFabricInfoRep> queryFabricInfo(BatchQueryFabInfoReq requestData);

    /**
     * 根据商品id批量查询重点图案
     * @param requestData 入参
     * @return 返回
     */
    List<FabProductPatternInfoRep> queryBatchPatternInfo(BatchQueryFabInfoReq requestData);

    /**
     * 查询商品货季
     * @return 返回
     */
    List<String> queryMerchandiseSeason();

    /**
     * 导出fab上新表格
     * @param keys key
     */
    void exportProductFabNewArrivalInfo(String keys, QueryGoodsFabEsReq req);

    /**
     * 根据款号查询商品的图片
     * @param requestData 入参
     * @return 返回
     */
    ProductSpuFabImgInfoResp queryFabImgInfo(ProductSpuFabImgInfoReq requestData);


    /**
     * 根据款号批量查询商品的fab信息
     * @param productIds 商品id
     * @return 返回
     */
    List<ProductSpuFabResp> batchQueryFabInfo(List<String> productIds, String key);


    /**
     * 根据面料数据查询相关的skc
     * @param requestData 面料数据
     * @return 返回
     */
    List<FabSkcByParamInfoResp> querySkcByFabricInfo(FabProductFabricInfoRep requestData);


    /**
     * 根据图案中心获取相关skc
     * @param requestData 图案信息
     * @return 返回
     */
    List<FabSkcByParamInfoResp> querySkcByPatternInfo(FabProductPatternInfoRep requestData);

    /**
     * 根据参数获取
     * @param requestData 入参
     * @return 返回
     */
    List<String> queryMerchandiseByParam(String requestData);


    /**
     * 根据搭配图查询重点图片
     * @param requestData 搭配图
     * @return 返回
     */
    ProductSpuKeyDpFabResp queryKeyCombinations(ProductSpuKeyDpFabReq requestData);

    /**
     * 根据据商品款号 查询商品名称等信息
     * @param requestData 款号
     * @return 返回
     */
    List<FabInfoByName> getFabInfoByName(List<String> requestData);

    /**
     * 根据款号查询网盘图片
     * @param names 款号
     * @param isMatchAndDetail 是否查细节和搭配图细节
     * @param isPngs 是否查一览图
     * @return 返回
     */
    Map<String, BuildFabImgInfo> buildFabImgInfo(List<String> names, Boolean isMatchAndDetail, Boolean isPngs);

    /**
     * 单独增加一个款
     * @param requestData 款号
     * @return 返回
     */
    ProductSpuFabResp addSingleFab(String requestData);
}
