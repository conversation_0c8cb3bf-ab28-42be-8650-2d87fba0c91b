package org.springcenter.product.modules.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date:2022/12/9 17:19
 */
@Data
@TableName(value = "M_PRODUCT_VERSION_CITATIONS")
public class MProductVersionCitations implements Serializable {


    /**
     * 引用次数
     */
    @TableField(value = "CITATIONS")
    private Integer citations;


    /**
     * 版型号
     */
    @TableField(value = "MODEL_NUMBER")
    private String modelNumber;

    private static final long serialVersionUID = 1L;
}
