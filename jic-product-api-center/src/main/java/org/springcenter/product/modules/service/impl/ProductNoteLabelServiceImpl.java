package org.springcenter.product.modules.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.common.system.vo.SysCategoryModel;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.api.dto.LabelNoteReq;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.modules.mapper.product.ProductNoteLabelMapper;
import org.springcenter.product.modules.model.LabelNoteInfo;
import org.springcenter.product.modules.service.IProductNoteLabelService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/3/25 9:49
 */
@Slf4j
@Service
public class ProductNoteLabelServiceImpl implements IProductNoteLabelService {

    @Autowired
    private ProductNoteLabelMapper productNoteLabelMapper;

    @Resource
    private ISysBaseAPI sysBaseAPI;

    @Value(value = "${label.note.leaf.tag}")
    private String labelNoteLeafTag;

    @Override
    public void updateLabelNote(LabelNoteReq requestData) {
        // 1、判断当前是都是叶子节点
        judgeIsLeaf(requestData);

        // 2、处理数据
        LabelNoteInfo noteInfo = Preconditions
                .checkNotNull(productNoteLabelMapper.selectById(requestData.getId()), "当前id的数据为空");
        noteInfo.setUpdateTime(new Date());
        noteInfo.setLabelNote(requestData.getLabelNote());
        noteInfo.setLabelName(requestData.getLabelName());
        noteInfo.setLabelId(requestData.getLabelId());
        noteInfo.setOperator(requestData.getOperator());
        productNoteLabelMapper.updateById(noteInfo);

    }

    private void judgeIsLeaf(LabelNoteReq requestData) {
        List<SysCategoryModel> sysCategoryModels2 = sysBaseAPI.queryAllDSysCategoryByCode(requestData.getLabelCode());
        if (CollectionUtils.isNotEmpty(sysCategoryModels2)) {
            throw new RuntimeException("当前标签下还有标签值，注释只能在叶子结点生效");
        }
    }

    @Override
    public void addLabelNote(LabelNoteReq requestData) {
        // 1、判断当前是都是叶子节点
        judgeIsLeaf(requestData);

        // 2、处理数据
        LabelNoteInfo info = new LabelNoteInfo();
        info.setLabelCode(requestData.getLabelCode());
        info.setLabelNote(requestData.getLabelNote());
        info.setLabelName(requestData.getLabelName());
        info.setLabelId(requestData.getLabelId());
        info.setOperator(requestData.getOperator());
        info.setCreateTime(new Date());
        info.setUpdateTime(new Date());
        info.setId(IdLeaf.getDateId(labelNoteLeafTag));
        productNoteLabelMapper.insert(info);
    }

    @Override
    public List<LabelNoteReq> searchLabelNote(List<String> requestData) {
        QueryWrapper<LabelNoteInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("LABEL_CODE", requestData);
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        List<LabelNoteInfo> labelNoteInfos = productNoteLabelMapper.selectList(queryWrapper);
        return CollectionUtils.isEmpty(labelNoteInfos) ? Collections.emptyList() : buildRet(labelNoteInfos);
    }

    private List<LabelNoteReq> buildRet(List<LabelNoteInfo> labelNoteInfos) {
        List<LabelNoteReq> rets = new ArrayList<>();
        labelNoteInfos.forEach(v -> {
            LabelNoteReq labelNoteReq = new LabelNoteReq();
            BeanUtils.copyProperties(v, labelNoteReq);
            rets.add(labelNoteReq);
        });

        return rets;
    }
}
