<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.MQueriesAttrConnectionMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.MQueriesAttrConnection">
            <id property="id" column="ID" />
            <result property="attributeId" column="ATTRIBUTE_ID" />
            <result property="attributeCode" column="ATTRIBUTE_CODE" />
            <result property="attributeName" column="ATTRIBUTE_NAME" />
            <result property="bigCategoryId" column="BIG_CATEGORY_ID" />
            <result property="bigCategoryCode" column="BIG_CATEGORY_CODE" />
            <result property="bigCategoryName" column="BIG_CATEGORY_NAME" />
            <result property="smallCategoryId" column="SMALL_CATEGORY_ID" />
            <result property="smallCategoryCode" column="SMALL_CATEGORY_CODE" />
            <result property="smallCategoryName" column="SMALL_CATEGORY_NAME" />
            <result property="connectAttributeId" column="CONNECT_ATTRIBUTE_ID" />
            <result property="connectAttributeCode" column="CONNECT_ATTRIBUTE_CODE" />
            <result property="connectAttributeName" column="CONNECT_ATTRIBUTE_NAME" />
            <result property="type" column="TYPE" />
            <result property="isDelete" column="IS_DELETE" />
            <result property="createTime" column="CREATE_TIME" />
            <result property="updateTime" column="UPDATE_TIME" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, BIG_CATEGORY_ID, BIG_CATEGORY_CODE, SMALL_CATEGORY_ID, SMALL_CATEGORY_CODE,
        ATTRIBUTE_ID, ATTRIBUTE_CODE, TYPE, IS_DELETE,CREATE_TIME, UPDATE_TIME, BIG_CATEGORY_NAME,
        SMALL_CATEGORY_NAME, ATTRIBUTE_NAME, CONNECT_ATTRIBUTE_ID, CONNECT_ATTRIBUTE_CODE, CONNECT_ATTRIBUTE_NAME
    </sql>

    <select id="selectStatsListByParams" resultMap="BaseResultMap">
        select a.ID as ATTRIBUTE_ID, a.CODE as ATTRIBUTE_CODE, a.NAME as ATTRIBUTE_NAME, a.IS_DELETE as IS_DELETE  from (
        select  ID, CODE, NAME, IS_DELETE from M_QUERIES_TAB where type = 3 and pid is null) a
        left join (
            select <include refid="Base_Column_List"></include> from M_QUERIES_ATTR_CONNECTION where IS_DELETE = 0 and type = 1
        ) b
        on  a.ID = b.ATTRIBUTE_ID and b.ATTRIBUTE_CODE = a.CODE
        where 1 = 1
        <if test="bigCategoryId != null ">
            and b.BIG_CATEGORY_ID = #{bigCategoryId}
        </if>
        <if test="smallCategoryId != null">
            and b.SMALL_CATEGORY_ID = #{smallCategoryId}
        </if>
        <if test="isDisabled != null">
            and a.IS_DELETE = #{isDisabled}
        </if>
        group by a.ID, a.CODE, a.NAME, a.IS_DELETE
        order by a.ID desc
    </select>

    <select id="selectStatsListByAttributeList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from M_QUERIES_ATTR_CONNECTION
        where IS_DELETE = 0
        <if test="attributeIds != null and attributeIds.size() > 0">
            and ATTRIBUTE_ID in
            <foreach collection="attributeIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="attributeCodes != null and attributeCodes.size() > 0">
            and ATTRIBUTE_CODE in
            <foreach collection="attributeCodes" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectBySmallCateCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from M_QUERIES_ATTR_CONNECTION where SMALL_CATEGORY_CODE = #{smallCategoryCode}
        and is_delete = 0
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update M_QUERIES_ATTR_CONNECTION
            set
            IS_DELETE = #{item.isDelete},
            <if test="item.connectAttributeName != null and  item.connectAttributeName != ''">
                CONNECT_ATTRIBUTE_NAME = #{item.connectAttributeName},
            </if>
            UPDATE_TIME = sysdate
            where ID = #{item.id}
        </foreach>
    </update>


    <update id="batchUpdateBySceAttrIds">
        update M_QUERIES_ATTR_CONNECTION
        set
        IS_DELETE = 1,
        UPDATE_TIME = sysdate
        where
        CONNECT_ATTRIBUTE_ID in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="updateByAttrIds">
        update M_QUERIES_ATTR_CONNECTION
        set
        IS_DELETE = 1,
        UPDATE_TIME = sysdate
        where
            ATTRIBUTE_ID = #{id}
    </update>
    <update id="updateValidByIds">
        update M_QUERIES_ATTR_CONNECTION
        set
        IS_DELETE = 0,
        UPDATE_TIME = sysdate
        where
        ID in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO M_QUERIES_ATTR_CONNECTION
            (ID, BIG_CATEGORY_ID, BIG_CATEGORY_CODE, SMALL_CATEGORY_ID, SMALL_CATEGORY_CODE,
            ATTRIBUTE_ID, ATTRIBUTE_CODE, TYPE, CREATE_TIME, UPDATE_TIME, BIG_CATEGORY_NAME,
            SMALL_CATEGORY_NAME, ATTRIBUTE_NAME, CONNECT_ATTRIBUTE_ID, CONNECT_ATTRIBUTE_CODE, CONNECT_ATTRIBUTE_NAME) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.bigCategoryId,jdbcType=DECIMAL}, #{item.bigCategoryCode,jdbcType=VARCHAR},
            #{item.smallCategoryId,jdbcType=DECIMAL}, #{item.smallCategoryCode,jdbcType=VARCHAR}, #{item.attributeId,jdbcType=DECIMAL},
            #{item.attributeCode,jdbcType=VARCHAR}, #{item.type,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.bigCategoryName,jdbcType=VARCHAR}, #{item.smallCategoryName,jdbcType=VARCHAR},
            #{item.attributeName,jdbcType=VARCHAR}, #{item.connectAttributeId,jdbcType=DECIMAL}, #{item.connectAttributeCode,jdbcType=VARCHAR},
            #{item.connectAttributeName,jdbcType=VARCHAR})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <select id="selectByAttrId" resultType="java.lang.String">
        select ID
        from M_QUERIES_ATTR_CONNECTION where ATTRIBUTE_ID = #{attrId}
        and is_delete = 0
    </select>

    <select id="selectConnectByAttrIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
            from M_QUERIES_ATTR_CONNECTION
        where ATTRIBUTE_ID = #{attrId}
        and is_delete = 0
    </select>


    <select id="selectBySmallCategory" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from M_QUERIES_ATTR_CONNECTION
        where SMALL_CATEGORY_ID = #{id}
        and is_delete = 0
    </select>

</mapper>
