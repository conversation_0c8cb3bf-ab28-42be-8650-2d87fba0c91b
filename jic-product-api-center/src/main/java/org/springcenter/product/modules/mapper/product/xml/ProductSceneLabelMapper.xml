<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.product.ProductSceneLabelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.ProductSceneLabel">
        <id column="ID" property="id" />
        <result column="SCENE_NAME" property="sceneName" />
        <result column="OWN_CHANNEL_ID" property="ownChannelId" />
        <result column="REMARK" property="remark" />
        <result column="SCENE_KEY" property="sceneKey" />
        <result column="CREATOR" property="creator" />
        <result column="UPDATER" property="updater" />
        <result column="STATUS" property="status" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SCENE_NAME, OWN_CHANNEL_ID, REMARK, SCENE_KEY, CREATOR, UPDATER, STATUS, CREATE_TIME, UPDATE_TIME, IS_DELETED
    </sql>
    <select id="selectByParam" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from PRODUCT_SCENE_LABEL
        where IS_DELETED = 0
        <if test="sceneName != '' and sceneName != null">
            and SCENE_NAME like #{sceneName}
        </if>
        <if test="ownChannelId != null">
            and OWN_CHANNEL_ID = #{ownChannelId}
        </if>
        order by CREATE_TIME desc
    </select>

    <select id="selectSceneLabelEntityByIds"
            resultType="org.springcenter.product.modules.model.ProductSceneLabelEntity">
        select a.ID, a.SCENE_NAME as sceneName, a.OWN_CHANNEL_ID as ownChannelId, b.CHANNEL_NAME as ownChannelName
        from PRODUCT_SCENE_LABEL a
        left join SCENE_CHANNEL_CONFIG b on a.OWN_CHANNEL_ID = b.ID
        where a.ID in
        <foreach collection="list" item="list" open="(" close=")" separator=",">
            #{list}
        </foreach>
        and a.IS_DELETED = 0 and b.IS_DELETED = 0 and a.STATUS = 0
    </select>


</mapper>
