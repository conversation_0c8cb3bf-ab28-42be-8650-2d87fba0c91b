package org.springcenter.product.modules.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springcenter.product.modules.mapper.product.MPQueAttrValueConnectMapper;
import org.springcenter.product.modules.mapper.product.MProductVersionMapper;
import org.springcenter.product.modules.model.MPQueAttrValueConnect;
import org.springcenter.product.modules.model.MProductVersion;
import org.springcenter.product.modules.model.MQueriesAttrConEntity;
import org.springcenter.product.modules.model.ProductVersionEntity;
import org.springcenter.product.modules.repository.IMProductVersionRepository;
import org.springcenter.product.api.enums.MQueriesTabFilterEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【M_PRODUCT_VERSION(版型库表)】的数据库操作Service实现
* @createDate 2022-12-05 09:43:30
*/
@Service
public class MProductVersionRepositoryImpl implements IMProductVersionRepository {

    @Autowired
    private MProductVersionMapper mProductVersionMapper;

    @Autowired
    private MPQueAttrValueConnectMapper mpQueAttrValueConnectMapper;

    @Override
    public List<ProductVersionEntity> queryList() {
        return mProductVersionMapper.selectQueryList();
    }

    @Override
    public List<ProductVersionEntity> queryListByParams(List<String> sampleNos, List<String> attrs,
                                                        List<String> brandList, List<String> categoryList, String sort,
                                                        String name, Integer isClosed) {
        MQueriesTabFilterEnum code = MQueriesTabFilterEnum.getByCode(sort);
        Integer sortInt = 0;
        if (code != null) {
            sortInt = code.getDealFlag();
        }
        isClosed = isClosed == 1 ? null : 0;
        return mProductVersionMapper.selectQueryListByParams(sampleNos, attrs, brandList, categoryList, sortInt, name, isClosed);
    }

    @Override
    public MProductVersion queryDetail(String id) {
        QueryWrapper<MProductVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return mProductVersionMapper.selectOne(queryWrapper);
    }

    @Override
    public Boolean updateProductVersion(MProductVersion mProductVersion) {
        return mProductVersionMapper.updateByEntityId(mProductVersion) > 0;
    }

    @Override
    public List<MProductVersion> queryByParams(String modelNumber) {
        QueryWrapper<MProductVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("MODEL_NUMBER", modelNumber);
        return mProductVersionMapper.selectList(queryWrapper);
    }

    @Override
    public List<MProductVersion> queryAll() {
        QueryWrapper<MProductVersion> queryWrapper = new QueryWrapper<>();
        return mProductVersionMapper.selectList(queryWrapper);
    }

    @Override
    public List<MProductVersion> queryByModelNumber(String modelNumber) {
        QueryWrapper<MProductVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("MODEL_NUMBER", modelNumber);
        return mProductVersionMapper.selectList(queryWrapper);
    }

    @Override
    public void batchSaveValueConnects(List<MPQueAttrValueConnect> mpQueAttrValueConnects) {
        mpQueAttrValueConnectMapper.batchInsert(mpQueAttrValueConnects);
    }

    @Override
    public void delAllMPValueConnectByMpIds(List<String> ids) {
        mpQueAttrValueConnectMapper.delAllMPValueConnectByMpIds(ids);
    }

    @Override
    public List<MQueriesAttrConEntity> queryMpAttrByMPId(String mpId) {
        return mpQueAttrValueConnectMapper.selectByMpId(mpId);
    }

    @Override
    public void batchUpdateValue(List<MPQueAttrValueConnect> valueConnects) {
         mpQueAttrValueConnectMapper.batchUpdateValue(valueConnects);
    }

    @Override
    public List<MPQueAttrValueConnect> selectValueByProductId(List<ProductVersionEntity> rets) {
        return mpQueAttrValueConnectMapper.selectByProductId(rets);
    }


}
