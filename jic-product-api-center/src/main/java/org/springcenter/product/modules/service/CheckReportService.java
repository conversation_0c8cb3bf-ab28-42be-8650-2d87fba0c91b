package org.springcenter.product.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.CheckReportDataResp;
import org.springcenter.product.api.dto.CheckReportReq;
import org.springcenter.product.modules.model.CheckReport;

import java.util.List;

/**
 * 检测报告service
 */
public interface CheckReportService {

    /**
     * 定时同步没有同步过的文件夹信息
     */
    public void syncFileInfo();


    List<CheckReport> list(CheckReportReq requestData, Page page);

    Boolean update(CheckReportReq requestData);

    /**
     * 导入excel
     * @param url
     */
    String importCheckReport(String url);

    CheckReportDataResp getReportCode(String productCode, String reportSeason, String fabirc);

    /**
     * 获取预览地址
     * @return
     */
    String getPdfViewUrl(String id );

    String download(String requestData);
}
