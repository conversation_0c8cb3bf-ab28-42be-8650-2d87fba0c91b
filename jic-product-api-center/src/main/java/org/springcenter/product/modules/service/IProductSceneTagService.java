package org.springcenter.product.modules.service;

import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.modules.remote.entity.JnbyAuthSysCateResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/19 10:55
 */
public interface IProductSceneTagService {
    /**
     * 添加标签场景
     * @param requestData
     * @return
     */
    Boolean addLabelScene(AddLabelSceneReq requestData);

    /**
     * 若没有参数，返回渠道信息；有id则返回渠道对应的场景信息
     * @param requestData 请求参数
     * @return 返回
     */
    List<QueryValidApplySceneResp> queryValidApplyScene(QueryValidApplySceneReq requestData);

    /**
     * 增加应用场景
     * @param requestData 入参
     * @return 返回
     */
    Boolean addScene(AddSceneReq requestData);

    /**
     * 根据id查询场景信息
     * @param requestData id
     * @return 返回
     */
    QuerySceneResp queryScene(QuerySceneReq requestData);

    /**
     * 更新数据
     * @param requestData 入参
     * @return 返回
     */
    Boolean updateScene(UpdateSceneReq requestData);

    /**
     * 起禁用场景
     * @param requestData 入参
     * @return 返回
     */
    Boolean switchScene(SwitchSceneReq requestData);

    /**
     * 查询场景关联的标签
     * @param requestData 入参
     * @return 出参
     */
    List<QuerySceneLabelResp> querySceneLabelList(QuerySceneReq requestData);

    /**
     * 解除标签场景关系
     * @param requestData 标签场景id
     * @return 返回
     */
    Boolean disassociateSceneLabels(List<DisassociateSceneLabelsReq> requestData);

    /**
     * 查询场景列表
     * @param requestData 请求参数
     * @return 返回
     */
    List<QuerySceneListResp> querySceneList(QuerySceneListReq requestData, Page page);


    /**
     * 根据code 查询标签场景
     * @param labelCode 标签
     * @return 返回
     */
    List<SearchLabelSceneResp> searchLabelScene(String labelCode);

    /**
     * 更新标签场景
     * @param requestData 入参
     * @return 返回
     */
    Boolean updateLabelScene(UpdateLabelSceneReq requestData);

    /**
     * 根据场景key 查询标签以及子级
     * @param requestData 入参
     * @return 返回
     */
    List<QuerySceneLabelByKeyResp> querySceneLabelsByKey(QuerySceneReq requestData);


    /**
     * 根据场景值获取当前适用的场景标签
     * @param requestData 场景key
     * @return 返回
     */
    List<QuerySceneLabelResp> queryChannelSysCateByKey(String requestData);


    /**
     * 根据场景key查询列表的标签 包含二级
     * @param requestData 场景key
     * @return 返回
     */
    List<QueryListSceneLabelResp> queryChannelListSysCateByKey(String requestData);
}
