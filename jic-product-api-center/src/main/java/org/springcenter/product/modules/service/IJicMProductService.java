package org.springcenter.product.modules.service;

import org.springcenter.product.api.dto.*;

import java.util.List;

/**
 * 微信库微盟商品信息
 * <AUTHOR>
 * @Date:2023/2/2 13:43
 */
public interface IJicMProductService {

    /**
     * 根据微盟skuId、weId、productNo(spu) 查询商品库skuId 批量
     * @param requestData 入参
     * @return 出参
     */
    List<ProductSkuIdByWeiMenInfoResp> searchProductSkuIdByWeiMenInfo(List<ProductByWeiMenInfoReq> requestData);

    /**
     * 根据微盟skuId、weId、productNo(spu) 查询商品库 批量 - 先试后买
     * @param requestData 入参
     * @return 出参
     */
    List<ProductByWeiMenInfoResp> searchProductByWeiMenInfo(List<ProductByWeiMenInfoReq> requestData);

    /**
     * 根据微盟商品id或者商品款号获取商品类型
     * @param requestData 入参
     * @return 出参
     */
    List<ProductIsTryBeforeBuyResp> searchProductIsTryBeforeBuy(ProductIsTryBeforeBuyReq requestData);

    /**
     * 将先试后买商品刷新到redis
     * @return 出参
     */
    String refreshTryBeforeBuy();


    /**
     * 根据微盟skuId、weId、productNo(spu) 查询商品库 批量 - 陪逛
     * @param requestData 入参
     * @return 出参
     */
    List<ProductByWeiMenInfoResp> searchProductByWeiMenInfoNoType(List<ProductByWeiMenInfoReq> requestData);
}
