package org.springcenter.product.modules.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date:2023/3/10 16:32
 */
@Data
public class StoreGoodSpuEntity extends GoodSpuEntity implements Serializable {
    /**
     * 门店ID + '-' + productid
     */
    private String store_product_id;
    private long c_store_id;
    private long sales_num;
    private long m_product_id;
    private String cover_imgs;
    private String detail_imgs;
    private String name_text;
    private long qty;

    @ApiModelProperty(value = "微商城库存")
    private long mall_qty;

    @ApiModelProperty(value = "内淘库存")
    private long eb_qty;

    private Integer is_display;
}
