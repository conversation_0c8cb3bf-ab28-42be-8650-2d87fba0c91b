package org.springcenter.product.modules.service;

import org.springcenter.product.modules.entity.AttrEntity;

import java.util.List;

/**
 * 类目-品牌服务
 * <AUTHOR>
 * @version 1.0
 * @date 3/29/21 10:32 AM
 */
public interface ICategoryService {

    /**
     * 查询大类目
     * @return
     */
    List<AttrEntity> getBigCategorys();

    /**
     * 查询小类目
     * @return
     */
    List<AttrEntity> getSmallCategorys();

    /**
     * 查询品牌
     * @return
     */
    List<AttrEntity> getBrands();

    /**
     * 获取品牌架构大类
     * @return
     */
    List<AttrEntity> getArcBrands();

    /**
     * 查询波段
     * @return
     */
    List<AttrEntity> getBands();

    /**
     * 获取年份分类
     * @return
     */
    List<AttrEntity> getYears();

    /**
     * 获取季节
     * @return
     */
    List<AttrEntity> getSeasons();

    /**
     * 获取v2 bands
     * @return
     */
    List<AttrEntity> getV2Bands();
}
