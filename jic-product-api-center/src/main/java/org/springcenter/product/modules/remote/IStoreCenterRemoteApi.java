package org.springcenter.product.modules.remote;

import org.springcenter.product.modules.remote.entity.StoreDetailReqEntity;
import org.springcenter.product.modules.remote.entity.StoreDetailRespEntity;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface IStoreCenterRemoteApi {

    /**
     * 查询门店详情
     * @param storeDetailReq
     * @return
     */
    @POST("/sdk/store-center/store/detail")
    Call<JicBaseResp<StoreDetailRespEntity>> detail(@Body StoreDetailReqEntity storeDetailReq);
}
