package org.springcenter.product.modules.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date:2024/4/25 17:40
 */
@Data
public class BigDataSearchRespEntity {

    @ApiModelProperty(value = "评分")
    private BigDecimal score;

    @ApiModelProperty(value = "skc")
    private String skc_no;

    @ApiModelProperty(value = "商品id")
    private Long spu_id;

    @ApiModelProperty(value = "搜索评分")
    private BigDecimal score_search;

    @ApiModelProperty(value = "款号")
    private String spu;

    @ApiModelProperty(value = "等级")
    private int rank;
}
