package org.springcenter.product.modules.convert;

import org.apache.commons.collections.CollectionUtils;
import org.springcenter.product.api.dto.QueryToppingTagListResp;
import org.springcenter.product.api.dto.QueryToppingTagResp;
import org.springcenter.product.modules.model.ProductToppingTag;
import org.springcenter.product.util.DateUtil;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/16 9:39
 */
public class ProductToppingTagConvert {
    public static QueryToppingTagResp buildToppingTag(ProductToppingTag productToppingTag) {
        QueryToppingTagResp resp = new QueryToppingTagResp();
        BeanUtils.copyProperties(productToppingTag, resp);
        String startDate = DateUtil.parseDate(productToppingTag.getStartTime(), DateUtil.DATEFORMATE_YYYY_MM_DD);
        String endDate = DateUtil.parseDate(productToppingTag.getEndTime(), DateUtil.DATEFORMATE_YYYY_MM_DD);
        resp.setStartTime(startDate);
        resp.setEndTime(endDate);
        return resp;
    }

    public static List<QueryToppingTagListResp> buildToppingTagList(List<ProductToppingTag> productToppingTags) {
        if (CollectionUtils.isEmpty(productToppingTags)) {
            return Collections.emptyList();
        }
        List<QueryToppingTagListResp> rets = new ArrayList<>();
        productToppingTags.forEach(v -> {
            QueryToppingTagListResp resp = new QueryToppingTagListResp();
            BeanUtils.copyProperties(v, resp);
            rets.add(resp);
        });

        return rets;
    }
}
