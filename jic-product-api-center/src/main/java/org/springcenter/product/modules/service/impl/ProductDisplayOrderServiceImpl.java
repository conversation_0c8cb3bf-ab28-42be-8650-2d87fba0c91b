package org.springcenter.product.modules.service.impl;

import com.jnby.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.product.api.dto.DisplayProductResp;
import org.springcenter.product.modules.service.IProductDisplayOrderService;
import org.springcenter.product.modules.util.EsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/1/31 13:27
 */
@Service
@Slf4j
@RefreshScope
public class ProductDisplayOrderServiceImpl implements IProductDisplayOrderService {

    @Value("${product.display.order.index}")
    private String productDisplayOrderIndex;

    @Autowired
    private EsUtil esUtil;


    @Override
    public List<DisplayProductResp> queryDisGoodsByStoreCode(String requestData, Page page) {
        if (StringUtils.isBlank(requestData)) {
            throw new RuntimeException("门店信息不能为空");
        }

        // 查询es
        List<DisplayProductResp> rets = searchDisplayOrderInEs(requestData, page);
        return rets;
    }

    private List<DisplayProductResp> searchDisplayOrderInEs(String requestData, Page page) {
        SearchRequest request = new SearchRequest();
        request.indices(productDisplayOrderIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        if (StringUtils.isNotBlank(requestData)) {
            queryBuilder.must(QueryBuilders.termsQuery("store_code", requestData));
        }


        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1) * page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        sourceBuilder.sort("id", SortOrder.ASC);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询门店陈列商品 {}", request.source().toString());
        List<DisplayProductResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                DisplayProductResp entity = DisplayProductResp.fromJson(hits[i].getSourceAsString(), DisplayProductResp.class);
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询门店陈列商品异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }
}
