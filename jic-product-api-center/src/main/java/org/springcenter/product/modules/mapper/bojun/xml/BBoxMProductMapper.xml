<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.product.modules.mapper.bojun.BBoxMProductMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.product.modules.model.bojun.BoxMProduct">
        <id column="CODEID" jdbcType="DECIMAL" property="codeid" />
        <result column="ID" jdbcType="DECIMAL" property="id" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="VALUE" jdbcType="VARCHAR" property="value" />
        <result column="BRANDID" jdbcType="DECIMAL" property="brandid" />
        <result column="BRAND" jdbcType="VARCHAR" property="brand" />
        <result column="YEAR" jdbcType="VARCHAR" property="year" />
        <result column="BIGSEASONID" jdbcType="DECIMAL" property="bigseasonid" />
        <result column="BIG_SEASON" jdbcType="VARCHAR" property="bigSeason" />
        <result column="SMALLSEASONID" jdbcType="DECIMAL" property="smallseasonid" />
        <result column="SMALL_SEASON" jdbcType="VARCHAR" property="smallSeason" />
        <result column="STYLE" jdbcType="VARCHAR" property="style" />
        <result column="BAND" jdbcType="VARCHAR" property="band" />
        <result column="BIGCLASSID" jdbcType="DECIMAL" property="bigclassid" />
        <result column="BIG_CLASS" jdbcType="VARCHAR" property="bigClass" />
        <result column="SMALLCLASSID" jdbcType="DECIMAL" property="smallclassid" />
        <result column="SMALL_CLASS" jdbcType="VARCHAR" property="smallClass" />
        <result column="TOPICID" jdbcType="DECIMAL" property="topicid" />
        <result column="TOPIC" jdbcType="VARCHAR" property="topic" />
        <result column="ELEMENT" jdbcType="VARCHAR" property="element" />
        <result column="PRICE" jdbcType="DECIMAL" property="price" />
        <result column="NO" jdbcType="VARCHAR" property="no" />
        <result column="GBCODE" jdbcType="VARCHAR" property="gbcode" />
        <result column="COLORNO" jdbcType="VARCHAR" property="colorno" />
        <result column="COLOR_NAME" jdbcType="VARCHAR" property="colorName" />
        <result column="SIZENO" jdbcType="VARCHAR" property="sizeno" />
        <result column="SIZE_NAME" jdbcType="VARCHAR" property="sizeName" />
        <result column="IMGURL" jdbcType="VARCHAR" property="imgurl" />
        <result column="BASIC_LABEL" jdbcType="VARCHAR" property="basicLabel" />
        <result column="FAVORABLEPRICE" jdbcType="DECIMAL" property="favorableprice" />
        <result column="BANDID" jdbcType="DECIMAL" property="bandid" />
        <result column="LIFESTART" jdbcType="DECIMAL" property="lifestart" />
        <result column="SEX" jdbcType="VARCHAR" property="sex" />
        <result column="C_ARCBRAND_ID" jdbcType="DECIMAL" property="cArcbrandId" />
        <result column="STYLEPARTSIZE_MODEL" jdbcType="VARCHAR" property="stylepartsizeModel" />
        <result column="IS_WARN" jdbcType="DECIMAL" property="isWarn" />
    </resultMap>
    <sql id="Base_Column_List">
        CODEID, ID, NAME, VALUE, BRANDID, BRAND, YEAR, BIGSEASONID, BIG_SEASON, SMALLSEASONID,
    SMALL_SEASON, STYLE, BAND, BIGCLASSID, BIG_CLASS, SMALLCLASSID, SMALL_CLASS, TOPICID,
    TOPIC, ELEMENT, PRICE, NO, GBCODE, COLORNO, COLOR_NAME, SIZENO, SIZE_NAME, IMGURL,
    BASIC_LABEL, FAVORABLEPRICE, BANDID, LIFESTART, SEX, C_ARCBRAND_ID, STYLEPARTSIZE_MODEL,
    IS_WARN
    </sql>


    <select id="getSameSpuProduct" resultType="org.springcenter.product.api.dto.SameSpuProductResp">
        SELECT t.value                            as                                               productName,
               t.zhuidan_no                        as                                              name,
               price                                 as                                            price,
               (SELECT a.imgurl FROM box_m_product a WHERE a.name = t.zhuidan_no AND ROWNUM = 1) as img,
               (SELECT a.id FROM box_m_product a WHERE a.name = t.zhuidan_no AND ROWNUM = 1) as productId
        FROM (SELECT DISTINCT b.value, a.zhuidan_no, b.price, a.CREATIONDATE
              FROM M_PRODUCTALIAS_AGAIN a
                       LEFT JOIN BOX_M_PRODUCT b
                                 ON a.zhuidan_no = b.name
              WHERE a.no = (SELECT NO
              FROM M_PRODUCTALIAS_AGAIN
              WHERE ZHUIDAN_NO = #{spu}))t
        WHERE t.zhuidan_no !=  #{spu}
        ORDER BY t.CREATIONDATE DESC
    </select>
    <select id="selectListByNos" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM box_m_product WHERE
        NO in
        <foreach collection="nos" item="no" open="(" close=")" separator=",">
            #{no}
        </foreach>
        UNION
        <!-- 特殊处理 国际码将码赋值给编码-->
        SELECT
        CODEID, ID, NAME, VALUE, BRANDID, BRAND, YEAR, BIGSEASONID, BIG_SEASON, SMALLSEASONID,
        SMALL_SEASON, STYLE, BAND, BIGCLASSID, BIG_CLASS, SMALLCLASSID, SMALL_CLASS, TOPICID,
        TOPIC, ELEMENT, PRICE, GBCODE AS NO, GBCODE, COLORNO, COLOR_NAME, SIZENO, SIZE_NAME, IMGURL,
        BASIC_LABEL, FAVORABLEPRICE, BANDID, LIFESTART, SEX, C_ARCBRAND_ID, STYLEPARTSIZE_MODEL,
        IS_WARN
        FROM box_m_product WHERE
        GBCODE in
        <foreach collection="nos" item="no" open="(" close=")" separator=",">
            #{no}
        </foreach>
    </select>

</mapper>
