package org.springcenter.product.modules.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.springcenter.product.api.dto.TestPushMqReq;
import org.springcenter.product.modules.service.IProductTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date:2023/8/30 17:35
 */
@Service
@Slf4j
@RefreshScope
public class ProductTestServiceImpl implements IProductTestService {

    @Autowired
    private DefaultMQProducer producer;

    @Override
    public Boolean pushMq(TestPushMqReq requestData) {
        log.info("========订阅用户消息推送mq");
        Message msg = new Message();
        msg.setKeys(IdWorker.getIdStr());
        msg.setTags(requestData.getServiceId());
        msg.setBody(JSON.toJSONBytes(requestData.getBody()));
        msg.setTopic(requestData.getTopic());
        try {
            producer.send(msg);
        } catch (MQClientException e) {
            e.printStackTrace();
            log.error("发送消息异常 e = {}", e);
        } catch (RemotingException e) {
            e.printStackTrace();
            log.error("发送消息异常 e = {}", e);
        } catch (MQBrokerException e) {
            e.printStackTrace();
            log.error("发送消息异常 e = {}", e);
        } catch (InterruptedException e) {
            e.printStackTrace();
            log.error("发送消息异常 e = {}", e);
        }
        return true;
    }
}
