package org.springcenter.product.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.SampleProductAutoNameReq;
import org.springcenter.product.api.dto.SampleProductReq;
import org.springcenter.product.api.dto.SampleProductSkcResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/1/16 15:41
 */
public interface IProductSampleCloService {
    /**
     * 查询样衣列表
     * @param goodsReq 参数
     * @param page 页码
     * @param component 品牌权限
     * @return 返回
     */
    List<SampleProductSkcResp> searchSampleCloProductSkc(SampleProductReq goodsReq, Page page, String component);

    /**
     * 样衣自动品名
     * @param requestData 入参
     */
    void sampleCloAutoName(SampleProductAutoNameReq requestData);

    /**
     * 样衣生成fab
     * @param requestData
     */
    void sampleCloFab(SampleProductAutoNameReq requestData);
}
