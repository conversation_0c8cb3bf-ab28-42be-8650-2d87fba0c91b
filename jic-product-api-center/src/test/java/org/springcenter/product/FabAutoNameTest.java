package org.springcenter.product;

import com.jnby.common.util.IdLeaf;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springcenter.product.modules.service.FabAutoNameInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date:2023/12/11 14:29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={JicProductCenterApplication.class})
public class FabAutoNameTest {

    @Autowired
    private FabAutoNameInfoService fabAutoNameInfoService;

    @Value("${fab.auto.name.info.tag}")
    private String fabAutoNameInfoTag;


    @Test
    public void testFabAutoNameAllTotal() {
        fabAutoNameInfoService.generateAutoName("");
    }

    @Test
    public void testGetId(){
        String id = IdLeaf.getId(fabAutoNameInfoTag);
        System.out.println("===================id:{}" + id);
    }


}
