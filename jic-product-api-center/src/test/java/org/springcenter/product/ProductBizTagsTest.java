package org.springcenter.product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import org.springcenter.product.modules.entity.GoodSpuEntity;
import org.springcenter.product.modules.model.ProductBizTags;
import org.springcenter.product.modules.service.IProductBizTagsService;
import org.springcenter.product.modules.service.IProductService;
import org.springcenter.product.modules.service.IProductStoreService;
import org.springcenter.product.api.dto.*;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes={JicProductCenterApplication.class})
public class ProductBizTagsTest {

    @Autowired
    private IProductBizTagsService iProductBizTagsService;

    @Autowired
    private IProductService iProductService;

    @Autowired
    private IProductStoreService iProductStoreService;

    @Test
    public void createTagsTest(){
        ProductBizTags tags = new ProductBizTags();
        tags.setProductCode("5LAC12201");
        tags.setId("1l233sslss222");
        tags.setCreateBy("yxz");
        tags.setCreateTime(new Date());
        tags.setUpdateTime(new Date());
        tags.setTagDictCode("1");
        tags.setBizType("box_product_dict");
        tags.setBeginTime(new Date());
        tags.setEndTime(DateUtils.addDays(new Date(), 1));
        GoodSpuEntity spuResp = iProductService.findGoodByProductCode(tags.getProductCode());
        tags.setProductId(BigDecimal.valueOf(spuResp.getId()));
        tags.setBigCategoryId(spuResp.getM_big_category_id());
        tags.setSmallCategoryId(spuResp.getM_small_category_id());
        tags.setBigSeasonId(spuResp.getBig_season_id());
        tags.setSmallSeasonId(spuResp.getSmall_season_id());
        tags.setArcBrandId(spuResp.getC_arcbrand_id());
        tags.setYear(spuResp.getYear());
        tags.setUpdateTime(new Date());
        iProductBizTagsService.saveOrUpdate(tags);
    }

    @Test
    public void queryTagsTest(){
        QueryProductBizTags queryProductBizTags = new QueryProductBizTags();
//        queryProductBizTags.setProductCodes(Lists.newArrayList("9L9C14490"));
        queryProductBizTags.setBizType("box_product_dict");
        List<ProductBizTagResp> resps = iProductBizTagsService.queryProductBizTags(queryProductBizTags);
        System.out.println("查询指定商品的业务标签=======");
        System.out.println(JSON.toJSONString(resps));
    }

    @Test
    public void mallSkcTest() {
        QueryGoodsSkcListReq req = new QueryGoodsSkcListReq();
        req.setWeid(11l);
        req.setWeiMenProductId(1);
        req.setSorted(1);
        req.setStoreId("417608-31066");
        /*List<Long> productIds = new ArrayList<>();
        productIds.add(648369L);
        req.setIncludeProductIds(productIds);*/
        req.setProductId(648369);
        System.out.println("=====" + JSON.toJSONString(iProductStoreService.searchMallProductSkc(req, new Page())));
    }

    @Test
    public void storeTestProduct() {
        QueryGoodsSkcListReq req = new QueryGoodsSkcListReq();
        req.setStoreId("JNBYP");
        req.setWeiMenProductId(0);
        req.setSorted(1);
        req.setBrandIds(Lists.newArrayList(2L,3L,4L,5L,12L,17L,57L));
        //req.setStoreId("417608-31066");
        /*List<Long> productIds = new ArrayList<>();
        productIds.add(648369L);
        req.setIncludeProductIds(productIds);*/
        //req.setProductId(621444);
        System.out.println("=====" + JSON.toJSONString(iProductStoreService.queryStoreGoodSkc(req, new Page(1, 50))));
    }


    @Test
    public void storeTest(){
        StoreGoodsReq req = new StoreGoodsReq();
        /*req.setWeid(2504948039l);
        req.setWeiMenProductId(1);
        req.setSorted(1);
        req.setStoreId("417608-31066");*/
        //req.setIsStockNotEmpty(0);
        //req.setName("5E32085");
        req.setEbQty(false);
        req.setQty(false);
        req.setIsStockNotEmpty(0);
        req.setName("HNB108060");
        req.setStoreId(Lists.newArrayList("421703","421704","421706","421708","421712","421713"));
        System.out.println("=======" + JSON.toJSONString(iProductStoreService.searchGoodsByStore(req, new Page())));
    }
    //[{"big_season_id":0,"c_arcbrand_id":2,"c_store_id":0,"eb_qty":0,"id":560384,"m_band_id":0,"m_big_category_id":0,"m_brand_id":0,"m_product_id":560384,"m_small_category_id":0,"mall_qty":0,"name":"5E32085","price":985.0,"qty":0,"sales_num":0,"small_season_id":0,"value":"外套"}]


    @Test
    public void goodsTest() {
        QueryGoodsReq req = new QueryGoodsReq();
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(10);
        req.setProductId(641232L);
        req.setIsStockNotEmpty(0);
        List<GoodSpuResp> resps = iProductService.searchGoods(req, page);
        System.out.println("===========" + JSONObject.toJSONString(resps));
    }

    @Test
    public void storeTestCode(){
        StoreGoodsReq req = new StoreGoodsReq();
        req.setProductCode("5M17008200710300000");
        req.setStoreId(Lists.newArrayList("421704","421706","421708","421712","421713"));
        System.out.println("=======" + JSON.toJSONString(iProductStoreService.searchGoodsByStore(req, new Page())));
    }

    @Test
    public void testMallSpu() {
        QueryGoodsSkcListReq req = new QueryGoodsSkcListReq();
        req.setWeid(2822095692l);
        /*req.setWeiMenProductId(1);
        req.setSorted(1);
        req.setStoreId("417608-31066");*/
        /*List<Long> productIds = new ArrayList<>();
        productIds.add(648369L);
        req.setIncludeProductIds(productIds);*/
        //req.setProductId(648369);
        //req.setName("短款");
        req.setBandId(Lists.newArrayList(1953L));
        System.out.println("=====" + JSON.toJSONString(iProductStoreService.searchMallProductSpu(req, new Page())));
    }

    @Test
    public void testVidSpu() {
        QueryGoodsSkcListReq req = new QueryGoodsSkcListReq();
        req.setStoreId("6001759555671");
        req.setName("7N0M14150");
        Page page = new Page();
        page.setPageSize(10);
        page.setPageNo(1);
        System.out.println("=====" + JSON.toJSONString(iProductStoreService.queryVidMallGoodSkc(req, page)));
    }
}
