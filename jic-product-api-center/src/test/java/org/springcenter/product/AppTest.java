package org.springcenter.product;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;

import static junit.framework.Assert.assertTrue;

/**
 * Unit test for simple App.
 */
public class AppTest
{
    /**
     * Rigorous Test :-)
     */
    public void shouldAnswerWithTrue()
    {
        assertTrue( true );
    }

    public static void main(String[] args) throws InterruptedException, InstantiationException, IllegalAccessException {
//        CountDownLatch start = new CountDownLatch(1);
//        for (int i = 0; i< 10; i++){
//            new Thread(new Worker(start)).start();
//        }
//        System.out.println("start线程已经阻塞了，打印不出任何东西");
//        start.countDown();
//        System.out.println("start释放锁，线程开始跑起来了");
//        downLatch.await();

//        Semaphore avail = new Semaphore(5);
//        for (int i = 0; i< 10; i++){
//            new Thread(new Pool(avail)).start();
//        }
//        avail.release();
//        System.out.println("总共剩余阻塞数：total = " + avail.drainPermits() + ";" + avail.getQueueLength());

//        User user = new User();
//        user = create(user.getClass());
//        System.out.println(user.getName());
        long a = 2l;
        System.out.println(a == 2);
    }

    static class Worker implements Runnable{
        private CountDownLatch startLatch;

        public Worker(CountDownLatch startLatch){
            this.startLatch = startLatch;
        }

        @Override
        public void run() {
            try {
                //获取同步状态，相当于阻塞了，看源码发现此时永远不可能拿到同步状态
                startLatch.await();
                System.out.println(Thread.currentThread().getName() + " 终于获取同步状态了，do work....");
            } catch (InterruptedException e) {

            }
        }
    }

    static class Pool implements Runnable{

        private Semaphore avail;
        public Pool(Semaphore semaphore){
            this.avail = semaphore;
        }

        @Override
        public void run() {
            try {
                System.out.println("被阻塞的线程数：num = " + avail.availablePermits());
                avail.acquire();
                System.out.println(Thread.currentThread().getName() + " 获取到了锁");
            } catch (InterruptedException e) {

            }
        }
    }
    static class User{
        private static final int A = 1;
        private Map<String, String> map;
        private String name;
        private Integer age;
        private Long userId;
        private List<String> list;


        public String getName() {
            return name;
        }

        public User setName(String name) {
            this.name = name;
            return this;
        }
    }

    //商品属性对象转化
    public static<T> T create(Class<T> t) throws IllegalAccessException, InstantiationException {
        Field[] fields = t.getDeclaredFields();
        T newInstance = t.newInstance();
        for (Field field: fields) {
            field.setAccessible(true);
            System.out.println(field.getGenericType().getTypeName().toString());
            if (field.getName().equals("name")){
                field.set(newInstance, "yxz");
            }
        }
        return newInstance;
    }

//    public class FinalTest{
//        final int j;
//        static FinalTest obj;
//
//        public FinalTest(){
//            j = 20;
//            obj = this;
//        }
//
//        public static void finalWriter(){
//            new FinalTest();
//        }
//
//        public static void finalRead(){
//            System.out.println(obj.j);
//        }
//    }
}
