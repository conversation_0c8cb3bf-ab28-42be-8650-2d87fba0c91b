package org.springcenter.product;

import com.jnby.common.Page;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springcenter.product.api.dto.AddToppingTagReq;
import org.springcenter.product.api.dto.QueryToppingTagListReq;
import org.springcenter.product.api.dto.QueryToppingTagReq;
import org.springcenter.product.api.dto.SwitchToppingTagReq;
import org.springcenter.product.modules.service.IProductToppingTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date:2023/5/16 17:20
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={JicProductCenterApplication.class})
public class ProductToppingTagTest {

    @Autowired
    private IProductToppingTagService productToppingTagService;

    @Test
    public void testAdd() {
        // 9MB13350K
        AddToppingTagReq addToppingTagReq5 = new AddToppingTagReq();
        addToppingTagReq5.setSpu("2M3G31650");
        addToppingTagReq5.setType(0);
        addToppingTagReq5.setStartTime("2023-05-31");
        addToppingTagReq5.setEndTime("2023-05-31");
        addToppingTagReq5.setIsFlow(1);
        addToppingTagReq5.setDuringTime(12);
        System.out.println("==============" + productToppingTagService.addToppingTag(addToppingTagReq5));

        // 批量插入 未测
        /*AddToppingTagReq addToppingTagReq4 = new AddToppingTagReq();
        addToppingTagReq4.setSpu("1J8222730,1K2202120,1K7316560 ， 1K9710960");
        addToppingTagReq4.setType(0);
        addToppingTagReq4.setStartTime("2023-05-17");
        addToppingTagReq4.setEndTime("2023-05-18");
        addToppingTagReq4.setIsFlow(1);
        addToppingTagReq4.setDuringTime(2);
        System.out.println("==============" + productToppingTagService.addToppingTag(addToppingTagReq4));

        // 时间不对
        AddToppingTagReq addToppingTagReq = new AddToppingTagReq();
        addToppingTagReq.setSpu("1K25P0440");
        addToppingTagReq.setType(0);
        addToppingTagReq.setStartTime("2023-05-17");
        addToppingTagReq.setEndTime("2023-05-18");
        addToppingTagReq.setIsFlow(1);
        addToppingTagReq.setDuringTime(2);
        System.out.println("==============" + productToppingTagService.addToppingTag(addToppingTagReq));

        // 未来时间
        AddToppingTagReq addToppingTagReq3 = new AddToppingTagReq();
        addToppingTagReq3.setSpu("1K5500980");
        addToppingTagReq3.setType(0);
        addToppingTagReq3.setStartTime("2023-05-19");
        addToppingTagReq3.setEndTime("2023-05-20");
        addToppingTagReq3.setIsFlow(1);
        addToppingTagReq3.setDuringTime(2);
        System.out.println("==============" + productToppingTagService.addToppingTag(addToppingTagReq3));

        // 款号不对
        AddToppingTagReq addToppingTagReq1 = new AddToppingTagReq();
        addToppingTagReq1.setSpu("1K25P0440,5H0821530，5H08215301");
        addToppingTagReq1.setType(0);
        addToppingTagReq1.setStartTime("2023-05-17");
        addToppingTagReq1.setEndTime("2023-05-18");
        addToppingTagReq1.setIsFlow(1);
        addToppingTagReq1.setDuringTime(2);
        System.out.println("==============" + productToppingTagService.addToppingTag(addToppingTagReq1));

        // 款号不对
        AddToppingTagReq addToppingTagReq2 = new AddToppingTagReq();
        addToppingTagReq2.setSpu("1K25P04401");
        addToppingTagReq2.setType(0);
        addToppingTagReq2.setStartTime("2023-05-17");
        addToppingTagReq2.setEndTime("2023-05-18");
        addToppingTagReq2.setIsFlow(1);
        addToppingTagReq2.setDuringTime(2);
        System.out.println("==============" + productToppingTagService.addToppingTag(addToppingTagReq2));*/
    }

    @Test
    public void testUpdate() {
        // 1、修改type 2、今天改成未来时间 3、未来时间改成今天
        AddToppingTagReq addToppingTagReq = new AddToppingTagReq();
        addToppingTagReq.setSpu("5M5252180");
        addToppingTagReq.setType(1);
        addToppingTagReq.setStartTime("2023-05-30");
        addToppingTagReq.setEndTime("2023-05-31");
        addToppingTagReq.setIsFlow(1);
        addToppingTagReq.setDuringTime(11);
        System.out.println("==============" + productToppingTagService.updateToppingTag(addToppingTagReq));
    }

    @Test
    public void testSearch() {
        QueryToppingTagReq req = new QueryToppingTagReq();
        req.setId("202305171014244");
        System.out.println("==============" + productToppingTagService.queryToppingTag(req));
    }

    @Test
    public void testSearchList() {
        QueryToppingTagListReq req = new QueryToppingTagListReq();
        /*req.setSpu("1K5500980");
        req.setStatus(0);
        req.setType(2);*/
        req.setStatus(2);
        Page page = new Page();
        page.setPageNo(2);
        page.setPageSize(10);
        System.out.println("==============" + productToppingTagService.queryToppingTagList(req, page));
    }

    @Test
    public void testSwitchToppingTag() {
        SwitchToppingTagReq req = new SwitchToppingTagReq();
        req.setId("202305171014244");
        req.setIsOpen(1);
        System.out.println("==============" + productToppingTagService.switchToppingTag(req));
    }

    @Test
    public void testJob() {

        productToppingTagService.switchOpenStatus();
        productToppingTagService.switchShutDownStatus();
        productToppingTagService.flowProductToppingTag();
    }

    @Test
    public void testQueryType() {
        System.out.println("======" + productToppingTagService.queryToppingTagType());
    }
}
