package org.springcenter.product;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springcenter.product.modules.mapper.product.LabelMainRuleMapper;
import org.springcenter.product.modules.mapper.product.LabelRuleDetailMapper;
import org.springcenter.product.modules.model.RuleResp;
import org.springcenter.product.modules.service.IProductLabelRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/10/19 14:02
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={JicProductCenterApplication.class})
public class ProductLabelRuleTest {


    @Autowired
    private IProductLabelRuleService productLabelRuleService;


    @Test
    public void dealCalcTest() {
        productLabelRuleService.dealLabelRuleToCreate();
    }



    @Autowired
    private LabelMainRuleMapper labelMainRuleMapper;
    @Test
    public void testCollectionMapper() {
        List<RuleResp> resps = labelMainRuleMapper.findAll();
        System.out.println("============" + JSONObject.toJSONString(resps));
    }
}
