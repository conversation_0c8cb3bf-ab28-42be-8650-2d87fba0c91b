package org.springcenter.product;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.modules.entity.BatchProductLabel;
import org.springcenter.product.modules.mapper.product.SysProductLabelMapper;
import org.springcenter.product.modules.service.IProductLabelService;
import org.springcenter.product.modules.service.IProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @Date:2023/3/13 9:23
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={JicProductCenterApplication.class})
public class ProductTest {

    @Autowired
    private IProductService productService;


    @Test
    public void testSearchSimilarGoods() {
        SimilarProductReq req = new SimilarProductReq();
        req.setSpu("9MB273600");
        req.setStoreIds(Lists.newArrayList(414347L));
        List<SimilarProductResp> resps = productService.searchSimilarGoods(req);
        System.out.println("=========================" + JSONObject.toJSONString(resps));
    }

    @Test
    public void testSearchSkuAgentStorageByIds() {
        ProductAgentStockReq req = new ProductAgentStockReq();
        req.setStoreId(Lists.newArrayList(414347L));
        List<Long> skuIds = new ArrayList<>();
        skuIds.add(1504549L);
        skuIds.add(1504550L);
        skuIds.add(1504551L);
        skuIds.add(1504552L);
        skuIds.add(1504553L);
        skuIds.add(1504544L);
        skuIds.add(1504545L);
        skuIds.add(1504546L);
        skuIds.add(1504547L);
        skuIds.add(1504548L);
        req.setSkuIds(skuIds);
        System.out.println("============" + productService.searchSkuAgentStorageByIds(req));
    }

    @Test
    public void testSearchSameSpuProduct() {
        /*SameSpuProductReq req = new SameSpuProductReq();
        req.setSpu("9L4330911");
        req.setStoreIds(Lists.newArrayList(416441L,416446L,421057L,421150L,421158L,421159L));
        req.setIsEB(Boolean.TRUE);
        System.out.println("============" + productService.searchSameSpuProduct(req));*/
        SameSpuProductReq req = new SameSpuProductReq();
        req.setSpu("9MB272500");
        req.setStoreIds(Lists.newArrayList(418227L));
        req.setIsEB(Boolean.TRUE);
        System.out.println("============" + productService.searchSameSpuProduct(req));
    }

    @Test
    public void testStorage() {
        productService.changeStoreGoodsByListenerStorage(421704L, 603379L, 1L);
    }

    @Test
    public void testSearchGoodDetail() {
        System.out.println("=======" + productService.searchProductDetail("636044"));
    }

    @Test
    public void getSpu() {
        QueryGoodsReq req = new QueryGoodsReq();
        req.setProductId(641232L);
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(100);
        List<ProductSkcResp> resps = productService.searchGoodsSkc(req, page, "");
        System.out.println("=========" + JSONObject.toJSONString(resps));
    }

    @Autowired
    IProductLabelService productLabelService;

    @Autowired
    private SysProductLabelMapper sysProductLabelMapper;

    @Test
    public void importExcel() throws IOException {
        /*productLabelService.downFileTransferMarkLabel("http://img.bzhz.jnbygroup.com/2023050501300001.xlsx", "11");*/
        List<String> updateIds = new ArrayList<>();
        updateIds.add("1K36120201");
        sysProductLabelMapper.delByProductIds(updateIds);
    }


    @Test
    public void test() {
        List<BatchProductLabel> batchProductLabelList = new ArrayList<>();
        BatchProductLabel batchProductLabel = new BatchProductLabel();
        batchProductLabel.setProductCode("1K36120201");
        batchProductLabel.setColorCode("420");
        batchProductLabelList.add(batchProductLabel);
        productLabelService.batchLabeling(batchProductLabelList, true);
    }


    @Test
    public void testReturn() {
        System.out.println("====" + new ArrayList<>());
    }

    @Test
    public void testGetSizes() {
        CommonRequest<Object> request = new CommonRequest<>();
        request.setComponent("1111");
        System.out.println("======================" + JSONObject.toJSONString(productService.getSizeNos(request)));
    }

    @Test
    public void testUpdateProdLabel() throws IOException {
        productLabelService.downFileMarkLabelInDataBase("http://img.bzhz.jnbygroup.com/20230906.xlsx", "11111");
    }

    @Test
    public void testSoutMap() {
        Map<String, String> map = new HashMap<>();
        map.put("背心","11");
        map.put("T恤","12");
        map.put("衬衣","13");
        map.put("毛衫","14");
        map.put("卫衣","15");
        map.put("马甲","21");
        map.put("夹克","22");
        map.put("西服","23");
        map.put("风衣","24");
        map.put("皮衣皮草","25");
        map.put("仿皮衣皮草","26");
        map.put("毛呢外套","27");
        map.put("棉衣","28");
        map.put("羽绒服","29");
        map.put("裤子","31");
        map.put("腰裙","32");
        map.put("连衣裙","41");
        map.put("连体衣","42");
        map.put("套装","51");
        System.out.println("===========" + JSONObject.toJSONString(map));
    }


    @Test
    public void testRandomInfo() {
        int[] numbers = {1, 7, 8, 9, 10, 11, 12};
        Random random = new Random();
        int index = random.nextInt(numbers.length);

        // 获取随机选择的数字
        int randomNumber = numbers[index];

        // 输出结果
        System.out.println("随机选择的数字是: " + randomNumber);
    }
}
