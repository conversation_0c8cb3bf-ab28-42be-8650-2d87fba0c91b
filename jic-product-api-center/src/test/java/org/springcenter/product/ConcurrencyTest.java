package org.springcenter.product;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springcenter.product.api.ProductApiController;
import org.springcenter.product.api.dto.QuerySkuDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @Date:2024/12/31 15:54
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={JicProductCenterApplication.class})
public class ConcurrencyTest {
    @Autowired
    private ProductApiController mockMvc;

    @Test
    public void testConcurrency() throws Exception {
        int numberOfThreads = 100;
        ExecutorService executorService = Executors.newFixedThreadPool(numberOfThreads);
        CountDownLatch latch = new CountDownLatch(numberOfThreads);
        String requestBody = "{\"page\":{\"count\":0,\"pageNo\":1,\"pageSize\":10,\"pages\":0},\"requestData\":{\"" +
                "gbCodes\":[],\"names\":[],\"skuId\":[1704619,1706133,1708385,1705951],\"skuNos\":[]}}";

        if (mockMvc == null) {
            throw new IllegalStateException("mockMvc is not initialized");
        }
        for (int i = 0; i < numberOfThreads; i++) {
            executorService.submit(() -> {
                try {
                    CommonRequest request = new CommonRequest();
                    QuerySkuDto skuDto = new QuerySkuDto();
                    skuDto.setSkuId(Lists.newArrayList(1704619L,1706133L,1708385L,1705951L));
                    request.setRequestData(skuDto);
                    ResponseResult responseResult = mockMvc.selectGoodsListBySkuIds(request);

                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executorService.shutdown();
    }
}
