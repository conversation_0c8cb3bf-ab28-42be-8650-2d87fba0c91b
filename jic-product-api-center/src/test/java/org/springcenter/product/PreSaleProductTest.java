package org.springcenter.product;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springcenter.product.api.dto.GoodSpuDetailEntity;
import org.springcenter.product.modules.service.IProductPreSaleService;
import org.springcenter.product.modules.service.IProductStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date:2023/8/10 15:23
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={JicProductCenterApplication.class})
public class PreSaleProductTest {

    @Autowired
    private IProductPreSaleService productPreSaleService;


    @Test
    public void testExchangeProd() {
        String ss = "{\"id\":\"111\",\"qqqq\":\"2222\"}";
        GoodSpuDetailEntity entity = GoodSpuDetailEntity.fromJson(ss, GoodSpuDetailEntity.class);
        System.out.println("============" + JSONObject.toJSONString(entity));
    }

    @Test
    public void testInsert() {
        productPreSaleService.importModelPics("E:\\jbny\\发布\\预售1024\\搭配图\\速写");
    }
}
