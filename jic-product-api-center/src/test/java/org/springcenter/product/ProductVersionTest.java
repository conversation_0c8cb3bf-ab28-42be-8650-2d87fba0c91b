package org.springcenter.product;

import com.alibaba.fastjson.JSONObject;
import com.jnby.common.Page;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.modules.service.IProductVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/7/10 10:08
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={JicProductCenterApplication.class})
public class ProductVersionTest {

    @Autowired
    private IProductVersionService productVersionService;

    @Test
    public void testQueryStatsParams() {
        PVQueryStatsReq req = new PVQueryStatsReq();
        req.setCategoryId(10);
        System.out.println("===============" + productVersionService.queryStatsParams(req));
    }


    @Test
    public void testQueryStatsLists() {
        PVQueryStatsListReq req = new PVQueryStatsListReq();
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(10);
        System.out.println("===============" + JSONObject.toJSONString(productVersionService.queryStatsList(req, page)));
    }

    @Test
    // 注意删除逻辑 照道理对应回传也是删除
    public void testSaveStatsDetail() {
        PVSaveQueryStatsDetailReq req = new PVSaveQueryStatsDetailReq();
        req.setAttributeId(32L);
        req.setAttributeCode("Q1");
        req.setAttributeName("合体度");

        // -----------------------------一级属性值
        List<PVSaveQueryStatsDetailReq.AttrValueData> attrList = new ArrayList<>();
        PVSaveQueryStatsDetailReq.AttrValueData attr = new PVSaveQueryStatsDetailReq.AttrValueData();
        attr.setAttrId(45L);
        attr.setAttrCode("Q1-A01");
        attr.setAttrDesc("修身");
        attr.setIsSelected(1);
        attrList.add(attr);

        PVSaveQueryStatsDetailReq.AttrValueData attr1 = new PVSaveQueryStatsDetailReq.AttrValueData();
        attr1.setAttrId(46L);
        attr1.setAttrCode("Q1-A02");
        attr1.setAttrDesc("偏合体");
        attr1.setIsSelected(1);
        attrList.add(attr1);


        PVSaveQueryStatsDetailReq.AttrValueData attr2 = new PVSaveQueryStatsDetailReq.AttrValueData();
        attr2.setAttrId(47L);
        attr2.setAttrCode("Q1-A03");
        attr2.setAttrDesc("合体");
        attr2.setIsSelected(1);
        attrList.add(attr2);


        PVSaveQueryStatsDetailReq.AttrValueData attr3 = new PVSaveQueryStatsDetailReq.AttrValueData();
        attr3.setAttrId(48L);
        attr3.setAttrCode("Q1-A04");
        attr3.setAttrDesc("偏宽松");
        attr3.setIsSelected(1);
        attrList.add(attr3);


        PVSaveQueryStatsDetailReq.AttrValueData attr4 = new PVSaveQueryStatsDetailReq.AttrValueData();
        attr4.setAttrId(49L);
        attr4.setAttrCode("Q1-A05");
        attr4.setAttrDesc("宽松");
        attr4.setIsSelected(1);
        attrList.add(attr4);

        PVSaveQueryStatsDetailReq.AttrValueData attr5 = new PVSaveQueryStatsDetailReq.AttrValueData();
        attr5.setAttrId(50L);
        attr5.setAttrCode("Q1-A06");
        attr5.setAttrDesc("OVERSIZE");
        attr5.setIsSelected(1);
        attrList.add(attr5);

        PVSaveQueryStatsDetailReq.AttrValueData attr6 = new PVSaveQueryStatsDetailReq.AttrValueData();
        attr6.setAttrId(51L);
        attr6.setAttrCode("Q1-A07");
        attr6.setAttrDesc("超OVERSIZE");
        attr6.setIsSelected(1);
        attrList.add(attr6);

        PVSaveQueryStatsDetailReq.AttrValueData attr7 = new PVSaveQueryStatsDetailReq.AttrValueData();
        attr7.setAttrId(209L);
        attr7.setAttrDesc("SMALL_OVERSIZE");
        attr7.setIsSelected(1);
        attrList.add(attr7);

        req.setAttrValueList(attrList);

        //-------------------------二级属性
        List<PVSaveQueryStatsDetailReq.AttrValueData> sceAttrList = new ArrayList<>();
        PVSaveQueryStatsDetailReq.AttrValueData sceAttr = new PVSaveQueryStatsDetailReq.AttrValueData();
        sceAttr.setAttrId(176L);
        sceAttr.setAttrCode("Q1-S1");
        sceAttr.setAttrDesc("胸围");
        sceAttr.setIsSelected(0);
        sceAttrList.add(sceAttr);

        PVSaveQueryStatsDetailReq.AttrValueData sceAttr1 = new PVSaveQueryStatsDetailReq.AttrValueData();
        sceAttr1.setAttrId(177L);
        sceAttr1.setAttrCode("Q1-S2");
        sceAttr1.setAttrDesc("腰围");
        sceAttr1.setIsSelected(1);
        sceAttrList.add(sceAttr1);

        PVSaveQueryStatsDetailReq.AttrValueData sceAttr2 = new PVSaveQueryStatsDetailReq.AttrValueData();
        sceAttr2.setAttrId(178L);
        sceAttr2.setAttrCode("Q1-S3");
        sceAttr2.setAttrDesc("臀围");
        sceAttr2.setIsSelected(1);
        sceAttrList.add(sceAttr2);

        PVSaveQueryStatsDetailReq.AttrValueData sceAttr3 = new PVSaveQueryStatsDetailReq.AttrValueData();
        sceAttr3.setAttrId(179L);
        sceAttr3.setAttrCode("Q1-S4");
        sceAttr3.setAttrDesc("摆围");
        sceAttr3.setIsSelected(1);
        sceAttrList.add(sceAttr3);

        PVSaveQueryStatsDetailReq.AttrValueData sceAttr4 = new PVSaveQueryStatsDetailReq.AttrValueData();
        sceAttr4.setAttrId(210L);
        sceAttr4.setAttrDesc("脚围");
        sceAttr4.setIsSelected(0);
        sceAttrList.add(sceAttr4);

        req.setSecAttrList(sceAttrList);

        // -----------------------------------类目属性关联
        List<PVSaveQueryStatsDetailReq.CategoryData> categoryData = new ArrayList<>();
        PVSaveQueryStatsDetailReq.CategoryData categoryData1 = new PVSaveQueryStatsDetailReq.CategoryData();
        categoryData1.setAttrCode("Q1");
        categoryData1.setAttrDesc("合体度");
        categoryData1.setAttrId(32L);

        categoryData1.setBigCategoryId(10L);
        categoryData1.setBigCategoryCode("3152");
        categoryData1.setBigCategoryDesc("可内可外");

        categoryData1.setSmallCategoryId(11L);
        categoryData1.setSmallCategoryCode("296");
        categoryData1.setSmallCategoryDesc("T恤");

        categoryData1.setIsSelected(1);
        categoryData.add(categoryData1);
        req.setCategoryList(categoryData);

        // ----------------------------二级类目属性关联
        List<PVSaveQueryStatsDetailReq.CategoryData> secAttrConnectList = new ArrayList<>();
        PVSaveQueryStatsDetailReq.CategoryData secAttrConnect = new PVSaveQueryStatsDetailReq.CategoryData();
        secAttrConnect.setAttrCode("Q1-S3");
        secAttrConnect.setAttrDesc("臀围");
        secAttrConnect.setAttrId(178L);

        secAttrConnect.setBigCategoryId(10L);
        secAttrConnect.setBigCategoryCode("3152");
        secAttrConnect.setBigCategoryDesc("可内可外");

        secAttrConnect.setSmallCategoryId(11L);
        secAttrConnect.setSmallCategoryCode("296");
        secAttrConnect.setSmallCategoryDesc("T恤");

        secAttrConnect.setIsSelected(1);
        secAttrConnectList.add(secAttrConnect);
        req.setSecAttrConnectList(secAttrConnectList);

        System.out.println("===============" + productVersionService.saveStatsDetail(req));
    }

    @Test
    public void testQueryStatsDetail() {
        PVQueryStatsDetailReq req = new PVQueryStatsDetailReq();
        req.setAttributeId(null);
        System.out.println("===============" + JSONObject.toJSONString(productVersionService.queryStatsDetail(req)));
    }

    @Test
    public void testQueryDetailDic() {
        ProductVersionDetailDicReq req = new ProductVersionDetailDicReq();
        req.setCategoryCode("296");
        System.out.println("===============" + JSONObject.toJSONString(productVersionService.queryDetailDic(req)));
    }

    @Test
    public void testQueryDetail() {
        ProductVersionDetailReq req = new ProductVersionDetailReq();
        req.setId("20230106143303681");
        System.out.println("===============" + JSONObject.toJSONString(productVersionService.queryDetail(req)));
    }

    @Test
    public void testEditDetail() {
        ProductVersionEditDetailReq req = new ProductVersionEditDetailReq();
        req.setId("20230106143303681");
        req.setBrandCode("3");
        req.setIsClose(0);
        req.setSmallName("111");
        req.setMainIngredient("");
        req.setMainIngredientNumber("ZMM0113");
        List<ProductVersionDetailResp.BaseInfo> attrs = new ArrayList<>();
        ProductVersionDetailResp.BaseInfo data = new ProductVersionDetailResp.BaseInfo();
        data.setId("11");
        data.setAttrName("偏合体");
        data.setAttrCode("Q1");
        data.setValueCode("Q1-A02");
        data.setValueName("偏合体");

        List<ProductVersionDetailResp.BaseInfo> child = new ArrayList<>();
        ProductVersionDetailResp.BaseInfo childData = new ProductVersionDetailResp.BaseInfo();
        childData.setId("1012");
        childData.setAttrCode("Q1-S3");
        childData.setAttrName("臀围");
        childData.setValueName("222");
        child.add(childData);

        data.setChild(child);
        attrs.add(data);
        req.setAttrs(attrs);
        System.out.println("===============" + JSONObject.toJSONString(productVersionService.editDetail(req)));
    }

    @Test
    public void testImport() throws IOException {
        // String url= "http://img.bzhz.jnbygroup.com//202003/product_version1111.xlsx";
        String url = "http://img.bzhz.jnbygroup.com//202003/product_version2222.xlsx";
        String key = "1";
        productVersionService.downFileForProductVersion(url, key);
    }


    @Test
    public void testImportUpdate() throws IOException {
        String url= "http://img.bzhz.jnbygroup.com//202003/product_version1111.xlsx";
        // String url = "http://img.bzhz.jnbygroup.com//202003/product_version2222.xlsx";
        String key = "1";
        productVersionService.downFileForUpdateProductVersion(url, key);
    }

    @Test
    public void testQueryList() throws IOException {
        ProductVersionListDataReq req = new ProductVersionListDataReq();
        req.setAttributeList(Lists.newArrayList("Q1-A01"));
        req.setIsClose(0);
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(10);
        System.out.println("=================" + JSONObject.toJSONString(productVersionService.queryList(req, page, "")));

    }
}
