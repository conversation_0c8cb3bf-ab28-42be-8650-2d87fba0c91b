package org.springcenter.product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springcenter.product.api.dto.GoodSpuDetailEntity;
import org.springcenter.product.modules.service.IProductStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date:2023/8/10 15:23
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={JicProductCenterApplication.class})
public class VidProductTest {

    @Autowired
    private IProductStoreService productStoreService;

    @Test
    public void listenerProductVidData() {
        // Long storeId, Long productId, Long id, String type, String weId, Long skuId
        //productStoreService.changeStoreGoodsByListenerVidStorage(null, 714992L, 178506972L, "CD", "5", 1636072L);
    }

    @Test
    public void test() {
        String ss = "{\"columns\":[{\"check\":true,\"column\":{\"name\":\"REMARK\",\"type\":12},\"value\":\"门店商品关系变更\"},{\"check\":true,\"column\":{\"name\":\"CREATE_TIME\",\"type\":93},\"value\":*************},{\"check\":true,\"column\":{\"name\":\"PARAMS\",\"type\":12},\"value\":\"{\\\\\\\"mid\\\\\\\":\\\\\\\"557191317267959420230825135700\\\\\\\",\\\\\\\"brandId\\\\\\\":\\\\\\\"**********\\\\\\\",\\\\\\\"id\\\\\\\":\\\\\\\"74cb7d16-d3ce-4dc6-915c-9e0cfa74b7a3\\\\\\\",\\\\\\\"business_id\\\\\\\":null,\\\\\\\"public_account_id\\\\\\\":null,\\\\\\\"topic\\\\\\\":\\\\\\\"weimob_shop.goods\\\\\\\",\\\\\\\"event\\\\\\\":\\\\\\\"goodsStoreRelationUpdate\\\\\\\",\\\\\\\"msg_body\\\\\\\":null,\\\\\\\"version\\\\\\\":1,\\\\\\\"msgBody\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"goodsIdList\\\\\\\\\\\\\\\":[***************],\\\\\\\\\\\\\\\"isAssigned\\\\\\\\\\\\\\\":true,\\\\\\\\\\\\\\\"isOnline\\\\\\\\\\\\\\\":true,\\\\\\\\\\\\\\\"vidList\\\\\\\\\\\\\\\":[*************]}\\\\\\\",\\\\\\\"bosId\\\\\\\":\\\\\\\"*************\\\\\\\"}\"},{\"check\":true,\"column\":{\"name\":\"BRANDID\",\"type\":12},\"value\":\"**********\"},{\"check\":true,\"column\":{\"name\":\"EVENT\",\"type\":12},\"value\":\"goodsStoreRelationUpdate\"}],\"discardType\":\"NONE\",\"opType\":\"U\",\"primaryKeys\":[{\"check\":true,\"column\":{\"name\":\"ID\",\"type\":3},\"value\":2487640}],\"rowId\":{\"check\":true,\"column\":{\"name\":\"ROWID\",\"type\":-8},\"value\":{\"bytes\":\"QUFCcjdMQUF1QUFDeGdwQUJr\",\"length\":18,\"stream\":{}}},\"schemaName\":\"USERWX\",\"tableName\":\"JIC_PRODUCT_PUTAWAY_MSG_LOG\"}";
        System.out.println("==========" + JSONObject.toJSONString(ss));
    }
}
