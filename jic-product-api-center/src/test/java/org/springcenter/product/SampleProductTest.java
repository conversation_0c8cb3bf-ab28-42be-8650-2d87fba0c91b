package org.springcenter.product;

import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springcenter.product.api.dto.MarkingLabelReq;
import org.springcenter.product.api.dto.QueryGoodsReq;
import org.springcenter.product.api.dto.SampleProductReq;
import org.springcenter.product.modules.service.IProductLabelService;
import org.springcenter.product.modules.service.impl.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import com.jnby.common.Page;

/**
 * <AUTHOR>
 * @Date:2023/3/15 17:36
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={JicProductCenterApplication.class})
public class SampleProductTest {

    @Autowired
    private ProductService productService;

    @Test
    public void getProduct() {
        SampleProductReq req = new SampleProductReq();
        /*List<Long> bandIds = new ArrayList<>();
        bandIds.add(2000L);
        bandIds.add(1952L);
        req.setBandIds(bandIds);
        List<String> brandIds = new ArrayList<>();
        brandIds.add("2");
        req.setBrandIds(brandIds);
        req.setName("JU9S02");*/
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(100000);
        req.setMustLabelLevels(Lists.newArrayList("B05-A03-A06"));
        req.setMustLabels(Lists.newArrayList("B05-A03-A06-A04"));
        System.out.println("=============" + productService.searchSampleProductSkc(req, page, ""));
    }

    @Test
    public void getProductSkc() {
        QueryGoodsReq req = new QueryGoodsReq();
        req.setNames(Lists.newArrayList("9LB103700"));
        req.setMustLabelLevels(Lists.newArrayList("B05-A03-A06"));
        req.setMustLabels(Lists.newArrayList("B05-A03-A06-A04"));
        System.out.println("=============" + productService.searchGoodsSkc(req, new Page(1, 10), ""));
    }

    @Autowired
    private IProductLabelService iProductLabelService;

    @Test
    public void testMarketLabel() {
        MarkingLabelReq req = new MarkingLabelReq();
        req.setProductId("641232");
        req.setLabelList(Lists.newArrayList());
        req.setSkcCode("5M4G30500209");
        iProductLabelService.markingProductLabel(req, "1566686931045117953");
    }
}
