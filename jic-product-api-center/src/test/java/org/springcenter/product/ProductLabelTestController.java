package org.springcenter.product;

import com.google.common.collect.Lists;
import com.jnby.common.Page;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.modules.service.IProductLabelRuleService;
import org.springcenter.product.modules.service.IProductSceneTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/5/21 14:19
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={JicProductCenterApplication.class})
public class ProductLabelTestController {

    @Autowired
    private IProductSceneTagService productSceneTagService;

    @Test
    public void addSceneConnectionTest() {
        AddLabelSceneReq sceneReq1 = new AddLabelSceneReq();
        sceneReq1.setLabelCode("D2-A1-B2, D2-A1-B3");
        AddLabelSceneReq.SceneData sceneData = new AddLabelSceneReq.SceneData();
        sceneData.setSceneId("111");
        sceneData.setSort(1);
        AddLabelSceneReq.SceneData sceneData2 = new AddLabelSceneReq.SceneData();
        sceneData2.setSceneId("222");
        sceneData2.setSort(2);
        sceneReq1.setSceneDataList(Lists.newArrayList(sceneData, sceneData2));
        System.out.println(productSceneTagService.addLabelScene(sceneReq1));
    }

    @Test
    public void searchSceneConnectionTest() {
        System.out.println("==========" + productSceneTagService.searchLabelScene("D2-A1-B2"));
    }


    @Test
    public void updateSceneConnectionTest() {
        List<UpdateLabelSceneReq> updateLabelSceneReqList = new ArrayList<>();
        UpdateLabelSceneReq req = new UpdateLabelSceneReq();
        /*req.setId("20230615143412119");
        req.setIsDeleted(1);
        req.setSceneId("20230529171236117");
        req.setLabelCode("D2-A1-B2");
        req.setSort(3);


        UpdateLabelSceneReq req1 = new UpdateLabelSceneReq();
        req1.setId("20230615143412120");
        req1.setIsDeleted(0);
        req1.setSceneId("222");
        req1.setLabelCode("D2-A1-B2");
        req1.setSort(4);

        UpdateLabelSceneReq req2 = new UpdateLabelSceneReq();
        req2.setIsDeleted(0);
        req2.setSceneId("111");
        req2.setLabelCode("D2-A1-B2");
        req2.setSort(1);*/

        /*UpdateLabelSceneReq req3 = new UpdateLabelSceneReq();
        req3.setIsDeleted(0);
        req3.setSceneId("111");
        req3.setLabelCode("D2-A1-B2");
        req3.setSort(2);*/
       /* updateLabelSceneReqList.add(req);
        updateLabelSceneReqList.add(req1);
        updateLabelSceneReqList.add(req2);
        //updateLabelSceneReqList.add(req3);
        productSceneTagService.updateLabelScene(updateLabelSceneReqList);*/
    }

    @Test
    public void addSceneTest() {
        AddSceneReq addSceneReq = new AddSceneReq();
        addSceneReq.setSceneName("搜索");
        addSceneReq.setRemark("备注");
        addSceneReq.setOwnChannelId(1);
        System.out.println(productSceneTagService.addScene(addSceneReq));
    }

    @Test
    public void querySceneReqTest() {
        QuerySceneReq req = new QuerySceneReq();
        req.setId("202305211535385");
        System.out.println("=====" + productSceneTagService.queryScene(req));
    }

    @Test
    public void updateSceneTest() {
        UpdateSceneReq req = new UpdateSceneReq();
        req.setId("202305211535385");
        req.setOwnChannelId(2);
        req.setSceneName("嗖嗖嗖");
        req.setRemark("remark");
        System.out.println("=====" + productSceneTagService.updateScene(req));
    }

    @Test
    public void switchSceneTest() {
        SwitchSceneReq req = new SwitchSceneReq();
        req.setId("202305211535385");
        req.setIsDisabled(1);
        System.out.println("=====" + productSceneTagService.switchScene(req));
    }


    @Test
    public void querySceneLabelListTest() {
        QuerySceneReq req = new QuerySceneReq();
        req.setId("1");
        //System.out.println("=============" + productSceneTagService.querySceneLabelList(req));
    }

    @Test
    public void querySceneListTest() {
        QuerySceneListReq req = new QuerySceneListReq();

        System.out.println("=============" + productSceneTagService.querySceneList(req, new Page(2, 1)));
    }

    @Test
    public void queryValidApplySceneTest() {
        // 获取渠道
        QueryValidApplySceneReq req = new QueryValidApplySceneReq();
        System.out.println("=============" + productSceneTagService.queryValidApplyScene(req));

        // 获取渠道下的标签
        req.setChannelId("1");
        System.out.println("=============" + productSceneTagService.queryValidApplyScene(req));
    }

    @Test
    public void disassociateSceneLabels() {
        DisassociateSceneLabelsReq req = new DisassociateSceneLabelsReq();
        req.setId("20230521152516102");
        //System.out.println(productSceneTagService.disassociateSceneLabels(req));
    }

    @Autowired
    private IProductLabelRuleService productLabelRuleService;

    @Test
    public void testInsertOutsideName() {
        productLabelRuleService.pullOutsideNameJob();
    }

}
