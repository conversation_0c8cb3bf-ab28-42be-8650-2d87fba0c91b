package org.springcenter.retail.api;

import com.jnby.common.ResponseResult;
import io.swagger.annotations.ApiOperation;
import org.springcenter.retail.api.dto.req.promotion.PromotionDTO;
import org.springcenter.retail.api.dto.req.promotion.StoreQueryDTO;
import org.springcenter.retail.api.dto.resp.promotion.StoreDTO;
import org.springcenter.retail.api.dto.resp.promotion.SupplierDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@FeignClient(contextId = "promotionApi", value = "jic-retail-api-center")
public interface IPromotionApi {
     @ApiOperation("提交宜搭促销活动到伯俊")
     @PostMapping("/bojun/promotion/api/submitPromotion")
     ResponseResult<?> submitPromotion(@RequestBody PromotionDTO promotionDTO);

     @ApiOperation("查询供应商")
     @PostMapping(value = "/bojun/promotion/api/listSupplier")
     ResponseResult<List<SupplierDTO>> listSupplier(@Validated @RequestBody StoreQueryDTO storeQueryDTO);

     @ApiOperation("查询门店")
     @PostMapping(value = "/bojun/promotion/api/listStore")
     ResponseResult<List<StoreDTO>> listStore(@Validated @RequestBody StoreQueryDTO storeQueryDTO);
}
