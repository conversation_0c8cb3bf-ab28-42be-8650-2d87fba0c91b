package org.springcenter.retail.api.dto.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/30 11:12
 * @description
 */
@Data
public class Query2JSTRespDTO {
      // 外部唯一id
      @JSONField(name = "outer_as_id")
      private String outerAsId;

      // 退仓单号
      @JSONField(name = "io_id")
      private Integer ioId;

      // 内部单号
      @JSONField(name = "o_id")
      private Integer oId;

      // 线上单号
      @JSONField(name = "so_id")
      private String soId;

      // 售后订单号
      @JSONField(name = "as_id")
      private Integer asId;

      // 入库日期
      @JSONField(name = "io_date")
      private String ioDate;

      // 币种；为空默认为人民币
      @JSONField(name = "currency")
      private String currency;

      // 仓库名称
      @JSONField(name = "warehouse")
      private String warehouse;

      // 修改时间
      @JSONField(name = "modified")
      private String modified;

      // 物流公司编码
      @JSONField(name = "lc_id")
      private String lcId;

      // 店铺编号
      @JSONField(name = "shop_id")
      private Integer shopId;

      // 时间戳
      @JSONField(name = "ts")
      private Integer ts;

      // 仓库代码（1主仓，2销退仓，3进货仓，4次品仓）
      @JSONField(name = "wh_id")
      private Integer whId;

      // 分仓编号
      @JSONField(name = "wms_co_id")
      private Integer wmsCoId;

      // 分销商名称
      @JSONField(name = "drp_co_name")
      private String drpCoName;

      // 分销商编号
      @JSONField(name = "drp_co_id")
      private Integer drpCoId;

      // 售后类型：普通退货，其它，拒收退货，仅退款，投诉，补发，换货，维修
      @JSONField(name = "type")
      private String type;

      // Confirmed代表已确认  WaitConfirm  待确认  Canclled 取消
      @JSONField(name = "status")
      private String status;

      @JSONField(name = "refund")
      private BigDecimal refund;

      // 商品集合
      @JSONField(name = "items")
      List<Query2JSTRespItemDTO> items;


      @Data
      public static class Query2JSTRespItemDTO{
            @JSONField(name = "io_id")
            private Integer ioId;	//	退仓单号
            @JSONField(name = "amount")
            private BigDecimal amount;
            @JSONField(name = "sku_id")
            private String skuId;	//商品编码
            @JSONField(name = "unit")
            private String unit;	//单位
            @JSONField(name = "qty")
            private Integer qty;	//商品数量
            @JSONField(name = "name")
            private String name;	//商品名称
            @JSONField(name = "properties_value")
            private String propertiesValue;	//属性值
            @JSONField(name = "sale_price")
            private BigDecimal salePrice;	//销售价格
            @JSONField(name = "sale_amount")
            private BigDecimal saleAmount;	//销售总金额
            @JSONField(name = "ioi_id")
            private String ioiId;	//退仓子单号，用于区分货品行的行号
      }

}
