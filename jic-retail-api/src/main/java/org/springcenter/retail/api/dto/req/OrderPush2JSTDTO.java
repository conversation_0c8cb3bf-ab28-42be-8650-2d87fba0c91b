package org.springcenter.retail.api.dto.req;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/29 13:33
 * @description
 */
@Data
public class OrderPush2JSTDTO {

     // 店铺编号
     @JSONField(name = "shop_id")
     @ApiModelProperty(value = "店铺编号",required = true)
     @NotNull(message = "店铺编号不允许为空")
     private Integer shopId;

     // 退货退款单号，平台唯一
     @JSONField(name = "outer_so_id")
     @ApiModelProperty(value = "外部交易单号",required = true)
     @NotNull(message = "外部交易单号")
     private String outerSoId;

     // 平台订单号(订单页面线上单号)
     @JSONField(name = "so_id")
     @ApiModelProperty(value = "平台订单号(订单页面线上单号",required = true)
     @NotNull(message = "平台订单号不允许为空")
     private String soId;

     @JSONField(name = "order_date")
     @ApiModelProperty(value = "订单日期,格式2019-12-15 18:14:26",required = true)
     @NotNull(message = "订单日期不允许为空")
     private String orderDate;

     @JSONField(name = "shop_status")
     @ApiModelProperty("单据状态,默认不用传入")
     private String shopStatus = "WAIT_SELLER_SEND_GOODS";

     // 备注
     @JSONField(name = "remark")
     @ApiModelProperty("备注")
     private String remark;

     // 收货仓编码
     @JSONField(name = "wms_co_id")
     @ApiModelProperty("收货仓编码")
     private Long wmsCoId;

     // 省份
     @JSONField(name = "receiver_state")
     @ApiModelProperty(value = "省份",required = true)
     private String receiverState;

     // 城市
     @JSONField(name = "receiver_city")
     @ApiModelProperty(value = "城市",required = true)
     private String receiverCity;

     // 县市
     @JSONField(name = "receiver_district")
     @ApiModelProperty(value = "县市",required = true)
     private String receiverDistrict;

     // 收货地址
     @JSONField(name = "receiver_address")
     @ApiModelProperty(value = "收货地址",required = true)
     private String receiverAddress;

     @JSONField(name = "receiver_name")
     @ApiModelProperty(value = "收件人",required = true)
     private String receiverName;


     @JSONField(name = "receiver_phone")
     @ApiModelProperty(value = "电话",required = true)
     private String receiverPhone;

     @JSONField(name = "pay_amount")
     @ApiModelProperty("应付金额")
     private BigDecimal payAmount = BigDecimal.ZERO;

     @JSONField(name = "freight")
     @ApiModelProperty("运费")
     private BigDecimal freight = BigDecimal.ZERO;

     @JSONField(name = "shop_buyer_id")
     @ApiModelProperty("买家账号")
     private String shopBuyerId = "B001";

     // 商品列表
     @JSONField(name = "items")
     @ApiModelProperty(value = "商品列表",required = true)
     @NotEmpty(message = "商品列表不允许为空")
     private List<OrderPush2JSTDTO.OrderPush2JSTDTOItem> items;

     @Data
     public static class OrderPush2JSTDTOItem{
          // 平台订单明细编号，存在则会按此作为唯一性判断，商品为组合装时需要上传订单的明细编号（开启售后单下载拦截订单，不传会整单退款，订单转取消）
          @JSONField(name = "outer_oi_id")
          @ApiModelProperty("商家系统订单商品明细主键,为了拆单合单时溯源，最长不超过50,保持订单内唯一，支持自定义")
          private String outerOiId;

          // 商家商品编码
          @JSONField(name = "sku_id")
          @ApiModelProperty(value = "商家商品编码",required = true)
          private String skuId;

          // 商家商品编码
          @JSONField(name = "shop_sku_id")
          @ApiModelProperty(value = "店铺商品编码",required = true)
          private String shopSkuId;

          // 退货数量
          @JSONField(name = "qty")
          @ApiModelProperty(value = "数量",required = true)
          private Integer qty;

          // SKU退款金额
          @JSONField(name = "amount")
          @ApiModelProperty(value = "成交总额单位",required = true)
          private BigDecimal amount;
          // SKU退款金额
          @JSONField(name = "base_price")
          @ApiModelProperty(value = "原价",required = true)
          private BigDecimal basePrice;
          // SKU退款金额
          @JSONField(name = "name")
          @ApiModelProperty(value = "商品名称",required = true)
          private String name;
     }

}
