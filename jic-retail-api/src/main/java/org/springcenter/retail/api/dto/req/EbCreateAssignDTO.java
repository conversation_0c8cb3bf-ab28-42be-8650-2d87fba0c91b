package org.springcenter.retail.api.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class EbCreateAssignDTO {

    @ApiModelProperty("类型：1门店包 2内淘 3BOX搭配师内淘")
    private Integer type;
    @ApiModelProperty("门店包id")
    private Long storePkgId;
    @ApiModelProperty("是否使用,作用于类型是2和3的")
    private Boolean ifUse;
    @ApiModelProperty("安全库存")
    private Integer safetyStock = 0;
    @ApiModelProperty("门店包中门店类型 1集合店 2品牌店")
    private Integer storePkgType;


}
