FROM jnbyharbor.jnby.com/base-images/baseimagejdk8:v1.3

MAINTAINER box-group

RUN mkdir -p /jic-retail-center/jic-trade-api-center-gateway

WORKDIR /jic-retail-center/jic-trade-api-center-gateway

EXPOSE 9378

COPY target/jic-trade-api-center-gateway.jar jic-trade-api-center-gateway.jar

ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java", "-Xmx2g", "-Xms2g","-XX:NewRatio=3","-Xss512k", "-Xmn1g","-XX:SurvivorRatio=2", "-XX:MaxMetaspaceSize=192m", "-XX:MetaspaceSize=192m", "-XX:+UseParallelGC","-Dreactor.netty.pool.leasingStrategy=lifo", "-jar", "jic-trade-api-center-gateway.jar"]

CMD ["--spring.profiles.active=prod"]
