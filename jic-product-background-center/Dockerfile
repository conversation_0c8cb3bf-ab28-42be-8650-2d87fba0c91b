FROM jnbyharbor.jnby.com/base-images/baseimagejdk8:v1.3

MAINTAINER box-group

RUN mkdir -p /jic-product-center/jic-product-background-center

WORKDIR /jic-product-center/jic-product-background-center

EXPOSE 9351
EXPOSE 9999

COPY target/jic-product-background-center.jar jic-product-background-center.jar

ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java", "-Xmx2g", "-Xms2g","-XX:NewRatio=3","-Xss512k", "-Xmn1g","-XX:SurvivorRatio=2", "-XX:+UseParallelGC","-Dreactor.netty.pool.leasingStrategy=lifo", "-jar", "jic-product-background-center.jar"]

CMD ["--spring.profiles.active=prod"]


