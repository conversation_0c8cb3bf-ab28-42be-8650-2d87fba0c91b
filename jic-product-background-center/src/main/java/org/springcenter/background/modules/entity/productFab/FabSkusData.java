package org.springcenter.background.modules.entity.productFab;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/6/5 20:28
 */
@Data
public class FabSkusData {

    private List<SkuList> skuList;
    private ScopedSlots scopedSlots;
    private String assembly;
    private String outName;
    private long id;
    private String type;
    private String key;
    private RadioInfo radioInfo;
    private String collocation;
    private Boolean cationType;
    private List cationList;
    private String mark;
    private String direction;
    private List<CopyData> copyList;

    private List<DataSource> dataSource;

    @Data
    public static class ScopedSlots implements Serializable {
        private String title;
    }

    @Data
    private static class DataSource implements Serializable {
        private String brandName;
        private List<Integer> bandIds;
        private String bomName;
        private String productPromotionMaterials;
        private String fabTexTitle;
        private List<String> brandIds;
        private String key;
        private String sjcf;
    }

    @Data
    public static class CopyData implements Serializable {
        private String bomName;
        private List<Integer> bandIds;
        private String productPromotionMaterials;
        private String fabTexTitle;
        private String key;
        private List<String> brandIds;
        private String sjcf;
    }


    @Data
    public static class RadioInfo implements Serializable {

        private String patternName;
        private List<String> bandIds;
        private String inspiraCopyText;
        private String patternType;
        private String inspiration;
        private List<String> brandIds;
        private String url;


    }


    @Data
    public class SkuList implements Serializable {

        private String colorName;
        private String designName;
        private String img;
        private String price;
        private String name;
        private boolean falg;
        private boolean checked;
        private String colorCode;
        private boolean disabled;
        private String key;


    }
}
