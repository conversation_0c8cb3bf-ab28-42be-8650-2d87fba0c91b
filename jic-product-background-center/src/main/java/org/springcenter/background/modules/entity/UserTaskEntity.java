package org.springcenter.background.modules.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2024/3/15 13:40
 */
@Data
public class UserTaskEntity {

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "门店id")
    private Long cStoreId;

    @ApiModelProperty(value = "门店名称")
    private String cStoreName;

    @ApiModelProperty(value = "品牌id")
    private Long storeBrandId;

    @ApiModelProperty(value = "区域id")
    private Long areaId;

    @ApiModelProperty(value = "区域名称")
    private String area;

    @ApiModelProperty(value = "城市id")
    private Long cityId;

    @ApiModelProperty(value = "城市名称")
    private String city;

    @ApiModelProperty(value = "产品册")
    private String volumeName;

    private String volumeId;

    private String hrName;

    private String hrId;

    private Date feedBackDate;

    private String feedBackUser;

    private String materitals;

    private String taskName;

    private String campaignId;
}
