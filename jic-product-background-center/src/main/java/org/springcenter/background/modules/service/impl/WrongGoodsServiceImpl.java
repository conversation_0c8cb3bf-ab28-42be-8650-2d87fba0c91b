package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.common.util.excel.IWriteDataExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springcenter.background.modules.entity.ImportWrongReason;
import org.springcenter.background.modules.mapper.product.WrongGoodsImgsMapper;
import org.springcenter.background.modules.mapper.product.WrongGoodsMainMapper;
import org.springcenter.background.modules.mapper.product.WrongGoodsPimgsRelationMapper;
import org.springcenter.background.modules.mapper.product.WrongGoodsProductsMapper;
import org.springcenter.background.modules.model.product.*;
import org.springcenter.background.modules.service.LianXiangYunFilezService;
import org.springcenter.background.modules.service.WrongGoodsService;
import org.springcenter.background.modules.util.FileParseUtil;
import org.springcenter.product.api.constant.IdConstant;
import org.springcenter.background.modules.entity.ImportStreetPhotoRealtion;
import org.springcenter.product.api.dto.background.ChangeStatusReq;
import org.springcenter.product.api.dto.background.WrongGoodsMainReq;
import org.springcenter.product.api.dto.background.WrongGoodsMainResp;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.api.lenovo.LenovoFileResp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class WrongGoodsServiceImpl implements WrongGoodsService {

    @Autowired
    private LianXiangYunFilezService lianXiangYunFilezService;

    @Autowired
    private WrongGoodsMainMapper wrongGoodsMainMapper;

    @Autowired
    private WrongGoodsImgsMapper wrongGoodsImgsMapper;

    @Autowired
    private QiniuUtil qiniuUtil;

    @Autowired
    private WrongGoodsProductsMapper wrongGoodsProductsMapper;

    @Autowired
    private WrongGoodsPimgsRelationMapper wrongGoodsPimgsRelationMapper;

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Override
    public void syncStreetPhotoImg(String netDiskPath,String wrongGoodsMainId) {
        if(StringUtils.isBlank(netDiskPath)){
            log.info("syncStreetPhotoImg 传递的路径为空 ，请重新传递!");
            return ;
        }
        // 同步数据处理
        int i = 0;
        while (true){
            LenovoFileResp resp = lianXiangYunFilezService.getLenovoFileResp(netDiskPath, i);
            if (resp == null || CollectionUtils.isEmpty(resp.getFileModelList())) {
                break ;
            }
            //一直读取到最后
            List<LenovoFileResp.LenovoFileModel> fileModelList = resp.getFileModelList();
            readLianxiangFile(fileModelList,0,0,wrongGoodsMainId);
            i++;
        }
    }

    @Override
    public void syncEcomImg(String netDiskPath) {
        if(StringUtils.isBlank(netDiskPath)){
            log.info("syncEcomImg 传递的路径为空 ，请重新传递!");
            return ;
        }
        // 同步数据处理
        int i = 0;
        while (true){
            LenovoFileResp resp = lianXiangYunFilezService.getLenovoFileResp(netDiskPath, i);
            if (resp == null || CollectionUtils.isEmpty(resp.getFileModelList())) {
                break ;
            }
            //一直读取到最后
            List<LenovoFileResp.LenovoFileModel> fileModelList = resp.getFileModelList();
            readLianxiangFile(fileModelList,0,1,null);
            i++;
        }
    }

    @Override
    public String importStreetPhotoRelation(String url,String keys) {
        List<Map<String,String>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {

            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,String> result = new HashMap<>();
                    //搭配号
                    result.put("stylingCode",integerStringMap.get(0));
                    result.put("sampleCloth",integerStringMap.get(1));
                    result.put("imgs",integerStringMap.get(2));
                    //款色号
                    result.put("skc",integerStringMap.get(3));
                    importData.add(result);
                }
            }
        });
        // 数据整合
        List<ImportStreetPhotoRealtion> list = buildStreetPhotoData(importData);

        // 上传七牛云 将地址发给前端
        if (CollectionUtils.isNotEmpty(list)) {
            String fileName = System.currentTimeMillis() + ".xlsx";
            EasyExcelUtil.write(fileName, ImportStreetPhotoRealtion.class, new IWriteDataExcel<ImportStreetPhotoRealtion>() {
                @Override
                public List<ImportStreetPhotoRealtion> getData() {
                    return list.stream().map(item -> {
                        ImportStreetPhotoRealtion data = new ImportStreetPhotoRealtion();
                        data.setStylingCode(item.getStylingCode());
                        data.setSampleCloth(item.getSampleCloth());
                        data.setImgs(item.getImgs());
                        data.setSkc(item.getSkc());
                        return data;
                    }).collect(Collectors.toList());
                }
            });
            File file = new File(fileName);
            String param = qiniuUtil.upload(file.getPath(), "异常货不对版excel"+System.currentTimeMillis() + ".xlsx");
            Integer success = importData.size() - list.size();
            Integer fail = list.size();
            Integer sum = success + fail;
            param = param + ";" + sum + ";" + success + ";" + fail;
            file.delete();
            log.info("redis的key{}", param);
            return param;
        }
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
        return "";
    }

    @Override
    public List<WrongGoodsMainResp> list(WrongGoodsMainReq requestData, Page page) {
        List<WrongGoodsMainResp> result = new ArrayList<>();
        // 查询数据
        com.github.pagehelper.Page<WrongGoodsMain> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<WrongGoodsMain> list = wrongGoodsMainMapper.selectByRequestData(requestData);
        PageInfo<WrongGoodsMain> pageInfo = new PageInfo(hPage);
        list = pageInfo.getList();
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        if(CollectionUtils.isNotEmpty(list)){
            // 处理数据  获取 商品个数  获取 图片个数   获取电商图片个数
            List<String> stylingCodes = list.stream().map(r -> r.getStylingCode()).collect(Collectors.toList());
            // 多个product
            List<WrongGoodsProducts> products = wrongGoodsProductsMapper.selectByStylingCodes(stylingCodes);
            // 分组
            Map<String, List<WrongGoodsProducts>> productsGroupByStylingCode = products.stream().collect(Collectors.groupingBy(r -> r.getStylingCode()));

            // 获取 街拍图片
            List<WrongGoodsImgs> imgs = wrongGoodsImgsMapper.selectByStylingCodes(stylingCodes);
            //分组
            Map<String, List<WrongGoodsImgs>> imgGroupByStylingCode= imgs.stream().collect(Collectors.groupingBy(r -> r.getStylingCode()));
            // 分组skc
            Map<String, List<WrongGoodsImgs>> ecomGroupBySkc = new HashMap<>();
            // 获取匹配的 电商图片
            if(CollectionUtils.isNotEmpty(products)){
                // skc多个
                List<String> skcsAll = products.stream().map(r -> r.getSkc()).collect(Collectors.toList());
                // 所有的ecomImgs
                List<WrongGoodsImgs> ecomImgs = wrongGoodsImgsMapper.selectBySkcsAndStylingCodeIsNull(skcsAll);
                ecomGroupBySkc = ecomImgs.stream().collect(Collectors.groupingBy(r -> r.getSkc()));

            }
            for (WrongGoodsMain wrongGoodsMain : list) {
                WrongGoodsMainResp wrongGoodsMainResp = new WrongGoodsMainResp();
                BeanUtils.copyProperties(wrongGoodsMain,wrongGoodsMainResp);
                // 赋值
                List<WrongGoodsProducts> wrongGoodsProducts = productsGroupByStylingCode.get(wrongGoodsMain.getStylingCode());
                if(CollectionUtils.isNotEmpty(wrongGoodsProducts)){
                    wrongGoodsMainResp.setProductNum(wrongGoodsProducts.size());
                    // 封装数据
                    List<WrongGoodsMainResp.SkcAndRelation> collect = wrongGoodsProducts.stream().map(r -> {
                        WrongGoodsMainResp.SkcAndRelation skcAndRelation = new WrongGoodsMainResp.SkcAndRelation();
                        skcAndRelation.setSkc(r.getSkc());
                        skcAndRelation.setId(r.getId());
                        skcAndRelation.setReason(r.getReason());
                        return skcAndRelation;
                    }).collect(Collectors.toList());

                    wrongGoodsMainResp.setSkcs(collect);
                }
                List<WrongGoodsImgs> wrongGoodsImgs = imgGroupByStylingCode.get(wrongGoodsMain.getStylingCode());
                if(CollectionUtils.isNotEmpty(wrongGoodsImgs)){
                    List<WrongGoodsMainResp.ImgData> collect = wrongGoodsImgs.stream().map(r -> {
                        WrongGoodsMainResp.ImgData imgData = new WrongGoodsMainResp.ImgData();
                        imgData.setId(r.getId());
                        imgData.setNeid(r.getNeid());
                        imgData.setNsid(r.getNsid());
                        return imgData;
                    }).collect(Collectors.toList());
                    wrongGoodsMainResp.setImgData(collect);
                    wrongGoodsMainResp.setImgNum(wrongGoodsImgs.size());
                }
                // 最终的匹配的数值 电商图片个数
                if(CollectionUtils.isNotEmpty(wrongGoodsMainResp.getSkcs())){
                    int num = 0;
                    List<WrongGoodsMainResp.SkcAndRelation> skcs = wrongGoodsMainResp.getSkcs();
                    for (WrongGoodsMainResp.SkcAndRelation skc : skcs) {
                        List<WrongGoodsImgs> wrongGoodsImgs1 = ecomGroupBySkc.get(skc.getSkc());
                        if(CollectionUtils.isNotEmpty(wrongGoodsImgs1)){
                            num += wrongGoodsImgs1.size();
                            // 设置信息
                            List<WrongGoodsMainResp.ImgData> collect = wrongGoodsImgs1.stream().map(r -> {
                                WrongGoodsMainResp.ImgData imgData = new WrongGoodsMainResp.ImgData();
                                imgData.setId(r.getId());
                                imgData.setNeid(r.getNeid());
                                imgData.setNsid(r.getNsid());
                                return imgData;
                            }).collect(Collectors.toList());
                            skc.setImgDatas(collect);
                        }
                    }
                    wrongGoodsMainResp.setRealationNum(num);
                }
                result.add(wrongGoodsMainResp);
            }
            // 如果数值只有一个  则进行查询标记信息

            for (WrongGoodsMainResp wrongGoodsMainResp : result) {
                // 查询标记数值  电商图片标记
                List<WrongGoodsMainResp.SkcAndRelation> skcs = wrongGoodsMainResp.getSkcs();

                if(CollectionUtils.isNotEmpty(skcs)){
                    // 查询数据  把所有数据都拿出来
                    List<String> wrongGoodsProductIds = skcs.stream().map(r -> r.getId()).collect(Collectors.toList());
                    // 查询数据
                    List<WrongGoodsPimgsRelation> wrongGoodsPimgsRelations = wrongGoodsPimgsRelationMapper.selectByWrongGoodsProductIds(wrongGoodsProductIds);
                    Map<String, List<WrongGoodsPimgsRelation>> groupByProductId = wrongGoodsPimgsRelations.stream().collect(Collectors.groupingBy(r -> r.getWrongGoodsProductId()));

                    // 对比数据
                    for (WrongGoodsMainResp.SkcAndRelation skc : skcs) {
                        String id = skc.getId();
                        List<WrongGoodsMainResp.ImgData> imgDatas = skc.getImgDatas();
                        List<WrongGoodsPimgsRelation> wrongGoodsPimgsRelations1 = groupByProductId.get(id);
                        if(CollectionUtils.isNotEmpty(wrongGoodsPimgsRelations1)){
                            for (WrongGoodsPimgsRelation wrongGoodsPimgsRelation : wrongGoodsPimgsRelations1) {
                                for (WrongGoodsMainResp.ImgData imgData : imgDatas) {
                                    if(imgData.getId().equals(wrongGoodsPimgsRelation.getWrongGoodsImgsId())){
                                        imgData.setIsFlag(1);
                                    }
                                }
                            }
                        }
                    }

                    // 街拍图片标记
                    List<WrongGoodsMainResp.ImgData> imgData = wrongGoodsMainResp.getImgData();
                    List<String> imgIds = imgData.stream().map(r -> r.getId()).collect(Collectors.toList());
                    List<WrongGoodsPimgsRelation> haveWrongRelation = wrongGoodsPimgsRelations.stream()
                            .filter(r -> imgIds.contains(r.getWrongGoodsImgsId())).filter(r->wrongGoodsProductIds.contains(r.getWrongGoodsProductId())).collect(Collectors.toList());

                    for (WrongGoodsMainResp.ImgData imgDatum : imgData) {
                        for (WrongGoodsPimgsRelation wrongGoodsPimgsRelation : haveWrongRelation) {
                            if(imgDatum.getId().equals(wrongGoodsPimgsRelation.getWrongGoodsImgsId())){
                                imgDatum.setIsFlag(1);
                            }
                        }
                    }
                }
            }
        }

        return result;
    }

    @Override
    public void changeStatus(ChangeStatusReq requestData) {
        WrongGoodsMain wrongGoodsMain = wrongGoodsMainMapper.selectById(requestData.getId());
        if(wrongGoodsMain != null){
            WrongGoodsMain update = new WrongGoodsMain();
            update.setId(requestData.getId());
            update.setStatus(requestData.getStatus());
            update.setRemark(requestData.getRemark());

            template.execute(action->{
                if(CollectionUtils.isNotEmpty(requestData.getSubmitRelationShipList())){
                    // 处理数据
                    List<ChangeStatusReq.SubmitRelationShip> submitRelationShipList = requestData.getSubmitRelationShipList();
                    List<String> wrongProductIds = submitRelationShipList.stream().map(r -> r.getWrongGoodsProductId()).collect(Collectors.toList());

                    List<WrongGoodsPimgsRelation> insertData = new ArrayList<>();
                    for (ChangeStatusReq.SubmitRelationShip submitRelationShip : submitRelationShipList) {
                        WrongGoodsPimgsRelation wrongGoodsPimgsRelation = new WrongGoodsPimgsRelation();
                        BeanUtils.copyProperties(submitRelationShip,wrongGoodsPimgsRelation);
                        wrongGoodsPimgsRelation.setCreateTime(new Date());
                        wrongGoodsPimgsRelation.setUpdateTime(new Date());
                        wrongGoodsPimgsRelation.setIsDel(IsDeleteEnum.NORMAL.getCode());
                        wrongGoodsPimgsRelation.setId(IdLeaf.getId(IdConstant.WRONG_GOODS_PIMGS_RELATION));
                        insertData.add(wrongGoodsPimgsRelation);
                    }

                    //重新新增
                    if(CollectionUtils.isNotEmpty(insertData)){
                        // 修改为删除  然后重新新增
                        wrongGoodsPimgsRelationMapper.updateIsDelByWrongProductIds(wrongProductIds,IsDeleteEnum.IS_DELETED.getCode());
                        wrongGoodsPimgsRelationMapper.batchInsert(insertData);
                    }
                }
                wrongGoodsMainMapper.updateById(update);
                return action;
            });
        }
    }

    @Override
    public void reSyncStreetPhoto(String wrongGoodsMainId) {
        WrongGoodsMain wrongGoodsMain = wrongGoodsMainMapper.selectById(wrongGoodsMainId);
        // 删除老的
        wrongGoodsImgsMapper.updateIsDelByStylingCode(wrongGoodsMain.getStylingCode(),IsDeleteEnum.IS_DELETED.getCode());
        syncStreetPhotoImg(wrongGoodsMain.getSyncPath(),wrongGoodsMainId);
    }

    @Override
    public String importWrongReason(String url, Object o) {
        List<Map<String,String>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {

            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,String> result = new HashMap<>();
                    //搭配号
                    result.put("productCode",integerStringMap.get(0));
                    result.put("color",integerStringMap.get(1));
                    result.put("reason",integerStringMap.get(2));
                    importData.add(result);
                }
            }
        });
        // 数据整合
        List<ImportWrongReason> list = buildWrongReason(importData);

        // 上传七牛云 将地址发给前端
        if (CollectionUtils.isNotEmpty(list)) {
            String fileName = System.currentTimeMillis() + ".xlsx";
            EasyExcelUtil.write(fileName, ImportWrongReason.class, new IWriteDataExcel<ImportWrongReason>() {
                @Override
                public List<ImportWrongReason> getData() {
                    return list.stream().map(item -> {
                        ImportWrongReason data = new ImportWrongReason();
                        data.setProductCode(item.getProductCode());
                        data.setColor(item.getColor());
                        data.setReason(item.getReason());
                        data.setErrorMsg(item.getErrorMsg());
                        return data;
                    }).collect(Collectors.toList());
                }
            });
            File file = new File(fileName);
            String param = qiniuUtil.upload(file.getPath(), "异常货不对版"+System.currentTimeMillis() + ".xlsx");
            Integer success = importData.size() - list.size();
            Integer fail = list.size();
            Integer sum = success + fail;
            param = param + ";" + sum + ";" + success + ";" + fail;
            file.delete();
            log.info("redis的key{}", param);
            return param;
        }
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
        return "";
    }

    private List<ImportWrongReason> buildWrongReason(List<Map<String, String>> importData) {
        List<ImportWrongReason> errorList = new ArrayList<>();

        for (Map<String, String> importDatum : importData) {
            if(StringUtils.isBlank(importDatum.get("productCode")) || StringUtils.isBlank(importDatum.get("color"))  || StringUtils.isBlank(importDatum.get("reason"))){
                ImportWrongReason importStreetPhotoRealtion = new ImportWrongReason();
                importStreetPhotoRealtion.setProductCode(importDatum.get("productCode"));
                importStreetPhotoRealtion.setColor(importDatum.get("color"));
                importStreetPhotoRealtion.setReason(importDatum.get("reason"));
                importStreetPhotoRealtion.setErrorMsg("检查数据是否传递了空信息");
                errorList.add(importStreetPhotoRealtion);
                continue;
            }
            String color = importDatum.get("color").trim();
            String productCode = importDatum.get("productCode").trim();
            String reason = importDatum.get("reason").trim();
            // 处理数据  stylingCode 和 skc  需要去除空格
            List<WrongGoodsProducts> wrongGoodsProducts = wrongGoodsProductsMapper.selectByProductCodeAndColor(productCode,color);
            if(CollectionUtils.isNotEmpty(wrongGoodsProducts)){
                // 依次更新
                for (WrongGoodsProducts wrongGoodsProduct : wrongGoodsProducts) {
                    WrongGoodsProducts update = new WrongGoodsProducts();
                    update.setId(wrongGoodsProduct.getId());
                    update.setUpdateTime(new Date());
                    update.setReason(reason);
                    wrongGoodsProductsMapper.updateById(update);
                }
            }else{
                ImportWrongReason importStreetPhotoRealtion = new ImportWrongReason();
                importStreetPhotoRealtion.setProductCode(importDatum.get("productCode"));
                importStreetPhotoRealtion.setColor(importDatum.get("color"));
                importStreetPhotoRealtion.setReason(importDatum.get("reason"));
                importStreetPhotoRealtion.setErrorMsg("根据款色未查询到数据，请检查后进行导入");
                errorList.add(importStreetPhotoRealtion);
            }
        }
        return errorList;



    }

    private List<ImportStreetPhotoRealtion> buildStreetPhotoData(List<Map<String, String>> importData) {
        List<ImportStreetPhotoRealtion> errorList = new ArrayList<>();

        for (Map<String, String> importDatum : importData) {
            if(StringUtils.isBlank(importDatum.get("stylingCode")) || StringUtils.isBlank(importDatum.get("skc"))){
                ImportStreetPhotoRealtion importStreetPhotoRealtion = new ImportStreetPhotoRealtion();
                importStreetPhotoRealtion.setStylingCode(importDatum.get("stylingCode"));
                importStreetPhotoRealtion.setSampleCloth(importDatum.get("sampleCloth"));
                importStreetPhotoRealtion.setImgs(importDatum.get("imgs"));
                importStreetPhotoRealtion.setSkc(importDatum.get("skc"));
                errorList.add(importStreetPhotoRealtion);
                continue;
            }
            String skc = importDatum.get("skc").trim();
            String stylingCode = importDatum.get("stylingCode").trim();
            // 处理数据  stylingCode 和 skc  需要去除空格
            List<WrongGoodsProducts> wrongGoodsProducts = wrongGoodsProductsMapper.selectByStylingCodeAndSkc(stylingCode,skc);
            if(CollectionUtils.isEmpty(wrongGoodsProducts)){
                WrongGoodsProducts insertData = new WrongGoodsProducts();
                insertData.setId(IdLeaf.getId(IdConstant.WRONG_GOODS_PRODUCTS));
                insertData.setCreateTime(new Date());
                insertData.setUpdateTime(new Date());
                insertData.setSkc(skc);
                insertData.setProductCode(skc.substring(0,skc.length()-3));
                insertData.setColorNo(skc.substring(skc.length()-3));
                insertData.setStylingCode(stylingCode);
                insertData.setIsDel(IsDeleteEnum.NORMAL.getCode());
                wrongGoodsProductsMapper.insert(insertData);
            }
        }
        return errorList;
    }

    /**
     * 处理 街拍图片   0
     * 处理 电商图片   1
     * @param fileModelList
     * @param pageNo  页码
     * @param type 处理 街拍图片   0  处理 电商图片   1
     */
    private void readLianxiangFile(List<LenovoFileResp.LenovoFileModel> fileModelList, int pageNo,int type,String wrongGoodsMainId) {

        for (LenovoFileResp.LenovoFileModel lenovoFileModel : fileModelList) {
            Boolean dir = lenovoFileModel.getDir();
            pageNo = 0;
            if (dir) {
                int pageNo2  = 0;
                while (true){
                    LenovoFileResp lenovoFileResp = lianXiangYunFilezService.getLenovoFileResp(lenovoFileModel.getPath(), pageNo);
                    if (lenovoFileResp == null || CollectionUtils.isEmpty(lenovoFileResp.getFileModelList())) {
                        break ;
                    }
                    readLianxiangFile(lenovoFileResp.getFileModelList(),pageNo2,type,wrongGoodsMainId);
                    pageNo ++;
                }
            }else{
                //  不是文件夹   如果是图片则
                try {
                    if(type == 0){
                        // 处理 街拍图片
                        if( lenovoFileModel.getPath().endsWith(".xls") || lenovoFileModel.getPath().endsWith(".xlsx")){
                            // 直接处理 落库
                            dealExcel(lenovoFileModel);
                        }else{
                            // 直接处理 落库
                            dealImg(lenovoFileModel,wrongGoodsMainId);
                        }
                    }else if(type == 1){
                        // 处理电商
                        // 直接处理 落库
                        dealEcomImg(lenovoFileModel);
                    }
                }catch (Exception e){
                    log.info("readLianxiangFile = {}", JSONObject.toJSONString(lenovoFileModel),e);
                }
            }
        }
    }

    private void dealEcomImg(LenovoFileResp.LenovoFileModel lenovoFileModel) {
        String path = lenovoFileModel.getPath();
        String colorNo = "";
        String productCode = "";
        if(lenovoFileModel.getPath().contains("(正面图)") || lenovoFileModel.getPath().contains("(背面图)") ){
            // 处理为正面图
            // 电商处理文件名
            // //企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款/色/文件.jpg
            // 文件名  将款和色处理出来
            // 生成为
            //企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款/色
            String substring = path.substring(0, path.lastIndexOf("/"));
            // 色
            colorNo = substring.substring(substring.lastIndexOf("/") + 1);
            if(colorNo.length()> 3){
                colorNo = colorNo.substring(0,3);
            }
            // 生成为
            // 企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款
            String productPath = substring.substring(0, substring.lastIndexOf("/"));
            // 款
            productCode = productPath.substring(productPath.lastIndexOf("/")+1);

        }else if(lenovoFileModel.getPath().contains("_全身图_")){
            // 处理为全身图
            //企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款/色
            String substring = path.substring(0, path.lastIndexOf("/"));
            // 色
            colorNo = substring.substring(substring.lastIndexOf("/")+1);
            if(colorNo.length()> 3){
                colorNo = colorNo.substring(0,3);
            }
            // 生成为
            // 企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款
            String productPath = substring.substring(0, substring.lastIndexOf("/"));
            // 款
            productCode = productPath.substring(productPath.lastIndexOf("/")+1);
        }else{
            return ;
        }

        // 查询数据是否有
        List<WrongGoodsImgs> wrongGoodsImgs = wrongGoodsImgsMapper.selectByNeId(lenovoFileModel.getNeid());
        if(CollectionUtils.isEmpty(wrongGoodsImgs)){
            WrongGoodsImgs insertData = new WrongGoodsImgs();
            insertData.setId(IdLeaf.getId(IdConstant.WRONG_GOODS_IMGS));
            insertData.setNeid(lenovoFileModel.getNeid());
            insertData.setNsid(lenovoFileModel.getNsid());
            insertData.setIsDel(IsDeleteEnum.NORMAL.getCode());
            insertData.setColorNo(colorNo);
            insertData.setProductCode(productCode);
            insertData.setSkc(productCode+colorNo);
            insertData.setCreateTime(new Date());
            insertData.setUpdateTime(new Date());
            wrongGoodsImgsMapper.insert(insertData);
        }
    }

    private void dealImg(LenovoFileResp.LenovoFileModel lenovoFileModel, String wrongGoodsMainId) {
        // 处理图片    获取前 7位  查询数据库主表中是否有  如果没有则插入主表  如果主表有    图片表没有   插入  如果图片表也有  则直接结束
        String nameAndSuffix = lenovoFileModel.getPath().substring(lenovoFileModel.getPath().lastIndexOf("/") + 1);
        // 文件名称
        String name = nameAndSuffix.substring(0, nameAndSuffix.indexOf("."));
        if(name.length() > 8){
            name = name.substring(0,8);
        }
        //取前7位 作为搭配号
        String stylingCode = name.substring(0, 7);
        // 获取 月份
        String substring = lenovoFileModel.getPath().substring(0, lenovoFileModel.getPath().lastIndexOf("/"));
        String month = substring.substring(substring.lastIndexOf("/") + 1);
        String finalMonth = month.substring(0, month.length() - 1);
        // 获取货季和品牌
        String substring1 = substring.substring(0, substring.lastIndexOf("/"));
        String brandAndSeason = substring1.substring(substring1.lastIndexOf("/") + 1);
        String[] split = brandAndSeason.split("-");
        String brand = split[0];
        String season = split[1];

        // 根据搭配号查询数据库
        List<WrongGoodsMain> wrongGoodsMains = wrongGoodsMainMapper.selectByStylingCode(stylingCode);
        if(CollectionUtils.isEmpty(wrongGoodsMains)){
            // 插入数据         // 如果数据库没有 则进行插入主表
            WrongGoodsMain insertData = new WrongGoodsMain();
            insertData.setId(IdLeaf.getId(IdConstant.WRONG_GOODS_MAIN));
            insertData.setSeason(season);
            insertData.setMonth(finalMonth);
            insertData.setStylingCode(stylingCode);
            insertData.setBrand(brand);
            insertData.setStatus(0);
            insertData.setIsDel(IsDeleteEnum.NORMAL.getCode());
            insertData.setCreateTime( new Date());
            insertData.setUpdateTime(new Date());
            // 同步路径
            insertData.setSyncPath( lenovoFileModel.getPath().substring(0,lenovoFileModel.getPath().lastIndexOf("/")));
            wrongGoodsMainMapper.insert(insertData);
        }
        // 根据neid查询图片表  如果有结束  没有则插入
        List<WrongGoodsImgs> wrongGoodsImgs = wrongGoodsImgsMapper.selectByNeId(lenovoFileModel.getNeid());
        if(CollectionUtils.isEmpty(wrongGoodsImgs)){
            if(StringUtils.isNotBlank(wrongGoodsMainId)){
                // 查询
                WrongGoodsMain wrongGoodsMain = wrongGoodsMainMapper.selectById(wrongGoodsMainId);
                String stylingCode1 = wrongGoodsMain.getStylingCode();
                // 包含则可以添加
                if(!stylingCode.contains(stylingCode1)){
                    return ;
                }
            }
            WrongGoodsImgs insertData = new WrongGoodsImgs();
            insertData.setId(IdLeaf.getId(IdConstant.WRONG_GOODS_IMGS));
            insertData.setNeid(lenovoFileModel.getNeid());
            insertData.setNsid(lenovoFileModel.getNsid());
            insertData.setIsDel(IsDeleteEnum.NORMAL.getCode());
            insertData.setStylingCode(stylingCode);
            insertData.setCreateTime(new Date());
            insertData.setUpdateTime(new Date());
            wrongGoodsImgsMapper.insert(insertData);
        }

    }

    private void dealExcel(LenovoFileResp.LenovoFileModel lenovoFileModel) {
        // 变更为上传excel  不从网盘里搞excel
        // 处理商品 查询数据库主表中是否有  没有 插入主表   如果主表有  则不插入，  商品表 没有 插入  如果商品表有  则不处理
        return ;
    }

}
