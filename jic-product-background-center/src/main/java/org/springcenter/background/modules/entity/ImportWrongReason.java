package org.springcenter.background.modules.entity;

import lombok.Data;
import org.springcenter.background.modules.util.ExcelField;

@Data
public class ImportWrongReason {

    @ExcelField(value = "款号")
    private String productCode;
    @ExcelField(value = "色号")
    private String color;
    @ExcelField(value = "原因")
    private String reason;

    @ExcelField(value = "导入失败原因")
    private String errorMsg;

}
