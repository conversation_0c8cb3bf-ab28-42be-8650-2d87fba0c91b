package org.springcenter.background.modules.mapper.product;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.WrongGoodsImgs;
import org.springcenter.background.modules.model.product.WrongGoodsProducts;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【WRONG_GOODS_IMGS(货不对版  图片)】的数据库操作Mapper
* @createDate 2024-10-22 09:21:33
* @Entity generator.domain.WrongGoodsImgs
*/
public interface WrongGoodsImgsMapper extends BaseMapper<WrongGoodsImgs> {

    List<WrongGoodsImgs> selectByNeId(@Param("neid") String neid);

    List<WrongGoodsImgs> selectByStylingCodes(@Param("stylingCodes") List<String> stylingCodes);

    List<WrongGoodsImgs> selectBySkcsAndStylingCodeIsNull(@Param("skcsAll") List<String> skcsAll);

    void updateIsDelByStylingCode(@Param("stylingCode") String stylingCode, @Param("isDel") Integer isDel);
}




