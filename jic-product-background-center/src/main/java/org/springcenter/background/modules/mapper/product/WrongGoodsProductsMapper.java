package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.WrongGoodsProducts;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【WRONG_GOODS_PRODUCTS(货不对版  商品)】的数据库操作Mapper
* @createDate 2024-10-22 09:21:33
* @Entity generator.domain.WrongGoodsProducts
*/
public interface WrongGoodsProductsMapper extends BaseMapper<WrongGoodsProducts> {

    List<WrongGoodsProducts> selectByStylingCodeAndSkc(@Param("stylingCode") String stylingCode,@Param("skc")  String skc);

    List<WrongGoodsProducts> selectByStylingCodes(@Param("stylingCodes") List<String> stylingCodes);

    List<WrongGoodsProducts> selectByProductCodeAndColor(@Param("productCode")String productCode,@Param("color")  String color);
}




