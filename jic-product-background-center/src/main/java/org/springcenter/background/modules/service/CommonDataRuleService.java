package org.springcenter.background.modules.service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date:2023/8/2 13:22
 */
public interface CommonDataRuleService {

    /**
     * 获取用户品牌权限
     * @param component 权限控制组件参数
     * @param brandIds 查询的品牌
     * @return 返回交集品牌值
     */
    List<Long> getAllowBrandIdRule(String component, List<Long> brandIds);

    /**
     * 获取有权限的渠道
     * @param component 权限控制组件参数
     * @return 有权限的渠道
     */
    List<String> getAllowChannel(String component);

    /**
     * 获取文件地址
     * @param idList
     * @return 文件id:文件地址
     */
    Map<String, String> listFileUrl(List<String> idList);


    /**
     * 获取有权限的数据
     * @param component 权限控制组件参数
     * @return 符合参数的数据
     * 默认没有任何数据权限
     */
    List<String> getAllowRuleData(String component, String ruleType);
}
