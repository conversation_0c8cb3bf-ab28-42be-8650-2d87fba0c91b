package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.api.dto.message.TemplateMessageDTO;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springcenter.background.modules.mapper.product.FabInfoMapper;
import org.springcenter.background.modules.mapper.product.ProductReminderInfoMapper;
import org.springcenter.background.modules.model.product.ProductReminderInfo;
import org.springcenter.background.modules.service.IProductReminderService;
import org.springcenter.background.modules.util.DateUtil;
import org.springcenter.product.api.dto.background.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2025/5/15 16:09
 */
@Slf4j
@Service
@RefreshScope
public class IProductReminderServiceImpl implements IProductReminderService {

    @Value("${reminder.type.list}")
    private String reminderTypeList;

    @Autowired
    private ProductReminderInfoMapper productReminderInfoMapper;

    @Value("${reminder.id.leaf.tag}")
    private String reminderIdLeafTag;

    @Value("${reminder.fab.info}")
    private String reminderFabInfo;


    @Override
    public List<ReminderTypeListResp> getReminderTypeList() {
        Map<String, String> map = JSONObject.parseObject(reminderTypeList, Map.class);
        List<ReminderTypeListResp> list = new ArrayList<>();
        map.entrySet().forEach(v -> {
            ReminderTypeListResp resp = new ReminderTypeListResp();
            resp.setType(Integer.valueOf(v.getKey()));
            resp.setName(v.getValue());
            list.add(resp);
        });
        return list;
    }

    @Override
    public List<ReminderListResp> getReminderList(ReminderListReq requestData, Page page) {
        com.github.pagehelper.Page<ReminderListResp> page1 = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<ProductReminderInfo> list = productReminderInfoMapper.selectReminderListByParams(requestData.getType());
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<ReminderListResp> rets = new ArrayList<>();
        list.forEach(v -> {
            ReminderListResp resp = new ReminderListResp();
            BeanUtils.copyProperties(v, resp);
            resp.setReminderTime(DateUtil.formatToStr(v.getReminderTime(), DateUtil.DATE_FORMAT_YMD));
            rets.add(resp);
        });
        PageInfo<ReminderListResp> pageInfo = new PageInfo(page1);
        pageInfo.setList(rets);
        page.setCount(page1.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    @Override
    public Boolean delReminderInfo(String requestData) {
        productReminderInfoMapper.delReminderInfoById(requestData);
        return true;
    }

    @Override
    public Boolean addReminderInfo(ReminderAddInfoReq requestData) {
        ProductReminderInfo reminderInfo = new ProductReminderInfo();
        BeanUtils.copyProperties(requestData, reminderInfo);
        reminderInfo.setCreateTime(new Date());
        reminderInfo.setUpdateTime(new Date());
        reminderInfo.setId(IdLeaf.getId(reminderIdLeafTag));
        productReminderInfoMapper.insert(reminderInfo);
        return true;
    }

    @Override
    public Boolean updateReminderInfo(ReminderUpdateInfoReq requestData) {
        ProductReminderInfo info = productReminderInfoMapper.selectById(requestData.getId());
        if (info == null) {
            throw new RuntimeException("数据不存在");
        }
        BeanUtils.copyProperties(requestData, info);
        info.setUpdateTime(new Date());
        productReminderInfoMapper.updateById(info);
        return true;
    }

    @Override
    public ProductReminderInfo getReminderInfo(String requestData) {
        ProductReminderInfo info = productReminderInfoMapper.selectById(requestData);
        if (info == null) {
            throw new RuntimeException("数据不存在");
        }
        return info;
    }

    @Autowired
    private ISysBaseAPI iSysBaseAPI;

    @Override
    public Boolean manualReminder(ReminderInfoReq requestData) {
        ProductReminderInfo info = productReminderInfoMapper.selectById(requestData.getId());
        if (info == null) {
            throw new RuntimeException("数据不存在");
        }
        AtomicReference<Boolean> isUpdate = new AtomicReference<>(false);
        Arrays.stream(requestData.getTemplateConcat().split(","))
                .forEach(v -> {
            TemplateMessageDTO messageDTO = new TemplateMessageDTO();
            messageDTO.setTemplateCode(requestData.getTemplateCode());
            Map<String, String> params = new HashMap<>();
            params.put("param", requestData.getTemplateContent());
            messageDTO.setTemplateParam(params);
            messageDTO.setFromUser("admin");
            messageDTO.setToUser(Objects.toString(v));
            try {
                log.info("发送模板消息：{}", JSONObject.toJSONString(messageDTO));
                iSysBaseAPI.sendTemplateAnnouncement(messageDTO);
                isUpdate.set(true);
            }catch (Exception e){
                log.info("发送模板消息异常", e.getMessage());
            }
        });

        if (isUpdate.get()) {
            info.setUpdateTime(new Date());
            info.setReminderTime(new Date());
            productReminderInfoMapper.updateById(info);
        }
        return true;

    }

    @Autowired
    private FabInfoMapper fabInfoMapper;

    @Override
    public Boolean fabReminderJob() {
        List<ProductReminderInfo> list = productReminderInfoMapper.selectByType(0);
        String date = DateUtil.parseDate(new Date(), "yyyy-MM-dd");
        Date dateStart = DateUtil.formatToDate(date + " 00:00:00", DateUtil.DATE_FORMAT_YMDHM);
        Date dateEnd = DateUtil.formatToDate(date + " 23:59:59", DateUtil.DATE_FORMAT_YMDHM);
        List<String> names = fabInfoMapper.selectNames(dateStart, dateEnd);
        if (CollectionUtils.isEmpty(names)) {
            log.info("没有数据");
            return true;
        }
        String nameStr = reminderFabInfo.replace("nameStr", names.stream().collect(Collectors.joining(",")));
        list.forEach(v -> {
            AtomicReference<Boolean> isUpdate = new AtomicReference<>(false);
            Arrays.stream(v.getTemplateConcat().split(",")).forEach(x -> {
                TemplateMessageDTO messageDTO = new TemplateMessageDTO();
                messageDTO.setTemplateCode(v.getTemplateCode());
                Map<String, String> params = new HashMap<>();

                params.put("param", nameStr);
                messageDTO.setTemplateParam(params);
                messageDTO.setFromUser("admin");
                messageDTO.setToUser(Objects.toString(x));
                try {
                    log.info("发送模板消息：{}", JSONObject.toJSONString(messageDTO));
                    iSysBaseAPI.sendTemplateAnnouncement(messageDTO);
                    isUpdate.set(true);
                }catch (Exception e){
                    log.info("发送模板消息异常", e.getMessage());
                }
            });
            if (isUpdate.get()) {
                v.setReminderTime(new Date());
                v.setUpdateTime(new Date());
                productReminderInfoMapper.updateById(v);
            }

        });
        return true;
    }
}
