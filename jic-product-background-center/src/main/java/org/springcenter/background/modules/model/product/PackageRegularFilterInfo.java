package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2024/7/25 15:00
 */
@TableName(value = "PACKAGE_REGULAR_FILTER_INFO")
@Data
public class PackageRegularFilterInfo {
    /**
     *
     */
    @TableField(value = "ID")
    private Integer id;

    /**
     * 对应字段
     */
    @TableField(value = "FIELD")
    private String field;

    /**
     *
     */
    @TableField(value = "CODE")
    private String code;

    /**
     * 对应code显示名称
     */
    @TableField(value = "CODE_VALUE")
    private String codeValue;

    /**
     * 0正常 1已删除
     */
    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    /**
     *
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;
}
