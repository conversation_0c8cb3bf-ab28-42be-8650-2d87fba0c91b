package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2024/7/25 13:41
 */
@TableName(value = "PACKAGE_DEPARTMENT_INFO")
@Data
public class PackageDepartmentInfo {
    /**
     *
     */
    @TableField(value = "ID")
    private String id;

    /**
     * 商品包模板id
     */
    @TableField(value = "PAC_ID")
    private String pacId;


    /**
     * 链接
     */
    @TableField(value = "DEPARTMENT_ID")
    private String departmentId;

    /**
     * 0正常 1删除
     */
    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;



}
