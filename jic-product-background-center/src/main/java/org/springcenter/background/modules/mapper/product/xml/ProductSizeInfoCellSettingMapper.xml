<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.ProductSizeInfoCellSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.ProductSizeInfoCellSetting">
        <id column="ID" property="id" />
        <result column="OUTSIDE_FILED" property="outsideFiled" />
        <result column="SYSTEM_FIELD" property="systemFiled" />
        <result column="BRAND_SETTING_ID" property="brandSettingId" />
        <result column="SMALL_CLASS_ID" property="smallClassId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="SORT" property="sort" />

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, OUTSIDE_FILED, SYSTEM_FIELD, BRAND_SETTING_ID, SMALL_CLASS_ID, CREATE_TIME, UPDATE_TIME,
        IS_DELETED, SORT
    </sql>

    <select id="selectByBandAndSmallClassIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM PRODUCT_SIZE_INFO_CELL_SETTING
        WHERE BRAND_SETTING_ID = #{brandSettingId}
        <if test="smallClassSettingIds != null and smallClassSettingIds.size() > 0">
            and SMALL_CLASS_ID in
            <foreach collection="smallClassSettingIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
            AND IS_DELETED = 0
    </select>

</mapper>