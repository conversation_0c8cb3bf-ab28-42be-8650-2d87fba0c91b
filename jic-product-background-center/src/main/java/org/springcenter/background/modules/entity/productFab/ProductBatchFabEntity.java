package org.springcenter.background.modules.entity.productFab;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springcenter.background.modules.model.product.FabInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/9/5 19:24
 */
@Data
public class ProductBatchFabEntity {

    private List<FabInfo> baseDataList;

    private List<AllFabInfo> errorDataList;


    private List<AllFabInfo> allDataList;

    @Data
    public static class ErrorData {

        @ApiModelProperty(value = "款号")
        private String name;

        @ApiModelProperty(value = "fab信息")
        private String fab;

        @ApiModelProperty(value = "原因")
        private String reason;


    }

    @Data
    public static class AllFabInfo {

        @ApiModelProperty(value = "款号")
        private String name;

        @ApiModelProperty(value = "fab信息")
        private String fab;

        @ApiModelProperty(value = "原因")
        private String reason;

        @ApiModelProperty(value = "类型 0正常 1异常")
        private Integer type;
    }

}
