package org.springcenter.background.modules.mapper.bojun;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.bojun.CArea;

import java.util.List;


/**
 * @Author: lwz
 * @Date: 2023-05-15 19:14:49
 * @Description: Mapper
 */
public interface CAreaMapper extends BaseMapper<CArea> {

    /**
     * 获取有效的区域
     * @return
     */
    List<CArea> getActiveList();

    List<CArea> selectByIds(@Param("list") List<Long> collect);
}
