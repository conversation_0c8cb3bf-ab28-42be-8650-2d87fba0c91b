<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.FabMatchingSugInfoLogMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.FabMatchingSugInfoLog">
        <id column="ID" property="id" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="NAME" property="name" />
        <result column="MATCHING_SUG" property="matchingSug" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="OPERATORS" property="operators" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="FAB_SUG_INFO_ID" property="fabSugInfoId"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, PRODUCT_ID, NAME, MATCHING_SUG, CREATE_TIME, UPDATE_TIME, OPERATORS, IS_DELETED, FAB_SUG_INFO_ID
    </sql>
</mapper>
