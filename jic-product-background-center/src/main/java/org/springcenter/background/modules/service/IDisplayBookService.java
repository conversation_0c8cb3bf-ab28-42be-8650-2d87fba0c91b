package org.springcenter.background.modules.service;

import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import org.springcenter.product.api.dto.*;

import java.util.List;

public interface IDisplayBookService {

    /**
     * 初始化陈列册记录
     * @param requestData
     * @return 主键ID
     */
    String saveInfo(DisplayBookInfoAddReq requestData);

    /**
     * 获取列表记录信息
     * @param requestData 条件
     * @param page 分页信息
     * @param component 组件权限控制
     * @return 返回不包含详情的其他信息
     */
    List<DisplayBookListQueryResp> listInfos(DisplayBookListQueryReq requestData, Page page, String component);
    /**
     * 获取列表记录信息-移动端
     * @param requestData 条件
     * @param page 分页信息
     * @param component 组件权限控制
     * @return 返回不包含详情的其他信息
     */
    List<DisplayBookListForPosQueryResp> listInfosForPos(DisplayBookListForPosQueryReq requestData, Page page, String component);

    /**
     * 获取详情信息
     * @param id 主键id
     * @return 返回包含详情的所有信息
     */
    DisplayBookInfoQueryResp getInfo(String id);

    /**
     * 更新
     * @param requestData 参数包含枚举，根据枚举按策略更新数据
     * @return 没有报错则认为成功
     */
    Boolean updateInfo(DisplayBookInfoUpdateReq requestData);

    /**
     * 删除
     * @param requestData 根据主键id进行删除
     * @return 没有报错则认为成功
     */
    Boolean delete(DisplayBookInfoUpdateReq requestData);

    /**
     * 初始化陈列册文件映射记录
     * @param requestData
     * @return 主键ID
     */
    String saveFileMappingInfo(DisplayBookFileMappingInfoAddReq requestData);

    /**
     * 根据条件分页查询文件映射记录
     * @param requestData
     * @param page
     * @param component
     * @return
     */
    List<DisplayBookFileMappingListQueryResp> listFileMappingInfos(DisplayBookFileMappingListQueryReq requestData, Page page, String component);

    /**
     * 人台图文件解析
     * @param requestData
     * @return 如果解析成功返回空，解析失败则返回对应的excel地址
     */
    String analysisMannequinPictureFile(DisplayBookPictureMappingAddReq requestData);

    /**
     * 货杆图文件解析
     * @param requestData
     * @return 如果解析成功返回空，解析失败则返回对应的excel地址
     */
    String analysisHangerPictureFile(DisplayBookPictureMappingAddReq requestData);

    /**
     * 获取渠道
     * @param requestData
     * @return 有权限的渠道
     */
    List<String> getChannel(String requestData);

    /**
     * 检查陈列册映射是否已经上传
     * @param requestData
     * @return false=不存在映射关系
     */
    Boolean checkFileExist(DisplayBookInfoAddCheckReq requestData);

    /**
     * 检查是否存在陈列册
     * @param id
     * @return false=不存在陈列册
     */
    Boolean checkInfoExist(String id);

//
//
//    /**
//     * 根据id查询详情
//     * @param requestData id
//     * @return 返回
//     */
//    FabVolumeInfoDetailResp searchFabVolumeInfo(String requestData);
//
//
//    /**
//     * 修改fab信息
//     * @param requestData 入参
//     * @return 返回
//     */
//    Boolean updateFabVolumeInfo(UpdateFabVolumeInfoReq requestData);
//
//    /**
//     * 设置展示时间
//     * @param requestData 入参
//     * @return 返回
//     */
//    Boolean setFabVolumeInfoDisplayTime(SetFabVolumeInfoDisplayTimeReq requestData);
//
//
//    /**
//     * 立即发布
//     * @param requestData 入参
//     * @return 返回
//     */
//    Boolean setFabVolumeInfoPublish(SetFabVolumeInfoDisplayTimeReq requestData);
//
//
//    /**
//     * 删除
//     * @param requestData 入参
//     * @return 返回
//     */
//    Boolean deleteFabVolumeInfo(SetFabVolumeInfoDisplayTimeReq requestData);
//
//
//    /**
//     * pos端查询产品册信息
//     * @param requestData 入参
//     * @return 返回
//     */
//    List<FabVolumeInfoListForC> queryFabVolumeInfoForC(QueryFabVolumeInfoReq requestData, Page page);
//
//
//    /**
//     * fab违禁词查询
//     * @return 返回违禁词列表
//     */
//    List<String> queryFabProhibitedWords();
}
