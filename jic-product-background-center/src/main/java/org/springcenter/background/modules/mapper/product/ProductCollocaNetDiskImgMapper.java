package org.springcenter.background.modules.mapper.product;


import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.ProductCollocaNetDiskImg;

import java.util.List;

public interface ProductCollocaNetDiskImgMapper {
    int deleteByPrimaryKey(String id);

    int insert(ProductCollocaNetDiskImg record);

    int insertSelective(ProductCollocaNetDiskImg record);

    ProductCollocaNetDiskImg selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(ProductCollocaNetDiskImg record);

    int updateByPrimaryKey(ProductCollocaNetDiskImg record);

    List<ProductCollocaNetDiskImg> selectByCollocationCode(@Param("collocationCode") String collocationCode, @Param("isDel") Integer isDel);

    void batchInsert(@Param("list") List<ProductCollocaNetDiskImg> insertList);

    List<ProductCollocaNetDiskImg> selectByProductCodeAndColorNo(@Param("productCode") String productCode,@Param("colorNo")  String colorNo, @Param("isDel") Integer isDel);

    List<ProductCollocaNetDiskImg> selectBySkcs(@Param("list") List<String> list);

    List<ProductCollocaNetDiskImg> selectByProductCodesAndType(@Param("productCodes") List<String> productCodes, @Param("type") Integer type);

    List<ProductCollocaNetDiskImg> selectByCollocationCodes(@Param("collocationCodes") List<String> collocationCodes);

    List<ProductCollocaNetDiskImg> selectByNeId(@Param("neId") String neId, @Param("isDel") Integer isDel);
}