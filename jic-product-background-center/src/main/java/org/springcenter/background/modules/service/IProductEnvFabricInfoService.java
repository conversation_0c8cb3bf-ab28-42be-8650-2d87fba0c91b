package org.springcenter.background.modules.service;

import org.springcenter.background.modules.model.product.ProductSellingPoints;
import org.springcenter.product.api.dto.background.ProductSellingPointsEntity;
import org.springcenter.product.api.dto.background.ProductSellingPointsReq;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/1/8 14:14
 */

public interface IProductEnvFabricInfoService {

    /**
     * 生成有环保的面料信息
     */
    void generateEnvironmentFabricInfo(String name);

    /**
     * 根据商品id查询商品面料信息
     * @param requestData
     * @return
     */
    String queryFabDetailEnvInfo(String requestData);

    /**
     * 批量查询商品卖点信息
     * @param requestData 入参
     * @return 返回
     */
    List<ProductSellingPointsEntity> batchQuerySellingPoints(List<String> requestData);

    /**
     * 批量
     * @param requestData
     */
    void addProductSellingPoints(ProductSellingPointsReq requestData);
}
