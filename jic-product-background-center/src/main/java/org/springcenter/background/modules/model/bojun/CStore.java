package org.springcenter.background.modules.model.bojun;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2023/3/10 10:46
 */
@Data
public class CStore {
    private Long id;

    private String isactive;

    private String name;

    private String description;

    private Long cCustomerId;

    private String code;

    private Long cUnionstoreId;

    private Long cArcbrandId;

    private String isNegative;

    private String mobil;

    private String phone;

    private Long cProvinceId;

    private Long cCityId;

    private Long cDistrictId;

    private String ztMonthcode;

    private Long calculation;

    private Double longitude;

    private Double latitude;

    private Long cAreaId;

    @ApiModelProperty(value = "如果是直营 defalut05 = 是 则是奥莱")
    private String default05;

    @ApiModelProperty(value = "如果是直营 defalut05 = 是 则是奥莱")
    private Long cStoreAttrib9Id;
}
