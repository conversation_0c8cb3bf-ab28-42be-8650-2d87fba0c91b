package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springcenter.background.modules.model.product.PackageFilterSetting;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【PACKAGE_FILETR_SEETING(商品包过滤配置)】的数据库操作Mapper
* @createDate 2024-07-25 13:28:59
* @Entity generator.domain.PackageFiletrSeeting
*/
public interface PackageFilterSettingMapper extends BaseMapper<PackageFilterSetting> {


    List<PackageFilterSetting> selectAllValid();
}
