package org.springcenter.background.modules.service.impl;

import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.common.util.excel.IWriteDataExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsSetQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.background.config.exception.ProductException;
import org.springcenter.background.modules.entity.ImportCommonInsertData;
import org.springcenter.background.modules.entity.ProductBatchCommonEntity;
import org.springcenter.background.modules.entity.productFab.KeyInfoParamsEntity;
import org.springcenter.background.modules.entity.productFab.KeyInfoSpuEntity;
import org.springcenter.background.modules.entity.productFab.ProductBatchFabEntity;
import org.springcenter.background.modules.entity.productFab.ProductBatchPatentEntity;
import org.springcenter.background.modules.enums.AutoNameEnum;
import org.springcenter.background.modules.enums.BrandBrandEnum;
import org.springcenter.background.modules.mapper.bojun.BBoxMProductMapper;
import org.springcenter.background.modules.mapper.bojun.MProductListMapper;
import org.springcenter.background.modules.mapper.product.*;
import org.springcenter.background.modules.mapper.bojun.MProductMapper;
import org.springcenter.background.modules.model.bojun.BoxMProduct;
import org.springcenter.background.modules.model.bojun.MProduct;
import org.springcenter.background.modules.model.bojun.MProductList;
import org.springcenter.background.modules.model.product.*;
import org.springcenter.background.modules.remote.entity.FabricInfoRespEntity;
import org.springcenter.background.modules.remote.service.IFabricInfoService;
import org.springcenter.background.modules.service.*;
import org.springcenter.background.modules.util.DateUtil;
import org.springcenter.background.modules.util.EsUtil;
import org.springcenter.background.modules.util.FileParseUtil;
import org.springcenter.background.modules.util.StringSortUtil;
import org.springcenter.product.api.IProductDetailsImgApi;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.dto.background.fab.*;
import org.springcenter.product.api.dto.fab.HomeWashInfoEsResp;
import org.springcenter.product.api.dto.fab.WpPicInfoResp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import strman.Strman;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/6/19 15:10
 */
@Service
@Slf4j
@RefreshScope
public class IProductFabServiceImpl implements IProductFabService {
    @Autowired
    private IFabricInfoService fabricInfoService;

    @Value("${fabric.weight.unit}")
    private String fabricWeightUnit;

    @Autowired
    private QiniuUtil qiniuUtil;

    @Autowired
    private RedisPoolUtil redisPoolUtil;

    @Value("${product.spu.fab.index}")
    private String productSpuFabIndex;

    @Value("${product.skc.fab.new.arrival.index}")
    private String productSkcFabNewArrivalIndex;

    @Value("${bandIds.config}")
    private String bandIdsConfig;

    @Value("${category.name}")
    private String categoryNames;

    @Value("${product.skc.index}")
    private String PRODUCT_SKC_INDEX;

    @Autowired
    private EsUtil esUtil;

    @Autowired
    private FabWearingImgInfoService fabWearingImgInfoService;

    @Autowired
    private FabInfoService fabInfoService;

    @Autowired
    private ProductEnvFabricInfoMapper productEnvFabricInfoMapper;

    @Value("${match.pic.prefix}")
    private String matchPicPrefix;

    @Value("${particular.pic.prefix}")
    private String particularPicPrefix;

    @Value("${old.pattern.pic.prefix}")
    private String oldPatternPicPrefix;

    @Value("${old.pattern.pic.prefix.im}")
    private String oldPatternPicPrefixIm;

    @Value("${new.pattern.pic.prefix}")
    private String newPatternPicPrefix;

    @Value("${big.category.limit}")
    private String bigCategory;

    @Value("${product.spu.fab.match.img.index}")
    private String productSpuFabMatchImgIndex;

    @Value("${product.category.order.str}")
    private String productCategoryOrderStr;

    @Value("${product.wash.info.index}")
    private String productWashInfoIndex;

    @Autowired
    private FabAutoNameInfoMapper fabAutoNameInfoMapper;

    @Autowired
    private KeyCombinationsMapper keyCombinationsMapper;

    @Autowired
    private MatchingSuggestionMapper matchingSuggestionMapper;

    @Autowired
    private MProductListMapper mProductListMapper;

    @Autowired
    private IProductDetailsImgApi productDetailsImgApi;

    @Autowired
    private IProductService productService;


    @Autowired
    private ProductFabricInfoMapper productFabricInfoMapper;

    @Autowired
    private ProductInspirationInfoMapper productInspirationInfoMapper;

    @Override
    public List<FabricWeightResp> getFabricWeightByFabricNo(List<String> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            throw new RuntimeException("入参的面料信息不能为空");
        }

        List<FabricWeightResp> resps = new ArrayList<>();
        requestData.stream().distinct().forEach(v -> {
            FabricWeightResp weightResp = new FabricWeightResp();
            weightResp.setFabricNo(v);
            // 没有批量接口 只有单个接口
            FabricInfoRespEntity batchFabricInfo = fabricInfoService.getBatchFabricInfo(v);
            if (batchFabricInfo == null || Objects.equals(batchFabricInfo.getScmlkz(), new BigDecimal("0.0"))
                || batchFabricInfo.getScmlkz() == null) {
                weightResp.setFabricWeight("");
            } else {
                weightResp.setFabricWeight(batchFabricInfo.getScmlkz() + fabricWeightUnit);
            }
            resps.add(weightResp);
        });
        return resps;
    }

    @Override
    public ProductFabResp exportProductFabInfo(QueryGoodsFabReq requestData, Page page) {
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(requestData.getYears()) || com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(requestData.getBandIds())
                || com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(requestData.getBrandIds())) {
            throw new RuntimeException("年份||波段||品牌 必传");
        }

        // 判断当前年份、波段、品牌是否只有一个
        if (requestData.getYears().size() > 1 || requestData.getBandIds().size() > 1 ||
                (!(requestData.getBrandIds().size() == 2 && requestData.getBrandIds().get(0) == 4L
                        && requestData.getBrandIds().get(1) == 4L  && StringUtils.isNotBlank(requestData.getBrandName())))
                        && (!requestData.getBrandIds().contains(4L) && requestData.getBrandIds().size() > 1)) {
            throw new RuntimeException("生成产品册时，品牌、波段、年份只允许选一个");
        }

        QueryGoodsFabEsReq req = new QueryGoodsFabEsReq();
        BeanUtils.copyProperties(requestData, req);
        Page page1 = new Page();
        page1.setPageNo(1);
        page1.setPageSize(100);
        List<ProductSpuFabEsResp> fabResps = spuFabInfoByParams(req, page1, "");
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(fabResps)) {
            return null;
        }
        // 对fab信息进行排序
        Map<String, String> map = JSONObject.parseObject(productCategoryOrderStr, Map.class);
        List<ProductSpuFabEsSortResp> rets = new ArrayList<>();
        List<ProductSpuFabEsSortResp> finalRets = rets;
        fabResps.forEach(v -> {
            String sort = map.get(v.getSmall_class());
            if (StringUtils.isBlank(sort)) {
                return;
            }
            ProductSpuFabEsSortResp resp = new ProductSpuFabEsSortResp();
            BeanUtils.copyProperties(v, resp);
            resp.setSort(Integer.valueOf(sort));
            finalRets.add(resp);
        });
        rets = rets.stream().sorted(Comparator.comparing(ProductSpuFabEsSortResp::getSort)).collect(Collectors.toList());


        // 获取所有款号
        List<String> productIds = fabResps.stream().map(ProductSpuFabEsResp::getId).collect(Collectors.toList());
        List<String> names = fabResps.stream().map(ProductSpuFabEsResp::getName).collect(Collectors.toList());

        // 获取商品的搭配图、单品图、一览图
        Map<String, BuildFabImgInfo> fabImgInfoMap = null;
        if (Integer.valueOf(requestData.getYears().get(0)) >= 2024 && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(names)) {
            fabImgInfoMap = buildFabImgInfo(names, false, true);
        }

        // 按大类分组
        Map<String, List<ProductSpuFabEsSortResp>> bigCategoriesMap = rets.stream().collect(Collectors.groupingBy(v -> v.getBig_class()));

        // 获取fabInfo信息
        List<FabInfo> fabInfos = fabInfoService.selectFabInfoByIdsAndNames(productIds);
        HashMap<String, String> fabInfoMap = fabInfos.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getProductId(), v.getFab()), HashMap::putAll);

        return ProductFabResp.build(productIds, bigCategoriesMap, categoryNames, rets,
                matchPicPrefix, oldPatternPicPrefix, newPatternPicPrefix, map, fabImgInfoMap, names, fabInfoMap);
    }


    public Map<String, BuildFabImgInfo> buildFabImgInfo(List<String> names, Boolean isMatchAndDetail, Boolean isPngs) {
        CommonRequest<FindProductDetailImgReq> findRequest = new CommonRequest<>();
        FindProductDetailImgReq imgReq = new FindProductDetailImgReq();
        imgReq.setProductCodes(names);
        findRequest.setRequestData(imgReq);
        ResponseResult<List<FindProductDetailImgResp>> detailImg = productDetailsImgApi.findProductDetailImg(findRequest);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(detailImg.getData())) {
            return null;
        }
        Map<String, BuildFabImgInfo> map = new HashMap<>();
        List<BatchGetViewUrlReq.LianxiangData> batchViewReq = new ArrayList<>();
        // 组装数据
        List<String> skcs = new ArrayList<>();
        detailImg.getData().forEach(v -> {
            BuildFabImgInfo imgInfo = new BuildFabImgInfo();
            Map<String, String> skcImgMap = new HashMap<>();
            Map<String, String> mattedImgMap = new HashMap<>();
            List<ProductSpuFabEsResp.MatchPicExpend> matchPicExpends = new ArrayList<>();
            List<ProductSpuFabEsResp.DetailPic> detailPics = new ArrayList<>();
            // 单品图
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(v.getMainImgs())) {
                v.getMainImgs().forEach(x -> {
                    skcImgMap.put(x.getColorNo(), x.getNeid());
                    BatchGetViewUrlReq.LianxiangData data = new BatchGetViewUrlReq.LianxiangData();
                    data.setNeid(x.getNeid());
                    data.setNsid(x.getNsid());
                    batchViewReq.add(data);
                });
                imgInfo.setSkcImgMap(skcImgMap);
            }

            // 一栏图
            if (isPngs && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(v.getPngs())) {
                v.getPngs().forEach(x -> {
                    mattedImgMap.put(x.getColorNo(), x.getNeid());
                    BatchGetViewUrlReq.LianxiangData data = new BatchGetViewUrlReq.LianxiangData();
                    data.setNeid(x.getNeid());
                    data.setNsid(x.getNsid());
                    batchViewReq.add(data);
                });
                imgInfo.setMattedImgMap(mattedImgMap);
            }


            // 搭配图
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(v.getCollocationImgs())) {
                AtomicInteger i = new AtomicInteger();
                v.getCollocationImgs().forEach(x -> {
                    if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(x.getImgDatas())) {
                        return;
                    }
                    ProductSpuFabEsResp.MatchPicExpend pic = new ProductSpuFabEsResp.MatchPicExpend();
                    pic.setId(i.get());
                    pic.setUrl(x.getImgDatas().get(0).getNeid());
                    if (isMatchAndDetail) {
                        List<ProductSpuFabEsResp.MatchPicExpendData> list = new ArrayList<>();
                        x.getImgDatas().forEach(u -> {
                            ProductSpuFabEsResp.MatchPicExpendData picData = new ProductSpuFabEsResp.MatchPicExpendData();
                            picData.setSkc(u.getProductCode() + u.getColorNo());
                            skcs.add(u.getProductCode() + u.getColorNo());
                            list.add(picData);
                        });
                        pic.setMatchPicRecSpus(list);
                    }
                    matchPicExpends.add(pic);

                    BatchGetViewUrlReq.LianxiangData data = new BatchGetViewUrlReq.LianxiangData();
                    data.setNeid(x.getImgDatas().get(0).getNeid());
                    data.setNsid(x.getImgDatas().get(0).getNsid());
                    batchViewReq.add(data);
                    i.getAndIncrement();
                });
                imgInfo.setMatchPicExpends(matchPicExpends);
            }

            // 细节图
            if (isMatchAndDetail && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(v.getDetailsImgs())) {
                v.getDetailsImgs().forEach(x -> {
                    ProductSpuFabEsResp.DetailPic pic = new ProductSpuFabEsResp.DetailPic();
                    pic.setUrl(x.getNeid());
                    detailPics.add(pic);

                    BatchGetViewUrlReq.LianxiangData data = new BatchGetViewUrlReq.LianxiangData();
                    data.setNeid(x.getNeid());
                    data.setNsid(x.getNsid());
                    batchViewReq.add(data);
                });
                imgInfo.setDetailPics(detailPics);
            }

            map.put(v.getProductCode(), imgInfo);
        });

        if (MapUtils.isEmpty(map)) {
            return null;
        }

        // 查询预览图
        CommonRequest<LianxiangBatchGetViewReq> commonRequest = new CommonRequest<>();
        LianxiangBatchGetViewReq viewReq = new LianxiangBatchGetViewReq();
        viewReq.setFile_array(batchViewReq);
        commonRequest.setRequestData(viewReq);
        ResponseResult<List<LianxiangBatchGetViewResp>> result = productDetailsImgApi.batchGetViewUrl(commonRequest);
        Map<String, String> viewMap = new HashMap<>();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(result.getData())) {
            result.getData().forEach(v -> {
                if (StringUtils.isBlank(v.getPreviewUrl())) {
                    return;
                }
                viewMap.put(v.getNeid(), v.getPreviewUrl());
            });
        }

        if (MapUtils.isEmpty(viewMap)) {
            return null;
        }

        Map<String, BuildFabImgInfo> retMap = new HashMap<>();
        map.entrySet().forEach(v -> {
            BuildFabImgInfo buildFabImgInfo = new BuildFabImgInfo();
            // skc图
            if (MapUtils.isNotEmpty(v.getValue().getSkcImgMap())) {
                Map<String, String> skcImgMap = new HashMap<>();
                v.getValue().getSkcImgMap().entrySet().forEach(x -> {
                    if (StringUtils.isBlank(viewMap.get(x.getValue()))) {
                        return;
                    }
                    skcImgMap.put(x.getKey(), viewMap.get(x.getValue()));
                });
                buildFabImgInfo.setSkcImgMap(skcImgMap);
            }

            // 一览图
            if (isPngs && MapUtils.isNotEmpty(v.getValue().getMattedImgMap())) {
                Map<String, String> pngs = new HashMap<>();
                v.getValue().getMattedImgMap().entrySet().forEach(x -> {
                    if (StringUtils.isBlank(viewMap.get(x.getValue()))) {
                        return;
                    }
                    pngs.put(x.getKey(), viewMap.get(x.getValue()));
                });
                buildFabImgInfo.setMattedImgMap(pngs);
            }

            // 搭配图
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(v.getValue().getMatchPicExpends())) {
                List<ProductSpuFabEsResp.MatchPicExpend> matchPicExpends = new ArrayList<>();
                v.getValue().getMatchPicExpends().forEach(x ->{
                    if (StringUtils.isBlank(viewMap.get(x.getUrl()))) {
                        return;
                    }
                    ProductSpuFabEsResp.MatchPicExpend data = new ProductSpuFabEsResp.MatchPicExpend();
                    data.setId(x.getId());
                    data.setUrl(viewMap.get(x.getUrl()));

                    List<ProductSpuFabEsResp.MatchPicExpendData> subList = new ArrayList<>();
                    if (isMatchAndDetail && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(x.getMatchPicRecSpus()) && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(skcs)) {
                        List<ProductSkcResp> byIds = productService.findGoodsSkcDetailByIds(skcs);
                        HashMap<String, ProductSkcResp> hashMap = byIds.stream()
                                .collect(HashMap::new, (kk, vv) -> kk.put(vv.getId(), vv), HashMap::putAll);
                        x.getMatchPicRecSpus().forEach(u -> {
                            if (MapUtils.isNotEmpty(hashMap) && hashMap.get(u.getSkc()) != null) {
                                ProductSpuFabEsResp.MatchPicExpendData subData = new ProductSpuFabEsResp.MatchPicExpendData();
                                subData.setProductId(Objects.toString(hashMap.get(u.getSkc()).getProduct_id()));
                                subData.setValue(hashMap.get(u.getSkc()).getValue());
                                subData.setSkc(u.getSkc());
                                subList.add(subData);
                            }
                        });
                        data.setMatchPicRecSpus(subList);
                    }

                    matchPicExpends.add(data);
                });
                buildFabImgInfo.setMatchPicExpends(matchPicExpends);
            }

            // 细节图
            if (isMatchAndDetail && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(v.getValue().getDetailPics())) {
                List<ProductSpuFabEsResp.DetailPic> detailPics = new ArrayList<>();
                v.getValue().getDetailPics().forEach(x -> {
                    if (StringUtils.isBlank(viewMap.get(x.getUrl()))) {
                        return;
                    }
                    ProductSpuFabEsResp.DetailPic pic = new ProductSpuFabEsResp.DetailPic();
                    pic.setUrl(viewMap.get(x.getUrl()));
                    detailPics.add(pic);
                });
                buildFabImgInfo.setDetailPics(detailPics);
            }

            retMap.put(v.getKey(), buildFabImgInfo);
        });
        return retMap;
    }

    @Override
    public List<HomeWashInfoEsResp> getHomeWashInfo(String requestData) {
        // 判断当前款号是否合规
        if (StringUtils.isBlank(requestData) || StringUtils.length(requestData) < 9) {
            throw new ProductException("当前款号不能为空也不能小于九位");
        }

        // 替换第六位去查询
        List<String> names = new ArrayList<>();
        StringBuilder sb = new StringBuilder(requestData);
        sb.setCharAt(5, 'A');
        names.add(sb.toString());
        sb.setCharAt(5, 'B');
        names.add(sb.toString());
        sb.setCharAt(5, 'C');
        names.add(sb.toString());

        List<HomeWashInfoEsResp> resps = searchWashInfoInEs(names);
        return resps;
    }

    @Autowired
    private IProductDetailService productDetailService;

    @Autowired
    private LianXiangYunFilezService lianXiangYunFilezService;

    @Autowired
    private ColorCodeNameMapper colorCodeNameMapper;

    @Override
    public List<WpPicInfoResp> getWpOriginalPis(String requestData) {
        if (StringUtils.isBlank(requestData)) {
            throw new RuntimeException("商品款号不能为空");
        }
        FindProductDetailImgReq imgReq = new FindProductDetailImgReq();
        imgReq.setProductCodes(Lists.newArrayList(requestData));
        List<FindProductDetailImgResp> detailImg = productDetailService.findProductDetailImg(imgReq);
        if (CollectionUtils.isEmpty(detailImg)) {
            return Collections.emptyList();
        }
        List<FindProductDetailImgResp.ImgData> mainImgs = detailImg.get(0).getMainImgs();
        if (CollectionUtils.isEmpty(mainImgs)) {
            return Collections.emptyList();
        }

        List<WpPicInfoResp> resps = new ArrayList<>();
        List<BatchGetViewUrlReq.LianxiangData> reqs = new ArrayList<>();
        List<String> colorCodes = new ArrayList<>();
        mainImgs.forEach(v -> {
            WpPicInfoResp infoResp = new WpPicInfoResp();
            infoResp.setColorCode(v.getColorNo());
            infoResp.setNeid(v.getNeid());
            infoResp.setNsid(v.getNsid());
            resps.add(infoResp);

            BatchGetViewUrlReq.LianxiangData data = new BatchGetViewUrlReq.LianxiangData();
            data.setNeid(v.getNeid());
            data.setNsid(v.getNsid());
            reqs.add(data);

            colorCodes.add(v.getColorNo());
        });

        LianxiangBatchGetViewReq imgReq1 = new LianxiangBatchGetViewReq();
        imgReq1.setFile_array(reqs);
        List<LianxiangBatchGetViewResp> resps1 = lianXiangYunFilezService.batchGetViewUrl(imgReq1);
        if (CollectionUtils.isEmpty(resps1)) {
            resps1 = new ArrayList<>();
        }

        List<ColorCodeName> colorCodeNames = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(colorCodes)) {
            colorCodeNames = colorCodeNameMapper.selectByColorCode(colorCodes);
        }

        HashMap<Object, Object> colorNameMap = colorCodeNames.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getColorCode(), v.getColorName()), HashMap::putAll);
        HashMap<Object, Object> urlMap = resps1.stream().collect(HashMap::new, (k, v) -> k.put(v.getNeid(), v.getPreviewUrl()), HashMap::putAll);
        resps.forEach(v -> {
            v.setPicUrl((String) urlMap.get(v.getNeid()));
            v.setColorName((String) colorNameMap.get(v.getColorCode()));
        });

        return resps;
    }

    @Autowired
    private FabInfoMapper fabInfoMapper;

    @Override
    public String importProductFabInfo(String url, String keys, String type) {
        List<FabInfo> fabInfos = new ArrayList<>();
        List<ProductBatchFabEntity.AllFabInfo> errorDataList = new ArrayList<>();
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {
            @Override
            protected void saveData() {
                ProductBatchFabEntity entity = dealBatchImportProductFbInfo(this.cachedDataList, type);
                //保存数据

                if (CollectionUtils.isNotEmpty(entity.getBaseDataList())) {
                    fabInfos.addAll(entity.getBaseDataList());
                }

                if(CollectionUtils.isNotEmpty(entity.getErrorDataList())){
                    errorDataList.addAll(entity.getErrorDataList());
                }
            }
        });

        // 导入对应数据 删除数据原来的数据
        if (CollectionUtils.isNotEmpty(fabInfos)) {
            Lists.partition(fabInfos, 1000)
                    .stream()
                    .forEach(v -> {
                        // 删除信息
                        fabInfoMapper.batchUpdateByIds(v);

                        // 添加信息
                        fabInfoMapper.batchInsert(v);
                    });
        }

        String param = "";
        if (CollectionUtils.isNotEmpty(errorDataList)) {
            String fileName = System.currentTimeMillis() + ".xlsx";
            EasyExcelUtil.write(fileName, ProductBatchFabEntity.ErrorData.class, new IWriteDataExcel<ProductBatchFabEntity.ErrorData>() {
                @Override
                public List<ProductBatchFabEntity.ErrorData> getData() {
                    return errorDataList.stream().map(item -> {
                        ProductBatchFabEntity.ErrorData data = new ProductBatchFabEntity.ErrorData();
                        data.setName(item.getName());
                        data.setFab(item.getFab());
                        data.setReason(item.getReason());
                        return data;
                    }).collect(Collectors.toList());
                }
            });
            File file = new File(fileName);
            param = qiniuUtil.upload(file.getPath(), "异常商品Fab信息"+System.currentTimeMillis() + ".xlsx");
            file.delete();
            log.info("redis的key{}", param);
            RedisTemplateUtil.setex(redisPoolUtil, keys, param,60);

        }
        log.info("是否存在脏数据：{}, params:{}", errorDataList.size(), param);
        return param;
    }

    @Override
    public Boolean judgeImportProductFabInfo(String requestData) {
        List<ProductBatchFabEntity.AllFabInfo> allFabInfos = new ArrayList<>();
        String filePath = FileParseUtil.downLoadExcel(requestData);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {
            @Override
            protected void saveData() {
                ProductBatchFabEntity entity = sumBatchImportProductFbInfo(this.cachedDataList);
                //保存数据

                if (CollectionUtils.isNotEmpty(entity.getAllDataList())) {
                    allFabInfos.addAll(entity.getAllDataList());
                }

            }
        });

        return allFabInfos.size() > 2000 ? false : true;
    }

    @Override
    public ProductSpuFabBaseInfoEsResp queryFabDetailBaseInfo(String requestData) {
        ProductSpuFabBaseInfoEsResp resp = baseFabInfoByProductId(requestData);
        if (resp == null) {
            return null;
        }
        // 查询商品的上市时间
        MProductList list = mProductListMapper.selectByProductId(requestData);
        if (list != null && list.getDateList() != null) {
            resp.setMarketTime(Objects.toString(list.getDateList()));
        }

        if (StringUtils.isNotBlank(resp.getYear()) && Integer.valueOf(resp.getYear()) >= 2024) {
            Map<String, BuildFabImgInfo> buildFabImgInfo = buildFabImgInfo(Lists.newArrayList(resp.getName()), false, false);
            if (MapUtils.isNotEmpty(buildFabImgInfo) && buildFabImgInfo.get(resp.getName()) != null) {
                BuildFabImgInfo fabImgInfo = buildFabImgInfo.get(resp.getName());
                resp.getColorNames().forEach(v -> {
                    if (StringUtils.isNotBlank(v.getImg()) && StringUtils.contains(v.getImg(), "weimob")) {
                        return;
                    }
                    if (MapUtils.isNotEmpty(fabImgInfo.getSkcImgMap()) &&
                            StringUtils.isNotBlank(fabImgInfo.getSkcImgMap().get(v.getColor_code()))) {
                        v.setImg(fabImgInfo.getSkcImgMap().get(v.getColor_code()));
                    }
                });
            }
        }
        return resp;
    }

    @Override
    public ProductSellingPointEsResp queryFabDetailSellingPoint(String requestData) {
        ProductSellingPointEsResp resp = sellingPointInfoByProductId(requestData);
        if (resp == null) {
            return null;
        }

        // 处理日本的数据
        if (StringUtils.isNotBlank(resp.getBom_name())) {
            resp.setBom_name(resp.getBom_name().replace("日本", "进口"));
        }
        if (StringUtils.isNotBlank(resp.getBom_area())) {
            resp.setBom_area(resp.getBom_area().replace("日本", "进口"));
        }
        if (StringUtils.isNotBlank(resp.getSc_environmental())) {
            resp.setSc_environmental(resp.getSc_environmental().replace("日本", "进口"));
        }

        // 获取自动品名
        List<FabAutoNameInfo> fabAutoNameInfos = fabAutoNameInfoMapper.selectByNameList(Lists.newArrayList(resp.getName()));
        if (CollectionUtils.isNotEmpty(fabAutoNameInfos)) {
            FabAutoNameInfo fabAutoNameInfo = fabAutoNameInfos.stream()
                    .filter(v -> Objects.equals(v.getAutoType(), 1)).findFirst().orElse(null);
            if (fabAutoNameInfo != null) {
                resp.setWscAutoName(fabAutoNameInfo.getAutoNameCalc());
            }
            FabAutoNameInfo fab = fabAutoNameInfos.stream()
                    .filter(v -> Objects.equals(v.getAutoType(), 2)).findFirst().orElse(null);
            if (fab != null) {
                resp.setDsPartAutoName(fab.getAutoNameCalc());
                resp.setDsWholeAutoName(fab.getAutoNameWhole());
            }

        }

        // 对图案灵感说明
        if (CollectionUtils.isNotEmpty(resp.getPattern_info_list())) {
            resp.setInspirations(resp.getPattern_info_list().stream().map(ProductSpuFabEsResp.PatternInfo::getInspiration)
                    .filter(v -> StringUtils.isNotBlank(v)).distinct().collect(Collectors.joining(" ")));
            resp.setArtist(resp.getPattern_info_list().stream().map(ProductSpuFabEsResp.PatternInfo::getArtist)
                    .filter(v -> StringUtils.isNotBlank(v)).distinct().collect(Collectors.joining(" ")));
            resp.setMeaning_desc(resp.getPattern_info_list().stream().map(ProductSpuFabEsResp.PatternInfo::getMeaning_desc)
                    .filter(v -> StringUtils.isNotBlank(v)).distinct().collect(Collectors.joining(" ")));
        }

        // 查询灵感说明和面料故事
        List<ProductInspirationInfo> infos = productInspirationInfoMapper.selectByProductId(resp.getId());
        if (CollectionUtils.isNotEmpty(infos)) {
            resp.setInspirations(infos.stream().map(ProductInspirationInfo::getInspiration)
                    .filter(v -> StringUtils.isNotBlank(v)).distinct().collect(Collectors.joining(";")));
        }
        List<ProductFabricInfo> fabricInfos = productFabricInfoMapper.selectByProductId(resp.getId());
        if (CollectionUtils.isNotEmpty(fabricInfos)) {
            resp.setFabric_story(fabricInfos.stream().map(ProductFabricInfo::getFabricInfo)
                    .filter(v -> StringUtils.isNotBlank(v)).distinct().collect(Collectors.joining(";")));
        }


        FabInfo fabInfo = fabInfoService.selectFabInfoByProductIdAndName(resp.getId(), resp.getName());
        if (fabInfo != null && StringUtils.isNotBlank(fabInfo.getFab())) {
            resp.setFab(fabInfo.getFab());
        }
        return resp;
    }

    @Override
    public ProductMatchEsResp queryFabDetailMatchInfo(String requestData) {
        ProductMatchEsResp resp = sellingMatchInfoByProductId(requestData);
        if (resp == null) {
            return null;
        }

        if (StringUtils.isNotBlank(resp.getYear()) && Integer.valueOf(resp.getYear()) >= 2024) {
            Map<String, BuildFabImgInfo> buildFabImgInfo = buildFabImgInfo(Lists.newArrayList(resp.getName()), true, false);
            if (MapUtils.isNotEmpty(buildFabImgInfo) && buildFabImgInfo.get(resp.getName()) != null) {
                BuildFabImgInfo fabImgInfo = buildFabImgInfo.get(resp.getName());

                if (CollectionUtils.isNotEmpty(fabImgInfo.getMatchPicExpends())) {
                    resp.setMatchPicExpends(fabImgInfo.getMatchPicExpends());
                } else {
                    Map<String, List<ProductSpuFabMatchImgEsResp>> map = null;
                    List<String> matchUrls = CollectionUtils.isEmpty(resp.getMatchPics()) ? Collections.emptyList() :
                            resp.getMatchPics().stream().map(ProductSpuFabEsResp.MatchPic::getUrl).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(matchUrls)) {
                        List<ProductSpuFabMatchImgEsResp> esResp = spuFabMatchImgInfoByProductIdAndUrl(requestData, matchUrls);
                        if (CollectionUtils.isNotEmpty(esResp)) {
                            map = new HashMap<>();
                            map = esResp.stream().filter(v -> StringUtils.isNotBlank(v.getDesign_name()) && (v.getProduct_id() != null))
                                    .collect(Collectors.groupingBy(ProductSpuFabMatchImgEsResp::getUrl));
                        }
                    }

                    resp.buildMatchPicPrefix(matchPicPrefix, map);
                }

                if (CollectionUtils.isNotEmpty(fabImgInfo.getDetailPics())) {
                    resp.setDetailPics(fabImgInfo.getDetailPics());
                } else {
                    resp.buildDetailPicPrefix(particularPicPrefix);
                }

            } else {
                buildMatchOldImg(requestData, resp);
            }
        } else {
            buildMatchOldImg(requestData, resp);
        }

        // 处理穿着方式
        List<String> czfsList = new ArrayList<>();
        czfsList.add(resp.getCzfs());
        czfsList.add(resp.getSense());
        czfsList.add(resp.getLingxing_czfs());
        czfsList.add(resp.getJian_czfs());
        resp.setCzfs(CollectionUtils.isEmpty(czfsList) ? "" :
                czfsList.stream().filter(v -> StringUtils.isNotBlank(v)).distinct().collect(Collectors.joining(";")));

        FabWearingImgInfo info = fabWearingImgInfoService.selectWearingByProductIdAndName(resp.getId(), resp.getName());
        if (info != null) {
            resp.setWearing_imgs(info.getImages());
        }

        // 处理搭配建议 先从存的 无则取索引替换的 无替换则取索引
        FabMatchingSugInfo sugInfo = fabInfoService.selectFabMatchingByProductIdAndName(resp.getId(), resp.getName());
        if (sugInfo != null) {
            resp.setMatch_advice(sugInfo.getMatchingSug());
        } else {
            if (StringUtils.isBlank(resp.getMatch_advice())) {

                return resp;
            }
            MatchingSuggestion matchingSuggestion = matchingSuggestionMapper.selectByMatch(resp.getMatch_advice());
            if (matchingSuggestion != null) {
                resp.setMatch_advice(matchingSuggestion.getNewSuggestion());
            }
        }


        return resp;
    }

    @Override
    public ProductWashInfoEsResp queryFabDetailWashInfo(String requestData) {
        ProductWashInfoEsResp resp = matchInfoByProductId(requestData);
        if (resp == null) {
            return null;
        }

        resp.buildWashInfo();
        return resp;
    }

    @Override
    public ProductSpuFabEsResp queryFabBomInfo(String requestData) {
        List<ProductSpuFabEsResp> fabInfoByNames = getFabInfoByNames(Lists.newArrayList(requestData));
        if (CollectionUtils.isEmpty(fabInfoByNames)) {
            return null;
        }
        return fabInfoByNames.get(0);
    }


    @Override
    public String importProductFabricStory(String url, String keys, String type) {
        Pair<List<ImportCommonInsertData>, List<ProductBatchCommonEntity.AllInfo>> pair = commonImport(url, type);
        // 导入对应数据 删除数据原来的数据
        if (CollectionUtils.isNotEmpty(pair.getKey())) {
            List<ProductFabricInfo> fabricInfos = new ArrayList<>();
            pair.getKey().forEach(v -> {
                ProductFabricInfo info = new ProductFabricInfo();
                BeanUtils.copyProperties(v, info);
                info.setFabricInfo(v.getInfo());
                fabricInfos.add(info);
            });
            Lists.partition(fabricInfos, 1000)
                    .stream()
                    .forEach(v -> {
                        // 删除信息
                        productFabricInfoMapper.batchUpdateByIds(v);

                        // 添加信息
                        productFabricInfoMapper.batchInsert(v);
                    });
        }
        return commonResult(pair.getValue(), keys);
    }

    @Override
    public String importProductPattern(String url, String keys, String type) {

        Pair<List<ImportCommonInsertData>, List<ProductBatchCommonEntity.AllInfo>> pair = commonImport(url, type);
        // 导入对应数据 删除数据原来的数据
        if (CollectionUtils.isNotEmpty(pair.getKey())) {
            List<ProductInspirationInfo> patterns = new ArrayList<>();
            pair.getKey().forEach(v -> {
                ProductInspirationInfo info = new ProductInspirationInfo();
                BeanUtils.copyProperties(v, info);
                info.setInspiration(v.getInfo());
                patterns.add(info);
            });
            Lists.partition(patterns, 1000)
                    .stream()
                    .forEach(v -> {
                        // 删除信息
                        productInspirationInfoMapper.batchUpdateByIds(v);

                        // 添加信息
                        productInspirationInfoMapper.batchInsert(v);
                    });
        }
        return commonResult(pair.getValue(), keys);
    }

    @Override
    public List<ProductSellingPointEsResp> batchQueryFabDetailSellingPoints(List<String> requestData) {
        List<ProductSellingPointEsResp> resp = batchSellingPointInfoByProductIds(requestData);
        return resp;
    }

    @Override
    public List<ProductWashInfoEsResp> batchQueryFabDetailWashInfos(List<String> requestData) {
        return batchQueryFabDetailWashInfosInEs(requestData);
    }


    private List<ProductWashInfoEsResp> batchQueryFabDetailWashInfosInEs(List<String> productIds) {
        SearchRequest request = new SearchRequest();
        request.indices(fabWashInfoIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("id", productIds));


        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(productIds.size());
        sourceBuilder.sort("id", SortOrder.DESC);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("批量查询商品洗涤信息 {}", request.source().toString());
        List<ProductWashInfoEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductWashInfoEsResp entity = ProductWashInfoEsResp.fromJson(hits[i].getSourceAsString(), ProductWashInfoEsResp.class);
                entity.setId(hits[i].getId());
                entity.buildWashInfo();
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("批量查询商品洗涤信息异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }


    public Pair<List<ImportCommonInsertData>, List<ProductBatchCommonEntity.AllInfo>> commonImport(String url, String type) {
        List<ImportCommonInsertData> commonInfos = new ArrayList<>();
        List<ProductBatchCommonEntity.AllInfo> errorDataList = new ArrayList<>();
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {
            @Override
            protected void saveData() {
                ProductBatchCommonEntity entity = dealBatchImportProductCommonInfo(this.cachedDataList, type);
                //保存数据

                if (CollectionUtils.isNotEmpty(entity.getBaseDataList())) {
                    commonInfos.addAll(entity.getBaseDataList());
                }

                if(CollectionUtils.isNotEmpty(entity.getErrorDataList())){
                    errorDataList.addAll(entity.getErrorDataList());
                }
            }
        });
        return Pair.of(commonInfos, errorDataList);
    }

    private String commonResult(List<ProductBatchCommonEntity.AllInfo> errorDataList, String keys) {
        String param = "";
        if (CollectionUtils.isNotEmpty(errorDataList)) {
            String fileName = System.currentTimeMillis() + ".xlsx";
            EasyExcelUtil.write(fileName, ProductBatchCommonEntity.ErrorData.class, new IWriteDataExcel<ProductBatchCommonEntity.ErrorData>() {
                @Override
                public List<ProductBatchCommonEntity.ErrorData> getData() {
                    return errorDataList.stream().map(item -> {
                        ProductBatchCommonEntity.ErrorData data = new ProductBatchCommonEntity.ErrorData();
                        data.setSkcNo(item.getSkcNo());
                        data.setInfo(item.getInfo());
                        data.setReason(item.getReason());
                        return data;
                    }).collect(Collectors.toList());
                }
            });
            File file = new File(fileName);
            param = qiniuUtil.upload(file.getPath(), "异常商品导入商品信息"+System.currentTimeMillis() + ".xlsx");
            file.delete();
            log.info("redis的key{}", param);
            RedisTemplateUtil.setex(redisPoolUtil, keys, param,60);

        }
        log.info("是否存在脏数据：{}, params:{}", errorDataList.size(), param);
        return param;
    }


    @Autowired
    private BBoxMProductMapper bBoxMProductMapper;

    @Value("${product.patent.id}")
    private String patentTagId;

    private ProductBatchCommonEntity dealBatchImportProductCommonInfo(List<Map<Integer, String>> cachedDataList, String operate) {
        ProductBatchCommonEntity entity = new ProductBatchCommonEntity();
        List<ProductBatchCommonEntity.AllInfo> oldInfos = new ArrayList<>();
        for (Map<Integer, String> batchPatentMap : cachedDataList) {
            if (StringUtils.isBlank(batchPatentMap.get(0)) && StringUtils.isBlank(batchPatentMap.get(1))) {
                continue;
            }
            ProductBatchCommonEntity.AllInfo allInfo = new ProductBatchCommonEntity.AllInfo();
            allInfo.setSkcNo(batchPatentMap.get(0));
            allInfo.setInfo(batchPatentMap.get(1));
            if (StringUtils.isBlank(batchPatentMap.get(1))) {
                allInfo.setReason("填写信息不能全为空");
                allInfo.setType(1);
            } else if (StringUtils.isBlank(batchPatentMap.get(0))) {
                allInfo.setReason("款色号不能为空");
                allInfo.setType(1);
            } else if (batchPatentMap.get(0).getBytes().length < 10 || batchPatentMap.get(0).getBytes().length < 12) {
                allInfo.setReason("款色号信息有误");
                allInfo.setType(1);
            }else {
                allInfo.setName(StringUtils.substring(batchPatentMap.get(0), 0, 9));
                allInfo.setSkcCode(StringUtils.substring(batchPatentMap.get(0), 9, 12));
                allInfo.setType(0);
            }
            oldInfos.add(allInfo);
        }

        // 判断当前款号是否存在
        List<String> nameList = oldInfos.stream()
                .map(ProductBatchCommonEntity.AllInfo::getName)
                .filter(v -> StringUtils.isNotBlank(v))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nameList)) {
            oldInfos.forEach(v -> {
                v.setReason("款号不存在");
                v.setType(1);
            });

            return entity;
        }


        List<MProduct> boxMProducts = mProductMapper.selectByNames(nameList);
        Map<String, List<MProduct>> map = boxMProducts.stream().collect(Collectors.groupingBy(MProduct::getMProductOrig));
        Map<Long, List<String>> skcMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(boxMProducts)) {
            List<Long> ids = boxMProducts.stream().map(MProduct::getId).collect(Collectors.toList());
            List<BoxMProduct> products = bBoxMProductMapper.selectByIds(ids);
            if (CollectionUtils.isNotEmpty(products)) {
                skcMap = products.stream().collect(Collectors.groupingBy(BoxMProduct::getId, Collectors.mapping(BoxMProduct::getColorno, Collectors.toList())));
            }
        }

        List<ProductBatchCommonEntity.AllInfo> newAllInfos = new ArrayList<>();
        Map<Long, List<String>> finalSkcMap = skcMap;
        oldInfos.forEach(v -> {
            if (Objects.equals(v.getType(), 1)) {
                newAllInfos.add(v);
                return;
            }

            List<MProduct> boxMProduct = map.get(v.getName());
            if (boxMProduct == null) {
                ProductBatchCommonEntity.AllInfo data = new ProductBatchCommonEntity.AllInfo();
                BeanUtils.copyProperties(v, data);
                data.setReason("款号有误");
                data.setType(1);
                newAllInfos.add(data);
            } else {
                List<Long> ids = boxMProduct.stream().map(MProduct::getId).collect(Collectors.toList());
                // 如果是home的则将所有商品id都放入 任意一个匹配上skc则匹配上
                List<ProductBatchCommonEntity.AllInfo> subPatentInfos = new ArrayList<>();
                ids.forEach(x -> {
                    ProductBatchCommonEntity.AllInfo data = new ProductBatchCommonEntity.AllInfo();
                    BeanUtils.copyProperties(v, data);
                    data.setProductId(x);
                    subPatentInfos.add(data);
                });

                // 如果home多个商品id都未找到色号才添加
                AtomicReference<Boolean> isExist = new AtomicReference<>(false);
                subPatentInfos.forEach(x -> {
                    if (MapUtils.isNotEmpty(finalSkcMap) && finalSkcMap.containsKey(x.getProductId())) {
                        List<String> list = finalSkcMap.get(x.getProductId());
                        if (list.contains(x.getSkcCode()) && !isExist.get()) {
                            isExist.set(true);
                        }
                    }
                });

                if (isExist.get()) {
                    subPatentInfos.forEach(x -> {
                        x.setType(0);
                        newAllInfos.add(x);
                    });
                } else {
                    subPatentInfos.forEach(x -> {
                        x.setReason("色号有误");
                        x.setType(1);
                        newAllInfos.add(x);
                    });
                }

            }
        });



        List<ProductBatchCommonEntity.AllInfo> errorList = newAllInfos.stream()
                .filter(v -> Objects.equals(v.getType(), 1)).collect(Collectors.toList());
        entity.setErrorDataList(errorList);
        entity.setAllDataList(newAllInfos);
        List<ImportCommonInsertData> baseDataList = new ArrayList<>();
        newAllInfos.stream()
                .filter(v -> Objects.equals(v.getType(), 0))
                .distinct()
                .forEach(v -> {
                    ImportCommonInsertData info = new ImportCommonInsertData();
                    info.setSkcNo(v.getSkcNo());
                    info.setInfo(v.getInfo());
                    info.setName(v.getName());
                    info.setProductId(Objects.toString(v.getProductId()));
                    info.setCreateTime(new Date());
                    info.setUpdateTime(new Date());
                    info.setId(IdLeaf.getId(patentTagId));
                    info.setOperators(operate);
                    baseDataList.add(info);
                });
        entity.setBaseDataList(baseDataList);
        return entity;
    }



    private ProductBatchFabEntity sumBatchImportProductFbInfo(List<Map<Integer, String>> cachedDataList) {
        ProductBatchFabEntity entity = new ProductBatchFabEntity();
        List<ProductBatchFabEntity.AllFabInfo> infos = new ArrayList<>();
        for (Map<Integer, String> batchFabMap : cachedDataList) {
            ProductBatchFabEntity.AllFabInfo allFabInfo = new ProductBatchFabEntity.AllFabInfo();
            allFabInfo.setName(batchFabMap.get(0));
            allFabInfo.setFab(batchFabMap.get(1));
            infos.add(allFabInfo);
        }
        entity.setAllDataList(infos);
        return entity;
    }

    @Autowired
    private MProductMapper mProductMapper;

    @Value("${fab.wear.tag.id}")
    private String fabWearTagId;

    private ProductBatchFabEntity dealBatchImportProductFbInfo(List<Map<Integer, String>> cachedDataList, String operate) {
        ProductBatchFabEntity entity = new ProductBatchFabEntity();
        List<ProductBatchFabEntity.AllFabInfo> allFabInfos = new ArrayList<>();
        for (Map<Integer, String> batchFabMap : cachedDataList) {
            if (allFabInfos.stream().map(ProductBatchFabEntity.AllFabInfo::getName).collect(Collectors.toList())
                    .contains(batchFabMap.get(0))) {
                continue;
            }
            ProductBatchFabEntity.AllFabInfo allFabInfo = new ProductBatchFabEntity.AllFabInfo();
            allFabInfo.setName(batchFabMap.get(0));
            allFabInfo.setFab(batchFabMap.get(1));
            if (StringUtils.isBlank(batchFabMap.get(1))) {
                allFabInfo.setReason("FAB不能为空");
                allFabInfo.setType(1);
            } else if (StringUtils.isBlank(batchFabMap.get(0))) {
                allFabInfo.setReason("款号不能为空");
                allFabInfo.setType(1);
            } else {
                allFabInfo.setType(0);
            }
            allFabInfos.add(allFabInfo);
        }

        // 判断当前款号是否存在
        List<String> nameList = allFabInfos.stream()
                .map(ProductBatchFabEntity.AllFabInfo::getName)
                .filter(v -> StringUtils.isNotBlank(v))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nameList)) {
            allFabInfos.forEach(v -> {
                v.setReason("款号有误");
                v.setType(1);
            });
            entity.setErrorDataList(allFabInfos);
            entity.setAllDataList(allFabInfos);
            return entity;
        }

        List<MProduct> boxMProducts = mProductMapper.selectByNames(nameList);
        Map<String, List<MProduct>> map = boxMProducts.stream().collect(Collectors.groupingBy(MProduct::getMProductOrig));
        allFabInfos.forEach(v -> {
            List<MProduct> boxMProduct = map.get(v.getName());
            if (boxMProduct == null) {
                v.setReason("款号有误");
                v.setType(1);
            }
        });

        List<ProductBatchFabEntity.AllFabInfo> errorList = allFabInfos.stream()
                .filter(v -> Objects.equals(v.getType(), 1)).collect(Collectors.toList());
        entity.setErrorDataList(errorList);
        entity.setAllDataList(allFabInfos);
        List<FabInfo> baseDataList = new ArrayList<>();
        allFabInfos.stream()
                .filter(v -> Objects.equals(v.getType(), 0))
                .forEach(v -> {
                    List<MProduct> mProducts = map.get(v.getName());
                    if (CollectionUtils.isEmpty(mProducts)) {
                        return;
                    }
                    mProducts.forEach(mPo -> {
                        FabInfo fabInfo = new FabInfo();
                        fabInfo.setFab(v.getFab());
                        fabInfo.setName(v.getName());
                        fabInfo.setProductId(Objects.toString(mPo.getId()));
                        fabInfo.setCreateTime(new Date());
                        fabInfo.setUpdateTime(new Date());
                        fabInfo.setId(IdLeaf.getId(fabWearTagId));
                        fabInfo.setOperators(operate);
                        baseDataList.add(fabInfo);
                    });

                });
        entity.setBaseDataList(baseDataList);
        return entity;
    }

    private List<HomeWashInfoEsResp> searchWashInfoInEs(List<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return new ArrayList<>();
        }
        SearchRequest request = new SearchRequest();
        request.indices(productWashInfoIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        if (CollectionUtils.isNotEmpty(names)) {
            queryBuilder.must(QueryBuilders.termsQuery("_id", names));
        }


        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(1000);
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1) * page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        sourceBuilder.sort("_id", SortOrder.DESC);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品home_wash_info_ES {}", request.source().toString());
        List<HomeWashInfoEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                HomeWashInfoEsResp entity = HomeWashInfoEsResp.fromJson(hits[i].getSourceAsString(), HomeWashInfoEsResp.class);
                entity.setId(hits[i].getId());
                entity.buildWashInfo();
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品home_wash_info_ES异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }

    @Override
    public ProductSpuFabResp queryFabInfo(ProductFabInfoReq requestData) {
        ProductSpuFabEsResp resp = spuFabInfoByProductId(requestData);
        if (resp == null) {
            return null;
        }
        ProductSpuFabResp ret = new ProductSpuFabResp();
        BeanUtils.copyProperties(resp, ret);

        // 查询商品的上市时间
        MProductList list = mProductListMapper.selectByProductId(requestData.getProductId());
        if (list != null && list.getDateList() != null) {
            ret.setMarketTime(Objects.toString(list.getDateList()));
        }

        // 查询对应搭配图的款号
        if (Objects.equals(ret.getFile1_name(), "JNBY同款") || Objects.equals(ret.getFile1_name(), "大小童类似款")
                || Objects.equals(ret.getFile1_name(), "婴量小童同款")
                || Objects.equals(ret.getFile1_name(), "大小童同款")
                || Objects.equals(ret.getFile1_name(), "婴量小童类似款")) {
            ret.setFile1_name(ret.getFile1_name());
        } else {
            ret.setFile1_name("");
        }

        // 处理日本的数据
        if (StringUtils.isNotBlank(ret.getBom_name())) {
            ret.setBom_name(ret.getBom_name().replace("日本", "进口"));
        }
        if (StringUtils.isNotBlank(ret.getBom_area())) {
            ret.setBom_area(ret.getBom_area().replace("日本", "进口"));
        }
        if (StringUtils.isNotBlank(ret.getSc_environmental())) {
            ret.setSc_environmental(ret.getSc_environmental().replace("日本", "进口"));
        }


        if (StringUtils.isNotBlank(ret.getYear()) && Integer.valueOf(ret.getYear()) >= 2024) {
            Map<String, BuildFabImgInfo> buildFabImgInfo = buildFabImgInfo(Lists.newArrayList(ret.getName()), true, false);
            if (MapUtils.isNotEmpty(buildFabImgInfo) && buildFabImgInfo.get(ret.getName()) != null) {
                BuildFabImgInfo fabImgInfo = buildFabImgInfo.get(ret.getName());
                ret.getColorNames().forEach(v -> {
                    if (StringUtils.isNotBlank(v.getImg()) && StringUtils.contains(v.getImg(), "weimob")) {
                        return;
                    }
                    if (MapUtils.isNotEmpty(fabImgInfo.getSkcImgMap()) &&
                            StringUtils.isNotBlank(fabImgInfo.getSkcImgMap().get(v.getColor_code()))) {
                        v.setImg(fabImgInfo.getSkcImgMap().get(v.getColor_code()));
                    }
                });
                if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(fabImgInfo.getMatchPicExpends())) {
                    ret.setMatchPicExpends(fabImgInfo.getMatchPicExpends());
                } else {
                    Map<String, List<ProductSpuFabMatchImgEsResp>> map = null;
                    List<String> matchUrls = com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(ret.getMatchPics()) ? Collections.emptyList() :
                            ret.getMatchPics().stream().map(ProductSpuFabEsResp.MatchPic::getUrl).collect(Collectors.toList());
                    if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(matchUrls)) {
                        List<ProductSpuFabMatchImgEsResp> esResp = spuFabMatchImgInfoByProductIdAndUrl(requestData.getProductId(), matchUrls);
                        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(esResp)) {
                            map = new HashMap<>();
                            map = esResp.stream().filter(v -> StringUtils.isNotBlank(v.getDesign_name()) && (v.getProduct_id() != null))
                                    .collect(Collectors.groupingBy(ProductSpuFabMatchImgEsResp::getUrl));
                        }
                    }

                    ret.buildMatchPicPrefix(matchPicPrefix, map);
                }

                if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(fabImgInfo.getDetailPics())) {
                    ret.setDetailPics(fabImgInfo.getDetailPics());
                } else {
                    ret.buildDetailPicPrefix(particularPicPrefix);
                }

            } else {
                buildOldImg(requestData, ret);
            }
        } else {
            buildOldImg(requestData, ret);
        }

        ret.buildPatternPicPrefix(oldPatternPicPrefix, newPatternPicPrefix);
        FabWearingImgInfo info = fabWearingImgInfoService.selectWearingByProductIdAndName(resp.getId(), resp.getName());
        if (info != null) {
            ret.setWearing_imgs(info.getImages());
        }
        FabInfo fabInfo = fabInfoService.selectFabInfoByProductIdAndName(resp.getId(), resp.getName());
        if (fabInfo != null && StringUtils.isNotBlank(fabInfo.getFab())) {
            ret.setFab(fabInfo.getFab());
        }

        // 获取自动品名
        List<FabAutoNameInfo> fabAutoNameInfos = fabAutoNameInfoMapper.selectByNameList(Lists.newArrayList(ret.getName()));
        if (CollectionUtils.isNotEmpty(fabAutoNameInfos)) {
            FabAutoNameInfo fabAutoNameInfo = fabAutoNameInfos.stream()
                    .filter(v -> Objects.equals(v.getAutoType(), 1)).findFirst().orElse(null);
            if (fabAutoNameInfo != null) {
                ret.setWscAutoName(fabAutoNameInfo.getAutoNameCalc());
            }
            FabAutoNameInfo fab = fabAutoNameInfos.stream()
                    .filter(v -> Objects.equals(v.getAutoType(), 2)).findFirst().orElse(null);
            if (fab != null) {
                ret.setDsPartAutoName(fab.getAutoNameCalc());
                ret.setDsWholeAutoName(fab.getAutoNameWhole());
            }

        }

        // 对图案灵感说明
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(ret.getPattern_info_list())) {
            ret.setInspirations(ret.getPattern_info_list().stream().map(ProductSpuFabEsResp.PatternInfo::getInspiration)
                    .filter(v -> StringUtils.isNotBlank(v)).distinct().collect(Collectors.joining(" ")));
        }

        // 处理搭配建议 先从存的 无则取索引替换的 无替换则取索引
        FabMatchingSugInfo sugInfo = fabInfoService.selectFabMatchingByProductIdAndName(resp.getId(), resp.getName());
        if (sugInfo != null) {
            ret.setMatch_advice(sugInfo.getMatchingSug());
        } else {
            if (StringUtils.isBlank(resp.getMatch_advice())) {
                return ret;
            }
            MatchingSuggestion matchingSuggestion = matchingSuggestionMapper.selectByMatch(resp.getMatch_advice());
            if (matchingSuggestion != null) {
                ret.setMatch_advice(matchingSuggestion.getNewSuggestion());
            }
        }

        return ret;
    }

    private void buildOldImg(ProductFabInfoReq requestData, ProductSpuFabResp ret) {
        // 单品图不用变
        Map<String, List<ProductSpuFabMatchImgEsResp>> map = null;
        List<String> matchUrls = CollectionUtils.isEmpty(ret.getMatchPics()) ? Collections.emptyList() :
                ret.getMatchPics().stream().map(ProductSpuFabEsResp.MatchPic::getUrl).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(matchUrls)) {
            List<ProductSpuFabMatchImgEsResp> esResp = spuFabMatchImgInfoByProductIdAndUrl(requestData.getProductId(), matchUrls);
            if (CollectionUtils.isNotEmpty(esResp)) {
                map = new HashMap<>();
                map = esResp.stream().filter(v -> StringUtils.isNotBlank(v.getDesign_name()) && (v.getProduct_id() != null))
                        .collect(Collectors.groupingBy(ProductSpuFabMatchImgEsResp::getUrl));
            }
        }

        ret.buildMatchPicPrefix(matchPicPrefix, map);
        ret.buildDetailPicPrefix(particularPicPrefix);
    }


    private void buildMatchOldImg(String productId, ProductMatchEsResp ret) {
        // 单品图不用变
        Map<String, List<ProductSpuFabMatchImgEsResp>> map = null;
        List<String> matchUrls = CollectionUtils.isEmpty(ret.getMatchPics()) ? Collections.emptyList() :
                ret.getMatchPics().stream().map(ProductSpuFabEsResp.MatchPic::getUrl).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(matchUrls)) {
            List<ProductSpuFabMatchImgEsResp> esResp = spuFabMatchImgInfoByProductIdAndUrl(productId, matchUrls);
            if (CollectionUtils.isNotEmpty(esResp)) {
                map = new HashMap<>();
                map = esResp.stream().filter(v -> StringUtils.isNotBlank(v.getDesign_name()) && (v.getProduct_id() != null))
                        .collect(Collectors.groupingBy(ProductSpuFabMatchImgEsResp::getUrl));
            }
        }

        ret.buildMatchPicPrefix(matchPicPrefix, map);
        ret.buildDetailPicPrefix(particularPicPrefix);
    }


    private List<ProductSpuFabMatchImgEsResp> spuFabMatchImgInfoByProductIdAndUrl(String productId, List<String> matchUrls) {
        SearchRequest request = new SearchRequest();
        request.indices(productSpuFabMatchImgIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(matchUrls)) {
            queryBuilder.must(QueryBuilders.termsQuery("url", matchUrls));
        }
        if (StringUtils.isNotBlank(productId)) {
            queryBuilder.mustNot(QueryBuilders.termQuery("product_id", Long.valueOf(productId)));
        }

        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(1000);
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1) * page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        sourceBuilder.sort("product_id", SortOrder.DESC);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品SKU_FAB-MATCH_IMG_ES {}", request.source().toString());
        List<ProductSpuFabMatchImgEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductSpuFabMatchImgEsResp entity = ProductSpuFabMatchImgEsResp.fromJson(hits[i].getSourceAsString(), ProductSpuFabMatchImgEsResp.class);
                entity.setId(hits[i].getId());
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品SKU_FAB-MATCH_IMG_ES异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }

    @Override
    public List<FabProductFabricInfoRep> queryFabricInfo(BatchQueryFabInfoReq requestData) {
        QueryGoodsFabEsReq esReq = new QueryGoodsFabEsReq();
        esReq.setProductIds(requestData.getProductIds());

        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(1000);
        List<ProductSpuFabEsResp> resps = spuFabInfoByParams(esReq, page, "");
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(resps)) {
            return Collections.emptyList();
        }

        // 业务要求只取存在推广资料的推荐面料
        return FabProductFabricInfoRep.build(resps.stream()
                .filter(v -> StringUtils.isNotBlank(v.getSc_popularize()))
                .collect(Collectors.toList()));
    }

    @Override
    public List<FabProductPatternInfoRep> queryBatchPatternInfo(BatchQueryFabInfoReq requestData) {
        QueryGoodsFabEsReq esReq = new QueryGoodsFabEsReq();
        esReq.setProductIds(requestData.getProductIds());

        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(1000);
        List<ProductSpuFabEsResp> resps = spuFabInfoByParams(esReq, page, "");
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(resps)) {
            return Collections.emptyList();
        }

        return FabProductPatternInfoRep.build(resps, oldPatternPicPrefix, newPatternPicPrefix, oldPatternPicPrefixIm);
    }

    @Override
    public List<String> queryMerchandiseSeason() {

        SearchRequest request = new SearchRequest();
        request.indices(PRODUCT_SKC_INDEX);

        TermsAggregationBuilder aggregation = AggregationBuilders.terms("distinct_values")
                .field("good_season.keyword")
                .size(100); // 指定返回的不同值的数量上限

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .aggregation(aggregation)
                .size(0);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        sourceBuilder.fetchSource(new String[]{"good_season"}, new String[]{});
        sourceBuilder.query(queryBuilder);
        sourceBuilder.sort("year", SortOrder.DESC);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品SKU_FAB-ES {}", request.source().toString());
        List<String> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            Aggregation distinct_values = response.getAggregations().getAsMap().get("distinct_values");
            for (Terms.Bucket sb : ((ParsedStringTerms) distinct_values).getBuckets()) {
                log.info("==============:{}", sb.getKeyAsString() + ":" + sb.getDocCount());
                entities.add(sb.getKeyAsString());
            }

        } catch (IOException e) {
            log.error("查询商品SPU-FAB-ES异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }

        String[] array = entities.toArray(new String[entities.size()]);
        StringSortUtil.sort(array);
        return Arrays.asList(array);
    }


    private ProductSpuFabEsResp spuFabInfoByProductId(ProductFabInfoReq requestData) {
        GetRequest request = new GetRequest(productSpuFabIndex, requestData.getProductId());
        ProductSpuFabEsResp resp = null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                resp = ProductSpuFabEsResp.fromJson(response.getSourceAsString(), ProductSpuFabEsResp.class);
                resp.setId(requestData.getProductId());
                if (Objects.nonNull(resp.getLabels()) && resp.getLabels().size() > 0){
                    resp.setLabels(resp.getLabels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                    resp.setLabel_levels(resp.getLabel_levels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                }

                resp.buildColorNameList();
                resp.buildMatchPic();
                resp.buildDetailPic();
                resp.buildColorUrlList();
                resp.buildPatternUrlList();
                resp.buildPatternList();
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品fab异常e = {}", e.getMessage());
        }
        return resp;
    }

    private List<ProductSpuFabEsResp> spuFabInfoByParams(QueryGoodsFabEsReq context, Page page, String img) {
        SearchRequest request = new SearchRequest();
        request.indices(productSpuFabIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        buildQuery(queryBuilder, context);
        if (context.getIsZhuiDan() != null && context.getIsZhuiDan() == 0) {
            queryBuilder.must(QueryBuilders.termQuery("is_zhuidan", 0));
        }
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(context.getSeasonGoods())) {
            queryBuilder.must(QueryBuilders.termsQuery("good_season", context.getSeasonGoods()));
        }
        if (StringUtils.isBlank(img)) {
            List<Long> bigCategoryList = Arrays.stream(bigCategory.split(",")).map(v -> Long.parseLong(v)).collect(Collectors.toList());
            queryBuilder.must(QueryBuilders.termsQuery("big_class_id", bigCategoryList));
        }

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1) * page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        sourceBuilder.sort("quantify_qty", SortOrder.DESC);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品SKU_FAB-ES {}", request.source().toString());
        List<ProductSpuFabEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductSpuFabEsResp entity = ProductSpuFabEsResp.fromJson(hits[i].getSourceAsString(), ProductSpuFabEsResp.class);
                entity.setId(hits[i].getId());
                entity.buildColorNameList();
                entity.buildMatchPic();
                entity.buildDetailPic();
                entity.buildColorUrlList();
                entity.buildPatternUrlList();
                entity.buildPatternList();
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品SPU-FAB-ES异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }

    private void buildQuery(BoolQueryBuilder queryBuilder, QueryGoodsFabEsReq context){
        if (StringUtils.isNotBlank(context.getName())){
            queryBuilder.must(QueryBuilders.termQuery("name",context.getName()));
        }

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(context.getNames())) {
            queryBuilder.must(QueryBuilders.termsQuery("name",context.getNames()));
        }

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(context.getSeasonIds())){
            queryBuilder.must(QueryBuilders.termsQuery("a_small_season_id", context.getSeasonIds()));
        }

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(context.getMustLabels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("labels", context.getMustLabels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(0)));
            queryBuilder.must(termsSetQueryBuilder);
        }

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(context.getSmallCategoryIds())){
            queryBuilder.must(QueryBuilders.termsQuery("small_class_id", context.getSmallCategoryIds()));
        }
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(context.getYears())){
            queryBuilder.must(QueryBuilders.termsQuery("a_year", context.getYears()));
        }
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(context.getBrandIds())){
            queryBuilder.must(QueryBuilders.termsQuery("c_arcbrand_id", context.getBrandIds()));
        }
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(context.getBandIds())){
            List<Long> other = new ArrayList<>();
            if(context.getBandIds().contains(2000L)){
                List<AttrResp> attrEntities = JSONObject.parseArray(bandIdsConfig, AttrResp.class);
                AttrResp attrResp = attrEntities.get(attrEntities.size() - 1);
                List<AttrResp> children = attrResp.getChildren();
                other = children.stream().map(r -> r.getId()).collect(Collectors.toList());
            }
            other.addAll(context.getBandIds());
            queryBuilder.must(QueryBuilders.termsQuery("band_id", other));
        }


        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(context.getProductIds())) {
            queryBuilder.must(QueryBuilders.termsQuery("_id", context.getProductIds()));
        }

        if (StringUtils.isNotBlank(context.getBomName())) {
            queryBuilder.must(QueryBuilders.termsQuery("bom_name.keyword", context.getBomName()));
        }

        if (StringUtils.isNotBlank(context.getScPopularize())) {
            queryBuilder.must(QueryBuilders.termsQuery("sc_popularize.keyword", context.getScPopularize()));
        }

        if (StringUtils.isNotBlank(context.getSjcf())) {
            queryBuilder.must(QueryBuilders.termsQuery("sjcf.keyword", context.getSjcf()));
        }

        if (StringUtils.isNotBlank(context.getPatternName())) {
            queryBuilder.must(QueryBuilders.termsQuery("pattern_info_list.pattern_name", context.getPatternName()));
        }

        if (StringUtils.isNotBlank(context.getInspiration())) {
            queryBuilder.filter(QueryBuilders.matchPhrasePrefixQuery("pattern_info_list.inspiration", context.getInspiration()).maxExpansions(50));
        }

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(context.getPatternUrl())) {
            queryBuilder.must(QueryBuilders.termsQuery("pattern_info_list.pattern_url", context.getPatternUrl()));
        }

        if (StringUtils.isNotBlank(context.getPatternType())) {
            queryBuilder.must(QueryBuilders.termsQuery("pattern_info_list.pattern_type", context.getPatternType()));
        }

        if (StringUtils.isNotBlank(context.getBrandName())) {
            queryBuilder.must(QueryBuilders.termQuery("brand", context.getBrandName()));
        }

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(context.getBrandNames())) {
            queryBuilder.must(QueryBuilders.termsQuery("brand", context.getBrandNames()));
        }
    }




    @Override
    public void exportProductFabNewArrivalInfo(String keys, QueryGoodsFabEsReq req) {
        // es处理
        if (CollectionUtils.isEmpty(req.getYears()) || CollectionUtils.isEmpty(req.getBandIds())
                || CollectionUtils.isEmpty(req.getBrandIds())) {
            throw new RuntimeException("年份||波段||品牌 必传");
        }
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(10000);

        // 对入参品牌进行处理进行处理 婴童和童装需要传品牌名称
        if (CollectionUtils.isNotEmpty(req.getBrandIds())) {
            List<BrandBrandEnum> brandInfoByCode = BrandBrandEnum.getBrandInfoByCode(req.getBrandIds());
            if (CollectionUtils.isNotEmpty(brandInfoByCode)) {
                List<String> names = new ArrayList<>();
                brandInfoByCode.stream().forEach(v -> {
                    names.addAll(v.getDesc());
                });
                req.setBrandNames(names);
                req.setBrandIds(brandInfoByCode.stream().map(v -> v.getInsideCode()).collect(Collectors.toList()));
            }
        }

        List<ProductSpcNewArrivalFabEsResp> esResps = skcNewArrivalFabInfoByParams(req, page);
        if(CollectionUtils.isEmpty(esResps)) {
            return;
        }

        // 获取当前数据的自动品名
        List<String> nameList = esResps.stream().map(ProductSpcNewArrivalFabEsResp::getStyle_id).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nameList)) {
            return;
        }
        List<FabAutoNameInfo> fabAutoNameInfos = fabAutoNameInfoMapper.selectByNameList(nameList);
        HashMap dsAutoMap = null;
        HashMap wscAutoMap = null;
        if (CollectionUtils.isNotEmpty(fabAutoNameInfos)) {
            wscAutoMap = fabAutoNameInfos.stream().filter(v -> Objects.equals(v.getAutoType(), AutoNameEnum.WSC.getCode()))
                    .collect(HashMap::new, (k, v) -> k.put(v.getName(), v), HashMap::putAll);
            dsAutoMap = fabAutoNameInfos.stream().filter(v -> Objects.equals(v.getAutoType(), AutoNameEnum.DS.getCode()))
                    .collect(HashMap::new, (k, v) -> k.put(v.getName(), v), HashMap::putAll);
        }

        // 查询这些信息的fab
        List<FabInfo> fabInfos = fabInfoService
                .selectFabInfoByIdsAndNames(esResps.stream().map(ProductSpcNewArrivalFabEsResp::getProduct_id)
                        .collect(Collectors.toList()));
        HashMap map = null;
        if (CollectionUtils.isNotEmpty(fabInfos)) {
            map = fabInfos.stream().collect(HashMap::new, (k, v) -> k.put(v.getProductId(), v.getFab()), HashMap::putAll);
        }

        // 查询商品的重点套装
        List<KeyInfoParamsEntity> keyCombinations = keyCombinationsMapper.selectKeyCombinationsByNames(nameList);
        Map<String, List<KeyInfoParamsEntity>> suitMap = null;
        List<KeyInfoSpuEntity> relationsSpus = new ArrayList<>();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(keyCombinations)) {
            suitMap = keyCombinations.stream().collect(Collectors.groupingBy(KeyInfoParamsEntity::getName));
            relationsSpus = keyCombinationsMapper.selectByBrandAndGoodSeasonAndSuit(keyCombinations, "");
        }

        // 查询所有搭配建议
        QueryWrapper<MatchingSuggestion> objectWrapper = new QueryWrapper<>();
        objectWrapper.eq("IS_DELETED", 0);
        List<MatchingSuggestion> suggestions = matchingSuggestionMapper.selectList(objectWrapper);
        HashMap<String, String> sugMap = suggestions.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getOldSuggestion(), v.getNewSuggestion()), HashMap::putAll);

        // 查询对应克重信息 small_class_id T恤卫衣 296 2428
        List<String> bomCodes = esResps.stream()
                .filter(v -> Objects.equals(v.getSmall_class_id(), 296L) || Objects.equals(v.getSmall_class_id(), 2428L))
                .filter(v -> Objects.nonNull(v.getBom_code()))
                .map(ProductSpcNewArrivalFabEsResp::getBom_code).distinct().collect(Collectors.toList());
        HashMap<String, String> fabricMap = null;
        if (CollectionUtils.isNotEmpty(bomCodes)) {
            List<FabricWeightResp> fabricWeightByFabricNo = getFabricWeightByFabricNo(bomCodes);
            fabricMap = fabricWeightByFabricNo.stream().collect(HashMap::new, (k, v) -> k.put(v.getFabricNo(), v.getFabricWeight()), HashMap::putAll);
        }

        // 查询这些信息的环保
        List<ProductEnvFabricInfo> productEnvFabricInfos = productEnvFabricInfoMapper
                .selectEnviFabricByIds(esResps.stream().map(ProductSpcNewArrivalFabEsResp::getProduct_id)
                        .collect(Collectors.toList()));
        HashMap productEnvFabricMap = null;
        if (CollectionUtils.isNotEmpty(productEnvFabricInfos)) {
            productEnvFabricMap = productEnvFabricInfos.stream()
                    .collect(HashMap::new, (k, v) -> k.put(v.getName() + v.getSkcNo(), v.getEnvFabricInfo()), HashMap::putAll);
        }

        // 处理对应skc数据
        Map<String, List<ProductSpcNewArrivalFabEsResp>> skcMap = esResps.stream()
                .collect(Collectors.groupingBy(ProductSpcNewArrivalFabEsResp::getProduct_id));

        String fileName = System.currentTimeMillis() +  ".xlsx";
        HashMap finalMap = map;
        HashMap finalDsAutoMap = dsAutoMap;
        HashMap finalWscAutoMap = wscAutoMap;
        Map<String, List<KeyInfoParamsEntity>> finalSuitMap = suitMap;
        List<KeyInfoSpuEntity> finalRelationsSpus = relationsSpus;
        HashMap<String, String> finalFabricMap = fabricMap;
        HashMap finalProductEnvFabricMap = productEnvFabricMap;
        EasyExcelUtil.write(fileName, FabProductNewArrivalEntity.class, new IWriteDataExcel<FabProductNewArrivalEntity>() {
            public List<FabProductNewArrivalEntity> getData() {
                List<FabProductNewArrivalEntity> list = ListUtils.newArrayList();

                esResps.forEach(v -> {
                    FabProductNewArrivalEntity entity = new FabProductNewArrivalEntity();
                    BeanUtils.copyProperties(v, entity);
                    if (MapUtils.isNotEmpty(skcMap)) {
                        List<ProductSpcNewArrivalFabEsResp> skcList = skcMap.get(v.getProduct_id());
                        if (CollectionUtils.isNotEmpty(skcList)) {
                            List<String> list1 = skcList.stream()
                                    .map(ProductSpcNewArrivalFabEsResp::getBom_code).distinct().collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(list1) && list1.size() > 1) {
                                entity.setIsSkcDiff("SKC有差异");
                            }
                        }
                    }

                    if (v.getSell_price() != null) {
                        entity.setSell_price(Objects.toString(v.getSell_price().intValue()));
                    }
                    if (v.getAdd_amt() != null) {
                        entity.setAdd_amt(Objects.toString(v.getAdd_amt()));
                    }
                    if (StringUtils.isNotBlank(v.getSale_date())) {
                        entity.setSale_date(DateUtil.parseDate((DateUtil.formatToDate(v.getSale_date(), DateUtil.DATEFORMATE_YYYY_MM_DD)), DateUtil.DATEFORMATE_YYYY_MM_DD));
                    }
                    if (StringUtils.isNotBlank(v.getIn_date())) {
                        entity.setIn_date(DateUtil.parseDate((DateUtil.formatToDate(v.getIn_date(), DateUtil.DATEFORMATE_YYYY_MM_DD)), DateUtil.DATEFORMATE_YYYY_MM_DD));
                    }
                    // 业务要求 羽绒服和棉衣时不给厚薄
                    if (Objects.equals(v.getCcchr5(), "羽绒服") || Objects.equals(v.getCcchr5(), "棉衣")) {
                        entity.setSpe_thick(v.getThick());
                    } else {
                        entity.setSpe_thick("");
                    }
                    if (Objects.equals(v.getXiu(), "/")) {
                        entity.setXiu("");
                    }
                    if (Objects.equals(v.getXiuxing(), "/")) {
                        entity.setXiuxing("");
                    }
                    if (Objects.equals(v.getLingxing(), "/")) {
                        entity.setLingxing("");
                    }

                    // T恤 卫衣 需要克重
                    if (Objects.equals(v.getSmall_class_id(), 296L) || Objects.equals(v.getSmall_class_id(), 2428L)) {
                        entity.setFabricWeight(MapUtils.isEmpty(finalFabricMap) ? "" : finalFabricMap.get(v.getBom_code()));
                    }

                    // 增加fab信息
                    if (finalMap != null) {
                        if (StringUtils.isNotBlank(v.getProduct_id())) {
                            entity.setFab(StringUtils.isNotBlank((String)finalMap.get(v.getProduct_id())) ?
                                    (String)finalMap.get(v.getProduct_id()) :
                                    v.getFab());
                        }

                    }

                    // 自动品名
                    if (MapUtils.isNotEmpty(finalDsAutoMap)) {
                        FabAutoNameInfo fabAutoNameInfo = (FabAutoNameInfo) finalDsAutoMap.get(v.getStyle_id());
                        entity.setDsPartAutoName(fabAutoNameInfo == null ? "" : fabAutoNameInfo.getAutoNameCalc());
                        entity.setDsWholeAutoName(fabAutoNameInfo == null ? "" : fabAutoNameInfo.getAutoNameWhole());
                    }
                    if (MapUtils.isNotEmpty(finalWscAutoMap)) {
                        FabAutoNameInfo fabAutoNameInfo = (FabAutoNameInfo) finalWscAutoMap.get(v.getStyle_id());
                        entity.setWscAutoName(fabAutoNameInfo == null ? "" : fabAutoNameInfo.getAutoNameCalc());
                    }

                    // 重点套装
                    List<String> spus = new ArrayList<>();
                    if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(v.getSkcList())) {
                        v.getSkcList().stream().filter(k -> !k.contains("-"))
                                .forEach(k -> spus.add(StringUtils.substring(k, 0, 9)));
                    }
                    entity.setSkc_list(spus.stream().collect(Collectors.joining(";")));
                    if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(keyCombinations)) {
                        if (MapUtils.isNotEmpty(finalSuitMap) && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(finalRelationsSpus)) {
                            List<KeyInfoParamsEntity> entities = finalSuitMap.get(v.getStyle_id());
                            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(entities)) {
                                List<String> names = new ArrayList<>();
                                entities.forEach(x -> {
                                    List<String> filterNames = finalRelationsSpus.stream().filter(u -> Objects.equals(u.getGoodSeason(), x.getGoodSeason())
                                                    && Objects.equals(u.getBrandCode(), x.getBrandCode())
                                                    && Objects.equals(u.getSuitNum(), x.getSuitNum())).collect(Collectors.toList())
                                            .stream().map(KeyInfoSpuEntity::getName).collect(Collectors.toList());
                                    names.addAll(filterNames);
                                });
                                if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(names)) {
                                    entity.setKey_combinations(names.stream().distinct().collect(Collectors.joining(",")));
                                }
                            }
                        }
                    }

                    // 搭配建议
                    if (MapUtils.isNotEmpty(sugMap) && StringUtils.isNotBlank(v.getMatch_advice())) {
                        entity.setMatch_advice(StringUtils.isNotBlank(sugMap.get(v.getMatch_advice())) ?
                                sugMap.get(v.getMatch_advice()) : v.getMatch_advice());
                    }

                    // 环保说明
                    if (StringUtils.isNotBlank(v.getSc_origin_area())) {
                        if (MapUtils.isEmpty(finalProductEnvFabricMap) || !finalProductEnvFabricMap.containsKey(v.getA_skc_code())) {
                            entity.setSc_origin_area("");
                        } else {
                            entity.setSc_origin_area((String) finalProductEnvFabricMap.get(v.getA_skc_code()));
                        }
                    }


                    if (StringUtils.isBlank(v.getWash_name())) {
                        list.add(entity);
                        return;
                    } else {
                        List<String> washNames = Arrays.stream(v.getWash_name().split(",")).distinct().collect(Collectors.toList());
                        int size = washNames.size();
                        dealWashNames(washNames, size, entity);
                        list.add(entity);
                    }
                });

                return list;
            }
        });
        File file = new File(fileName);
        String exportUrl = qiniuUtil.upload(file.getPath(), "fab信息" + fileName);
        file.delete();
        RedisTemplateUtil.setex(redisPoolUtil, keys, exportUrl,60);
        log.info("====================导出fab的exportUrl:{}, key:{}", JSONObject.toJSONString(exportUrl), keys);
    }

    @Override
    public ProductSpuFabImgInfoResp queryFabImgInfo(ProductSpuFabImgInfoReq requestData) {
        QueryGoodsFabEsReq req = new QueryGoodsFabEsReq();
        // 将数据进行切割
        String name = StringUtils.substring(requestData.getSkc(), 0, requestData.getSkc().length() - 3);
        req.setName(name);
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(100);
        List<ProductSpuFabEsResp> resps = spuFabInfoByParams(req, page, requestData.getSkc());
        Map<String, BuildFabImgInfo> buildFabImgInfo = null;
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(resps) && Integer.valueOf(resps.get(0).getYear()) >= 2024) {
            buildFabImgInfo = buildFabImgInfo(Lists.newArrayList(resps.get(0).getName()), false, false);
        }

        return ProductSpuFabImgInfoResp.build(resps, requestData.getSkc(), buildFabImgInfo);
    }

    @Override
    public List<ProductSpuFabResp> batchQueryFabInfo(List<String> productIds, String key) {
        QueryGoodsFabEsReq req = new QueryGoodsFabEsReq();
        req.setProductIds(productIds);
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(100);
        List<ProductSpuFabEsResp> resps = spuFabInfoByParams(req, page, key);

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(resps)) {
            return Collections.emptyList();
        }

        List<FabInfo> fabInfos = fabInfoService.selectFabInfoByIdsAndNames(productIds);
        HashMap<String, String> map = null;
        List<ProductSpuFabResp> rets = new ArrayList<>();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(fabInfos)) {
            map = fabInfos.stream().collect(HashMap::new, (k, v) -> k.put(v.getProductId(), v.getFab()), HashMap::putAll);
        }
        HashMap<String, String> finalMap = map;
        resps.forEach(v -> {
            ProductSpuFabResp resp = new ProductSpuFabResp();
            BeanUtils.copyProperties(v, resp);
            if (finalMap != null && StringUtils.isNotBlank(finalMap.get(v.getId()))) {
                resp.setFab(finalMap.get(v.getId()));
            }
            rets.add(resp);
        });

        return rets;
    }

    @Override
    public List<FabSkcByParamInfoResp> querySkcByFabricInfo(FabProductFabricInfoRep requestData) {
        // 根据面料查询数据
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(100);

        QueryGoodsFabEsReq req = new QueryGoodsFabEsReq();
        if (StringUtils.isNotBlank(requestData.getSjcf())) {
            req.setSjcf(requestData.getSjcf());
        }
        if (StringUtils.isNotBlank(requestData.getProductPromotionMaterials())) {
            req.setScPopularize(requestData.getProductPromotionMaterials());
        }
        if (StringUtils.isNotBlank(requestData.getBomName())) {
            req.setBomName(requestData.getBomName());
        }
        req.setBrandIds(requestData.getBrandIds());
        req.setBandIds(requestData.getBandIds());
        req.setBrandName(requestData.getBrandName());

        List<ProductSpuFabEsResp> resps = spuFabInfoByParams(req, page, "");
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(resps)) {
            return Collections.emptyList();
        }

        // 处理数据
        getFabSkcByParamInfoResps(resps);
        return FabSkcByParamInfoResp.build(resps);
    }

    private void getFabSkcByParamInfoResps(List<ProductSpuFabEsResp> resps) {
        if (Integer.valueOf(resps.get(0).getYear()) >= 2024) {
            Map<String, BuildFabImgInfo> map = buildFabImgInfo(resps.stream().map(ProductSpuFabEsResp::getName).collect(Collectors.toList()), false, false);
            if (MapUtils.isEmpty(map)) {
                return;
            }
            resps.forEach(v -> {
                if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(v.getColorNames())) {
                    return;
                }

                if (map.get(v.getName()) == null || MapUtils.isEmpty(map.get(v.getName()).getSkcImgMap())) {
                    return;
                }
                v.getColorNames().forEach(x -> {
                    if (StringUtils.isNotBlank(x.getImg()) && StringUtils.contains(x.getImg(), "weimob")) {
                        return;
                    }
                    if (StringUtils.isBlank(map.get(v.getName()).getSkcImgMap().get(x.getColor_code()))) {
                        return;
                    }
                    x.setImg(map.get(v.getName()).getSkcImgMap().get(x.getColor_code()));
                });
            });
        }
    }

    @Override
    public List<FabSkcByParamInfoResp> querySkcByPatternInfo(FabProductPatternInfoRep requestData) {
        // 根据图案查询数据
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(100);

        QueryGoodsFabEsReq req = new QueryGoodsFabEsReq();
        BeanUtils.copyProperties(requestData, req);
        List<String> urls = new ArrayList<>();
        if (StringUtils.isNotBlank(requestData.getUrl())) {
            urls.add(requestData.getUrl().replace(newPatternPicPrefix, oldPatternPicPrefix));
            urls.add(requestData.getUrl().replace(newPatternPicPrefix, oldPatternPicPrefixIm));
            req.setPatternUrl(urls);
        }
        req.setBrandIds(requestData.getBrandIds());

        List<ProductSpuFabEsResp> resps = spuFabInfoByParams(req, page, "");
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(resps)) {
            return Collections.emptyList();
        }

        // 处理数据
        getFabSkcByParamInfoResps(resps);
        return FabSkcByParamInfoResp.build(resps);
    }

    @Override
    public List<String> queryMerchandiseByParam(String requestData) {
        SearchRequest request = new SearchRequest();
        request.indices(PRODUCT_SKC_INDEX);


        TermsAggregationBuilder aggregation = AggregationBuilders.terms("distinct_values")
                .field(requestData)
                .size(10000); // 指定返回的不同值的数量上限

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .aggregation(aggregation)
                .size(0);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        sourceBuilder.fetchSource(new String[]{requestData}, new String[]{});
        sourceBuilder.query(queryBuilder);
        sourceBuilder.sort(requestData, SortOrder.DESC);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品SKU_FAB-ES {}", request.source().toString());
        List<String> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            Aggregation distinct_values = response.getAggregations().getAsMap().get("distinct_values");
            for (Terms.Bucket sb : ((ParsedStringTerms) distinct_values).getBuckets()) {
                log.info("==============:{}", sb.getKeyAsString() + ":" + sb.getDocCount());
                entities.add(sb.getKeyAsString());
            }

        } catch (IOException e) {
            log.error("查询商品SPU-FAB-ES异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }

        String[] array = entities.toArray(new String[entities.size()]);
        StringSortUtil.sort(array);
        return Arrays.asList(array);
    }

    @Override
    public ProductSpuKeyDpFabResp queryKeyCombinations(ProductSpuKeyDpFabReq requestData) {
        // 查询相关套装
        List<KeyInfoParamsEntity> suitNumbers = keyCombinationsMapper.selectKeyCombinationsBySuit(requestData.getName());
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(suitNumbers)) {
            return ProductSpuKeyDpFabResp.buildRet(requestData.getMatchPicExpends(), null);
        }

        List<KeyInfoSpuEntity> keyCombinations = keyCombinationsMapper.selectByBrandAndGoodSeasonAndSuit(suitNumbers, requestData.getName());
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(keyCombinations)) {
            return ProductSpuKeyDpFabResp.buildRet(requestData.getMatchPicExpends(), null);
        }

        // 根据套装分组
        Map<String, List<KeyInfoSpuEntity>> map = keyCombinations.stream().collect(Collectors.groupingBy(KeyInfoSpuEntity::getSuitNum));

        // 标记重点套装 和 完全没有搭配的套装
        List<KeyInfoSpuEntity> list = new ArrayList<>();
        map.entrySet().forEach(v -> {
            List<KeyInfoSpuEntity> combinations = v.getValue();
            AtomicReference<Integer> i = new AtomicReference<>(0);
            requestData.getMatchPicExpends().forEach(x -> {
                if (!Objects.equals(combinations.size(), x.getMatchPicRecSpus().size())) {
                    return;
                }
                boolean all = x.getMatchPicRecSpus().stream()
                        .map(u -> StringUtils.substring(u.getSkc(), 0, 9))
                        .collect(Collectors.toList()).containsAll(combinations.stream().map(u -> u.getName()).collect(Collectors.toList()));
                if (all) {
                    i.getAndSet(1);
                    x.setIsKey(1);
                }
            });
            if (i.get() == 0) {
                list.addAll(combinations);
            }
        });

        // 处理查询对应套装信息
        ProductSpuKeyDpFabResp fabResp = new ProductSpuKeyDpFabResp();
        fabResp.setMatchPicExpends(requestData.getMatchPicExpends());
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(list)) {
            HashMap<String, String> nameSkcMap = list.stream().collect(HashMap::new, (k, v) -> k.put(v.getName(), v.getSkc()), HashMap::putAll);
            List<ProductSpuFabEsResp> productSpuFabEsResps = getFabInfoByNames(list.stream().map(KeyInfoSpuEntity::getName).distinct().collect(Collectors.toList()));
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(productSpuFabEsResps)) {
                return fabResp;
            }
            HashMap<String, ProductSpuFabEsResp> fabMap = productSpuFabEsResps.stream()
                    .collect(HashMap::new, (k, v) -> k.put(v.getName(), v), HashMap::putAll);
            List<ProductSpuKeyDpFabResp.NoKeyMatchData> noKeyRets = new ArrayList<>();
            list.forEach(v -> {
                ProductSpuKeyDpFabResp.NoKeyMatchData data = new ProductSpuKeyDpFabResp.NoKeyMatchData();
                data.setName(v.getName());
                data.setSuitNum(v.getSuitNum());
                ProductSpuFabEsResp esResp = fabMap.get(v.getName());
                if (esResp == null) {
                    return;
                }
                data.setProductId(esResp.getId());
                data.setValue(esResp.getValue());
                data.setSkc(nameSkcMap.get(v.getName()));
                noKeyRets.add(data);
            });
            fabResp.setNoKeyMatchDataList(noKeyRets);
        }

        return fabResp;
    }

    private List<ProductSpuFabEsResp> getFabInfoByNames(List<String> names) {
        QueryGoodsFabEsReq fabEsReq = new QueryGoodsFabEsReq();
        fabEsReq.setNames(names);
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(1000);
        List<ProductSpuFabEsResp> productSpuFabEsResps = spuFabInfoByParams(fabEsReq, page, "");
        return productSpuFabEsResps;
    }

    @Override
    public List<FabInfoByName> getFabInfoByName(List<String> requestData) {
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(requestData)) {
            throw new RuntimeException("商品款号信息不能为空");
        }
        List<ProductSpuFabEsResp> fabInfoByNames = getFabInfoByNames(requestData.stream().distinct().collect(Collectors.toList()));
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(fabInfoByNames)) {
            return Collections.emptyList();
        }

        List<FabInfoByName> names = new ArrayList<>();
        fabInfoByNames.forEach(v -> {
            FabInfoByName infoByName = new FabInfoByName();
            infoByName.setName(v.getName());
            infoByName.setValue(v.getValue());
            infoByName.setProductId(v.getId());
            names.add(infoByName);
        });

        return names;
    }

    private void dealWashNames(List<String> washNames, int size, FabProductNewArrivalEntity entity) {
        if (size == 1) {
            entity.setWashName1(washNames.get(0));
        }
        if (size == 2) {
            entity.setWashName1(washNames.get(0));
            entity.setWashName2(washNames.get(1));
        }
        if (size == 3) {
            entity.setWashName1(washNames.get(0));
            entity.setWashName2(washNames.get(1));
            entity.setWashName3(washNames.get(2));
        }
        if (size == 4) {
            entity.setWashName1(washNames.get(0));
            entity.setWashName2(washNames.get(1));
            entity.setWashName3(washNames.get(2));
            entity.setWashName4(washNames.get(3));
        }
        if (size == 5) {
            entity.setWashName1(washNames.get(0));
            entity.setWashName2(washNames.get(1));
            entity.setWashName3(washNames.get(2));
            entity.setWashName4(washNames.get(3));
            entity.setWashName5(washNames.get(4));
        }
        if (size == 6) {
            entity.setWashName1(washNames.get(0));
            entity.setWashName2(washNames.get(1));
            entity.setWashName3(washNames.get(2));
            entity.setWashName4(washNames.get(3));
            entity.setWashName5(washNames.get(4));
            entity.setWashName6(washNames.get(5));
        }
    }

    private List<ProductSpcNewArrivalFabEsResp> skcNewArrivalFabInfoByParams(QueryGoodsFabEsReq context, Page page) {
        SearchRequest request = new SearchRequest();
        request.indices(productSkcFabNewArrivalIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        buildQuery(queryBuilder, context);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(context.getSeasonGoods())) {
            queryBuilder.must(QueryBuilders.termsQuery("good_season.keyword", context.getSeasonGoods()));
        }


        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1) * page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        sourceBuilder.sort("pp.keyword", SortOrder.ASC)
                .sort("a_small_season_id", SortOrder.ASC)
                .sort("band_id", SortOrder.ASC)
                .sort("ccchr5.keyword", SortOrder.ASC);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品SKU-FAB-NEW-ARRIVAL-ES {}", request.source().toString());
        List<ProductSpcNewArrivalFabEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductSpcNewArrivalFabEsResp entity = ProductSpcNewArrivalFabEsResp.fromJson(hits[i].getSourceAsString(), ProductSpcNewArrivalFabEsResp.class);
                entity.setSkc_id(hits[i].getId());
                entity.buildSkcList();
                entity.buildFabricList();
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品SPU-FAB-ES异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }


    @Value("${fab.base.info.index}")
    private String fabBaseInfoIndex;

    private ProductSpuFabBaseInfoEsResp baseFabInfoByProductId(String productId) {
        GetRequest request = new GetRequest(fabBaseInfoIndex, productId);
        ProductSpuFabBaseInfoEsResp resp = null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                resp = ProductSpuFabBaseInfoEsResp.fromJson(response.getSourceAsString(), ProductSpuFabBaseInfoEsResp.class);
                resp.setId(productId);
                resp.buildColorNameList();
                if (CollectionUtils.isNotEmpty(resp.getColorNames())) {
                    List<String> boms = resp.getColorNames().stream()
                            .filter(item -> StringUtils.isNotBlank(item.getBom_code()))
                            .map(ProductSpuFabEsResp.ColorName::getBom_code)
                            .distinct().collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(boms) && boms.size() > 1) {
                        resp.setIsSkcDiff(true);
                    } else {
                        resp.setIsSkcDiff(false);
                    }
                }

                if (CollectionUtils.isNotEmpty(resp.getLabels())){
                    resp.setLabels(resp.getLabels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                    resp.setLabel_levels(resp.getLabel_levels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                }

            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品fab-baseFabInfoByProductId异常e = {}", e.getMessage());
        }
        return resp;
    }

    @Value("${fab.selling.point.info.index}")
    private String fabSellingPointInfoIndex;

    private ProductSellingPointEsResp sellingPointInfoByProductId(String productId) {
        GetRequest request = new GetRequest(fabSellingPointInfoIndex, productId);
        ProductSellingPointEsResp resp = null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                resp = ProductSellingPointEsResp.fromJson(response.getSourceAsString(), ProductSellingPointEsResp.class);
                resp.setId(productId);
                resp.buildColorNameList();
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品fab-baseFabInfoByProductId异常e = {}", e.getMessage());
        }
        return resp;
    }



    private List<ProductSellingPointEsResp> batchSellingPointInfoByProductIds(List<String> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            log.error("传入的商品id不能为空");
            return new ArrayList<>();
        }
        SearchRequest request = new SearchRequest();
        request.indices(fabSellingPointInfoIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("id", productIds));


        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(productIds.size());
        sourceBuilder.sort("id", SortOrder.DESC);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("批量查询商品卖点 {}", request.source().toString());
        List<ProductSellingPointEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductSellingPointEsResp entity = ProductSellingPointEsResp.fromJson(hits[i].getSourceAsString(), ProductSellingPointEsResp.class);
                entity.setId(hits[i].getId());
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品SPU-FAB-ES异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }

    @Value("${fab.match.info.index}")
    private String fabMatchInfoIndex;

    private ProductMatchEsResp sellingMatchInfoByProductId(String productId) {
        GetRequest request = new GetRequest(fabMatchInfoIndex, productId);
        ProductMatchEsResp resp = null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                resp = ProductMatchEsResp.fromJson(response.getSourceAsString(), ProductMatchEsResp.class);
                resp.setId(productId);
                resp.buildMatchPic();
                resp.buildDetailPic();
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品fab-baseFabInfoByProductId异常e = {}", e.getMessage());
        }
        return resp;
    }


    @Value("${fab.wash.info.index}")
    private String fabWashInfoIndex;
    private ProductWashInfoEsResp matchInfoByProductId(String productId) {
        GetRequest request = new GetRequest(fabWashInfoIndex, productId);
        ProductWashInfoEsResp resp = null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                resp = ProductWashInfoEsResp.fromJson(response.getSourceAsString(), ProductWashInfoEsResp.class);
                resp.setId(productId);
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品fab-baseFabInfoByProductId异常e = {}", e.getMessage());
        }
        return resp;
    }
}
