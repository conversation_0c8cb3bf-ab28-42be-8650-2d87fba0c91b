package org.springcenter.background.modules.entity.productFab;

import lombok.Data;
import org.springcenter.product.api.dto.ProductSpuFabResp;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/6/6 0:26
 */
@Data
public class FabCategorySkcData {

    private long productId;
    private FabSkusData.ScopedSlots scopedSlots;
    private List<Children> children;
    private String assembly;
    private String outName;
    private int sort;
    private long id;
    private int type;
    private String key;
    private ProductSpuFabResp productInfo;

    @Data
    public static class Children implements Serializable {
        private Integer productId;
        private FabSkusData.ScopedSlots scopedSlots;
        private List<SubChildren> children;
        private String assembly;
        private String outName;
        private Integer sort;
        private long id;
        private Integer type;
        private String key;
        private ProductSpuFabResp productInfo;

    }

    @Data
    public static class SubChildren implements Serializable {
        private Integer productId;
        private FabSkusData.ScopedSlots scopedSlots;
        private Object children = null;
        private String assembly;
        private String outName;
        private Integer sort;
        private Integer id;
        private String key;
        private ProductSpuFabResp productInfo;
        private Boolean flag;
    }
}
