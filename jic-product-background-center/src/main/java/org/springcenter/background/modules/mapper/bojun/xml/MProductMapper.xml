<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.bojun.MProductMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.bojun.MProduct">
        <id column="ID"  property="id" />
        <result column="M_PRODUCT_ORIG" property="mProductOrig" />
    </resultMap>
    <sql id="Base_Column_List">
        ID, M_PRODUCT_ORIG
    </sql>

    <select id="selectByNames" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM m_product
        WHERE M_PRODUCT_ORIG in
        <foreach collection="list" item="name" close=")" open="(" separator=",">
            #{name}
        </foreach>
    </select>

    <select id="selectByOrigName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM M_PRODUCT
        WHERE NAME = #{name}
    </select>


</mapper>
