<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.FabAnDynamicFieldSettingMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.FabAnDynamicFieldSetting">
        <id column="ID" property="id" />
        <result column="FIELD_NAME" property="fieldName" />
        <result column="FIELD" property="field" />
        <result column="BELONG_ATTR" property="belongAttr" />
        <result column="TYPE" property="type" />
        <result column="PART_ORDER" property="partOrder" />
        <result column="WHOLE_ORDER" property="wholeOrder" />
        <result column="FAB_AUTO_NAME_ID" property="fabAutoNameId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="AUTO_TYPE" property="autoType" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, FIELD_NAME, FIELD, BELONG_ATTR, TYPE, PART_ORDER, WHOLE_ORDER, FAB_AUTO_NAME_ID,
            CREATE_TIME, UPDATE_TIME, IS_DELETED, AUTO_TYPE
    </sql>
</mapper>
