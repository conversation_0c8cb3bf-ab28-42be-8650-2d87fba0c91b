<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.WrongGoodsMainMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.WrongGoodsMain">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="stylingCode" column="STYLING_CODE" jdbcType="VARCHAR"/>
            <result property="brand" column="BRAND" jdbcType="VARCHAR"/>
            <result property="season" column="SEASON" jdbcType="VARCHAR"/>
            <result property="month" column="MONTH" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="remark" column="REMARK" jdbcType="VARCHAR"></result>
        <result property="syncPath" column="SYNC_PATH" jdbcType="VARCHAR"></result>
    </resultMap>

    <sql id="Base_Column_List">
        ID,STYLING_CODE,BRAND,
        SEASON,MONTH,STATUS,
        IS_DEL,CREATE_TIME,UPDATE_TIME,REMARK,SYNC_PATH
    </sql>
    <select id="selectByStylingCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from WRONG_GOODS_MAIN where STYLING_CODE = #{stylingCode} and is_del = 0

    </select>
    <select id="selectByRequestData" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from WRONG_GOODS_MAIN
        <where>
            and is_del = 0
            <if test="stylingCode != null and stylingCode != '' ">
                and STYLING_CODE  like concat(concat('%',#{stylingCode}),'%')
            </if>
            <if test="brand != null and brand != '' ">
                and BRAND = #{brand}
            </if>
            <if test="season != null and season != '' ">
                and SEASON = #{season}
            </if>
            <if test="month != null and month != '' ">
                and MONTH = #{month}
            </if>
            <if test="status != null ">
                and status = #{status}
            </if>
            <if test="id != null and id != ''  ">
                and ID = #{id}
            </if>
        </where>
    </select>
</mapper>
