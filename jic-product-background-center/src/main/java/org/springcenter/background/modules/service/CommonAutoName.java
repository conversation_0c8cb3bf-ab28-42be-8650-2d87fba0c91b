package org.springcenter.background.modules.service;

import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.product.api.dto.FabSpuForAutoNameEsResp;
import org.springcenter.background.modules.entity.productFab.CommonAutoNameGenerateEntity;
import org.springcenter.background.modules.entity.productFab.CommonAutoNameSettingEntity;
import org.springcenter.background.modules.entity.productFab.CommonSearchByPageAndIdEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/1/17 14:53
 */
public interface CommonAutoName {

    /**
     * 获取自动品名的基本配值
     * @return 返回
     */
    CommonAutoNameSettingEntity getAutoNameSettingInfo(Integer code);

    /**
     * 生成自动品名逻辑
     * tag 有标识代表是样衣
     * @param entity
     */
    void getAutoGenerate(CommonAutoNameGenerateEntity entity, String tag);


    /**
     * 根据索引和顺序查最大和最小id
     * @param sortOrder 排序规则
     * @param index 索引
     * @return 返回
     */
    Integer searchMinAndMaxNumInEs(SortOrder sortOrder, String index);


    /**
     * 根据id 分页查询数据
     * @return 返回
     */
    List<FabSpuForAutoNameEsResp> searchDataForAutoNameInEsForNum(CommonSearchByPageAndIdEntity entity);

}
