package org.springcenter.background.modules.service;

import org.springcenter.product.api.dto.ExportSizeInfoDataReq;
import org.springcenter.product.api.dto.ProductFabInfoReq;
import org.springcenter.product.api.dto.ProductSizeInfoResp;
import org.springcenter.product.api.dto.back.ProductFabInfoSizeReq;
import org.springcenter.product.api.dto.back.ProductFabInfoSizeResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/12/21 16:59
 */
public interface IProductSizeInfoService {
    /**
     * 返回
     * @param requestData 商品id
     * @return 返回map
     */
    List<ProductSizeInfoResp> queryFabSizeInfo(ProductFabInfoReq requestData);

    /**
     * 返回导入的excel
     * @param requestData 入参
     * @return 返回
     */
    void exportSizeInfoData(String keys, ExportSizeInfoDataReq requestData);

    /**
     * 查询商品的尺码信息-横竖切换
     * @param requestData 入参
     * @return 返回
     */
    ProductFabInfoSizeResp queryFabSizeInfoNew(ProductFabInfoSizeReq requestData);

    /**
     * 双部位去重
     * @param productId
     * @return
     */
    List<List<String>> queryDistinctSizeInfoByProductId(String productId);
}
