<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.ColorCodeNameMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.ColorCodeName">
        <id column="ID" property="id" />
        <result column="COLOR_CODE" property="colorCode" />
        <result column="COLOR_NAME" property="colorName" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, COLOR_CODE, COLOR_NAME
    </sql>

    <select id="selectByColorCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM COLOR_CODE_NAME
        WHERE COLOR_CODE IN
        <foreach collection="list" item="colorCode" separator="," open="(" close=")">
            #{colorCode}
        </foreach>
    </select>


</mapper>
