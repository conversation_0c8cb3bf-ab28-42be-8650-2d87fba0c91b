<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.WrongGoodsPimgsRelationMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.WrongGoodsPimgsRelation">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="wrongGoodsProductId" column="WRONG_GOODS_PRODUCT_ID" jdbcType="VARCHAR"/>
            <result property="wrongGoodsImgsId" column="WRONG_GOODS_IMGS_ID" jdbcType="VARCHAR"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,WRONG_GOODS_PRODUCT_ID,WRONG_GOODS_IMGS_ID,
        IS_DEL,CREATE_TIME,UPDATE_TIME
    </sql>
    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            into WRONG_GOODS_PIMGS_RELATION
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    ID,
                </if>
                <if test="item.wrongGoodsProductId != null">
                    WRONG_GOODS_PRODUCT_ID,
                </if>
                <if test="item.wrongGoodsImgsId != null">
                    WRONG_GOODS_IMGS_ID,
                </if>
                <if test="item.isDel != null">
                    IS_DEL,
                </if>
                <if test="item.createTime != null">
                    CREATE_TIME,
                </if>
                <if test="item.updateTime != null">
                    UPDATE_TIME,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=VARCHAR},
                </if>
                <if test="item.wrongGoodsProductId != null">
                    #{item.wrongGoodsProductId,jdbcType=VARCHAR},
                </if>
                <if test="item.wrongGoodsImgsId != null">
                    #{item.wrongGoodsImgsId,jdbcType=VARCHAR},
                </if>
                <if test="item.isDel != null">
                    #{item.isDel,jdbcType=DECIMAL},
                </if>
                <if test="item.createTime != null">
                    #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateTime != null">
                    #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
            </trim>
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <update id="updateIsDelByWrongProductIds">
        update WRONG_GOODS_PIMGS_RELATION set is_del = #{isDel} where
        WRONG_GOODS_PRODUCT_ID in
        <foreach collection="wrongGoodsProductIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>

    </update>
    <select id="selectByWrongGoodsProductIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from WRONG_GOODS_PIMGS_RELATION
            where is_del = 0 and WRONG_GOODS_PRODUCT_ID in
                                 <foreach collection="wrongGoodsProductIds" open="(" close=")" item="item" separator=",">
                                     #{item}
                                 </foreach>
    </select>
</mapper>
