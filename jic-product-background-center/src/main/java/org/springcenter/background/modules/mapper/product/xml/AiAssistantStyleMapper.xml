<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.AiAssistantStyleMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.AiAssistantStyle">
        <id column="ID" property="id" />
        <result column="STYLE_TITLE" property="styleTitle" />
        <result column="STYLE_INTRODUCTION" property="styleIntroduction" />
        <result column="INPUT_INFO" property="inputInfo" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="FIELDS" property="fields" />
        <result column="TYPE" property="type" />
        <result column="BRAND_TYPE" property="brandType" />
    </resultMap>


</mapper>
