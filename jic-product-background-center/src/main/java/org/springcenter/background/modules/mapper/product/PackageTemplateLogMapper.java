package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.PackageTemplateLog;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【PACKAGE_TEMPLATE_LOG(商品模板日志表)】的数据库操作Mapper
* @createDate 2024-07-26 17:30:44
* @Entity generator.domain.PackageTemplateLog
*/
public interface PackageTemplateLogMapper extends BaseMapper<PackageTemplateLog> {

    List<PackageTemplateLog> selectByPacMainId(@Param("pacMainId") String requestData);
}
