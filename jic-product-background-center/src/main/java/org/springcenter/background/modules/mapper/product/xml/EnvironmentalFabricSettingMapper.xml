<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.EnvironmentalFabricSettingMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.EnvironmentalFabricSetting">
        <id column="ID" property="id" />
        <result column="SYSTEM_NAME" property="systemName" />
        <result column="OUTSIDE_NAME" property="outsideName" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, SYSTEM_NAME, OUTSIDE_NAME,  CREATE_TIME, UPDATE_TIME,IS_DELETED
    </sql>
    <select id="selectAllInfos" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include> FROM ENVIRONMENTAL_FABRIC_SETTING WHERE IS_DELETED = 0
    </select>


</mapper>
