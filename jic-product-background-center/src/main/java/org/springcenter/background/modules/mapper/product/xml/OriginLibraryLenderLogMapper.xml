<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.OriginLibraryLenderLogMapper">

    <!-- 通用查询映射结果,实现org.springcenter.product.api.dto.back.LenderRecordLog类的属性与数据库字段的映射 -->
    <resultMap id="BaseResultMap" type="org.springcenter.product.api.dto.back.LenderRecordLog">
        <result column="SMALL_CATE" property="smallCate" jdbcType="VARCHAR"/>
        <result column="POSITION_NUM" property="positionNum" jdbcType="VARCHAR"/>
        <result column="SAMPLE_NUM" property="sampleNum" jdbcType="VARCHAR"/>
        <result column="OUTER_BRAND" property="outerBrand" jdbcType="VARCHAR"/>
        <result column="INNER_BRAND" property="innerBrand" jdbcType="VARCHAR"/>
        <result column="IMG1" property="img1" jdbcType="VARCHAR"/>
        <result column="ORIGIN_LIBRARY_ID" property="originLibraryId" jdbcType="VARCHAR"/>
        <result column="RETURN_TIME" property="returnTime" jdbcType="TIMESTAMP"/>
    </resultMap>

<!--    <select id="queryLenderLog" parameterType="java.lang.String" resultMap="BaseResultMap">-->
<!--        select-->
<!--        SMALL_CATE,-->
<!--        POSITION_NUM,-->
<!--        SAMPLE_NUM,-->
<!--        OUTER_BRAND,-->
<!--        INNER_BRAND,-->
<!--        IMG1,-->
<!--        ORIGIN_LIBRARY_ID,-->
<!--        RETURN_TIME-->
<!--        from ORIGIN_LIBRARY as a -->
<!--        left join ORIGIN_LIBRARY_LENDER_LOG as b on a.ID = b.ORIGIN_LIBRARY_ID-->
<!--        where ORIGIN_LIBRARY_ID = #{originLibraryId}-->
<!--    </select>-->

</mapper>