package org.springcenter.background.modules.remote;

import org.springcenter.background.modules.remote.entity.AddFeedbackReqEntity;
import org.springcenter.background.modules.remote.entity.DistributeTaskReqEntity;
import org.springcenter.background.modules.remote.entity.FinishTaskReqEntity;
import org.springcenter.background.modules.remote.entity.JicBaseResp;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * <AUTHOR>
 * @Date:2024/3/14 11:10
 */
public interface ITaskCenterRemoteApi {

    // 测试 @POST("/api/gateway/jic-task-api-center/tmc/task/crowd")
    @POST("/api/gateway/task-center/tmc/task/crowd")
    Call<JicBaseResp> distributeTask(@Body DistributeTaskReqEntity req);

    // 测试 @POST("/api/gateway/jic-task-api-center/taskAllocate/changeOverTask")
    @POST("/api/gateway/task-center/taskAllocate/changeOverTask")
    Call<JicBaseResp> finishTask(@Body FinishTaskReqEntity req);


    // 测试 @POST("/api/gateway/jic-task-api-center/taskAllocate/addCheckFeedback")
    @POST("/api/gateway/task-center/taskAllocate/addCheckFeedback")
    Call<JicBaseResp> addCheckFeedback(@Body AddFeedbackReqEntity req);
}
