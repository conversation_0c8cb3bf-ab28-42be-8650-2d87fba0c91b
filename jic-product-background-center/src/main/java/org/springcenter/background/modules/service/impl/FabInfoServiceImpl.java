package org.springcenter.background.modules.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.modules.mapper.product.FabInfoLogMapper;
import org.springcenter.background.modules.mapper.product.FabInfoMapper;
import org.springcenter.background.modules.mapper.product.FabMatchingSugInfoLogMapper;
import org.springcenter.background.modules.mapper.product.FabMatchingSugInfoMapper;
import org.springcenter.background.modules.model.product.FabInfo;
import org.springcenter.background.modules.model.product.FabInfoLog;
import org.springcenter.background.modules.model.product.FabMatchingSugInfo;
import org.springcenter.background.modules.model.product.FabMatchingSugInfoLog;
import org.springcenter.background.modules.service.FabInfoService;
import org.springcenter.product.api.dto.AddProductFabInfoReq;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/9/19 13:19
 */
@Service
@RefreshScope
@Slf4j
public class FabInfoServiceImpl implements FabInfoService {

    @Autowired
    private FabInfoMapper fabInfoMapper;

    @Autowired
    private FabInfoLogMapper fabInfoLogMapper;


    @Autowired
    private FabMatchingSugInfoMapper fabMatchingSugInfoMapper;

    @Autowired
    private FabMatchingSugInfoLogMapper fabMatchingSugInfoLogMapper;

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Value("${fab.wear.tag.id}")
    private String fabWearTagId;

    @Override
    public Boolean saveFabInfo(AddProductFabInfoReq requestData) {
        Boolean isInsert = false;
        FabInfo fabInfo = this.selectFabInfoByProductIdAndName(requestData.getProductId(), requestData.getName());
        if (fabInfo == null) {
            fabInfo = new FabInfo();
            fabInfo.setId(IdLeaf.getId(fabWearTagId));
            fabInfo.setName(requestData.getName());
            fabInfo.setProductId(requestData.getProductId());
            fabInfo.setFab(requestData.getFabInfo());
            fabInfo.setOperators(requestData.getOperates());
            fabInfo.setCreateTime(new Date());
            fabInfo.setUpdateTime(new Date());
            isInsert = true;
        } else {
            fabInfo.setUpdateTime(new Date());
            fabInfo.setOperators(requestData.getOperates());
            fabInfo.setFab(requestData.getFabInfo());
        }

        // 维护日志
        FabInfoLog log = new FabInfoLog();
        BeanUtils.copyProperties(fabInfo, log);
        log.setId(IdLeaf.getId(fabWearTagId));
        log.setFabInfoId(fabInfo.getId());
        log.setCreateTime(new Date());
        log.setUpdateTime(new Date());

        Boolean finalIsInsert = isInsert;
        FabInfo finalFabInfo = fabInfo;
        template.execute(v -> {
            if (finalIsInsert) {
                fabInfoMapper.insert(finalFabInfo);
            } else {
                fabInfoMapper.updateById(finalFabInfo);
            }
            fabInfoLogMapper.insert(log);
            return true;
        });
        return true;
    }

    public FabInfo selectFabInfoByProductIdAndName(String productId, String name) {
        LambdaQueryWrapper<FabInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FabInfo::getProductId, productId);
        wrapper.eq(FabInfo::getName, name);
        wrapper.eq(FabInfo::getIsDeleted, 0);
        return fabInfoMapper.selectOne(wrapper);
    }

    @Override
    public List<FabInfo> selectFabInfoByIdsAndNames(List<String> productIds) {
        QueryWrapper<FabInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("PRODUCT_ID", productIds);
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        return fabInfoMapper.selectList(queryWrapper);
    }

    public FabMatchingSugInfo selectFabMatchingByProductIdAndName(String productId, String name) {
        LambdaQueryWrapper<FabMatchingSugInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FabMatchingSugInfo::getProductId, productId);
        wrapper.eq(FabMatchingSugInfo::getName, name);
        wrapper.eq(FabMatchingSugInfo::getIsDeleted, 0);
        return fabMatchingSugInfoMapper.selectOne(wrapper);
    }

    @Override
    public Boolean saveMatching(AddProductFabInfoReq requestData) {
        Boolean isInsert = false;
        FabMatchingSugInfo fabInfo = this.selectFabMatchingByProductIdAndName(requestData.getProductId(), requestData.getName());
        if (fabInfo == null) {
            fabInfo = new FabMatchingSugInfo();
            fabInfo.setId(IdLeaf.getId(fabWearTagId));
            fabInfo.setName(requestData.getName());
            fabInfo.setProductId(requestData.getProductId());
            fabInfo.setMatchingSug(requestData.getFabInfo());
            fabInfo.setOperators(requestData.getOperates());
            fabInfo.setCreateTime(new Date());
            fabInfo.setUpdateTime(new Date());
            isInsert = true;
        } else {
            fabInfo.setUpdateTime(new Date());
            fabInfo.setOperators(requestData.getOperates());
            fabInfo.setMatchingSug(requestData.getFabInfo());
        }

        // 维护日志
        FabMatchingSugInfoLog log = new FabMatchingSugInfoLog();
        BeanUtils.copyProperties(fabInfo, log);
        log.setId(IdLeaf.getId(fabWearTagId));
        log.setFabSugInfoId(fabInfo.getId());
        log.setCreateTime(new Date());
        log.setUpdateTime(new Date());

        Boolean finalIsInsert = isInsert;
        FabMatchingSugInfo finalFabInfo = fabInfo;
        template.execute(v -> {
            if (finalIsInsert) {
                fabMatchingSugInfoMapper.insert(finalFabInfo);
            } else {
                fabMatchingSugInfoMapper.updateById(finalFabInfo);
            }
            fabMatchingSugInfoLogMapper.insert(log);
            return true;
        });
        return true;
    }
}
