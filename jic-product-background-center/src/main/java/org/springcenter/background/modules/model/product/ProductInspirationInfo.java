package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2025/3/24 17:15
 */
@Data
@TableName(value = "PRODUCT_INSPIRATION_INFO")
public class ProductInspirationInfo {
    @TableId(value = "ID")
    private String id;

    @TableField(value = "PRODUCT_ID")
    private String productId;

    @TableField(value = "NAME")
    private String name;

    @TableField(value = "SKC_NO")
    private String skcNo;

    @TableField(value = "INSPIRATION")
    private String inspiration;

    @TableField(value = "OPERATORS")
    private String operators;

    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;
}
