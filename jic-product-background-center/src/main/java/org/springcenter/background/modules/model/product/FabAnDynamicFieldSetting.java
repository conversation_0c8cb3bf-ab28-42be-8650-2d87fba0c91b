package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/12/11 13:31
 */
@Data
@TableName(value = "FAB_AN_DYNAMIC_FIELD_SETTING")
public class FabAnDynamicFieldSetting {

    @TableId(value = "ID")
    private Integer id;

    @TableField(value = "FIELD_NAME")
    private String fieldName;

    @TableField(value = "FIELD")
    private String field;

    @TableField(value = "BELONG_ATTR")
    private String belongAttr;

    @TableField(value = "TYPE")
    @ApiModelProperty(value = "1为字段的数据， 2为直接是属性值")
    private Integer type;

    @TableField(value = "PART_ORDER")
    private Integer partOrder;

    @TableField(value = "WHOLE_ORDER")
    private Integer wholeOrder;

    @TableField(value = "FAB_AUTO_NAME_ID")
    private Integer fabAutoNameId;

    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    @TableField(value = "AUTO_TYPE")
    @ApiModelProperty(value = "自动品名参数类型 0样衣 1微商城 2电商")
    private Integer autoType;
}
