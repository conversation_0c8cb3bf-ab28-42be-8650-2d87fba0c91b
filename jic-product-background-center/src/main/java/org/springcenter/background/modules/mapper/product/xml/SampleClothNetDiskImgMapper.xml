<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.SampleClothNetDiskImgMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.SampleClothNetDiskImg">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="sampleClothCode" column="SAMPLE_CLOTH_CODE" jdbcType="VARCHAR"/>
            <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="qiniuImgUrl" column="QINIU_IMG_URL" jdbcType="VARCHAR"/>
            <result property="neid" column="NEID" jdbcType="VARCHAR"/>
            <result property="nsid" column="NSID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,SAMPLE_CLOTH_CODE,FILE_NAME,
        IS_DEL,CREATE_TIME,UPDATE_TIME,
        QINIU_IMG_URL,NEID,NSID
    </sql>
    <select id="selectSampleClothCodeAndName"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from SAMPLE_CLOTH_NET_DISK_IMG
        <where>
            <if test="sampleClothCode != null and sampleClothCode != ''">
                and SAMPLE_CLOTH_CODE = #{sampleClothCode}
            </if>
            <if test="name != null and name != ''">
                and FILE_NAME = #{name}
            </if>
            <if test="isDel != null">
                and IS_DEL = #{isDel}
            </if>
        </where>
    </select>
    <select id="selectbySampleCloths"
           resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from SAMPLE_CLOTH_NET_DISK_IMG
        <where>
            and  IS_DEL = 0
            <if test="sampleCloths != null">
                and SAMPLE_CLOTH_CODE in
                <foreach collection="sampleCloths" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
