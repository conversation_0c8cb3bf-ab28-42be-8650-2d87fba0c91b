package org.springcenter.background.modules.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/3/24 15:55
 */
@Data
public class ProductBatchCommonEntity {

    private List<ImportCommonInsertData> baseDataList;

    private List<AllInfo> errorDataList;


    private List<AllInfo> allDataList;

    @Data
    public static class ErrorData {

        @ApiModelProperty(value = "款色号")
        private String skcNo;

        @ApiModelProperty(value = "信息")
        private String info;

        @ApiModelProperty(value = "原因")
        private String reason;


    }

    @Data
    public static class AllInfo {

        @ApiModelProperty(value = "款色号")
        private String skcNo;

        @ApiModelProperty(value = "信息")
        private String info;

        private String name;

        @ApiModelProperty(value = "原因")
        private String reason;

        @ApiModelProperty(value = "类型 0正常 1异常")
        private Integer type;

        private Long productId;

        @ApiModelProperty(value = "色号")
        private String skcCode;
    }
}
