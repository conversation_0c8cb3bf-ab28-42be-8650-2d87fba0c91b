package org.springcenter.background.modules.mapper.product;


import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.ProductDetailNetDiskImg;
import org.springcenter.background.modules.model.product.ProductOverseasNetDiskImg;

import java.util.List;

public interface ProductOverseasNetDiskImgMapper {
    int deleteByPrimaryKey(String id);

    int insert(ProductOverseasNetDiskImg record);

    int insertSelective(ProductOverseasNetDiskImg record);

    ProductOverseasNetDiskImg selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(ProductOverseasNetDiskImg record);

    int updateByPrimaryKey(ProductOverseasNetDiskImg record);

    List<ProductOverseasNetDiskImg> selectBySkcAndIsDel(@Param("productCode") String productCode,
                                                        @Param("colorNo") String colorNo,
                                                        @Param("isDel") Integer isDel,
                                                        @Param("type") Integer type);

    void batchInsert(@Param("list") List<ProductOverseasNetDiskImg> insertList);

    List<ProductOverseasNetDiskImg> selectByProductCodesAndType(@Param("list") List<String> productCodes, @Param("type") Integer type);
}