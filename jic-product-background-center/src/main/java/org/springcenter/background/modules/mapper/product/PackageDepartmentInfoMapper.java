package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.PackageDepartmentInfo;
import org.springcenter.background.modules.model.product.PackageImportInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【PACKAGE_IMPORT_INFO(商品包导入信息)】的数据库操作Mapper
* @createDate 2024-07-25 13:28:59
* @Entity generator.domain.PackageImportInfo
*/
public interface PackageDepartmentInfoMapper extends BaseMapper<PackageDepartmentInfo> {


    void batchInsert(@Param("list") List<PackageDepartmentInfo> importInfos);

    List<PackageDepartmentInfo> selectByPacId(@Param("pacId") String id);

    void updateByPacId(@Param("pacId") String id);

    List<String> selectByDepartmentIds(@Param("list") List<String> selfAllow);
}
