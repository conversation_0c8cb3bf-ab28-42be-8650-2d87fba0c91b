package org.springcenter.background.modules.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2025/3/24 15:57
 */
@Data
public class ImportCommonInsertData {

    private String skcNo;

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品款号")
    private String name;

    @ApiModelProperty(value = "导入信息")
    private String info;

    @ApiModelProperty(value = "操作人")
    private String operators;

    private String id;

    private Date createTime;

    private Date updateTime;
}
