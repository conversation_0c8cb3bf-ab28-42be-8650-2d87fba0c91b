package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.MapUtils;
import com.beust.jcommander.internal.Lists;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.background.config.exception.ProductException;
import org.springcenter.background.modules.entity.UserTaskEntity;
import org.springcenter.background.modules.event.IAddFeedbackTaskEvent;
import org.springcenter.background.modules.event.IDistributeTaskEvent;
import org.springcenter.background.modules.event.IFinishTaskEvent;
import org.springcenter.background.modules.event.bus.IAddFeedbackTaskEventBus;
import org.springcenter.background.modules.event.bus.IDistributeTaskEventBus;
import org.springcenter.background.modules.event.bus.IFinishTaskEventBus;
import org.springcenter.background.modules.event.context.AddFeedbackContext;
import org.springcenter.background.modules.event.context.DistributeContext;
import org.springcenter.background.modules.event.context.FinishTaskContext;
import org.springcenter.background.modules.mapper.bojun.CAreaMapper;
import org.springcenter.background.modules.mapper.bojun.CCityMapper;
import org.springcenter.background.modules.mapper.bojun.CStoreMapper;
import org.springcenter.background.modules.mapper.product.FabVolumeBrowseFinishLogMapper;
import org.springcenter.background.modules.mapper.product.FabVolumeDistributeTaskLogMapper;
import org.springcenter.background.modules.mapper.product.FabVolumeInfoMapper;
import org.springcenter.background.modules.mapper.product.UserTaskMapper;
import org.springcenter.background.modules.model.bojun.CArea;
import org.springcenter.background.modules.model.bojun.CCity;
import org.springcenter.background.modules.model.bojun.CStore;
import org.springcenter.background.modules.model.product.FabVolumeBrowseFinishLog;
import org.springcenter.background.modules.model.product.FabVolumeDistributeTaskLog;
import org.springcenter.background.modules.model.product.FabVolumeInfo;
import org.springcenter.background.modules.remote.entity.DistributeTaskReqEntity;
import org.springcenter.background.modules.remote.service.ITaskCenterService;
import org.springcenter.background.modules.service.IFabTaskCenterService;
import org.springcenter.background.modules.util.DateUtil;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.DistributeTaskTypeEnum;
import org.springcenter.product.api.enums.FabVolumeTypeEnum;
import org.springcenter.product.api.enums.TaskCheckTypeEnum;
import org.springcenter.product.api.enums.TaskFeedBackEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/3/14 14:11
 */
@Service
@Slf4j
@RefreshScope
public class FabTaskCenterServiceImpl implements IFabTaskCenterService {

    @Value(value = "${periodDay}")
    private String periodDay;

    @Value(value = "${task.template}")
    private String taskTemplate;

    @Value(value = "${task.store.id}")
    private String taskStoreId;

    @Value(value = "${zhiying.task.store.id}")
    private String zhiYingTaskStoreId;

    @Value(value = "${fab.distribute.task.leaf.tag}")
    private String fabDisTaskLeafTag;

    @Value(value = "${brand.str}")
    private String brandStr;

    @Value(value = "${task.url}")
    private String taskUrl;

    @Autowired
    private IDistributeTaskEventBus distributeTaskEventBus;

    @Autowired
    private IFinishTaskEventBus finishTaskEventBus;

    @Autowired
    private IAddFeedbackTaskEventBus addFeedbackTaskEventBus;

    @Autowired
    private FabVolumeDistributeTaskLogMapper fabVolumeDistributeTaskLogMapper;

    @Autowired
    private FabVolumeInfoMapper fabVolumeInfoMapper;

    @Autowired
    private FabVolumeBrowseFinishLogMapper fabVolumeBrowseFinishLogMapper;

    @Autowired
    private UserTaskMapper userTaskMapper;

    @Autowired
    private CStoreMapper cStoreMapper;

    @Autowired
    private CAreaMapper cAreaMapper;

    @Autowired
    private CCityMapper cCityMapper;

    @Override
    public void createBrowseTask() {
        // 根据产品册的发布时间捞取今日发布的
        List<FabVolumeInfo> favVolumeList = fabVolumeInfoMapper.selectDisplayIsToday();
        if (CollectionUtils.isEmpty(favVolumeList)) {
            return;
        }

        // 组装触发的实体
        List<DistributeTaskReqEntity> distributeEntities = new ArrayList<>();
        List<FabVolumeDistributeTaskLog> logs = new ArrayList<>();
        favVolumeList.forEach(v -> {
            DistributeTaskReqEntity taskReqEntity = buildDistributeTaskEntity(DistributeTaskTypeEnum.BROWSE.getDesc(),
                    v, null);
            if (taskReqEntity == null) {
                return;
            }
            distributeEntities.add(taskReqEntity);
            FabVolumeDistributeTaskLog log = buildFabVolumeDisLog(v, taskReqEntity, DistributeTaskTypeEnum.BROWSE.getCode());
            logs.add(log);
        });
        if (CollectionUtils.isEmpty(logs)) {
            return;
        }

        // 实际触发&&保存记录
        log.info("======处理触发浏览任务:{}", distributeEntities.stream().map(DistributeTaskReqEntity::getCampaignId).collect(Collectors.toList()));
        triggerAndSave(distributeEntities, logs);
    }

    private void triggerAndSave(List<DistributeTaskReqEntity> distributeEntities, List<FabVolumeDistributeTaskLog> logs) {
        /*DistributeContext context = new DistributeContext();
        context.setDistributeTaskReqEntityList(distributeEntities);
        IDistributeTaskEvent event = new IDistributeTaskEvent(context,
                Long.valueOf(IdLeaf.getDateId(fabDisTaskLeafTag)),
                Long.valueOf(IdLeaf.getDateId(fabDisTaskLeafTag)),
                UUID.randomUUID());
        try {
            distributeTaskEventBus.post(event);
        } catch (PersistentBus.EventBusException e) {
            throw new RuntimeException("处理触发浏览任务失败");
        }*/

        // 异步调用生成任务
        CompletableFuture.runAsync(() -> {
            triggerCreateTask(distributeEntities);
        });

        // 保存调用记录
        if (CollectionUtils.isNotEmpty(logs)) {
            log.info("==============保存数据");
            fabVolumeDistributeTaskLogMapper.batchInsert(logs);
        }
    }

    @Autowired
    private ITaskCenterService taskCenterService;

    private void triggerCreateTask(List<DistributeTaskReqEntity> distributeEntities) {
        log.info("===================调用分发任务：{}", JSONObject.toJSONString(distributeEntities));
        // 调用分发任务
        distributeEntities.forEach(v -> {
            taskCenterService.distributeTask(v);
        });
    }


    @Override
    public void createAssessTask() {
        // 捞取日志任务类型为浏览的中nextDay是今天
        List<FabVolumeDistributeTaskLog> fabVolumeDistributeTaskLogs = fabVolumeDistributeTaskLogMapper.selectDisTaskIsToday();
        if (CollectionUtils.isEmpty(fabVolumeDistributeTaskLogs)) {
            return;
        }

        // 组装触发的实体
        List<DistributeTaskReqEntity> distributeEntities = new ArrayList<>();
        List<FabVolumeDistributeTaskLog> logs = new ArrayList<>();
        fabVolumeDistributeTaskLogs.forEach(v -> {
            DistributeTaskReqEntity taskReqEntity = buildDistributeTaskEntity(DistributeTaskTypeEnum.ASSESS.getDesc(),
                    null, v);
            if (taskReqEntity == null) {
                return;
            }
            distributeEntities.add(taskReqEntity);
            FabVolumeDistributeTaskLog log = buildFabVolumeDisLogForAssess(v, taskReqEntity, DistributeTaskTypeEnum.ASSESS.getCode());
            logs.add(log);
        });
        if (CollectionUtils.isEmpty(logs)) {
            return;
        }

        // 实际触发&&保存记录
        log.info("======处理触发考核任务:{}", distributeEntities.stream().map(DistributeTaskReqEntity::getPromotionIds).collect(Collectors.toList()));
        triggerAndSave(distributeEntities, logs);
    }



    @Override
    public Boolean isFinishTask(FabTaskIsFinishReq requestData) {
        // 1、判断当前产品册是否已经发布 没有发布且为展示不展示悬浮球
        FabVolumeInfo fabVolumeInfo = fabVolumeInfoMapper.selectById(requestData.getFabVolumeId());
        if (fabVolumeInfo == null) {
            throw new RuntimeException("当前产品册不存在");
        }
        if (fabVolumeInfo.getDisplayTime() == null || fabVolumeInfo.getDisplayTime().after(new Date())) {
            return true;
        }


        // 2、判断当前产品册是否结束 展示但是已过期不展示悬浮球
        if (fabVolumeInfo.getDisplayTime() != null && fabVolumeInfo.getDisplayTime().before(new Date())) {
            FabVolumeDistributeTaskLog taskLog = fabVolumeDistributeTaskLogMapper.selectByBrowseNextDay(requestData.getFabVolumeId());
            if ((taskLog == null && DateUtil.addOnlyDate(fabVolumeInfo.getDisplayTime(), 7).before(new Date()))
                    || taskLog != null && taskLog.getNextDistributeTime().before(new Date())) {
                return true;
            }
        }


        // 3、查看当前任务是否完成
        List<FabVolumeBrowseFinishLog> rets = fabVolumeBrowseFinishLogMapper
                .judgeIsFinish(requestData.getFabVolumeId(), requestData.getHrId(), requestData.getCStoreId());
        return CollectionUtils.isNotEmpty(rets);
    }

    @Override
    public Boolean finishTask(FabTaskIsFinishReq requestData) {
        List<FabVolumeBrowseFinishLog> rets = fabVolumeBrowseFinishLogMapper
                .judgeIsFinish(requestData.getFabVolumeId(), requestData.getHrId(), requestData.getCStoreId());

        if (CollectionUtils.isNotEmpty(rets)) {
            return true;
        }

        // 查找当前产品册的导购的任务id
        // 查询当前产品册的
        List<UserTaskEntity> taskIdInfo = userTaskMapper.getTaskIdInfo(Lists.newArrayList(requestData.getHrId()),
                Lists.newArrayList(requestData.getCStoreId()),
                Lists.newArrayList(requestData.getFabVolumeId()));


        // 触发任务
        triggerFinishTask(taskIdInfo);

        // 组装完成任务实体 不兼容同个任务分发多个导购的情况
        FabVolumeBrowseFinishLog log = buildFabVolumeBrowseFinishLog(taskIdInfo, requestData);
        fabVolumeBrowseFinishLogMapper.insert(log);
        return true;
    }

    @Override
    public FabTaskIsFilterResp getFilters() {
        // 序列化
        List<FabTaskIsFilterDataResp> brands = JSONObject.parseArray(brandStr, FabTaskIsFilterDataResp.class);

        // 查询大区
        List<FabTaskIsFilterDataResp> regions = cStoreMapper.selectRegions();
        FabTaskIsFilterResp resp = new FabTaskIsFilterResp();
        resp.setBrands(brands);
        resp.setRegions(regions);
        return resp;
    }

    @Override
    public List<FabTaskIsFilterDataResp> getFiltersByParam(FabTaskIsFilterByParamReq req) {
        List<FabTaskIsFilterDataResp> rets;
        // 查询
        if (Objects.equals(req.getType(), 0)) {
            // 城市
            rets = cStoreMapper.selectCity(req.getParam());

        } else {
            // 门店
            rets = cStoreMapper.selectStore(req.getParam());
        }
        return rets;
    }

    @Override
    public List<FabTaskCheckListResp> getCheckTaskList(FabTaskCheckParamReq req, Page page) {
        // 分页
        com.github.pagehelper.Page<FabTaskCheckListResp> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<FabTaskCheckListResp> rets = userTaskMapper.getCheckTaskList(req.getBrandId(), req.getRegionId(),
                req.getCityId(), req.getStoreId());

        // 查询对用的城市 区域 品牌
        if (CollectionUtils.isEmpty(rets)) {
            return Collections.emptyList();
        }
        List<FabTaskIsFilterDataResp> brands = JSONObject.parseArray(brandStr, FabTaskIsFilterDataResp.class);
        List<CArea> areaList = cAreaMapper.selectByIds(rets.stream()
                .map(FabTaskCheckListResp::getRegionId).collect(Collectors.toList()));
        List<CCity> cityList = cCityMapper.selectByIds(rets.stream()
                .map(FabTaskCheckListResp::getCityId).collect(Collectors.toList()));
        List<FabVolumeInfo> infos = fabVolumeInfoMapper.selectByIds(rets.stream()
                .filter(v -> StringUtils.isNotBlank(v.getVolumeId()))
                .map(FabTaskCheckListResp::getVolumeId).collect(Collectors.toList()));

        PageInfo<FabTaskCheckListResp> pageInfo = new PageInfo<>(hPage);
        pageInfo.setList(buildCheckListRet(brands, areaList, cityList, rets, infos));
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }


    @Override
    public Boolean addFeedbackInfo(AddFeedbackReq requestData) {
        // 查询当前任务是否有材料信息 无则不让评价
        String id = userTaskMapper.selectTaskCheckReport(requestData.getTaskId());
        if (StringUtils.isBlank(id)) {
            throw new ProductException("当前任务没有材料信息，不允许反馈");
        }

        AddFeedbackContext context = new AddFeedbackContext();
        context.setAddFeedbackReq(requestData);
        IAddFeedbackTaskEvent event = new IAddFeedbackTaskEvent(context,
                Long.valueOf(IdLeaf.getDateId(fabDisTaskLeafTag)),
                Long.valueOf(IdLeaf.getDateId(fabDisTaskLeafTag)),
                UUID.randomUUID());
        try {
            addFeedbackTaskEventBus.post(event);
        } catch (PersistentBus.EventBusException e) {
            throw new RuntimeException("增加任务反馈失败");
        }
        return true;
    }

    @Override
    public void tryFinishTask() {
        List<FabVolumeBrowseFinishLog> logs = fabVolumeBrowseFinishLogMapper.selectNotPush();
        if (CollectionUtils.isEmpty(logs)) {
            return;
        }

        List<UserTaskEntity> taskIdInfo = userTaskMapper.getTaskIdInfo(
                logs.stream().map(FabVolumeBrowseFinishLog::getHrId).collect(Collectors.toList()),
                logs.stream().map(FabVolumeBrowseFinishLog::getCStoreId).collect(Collectors.toList()),
                logs.stream().map(FabVolumeBrowseFinishLog::getFabVolumeId).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(taskIdInfo)) {
            return;
        }

        log.info("=============未推过去的数据量：{}", logs.size());
        // 触发任务
        triggerFinishTask(taskIdInfo);

        // 更新任务
        fabVolumeBrowseFinishLogMapper.updateByIds(logs);
        return;
    }

    private void triggerFinishTask(List<UserTaskEntity> taskIdInfo) {
        if (CollectionUtils.isNotEmpty(taskIdInfo)) {
            FinishTaskContext context = new FinishTaskContext();
            context.setTaskIds(taskIdInfo.stream().map(UserTaskEntity::getTaskId).collect(Collectors.toList()));
            IFinishTaskEvent event = new IFinishTaskEvent(context,
                    Long.valueOf(IdLeaf.getDateId(fabDisTaskLeafTag)),
                    Long.valueOf(IdLeaf.getDateId(fabDisTaskLeafTag)),
                    UUID.randomUUID());
            try {
                finishTaskEventBus.post(event);
            } catch (PersistentBus.EventBusException e) {
                throw new RuntimeException("处理完成浏览任务失败");
            }
        }
    }

    private List<FabTaskCheckListResp> buildCheckListRet(List<FabTaskIsFilterDataResp> brands, List<CArea> areaList,
                                                         List<CCity> cityList, List<FabTaskCheckListResp> rets,
                                                         List<FabVolumeInfo> infos) {
        if (CollectionUtils.isEmpty(brands) || CollectionUtils.isEmpty(areaList) || CollectionUtils.isEmpty(cityList)
            || CollectionUtils.isEmpty(infos)) {
            return rets;
        }

        HashMap<Long, String> brandMap = brands.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getCode(), v.getName()), HashMap::putAll);

        HashMap<Long, String> areaMap = areaList.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getId(), v.getAreaName()), HashMap::putAll);

        HashMap<Long, String> cityMap = cityList.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getId(), v.getName()), HashMap::putAll);

        HashMap<String, FabVolumeInfo> infoMap = infos.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getId(), v), HashMap::putAll);

        List<FabTaskCheckListResp> backRets = new ArrayList<>();
        rets.forEach(v -> {
            FabTaskCheckListResp resp = new FabTaskCheckListResp();
            BeanUtils.copyProperties(v, resp);
            resp.setHrId(v.getHrId());
            resp.setFeedbackTimeStr(v.getFeedbackTime() == null ? null :DateUtil.getStrDate(v.getFeedbackTime()));
            if (StringUtils.isBlank(brandMap.get(v.getCArcBrandId()))) {
                return;
            }
            resp.setBrandName(brandMap.get(v.getCArcBrandId()));

            if (StringUtils.isBlank(areaMap.get(v.getRegionId()))) {
                return;
            }
            resp.setRegionName(areaMap.get(v.getRegionId()));

            if (StringUtils.isBlank(cityMap.get(v.getCityId()))) {
                return;
            }
            resp.setCityName(cityMap.get(v.getCityId()));

            if (infoMap.get(v.getVolumeId()) == null) {
                return;
            }
            resp.setVolumeName(infoMap.get(v.getVolumeId()).getFabName());
            resp.setBandName(infoMap.get(v.getVolumeId()).getBandName());
            backRets.add(resp);
        });
        return backRets;
    }

    private FabVolumeBrowseFinishLog buildFabVolumeBrowseFinishLog(List<UserTaskEntity> taskIdInfo,
                                                                   FabTaskIsFinishReq requestData) {
        // 查询门店
        CStore cStore = cStoreMapper.selectById(requestData.getCStoreId());
        if (cStore == null) {
            throw new RuntimeException("未找到门店");
        }

        FabVolumeBrowseFinishLog finishLog = new FabVolumeBrowseFinishLog();
        finishLog.setId(IdLeaf.getDateId(fabDisTaskLeafTag));
        finishLog.setCreateTime(new Date());
        finishLog.setUpdateTime(new Date());
        finishLog.setFabVolumeId(requestData.getFabVolumeId());
        finishLog.setCStoreId(requestData.getCStoreId());
        finishLog.setHrId(requestData.getHrId());
        finishLog.setStoreBrandId(cStore.getCArcbrandId());
        finishLog.setCStoreId(cStore.getId());
        if (CollectionUtils.isNotEmpty(taskIdInfo)) {
            // 查询浏览任务的场景id
            List<FabVolumeDistributeTaskLog> distributeTaskLogs = fabVolumeDistributeTaskLogMapper.selectFabInfo(requestData.getFabVolumeId());
            if (CollectionUtils.isEmpty(distributeTaskLogs)) {
                throw new RuntimeException("未找到当前下发任务");
            }
            finishLog.setTaskItemId(Objects.toString(taskIdInfo.get(0).getTaskId()));
            finishLog.setSendType(1);
            finishLog.setCampaignId(distributeTaskLogs.get(0).getCampaignId());
        } else {
            finishLog.setSendType(0);
        }
        return finishLog;
    }

    private FabVolumeDistributeTaskLog buildFabVolumeDisLogForAssess(FabVolumeDistributeTaskLog taskLog, 
                                                                     DistributeTaskReqEntity taskReqEntity, 
                                                                     Integer code) {
        FabVolumeDistributeTaskLog log = new FabVolumeDistributeTaskLog();
        log.setId(IdLeaf.getDateId(fabDisTaskLeafTag));
        log.setFabVolumeId(taskLog.getFabVolumeId());
        log.setFabVolumeName(taskLog.getFabVolumeName());
        log.setStorePakId(taskLog.getStorePakId());
        log.setBrandId(taskLog.getBrandId());
        log.setTaskType(code);
        log.setCreateTime(new Date());
        log.setUpdateTime(new Date());
        log.setTaskTemplateId(taskReqEntity.getTemplateId());
        log.setTaskCheckType(taskReqEntity.getCheckType());
        log.setTaskFeedBack(taskReqEntity.getCheckFeedback());
        log.setCampaignId(taskReqEntity.getCampaignId());
        log.setTaskValidPeriod(taskReqEntity.getTaskValidPeriod());
        log.setFabType(taskLog.getFabType());
        return log;
    }

    private FabVolumeDistributeTaskLog buildFabVolumeDisLog(FabVolumeInfo info, DistributeTaskReqEntity taskReqEntity,
                                                            Integer taskType) {
        FabVolumeDistributeTaskLog log = new FabVolumeDistributeTaskLog();
        log.setId(IdLeaf.getDateId(fabDisTaskLeafTag));
        log.setFabVolumeId(info.getId());
        log.setFabVolumeName(info.getFabName());
        log.setStorePakId(Long.valueOf(taskReqEntity.getStorePackageIds().get(0)));
        log.setBrandId(info.getBrandCode());
        log.setTaskType(taskType);
        log.setCreateTime(new Date());
        log.setUpdateTime(new Date());
        log.setTaskTemplateId(taskReqEntity.getTemplateId());
        log.setTaskUrl(taskReqEntity.getTaskUrl());
        log.setTaskCheckType(taskReqEntity.getCheckType());
        log.setTaskFeedBack(taskReqEntity.getCheckFeedback());
        log.setCampaignId(taskReqEntity.getCampaignId());
        log.setTaskValidPeriod(taskReqEntity.getTaskValidPeriod());
        log.setNextDistributeTime(Objects.equals(taskType, 0) ?
                DateUtil.addOnlyDate(new Date(), 7)
                : null);
        log.setFabType(info.getType());
        return log;
    }

    private DistributeTaskReqEntity buildDistributeTaskEntity(String type, FabVolumeInfo fabVolume,
                                                              FabVolumeDistributeTaskLog taskLog) {
        DistributeTaskReqEntity entity = new DistributeTaskReqEntity();
        // 用当前时间戳当作场景id
        entity.setCampaignId("FAB" + System.currentTimeMillis());
        // 任务持续时间
        entity.setTaskValidPeriod(Integer.parseInt(periodDay));
        // 为空默认为day
        // entity.setPeriodUnit("day");

        // 任务模板id
        Map<String, String> map = JSONObject.parseObject(taskTemplate, Map.class);
        if (map.isEmpty() || StringUtils.isBlank(map.get(type))) {
            log.error("==========================未找到对应的模板id:{}", taskTemplate);
            return null;
            //throw new RuntimeException("未找到对应的模板id");
        }
        entity.setTemplateId(map.get(type));

        // 门店包 区分直营和经销
        Integer fabType = Objects.equals(DistributeTaskTypeEnum.ASSESS.getDesc(), type) ?
                taskLog.getFabType() : fabVolume.getType();
        Long brandCode = Objects.equals(DistributeTaskTypeEnum.ASSESS.getDesc(), type) ?
                taskLog.getBrandId() : fabVolume.getBrandCode();
        Map<Long, String> storeMap = new HashMap<>();
        if (Objects.equals(FabVolumeTypeEnum.ZHI_YING.getCode(), fabType)) {
            storeMap = JSONObject.parseObject(zhiYingTaskStoreId, Map.class);
        } else {
            storeMap = JSONObject.parseObject(taskStoreId, Map.class);
        }
        if (MapUtils.isEmpty(storeMap) || StringUtils.isBlank(storeMap.get(brandCode))) {
            log.error("==========================未找到对应的门店id:{}", storeMap);
            return null;
        }

        List<Integer> storePackages = new ArrayList<>();
        storePackages.add(Integer.valueOf(storeMap.get(brandCode)));
        entity.setStorePackageIds(storePackages);



        // --------------------------------不同的字段------------------------------
        // 浏览任务
        if (Objects.equals(DistributeTaskTypeEnum.BROWSE.getDesc(), type)) {
            entity.setProductBrochureBrand(brandCode);
            entity.setProductBrochureId(Long.valueOf(fabVolume.getId()));
            entity.setTaskUrl(taskUrl);
        } else {
        // 考核任务
            // 2是线上
            entity.setCheckType(TaskCheckTypeEnum.ONLINE.getCode());
            // 1是需要反馈
            entity.setCheckFeedback(TaskFeedBackEnum.NEED_FEED_BACK.getCode());
        }
        return entity;
    }
}
