package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 陈列册文件映射
 */
@TableName(value = "DISPLAY_BOOK_FILE_MAPPING")
@Data
public class DisplayBookFileMapping {

    @TableId(value = "ID")
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField(value = "CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 0正常 1已删除")
    private Integer isDeleted;

    @TableField(value = "BRAND_CODE")
    @ApiModelProperty(value = "品牌id")
    private Long brandCode;

    @TableField(value = "BRAND_NAME")
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @TableField(value = "YEAR")
    @ApiModelProperty(value = "年份")
    private String year;

    @TableField(value = "SEASON_GOODS")
    @ApiModelProperty(value = "货季")
    private String seasonGoods;

    @TableField(value = "CHANNEL")
    @ApiModelProperty(value = "渠道:直营/经销")
    private String channel;

    @TableField(value = "OPERATOR")
    @ApiModelProperty(value = "操作人")
    private String operator;

    @TableField(value = "MANNEQUIN_EXCEL_URL")
    @ApiModelProperty(value = "人台图Excel地址")
    private String mannequinExcelUrl;

    @TableField(value = "HANGER_EXCEL_URL")
    @ApiModelProperty(value = "货杆图Excel地址")
    private String hangerExcelUrl;

}
