<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.DisplayBookPictureMappingMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.DisplayBookPictureMapping">
        <id column="ID" property="id" />
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="IS_DELETED" property="isDeleted"/>
        <result column="DISPLAY_BOOK_FILE_MAPPING_ID" property="displayBookFileMappingId"/>
        <result column="BRAND_CODE" property="brandCode"/>
        <result column="BRAND_NAME" property="brandName"/>
        <result column="YEAR" property="year"/>
        <result column="SEASON_GOODS" property="seasonGoods"/>
        <result column="CHANNEL" property="channel"/>
        <result column="OPERATOR" property="operator"/>
        <result column="PICTURE_TYPE" property="pictureType"/>
        <result column="PICTURE_ID" property="pictureId"/>
        <result column="PICTURE_URL" property="pictureUrl"/>
        <result column="BAND_ID" property="bandId"/>
        <result column="BAND_NAME" property="bandName"/>
        <result column="GROUP_NUMBER" property="groupNumber"/>
        <result column="SKC_CODE" property="skcCode"/>
        <result column="SKC_SMALL_CLASS_NAME" property="skcSmallClassName"/>
        <result column="SKC_IMG_URL" property="skcImgUrl"/>
        <result column="STATUS" property="status"/>
        <result column="ERROR_MSG" property="errorMsg"/>
    </resultMap>

    <insert id="insertBatch" parameterType="list" useGeneratedKeys="false">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO DISPLAY_BOOK_PICTURE_MAPPING
            (ID, CREATE_TIME, UPDATE_TIME, IS_DELETED, DISPLAY_BOOK_FILE_MAPPING_ID, BRAND_CODE, BRAND_NAME, YEAR,
            SEASON_GOODS, CHANNEL, OPERATOR, PICTURE_TYPE, PICTURE_ID, PICTURE_URL, BAND_ID, BAND_NAME, GROUP_NUMBER,
            SKC_CODE, SKC_SMALL_CLASS_NAME, SKC_IMG_URL, STATUS, ERROR_MSG) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=VARCHAR}, #{item.displayBookFileMappingId,jdbcType=VARCHAR},
            #{item.brandCode,jdbcType=VARCHAR}, #{item.brandName,jdbcType=VARCHAR}, #{item.year,jdbcType=VARCHAR},
            #{item.seasonGoods,jdbcType=VARCHAR}, #{item.channel,jdbcType=VARCHAR}, #{item.operator,jdbcType=VARCHAR},
            #{item.pictureType,jdbcType=VARCHAR}, #{item.pictureId,jdbcType=VARCHAR}, #{item.pictureUrl,jdbcType=VARCHAR},
            #{item.bandId,jdbcType=VARCHAR}, #{item.bandName,jdbcType=VARCHAR}, #{item.groupNumber,jdbcType=VARCHAR},
            #{item.skcCode,jdbcType=VARCHAR}, #{item.skcSmallClassName,jdbcType=VARCHAR}, #{item.skcImgUrl,jdbcType=VARCHAR}
            ,#{item.status,jdbcType=VARCHAR}, #{item.errorMsg,jdbcType=VARCHAR}
            )
        </foreach>
        SELECT 1 FROM DUAL
    </insert>
</mapper>
