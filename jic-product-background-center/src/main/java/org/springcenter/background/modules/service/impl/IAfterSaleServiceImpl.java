package org.springcenter.background.modules.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springcenter.background.config.exception.ProductException;
import org.springcenter.background.modules.mapper.product.AfterSaleProductLabelMapper;
import org.springcenter.background.modules.model.product.AfterSaleProductLabel;
import org.springcenter.product.api.dto.*;
import org.springcenter.background.modules.service.IAfterSaleService;
import org.springcenter.background.modules.util.EsUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/6/25 16:33
 */
@Service
@Slf4j
@RefreshScope
public class IAfterSaleServiceImpl implements IAfterSaleService {

    @Value(value = "${after.sale.index}")
    private String afterSaleIndex;

    @Value(value = "${after.sale.quantify.index}")
    private String afterSaleQuantifyIndex;

    @Autowired
    private EsUtil esUtil;

    @Autowired
    private AfterSaleProductLabelMapper afterSaleProductLabelMapper;

    @Value("${after.sale.tag.leaf.tag}")
    private String afterSaleTagLeafTag;

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Override
    public AfterSalesResp searchAfterSaleInfo(String requestData) {
        if (StringUtils.isBlank(requestData)) {
            throw new RuntimeException("入参款号信息不能为空");
        }

        // 根据款号查询纽扣信息
        List<AfterSalesEsResp> entities = getAfterSalesEsResps(requestData);

        // 根据款号查询售后质量信息
        List<AfterSalesQuantifyEsResp> quantifyEsResps = getAfterSalesQuantifyResps(requestData);


        return buildAfterSalesInfoRet(entities, quantifyEsResps);
    }

    @Override
    public Boolean addSkcSaleTagInfo(List<AfterSalesAddTagReq> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            return true;
        }

        // 删除的需要单独判断
        if (requestData.size() == 1 && StringUtils.isBlank(requestData.get(0).getSecondLabelCode())) {
            afterSaleProductLabelMapper.updateByProInfo(requestData.get(0).getProductId(),
                    requestData.get(0).getFirstLabelCode(), requestData.get(0).getSkcCode());
        } else {
            // 校验参数 每个二级下只能填写一个
            Map<String, List<AfterSalesAddTagReq>> listMap = requestData.stream()
                    .collect(Collectors.groupingBy(AfterSalesAddTagReq::getSecondLabelCode));
            if (MapUtils.isEmpty(listMap)) {
                throw new ProductException("入参不能为空");
            }
            listMap.forEach((k, v) -> {
                if (CollectionUtils.isNotEmpty(v) && v.size() > 1) {
                    throw new ProductException("二级标签下只能填写一个");
                }
            });


            List<AfterSaleProductLabel> inserts = new ArrayList<>();
            requestData.forEach(v -> {
                AfterSaleProductLabel productLabel = new AfterSaleProductLabel();
                BeanUtils.copyProperties(v, productLabel);
                productLabel.setIsDeleted(0);
                productLabel.setId(IdLeaf.getDateId(afterSaleTagLeafTag));
                productLabel.setCreateTime(new Date());
                productLabel.setUpdateTime(new Date());
                // 手动
                productLabel.setType(1);
                inserts.add(productLabel);
            });

            template.execute(action -> {
                afterSaleProductLabelMapper.updateByProInfo(requestData.get(0).getProductId(),
                        requestData.get(0).getFirstLabelCode(), requestData.get(0).getSkcCode());
                afterSaleProductLabelMapper.batchInsert(inserts);
                return action;
            });

        }

        return true;
    }

    @Override
    public List<AfterSalesTagResp> searchSkcSaleTagInfo(String requestData) {
        if (StringUtils.isBlank(requestData)) {
            throw new RuntimeException("入参商品id信息不能为空");
        }
        List<AfterSaleProductLabel> labels = afterSaleProductLabelMapper.selectByProductId(requestData);
        if (CollectionUtils.isEmpty(labels)) {
            return Collections.emptyList();
        }

        Map<String, List<AfterSaleProductLabel>> map = labels.stream().collect(Collectors.groupingBy(AfterSaleProductLabel::getSkcCode));
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyList();
        }

        List<AfterSalesTagResp> rets = new ArrayList<>();
        map.forEach((k, v) -> {
            AfterSalesTagResp ret = new AfterSalesTagResp();
            ret.setSkcCode(k);
            if (CollectionUtils.isEmpty(v)) {
                return;
            }
            ret.setName(v.get(0).getName());
            ret.setColorCode(StringUtils.substring(k, 9, 12));
            ret.setProductId(v.get(0).getProductId());
            List<AfterSalesTagResp.TagData> tagDataList = new ArrayList<>();
            v.forEach(v1 -> {
                AfterSalesTagResp.TagData data = new AfterSalesTagResp.TagData();
                data.setFirstLabelCode(v1.getFirstLabelCode());
                data.setSecondLabelCode(v1.getSecondLabelCode());
                data.setThirdLabelCode(v1.getThirdLabelCode());
                tagDataList.add(data);
            });
            ret.setTagDataList(tagDataList);
            rets.add(ret);
        });
        return rets;
    }

    private List<AfterSalesQuantifyEsResp> getAfterSalesQuantifyResps(String requestData) {
        SearchRequest request = new SearchRequest();
        request.indices(afterSaleQuantifyIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("style_id", requestData));

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(100);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品售后质量信息-ES {}", request.source().toString());
        List<AfterSalesQuantifyEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return null;
            }

            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                AfterSalesQuantifyEsResp entity = AfterSalesQuantifyEsResp.fromJson(hit.getSourceAsString(), AfterSalesQuantifyEsResp.class);
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品售后质量信息ES异常e = {}", e.getMessage());
            return null;
        }
        return entities;
    }

    private List<AfterSalesEsResp> getAfterSalesEsResps(String requestData) {
        SearchRequest request = new SearchRequest();
        request.indices(afterSaleIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("style_id", requestData));
        queryBuilder.must(QueryBuilders.termQuery("is_invalid", 1));

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(100);


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品售后信息-ES {}", request.source().toString());
        List<AfterSalesEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return null;
            }

            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                AfterSalesEsResp entity = AfterSalesEsResp.fromJson(hit.getSourceAsString(), AfterSalesEsResp.class);
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品售后信息ES异常e = {}", e.getMessage());
            return null;
        }
        return entities;
    }

    private AfterSalesResp buildAfterSalesInfoRet(List<AfterSalesEsResp> entities,
                                                  List<AfterSalesQuantifyEsResp> quantifyEsResps) {
        if (CollectionUtils.isEmpty(entities) && CollectionUtils.isEmpty(quantifyEsResps)) {
            return null;
        }

        AfterSalesResp ret = new AfterSalesResp();
        if (CollectionUtils.isNotEmpty(entities)) {
            Map<String, List<AfterSalesEsResp>> map = entities.stream().filter(v -> Objects.nonNull(v.getBomcode()))
                    .sorted(Comparator.comparing(AfterSalesEsResp::getBomcode))
                    .collect(Collectors.groupingBy(AfterSalesEsResp::getSty_color_id));
            if (MapUtils.isEmpty(map)) {
                return ret;
            }
            ret.setProductId(entities.get(0).getProduct_id());
            ret.setRequirements(entities.get(0).getReqtext_list());
            ret.setName(entities.get(0).getStyle_id());
            List<AfterSalesResp.SkcInfoData> skcInfoData = new ArrayList<>();

            map.entrySet().forEach(data -> {
                AfterSalesResp.SkcInfoData skcData = new AfterSalesResp.SkcInfoData();
                skcData.setColorCode(data.getKey());
                skcData.setColorName(data.getValue().get(0) == null ? "" : data.getValue().get(0).getSty_color_name());
                List<AfterSalesResp.ButtonInfo> buttonInfos = new ArrayList<>();
                List<AfterSalesResp.DosingInfo> dosingInfos = new ArrayList<>();
                List<AfterSalesResp.AccessoryInfo> accessoryInfos = new ArrayList<>();
                data.getValue().forEach(entity -> {
                    // 0-主料 1辅料 2配料
                    if (Objects.equals(entity.getIs_button(), 1)) {
                        AfterSalesResp.ButtonInfo buttonInfo = new AfterSalesResp.ButtonInfo();
                        buttonInfo.setClassifyCode(entity.getClassify_small());
                        buttonInfo.setButtonNum(new BigDecimal(entity.getDh()).intValue());
                        buttonInfo.setBomName(entity.getButton_name());
                        buttonInfo.setBomCode(entity.getBomcode());
                        buttonInfos.add(buttonInfo);
                    }

                    if (Objects.equals(entity.getDtype(), 0)) {
                        skcData.setMainBomCode(entity.getBomcode());
                    } else if (Objects.equals(entity.getDtype(), 1)) {
                        AfterSalesResp.DosingInfo dosingInfo = new AfterSalesResp.DosingInfo();
                        dosingInfo.setBomName(entity.getBomname());
                        dosingInfo.setGg(entity.getGg());
                        dosingInfo.setDw(entity.getDh());
                        dosingInfo.setBw(entity.getBw());
                        dosingInfo.setDh(entity.getDh());
                        dosingInfo.setBomCode(entity.getBomcode());
                        dosingInfos.add(dosingInfo);
                    } else {
                        AfterSalesResp.AccessoryInfo info = new AfterSalesResp.AccessoryInfo();
                        info.setNum(entity.getDtype_name());
                        info.setBw(entity.getBw());
                        info.setBomCode(entity.getBomcode());
                        info.setBomName(entity.getBomname());
                        accessoryInfos.add(info);
                    }
                });
                skcData.setButtonInfos(buttonInfos.stream().distinct().collect(Collectors.toList()));
                skcData.setDosingInfos(dosingInfos.stream().distinct().collect(Collectors.toList()));
                skcData.setAccessoryInfos(accessoryInfos.stream().distinct()
                        .sorted(Comparator.comparing(AfterSalesResp.AccessoryInfo::getNum)).collect(Collectors.toList()));
                skcInfoData.add(skcData);
            });

            ret.setSkcInfoDataList(skcInfoData);
        }

        if (CollectionUtils.isNotEmpty(quantifyEsResps)) {
            Map<String, AfterSalesQuantifyEsResp> map = quantifyEsResps.stream()
                    .collect(HashMap::new, (k, v) -> k.put(v.getSty_color_id(), v), HashMap::putAll);
            if (MapUtils.isEmpty(map)) {
                return ret;
            }
            // 塞默认spu的info
            ret.setSampleCat(quantifyEsResps.get(0).getSample_cat());
            ret.setSampleCode(quantifyEsResps.get(0).getSample_code());
            ret.setDesignType4(quantifyEsResps.get(0).getDesign_type4());
            ret.setPlanMarketTime(quantifyEsResps.get(0).getDate_list());
            ret.setSupplyShutName(quantifyEsResps.get(0).getSupply_shutname());
            ret.setStInDate(quantifyEsResps.get(0).getSt_indate());
            ret.setUpDept(quantifyEsResps.get(0).getUpdept());


            if (CollectionUtils.isEmpty(ret.getSkcInfoDataList())) {
                List<AfterSalesResp.SkcInfoData> rets = new ArrayList<>();
                map.forEach((k, v) -> {
                    AfterSalesResp.SkcInfoData skcData = new AfterSalesResp.SkcInfoData();
                    AfterSalesResp.SkcInfoData skcInfoData = buildRet(k, v, skcData);
                    rets.add(skcInfoData);
                });
                ret.setSkcInfoDataList(rets);
            } else {
                ret.getSkcInfoDataList().forEach(data -> {
                    AfterSalesQuantifyEsResp resp = map.get(data.getColorCode());
                    if (resp == null) {
                        return;
                    }
                    buildRet(data.getColorCode(), resp, data);

                });
            }
        }

        return ret;
    }

    private static AfterSalesResp.SkcInfoData buildRet(String k, AfterSalesQuantifyEsResp v, AfterSalesResp.SkcInfoData skcData) {

        skcData.setColorCode(k);
        skcData.setColorName(v == null ? "" : v.getSty_color_name());

        // ------------面料部分信息 ------------------------------
        AfterSalesResp.FabricInfo fabricInfos = new AfterSalesResp.FabricInfo();
        if (StringUtils.isNotBlank(v.getBomcode())) {
            fabricInfos.setBomCode(v.getBomcode() + "-" + k);
        }
        if (StringUtils.isNotBlank(v.getSupply_shutname())) {
            fabricInfos.setFabricComposition(v.getSupply_shutname());
        }
        if (StringUtils.isNotBlank(v.getBomname())) {
            fabricInfos.setFabricComposition(v.getBomname());
        }
        if (StringUtils.isNotBlank(v.getSc_popular())) {
            fabricInfos.setSupplier(v.getSc_popular());
        }
        if (StringUtils.isNotBlank(v.getSc_riskassessment())) {
            fabricInfos.setRiskAssessment(v.getSc_riskassessment());
        }
        skcData.setFabricInfo(fabricInfos);

        // ------------成衣部分信息 ------------------------------
        AfterSalesResp.GarmentInfo garmentInfos = new AfterSalesResp.GarmentInfo();
        if (StringUtils.isNotBlank(v.getCy_in_ku())) {
            garmentInfos.setGarmentInSuggestion(v.getCy_in_ku());
        }
        if (StringUtils.isNotBlank(v.getCych())) {
            garmentInfos.setGarmentCheck(v.getCych());
        }
        if (StringUtils.isNotBlank(v.getCy_jcjg())) {
            garmentInfos.setGarmentInteriorInspection(v.getCy_jcjg());
        }
        if (StringUtils.isNotBlank(v.getCy_wj_jcjg())) {
            garmentInfos.setGarmentExternalInspection(v.getCy_wj_jcjg());
        }
        if (StringUtils.isNotBlank(v.getMlch())) {
            garmentInfos.setFabricCheck(v.getMlch());
        }
        if (StringUtils.isNotBlank(v.getUnquantify_sug())) {
            garmentInfos.setGarmentUnqualifiedSuggestion(v.getUnquantify_sug());
        }
        skcData.setGarmentInfo(garmentInfos);


        // ------------原料部分信息 ------------------------------
        AfterSalesResp.CoatInfo coatInfos = new AfterSalesResp.CoatInfo();
        if (StringUtils.isNotBlank(v.getYl_dh_jcjg())) {
            coatInfos.setInternalInspection(v.getYl_dh_jcjg());
        }
        if (StringUtils.isNotBlank(v.getYl_sug())) {
            coatInfos.setUnQualifiedSuggestion(v.getYl_sug());
        }
        if (StringUtils.isNotBlank(v.getYl_qq_jcjg())) {
            coatInfos.setPreliminaryInspection(v.getYl_qq_jcjg());
        }
        if (StringUtils.isNotBlank(v.getRstsub_fault())) {
            coatInfos.setExternalInspection(v.getRstsub_fault());
        }
        skcData.setCoatInfo(coatInfos);

        return skcData;
    }
}
