<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.ProductSizeInfoSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.ProductSizeInfoSetting">
        <id column="ID" property="id" />
        <result column="SETTING_ID" property="settingId" />
        <result column="SETTING_NAME" property="settingName" />
        <result column="TYPE" property="type" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SETTING_ID, SETTING_NAME, TYPE, CREATE_TIME, UPDATE_TIME, IS_DELETED
    </sql>
    <select id="selectBrandAndSmallClassInfo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM PRODUCT_SIZE_INFO_SETTING
        WHERE SETTING_ID in (#{brandId}, #{smallClassId})
        AND IS_DELETED = 0
    </select>

    <select id="selectBrandIdAndCategory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM PRODUCT_SIZE_INFO_SETTING
        WHERE IS_DELETED = 0
        <if test="brandIds != null and brandIds.size() > 0">
            and SETTING_ID in
            <foreach collection="brandIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        UNION ALL
        SELECT <include refid="Base_Column_List"></include>
        FROM PRODUCT_SIZE_INFO_SETTING
        WHERE IS_DELETED = 0 AND TYPE = 1
    </select>

</mapper>