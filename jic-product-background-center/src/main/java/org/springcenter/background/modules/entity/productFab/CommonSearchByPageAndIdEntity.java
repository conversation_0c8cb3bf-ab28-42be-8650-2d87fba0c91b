package org.springcenter.background.modules.entity.productFab;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2024/1/19 13:23
 */
@Data
public class CommonSearchByPageAndIdEntity {

    private Integer i;

    private Integer pageSize;

    private Integer minNum;

    private Integer maxNum;

    private String index;

    public static CommonSearchByPageAndIdEntity build(Integer minNum, int i, Integer maxNum,
                                                      int pageSize, String fabAutoNameInfoIndex) {
        CommonSearchByPageAndIdEntity entity = new CommonSearchByPageAndIdEntity();
        entity.setI(i);
        entity.setIndex(fabAutoNameInfoIndex);
        entity.setPageSize(pageSize);
        entity.setMaxNum(maxNum);
        entity.setMinNum(minNum);
        return entity;
    }
}
