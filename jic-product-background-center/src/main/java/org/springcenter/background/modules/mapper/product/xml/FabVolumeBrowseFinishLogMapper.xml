<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.FabVolumeBrowseFinishLogMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.FabVolumeBrowseFinishLog">
        <id column="ID" property="id" />
        <result column="FAB_VOLUME_ID" property="fabVolumeId" />
        <result column="C_STORE_ID" property="cStoreId" />
        <result column="STORE_BRAND_ID" property="storeBrandId" />
        <result column="HR_ID" property="hrId" />
        <result column="TASK_ITEM_ID" property="taskItemId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="CAMPAIGN_ID" property="campaignId" />
        <result column="SEND_TYPE" property="sendType" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, FAB_VOLUME_ID, C_STORE_ID, STORE_BRAND_ID, HR_ID, TASK_ITEM_ID, CREATE_TIME, UPDATE_TIME,
        IS_DELETED, CAMPAIGN_ID, SEND_TYPE
    </sql>

    <update id="updateByIds">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update FAB_VOLUME_BROWSE_FINISH_LOG
            set
            SEND_TYPE = 1,
            UPDATE_TIME = sysdate
            where ID = #{item.id}
        </foreach>
    </update>

    <select id="judgeIsFinish" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM FAB_VOLUME_BROWSE_FINISH_LOG
        WHERE IS_DELETED = 0 AND C_STORE_ID = #{storeId}
        AND HR_ID = #{hrId} AND FAB_VOLUME_ID = #{fabVolumeId}
    </select>

    <select id="selectNotPush" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM FAB_VOLUME_BROWSE_FINISH_LOG
        WHERE IS_DELETED = 0 AND SEND_TYPE = 0
    </select>


</mapper>
