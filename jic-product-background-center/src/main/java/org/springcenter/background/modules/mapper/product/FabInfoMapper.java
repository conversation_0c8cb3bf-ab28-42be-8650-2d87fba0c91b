package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.FabInfo;

import java.util.Date;
import java.util.List;


public interface FabInfoMapper extends BaseMapper<FabInfo> {


    void batchUpdateByIds(@Param("list") List<FabInfo> v);

    void batchInsert(@Param("list") List<FabInfo> v);

    List<String> selectNames(@Param("start") Date start, @Param("end") Date end);
}
