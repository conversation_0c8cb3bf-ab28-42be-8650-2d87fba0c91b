package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Preconditions;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.common.system.vo.DictModel;
import com.jnby.authority.common.system.vo.LinkDinkTalkMessage;
import com.jnby.authority.common.util.DateUtils;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.background.modules.enums.OriginLibraryStorageEnum;
import org.springcenter.background.modules.mapper.product.OriginLibraryLenderLogMapper;
import org.springcenter.background.modules.mapper.product.OriginLibraryMapper;
import org.springcenter.background.modules.model.product.DetailOriginLibrary;
import org.springcenter.background.modules.model.product.OriginLibrary;
import org.springcenter.background.modules.model.product.OriginLibraryLenderLog;
import org.springcenter.background.modules.service.IOriginLibraryLenderLogService;
import org.springcenter.background.modules.service.IOriginLibraryService;
import org.springcenter.background.modules.util.DateUtil;
import org.springcenter.product.api.dto.back.LendOriginLibraryReq;
import org.springcenter.product.api.dto.back.ReturnOriginLibraryReq;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OriginLibraryServiceImpl extends ServiceImpl<OriginLibraryMapper, OriginLibrary> implements IOriginLibraryService {

    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    private IOriginLibraryLenderLogService iOriginLibraryLenderLogService;

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Override
    public void lendOriginLibrary(LendOriginLibraryReq lendOriginLibraryReq) {
        List<OriginLibrary> originLibrarys = baseMapper.selectBatchIds(lendOriginLibraryReq.getOriginLibraryIds());
        List<OriginLibraryLenderLog> originLibraryLenderLogs = new ArrayList<>();
        originLibrarys.forEach(originLibrary -> {
            Preconditions.checkArgument(Objects.nonNull(originLibrary), "原样库不存在: " + originLibrary.getSampleNum());
            Preconditions.checkArgument(originLibrary.getStorageIn().equals("1"), "该原样已被出借：" + originLibrary.getSampleNum());

            //插入借用记录
            String id = IdLeaf.getId("ORIGIN_LIBRARY_LENDER_LOG");
            OriginLibraryLenderLog originLibraryLenderLog = new OriginLibraryLenderLog();
            originLibraryLenderLog.setId(id);
            originLibraryLenderLog.setOriginLibraryId(originLibrary.getId());
            originLibraryLenderLog.setLenderName(lendOriginLibraryReq.getUsername());
            originLibraryLenderLog.setLenderPhone(lendOriginLibraryReq.getPhone());
            originLibraryLenderLog.setReturnTime(lendOriginLibraryReq.getReturnTime());
            originLibraryLenderLog.setCreateBy(lendOriginLibraryReq.getUsername());
            originLibraryLenderLog.setCreateTime(new java.util.Date());
            originLibraryLenderLogs.add(originLibraryLenderLog);
        });

        template.executeWithoutResult(status -> {
            LambdaUpdateWrapper<OriginLibrary> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(OriginLibrary::getId, originLibrarys.stream().map(OriginLibrary::getId).toArray());
            updateWrapper.set(OriginLibrary::getStorageIn, "0");
            updateWrapper.set(OriginLibrary::getCurLenderPhone, lendOriginLibraryReq.getPhone());
            updateWrapper.set(OriginLibrary::getCurLender, lendOriginLibraryReq.getUsername());
            updateWrapper.setSql("LENDER_NUM = LENDER_NUM + 1");
            updateWrapper.set(OriginLibrary::getReturnTime, Optional.ofNullable(lendOriginLibraryReq.getReturnTime()).orElse(null));
            updateWrapper.set(OriginLibrary::getUpdateBy, lendOriginLibraryReq.getUsername());
            updateWrapper.set(OriginLibrary::getUpdateTime, new Date());
            baseMapper.update(null, updateWrapper);
            iOriginLibraryLenderLogService.saveBatch(originLibraryLenderLogs);
        });
    }

    @Override
    public void returnOriginLibrary(ReturnOriginLibraryReq returnOriginLibraryReq) {
        List<OriginLibrary> originLibrarys = baseMapper.selectBatchIds(returnOriginLibraryReq.getOriginLibraryIds());
        originLibrarys.forEach(originLibrary -> {
            Preconditions.checkArgument(Objects.nonNull(originLibrary), "原样库不存在: " + originLibrary.getSampleNum());
            Preconditions.checkArgument(originLibrary.getStorageIn().equals("0"), "原样库未被出借: " + originLibrary.getSampleNum());
            //Preconditions.checkArgument(StringUtils.equals(originLibrary.getCurLenderPhone(), returnOriginLibraryReq.getPhone()), "当前借用人与归还人手机号不匹配");
        });

        template.executeWithoutResult(status -> {
            LambdaUpdateWrapper<OriginLibrary> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(OriginLibrary::getId, originLibrarys.stream().map(OriginLibrary::getId).toArray());
            updateWrapper.set(OriginLibrary::getStorageIn, "1");
            updateWrapper.set(OriginLibrary::getCurLenderPhone, "");
            updateWrapper.set(OriginLibrary::getCurLender, "");
            updateWrapper.setSql("RETURN_TIME = NULL");
            updateWrapper.set(OriginLibrary::getUpdateBy, originLibrarys.get(0).getCurLender());
            updateWrapper.set(OriginLibrary::getUpdateTime, new Date());
            baseMapper.update(null, updateWrapper);
        });
    }

    @Override
    public List<OriginLibrary> batchGetOriginLibrary(List<String> originLibraryIds) {
        LambdaQueryWrapper<OriginLibrary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OriginLibrary::getId, originLibraryIds);
        List<OriginLibrary> originLibraryList = baseMapper.selectList(queryWrapper);
        List<DictModel> smallCateList = sysBaseAPI.queryDictItemsByCode("orig_library_sm_cate");
        originLibraryList.forEach(originLibrary -> {
            smallCateList.forEach(item -> {
                if(item.getValue().equals(originLibrary.getSmallCate())){
                    originLibrary.setSmallCate(item.getText());
                }
            });
        });

        return originLibraryList;
    }

    @Override
    public List<OriginLibrary> getOriginLibraryByLender(String lender) {
        LambdaQueryWrapper<OriginLibrary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OriginLibrary::getCurLenderPhone, lender);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void sendMsgOriginLibraryByOverdue() {
        LambdaQueryWrapper<OriginLibrary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OriginLibrary::getStorageIn, OriginLibraryStorageEnum.OUT.getCode());
        queryWrapper.lt(OriginLibrary::getReturnTime, DateUtil.addDate(new Date(), 7));
        List<OriginLibrary> originLibrarys = baseMapper.selectList(queryWrapper);
        //将集合originLibrarys按照归还人手机号进行去重
        Map<String, List<OriginLibrary>> maps = originLibrarys.stream().collect(Collectors.groupingBy(OriginLibrary::getCurLenderPhone));
        List<OriginLibrary> result = maps.values().stream().map(list -> list.get(0)).collect(Collectors.toList());
        for (OriginLibrary originLibrary : result) {
            LinkDinkTalkMessage linkDinkTalkMessage = new LinkDinkTalkMessage();
            linkDinkTalkMessage.setTitle("原样借用到期通知");
            //判断归还时间和当前时间的差值
            int diffDay = DateUtil.diffDate(originLibrary.getReturnTime(), new Date());
            String content = "您借用的原样%s，请尽快归还；点击查看借用原样详情；";
            linkDinkTalkMessage.setContent(String.format(content, diffDay <=0 ? "已到期" : "还剩"+diffDay + "天到期"));
            linkDinkTalkMessage.setToUser(originLibrary.getCurLenderPhone());
            linkDinkTalkMessage.setLinkUrl("https://retail.jnby.com/webapp/og-sample-library/index");
            linkDinkTalkMessage.setPicUrl("@lALOACZwe2Rk");
            try {
                sysBaseAPI.sendDingTalkLinkMsg(linkDinkTalkMessage);
            } catch (Exception e) {
                log.error("发送钉钉消息失败", e);
            }

        }

    }

    @Override
    public void updateOriginLibraryByOverdue() {
        LambdaQueryWrapper<OriginLibrary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OriginLibrary::getStorageIn, "0");
        //归还时间小于当前时间
        queryWrapper.lt(OriginLibrary::getReturnTime, new java.util.Date());
        List<OriginLibrary> originLibrarys = baseMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(originLibrarys)) return;
        OriginLibrary originLibrary = new OriginLibrary();
        originLibrary.setIsDelay(1);
        originLibrary.setUpdateTime(new Date());
        LambdaUpdateWrapper<OriginLibrary> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(OriginLibrary::getId, originLibrarys.stream().map(OriginLibrary::getId).toArray());
        baseMapper.update(originLibrary, updateWrapper);
    }

    @Override
    public DetailOriginLibrary getById(String id) {
        OriginLibrary originLibrary = baseMapper.selectById(id);
        sysBaseAPI.queryDictItemsByCode("orig_library_type").forEach(item -> {
            if(item.getValue().equals(originLibrary.getOriginType())){
                originLibrary.setOriginType(item.getText());
            }
        });
        sysBaseAPI.queryDictItemsByCode("orig_library_big_cate").forEach(item -> {
            if(item.getValue().equals(originLibrary.getBigCate())){
                originLibrary.setBigCate(item.getText());
            }
        });
        sysBaseAPI.queryDictItemsByCode("orig_library_sm_cate").forEach(item -> {
            if(item.getValue().equals(originLibrary.getSmallCate())){
                originLibrary.setSmallCate(item.getText());
            }
        });
        sysBaseAPI.queryDictItemsByCode("orig_library_brand").forEach(item -> {
            if(item.getValue().equals(originLibrary.getInnerBrand())){
                originLibrary.setInnerBrand(item.getText());
            }
        });
        //批量查询所有的分类字典子集
        List<String> pidCodes = Arrays.asList("B05-A21-A02", "B05-A21-A01", "B05-A21-A05", "B05-A21-A06", "B05-A21-A04", "B05-A27-A02", "B05-A25-A02");
        sysBaseAPI.queryChildDSysCateByPCodes(pidCodes).forEach(item -> {
            if(item.getId().equals(originLibrary.getFit())){
                originLibrary.setFit(item.getName());
            }
            if(item.getId().equals(originLibrary.getBodyStyle())){
                originLibrary.setBodyStyle(item.getName());
            }
            if(item.getId().equals(originLibrary.getCollar())){
                originLibrary.setCollar(item.getName());
            }
            if(item.getId().equals(originLibrary.getSleeve())){
                originLibrary.setSleeve(item.getName());
            }
            if(item.getId().equals(originLibrary.getSleeveLen())){
                originLibrary.setSleeveLen(item.getName());
            }
            if(item.getId().equals(originLibrary.getColor())){
                originLibrary.setColor(item.getName());
            }
            if(item.getId().equals(originLibrary.getAppearance())){
                originLibrary.setAppearance(item.getName());
            }
        });

        DetailOriginLibrary detailOriginLibrary = new DetailOriginLibrary();
        BeanUtils.copyProperties(originLibrary, detailOriginLibrary);
        if(originLibrary.getCurLenderPhone() != null && !"".equals(originLibrary.getCurLenderPhone())){
            LambdaQueryWrapper<OriginLibraryLenderLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OriginLibraryLenderLog::getLenderPhone, originLibrary.getCurLenderPhone());
            queryWrapper.eq(OriginLibraryLenderLog::getOriginLibraryId, originLibrary.getId());
            List<OriginLibraryLenderLog> originLibraryLenderLogs = iOriginLibraryLenderLogService.list(queryWrapper);
            if(originLibraryLenderLogs.size() > 0){
                detailOriginLibrary.setLenderTime(originLibraryLenderLogs.get(0).getCreateTime());
                if(detailOriginLibrary.getIsDelay() == 1){
                    detailOriginLibrary.setDelayDays(DateUtil.diffDate(new Date(), originLibrary.getReturnTime()));
                }
            }
        }
        return detailOriginLibrary;
    }
}
