package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.modules.mapper.bojun.CStoreMapper;
import org.springcenter.background.modules.mapper.product.FabVolumeInfoMapper;
import org.springcenter.background.modules.mapper.product.UserTaskMapper;
import org.springcenter.background.modules.model.bojun.CStore;
import org.springcenter.background.modules.model.product.FabVolumeInfo;
import org.springcenter.background.modules.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date:2024/3/19 14:09
 */
@Service
@Slf4j
public class TestServiceImpl implements TestService {

    @Autowired
    private FabVolumeInfoMapper fabVolumeInfoMapper;

    @Autowired
    private UserTaskMapper userTaskMapper;

    @Autowired
    private CStoreMapper cStoreMapper;

    @Override
    public void testDataBase() {

        log.info("===================商品库存数据========================");
        FabVolumeInfo ret1 = fabVolumeInfoMapper.selectById("147");
        log.info("===================商品数据库存ret========================{}", JSONObject.toJSONString(ret1));

        log.info("===================伯俊数据========================");
        CStore ret2 = cStoreMapper.selectById(24113L);
        log.info("===================伯俊数据ret========================{}", JSONObject.toJSONString(ret2));

        log.info("===================任务库数据========================");
        String id = userTaskMapper.selectByIdInData(3105L);
        log.info("===================任务库数据ret========================{}", JSONObject.toJSONString(id));


    }
}
