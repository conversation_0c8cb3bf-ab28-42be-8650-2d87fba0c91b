package org.springcenter.background.modules.entity.productFab;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springcenter.background.modules.enums.AutoNameEnum;
import org.springcenter.background.modules.model.product.FabAnDynamicFieldSetting;
import org.springcenter.background.modules.model.product.FabAutoNameSetting;
import org.springcenter.product.api.dto.FabSpuForAutoNameEsResp;
import org.springcenter.product.api.dto.FabSpuForAutoNameResp;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @Date:2024/1/17 15:44
 */
@Data
public class CommonAutoNameGenerateEntity {

    private List<FabAutoNameSetting> partSortedList;

    private List<FabSpuForAutoNameResp> partList;

    private List<FabSpuForAutoNameResp> wholeList;

    private Integer[] charLen;

    private AtomicReference<String> dpp;

    private AtomicReference<String> designName;

    private List<FabAnDynamicFieldSetting> fabAnDynamicFieldSettings;

    private List<String> forbiddenList;

    private FabSpuForAutoNameEsResp resp;

    @ApiModelProperty(value = "字符个数")
    private Integer size;

    @ApiModelProperty(value = "类型")
    private AutoNameEnum autoNameEnum;

    @ApiModelProperty(value = "当季 往季标识")
    private String isInCycle;
}
