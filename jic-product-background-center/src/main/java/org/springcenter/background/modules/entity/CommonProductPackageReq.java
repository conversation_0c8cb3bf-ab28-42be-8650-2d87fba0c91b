package org.springcenter.background.modules.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springcenter.product.api.dto.background.AddProductPackageReq;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/7/26 17:04
 */
@Data
public class CommonProductPackageReq {

    @ApiModelProperty(value = "是否有条件筛选 0:无筛选条件 1:有筛选条件")
    private Integer hasFilter;

    @ApiModelProperty(value = "是否名单导入  0:无名单导入 1:有名单导入")
    private Integer hasImport;

    @ApiModelProperty(value = "生效类型 0长期有效 1定期有效")
    private Integer validType;

    @ApiModelProperty(value = "开始时间 格式：yyyy-MM-dd HH:mm:ss")
    private String startDate;

    @ApiModelProperty(value = "结束时间 格式：yyyy-MM-dd HH:mm:ss")
    private String endDate;

    @ApiModelProperty(value = "筛选条件")
    private List<AddProductPackageReq.FilterInfoData> filterInfoDataList;

    @ApiModelProperty(value = "填写条件")
    private List<AddProductPackageReq.ImportInfoData> importInfoDataList;
}
