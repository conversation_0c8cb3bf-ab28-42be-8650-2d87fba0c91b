package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2025/1/8 14:54
 */
@Data
@TableName(value = "PRODUCT_ENV_FABRIC_INFO")
public class ProductEnvFabricInfo {

    @TableId
    private String id;

    @TableField(value = "PRODUCT_ID")
    private Long productId;

    @TableField(value = "NAME")
    private String name;

    @TableField(value = "ENV_FABRIC_INFO")
    private String envFabricInfo;

    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    @TableField(value = "SKC_NO")
    private String skcNo;
}
