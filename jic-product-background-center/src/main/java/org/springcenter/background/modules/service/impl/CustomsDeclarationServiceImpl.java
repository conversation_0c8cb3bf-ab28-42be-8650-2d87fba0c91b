package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.background.modules.mapper.product.CustomsDeclarationMapper;
import org.springcenter.background.modules.model.product.CustomsDeclaration;
import org.springcenter.background.modules.model.product.DisplayBookInfo;
import org.springcenter.background.modules.service.CustomsDeclarationService;
import org.springcenter.background.modules.service.LianXiangYunFilezService;
import org.springcenter.product.api.constant.IdConstant;
import org.springcenter.product.api.dto.CustomsDeclarationReq;
import org.springcenter.product.api.dto.CustomsDeclarationResp;
import org.springcenter.product.api.dto.DisplayBookListQueryResp;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.api.lenovo.LenovoFileReq;
import org.springcenter.product.api.lenovo.LenovoFileResp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomsDeclarationServiceImpl implements CustomsDeclarationService {

    @Autowired
    private LianXiangYunFilezService lianXiangYunFilezService;


    @Autowired
    private CustomsDeclarationMapper customsDeclarationMapper;





    @Override
    public String importReportPortToDatabase(String filePath) {
        int i = 0;
        boolean firstFlag = true;
        out:while (firstFlag) {
            LenovoFileResp resp = getLenovoFileResp(filePath, i);
            if (resp == null || CollectionUtils.isEmpty(resp.getFileModelList())) {
                log.info("importReportPortToDatabase = 无数据  结束");
                break out;
            }
            readLenovoFileAndInsertDatabase(resp);
            i++;
        }
        return "成功";
    }

    @Override
    public List<CustomsDeclarationResp> list(CustomsDeclarationReq requestData, Page page) {
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<CustomsDeclaration> customsDeclarations = customsDeclarationMapper.selectByParams(requestData);
        PageInfo<CustomsDeclaration> pageInfo = new PageInfo(hPage);
        pageInfo.setList(customsDeclarations);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return JSONObject.parseArray(JSONObject.toJSONString(pageInfo.getList()),CustomsDeclarationResp.class);
    }

    @Override
    public void deleteById(String id) {
        if(StringUtils.isBlank(id)){
            return ;
        }
        CustomsDeclaration update = new CustomsDeclaration();
        update.setId(id);
        update.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
        customsDeclarationMapper.updateById(update);
    }

    @Override
    public List<CustomsDeclarationResp> batchGetByNames(List<String> requestData) {
        if(CollectionUtils.isEmpty(requestData)){
            return new ArrayList<>();
        }

        List<CustomsDeclaration> customsDeclarations = customsDeclarationMapper.selectByNames(requestData);
        return JSONObject.parseArray(JSONObject.toJSONString(customsDeclarations),CustomsDeclarationResp.class);
    }

    private void readLenovoFileAndInsertDatabase(LenovoFileResp resp) {
        if(CollectionUtils.isEmpty(resp.getFileModelList())){
            return ;
        }
        for (LenovoFileResp.LenovoFileModel lenovoFileModel : resp.getFileModelList()) {
            int pageNo = 0;
            if(lenovoFileModel.getDir()){
                while (true){
                    LenovoFileResp lenovoFileResp = getLenovoFileResp(lenovoFileModel.getPath(), pageNo);
                    if (lenovoFileResp == null || CollectionUtils.isEmpty(lenovoFileResp.getFileModelList())) {
                        break ;
                    }
                    readLenovoFileAndInsertDatabase(lenovoFileResp);
                    pageNo ++;
                }
            }else{
                // 进行处理数据
                dealCustomsDeclarationData(lenovoFileModel);
            }
        }
    }

    private void dealCustomsDeclarationData(LenovoFileResp.LenovoFileModel lenovoFileModel) {
        try {
            //处理数据  上层文件夹  获取 货季  本层文件夹 获取文件名， 如果neid和nsid相同并且在数据库中存在，并且名称不同  那么进行更新
            String path = lenovoFileModel.getPath();
            // filename  文件名称
            String fileName = path.substring(path.lastIndexOf("/") + 1);
            // 获取上层文件夹
            String fileDirPath = path.substring(0, path.lastIndexOf("/"));
            // 获取上层文件夹名称
            String fileDirName = fileDirPath.substring(fileDirPath.lastIndexOf("/") + 1);
            // 处理数据
            // 处理数据  货季
            String goodsSeason = fileDirName.substring(0,4);
            fileName = fileName.split("\\.")[0];
            // 根据neid和nsid查询数据 如果存在 并且名称不同 则进行更新操作  其他则为插入数据
            List<CustomsDeclaration> list = customsDeclarationMapper.selectByNeIdAndNsid(lenovoFileModel.getNeid(),lenovoFileModel.getNsid());
            if(CollectionUtils.isNotEmpty(list)){
                if(fileName.equals(list.get(0).getName())){
                    return ;
                }
                // 更新
                CustomsDeclaration update = new CustomsDeclaration();
                update.setId(list.get(0).getId());
                update.setName(fileName);
                update.setUpdateTime(new Date());
                customsDeclarationMapper.updateById(update);
                return ;
            }
            // 插入数据
            CustomsDeclaration insert = new CustomsDeclaration();
            insert.setId(IdLeaf.getId(IdConstant.CUSTOMS_DECLARATION));
            insert.setNeid(lenovoFileModel.getNeid());
            insert.setNsid(lenovoFileModel.getNsid());
            insert.setSeason(goodsSeason);
            insert.setName(fileName);
            insert.setcreateTime(new Date());
            insert.setUpdateTime(new Date());
            insert.setIsDel(IsDeleteEnum.NORMAL.getCode());
            customsDeclarationMapper.insert(insert);
        }catch (Exception e){
            log.info("dealCustomsDeclarationData = {}", JSONObject.toJSONString(lenovoFileModel),e);
        }
    }


    private LenovoFileResp getLenovoFileResp(String path, int pageNum) {
        try {
            Thread.sleep(610);
        }catch (Exception e){
            log.info("睡500毫秒");
        }
        LenovoFileReq params = new LenovoFileReq();
        params.setPath(path);
        params.setPage_num(pageNum+"");
        LenovoFileResp resp = lianXiangYunFilezService.getLenovoFile(params);
        return resp;
    }
}
