package org.springcenter.background.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.dto.background.fab.ProductMatchEsResp;
import org.springcenter.product.api.dto.background.fab.ProductSellingPointEsResp;
import org.springcenter.product.api.dto.background.fab.ProductSpuFabBaseInfoEsResp;
import org.springcenter.product.api.dto.background.fab.ProductWashInfoEsResp;
import org.springcenter.product.api.dto.fab.HomeWashInfoEsResp;
import org.springcenter.product.api.dto.fab.WpPicInfoResp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date:2024/6/19 15:10
 */
public interface IProductFabService {
    /**
     * 根据面料号查询面料信息
     * @param requestData 面料号
     * @return 返回面料克重信息
     */
    List<FabricWeightResp> getFabricWeightByFabricNo(List<String> requestData);


    /**
     * 导出对应条件fab的品类信息
     * @param requestData 入参
     * @param page 分页
     * @return 返回
     */
    ProductFabResp exportProductFabInfo(QueryGoodsFabReq requestData, Page page);

    /**
     * 根据款号查询商品的fab信息
     * @param requestData 商品款号
     * @return 返回
     */
    ProductSpuFabResp queryFabInfo(ProductFabInfoReq requestData);

    /**
     * 根据商品id批量查询重点面料
     * @param requestData 入参
     * @return 返回
     */
    List<FabProductFabricInfoRep> queryFabricInfo(BatchQueryFabInfoReq requestData);

    /**
     * 根据商品id批量查询重点图案
     * @param requestData 入参
     * @return 返回
     */
    List<FabProductPatternInfoRep> queryBatchPatternInfo(BatchQueryFabInfoReq requestData);

    /**
     * 查询商品货季
     * @return 返回
     */
    List<String> queryMerchandiseSeason();

    /**
     * 导出fab上新表格
     * @param keys key
     */
    void exportProductFabNewArrivalInfo(String keys, QueryGoodsFabEsReq req);

    /**
     * 根据款号查询商品的图片
     * @param requestData 入参
     * @return 返回
     */
    ProductSpuFabImgInfoResp queryFabImgInfo(ProductSpuFabImgInfoReq requestData);


    /**
     * 根据款号批量查询商品的fab信息
     * @param productIds 商品id
     * @return 返回
     */
    List<ProductSpuFabResp> batchQueryFabInfo(List<String> productIds, String key);


    /**
     * 根据面料数据查询相关的skc
     * @param requestData 面料数据
     * @return 返回
     */
    List<FabSkcByParamInfoResp> querySkcByFabricInfo(FabProductFabricInfoRep requestData);


    /**
     * 根据图案中心获取相关skc
     * @param requestData 图案信息
     * @return 返回
     */
    List<FabSkcByParamInfoResp> querySkcByPatternInfo(FabProductPatternInfoRep requestData);

    /**
     * 根据参数获取
     * @param requestData 入参
     * @return 返回
     */
    List<String> queryMerchandiseByParam(String requestData);


    /**
     * 根据搭配图查询重点图片
     * @param requestData 搭配图
     * @return 返回
     */
    ProductSpuKeyDpFabResp queryKeyCombinations(ProductSpuKeyDpFabReq requestData);

    /**
     * 根据据商品款号 查询商品名称等信息
     * @param requestData 款号
     * @return 返回
     */
    List<FabInfoByName> getFabInfoByName(List<String> requestData);

    /**
     * 根据款号查询网盘图片
     * @param names 款号
     * @param isMatchAndDetail 是否查细节和搭配图细节
     * @param isPngs 是否查一览图
     * @return 返回
     */
    Map<String, BuildFabImgInfo> buildFabImgInfo(List<String> names, Boolean isMatchAndDetail, Boolean isPngs);


    /**
     * 根据款号获取水洗说明
     * @param requestData 款号
     * @return 返回
     */
    List<HomeWashInfoEsResp> getHomeWashInfo(String requestData);

    /**
     * 获取网盘图片
     * @param requestData 入参
     * @return 返回
     */
    List<WpPicInfoResp> getWpOriginalPis(String requestData);

    /**
     * 批量导入商品fab信息
     * @param url 链接
     * @return 返回
     */
    String importProductFabInfo(String url, String keys, String type);

    /**
     * 判断当前导入表格是否超两千条
     * @param requestData
     * @return true在两千以内 false在两千以外
     */
    Boolean judgeImportProductFabInfo(String requestData);

    /**
     * 查询商品fab基本信息
     * @param requestData 商品id
     * @return 返回
     */
    ProductSpuFabBaseInfoEsResp queryFabDetailBaseInfo(String requestData);


    /**
     * 根据商品id查询商品卖点
     * @param requestData 商品id
     * @return 返回
     */
    ProductSellingPointEsResp queryFabDetailSellingPoint(String requestData);

    /**
     * 根据商品id查询商品搭配信息
     * @param requestData 商品id
     * @return 返回
     */
    ProductMatchEsResp queryFabDetailMatchInfo(String requestData);

    /**
     * 根据商品id查询商品水洗说明
     * @param requestData 商品id
     * @return 返回
     */
    ProductWashInfoEsResp queryFabDetailWashInfo(String requestData);

    /**
     * 根据款号查询面料信息
     * @param requestData 款号
     * @return 返回
     */
    ProductSpuFabEsResp queryFabBomInfo(String requestData);

    /**
     * 导入商品面料故事
     * @param url 路径
     * @param keys key
     * @param type 类型
     * @return 返回
     */
    String importProductFabricStory(String url, String keys, String type);

    /**
     * 导入商品图案灵感说明
     * @param url 路径
     * @param keys key
     * @param type 类型
     * @return 返回
     */
    String importProductPattern(String url, String keys, String type);

    /**
     * 批量查询商品卖点等信息
     * @param requestData 批量商品id
     * @return 返回信息
     */
    List<ProductSellingPointEsResp> batchQueryFabDetailSellingPoints(List<String> requestData);

    /**
     * 批量获取商品的洗涤信息
     * @param requestData 批量商品id
     * @return 返回信息
     */
    List<ProductWashInfoEsResp> batchQueryFabDetailWashInfos(List<String> requestData);
}
