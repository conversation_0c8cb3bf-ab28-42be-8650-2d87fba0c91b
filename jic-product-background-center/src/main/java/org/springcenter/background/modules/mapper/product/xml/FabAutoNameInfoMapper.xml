<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.FabAutoNameInfoMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.FabAutoNameInfo">
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="AUTO_NAME_CALC" property="autoNameCalc" />
        <result column="AUTO_NAME_WHOLE" property="autoNameWhole" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="AUTO_TYPE" property="autoType" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, NAME, AUTO_NAME_CALC, AUTO_NAME_WHOLE, CREATE_TIME, UPDATE_TIME, IS_DELETED, AUTO_TYPE
    </sql>

    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO FAB_AUTO_NAME_INFO
            (ID, NAME, AUTO_NAME_CALC, AUTO_NAME_WHOLE, CREATE_TIME, UPDATE_TIME, AUTO_TYPE) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.autoNameCalc,jdbcType=VARCHAR},
            #{item.autoNameWhole,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.autoType,jdbcType=VARCHAR})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <update id="updateAll">
        UPDATE FAB_AUTO_NAME_INFO
        SET IS_DELETED = 1
        WHERE IS_DELETED = 0 AND AUTO_TYPE = #{autoType}
        <if test="nameList != null and nameList.size() > 0">
            and NAME in
            <foreach collection="nameList" item="list" close=")" open="(" separator=",">
                #{list}
            </foreach>
        </if>
    </update>

    <select id="selectByNameList" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"></include>
            FROM FAB_AUTO_NAME_INFO
        WHERE IS_DELETED = 0
        <if test="nameList != null and nameList.size() > 0">
            and NAME in
            <foreach collection="nameList" item="list" close=")" open="(" separator=",">
                #{list}
            </foreach>
        </if>
        ORDER BY CREATE_TIME desc
    </select>
</mapper>
