<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.ProductSellingPointsMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.ProductSellingPoints">
        <id column="ID" property="id" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="SELLING_POINTS" property="sellingPoints" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, PRODUCT_ID, SELLING_POINTS, CREATE_TIME, UPDATE_TIME, IS_DELETED
    </sql>


    <select id="selectByProductIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM PRODUCT_SELLING_POINTS
            WHERE IS_DELETED = 0 AND PRODUCT_ID IN
            <foreach collection="ids" item="productId" open="(" separator="," close=")">
                #{productId}
            </foreach>
    </select>

    <update id="updateByProductId">
        UPDATE PRODUCT_SELLING_POINTS
        SET IS_DELETED = 1,
        UPDATE_TIME = sysdate
        WHERE PRODUCT_ID = #{productId}
    </update>


    <insert id="insertBatch">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO PRODUCT_SELLING_POINTS
            (ID, PRODUCT_ID, SELLING_POINTS, CREATE_TIME, UPDATE_TIME, IS_DELETED) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.productId,jdbcType=DECIMAL}, #{item.sellingPoints,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},#{item.updateTime,jdbcType=TIMESTAMP}, 0)
        </foreach>
        SELECT 1 FROM DUAL
    </insert>
</mapper>
