package org.springcenter.background.modules.mapper.product;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.WrongGoodsMain;
import org.springcenter.product.api.dto.background.WrongGoodsMainReq;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【WRONG_GOODS_MAIN(货不对版主表)】的数据库操作Mapper
* @createDate 2024-10-22 09:21:33
* @Entity generator.domain.WrongGoodsMain
*/
public interface WrongGoodsMainMapper extends BaseMapper<WrongGoodsMain> {

    List<WrongGoodsMain> selectByStylingCode(@Param("stylingCode") String stylingCode);

    List<WrongGoodsMain> selectByRequestData(WrongGoodsMainReq requestData);
}




