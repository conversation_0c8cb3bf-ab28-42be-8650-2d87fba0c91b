package org.springcenter.background.modules.service;


import org.springcenter.product.api.dto.BatchGetViewUrlReq;
import org.springcenter.product.api.dto.LianxiangBatchGetViewReq;
import org.springcenter.product.api.dto.LianxiangBatchGetViewResp;
import org.springcenter.product.api.dto.UploadLianxiangDto;
import org.springcenter.product.api.lenovo.LenovoFileReq;
import org.springcenter.product.api.lenovo.LenovoFileResp;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface LianXiangYunFilezService {


    /**
     *  获取accesstoken  加锁 redis 缓存
     * @return
     */
    public String getAccessToken();


    /**
     *  根据配置的路径  去获取路径中的所有文件信息
     */
    public LenovoFileResp getLenovoFile(LenovoFileReq lenovoFileReq);

    /**
     * 获取预览
     * @return
     */
    String getPdfView(String neid,String nsid);

    String download(String neid, String nsid);


    List<LianxiangBatchGetViewResp> batchGetViewUrl(LianxiangBatchGetViewReq lianxiangBatchGetViewReq);

    public LenovoFileResp getLenovoFileResp(String path,int pageNum);

    List<String> batchDownload(List<BatchGetViewUrlReq.LianxiangData> requestData);

    UploadLianxiangDto upload(MultipartFile multipartFile, String path);
}
