package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 图片和商品关联表   打标记
 * @TableName WRONG_GOODS_PIMGS_RELATION
 */
@TableName(value ="WRONG_GOODS_PIMGS_RELATION")
@Data
public class WrongGoodsPimgsRelation implements Serializable {
    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 
     */
    private String wrongGoodsProductId;

    /**
     * 
     */
    private String wrongGoodsImgsId;

    /**
     * 
     */
    private Integer isDel;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}