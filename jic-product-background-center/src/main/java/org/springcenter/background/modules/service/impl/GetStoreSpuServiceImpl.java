package org.springcenter.background.modules.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.jnby.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsSetQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.background.modules.mapper.bojun.CStoreMapper;
import org.springcenter.background.modules.model.bojun.CStore;
import org.springcenter.background.modules.service.GetStoreSpuService;
import org.springcenter.background.modules.util.EsUtil;
import org.springcenter.product.api.dto.StoreGoodSpuResp;
import org.springcenter.product.api.dto.StoreGoodsForZhReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import strman.Strman;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/8/13 21:01
 */
@Slf4j
@Service
@RefreshScope
public class GetStoreSpuServiceImpl implements GetStoreSpuService {

    @Autowired
    private CStoreMapper cStoreMapper;

    @Value("${es.index.split.store.goods}")
    private String storeSplitGoodsIndex;


    @Value("${es.index.split.store.goods.mode}")
    private Long storeSplitGoodsIndexMode;

    @Autowired
    private EsUtil esUtil;

    @Override
    public List<StoreGoodSpuResp> searchGoodsByStore(StoreGoodsForZhReq context, Page page) {
        SearchRequest request = new SearchRequest();
        CStore cStore = Preconditions.checkNotNull(cStoreMapper.selectById(Long.valueOf(context.getStoreId().get(0))),
                "未找到当前门店");
        Long yuShu = null;
        if (cStore.getCUnionstoreId() == null) {
            yuShu = cStore.getId() % storeSplitGoodsIndexMode;
        } else {
            yuShu = cStore.getCUnionstoreId() % storeSplitGoodsIndexMode;
        }
        if (yuShu == null) {
            throw new RuntimeException("未计算到余数");
        }

        request.indices(StringUtils.replace(storeSplitGoodsIndex, "mode", Objects.toString(yuShu)));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        //搜索关键字逻辑
        String productCode = context.getProductCode();
        Boolean isTransSpu = false;

        if (context.getM_band_id() != null){
            queryBuilder.must(QueryBuilders.termQuery("m_band_id", context.getM_band_id()));
        }

        if (CollectionUtils.isNotEmpty(context.getM_band_ids())){
            queryBuilder.must(QueryBuilders.termsQuery("m_band_id", context.getM_band_ids()));
        }

        if (Objects.nonNull(context.getStoreId())){
            queryBuilder.must(QueryBuilders.termsQuery("c_store_id", context.getStoreId()));
        }

        if (Objects.nonNull(context.getM_big_category_id())){
            queryBuilder.must(QueryBuilders.termQuery("m_big_category_id", context.getM_big_category_id()));
        }
        if (Objects.nonNull(context.getM_small_category_id())){
            queryBuilder.must(QueryBuilders.termQuery("m_small_category_id", context.getM_small_category_id()));
        }

        if (CollectionUtils.isNotEmpty(context.getMBigCategoryIds())){
            queryBuilder.must(QueryBuilders.termsQuery("m_big_category_id", context.getMBigCategoryIds()));
        }
        if (CollectionUtils.isNotEmpty(context.getMSmallCategoryIds())){
            queryBuilder.must(QueryBuilders.termsQuery("m_small_category_id", context.getMSmallCategoryIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getM_brand_id())){
            queryBuilder.must(QueryBuilders.termsQuery("c_arcbrand_id", context.getM_brand_id()));
        }
        if (context.getYear() != null && !context.getYear().isEmpty()){
            queryBuilder.must(QueryBuilders.termsQuery("year", context.getYear()));
        }
        if (context.getIsStockNotEmpty() == 0 && context.isQty() && context.isEbQty() && context.isMallQty()) {
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.should(QueryBuilders.rangeQuery("qty").gt(0));
            boolQueryBuilder.should(QueryBuilders.rangeQuery("eb_qty").gt(0));
            boolQueryBuilder.should(QueryBuilders.rangeQuery("mall_qty").gt(0));
            queryBuilder.must(boolQueryBuilder);
        } else if (context.getIsStockNotEmpty() == 0 && context.isQty() && context.isEbQty() && !context.isMallQty()) {
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.should(QueryBuilders.rangeQuery("qty").gt(0));
            boolQueryBuilder.should(QueryBuilders.rangeQuery("eb_qty").gt(0));
            queryBuilder.must(boolQueryBuilder);
        } else if (context.getIsStockNotEmpty() == 0 && !context.isQty() && context.isEbQty() && context.isMallQty()) {
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.should(QueryBuilders.rangeQuery("eb_qty").gt(0));
            boolQueryBuilder.should(QueryBuilders.rangeQuery("mall_qty").gt(0));
            queryBuilder.must(boolQueryBuilder);
        } else if (context.getIsStockNotEmpty() == 0 && context.isQty() && !context.isEbQty() && context.isMallQty()) {
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.should(QueryBuilders.rangeQuery("qty").gt(0));
            boolQueryBuilder.should(QueryBuilders.rangeQuery("mall_qty").gt(0));
            queryBuilder.must(boolQueryBuilder);
        } else if (context.getIsStockNotEmpty() == 1 || context.isQty()){
            queryBuilder.must(QueryBuilders.rangeQuery("qty").gt(0));
        } else if (context.isEbQty()){
            queryBuilder.must(QueryBuilders.rangeQuery("eb_qty").gt(0));
        } else if (context.isMallQty()){
            queryBuilder.must(QueryBuilders.rangeQuery("mall_qty").gt(0));
        }



        if(CollectionUtils.isNotEmpty(context.getProductIds())){
            queryBuilder.must(QueryBuilders.termsQuery("m_product_id", context.getProductIds()));
        }
        // 筛选商品标签tag
        if (StringUtils.isNotBlank(context.getProductTag())) {
            queryBuilder.must(QueryBuilders.termsQuery("product_tag_list.keyword", context.getProductTag()));
        }
        //排序,最新排序，获取当前的年份
        sourceBuilder.sort("_id", SortOrder.DESC);
        if (StringUtils.isNotBlank(context.getNextId())) {
            Object[] searchAfterValues = new Object[]{context.getNextId()};
            sourceBuilder.searchAfter(searchAfterValues);
        }


        if (CollectionUtils.isNotEmpty(context.getMustLabels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("labels", context.getMustLabels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script("1"));
            queryBuilder.must(termsSetQueryBuilder);
        }

        if (CollectionUtils.isNotEmpty(context.getMustLabelLevels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("label_levels", context.getMustLabelLevels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script("1"));
            queryBuilder.must(termsSetQueryBuilder);
        }
        // 过滤商品重复问题 【微定制出现LESS出现jnby商品】
        queryBuilder.filter(QueryBuilders.termQuery("is_display", 1));

        sourceBuilder.fetchSource(new String[]{"c_arcbrand_id","c_store_id","store_product_id","m_product_id",
                "m_brand_id", "name", "value", "price", "sales_num", "sku_list", "detail_imgs","cover_imgs", "qty",
                "eb_qty", "mall_qty", "product_tag_list"}, new String[]{});
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(page.getPageSize());
        request.source(sourceBuilder);
        List<StoreGoodSpuResp> entities = new ArrayList<>();
        try {
            log.info("storeGoodsIndex sourceBuilder：{}", sourceBuilder);
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }

            page.setCount(response.getHits().getTotalHits().value);

            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                StoreGoodSpuResp entity = StoreGoodSpuResp.fromJson(hits[i].getSourceAsString(), StoreGoodSpuResp.class);
                entity.buildSku();
                entity.setId(entity.getM_product_id());
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品异常e = {}", e.getMessage());
            return new ArrayList<>();
        }

        return entities;
    }
}
