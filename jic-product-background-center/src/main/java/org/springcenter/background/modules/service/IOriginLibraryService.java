package org.springcenter.background.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springcenter.background.modules.model.product.DetailOriginLibrary;
import org.springcenter.background.modules.model.product.OriginLibrary;
import org.springcenter.product.api.dto.back.LendOriginLibraryReq;
import org.springcenter.product.api.dto.back.ReturnOriginLibraryReq;

import java.util.List;

public interface IOriginLibraryService extends IService<OriginLibrary> {

    /**
     * 原样库出借
     * @param lendOriginLibraryReq
     */
    void lendOriginLibrary(LendOriginLibraryReq lendOriginLibraryReq);

    /**
     * 原样库归还
     * @param returnOriginLibraryReq
     */
    void returnOriginLibrary(ReturnOriginLibraryReq returnOriginLibraryReq);

    /**
     * 批量获取原样库记录
     */
    List<OriginLibrary> batchGetOriginLibrary(List<String> originLibraryIds);

    /**
     * 查询借用人待归还的原样库
     * @param lender 手机号
     * @return
     */
    List<OriginLibrary> getOriginLibraryByLender(String lender);

    /**
     * 查询所有还剩7天未归还的原样库
     */
    void sendMsgOriginLibraryByOverdue();

    /**
     * 批量更新超期未归还的原样库状态
     */
    void updateOriginLibraryByOverdue();

    /**
     * 获取原样详情
     * @param id
     * @return
     */
    DetailOriginLibrary getById(String id);
}
