package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.ProductEnvFabricInfo;

import java.util.List;

public interface ProductEnvFabricInfoMapper extends BaseMapper<ProductEnvFabricInfo> {


    void updateAll(@Param("list") List<String> names);

    void insertBatch(@Param("list") List<ProductEnvFabricInfo> inserts);

    List<ProductEnvFabricInfo> selectByProductId(@Param("productId") String requestData);

    List<ProductEnvFabricInfo> selectEnviFabricByIds(@Param("list") List<String> collect);
}
