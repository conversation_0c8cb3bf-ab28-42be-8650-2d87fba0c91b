<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.bojun.EmployeeBaseMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.bojun.EmployeeBase">
    <result column="ID" jdbcType="DECIMAL" property="id" />
    <result column="LINKID" jdbcType="VARCHAR" property="linkid" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="POSITION" jdbcType="VARCHAR" property="position" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="WEIXINID" jdbcType="VARCHAR" property="weixinid" />
    <result column="PPID" jdbcType="VARCHAR" property="ppid" />
    <result column="ROLEID" jdbcType="VARCHAR" property="roleid" />
    <result column="WXOPENID" jdbcType="VARCHAR" property="wxopenid" />
    <result column="WXUNIONID" jdbcType="VARCHAR" property="wxunionid" />
    <result column="ISACTIVE" jdbcType="DECIMAL" property="isactive" />
    <result column="OWNERID" jdbcType="VARCHAR" property="ownerid" />
    <result column="MODIFIERID" jdbcType="VARCHAR" property="modifierid" />
    <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
    <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
    <result column="RENTID" jdbcType="DECIMAL" property="rentid" />
    <result column="ISNEEDSYNC" jdbcType="DECIMAL" property="isneedsync" />
    <result column="LASTSYNCDATE" jdbcType="TIMESTAMP" property="lastsyncdate" />
    <result column="LINKSTORE" jdbcType="VARCHAR" property="linkstore" />
    <result column="CERTNO" jdbcType="VARCHAR" property="certno" />
    <result column="LINKID2" jdbcType="VARCHAR" property="linkid2" />
    <result column="WXSTATUS" jdbcType="DECIMAL" property="wxstatus" />
    <result column="AVATAR" jdbcType="VARCHAR" property="avatar" />
    <result column="WXUPDATEDATE" jdbcType="TIMESTAMP" property="wxupdatedate" />
    <result column="WXIMAGE" jdbcType="VARCHAR" property="wximage" />
    <result column="LINKSOURCE" jdbcType="VARCHAR" property="linksource" />
    <result column="SYNCERROR" jdbcType="VARCHAR" property="syncerror" />
    <result column="RECMSG" jdbcType="CHAR" property="recmsg" />
  </resultMap>
  <sql id="Base_Column">
    ID, LINKID, NAME,
      POSITION, MOBILE, EMAIL,
      WEIXINID, PPID, ROLEID,
      WXOPENID, WXUNIONID, ISACTIVE,
      OWNERID, MODIFIERID, CREATIONDATE,
      MODIFIEDDATE, RENTID, ISNEEDSYNC,
      LASTSYNCDATE, LINKSTORE, CERTNO,
      LINKID2, WXSTATUS, AVATAR,
      WXUPDATEDATE, WXIMAGE, LINKSOURCE,
      SYNCERROR, RECMSG
  </sql>


</mapper>
