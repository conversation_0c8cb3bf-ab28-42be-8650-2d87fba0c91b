package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2024/3/14 16:21
 */
@Data
@TableName(value = "FAB_VOLUME_DISTRIBUTE_TASK_LOG")
public class FabVolumeDistributeTaskLog {

    @TableId
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField(value = "FAB_VOLUME_ID")
    @ApiModelProperty(value = "产品册id")
    private String fabVolumeId;

    @TableField(value = "FAB_VOLUME_NAME")
    @ApiModelProperty(value = "产品册名称")
    private String fabVolumeName;

    @TableField(value = "STORE_PAK_ID")
    @ApiModelProperty(value = "门店包信息")
    private Long storePakId;

    @TableField(value = "BRAND_ID")
    @ApiModelProperty(value = "品牌id")
    private Long brandId;

    @TableField(value = "TASK_TYPE")
    @ApiModelProperty(value = "任务类型 0浏览任务 1考核任务")
    private Integer taskType;

    @TableField(value = "TASK_TEMPLATE_ID")
    @ApiModelProperty(value = "任务模板")
    private String taskTemplateId;

    @TableField(value = "NEXT_DISTRIBUTE_TIME")
    @ApiModelProperty(value = "下次分发时间")
    private Date nextDistributeTime;

    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "IS_DELETED")
    @ApiModelProperty(value = "0正常 1已删除")
    private Integer isDeleted;

    @TableField(value = "TASK_URL")
    @ApiModelProperty(value = "任务链接")
    private String taskUrl;


    @TableField(value = "TASK_CHECK_TYPE")
    @ApiModelProperty(value = "导购考核任务方式 1线下 2线上")
    private Integer taskCheckType;

    @TableField(value = "TASK_FEED_BACK")
    @ApiModelProperty(value = "是否需要反馈0不需要 1需要")
    private Integer taskFeedBack;

    @TableField(value = "CAMPAIGN_ID")
    @ApiModelProperty(value = "活动id")
    private String campaignId;

    @TableField(value = "TASK_VALID_PERIOD")
    @ApiModelProperty(value = "任务模板")
    private Integer taskValidPeriod;

    @TableField(value = "PERIOD_UNIT")
    @ApiModelProperty(value = "有效时间单位")
    private String periodUnit;

    @TableField(value = "FAB_TYPE")
    @ApiModelProperty(value = "产品册的类型")
    private Integer fabType;
}
