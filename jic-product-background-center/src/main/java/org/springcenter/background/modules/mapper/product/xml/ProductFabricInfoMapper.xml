<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.ProductFabricInfoMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.ProductFabricInfo">
        <id column="ID" property="id" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="NAME" property="name" />
        <result column="SKC_NO" property="skcNo" />
        <result column="FABRIC_INFO" property="fabricInfo" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="OPERATORS" property="operators" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, PRODUCT_ID, NAME, SKC_NO, FABRIC_INFO, CREATE_TIME, UPDATE_TIME, OPERATORS,IS_DELETED
    </sql>
    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO PRODUCT_FABRIC_INFO
            (ID, NAME, PRODUCT_ID, SKC_NO, FABRIC_INFO, CREATE_TIME, UPDATE_TIME, OPERATORS) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.productId,jdbcType=VARCHAR},
             #{item.skcNo,jdbcType=VARCHAR}, #{item.fabricInfo,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.operators,jdbcType=VARCHAR})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <update id="batchUpdateByIds">
        <foreach collection="list" item="item" index="index"  open="begin" close=";end;" separator=";">
            update PRODUCT_FABRIC_INFO
            set
            <if test="item.operators != null and item.operators !=''">
                "OPERATORS" = #{item.operators,jdbcType=VARCHAR},
            </if>
            UPDATE_TIME = sysdate,
            IS_DELETED = 1
            where NAME = #{item.name} AND PRODUCT_ID = #{item.productId}
        </foreach>
    </update>
    <select id="selectByProductId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from PRODUCT_FABRIC_INFO
        where PRODUCT_ID = #{productId}
        and IS_DELETED = 0
    </select>
</mapper>
