package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.WrongGoodsPimgsRelation;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【WRONG_GOODS_PIMGS_RELATION(图片和商品关联表   打标记)】的数据库操作Mapper
* @createDate 2024-10-22 09:21:33
* @Entity generator.domain.WrongGoodsPimgsRelation
*/
public interface WrongGoodsPimgsRelationMapper extends BaseMapper<WrongGoodsPimgsRelation> {

    List<WrongGoodsPimgsRelation> selectByWrongGoodsProductIds(@Param("wrongGoodsProductIds") List<String> wrongGoodsProductIds);

    void updateIsDelByWrongProductIds(@Param("wrongGoodsProductIds") List<String> wrongProductIds, @Param("isDel") Integer isDel);

    void batchInsert(@Param("list") List<WrongGoodsPimgsRelation> insertData);
}




