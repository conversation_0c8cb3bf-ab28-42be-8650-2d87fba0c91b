package org.springcenter.background.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.CustomsDeclarationReq;
import org.springcenter.product.api.dto.CustomsDeclarationResp;

import java.util.List;

public interface CustomsDeclarationService {
    /**
     * 导入报关单从网盘到数据库
     * @param filePath
     * @return
     */
    String importReportPortToDatabase(String filePath);

    List<CustomsDeclarationResp> list(CustomsDeclarationReq requestData, Page page);

    void deleteById(String id);

    List<CustomsDeclarationResp> batchGetByNames(List<String> requestData);
}
