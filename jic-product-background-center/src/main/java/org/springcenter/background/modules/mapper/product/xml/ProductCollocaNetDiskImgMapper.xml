<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.ProductCollocaNetDiskImgMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.ProductCollocaNetDiskImg">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="COLLOCATION_CODE" jdbcType="VARCHAR" property="collocationCode" />
    <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode" />
    <result column="COLOR_NO" jdbcType="VARCHAR" property="colorNo" />
    <result column="IS_DEL" jdbcType="DECIMAL" property="isDel" />
    <result column="NEID" jdbcType="VARCHAR" property="neid" />
    <result column="NSID" jdbcType="VARCHAR" property="nsid" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="TYPE" jdbcType="DECIMAL" property="type" />
    <result column="QINIU_IMG_PATH" jdbcType="VARCHAR" property="qiniuImgPath" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, COLLOCATION_CODE, PRODUCT_CODE, COLOR_NO, IS_DEL, NEID, NSID, CREATE_TIME, UPDATE_TIME, 
    "TYPE", QINIU_IMG_PATH
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PRODUCT_COLLOCA_NET_DISK_IMG
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectByCollocationCode"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PRODUCT_COLLOCA_NET_DISK_IMG
    where COLLOCATION_CODE = #{collocationCode} and IS_DEL = #{isDel}
  </select>
  <select id="selectByProductCodeAndColorNo"
          resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from PRODUCT_COLLOCA_NET_DISK_IMG
    where PRODUCT_CODE = #{productCode}  and COLOR_NO = #{colorNo}  and IS_DEL = #{isDel}

  </select>
  <select id="selectBySkcs" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from PRODUCT_COLLOCA_NET_DISK_IMG
    where
        concat(PRODUCT_CODE,COLOR_NO) in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
        and is_del = 0

  </select>
    <select id="selectByProductCodesAndType"
            resultMap="BaseResultMap">

      select
      <include refid="Base_Column_List" />
      from PRODUCT_COLLOCA_NET_DISK_IMG
      where
      PRODUCT_CODE in
      <foreach collection="productCodes" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
      and is_del = 0
      and "TYPE" = #{type}

    </select>
  <select id="selectByCollocationCodes"
          resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from PRODUCT_COLLOCA_NET_DISK_IMG
    where
    COLLOCATION_CODE in
    <foreach collection="collocationCodes" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and is_del = 0
    and "TYPE" = 0


  </select>
  <select id="selectByNeId"
         resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PRODUCT_COLLOCA_NET_DISK_IMG
    where
    NEID  = #{neId}
    and is_del = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from PRODUCT_COLLOCA_NET_DISK_IMG
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.springcenter.background.modules.model.product.ProductCollocaNetDiskImg">
    insert into PRODUCT_COLLOCA_NET_DISK_IMG (ID, COLLOCATION_CODE, PRODUCT_CODE, 
      COLOR_NO, IS_DEL, NEID, 
      NSID, CREATE_TIME, UPDATE_TIME, 
      "TYPE", QINIU_IMG_PATH)
    values (#{id,jdbcType=VARCHAR}, #{collocationCode,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, 
      #{colorNo,jdbcType=VARCHAR}, #{isDel,jdbcType=DECIMAL}, #{neid,jdbcType=VARCHAR}, 
      #{nsid,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=DECIMAL}, #{qiniuImgPath,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.springcenter.background.modules.model.product.ProductCollocaNetDiskImg">
    insert into PRODUCT_COLLOCA_NET_DISK_IMG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="collocationCode != null">
        COLLOCATION_CODE,
      </if>
      <if test="productCode != null">
        PRODUCT_CODE,
      </if>
      <if test="colorNo != null">
        COLOR_NO,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="neid != null">
        NEID,
      </if>
      <if test="nsid != null">
        NSID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="type != null">
        "TYPE",
      </if>
      <if test="qiniuImgPath != null">
        QINIU_IMG_PATH,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="collocationCode != null">
        #{collocationCode,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="colorNo != null">
        #{colorNo,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="neid != null">
        #{neid,jdbcType=VARCHAR},
      </if>
      <if test="nsid != null">
        #{nsid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=DECIMAL},
      </if>
      <if test="qiniuImgPath != null">
        #{qiniuImgPath,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert">
    INSERT ALL
    <foreach item="item" index="index" collection="list">
      into PRODUCT_COLLOCA_NET_DISK_IMG
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          ID,
        </if>
        <if test="item.collocationCode != null">
          COLLOCATION_CODE,
        </if>
        <if test="item.productCode != null">
          PRODUCT_CODE,
        </if>
        <if test="item.colorNo != null">
          COLOR_NO,
        </if>
        <if test="item.isDel != null">
          IS_DEL,
        </if>
        <if test="item.neid != null">
          NEID,
        </if>
        <if test="item.nsid != null">
          NSID,
        </if>
        <if test="item.createTime != null">
          CREATE_TIME,
        </if>
        <if test="item.updateTime != null">
          UPDATE_TIME,
        </if>
        <if test="item.type != null">
          "TYPE",
        </if>
        <if test="item.qiniuImgPath != null">
          QINIU_IMG_PATH,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=VARCHAR},
        </if>
        <if test="item.collocationCode != null">
          #{item.collocationCode},
        </if>
        <if test="item.productCode != null">
          #{item.productCode,jdbcType=VARCHAR},
        </if>
        <if test="item.colorNo != null">
          #{item.colorNo,jdbcType=VARCHAR},
        </if>
        <if test="item.isDel != null">
          #{item.isDel,jdbcType=DECIMAL},
        </if>
        <if test="item.neid != null">
          #{item.neid,jdbcType=VARCHAR},
        </if>
        <if test="item.nsid != null">
          #{item.nsid,jdbcType=VARCHAR},
        </if>
        <if test="item.createTime != null">
          #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updateTime != null">
          #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.type != null">
          #{item.type,jdbcType=DECIMAL},
        </if>
        <if test="item.qiniuImgPath != null">
          #{item.qiniuImgPath,jdbcType=VARCHAR},
        </if>
      </trim>
    </foreach>
    SELECT 1 FROM DUAL
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.springcenter.background.modules.model.product.ProductCollocaNetDiskImg">
    update PRODUCT_COLLOCA_NET_DISK_IMG
    <set>
      <if test="collocationCode != null">
        COLLOCATION_CODE = #{collocationCode,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        PRODUCT_CODE = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="colorNo != null">
        COLOR_NO = #{colorNo,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="neid != null">
        NEID = #{neid,jdbcType=VARCHAR},
      </if>
      <if test="nsid != null">
        NSID = #{nsid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        "TYPE" = #{type,jdbcType=DECIMAL},
      </if>
      <if test="qiniuImgPath != null">
        QINIU_IMG_PATH = #{qiniuImgPath,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.springcenter.background.modules.model.product.ProductCollocaNetDiskImg">
    update PRODUCT_COLLOCA_NET_DISK_IMG
    set COLLOCATION_CODE = #{collocationCode,jdbcType=VARCHAR},
      PRODUCT_CODE = #{productCode,jdbcType=VARCHAR},
      COLOR_NO = #{colorNo,jdbcType=VARCHAR},
      IS_DEL = #{isDel,jdbcType=DECIMAL},
      NEID = #{neid,jdbcType=VARCHAR},
      NSID = #{nsid,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      "TYPE" = #{type,jdbcType=DECIMAL},
      QINIU_IMG_PATH = #{qiniuImgPath,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>