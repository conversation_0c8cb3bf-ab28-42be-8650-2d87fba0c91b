<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.PackageTemplateMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.PackageTemplate">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="packageId" column="PACKAGE_ID" jdbcType="VARCHAR"/>
            <result property="packageName" column="PACKAGE_NAME" jdbcType="VARCHAR"/>
            <result property="hasFilter" column="HAS_FILTER" jdbcType="DECIMAL"/>
            <result property="hasImport" column="HAS_IMPORT" jdbcType="DECIMAL"/>
            <result property="updator" column="UPDATOR" jdbcType="VARCHAR"/>
            <result property="invalidType" column="INVALID_TYPE" jdbcType="DECIMAL"/>
            <result property="startDate" column="START_DATE" jdbcType="TIMESTAMP"/>
            <result property="endDate" column="END_DATE" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
            <result property="department" column="DEPARTMENT" jdbcType="VARCHAR"/>
            <result property="isOpen" column="IS_OPEN" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PACKAGE_ID,PACKAGE_NAME,
        HAS_FILTER,HAS_IMPORT,UPDATOR,
        INVALID_TYPE,START_DATE,END_DATE,
        UPDATE_TIME,CREATE_TIME,IS_DELETED,
        PACKAGE_INTRODUCTION, DEPARTMENT, IS_OPEN
    </sql>

    <update id="updateEndDateTime">
        UPDATE PACKAGE_TEMPLATE
        SET END_DATE = null
        WHERE ID = #{id}
    </update>

    <select id="getPacId" resultType="java.lang.Long" useCache="false" flushCache="true">
        select seq_PRODUCT_PACKAGE_ID.nextval from dual
    </select>

    <select id="selectByParams" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM PACKAGE_TEMPLATE a
        WHERE a.IS_DELETED = 0
        <if test="list != null and list != '' and list.size > 0">
            AND ID IN
            <foreach collection="list" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="pacId != null and pacId != ''">
            AND PACKAGE_ID = #{pacId}
        </if>
        <if test="pacName != null and pacName != ''">
            AND PACKAGE_NAME like concat(concat('%',#{pacName}),'%')
        </if>
        <if test="isOpen != null">
            AND IS_OPEN = #{isOpen}
        </if>
        ORDER BY UPDATE_TIME DESC
    </select>

    <select id="selectByValidId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM PACKAGE_TEMPLATE
        WHERE IS_DELETED = 0 AND ID = #{id}
    </select>

    <select id="selectByPackIdValid" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM PACKAGE_TEMPLATE
        WHERE IS_DELETED = 0 AND PACKAGE_ID = #{pacId}
              AND IS_OPEN = 1
    </select>


    <select id="selectByPackIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM PACKAGE_TEMPLATE
        WHERE PACKAGE_ID in
        <foreach collection="list" item="pacId" separator="," open="(" close=")">
           #{pacId}
        </foreach>
    </select>

    <select id="getAllPacTemplate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM PACKAGE_TEMPLATE
        WHERE IS_DELETED = 0
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectByPacIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM PACKAGE_TEMPLATE
        WHERE IS_DELETED = 0
        AND PACKAGE_ID IN
        <foreach collection="list" item="pacId" separator="," open="(" close=")">
            #{pacId}
        </foreach>
    </select>
</mapper>
