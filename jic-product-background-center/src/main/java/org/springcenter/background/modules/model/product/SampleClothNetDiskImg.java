package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName SAMPLE_CLOTH_NET_DISK_IMG
 */
public class SampleClothNetDiskImg implements Serializable {
    /**
     * 主键
     */
    @TableField(value = "ID")
    private String id;

    /**
     * 样衣号
     */
    @TableField(value = "sample_Cloth_Code")
    private String sampleClothCode;

    /**
     * 文件名称  均是数字 可以按照这个进行排序
     */
    @TableField(value = "file_Name")
    private String fileName;

    /**
     * 0 正常  1 删除
     */
    @TableField(value = "is_Del")
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField(value = "create_Time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_Time")
    private Date updateTime;

    /**
     * 七牛云路径
     */
    @TableField(value = "qiniu_Img_Url")
    private String qiniuImgUrl;

    /**
     * NEID
     */
    @TableField(value = "neid")
    private String neid;

    /**
     * NSID
     */
    @TableField(value = "nsid")
    private String nsid;

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 样衣号
     */
    public String getSampleClothCode() {
        return sampleClothCode;
    }

    /**
     * 样衣号
     */
    public void setSampleClothCode(String sampleClothCode) {
        this.sampleClothCode = sampleClothCode;
    }

    /**
     * 文件名称  均是数字 可以按照这个进行排序
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * 文件名称  均是数字 可以按照这个进行排序
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 0 正常  1 删除
     */
    public Integer getIsDel() {
        return isDel;
    }

    /**
     * 0 正常  1 删除
     */
    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 七牛云路径
     */
    public String getQiniuImgUrl() {
        return qiniuImgUrl;
    }

    /**
     * 七牛云路径
     */
    public void setQiniuImgUrl(String qiniuImgUrl) {
        this.qiniuImgUrl = qiniuImgUrl;
    }

    /**
     * NEID
     */
    public String getNeid() {
        return neid;
    }

    /**
     * NEID
     */
    public void setNeid(String neid) {
        this.neid = neid;
    }

    /**
     * NSID
     */
    public String getNsid() {
        return nsid;
    }

    /**
     * NSID
     */
    public void setNsid(String nsid) {
        this.nsid = nsid;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SampleClothNetDiskImg other = (SampleClothNetDiskImg) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSampleClothCode() == null ? other.getSampleClothCode() == null : this.getSampleClothCode().equals(other.getSampleClothCode()))
            && (this.getFileName() == null ? other.getFileName() == null : this.getFileName().equals(other.getFileName()))
            && (this.getIsDel() == null ? other.getIsDel() == null : this.getIsDel().equals(other.getIsDel()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getQiniuImgUrl() == null ? other.getQiniuImgUrl() == null : this.getQiniuImgUrl().equals(other.getQiniuImgUrl()))
            && (this.getNeid() == null ? other.getNeid() == null : this.getNeid().equals(other.getNeid()))
            && (this.getNsid() == null ? other.getNsid() == null : this.getNsid().equals(other.getNsid()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSampleClothCode() == null) ? 0 : getSampleClothCode().hashCode());
        result = prime * result + ((getFileName() == null) ? 0 : getFileName().hashCode());
        result = prime * result + ((getIsDel() == null) ? 0 : getIsDel().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getQiniuImgUrl() == null) ? 0 : getQiniuImgUrl().hashCode());
        result = prime * result + ((getNeid() == null) ? 0 : getNeid().hashCode());
        result = prime * result + ((getNsid() == null) ? 0 : getNsid().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sampleClothCode=").append(sampleClothCode);
        sb.append(", fileName=").append(fileName);
        sb.append(", isDel=").append(isDel);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", qiniuImgUrl=").append(qiniuImgUrl);
        sb.append(", neid=").append(neid);
        sb.append(", nsid=").append(nsid);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}