package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.api.dto.FileDto;
import com.jnby.authority.common.system.vo.LoginUser;
import com.jnby.authority.common.system.vo.SysPermissionDataRuleModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springcenter.background.modules.service.CommonDataRuleService;
import org.springcenter.product.api.enums.ChannelTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/8/2 13:22
 */
@Service
@Slf4j
public class CommonDataRuleServiceImpl implements CommonDataRuleService {


    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Override
    public List<Long> getAllowBrandIdRule(String component, List<Long> brandIds) {
        if (StringUtils.isBlank(component)) {
            return brandIds;
        }

        List<SysPermissionDataRuleModel> ruleModels = new ArrayList<>();
        try {
            // 案例：[{"id":"1683727672999731201","permissionId":"1429683832036990978","ruleName":"JNBY","ruleColumn":"brand","ruleConditions":"=","ruleValue":"2","createTime":"2023-07-25 14:35:34","createBy":"18668166209","updateTime":"2023-08-15 20:25:07","updateBy":"15957109716"},{"id":"1691743369720729602","permissionId":"1429683832036990978","ruleName":"LESS","ruleColumn":"brand","ruleConditions":"=","ruleValue":"5","createTime":"2023-08-16 17:27:05","createBy":"15957109716","updateTime":null,"updateBy":null}]
            LoginUser userInfo = sysBaseAPI.getCurrentLoginUserInfo();
            ruleModels = sysBaseAPI.queryPermissionDataRule(component, "", userInfo.getUsername());
            log.info("获取品牌规则:{}", JSONObject.toJSONString(ruleModels));
        } catch (Exception e) {
            log.error("获取品牌规则异常:{}", e);
        }
        // 如果为空代表默认有所有品牌的权限。
        if (CollectionUtils.isEmpty(ruleModels)) {
            return brandIds;
        }

        List<Long> codeList = ruleModels.stream()
                .filter(model-> Objects.equals(model.getRuleColumn(), "brand"))
                .map(SysPermissionDataRuleModel::getRuleValue)
                .map(Long::valueOf)
                .collect(Collectors.toList());

        // 如果入参是所有品牌，则取有权限的品类。
        if (CollectionUtils.isEmpty(brandIds)) {
            return codeList;
        }
        // 都不为空则取交集
        codeList.retainAll(brandIds);
        return codeList;
    }

    @Override
    public List<String> getAllowChannel(String component) {
        List<String> channelList = ChannelTypeEnum.listDesc();
        if (StringUtils.isBlank(component)) {
            return channelList;
        }
        List<SysPermissionDataRuleModel> ruleModels = new ArrayList<>();
        try {
            LoginUser userInfo = sysBaseAPI.getCurrentLoginUserInfo();
            ruleModels = sysBaseAPI.queryPermissionDataRule(component, "", userInfo.getUsername());
            log.info("获取渠道规则:{}", JSONObject.toJSONString(ruleModels));
        } catch (Exception e) {
            log.error("获取渠道规则异常:{}", e);
        }
        // 如果为空代表默认有所有品牌的权限。
        if (CollectionUtils.isEmpty(ruleModels)) {
            return channelList;
        }

        List<String> codeList = ruleModels.stream()
                .filter(model-> Objects.equals(model.getRuleColumn(), "channel"))
                .map(SysPermissionDataRuleModel::getRuleValue)
                .collect(Collectors.toList());

        return codeList;
    }

    @Override
    public Map<String, String> listFileUrl(List<String> idList) {
        Map<String, String> id2UrlMap = Collections.emptyMap();
        if (CollectionUtils.isEmpty(idList)) {
            return id2UrlMap;
        }
        // 按逗号组装idList，使其成为一个字符串。例如: 1,2,3
        String idStr = idList.stream().map(String::valueOf).collect(Collectors.joining(","));
        log.info("开始获取下列图片[{}]", idStr);
        List<FileDto> fileList = null;
        try {
            fileList = sysBaseAPI.getFileList(idStr);
        } catch (Exception e) {
            log.error("获取图片异常", e);
        }
        if (CollectionUtils.isEmpty(fileList)) {
            log.info("没有获取到图片信息");
            return id2UrlMap;
        }
        id2UrlMap = fileList.stream().collect(Collectors.toMap(FileDto::getId, FileDto::getUrl));
        log.info("获取到的所有图片映射信息:{}", JSONObject.toJSONString(id2UrlMap));
        return id2UrlMap;
    }

    @Override
    public List<String> getAllowRuleData(String component, String ruleType) {
        if (StringUtils.isBlank(component)) {
            return new ArrayList<>();
        }
        List<SysPermissionDataRuleModel> ruleModels = new ArrayList<>();
        try {
            LoginUser userInfo = sysBaseAPI.getCurrentLoginUserInfo();
            ruleModels = sysBaseAPI.queryPermissionDataRule(component, "", userInfo.getUsername());
            log.info("获取数据权限规则:{}", JSONObject.toJSONString(ruleModels));
        } catch (Exception e) {
            log.error("获取数据权限规则异常:{}", e);
        }
        // 如果为空代表没有任何数据权限
        if (CollectionUtils.isEmpty(ruleModels)) {
            return new ArrayList<>();
        }

        List<String> codeList = ruleModels.stream()
                .filter(model-> Objects.equals(model.getRuleColumn(), ruleType))
                .map(SysPermissionDataRuleModel::getRuleValue)
                .collect(Collectors.toList());

        return codeList;
    }
}
