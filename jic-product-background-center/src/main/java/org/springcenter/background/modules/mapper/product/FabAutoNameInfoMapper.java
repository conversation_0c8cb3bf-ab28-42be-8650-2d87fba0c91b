package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.FabAutoNameInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/12/8 11:05
 */
public interface FabAutoNameInfoMapper extends BaseMapper<FabAutoNameInfo> {

    void batchInsert(@Param("list") List<FabAutoNameInfo> partBatchInsert);

    void updateAll(@Param("nameList") List<String> nameList, @Param("autoType") Integer autoType);

    List<FabAutoNameInfo> selectByNameList(@Param("nameList") List<String> nameList);
}
