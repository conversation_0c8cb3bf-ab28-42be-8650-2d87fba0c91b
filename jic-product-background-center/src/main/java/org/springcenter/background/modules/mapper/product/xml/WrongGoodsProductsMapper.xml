<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.WrongGoodsProductsMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.WrongGoodsProducts">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="productCode" column="PRODUCT_CODE" jdbcType="VARCHAR"/>
            <result property="colorNo" column="COLOR_NO" jdbcType="VARCHAR"/>
            <result property="stylingCode" column="STYLING_CODE" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="skc" column="SKC" jdbcType="VARCHAR"/>
        <result property="reason" column="REASON" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PRODUCT_CODE,COLOR_NO,
        STYLING_CODE,CREATE_TIME,UPDATE_TIME,
        IS_DEL,SKC,REASON
    </sql>
    <select id="selectByStylingCodeAndSkc"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from       WRONG_GOODS_PRODUCTS
        where STYLING_CODE = #{stylingCode} and is_del = 0
          <if test="skc != null and skc != ''">
              and SKC = #{skc}
          </if>

    </select>
    <select id="selectByStylingCodes"  resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from       WRONG_GOODS_PRODUCTS
        where STYLING_CODE in
              <foreach collection="stylingCodes" open="(" close=")" item="item" separator=",">
                  #{item}
              </foreach>
          and is_del = 0
    </select>
    <select id="selectByProductCodeAndColor"
           resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from       WRONG_GOODS_PRODUCTS
        where  is_del = 0
        <if test="productCode != null and productCode != ''">
            and PRODUCT_CODE = #{productCode}
        </if>
        <if test="color != null and color != ''">
            and COLOR_NO = #{color}
        </if>

    </select>
</mapper>
