package org.springcenter.background.modules.remote.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.modules.remote.ITaskCenterRemoteApi;
import org.springcenter.background.modules.remote.entity.AddFeedbackReqEntity;
import org.springcenter.background.modules.remote.entity.DistributeTaskReqEntity;
import org.springcenter.background.modules.remote.entity.FinishTaskReqEntity;
import org.springcenter.background.modules.remote.entity.JicBaseResp;
import org.springcenter.background.modules.remote.service.ITaskCenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date:2024/3/14 13:26
 */
@Service
@Slf4j
public class TaskCenterServiceImpl implements ITaskCenterService {

    @Autowired
    private ITaskCenterRemoteApi taskCenterRemoteApi;


    @Override
    public Boolean distributeTask(DistributeTaskReqEntity entity) {
        try {
            // 组装触发任务的实体
            Response<JicBaseResp> response = taskCenterRemoteApi.distributeTask(entity).execute();
            if (response.isSuccessful()){
                return response.isSuccessful();
            }
            log.error("===========调用创建任务失败， 入参：{}", JSONObject.toJSONString(entity));
            throw new RuntimeException("===========调用创建任务失败");
        } catch (IOException e) {
            log.error("===========调用创建任务失败  e:{} message:{}", e, e.getMessage());
            throw new RuntimeException("===========调用创建任务失败");
        }
    }


    @Override
    public Boolean finishTask(Long taskItemId) {
        try {
            // 组装触发任务的实体
            FinishTaskReqEntity entity = new FinishTaskReqEntity();
            entity.setTaskAllocateId(taskItemId);
            Response<JicBaseResp> response = taskCenterRemoteApi.finishTask(entity).execute();
            if (response.isSuccessful()){
                return response.isSuccessful();
            }
            log.error("===========调用完成任务失败， 入参：{}", JSONObject.toJSONString(entity));
            throw new RuntimeException("===========调用完成任务失败");
        } catch (IOException e) {
            log.error("===========调用完成任务失败  e:{} message:{}", e, e.getMessage());
            throw new RuntimeException("===========调用完成任务失败");
        }
    }

    @Override
    public Boolean addTaskFeedback(AddFeedbackReqEntity addFeedbackReq) {
        try {
            // 组装实体
            Response<JicBaseResp> response = taskCenterRemoteApi.addCheckFeedback(addFeedbackReq).execute();
            if (response.isSuccessful()){
                return response.isSuccessful();
            }
            log.error("===========调用增加任务反馈失败， 入参：{}", JSONObject.toJSONString(addFeedbackReq));
            throw new RuntimeException("===========调用完成任务失败");
        } catch (IOException e) {
            log.error("===========调用增加任务反馈失败  e:{} message:{}", e, e.getMessage());
            throw new RuntimeException("===========调用增加任务反馈失败");
        }
    }


}
