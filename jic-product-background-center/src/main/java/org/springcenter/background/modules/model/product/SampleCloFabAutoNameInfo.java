package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/7/1 15:23
 */
@TableName(value = "SAMPLE_CLO_FAB_AUTO_NAME_INFO")
@Data
public class SampleCloFabAutoNameInfo {

    @TableId(value = "ID")
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField(value = "BRAND")
    @ApiModelProperty(value = "品牌")
    private String brand;

    @TableField(value = "BAND")
    @ApiModelProperty(value = "波段")
    private String band;

    @TableField(value = "YEAR")
    @ApiModelProperty(value = "年份")
    private String year;

    @TableField(value = "SAMPLE_CODE")
    @ApiModelProperty(value = "样衣号")
    private String sampleCode;

    @TableField(value = "AUTO_NAME_CALC")
    @ApiModelProperty(value = "32字符的自动品名")
    private String autoNameCalc;

    @TableField(value = "AUTO_NAME_WHOLE")
    @ApiModelProperty(value = "完整的自动品名")
    private String autoNameWhole;


    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "IS_DELETED")
    @ApiModelProperty(value = "0正常 1已删除")
    private Integer isDeleted;

    @TableField(value = "GOOD_SEASON")
    @ApiModelProperty(value = "货季")
    private String goodSeason;

    @TableField(value = "COLOR_NO")
    @ApiModelProperty(value = "数字最大的色号")
    private String colorNo;
}
