package org.springcenter.background.modules.service.impl.autoName;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.background.modules.entity.productFab.CommonAutoNameGenerateEntity;
import org.springcenter.background.modules.entity.productFab.CommonAutoNameSettingEntity;
import org.springcenter.background.modules.entity.productFab.CommonSearchByPageAndIdEntity;
import org.springcenter.background.modules.enums.AutoNameEnum;
import org.springcenter.background.modules.mapper.product.FabAutoNameInfoMapper;
import org.springcenter.background.modules.model.product.FabAutoNameInfo;
import org.springcenter.background.modules.model.product.FabAutoNameSetting;
import org.springcenter.background.modules.service.CommonAutoName;
import org.springcenter.background.modules.service.FabAutoNameInfoService;
import org.springcenter.background.modules.util.EsUtil;
import org.springcenter.product.api.dto.FabSpuForAutoNameEsResp;
import org.springcenter.product.api.dto.FabSpuForAutoNameResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/12/8 11:08
 */
@Service
@Slf4j
@RefreshScope
public class FabAutoNameInfoServiceImpl implements FabAutoNameInfoService {

    @Value("${limit.data.size.index}")
    private String dataSizeTemplate;

    @Value("${fab.auto.name.info.index}")
    private String indexTemplate;

    @Value("${fab.auto.name.info.tag}")
    private String fabAutoNameInfoTag;

    @Autowired
    private EsUtil esUtil;

    @Autowired
    private FabAutoNameInfoMapper fabAutoNameInfoMapper;

    @Autowired
    private CommonAutoName commonAutoName;

    @Override
    public void generateAutoName(String name, AutoNameEnum nameEnum) {
        // 删除表内数据
        List<String> nameList = new ArrayList<>();
        if (StringUtils.isNotBlank(name)) {
            nameList = Arrays.stream(StringUtils.split(name, ",")).distinct().collect(Collectors.toList());
        }
        fabAutoNameInfoMapper.updateAll(nameList, nameEnum.getCode());

        // 获取索引和对应字数
        Map<Integer, String> indexMap = JSONObject.parseObject(indexTemplate, Map.class);
        Map<Integer, Integer> dataSizeMap = JSONObject.parseObject(dataSizeTemplate, Map.class);
        String index = indexMap.get(nameEnum.getCode());
        Integer dataSize = dataSizeMap.get(nameEnum.getCode());
        if (StringUtils.isBlank(index) || dataSize == null) {
            return;
        }

        // 1、获取总数
        Integer totalNum = searchTotalNumInEs(nameList, index);
        if (totalNum == 0) {
            return;
        }
        Integer maxNum = 0;
        Integer minNum = 0;
        if (StringUtils.isBlank(name)) {
            maxNum = commonAutoName.searchMinAndMaxNumInEs(SortOrder.DESC, index);
            minNum = commonAutoName.searchMinAndMaxNumInEs(SortOrder.ASC, index);
            log.info("===============最小数：{}，最大数：{}", minNum, maxNum);
        }

        if (minNum == 0 && totalNum == 0) {
            return;
        }

        // 2、获取配置
        // 2、获取配置
        CommonAutoNameSettingEntity info = commonAutoName.getAutoNameSettingInfo(nameEnum.getCode());


        long pageTotal = 1;
        int pageSize = 5000;
        if(totalNum % pageSize == 0){
            pageTotal = (maxNum - minNum) / pageSize;
        }else{
            pageTotal = (maxNum - minNum) / pageSize + 1;
        }

        for (int i = 0; i < pageTotal; i++) {
            List<FabSpuForAutoNameEsResp> respList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(nameList)) {
                Page page = new Page(1, 100);
                respList = searchDataForAutoNameInEs(nameList, page, index);
            } else {
                respList = commonAutoName.searchDataForAutoNameInEsForNum(
                        CommonSearchByPageAndIdEntity.build(minNum, i, maxNum ,pageSize, index));
            }

            if (CollectionUtils.isEmpty(respList)) {
                continue;
            }

            List<FabAutoNameInfo> partBatchInsert = new ArrayList<>();
            respList.forEach(v -> {
                List<FabSpuForAutoNameResp> partList = new ArrayList<>();
                List<FabSpuForAutoNameResp> wholeList = new ArrayList<>();
                final Integer[] charLen = {0};
                AtomicReference<String> dpp = new AtomicReference<>("");
                AtomicReference<String> designName = new AtomicReference<>("");

                CommonAutoNameGenerateEntity generateEntity = new CommonAutoNameGenerateEntity();
                generateEntity.setDpp(dpp);
                generateEntity.setDesignName(designName);
                generateEntity.setCharLen(charLen);
                generateEntity.setPartList(partList);
                generateEntity.setWholeList(wholeList);
                generateEntity.setForbiddenList(info.getForbiddenList());
                generateEntity.setFabAnDynamicFieldSettings(info.getFabAnDynamicFieldSettings());
                // 只有微商城的要根据当季往季获取配置信息
                List<FabAutoNameSetting> settings = info.getPartSortedList().stream()
                        .filter(u -> Objects.equals(u.getIsInCycle(), v.getProduct_life_cycle())).collect(Collectors.toList());
                generateEntity.setPartSortedList(AutoNameEnum.WSC == nameEnum ? settings : info.getPartSortedList());
                generateEntity.setSize(dataSize);
                generateEntity.setAutoNameEnum(nameEnum);
                generateEntity.setIsInCycle(v.getProduct_life_cycle());
                generateEntity.setResp(v);

                commonAutoName.getAutoGenerate(generateEntity, "");

                // 组装数据插入
                String partAutoName = partList.stream().sorted(Comparator.comparing(FabSpuForAutoNameResp::getAssembleWholeOrder))
                        .map(FabSpuForAutoNameResp::getDataValue).collect(Collectors.joining("")).toString();
                String wholeAutoName = wholeList.stream().sorted(Comparator.comparing(FabSpuForAutoNameResp::getAssembleWholeOrder))
                        .map(FabSpuForAutoNameResp::getDataValue).collect(Collectors.joining("")).toString();
                FabAutoNameInfo fabAutoNameInfo = new FabAutoNameInfo();
                fabAutoNameInfo.setName(v.getStyle_id());
                fabAutoNameInfo.setAutoNameWhole(wholeAutoName);
                fabAutoNameInfo.setAutoNameCalc(partAutoName);
                fabAutoNameInfo.setId(IdLeaf.getId(fabAutoNameInfoTag));
                fabAutoNameInfo.setUpdateTime(new Date());
                fabAutoNameInfo.setCreateTime(new Date());
                fabAutoNameInfo.setAutoType(nameEnum.getCode());
                partBatchInsert.add(fabAutoNameInfo);
            });

            // 操作插入语句
            if (CollectionUtils.isEmpty(partBatchInsert)) {
                return;
            }
            List<List<FabAutoNameInfo>> partition = com.google.common.collect.Lists.partition(partBatchInsert, 1000);
            partition.forEach(v -> {
                fabAutoNameInfoMapper.batchInsert(v);
            });


        }

    }

    /*private List<FabSpuForAutoNameEsResp> searchDataForAutoNameInEsForNum(Integer minNum, int i, Integer maxNum, int pageSize) {
        SearchRequest request = new SearchRequest();
        request.indices(index);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        int minPage = minNum;
        int maxPage = minNum + pageSize;
        if (i != 0) {
            minPage = minNum + i * pageSize;
            maxPage = minPage + pageSize;
            if (maxPage == maxNum) {
                maxPage = maxNum + 1;
            }
        }
        queryBuilder.must(QueryBuilders.rangeQuery("id").gte(minPage).lt(maxPage));
        sourceBuilder.sort("id", SortOrder.ASC);
        sourceBuilder.size(10000);
        sourceBuilder.query(queryBuilder);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品FAB_FOR_AUTO_NAME:{}", request.source().toString());
        List<FabSpuForAutoNameEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                FabSpuForAutoNameEsResp entity = FabSpuForAutoNameEsResp.fromJson(hit.getSourceAsString(), FabSpuForAutoNameEsResp.class);
                entity.setId(Integer.valueOf(hit.getId()));
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品FAB_FOR_AUTO_NAME异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }

    *//**
     * 获取最小或最大id
     * @return
     *//*
    private Integer searchMinAndMaxNumInEs(SortOrder sortOrder) {
        SearchRequest request = new SearchRequest();
        request.indices(fabAutoNameInfoIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(1);
        sourceBuilder.sort("id", sortOrder);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品searchMinNumInEs {}", request.source().toString());
        Integer minNum = 0;
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return 0;
            }
            SearchHit[] hits = response.getHits().getHits();
            FabSpuForAutoNameEsResp entity = FabSpuForAutoNameEsResp.fromJson(hits[0].getSourceAsString(), FabSpuForAutoNameEsResp.class);
            minNum = Integer.valueOf(entity.getId());
        } catch (IOException e) {
            log.error("查询商品searchMinNumInEs异常e = {}", e.getMessage());
            return 0;
        }
        return minNum;
    }


   */


    private List<FabSpuForAutoNameEsResp> searchDataForAutoNameInEs(List<String> nameList, Page page, String index) {
        SearchRequest request = new SearchRequest();
        request.indices(index);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        buildQuery(queryBuilder, nameList);

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1) * page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        sourceBuilder.sort("id", SortOrder.DESC);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品FAB_FOR_AUTO_NAME {}", request.source().toString());
        List<FabSpuForAutoNameEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                FabSpuForAutoNameEsResp entity = FabSpuForAutoNameEsResp.fromJson(hit.getSourceAsString(), FabSpuForAutoNameEsResp.class);
                entity.setId(Integer.valueOf(hit.getId()));
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品FAB_FOR_AUTO_NAME异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }





    private void buildQuery(BoolQueryBuilder queryBuilder, List<String> name) {
        if (CollectionUtils.isNotEmpty(name)) {
            queryBuilder.must(QueryBuilders.termsQuery("style_id.keyword", name));
        }
    }

    private Integer searchTotalNumInEs(List<String> name, String index) {
        if (CollectionUtils.isNotEmpty(name)) {
            return name.size();
        }

        SearchRequest request = new SearchRequest();
        request.indices(index);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.trackTotalHits(true);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品FAB_FOR_AUTO_NAME_TOTAL {}", request.source().toString());
        int entities = 0;
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return 0;
            }

            SearchHits hits = response.getHits();
            entities = Math.toIntExact(hits.getTotalHits().value);

        } catch (IOException e) {
            log.error("查询商品FAB_FOR_AUTO_NAME_TOTAL异常e = {}", e.getMessage());
            return 0;
        }
        return entities;
    }



}
