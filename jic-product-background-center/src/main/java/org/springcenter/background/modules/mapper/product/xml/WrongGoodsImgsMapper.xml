<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.WrongGoodsImgsMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.WrongGoodsImgs">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="stylingCode" column="STYLING_CODE" jdbcType="VARCHAR"/>
            <result property="skc" column="SKC" jdbcType="VARCHAR"/>
            <result property="productCode" column="PRODUCT_CODE" jdbcType="VARCHAR"/>
            <result property="colorNo" column="COLOR_NO" jdbcType="VARCHAR"/>
            <result property="neid" column="NEID" jdbcType="VARCHAR"/>
            <result property="nsid" column="NSID" jdbcType="VARCHAR"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,STYLING_CODE,SKC,
        PRODUCT_CODE,COLOR_NO,NEID,
        NSID,IS_DEL,CREATE_TIME,
        UPDATE_TIME
    </sql>
    <update id="updateIsDelByStylingCode">
        update         WRONG_GOODS_IMGS set is_del = #{isDel} where STYLING_CODE = #{stylingCode}
    </update>
    <select id="selectByNeId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from WRONG_GOODS_IMGS where NEID = #{neid} and IS_DEL = 0

    </select>
    <select id="selectByStylingCodes" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from WRONG_GOODS_IMGS
                where STYLING_CODE in
                      <foreach collection="stylingCodes" separator="," item="item" close=")" open="(">
                          #{item}
                      </foreach>
                  and IS_DEL = 0
    </select>
    <select id="selectBySkcsAndStylingCodeIsNull"  resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from WRONG_GOODS_IMGS
        where skc in
        <foreach collection="skcsAll" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        and STYLING_CODE is null
        and IS_DEL = 0
    </select>
</mapper>
