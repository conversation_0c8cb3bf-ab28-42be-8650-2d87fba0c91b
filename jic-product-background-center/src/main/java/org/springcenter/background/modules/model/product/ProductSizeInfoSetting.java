package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/12/21 16:48
 */
@Data
@TableName(value = "PRODUCT_SIZE_INFO_SETTING")
public class ProductSizeInfoSetting {

    @TableId
    private Integer id;

    @TableField(value = "SETTING_ID")
    private Integer settingId;

    @TableField(value = "SETTING_NAME")
    private String settingName;

    @TableField(value = "TYPE")
    @ApiModelProperty(value = "0品牌 1小类")
    private Integer type;

    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;
}
