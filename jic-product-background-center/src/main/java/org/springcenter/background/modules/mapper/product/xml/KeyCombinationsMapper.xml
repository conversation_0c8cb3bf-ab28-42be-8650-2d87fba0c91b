<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.KeyCombinationsMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.KeyCombinations">
    <result column="ID" property="id" />
    <result column="GOODSEASON" property="goodSeason" />
    <result column="BRAND_CODE" property="brandCode" />
    <result column="BRAND" property="brand" />
    <result column="SUIT_NUM" property="suitNum" />
    <result column="MODEL_NUMBER" property="modelNumber" />
    <result column="SAMPLE_CODE" property="sampleCode" />
    <result column="SAMPLE_SKC_CODE" property="sampleSkcCode" />
    <result column="NAME" property="name" />
    <result column="SKC" property="skc" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="IS_DELETED" property="isDeleted" />
  </resultMap>

  <sql id="Base_Column_List">
    ID, GOODSEASON, BRAND_CODE, BRAND, SUIT_NUM, MODEL_NUMBER, SAMPLE_CODE,
       SAMPLE_SKC_CODE, NAME, SKC, CREATE_TIME, UPDATE_TIME, IS_DELETED
  </sql>


  <select id="selectKeyCombinationsBySuit"
          resultType="org.springcenter.background.modules.entity.productFab.KeyInfoParamsEntity">
        SELECT GOODSEASON as goodSeason, BRAND_CODE as brandCode, SUIT_NUM as suitNum, NAME as name
        FROM KEY_COMBINATIONS WHERE NAME = #{name} AND IS_DELETED = 0
  </select>


  <select id="selectByBrandAndGoodSeasonAndSuit" resultType="org.springcenter.background.modules.entity.productFab.KeyInfoSpuEntity">
        SELECT SUIT_NUM as suitNum, NAME as name, GOODSEASON as goodSeason, BRAND_CODE as brandCode, SKC as skc
            FROM KEY_COMBINATIONS
        WHERE IS_DELETED = 0
        <if test="name != null and name != ''">
              AND NAME <![CDATA[ <> ]]> #{name}
        </if>
        and GOODSEASON in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
          #{item.goodSeason}
        </foreach>
        and BRAND_CODE in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
          #{item.brandCode}
        </foreach>
        and SUIT_NUM in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
          #{item.suitNum}
        </foreach>
  </select>

    <select id="selectKeyCombinationsByNames" resultType="org.springcenter.background.modules.entity.productFab.KeyInfoParamsEntity">
        SELECT GOODSEASON as goodSeason, BRAND_CODE as brandCode, SUIT_NUM as suitNum, NAME as name
            FROM KEY_COMBINATIONS
        WHERE IS_DELETED = 0
        AND NAME in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by SUIT_NUM desc
    </select>


</mapper>