package org.springcenter.background.modules.service;

import org.springcenter.product.api.dto.background.fab.PatentInfoResp;

/**
 * <AUTHOR>
 */
public interface IProductPatentService {

    /**
     * 批量导入专利信息
     * @param url 链接
     * @param keys key
     * @param type 操作人
     * @return 返回错误链接
     */
    String importProductPatentInfo(String url, String keys, String type);

    /**
     * 根据商品id获取专利信息
     * @param requestData 商品id
     * @return 返回
     */
    PatentInfoResp queryFabDetailPatentInfo(String requestData);
}
