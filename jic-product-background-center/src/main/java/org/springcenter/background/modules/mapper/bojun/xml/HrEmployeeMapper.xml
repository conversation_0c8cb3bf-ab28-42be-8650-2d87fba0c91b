<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.bojun.HrEmployeeMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.bojun.HrEmployee">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="AD_CLIENT_ID" jdbcType="DECIMAL" property="adClientId" />
    <result column="AD_ORG_ID" jdbcType="DECIMAL" property="adOrgId" />
    <result column="NO" jdbcType="VARCHAR" property="no" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="PIC" jdbcType="VARCHAR" property="pic" />
    <result column="C_DEPARTMENT_ID" jdbcType="DECIMAL" property="cDepartmentId" />
    <result column="HR_ROLE_ID" jdbcType="DECIMAL" property="hrRoleId" />
    <result column="HR_TITLE_ID" jdbcType="DECIMAL" property="hrTitleId" />
    <result column="HR_POST_ID" jdbcType="DECIMAL" property="hrPostId" />
    <result column="INCUMBENCY_STS" jdbcType="CHAR" property="incumbencySts" />
    <result column="WORK_FORM" jdbcType="CHAR" property="workForm" />
    <result column="BEGIN_DATE" jdbcType="DECIMAL" property="beginDate" />
    <result column="END_DATE" jdbcType="DECIMAL" property="endDate" />
    <result column="CONTRACT_START" jdbcType="DECIMAL" property="contractStart" />
    <result column="CONTRACT_END" jdbcType="DECIMAL" property="contractEnd" />
    <result column="FULL_MEMBER_DATE" jdbcType="DECIMAL" property="fullMemberDate" />
    <result column="RETIRE_DATE" jdbcType="DECIMAL" property="retireDate" />
    <result column="WORK_LENGTH" jdbcType="VARCHAR" property="workLength" />
    <result column="EMPLOYEE_FN" jdbcType="VARCHAR" property="employeeFn" />
    <result column="BANK_NAME" jdbcType="VARCHAR" property="bankName" />
    <result column="BANK_ACCOUNT" jdbcType="VARCHAR" property="bankAccount" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="SEX" jdbcType="CHAR" property="sex" />
    <result column="C_REGION_ID" jdbcType="DECIMAL" property="cRegionId" />
    <result column="NATION" jdbcType="VARCHAR" property="nation" />
    <result column="MARRIAGE" jdbcType="CHAR" property="marriage" />
    <result column="POLITY" jdbcType="VARCHAR" property="polity" />
    <result column="BIRTHDATE" jdbcType="DECIMAL" property="birthdate" />
    <result column="ID_CARD" jdbcType="VARCHAR" property="idCard" />
    <result column="HUKOU" jdbcType="VARCHAR" property="hukou" />
    <result column="DOMICILE_ADD" jdbcType="VARCHAR" property="domicileAdd" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="POSTALCODE" jdbcType="VARCHAR" property="postalcode" />
    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="HANDSET" jdbcType="VARCHAR" property="handset" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="EDUCATION" jdbcType="CHAR" property="education" />
    <result column="HR_SPECIALITY_ID" jdbcType="DECIMAL" property="hrSpecialityId" />
    <result column="SCHOOL" jdbcType="VARCHAR" property="school" />
    <result column="GRADUATE_DATE" jdbcType="DECIMAL" property="graduateDate" />
    <result column="C_LANGUAGE1_ID" jdbcType="DECIMAL" property="cLanguage1Id" />
    <result column="LEVEL1" jdbcType="CHAR" property="level1" />
    <result column="C_LANGUAGE2_ID" jdbcType="DECIMAL" property="cLanguage2Id" />
    <result column="LEVEL2" jdbcType="CHAR" property="level2" />
    <result column="COMPUTER_LEVEL" jdbcType="CHAR" property="computerLevel" />
    <result column="BODY_STS" jdbcType="VARCHAR" property="bodySts" />
    <result column="HEIGHT" jdbcType="VARCHAR" property="height" />
    <result column="WEIGHT" jdbcType="VARCHAR" property="weight" />
    <result column="EYESIGHT" jdbcType="VARCHAR" property="eyesight" />
    <result column="BLOOD_TYPE" jdbcType="VARCHAR" property="bloodType" />
    <result column="BIO" jdbcType="VARCHAR" property="bio" />
    <result column="ACCESSORIES" jdbcType="VARCHAR" property="accessories" />
    <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
    <result column="MODIFIERID" jdbcType="DECIMAL" property="modifierid" />
    <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
    <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
    <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
    <result column="USER_ID" jdbcType="DECIMAL" property="userId" />
    <result column="CONTRACTURL" jdbcType="VARCHAR" property="contracturl" />
    <result column="C_STORE_ID" jdbcType="DECIMAL" property="cStoreId" />
    <result column="ISSALER" jdbcType="CHAR" property="issaler" />
    <result column="ISOPR" jdbcType="CHAR" property="isopr" />
    <result column="C_CUSTOMER_ID" jdbcType="DECIMAL" property="cCustomerId" />
    <result column="C_CUSTOMERUP_ID" jdbcType="DECIMAL" property="cCustomerupId" />
    <result column="ISSTORER" jdbcType="CHAR" property="isstorer" />
    <result column="BASE_WAGE" jdbcType="DECIMAL" property="baseWage" />
    <result column="DISCOUNTLIMIT" jdbcType="DECIMAL" property="discountlimit" />
    <result column="BIGAREAMNG_ID" jdbcType="DECIMAL" property="bigareamngId" />
    <result column="EMPTYPE" jdbcType="VARCHAR" property="emptype" />
    <result column="PDMTYPE" jdbcType="VARCHAR" property="pdmtype" />
    <result column="TIMECARDNO" jdbcType="VARCHAR" property="timecardno" />
    <result column="C_SALESTORE_ID" jdbcType="DECIMAL" property="cSalestoreId" />
    <result column="HR_PSN_ID" jdbcType="DECIMAL" property="hrPsnId" />
    <result column="IS_CONPEO" jdbcType="CHAR" property="isConpeo" />
    <result column="IS_CAN_EBSOOUT" jdbcType="CHAR" property="isCanEbsoout" />
    <result column="YG_ID" jdbcType="DECIMAL" property="ygId" />
    <result column="IS_PUBLICSHOPPERS" jdbcType="CHAR" property="isPublicshoppers" />
    <result column="CHECKPERSON" jdbcType="CHAR" property="checkperson" />
    <result column="CHECKCODE" jdbcType="VARCHAR" property="checkcode" />
    <result column="EHR_ID" jdbcType="DECIMAL" property="ehrId" />
    <result column="EHR_ROLEID" jdbcType="DECIMAL" property="ehrRoleid" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, AD_CLIENT_ID, AD_ORG_ID, NO, NAME, PIC, C_DEPARTMENT_ID, HR_ROLE_ID, HR_TITLE_ID, 
    HR_POST_ID, INCUMBENCY_STS, WORK_FORM, BEGIN_DATE, END_DATE, CONTRACT_START, CONTRACT_END, 
    FULL_MEMBER_DATE, RETIRE_DATE, WORK_LENGTH, EMPLOYEE_FN, BANK_NAME, BANK_ACCOUNT, 
    DESCRIPTION, SEX, C_REGION_ID, NATION, MARRIAGE, POLITY, BIRTHDATE, ID_CARD, HUKOU, 
    DOMICILE_ADD, ADDRESS, POSTALCODE, PHONE, HANDSET, EMAIL, EDUCATION, HR_SPECIALITY_ID, 
    SCHOOL, GRADUATE_DATE, C_LANGUAGE1_ID, LEVEL1, C_LANGUAGE2_ID, LEVEL2, COMPUTER_LEVEL, 
    BODY_STS, HEIGHT, WEIGHT, EYESIGHT, BLOOD_TYPE, BIO, ACCESSORIES, OWNERID, MODIFIERID, 
    CREATIONDATE, MODIFIEDDATE, ISACTIVE, USER_ID, CONTRACTURL, C_STORE_ID, ISSALER, 
    ISOPR, C_CUSTOMER_ID, C_CUSTOMERUP_ID, ISSTORER, BASE_WAGE, DISCOUNTLIMIT, BIGAREAMNG_ID, 
    EMPTYPE, PDMTYPE, TIMECARDNO, C_SALESTORE_ID, HR_PSN_ID, IS_CONPEO, IS_CAN_EBSOOUT, 
    YG_ID, IS_PUBLICSHOPPERS, CHECKPERSON, CHECKCODE, EHR_ID, EHR_ROLEID
  </sql>
  <select id="selectEmployeeBaseById" resultType="java.lang.Long">
    SELECT b.ID
    FROM HR_EMPLOYEE a
    LEFT JOIN EMPLOYEE_BASE b ON a.EHR_ID = b.LINKID
    WHERE a.ISACTIVE = 'Y'
    AND a.ID = #{hrId}
  </select>

  <select id="selectRetailByNo" resultType="org.springcenter.background.modules.entity.RetailEntity">
    SELECT
      mr.id,DOCNO AS docno ,mr.TOT_AMT_ACTUAL AS totAmtActual,mr.AVG_DISCOUNT AS avgDiscount, mr.C_VIP_ID AS cVipId,ccv.CARDNO AS cardNo
    FROM
      NEANDS3.M_RETAIL mr
        LEFT JOIN
      NEANDS3.C_CLIENT_VIP ccv
      ON
        mr.C_VIP_ID = ccv.ID
    WHERE
      mr.DOCNO = #{docno} AND rownum = 1
  </select>
</mapper>