package org.springcenter.background.modules.service.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.common.util.excel.IWriteDataExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsSetQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.background.modules.entity.productFab.CellSizeEntity;
import org.springcenter.background.modules.entity.productFab.SearchSizeInfoEntityReq;
import org.springcenter.background.modules.enums.BrandBrandEnum;
import org.springcenter.background.modules.mapper.product.ProductSizeInfoCellSettingMapper;
import org.springcenter.background.modules.mapper.product.ProductSizeInfoSettingMapper;
import org.springcenter.background.modules.model.product.ProductSizeInfoCellSetting;
import org.springcenter.background.modules.model.product.ProductSizeInfoSetting;
import org.springcenter.background.modules.service.IProductSizeInfoService;
import org.springcenter.background.modules.util.EsUtil;
import org.springcenter.background.modules.util.SizeUtil;
import org.springcenter.product.api.dto.ExportSizeInfoDataReq;
import org.springcenter.product.api.dto.ProductFabInfoReq;
import org.springcenter.product.api.dto.ProductSizeInfoEsResp;
import org.springcenter.product.api.dto.ProductSizeInfoResp;
import org.springcenter.product.api.dto.back.ProductFabInfoSizeReq;
import org.springcenter.product.api.dto.back.ProductFabInfoSizeResp;
import org.springcenter.product.api.enums.BrandSizeInfoEntityEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import strman.Strman;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/12/21 17:00
 */
@Service
@Slf4j
@RefreshScope
public class ProductSizeInfoServiceImpl implements IProductSizeInfoService {

    @Autowired
    private ProductSizeInfoCellSettingMapper productSizeInfoCellSettingMapper;

    @Autowired
    private ProductSizeInfoSettingMapper productSizeInfoSettingMapper;

    @Value("${product.size.info.index}")
    private String productSizeInfoIndex;

    @Autowired
    private EsUtil esUtil;

    @Value("${first.cell.one}")
    private String firstCellOne;

    @Value("${first.cell.two}")
    private String firstCellTwo;

    @Autowired
    private QiniuUtil qiniuUtil;

    @Autowired
    private RedisPoolUtil redisPoolUtil;

    @Value(value = "${product.size.head.str}")
    private String sizeInfoHeadStr;

    @Value(value = "${product.size.head.sec.str}")
    private String sizeInfoHeadSecStr;

    @Value(value = "${product.size.head.foc.str}")
    private String sizeInfoHeadForStr;

    @Override
    public List<ProductSizeInfoResp> queryFabSizeInfo(ProductFabInfoReq requestData) {
        List<ProductSizeInfoResp> list = new ArrayList<>();
        // 查询当前商品的尺码信息
        SearchSizeInfoEntityReq req = new SearchSizeInfoEntityReq();
        req.setProductId(requestData.getProductId());
        List<ProductSizeInfoEsResp> sizeInfoEsResps = searchProductSizeInfoInEs(req);
        if (CollectionUtils.isEmpty(sizeInfoEsResps)) {
            return list;
        }

        // 获取当前品牌和当前小类的数据 过滤需要的部位
        List<ProductSizeInfoSetting> settingInfo = productSizeInfoSettingMapper
                .selectBrandAndSmallClassInfo(sizeInfoEsResps.get(0).getC_arcbrand_id(), sizeInfoEsResps.get(0).getSmall_class_id());
        Integer brandSettingId = null;
        Integer smallClassSettingId = null;
        if (CollectionUtils.isNotEmpty(settingInfo)) {
            ProductSizeInfoSetting brandSetting = settingInfo.stream().filter(v -> Objects.equals(v.getType(), 0)).findFirst().orElse(null);
            brandSettingId = brandSetting == null ? brandSettingId : brandSetting.getId();
            ProductSizeInfoSetting smallClassSetting = settingInfo.stream().filter(v -> Objects.equals(v.getType(), 1)).findFirst().orElse(null);
            smallClassSettingId = smallClassSetting == null ? smallClassSettingId : smallClassSetting.getId();
        }

        List<ProductSizeInfoCellSetting> cellSettings = new ArrayList<>();
        if (smallClassSettingId != null && brandSettingId != null) {
            cellSettings = productSizeInfoCellSettingMapper.selectByBandAndSmallClassIds(brandSettingId, Lists.newArrayList(smallClassSettingId));
            cellSettings = cellSettings.stream().sorted(Comparator.comparing(ProductSizeInfoCellSetting::getId)).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(cellSettings)) {
            // 若没有则 一期先不处理
            return list;

        } else {
            // 若有 则部位名称按照配置表的给
            sizeInfoEsResps = sizeInfoEsResps.stream()
                    .sorted(Comparator.comparing(ProductSizeInfoEsResp::getStylepartsize_model)).collect(Collectors.toList());

            // 处理双部位 重写表头
            Map<String, List<ProductSizeInfoEsResp>> partCellMap = sizeInfoEsResps.stream().collect(Collectors.groupingBy(ProductSizeInfoEsResp::getPart));
            if (MapUtils.isEmpty(partCellMap)) {
                return list;
            }
            List<CellSizeEntity> cellSizeEntities = new ArrayList<>();
            cellSettings.forEach(v -> {
                List<ProductSizeInfoEsResp> productSizeInfoEsResps = partCellMap.get(v.getSystemFiled());
                if (CollectionUtils.isEmpty(productSizeInfoEsResps)) {
                    return;
                }

                List<CellSizeEntity> list1 = productSizeInfoEsResps.stream().map(x -> {
                    CellSizeEntity cell = new CellSizeEntity();
                    cell.setSystemFiled(x.getPart());
                    cell.setClff(x.getClff());
                    return cell;
                }).distinct().collect(Collectors.toList());
                List<ProductSizeInfoEsResp> esResps = partCellMap.get(v.getSystemFiled());


                if (CollectionUtils.isNotEmpty(list1) && list1.size() > 1) {
                    list1.forEach(s -> {
                        CellSizeEntity cellSizeEntity = new CellSizeEntity();
                        cellSizeEntity.setOutsideFiled(v.getOutsideFiled());
                        cellSizeEntity.setSystemFiled(v.getSystemFiled());
                        cellSizeEntity.setClff(s.getClff());
                        cellSizeEntity.setIsDouble(list1.size() > 1 ? true : false);
                        cellSizeEntity.setSmallClassId(s.getSmallClassId());
                        cellSizeEntities.add(cellSizeEntity);
                    });
                } else {
                    CellSizeEntity cellSizeEntity = new CellSizeEntity();
                    cellSizeEntity.setOutsideFiled(v.getOutsideFiled());
                    cellSizeEntity.setSystemFiled(v.getSystemFiled());
                    cellSizeEntity.setClff(esResps.get(0).getClff());
                    cellSizeEntity.setIsDouble(false);
                    cellSizeEntity.setSmallClassId(v.getSmallClassId());
                    cellSizeEntities.add(cellSizeEntity);
                }

            });

            if (CollectionUtils.isEmpty(cellSizeEntities)) {
                return list;
            }


            // 头部说明
            List<String> model = sizeInfoEsResps.stream().map(ProductSizeInfoEsResp::getStylepartsize_model)
                    .distinct().collect(Collectors.toList());
            List<String> firstHead = new ArrayList<>();
            firstHead.add(firstCellOne);
            firstHead.add(firstCellTwo);
            firstHead.addAll(model);
            ProductSizeInfoResp infoResp = new ProductSizeInfoResp();
            infoResp.setLineNum(0);
            infoResp.setLineInfo(firstHead);
            list.add(infoResp);

            // 每行下面的字段值
            AtomicInteger i = new AtomicInteger(1);
            // 处理部位的map
            Map<String, List<ProductSizeInfoEsResp>> partMap = sizeInfoEsResps.stream().collect(Collectors.groupingBy(ProductSizeInfoEsResp::getPart));
            if (CollectionUtils.isEmpty(partMap)) {
                return list;
            }
            cellSizeEntities.forEach(v -> {
                ProductSizeInfoResp lineInfoResp = new ProductSizeInfoResp();
                lineInfoResp.setLineNum(i.get());
                List<String> lineInfo = new ArrayList<>();
                lineInfo.add(v.getOutsideFiled());

                // 根据部位塞值
                List<ProductSizeInfoEsResp> productSizeInfoEsResps = partMap.get(v.getSystemFiled());

                // 特殊处理 如果品类为裤子、连体衣 没有裤腿围取横档宽
                if ((Objects.equals(v.getSmallClassId(), 23) || Objects.equals(v.getSmallClassId(), 26)) &&
                        (Objects.equals(v.getOutsideFiled(), "大腿围") || Objects.equals(v.getOutsideFiled(), "裤腿围"))) {
                    productSizeInfoEsResps = partMap.get("裤腿围");
                    if (CollectionUtils.isEmpty(productSizeInfoEsResps)) {
                        productSizeInfoEsResps = partMap.get("横档宽");
                    }
                }

                if (CollectionUtils.isEmpty(productSizeInfoEsResps)) {
                    lineInfo.add("");
                    return;
                }
                lineInfo.add(v.getClff());
                // 处理成尺寸的map
                Map<String, ProductSizeInfoEsResp> sizeMap = productSizeInfoEsResps.stream()
                        .filter(x -> Objects.equals(x.getClff(), v.getClff()))
                        .collect(HashMap::new, (k, u) -> k.put(u.getStylepartsize_model(), u), HashMap::putAll);
                if (CollectionUtils.isEmpty(sizeMap)) {
                    return;
                }

                // 填数
                model.forEach(x -> lineInfo.add(sizeMap.get(x).getSize_num()));
                lineInfoResp.setLineInfo(lineInfo);
                list.add(lineInfoResp);
                i.getAndIncrement();
            });
        }
        return list;
    }


    @Override
    public void exportSizeInfoData(String keys, ExportSizeInfoDataReq requestData) {

        // 判断当前是否为空 且 都只为一个参数
        if (CollectionUtils.isEmpty(requestData.getBrandIds())) {
            throw new RuntimeException("品牌 必传");
        }

        // 判断当前年份、波段、品牌是否只有一个
        if (requestData.getBrandIds().size() > 1) {
            throw new RuntimeException("生成产品册时，品牌只允许选一个");
        }

        // 对品牌需要处理 如果品牌为4则塞名字brandName童装 如果是为41则是婴童
        // 对入参品牌进行处理进行处理 婴童和童装需要传品牌名称
        if (CollectionUtils.isNotEmpty(requestData.getBrandIds())) {
            List<BrandBrandEnum> brandInfoByCode = BrandBrandEnum.getBrandInfoByCode(requestData.getBrandIds());
            if (CollectionUtils.isNotEmpty(brandInfoByCode)) {
                List<String> names = new ArrayList<>();
                brandInfoByCode.stream().forEach(v -> {
                    names.addAll(v.getDesc());
                });
                requestData.setBrandNames(names);
                requestData.setBrandIds(brandInfoByCode.stream().map(v -> v.getInsideCode()).collect(Collectors.toList()));
            }
        }

        // 根据条件查询es
        SearchSizeInfoEntityReq req = new SearchSizeInfoEntityReq();
        BeanUtils.copyProperties(requestData, req);
        List<ProductSizeInfoEsResp> reps = searchProductSizeInfoInEs(req);
        if (CollectionUtils.isEmpty(reps)) {
            log.info("=============未找到当前波段年份品牌的信息：{}", JSONObject.toJSONString(requestData));
            return;
        }
        reps = reps.stream().filter(v -> StringUtils.isNotBlank(v.getStylepartsize_model())).collect(Collectors.toList());


        // 查询当前品牌和所有波段 对应的部位信息
        List<ProductSizeInfoSetting> settingInfo = productSizeInfoSettingMapper.selectBrandIdAndCategory(req.getBrandIds());
        if (CollectionUtils.isEmpty(settingInfo)) {
            return;
        }
        List<Integer> smallIds = settingInfo.stream().filter(v -> Objects.equals(v.getType(), 1))
                .map(ProductSizeInfoSetting::getId).collect(Collectors.toList());
        Integer brandId = settingInfo.stream().filter(v -> Objects.equals(v.getType(), 0))
                .map(ProductSizeInfoSetting::getId).findFirst().orElse(null);
        if (CollectionUtils.isEmpty(smallIds) || brandId == null) {
            return;
        }

        HashMap<Integer, ProductSizeInfoSetting> smallClassMap = settingInfo.stream().filter(v -> Objects.equals(v.getType(), 1))
                .collect(HashMap::new, (k, v) -> k.put(v.getId(), v), HashMap::putAll);

        List<ProductSizeInfoCellSetting> cellSettings = productSizeInfoCellSettingMapper.selectByBandAndSmallClassIds(brandId, smallIds);
        if (CollectionUtils.isEmpty(cellSettings)) {
            return;
        }
        cellSettings.forEach(v -> {
            if (CollectionUtils.isNotEmpty(smallClassMap) && smallClassMap.get(v.getSmallClassId()) != null) {
                v.setSmallClassId(smallClassMap.get(v.getSmallClassId()).getSettingId());
            }
        });

        Map<Integer, List<ProductSizeInfoCellSetting>> partMap = cellSettings.stream()
                .collect(Collectors.groupingBy(ProductSizeInfoCellSetting::getSmallClassId));
        Map<Integer, List<String>> filterPartMap = new HashMap<>();
        partMap.forEach((key, value) -> filterPartMap
                .put(key, value.stream().map(ProductSizeInfoCellSetting::getSystemFiled).collect(Collectors.toList())));



        // 对内对外名称
        HashMap<String, ProductSizeInfoCellSetting> partNameMap = cellSettings.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getSystemFiled() + "@" + v.getSmallClassId() , v), HashMap::putAll);

        // 处理当前类根据
        Map<String, Map<String, List<ProductSizeInfoEsResp>>> dealMap = reps.stream()
                .collect(Collectors.groupingBy(ProductSizeInfoEsResp::getStyle_id,
                Collectors.groupingBy(ProductSizeInfoEsResp::getPart)));

        // 或者当前返回体
        BrandSizeInfoEntityEnum entityEnum = BrandSizeInfoEntityEnum.getClassByBrands(requestData.getBrandIds().get(0), requestData.getBrandNames());
        if (entityEnum == null) {
            return;
        }

        HashMap<String, Integer> spuSmallClassMap = reps.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getStyle_id(), v.getSmall_class_id()), HashMap::putAll);


        String fileName = System.currentTimeMillis() +  ".xlsx";
        EasyExcelUtil.write(fileName, entityEnum.getCla(), new IWriteDataExcel<Object>() {
            List<Object> list = new ArrayList<>();
            public List<Object> getData() {


                for (String spu : dealMap.keySet()) {

                    Map<String, List<ProductSizeInfoEsResp>> partSizeInfos = dealMap.get(spu);

                    Map<String, Integer> needSkipOutPart = new HashMap<>();
                    for (String partName : partSizeInfos.keySet()) {
                        ProductSizeInfoCellSetting cellSetting = partNameMap.get(partName + "@" + spuSmallClassMap.get(spu));
                        if (cellSetting == null) {
                            continue;
                        }
                        if (needSkipOutPart.get(cellSetting.getOutsideFiled()) != null
                                && !Objects.equals(needSkipOutPart.get(cellSetting.getOutsideFiled()), cellSetting.getSort())) {
                            continue;
                        }

                        needSkipOutPart.put(cellSetting.getOutsideFiled(), cellSetting.getSort());

                        List<ProductSizeInfoEsResp> sizeInfos = partSizeInfos.get(partName);

                        try {
                            // 处理双部位
                            List<CellSizeEntity> list1 = sizeInfos.stream().map(x -> {
                                CellSizeEntity cell = new CellSizeEntity();
                                cell.setSystemFiled(x.getPart());
                                cell.setClff(x.getClff());
                                return cell;
                            }).distinct().collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(list1)) {
                                continue;
                            }

                            if (list1.size() > 1) {
                                list1.forEach(v -> {
                                    Map<String, String> map = sizeInfos.stream().filter(x -> Objects.equals(x.getClff(), v.getClff()))
                                            .collect(HashMap::new, (k, t) -> k.put(Objects.equals(t.getStylepartsize_model(), "/") ?
                                                    "/" : StringUtils.split(t.getStylepartsize_model(), "/")[0], t.getSize_num()), HashMap::putAll);
                                    Object o = null;
                                    try {
                                        o = getObject(sizeInfos.stream().filter(x -> Objects.equals(x.getClff(), v.getClff()))
                                                .collect(Collectors.toList()), map, entityEnum, cellSetting);
                                    } catch (Exception e) {
                                        throw new RuntimeException(e);
                                    }
                                    if (o == null) return;
                                    list.add(o);
                                });

                            } else {
                                Map<String, String> map = sizeInfos.stream()
                                        .collect(HashMap::new, (k, t) ->
                                                        k.put(Objects.equals(t.getStylepartsize_model(), "/") ?
                                                                "/" : StringUtils.split(t.getStylepartsize_model(), "/")[0], t.getSize_num()),
                                                HashMap::putAll);
                                Object o = getObject(sizeInfos, map, entityEnum, cellSetting);
                                if (o == null) continue;
                                list.add(o);
                            }

                        } catch (InstantiationException | IllegalAccessException e) {
                            log.error("=====================导出尺码信息报错");
                            throw new RuntimeException(e);
                        }

                    }



                }





                return list;
            }
        });
        File file = new File(fileName);
        String exportUrl = qiniuUtil.upload(file.getPath(), "尺码信息" + fileName);
        file.delete();
        RedisTemplateUtil.setex(redisPoolUtil, keys, exportUrl,60);
        log.info("====================导出尺码信息的exportUrl:{}, key:{}", JSONObject.toJSONString(exportUrl), keys);
    }

    @Override
    public ProductFabInfoSizeResp queryFabSizeInfoNew(ProductFabInfoSizeReq requestData) {
        ProductFabInfoSizeResp resp = new ProductFabInfoSizeResp();
        List<ProductFabInfoSizeResp.SizeCliffInfo> retCliffInfos = new ArrayList<>();

        // 查询当前商品的尺码信息
        SearchSizeInfoEntityReq req = new SearchSizeInfoEntityReq();
        req.setProductId(requestData.getProductId());
        List<ProductSizeInfoEsResp> sizeInfoEsResps = searchProductSizeInfoInEs(req);
        if (CollectionUtils.isEmpty(sizeInfoEsResps)) {
            return resp;
        }

        // 获取当前品牌和当前小类的数据 过滤需要的部位
        List<ProductSizeInfoSetting> settingInfo = productSizeInfoSettingMapper
                .selectBrandAndSmallClassInfo(sizeInfoEsResps.get(0).getC_arcbrand_id(), sizeInfoEsResps.get(0).getSmall_class_id());


        // 获取品牌和品类id
        Integer brandSettingId = null;
        Integer smallClassSettingId = null;
        if (CollectionUtils.isNotEmpty(settingInfo)) {
            ProductSizeInfoSetting brandSetting = settingInfo.stream().filter(v -> Objects.equals(v.getType(), 0)).findFirst().orElse(null);
            brandSettingId = brandSetting == null ? brandSettingId : brandSetting.getId();
            ProductSizeInfoSetting smallClassSetting = settingInfo.stream().filter(v -> Objects.equals(v.getType(), 1)).findFirst().orElse(null);
            smallClassSettingId = smallClassSetting == null ? smallClassSettingId : smallClassSetting.getId();
        }

        // 获取配置的表头信息
        List<ProductSizeInfoCellSetting> cellSettings = new ArrayList<>();
        if (smallClassSettingId != null && brandSettingId != null) {
            cellSettings = productSizeInfoCellSettingMapper.selectByBandAndSmallClassIds(brandSettingId, Lists.newArrayList(smallClassSettingId));
            cellSettings = cellSettings.stream().sorted(Comparator.comparing(ProductSizeInfoCellSetting::getId)).collect(Collectors.toList());
            // 特殊处理 若当前的xiu为插肩袖 则不取肩宽信息
            if (StringUtils.isNotBlank(sizeInfoEsResps.get(0).getXiu()) && sizeInfoEsResps.get(0).getXiu().contains("插肩袖")) {
                cellSettings = cellSettings.stream().filter(v -> !Objects.equals(v.getOutsideFiled(), "肩宽")).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(cellSettings)) {return resp;}


        // 处理获取行数据
        // 如果是婴童
        List<String> model = new ArrayList<>();
        if (Objects.equals(sizeInfoEsResps.get(0).getC_arcbrand_id(), 4)) {
            List<String> sizeInfos = sizeInfoEsResps.stream()
                    .filter(v -> StringUtils.isNotBlank(v.getSize_num()))
                    .filter(v -> StringUtils.isNotBlank(v.getStylepartsize_model()))
                    .map(ProductSizeInfoEsResp::getStylepartsize_model)
                    .distinct().collect(Collectors.toList());
            model = SizeUtil.getStylepartsize_modelPx(sizeInfos);
        } else {
            model = sizeInfoEsResps.stream()
                    .filter(v -> StringUtils.isNotBlank(v.getSize_num()))
                    .filter(v -> StringUtils.isNotBlank(v.getStylepartsize_model()))
                    .map(ProductSizeInfoEsResp::getStylepartsize_model)
                    .distinct().sorted().collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(model)) {
            return resp;
        }

        // 获取所有尺码对应数据各个部位的数据
        Map<String, List<ProductSizeInfoEsResp>> sizeMap = sizeInfoEsResps.stream()
                .filter(v -> StringUtils.isNotBlank(v.getStylepartsize_model()))
                .collect(Collectors.groupingBy(ProductSizeInfoEsResp::getStylepartsize_model));
        if (MapUtils.isEmpty(sizeMap)) {
            return resp;
        }
        // 各个尺码对应的双部位对应的数据
        HashMap<String, Map<String, List<ProductSizeInfoEsResp>>> sizePartMap = new HashMap<>();
        sizeMap.forEach((key, value1) -> {
            Map<String, List<ProductSizeInfoEsResp>> value = value1.stream()
                    .filter(v -> StringUtils.isNotBlank(v.getSize_num()))
                    .collect(Collectors.groupingBy(ProductSizeInfoEsResp::getPart));
            sizePartMap.put(key, value);
        });
        if (MapUtils.isEmpty(sizePartMap)) {
            return resp;
        }
        // {"系统名称":{"部位说明1", "部位说名2"}}
        Map<String, Set<String>> map = new HashMap<>();
        // {"尺码"：{"系统名称@部位说明": "尺寸", "系统名称@部位说明2":"尺寸2"}}
        Map<String, Map<String, String>> size2PartClffMap = new HashMap<>();
        for (ProductSizeInfoEsResp sizeInfoEsResp : sizeInfoEsResps) {
            sizeInfoEsResp.setClff(StringUtils.defaultIfBlank(sizeInfoEsResp.getClff(), ""));
            //根据part 获取 该part下的clff数据
            Set<String> clffSet = map.get(sizeInfoEsResp.getPart());
            if (clffSet == null) {
                clffSet = new HashSet<>();
                map.put(sizeInfoEsResp.getPart(), clffSet);
            }
            clffSet.add(sizeInfoEsResp.getClff());


            //将尺寸对应partclff数据记录一下
            Map<String, String> partClffMap = size2PartClffMap.get(sizeInfoEsResp.getStylepartsize_model());
            if (partClffMap == null) {
                partClffMap = new HashMap<>();
                size2PartClffMap.put(sizeInfoEsResp.getStylepartsize_model(), partClffMap);
            }
            partClffMap.put(sizeInfoEsResp.getPart() + "@" + StringUtils.defaultIfBlank(sizeInfoEsResp.getClff(), ""), sizeInfoEsResp.getSize_num());

        }

        List<String> title = new ArrayList<>();
        if (requestData.getIsNeedCliff()) {
            title.add(sizeInfoHeadStr);
        } else {
            title.add(sizeInfoHeadForStr);
        }
        List<String> clffTitle = new ArrayList<>();
        clffTitle.add(sizeInfoHeadSecStr);

        List<List<String>> sizeTableInfo = new ArrayList<>();
        for (String m : model) {
            List<String> sizeTableRow = new ArrayList<>();
            sizeTableRow.add(m);

            sizeTableInfo.add(sizeTableRow);
        }

        HashMap<String, Integer> needSkipPart = new HashMap<>();
        // {"系统名称":{"部位说明1", "部位说名2"}}

        for (ProductSizeInfoCellSetting setting : cellSettings) {
            if (needSkipPart.get(setting.getOutsideFiled()) != null && !Objects.equals(needSkipPart.get(setting.getOutsideFiled()), setting.getSort())) {
                continue;
            }
            Set<String> clffSet = map.get(setting.getSystemFiled());
            if (clffSet != null) {
                needSkipPart.put(setting.getOutsideFiled(), setting.getSort());
                for (String clff : clffSet) {
                   title.add(setting.getOutsideFiled());
                   clffTitle.add(clff);

                    ProductFabInfoSizeResp.SizeCliffInfo info = new ProductFabInfoSizeResp.SizeCliffInfo();
                    info.setPart(setting.getOutsideFiled());
                    info.setCliff(clff);
                    info.setInfo(setting.getOutsideFiled() + "：" + clff);
                    retCliffInfos.add(info);

                    for (List<String> sizeTableRow : sizeTableInfo) {
                        sizeTableRow.add(size2PartClffMap.get(sizeTableRow.get(0)).get(setting.getSystemFiled() + "@" + clff));
                    }
                }
            }
        }

        if (requestData.getIsNeedCliff()) {
            sizeTableInfo.add(0, clffTitle);
        }
        sizeTableInfo.add(0, title);

        resp.setSizeInfos(sizeTableInfo);
        resp.setSizeCliffInfos(retCliffInfos);
        return resp;
    }

    @Override
    public List<List<String>> queryDistinctSizeInfoByProductId(String productId) {
        // 查询当前商品的尺码信息
        SearchSizeInfoEntityReq req = new SearchSizeInfoEntityReq();
        req.setProductId(productId);
        List<ProductSizeInfoEsResp> sizeInfoEsResps = searchProductSizeInfoInEs(req);
        if (CollectionUtils.isEmpty(sizeInfoEsResps)) {
            return Collections.emptyList();
        }

        // 获取当前品牌和当前小类的数据 过滤需要的部位
        List<ProductSizeInfoSetting> settingInfo = productSizeInfoSettingMapper
                .selectBrandAndSmallClassInfo(sizeInfoEsResps.get(0).getC_arcbrand_id(), sizeInfoEsResps.get(0).getSmall_class_id());


        // 获取品牌和品类id
        Integer brandSettingId = null;
        Integer smallClassSettingId = null;
        if (CollectionUtils.isNotEmpty(settingInfo)) {
            ProductSizeInfoSetting brandSetting = settingInfo.stream().filter(v -> Objects.equals(v.getType(), 0)).findFirst().orElse(null);
            brandSettingId = brandSetting == null ? brandSettingId : brandSetting.getId();
            ProductSizeInfoSetting smallClassSetting = settingInfo.stream().filter(v -> Objects.equals(v.getType(), 1)).findFirst().orElse(null);
            smallClassSettingId = smallClassSetting == null ? smallClassSettingId : smallClassSetting.getId();
        }

        // 获取配置的表头信息
        List<ProductSizeInfoCellSetting> cellSettings = new ArrayList<>();
        if (smallClassSettingId != null && brandSettingId != null) {
            cellSettings = productSizeInfoCellSettingMapper.selectByBandAndSmallClassIds(brandSettingId, org.assertj.core.util.Lists.newArrayList(smallClassSettingId));
            cellSettings = cellSettings.stream().sorted(Comparator.comparing(ProductSizeInfoCellSetting::getId)).collect(Collectors.toList());
            // 特殊处理 若当前的xiu为插肩袖 则不取肩宽信息
            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(sizeInfoEsResps.get(0).getXiu()) && sizeInfoEsResps.get(0).getXiu().contains("插肩袖")) {
                cellSettings = cellSettings.stream().filter(v -> !Objects.equals(v.getOutsideFiled(), "肩宽")).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(cellSettings)) {return Collections.emptyList();}


        // 处理获取行数据
        List<String> model = new ArrayList<>();
        if (Objects.equals(sizeInfoEsResps.get(0).getC_arcbrand_id(), 4)) {
            List<String> sizeInfos = sizeInfoEsResps.stream()
                    .filter(v -> StringUtils.isNotBlank(v.getSize_num()))
                    .filter(v -> StringUtils.isNotBlank(v.getStylepartsize_model()))
                    .map(ProductSizeInfoEsResp::getStylepartsize_model)
                    .distinct().collect(Collectors.toList());
            model = SizeUtil.getStylepartsize_modelPx(sizeInfos);
        } else {
            model = sizeInfoEsResps.stream()
                    .filter(v -> StringUtils.isNotBlank(v.getSize_num()))
                    .filter(v -> StringUtils.isNotBlank(v.getStylepartsize_model()))
                    .map(ProductSizeInfoEsResp::getStylepartsize_model)
                    .distinct().sorted().collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(model)) {
            return Collections.emptyList();
        }

        // 获取所有尺码对应数据各个部位的数据
        Map<String, List<ProductSizeInfoEsResp>> sizeMap = sizeInfoEsResps.stream()
                .filter(v -> StringUtils.isNotBlank(v.getStylepartsize_model()))
                .collect(Collectors.groupingBy(ProductSizeInfoEsResp::getStylepartsize_model));
        if (MapUtils.isEmpty(sizeMap)) {
            return Collections.emptyList();
        }
        // 各个尺码对应的双部位对应的数据
        HashMap<String, Map<String, List<ProductSizeInfoEsResp>>> sizePartMap = new HashMap<>();
        sizeMap.forEach((key, value1) -> {
            Map<String, List<ProductSizeInfoEsResp>> value = value1.stream()
                    .filter(v -> org.apache.commons.lang3.StringUtils.isNotBlank(v.getSize_num()))
                    .collect(Collectors.groupingBy(ProductSizeInfoEsResp::getPart));
            sizePartMap.put(key, value);
        });
        if (MapUtils.isEmpty(sizePartMap)) {
            return Collections.emptyList();
        }
        // {"系统名称":{"部位说明1", "部位说名2"}}
        Map<String, Set<String>> map = new HashMap<>();
        // {"尺码"：{"系统名称@部位说明": "尺寸", "系统名称@部位说明2":"尺寸2"}}
        Map<String, Map<String, String>> size2PartClffMap = new HashMap<>();
        for (ProductSizeInfoEsResp sizeInfoEsResp : sizeInfoEsResps) {
            sizeInfoEsResp.setClff(org.apache.commons.lang3.StringUtils.defaultIfBlank(sizeInfoEsResp.getClff(), ""));
            //根据part 获取 该part下的clff数据
            Set<String> clffSet = map.get(sizeInfoEsResp.getPart());
            if (clffSet == null) {
                clffSet = new HashSet<>();
                map.put(sizeInfoEsResp.getPart(), clffSet);
            }
            clffSet.add(sizeInfoEsResp.getClff());


            //将尺寸对应partclff数据记录一下
            Map<String, String> partClffMap = size2PartClffMap.get(sizeInfoEsResp.getStylepartsize_model());
            if (partClffMap == null) {
                partClffMap = new HashMap<>();
                size2PartClffMap.put(sizeInfoEsResp.getStylepartsize_model(), partClffMap);
            }
            partClffMap.put(sizeInfoEsResp.getPart() + "@" + org.apache.commons.lang3.StringUtils.defaultIfBlank(sizeInfoEsResp.getClff(), ""), sizeInfoEsResp.getSize_num());

        }

        List<String> title = new ArrayList<>();
        title.add("尺码");
        List<String> clffTitle = new ArrayList<>();
        clffTitle.add("部位测量说明");

        List<List<String>> sizeTableInfo = new ArrayList<>();
        for (String m : model) {
            List<String> sizeTableRow = new ArrayList<>();
            sizeTableRow.add(m);

            sizeTableInfo.add(sizeTableRow);
        }

        HashMap<String, Integer> needSkipPart = new HashMap<>();
        // {"系统名称":{"部位说明1", "部位说名2"}}

        for (ProductSizeInfoCellSetting setting : cellSettings) {
            if (needSkipPart.get(setting.getOutsideFiled()) != null) {
                continue;
            }
            Set<String> clffSet = map.get(setting.getSystemFiled());
            if (clffSet != null) {
                needSkipPart.put(setting.getOutsideFiled(), setting.getSort());
                for (String clff : clffSet) {
                    title.add(setting.getOutsideFiled());
                    clffTitle.add(clff);

                    for (List<String> sizeTableRow : sizeTableInfo) {
                        sizeTableRow.add(size2PartClffMap.get(sizeTableRow.get(0)).get(setting.getSystemFiled() + "@" + clff));
                    }
                    break;
                }
            }
        }

        //sizeTableInfo.add(0, clffTitle);
        sizeTableInfo.add(0, title);
        return sizeTableInfo;
    }

    private static Object getObject(List<ProductSizeInfoEsResp> value2, Map<String, String> map,
                                    BrandSizeInfoEntityEnum entityEnum, ProductSizeInfoCellSetting cellSetting)
            throws InstantiationException, IllegalAccessException {
        Field[] declaredFields = entityEnum.getCla().getDeclaredFields();
        Object o = entityEnum.getCla().newInstance();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            ExcelProperty annotation1 = declaredField.getAnnotation(ExcelProperty.class);
            String value = annotation1.value()[0];
            if (Objects.equals(value, "款号")) {
                declaredField.set(o, value2.get(0).getStyle_id());
            } else if (Objects.equals(value, "年份")) {
                declaredField.set(o, value2.get(0).getYear());
            } else if (Objects.equals(value, "季节")) {
                declaredField.set(o, value2.get(0).getSampleId());
            } else if (Objects.equals(value, "品牌")) {
                declaredField.set(o, value2.get(0).getD_pp());
            } else if (Objects.equals(value, "部位")) {
                declaredField.set(o, cellSetting.getOutsideFiled());
            } else if (Objects.equals(value, "测量说明")) {
                declaredField.set(o, value2.get(0).getClff());
            } else {
                String key2 = StringUtils.split(value, "/")[0];
                if (CollectionUtils.isEmpty(map) || StringUtils.isBlank(map.get(key2))) {
                    declaredField.set(o, null);
                }
                if (StringUtils.isNotBlank(map.get(key2))) {
                    declaredField.set(o, StringUtils.isBlank(map.get(key2)) ? null :
                            numberRemoveZero(map.get(key2)));
                } else {
                    declaredField.set(o, null);
                }
            }

        }
        return o;
    }

    /**
     * 数字字符串去除小数点后末尾多余的0
     * 如果字符串为空返回0，非数字则返回原字符串
     * @param str
     * @return
     */
    public  static String numberRemoveZero(String str){
        String str2 = "";
        if(str==null||"".equals(str)){
            str="0";
        }else{
            try{
                BigDecimal b = new BigDecimal(str);
                if(b.compareTo(BigDecimal.ZERO)==0){
                    str2 = "0";
                }else{
                    str2 = b.stripTrailingZeros().toPlainString();
                }
            }catch(Exception e){
                log.info("数字转化异常",str+e.toString());
                str2 =str;
            }
        }
        return str2;
    }





    private List<ProductSizeInfoEsResp> searchProductSizeInfoInEs(SearchSizeInfoEntityReq req) {
        SearchRequest request = new SearchRequest();
        request.indices(productSizeInfoIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        buildQuery(req, queryBuilder);

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(10000);
        sourceBuilder.sort("product_id", SortOrder.ASC);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品SPU_SIZE_INFO入参 {}", request.source().toString());
        List<ProductSizeInfoEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }

            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                ProductSizeInfoEsResp entity = ProductSizeInfoEsResp.fromJson(hit.getSourceAsString(), ProductSizeInfoEsResp.class);
                entity.setPart_clff(entity.getPart() + "@" + entity.getClff());
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品SPU_SIZE_INFO入参异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }

    private void buildQuery(SearchSizeInfoEntityReq req, BoolQueryBuilder queryBuilder) {
        if (StringUtils.isNotBlank(req.getProductId())) {
            queryBuilder.must(QueryBuilders.termQuery("product_id", req.getProductId()));
        }
        queryBuilder.must(QueryBuilders.termQuery("is_sync", "Y"));

        if (CollectionUtils.isNotEmpty(req.getBrandIds())) {
            queryBuilder.must(QueryBuilders.termsQuery("c_arcbrand_id", req.getBrandIds()));
        }

        if (CollectionUtils.isNotEmpty(req.getBandIds())) {
            queryBuilder.must(QueryBuilders.termsQuery("band_id", req.getBandIds()));
        }

        if (CollectionUtils.isNotEmpty(req.getYears())) {
            queryBuilder.must(QueryBuilders.termsQuery("year", req.getYears()));
        }

        if (StringUtils.isNotBlank(req.getBrandName())) {
            queryBuilder.must(QueryBuilders.termQuery("d_pp.keyword", req.getBrandName()));
        }

        if (CollectionUtils.isNotEmpty(req.getBrandNames())) {
            queryBuilder.must(QueryBuilders.termsQuery("d_pp.keyword", req.getBrandNames()));
        }

        if (CollectionUtils.isNotEmpty(req.getSmallCategoryIds())) {
            queryBuilder.must(QueryBuilders.termsQuery("small_class_id", req.getSmallCategoryIds()));
        }

        if (StringUtils.isNotBlank(req.getName())) {
            queryBuilder.must(QueryBuilders.termsQuery("style_id.keyword", req.getName()));
        }

        if (CollectionUtils.isNotEmpty(req.getMustLabels())) {
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("labels",
                    req.getMustLabels().stream().map(item -> item.toLowerCase())
                            .map(item -> Strman.replace(item, "-", "_", true))
                            .collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(0)));
            queryBuilder.must(termsSetQueryBuilder);
        }

        if (CollectionUtils.isNotEmpty(req.getSeasonIds())) {
            queryBuilder.filter(QueryBuilders.termsQuery("small_season_id", req.getSeasonIds()));
        }

    }
}
