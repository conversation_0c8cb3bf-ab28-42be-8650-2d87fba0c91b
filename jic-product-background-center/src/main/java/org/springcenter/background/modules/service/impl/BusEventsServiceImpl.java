package org.springcenter.background.modules.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.killbill.CreatorName;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.BusEvent;
import org.springcenter.background.modules.mapper.product.BusEventsMapper;
import org.springcenter.background.modules.model.product.BusEvents;
import org.springcenter.background.modules.service.IBusEventsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @auther yuanxiaozhong
 * @create 2022-03-22 15:24:26
 * @describe 事件表服务实现类
 */
@Service
@Slf4j
public class BusEventsServiceImpl extends ServiceImpl<BusEventsMapper, BusEvents> implements IBusEventsService {

    @Autowired
    private DefaultPersistentBus persistentBus;

    private final ConcurrentHashMap<UUID,Integer> tokenMap = new ConcurrentHashMap<UUID, Integer>();

    @Override
    public void createBusEvent(BusEvent event) {
        String json;
        try {
            json = persistentBus.getObjectMapper().writeValueAsString(event);
        } catch (JsonProcessingException var6) {
            return;
        }
        BusEvents entry = new BusEvents(CreatorName.get(), persistentBus.getClock().getUTCNow().toDate(), event.getClass().getName(), json, event.getUserToken().toString(), event.getSearchKey1(), event.getSearchKey2());
        this.save(entry);
    }

    @Override
    public void createBusEvent(BusEvent event,boolean reTry) {
        this.createBusEvent(event);
        if(!reTry) return;
        setCurrentNum(event.getUserToken(), 0);
    }


    @Override
    public Integer getCurrentNumByToken(UUID token){
        return tokenMap.get(token);
    }

    @Override
    public void setCurrentNum(UUID token, Integer currentNum) {
        tokenMap.put(token, currentNum);
    }


    @Override
    public boolean retryCompletion(UUID token, Integer totNum) {
        Integer currentNumByToken = this.getCurrentNumByToken(token);
        if(currentNumByToken == null){
            // 如果为null，默认结束轮询
            return true;
        }
        if(currentNumByToken < totNum){
            this.setCurrentNum(token,currentNumByToken+1);
            return false;
        }
        this.removeToken(token);
        return true;
    }

    @Override
    public void removeToken(UUID token) {
        tokenMap.remove(token);
    }
}
