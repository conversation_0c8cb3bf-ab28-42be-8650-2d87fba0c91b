<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.ProductEnvFabricInfoMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.ProductEnvFabricInfo">
        <id column="ID" property="id" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="NAME" property="name" />
        <result column="ENV_FABRIC_INFO" property="envFabricInfo" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="SKC_NO" property="skcNo" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, PRODUCT_ID, NAME, ENV_FABRIC_INFO, CREATE_TIME, UPDATE_TIME,IS_DELETED, SKC_NO
    </sql>
    <insert id="insertBatch">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO PRODUCT_ENV_FABRIC_INFO
            (ID, PRODUCT_ID, NAME, ENV_FABRIC_INFO, CREATE_TIME, UPDATE_TIME, SKC_NO) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.productId,jdbcType=DECIMAL}, #{item.name,jdbcType=VARCHAR},
            #{item.envFabricInfo,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.skcNo,jdbcType=VARCHAR})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <update id="updateAll">
        UPDATE PRODUCT_ENV_FABRIC_INFO
        SET IS_DELETED = 1
        WHERE IS_DELETED = 0
        <if test="list != null and list.size > 0">
            and NAME in
            <foreach collection="list" item="list" close=")" open="(" separator=",">
                #{list}
            </foreach>
        </if>
    </update>
    <select id="selectByProductId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from PRODUCT_ENV_FABRIC_INFO
        where PRODUCT_ID = #{productId}
        and IS_DELETED = 0
    </select>
    <select id="selectEnviFabricByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from PRODUCT_ENV_FABRIC_INFO
        where PRODUCT_ID in
        <foreach collection="list" item="list" close=")" open="(" separator=",">
            #{list}
        </foreach>
        and IS_DELETED = 0
    </select>


</mapper>
