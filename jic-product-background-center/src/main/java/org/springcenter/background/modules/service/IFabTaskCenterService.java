package org.springcenter.background.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/3/14 14:11
 */
public interface IFabTaskCenterService {

    void createBrowseTask();


    void createAssessTask();


    /**
     * 查询当前门店导购是否完成任务
     * @param requestData 门店导购信息
     * @return true为已完成 false未完成
     */
    Boolean isFinishTask(FabTaskIsFinishReq requestData);

    /**
     * 用户完成任务
     * @param requestData 门店导购信息
     * @return 返回
     */
    Boolean finishTask(FabTaskIsFinishReq requestData);

    /**
     * 查询列表的参数
     * @return 返回
     */
    FabTaskIsFilterResp getFilters();

    /**
     * 根据条件筛选城市和门店
     * @param req 入参
     * @return 返回
     */
    List<FabTaskIsFilterDataResp> getFiltersByParam(FabTaskIsFilterByParamReq req);

    /**
     * 获取检查列表
     * @param req 筛选条件
     * @return 返回
     */
    List<FabTaskCheckListResp> getCheckTaskList(FabTaskCheckParamReq req, Page page);

    /**
     * 添加反馈信息
     * @param requestData 入参
     * @return 返回
     */
    Boolean addFeedbackInfo(AddFeedbackReq requestData);

    /**
     * 充实完成任务
     */
    void tryFinishTask();
}
