package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/12/11 13:26
 */
@Data
@TableName(value = "FAB_AUTO_NAME_SETTING")
public class FabAutoNameSetting {

    @TableId
    private Integer id;

    @TableField(value = "FIELD_NAME")
    private String fieldName;

    @TableField(value = "FIELD")
    private String field;

    @TableField(value = "ASSEMBLE_PART_ORDER")
    private Integer assemblePartOrder;

    @TableField(value = "ASSEMBLE_WHOLE_ORDER")
    private Integer assembleWholeOrder;

    @TableField(value = "TYPE")
    @ApiModelProperty(value = "0拿当前字段 1拿动态表的字段")
    private Integer type;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    @TableField(value = "AUTO_TYPE")
    @ApiModelProperty(value = "自动品名参数类型 0样衣 1微商城 2电商")
    private Integer autoType;

    @TableField(value = "IS_IN_CYCLE")
    @ApiModelProperty(value = "Y是当季 N是往季")
    private String isInCycle;
}
