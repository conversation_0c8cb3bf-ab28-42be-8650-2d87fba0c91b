package org.springcenter.background.modules.mapper.product;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.PackageChangeFilterInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【PACKAGE_CHANGE_FILTER_INFO(商品包变化筛选参数数据)】的数据库操作Mapper
* @createDate 2024-07-25 14:58:22
* @Entity generator.domain.PackageChangeFilterInfo
*/
public interface PackageChangeFilterInfoMapper extends BaseMapper<PackageChangeFilterInfo> {


    List<PackageChangeFilterInfo> selectByFieldAndParam(@Param("field") String field, @Param("param") String param);
}
