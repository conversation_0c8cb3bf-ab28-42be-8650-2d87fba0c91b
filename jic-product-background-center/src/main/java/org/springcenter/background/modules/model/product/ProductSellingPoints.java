package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2025/4/8 14:54
 */
@Data
@TableName(value = "PRODUCT_SELLING_POINTS")
public class ProductSellingPoints {
    @TableId(value = "id")
    private String id;

    @TableField(value = "PRODUCT_ID")
    private String productId;

    @TableField(value = "SELLING_POINTS")
    private String sellingPoints;

    @TableField(value = "IS_DELETED")
    private String isDeleted;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;
}
