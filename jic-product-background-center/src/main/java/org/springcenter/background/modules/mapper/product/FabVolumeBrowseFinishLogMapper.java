package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.FabVolumeBrowseFinishLog;

import java.util.List;
import java.util.stream.Stream;

public interface FabVolumeBrowseFinishLogMapper extends BaseMapper<FabVolumeBrowseFinishLog> {


    List<FabVolumeBrowseFinishLog> judgeIsFinish(@Param("fabVolumeId") String fabVolumeId,@Param("hrId") Long hrId,
                                                 @Param("storeId") Long storeId);

    List<FabVolumeBrowseFinishLog> selectNotPush();

    void updateByIds(@Param("list") List<FabVolumeBrowseFinishLog> ids);
}
