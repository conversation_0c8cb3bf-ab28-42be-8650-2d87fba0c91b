<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.bojun.CCityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.bojun.CCity">
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="C_PROVINCE_ID" property="provinceId" />
        <result column="CODE" property="code" />
        <result column="ISACTIVE" property="isactive"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, C_PROVINCE_ID, CODE
    </sql>
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM C_CITY
        WHERE ISACTIVE = 'Y'
        <if test="list != null and list.size() > 0">
            and id in
            <foreach collection="list" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>


</mapper>