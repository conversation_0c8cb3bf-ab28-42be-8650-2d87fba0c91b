<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.bojun.CAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.bojun.CArea">
        <id column="ID" property="id" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="MODIFIERID" property="modifierid" />
        <result column="CREATIONDATE" property="creationdate" />
        <result column="MODIFIEDDATE" property="modifieddate" />
        <result column="OWNERID" property="ownerid" />
        <result column="ISACTIVE" property="isactive" />
        <result column="NAME" property="name" />
        <result column="CHARGER_ID" property="chargerId" />
        <result column="CODE" property="code" />
        <result column="AREA_NAME" property="areaName" />
        <result column="PRA_NAME" property="praName" />
        <result column="C_DEPART_ID" property="cDepartId" />
        <result column="ORGANIZATION" property="organization" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, MODIFIERID, CREATIONDATE, MODIFIEDDATE, OWNERID, ISACTIVE, NAME, CHARGER_ID, CODE,
        AREA_NAME, PRA_NAME, C_DEPART_ID, ORGANIZATION
    </sql>

    <select id="getActiveList" resultMap="BaseResultMap">
         select ID,NAME from C_AREA where  ISACTIVE='Y'
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
         SELECT <include refid="Base_Column_List"></include>
             FROM C_AREA
         WHERE ISACTIVE = 'Y'
        <if test="list != null and list.size() > 0">
            and id in
            <foreach collection="list" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>