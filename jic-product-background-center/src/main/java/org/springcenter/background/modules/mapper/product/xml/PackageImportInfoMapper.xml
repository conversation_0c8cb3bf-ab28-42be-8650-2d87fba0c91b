<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.PackageImportInfoMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.PackageImportInfo">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="pacId" column="PAC_ID" jdbcType="VARCHAR"/>
            <result property="linkType" column="LINK_TYPE" jdbcType="DECIMAL"/>
            <result property="link" column="LINK" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="linkData" column="LINK_DATA" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PAC_ID,LINK_TYPE,
        LINK,IS_DELETED,CREATE_TIME,
        UPDATE_TIME,LINK_DATA
    </sql>
    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO PACKAGE_IMPORT_INFO
            (ID, PAC_ID, LINK_TYPE, LINK_DATA, CREATE_TIME, UPDATE_TIME) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.pacId,jdbcType=VARCHAR}, #{item.linkType,jdbcType=DECIMAL},
            #{item.linkData,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>
    <update id="updateByPacId">
        UPDATE PACKAGE_IMPORT_INFO
        set IS_DELETED = 1, update_time = sysdate
        WHERE PAC_ID = #{pacId,jdbcType=VARCHAR} AND IS_DELETED = 0
    </update>

    <select id="selectByPacId" resultMap="BaseResultMap">
        SELECT ID,PAC_ID,LINK_TYPE,
               LINK,IS_DELETED,CREATE_TIME,
               UPDATE_TIME, LINK_DATA
            FROM PACKAGE_IMPORT_INFO
        WHERE PAC_ID = #{pacId,jdbcType=VARCHAR} AND IS_DELETED = 0
    </select>
</mapper>
