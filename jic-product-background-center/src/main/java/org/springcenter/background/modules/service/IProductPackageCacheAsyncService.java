package org.springcenter.background.modules.service;

import org.springcenter.background.modules.model.product.PackageFilterInfo;
import org.springcenter.background.modules.model.product.PackageImportInfo;
import org.springcenter.background.modules.model.product.PackageTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/7/25 14:14
 */
public interface IProductPackageCacheAsyncService {

    /**
     * 根据商品信息缓存商品id
     * @param template 商品包
     * @param filterInfos 过滤信息
     * @param importInfos 导入信息
     */
    void cashPackageInfoByPacId(PackageTemplate template, List<PackageFilterInfo> filterInfos,
                                List<PackageImportInfo> importInfos);


}
