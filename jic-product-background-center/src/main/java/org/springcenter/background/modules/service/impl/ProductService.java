package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jnby.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsSetQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.background.modules.service.CommonDataRuleService;
import org.springcenter.background.modules.service.IProductService;
import org.springcenter.background.modules.util.EsUtil;
import org.springcenter.product.api.dto.AttrResp;
import org.springcenter.product.api.dto.ProductSkcResp;
import org.springcenter.product.api.dto.QueryGoodsReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import strman.Strman;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/15/21 11:19 AM
 */
@Slf4j
@Service
@RefreshScope
public class ProductService implements IProductService {
    @Autowired
    private EsUtil esUtil;

    @Value("${bandIds.config}")
    private String bandIdsConfig;

    @Value("${product.skc.index}")
    private String PRODUCT_SKC_INDEX;

    @Autowired
    private CommonDataRuleService commonDataRuleService;


    @Override
    public List<ProductSkcResp> searchGoodsSkc(QueryGoodsReq context, Page page, String component) {
        // 获取品牌权限
//        List<Long> brandIds = context.getBrandIds();
//        context.setBrandIds(brandIds);
        List<Long> brandIds = context.getBrandIds();
        List<Long> allowBrandIds = commonDataRuleService.getAllowBrandIdRule(component, brandIds);
        context.setBrandIds(allowBrandIds);

        SearchRequest request = new SearchRequest();
        log.info("========searchGoodsSkc, 过滤的品牌信息:{}", JSONObject.toJSONString(context.getBrandIds()));
        request.indices(PRODUCT_SKC_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        buildQuery(queryBuilder, context);
        sourceBuilder.fetchSource(new String[]{"name", "value", "price", "skc_code", "sales_num", "colorno",
                "color_name", "imgurl", "cover_imgs", "product_id", "brand", "c_arcbrand_id", "m_band", "m_band_id", "year", "small_season_id", "m_big_category_id",
                "m_small_category_id", "small_season", "m_small_category", "m_big_category"}, new String[]{});
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1) * page.getPageSize());
        sourceBuilder.size(page.getPageSize());
//        if (context.getSorted() == 2){
        sourceBuilder.sort("product_id", SortOrder.DESC);
//        }
        if (context.getSorted() == 1) {
            sourceBuilder.sort("year", SortOrder.DESC).sort("m_band_id", SortOrder.DESC);
        }
        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品SKC-ES {}", request.source().toString());
        List<ProductSkcResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0) {
                return new ArrayList<>();
            }
            page.setCount(response.getHits().getTotalHits().value);
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductSkcResp entity = ProductSkcResp.fromJson(hits[i].getSourceAsString(), ProductSkcResp.class);
                entity.setId(hits[i].getId());
                entity.setPrimaryKey(hits[i].getId());
                entities.add(entity);
            }
            //相同SKC排在一起
            entities = entities.stream().sorted(Comparator.comparing(ProductSkcResp::getName).reversed()).collect(Collectors.toList());
        } catch (IOException e) {
            log.error("查询商品SKC异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }


    private void buildQuery(BoolQueryBuilder queryBuilder, QueryGoodsReq context) {
        if (CollectionUtils.isNotEmpty(context.getNames())) {
            queryBuilder.must(QueryBuilders.termsQuery("name", context.getNames()));
        }
        if (StringUtils.isNotBlank(context.getName())) {
            // 处理 如果是九位且没有中文 走款号搜索
            //          走模糊匹配
            if (!isChinese(context.getName())) {
                queryBuilder.must(QueryBuilders.termQuery("name", context.getName()));
            } else {
                queryBuilder.must(QueryBuilders.matchQuery("bom_text", context.getName()));
            }
        }
        if (context.getBandId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("m_band_id", context.getBandId()));
        }
        if (CollectionUtils.isNotEmpty(context.getSeasonIds())) {
            queryBuilder.must(QueryBuilders.termsQuery("small_season_id", context.getSeasonIds()));
        }
        if (context.getSeasonId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("big_season_id", context.getSeasonId()));
        }
        if (!context.getBigCategoryIds().isEmpty()) {
            queryBuilder.must(QueryBuilders.termsQuery("m_big_category_id", context.getBigCategoryIds()));
        }
        if (!context.getSmallCategoryIds().isEmpty()) {
            queryBuilder.must(QueryBuilders.termsQuery("m_small_category_id", context.getSmallCategoryIds()));
        }
        if (context.getBrandId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("c_arcbrand_id", context.getBrandId()));
        }
        if (context.getStoreId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("store_id", context.getStoreId()));
        }
        if (context.getYear() != null) {
            queryBuilder.must(QueryBuilders.termQuery("year", context.getYear()));
        }
        if (context.getIsStockNotEmpty() == 1) {
            queryBuilder.must(QueryBuilders.termQuery("qty", 1));
        }
        if (context.getLPrice() != null || context.getGPrice() != null) {
            if (context.getLPrice() != null && context.getGPrice() != null) {
                queryBuilder.must(QueryBuilders.rangeQuery("price").gte(context.getLPrice()).lte(context.getGPrice()));
            }

            if (context.getLPrice() != null && context.getGPrice() == null) {
                queryBuilder.must(QueryBuilders.rangeQuery("price").gte(context.getLPrice()));
            }

            if (context.getLPrice() == null && context.getGPrice() != null) {
                queryBuilder.must(QueryBuilders.rangeQuery("price").lte(context.getGPrice()));
            }
        }
        if (CollectionUtils.isNotEmpty(context.getSkcIds())) {
            queryBuilder.must(QueryBuilders.termsQuery("_id", context.getSkcIds()));
        }

        if (!ObjectUtils.isEmpty(context.getMustLabels())) {
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("labels", context.getMustLabels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(context.getMustLabelLevels().size())));
            queryBuilder.must(termsSetQueryBuilder);
        }

        if (!ObjectUtils.isEmpty(context.getMustLabelLevels())) {
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("label_levels", context.getMustLabelLevels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(context.getMustLabelLevels().size())));
            queryBuilder.must(termsSetQueryBuilder);
        }

        if (!ObjectUtils.isEmpty(context.getMustNotLabels())) {
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("label_levels", context.getMustNotLabels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(context.getMustNotLabels().size())));
            queryBuilder.mustNot(termsSetQueryBuilder);
        }
        if (context.getProductId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("product_id", context.getProductId()));
        }
        if (!context.getBigCategoryIds().isEmpty()) {
            queryBuilder.must(QueryBuilders.termsQuery("m_big_category_id", context.getBigCategoryIds()));
        }
        if (!context.getSmallCategoryIds().isEmpty()) {
            queryBuilder.must(QueryBuilders.termsQuery("m_small_category_id", context.getSmallCategoryIds()));
        }
        if (!context.getYears().isEmpty()) {
            queryBuilder.must(QueryBuilders.termsQuery("year", context.getYears()));
        }
        if (!context.getBrandIds().isEmpty()) {
            queryBuilder.must(QueryBuilders.termsQuery("c_arcbrand_id", context.getBrandIds()));
        }
        if (!context.getBandIds().isEmpty()) {
            List<Long> other = new ArrayList<>();
            if (context.getBandIds().contains(2000L)) {
                List<AttrResp> attrEntities = JSONObject.parseArray(bandIdsConfig, AttrResp.class);
                AttrResp attrResp = attrEntities.get(attrEntities.size() - 1);
                List<AttrResp> children = attrResp.getChildren();
                other = children.stream().map(r -> r.getId()).collect(Collectors.toList());
            }
            other.addAll(context.getBandIds());
            queryBuilder.must(QueryBuilders.termsQuery("m_band_id", other));
        }

        if (CollectionUtils.isNotEmpty(context.getSeasonGoods())) {
            queryBuilder.must(QueryBuilders.termsQuery("good_season.keyword", context.getSeasonGoods()));
        }

        if (StringUtils.isNotBlank(context.getBrandName())) {
            queryBuilder.must(QueryBuilders.termsQuery("brand", context.getBrandName()));
        }
        if (context.getIsZhuiDan() != null) {
            queryBuilder.must(QueryBuilders.termQuery("is_zhuidan", context.getIsZhuiDan()));
        }

        if (context.getIsPattern() != null) {
            queryBuilder.must(QueryBuilders.termQuery("is_pattern", context.getIsPattern()));
        }

        if (Objects.equals(context.getHasFab(), 1)) {
            queryBuilder.must(QueryBuilders.termQuery("has_fab", 1));
        } else if (Objects.equals(context.getHasFab(), 0)) {
            queryBuilder.must(QueryBuilders.termQuery("has_fab", 0));
        }

        if (Objects.equals(context.getHasReport(), 1)) {
            queryBuilder.must(QueryBuilders.termQuery("report_tag", 1));
        } else if (Objects.equals(context.getHasReport(), 0)) {
            queryBuilder.must(QueryBuilders.termQuery("report_tag", 0));
        }

        if (StringUtils.isNotBlank(context.getGys())) {
            queryBuilder.must(QueryBuilders.termQuery("gys", context.getGys()));
        }

        if (StringUtils.isNotBlank(context.getDesigner())) {
            queryBuilder.must(QueryBuilders.termQuery("designer", context.getDesigner()));
        }
    }


    /**
     * 判断字符串是否为中文
     *
     * @param input
     * @return
     */
    private static boolean isChinese(String input) {
        if (StringUtils.isBlank(input)) {
            return false;
        }
        int length = input.length();
        for (int i = 0; i < length; i++) {
            String code = input.charAt(i) + "";
            boolean matches = code.matches("^[\u4e00-\u9fa5]+$");
            if (matches) {
                return true;
            }
        }
        return false;
    }


    @Override
    public List<ProductSkcResp> findGoodsSkcDetailByIds(List<String> ids) {
        SearchRequest request = new SearchRequest();
        request.indices(PRODUCT_SKC_INDEX);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        queryBuilder.must(QueryBuilders.termsQuery("_id", ids));
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(ids.size());
        request.source(sourceBuilder);

        List<ProductSkcResp> resps = new ArrayList<>();
        try {
            SearchResponse search = esUtil.search(request);
            if (search.getHits().getTotalHits().value == 0){
                return Collections.emptyList();
            }
            SearchHit[] hits = search.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductSkcResp entity = ProductSkcResp.fromJson(hits[i].getSourceAsString(), ProductSkcResp.class);
                if (CollectionUtils.isNotEmpty(entity.getLabels())){
                    entity.setLabels(entity.getLabels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                    entity.setLabel_levels(entity.getLabel_levels().stream().map(item -> Strman.replace(item, "_", "-", true)).collect(Collectors.toList()));
                }
                entity.setId(hits[i].getId());
                resps.add(entity);
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品异常e = {}", e.getMessage());
        }
        return resps;
    }

}
