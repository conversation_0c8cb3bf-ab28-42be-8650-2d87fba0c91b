package org.springcenter.background.modules.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/3/14 11:15
 */
@Data
public class DistributeTaskReqEntity {

    @ApiModelProperty(value = "活动 id")
    private String campaignId;

    @ApiModelProperty(value = "活动名称（未启⽤）")
    private String campaignName;

    @ApiModelProperty(value = "任务id（未启⽤）")
    private String taskId;

    @ApiModelProperty(value = "任务名称（未启⽤）")
    private String taskName;

    @ApiModelProperty(value = "节点 id（未启⽤）")
    private String stepId;

    @ApiModelProperty(value = "节点名称（未启⽤）")
    private String stepName;

    @ApiModelProperty(value = "任务执⾏有效期为空时默认为 1")
    private int taskValidPeriod;

    @ApiModelProperty(value = "任务执⾏有效期时间单位（hour,day）为空时默认为day")
    private String periodUnit;

    @ApiModelProperty(value = "任务模版编号 为空时为优惠券类型任务 不为空时为⾮优惠券类型任务")
    private String templateId;

    @ApiModelProperty(value = "优惠券ID templateId为空时 该项必填")
    private String couponId;

    @ApiModelProperty(value = "会员卡号")
    private String cardNo;

    @ApiModelProperty(value = "批量会员卡号 templateId不为空时 该项和cardNo必传其⼀ ⻔店⽇常/导购考核任务模版 不传⽤户信息")
    private List<String> cardNos;

    @ApiModelProperty(value = "导购 id")
    private Integer dealerId;

    @ApiModelProperty(value = "批量⽤户信息 templateId为空时 该项和cardNo必传其⼀ ⻔店⽇常/导购考核任务模版 不传⽤户信息")
    private List<UserInfos> userInfos;

    @ApiModelProperty(value = "任务下发时间 为空时⽴即发送（格式：HH:mm:ss）")
    private List<String> taskSendTimes;

    @ApiModelProperty(value = "⻔店包 ids ⻔店⽇常/导购考核任务模版 必传")
    private List<Integer> storePackageIds;

    @ApiModelProperty(value = "跳转链接 ⻔店⽇常任务模版 必传")
    private String taskUrl;

    @ApiModelProperty(value = "策略ids 材料类型为策略时必传")
    private List<Long> promotionIds;

    @ApiModelProperty(value = "产品册品牌 材料类型为产品册时必传")
    private Long productBrochureBrand;

    @ApiModelProperty(value = "产品册id 材料类型为产品册时必传")
    private Long productBrochureId;

    @ApiModelProperty(value = "导购考核任务 考核限制⼈数 为空或者⼤于⻔店⼈数时为⻔店⼈数上限")
    private Integer checkNum;

    @ApiModelProperty(value = "导购考核任务 考核⽅式 1 线下 2 线上")
    private Integer checkType;

    @ApiModelProperty(value = "导购考核任务 考核反馈 1 需要反馈 0 不需要反馈")
    private Integer checkFeedback;


    @Data
    private static class UserInfos {

        private String cardNo;

    }
}
