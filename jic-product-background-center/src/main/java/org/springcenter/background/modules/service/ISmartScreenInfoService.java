package org.springcenter.background.modules.service;

import com.alibaba.fastjson.JSONArray;
import com.jnby.common.Page;
import org.springcenter.product.api.dto.back.SmartScreenProductInfoResp;
import org.springcenter.product.api.dto.back.SmartScreenSkcInfoResp;
import org.springcenter.product.api.dto.back.screen.StoreRecommendGoodsReq;
import org.springcenter.product.api.dto.back.screen.StoreRecommendGoodsResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/9/29 20:12
 */
public interface ISmartScreenInfoService {
    /**
     * 根据商品id查询商品skc信息
     * @param requestData 商品id
     * @return 返回
     */
    List<SmartScreenSkcInfoResp> searchProductSkcInfo(String requestData);


    /**
     * 根据商品id查询商品信息
     * @param requestData 商品id
     * @return 返回
     */
    SmartScreenProductInfoResp searchProductInfo(String requestData);

    /**
     * 获取参数
     * @return 返回
     */
    JSONArray searchParamInfo();

    /**
     * 筛选推荐商品列表
     * @param requestData 请求参数
     * @param page 分页
     * @return 返回
     */
    List<StoreRecommendGoodsResp> screenRecommendProductList(StoreRecommendGoodsReq requestData, Page page);

    List<StoreRecommendGoodsResp> screenRecommendScrollProductList(StoreRecommendGoodsReq requestData, Page page);
}
