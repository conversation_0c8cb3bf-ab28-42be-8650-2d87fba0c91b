package org.springcenter.background.modules.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.modules.mapper.product.FabWearingImgInfoLogMapper;
import org.springcenter.background.modules.mapper.product.FabWearingImgInfoMapper;
import org.springcenter.background.modules.model.product.FabWearingImgInfo;
import org.springcenter.background.modules.model.product.FabWearingImgInfoLog;
import org.springcenter.background.modules.service.FabWearingImgInfoService;
import org.springcenter.product.api.dto.AddProductFabWearingReq;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/7/1 15:43
 */
@Service
@Slf4j
public class FabWearingImgInfoImpl implements FabWearingImgInfoService {

    @Autowired
    private FabWearingImgInfoMapper fabWearingImgInfoMapper;

    @Autowired
    private FabWearingImgInfoLogMapper fabWearingImgInfoLogMapper;

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Value("${fab.wear.tag.id}")
    private String fabWearTagId;

    @Override
    public Boolean saveWearing(AddProductFabWearingReq requestData) {
        Boolean isInsert = false;
        FabWearingImgInfo fabWearingImgInfo = this.selectWearingByProductIdAndName(requestData.getProductId(), requestData.getName());
        if (fabWearingImgInfo == null) {
            fabWearingImgInfo = new FabWearingImgInfo();
            fabWearingImgInfo.setId(IdLeaf.getId(fabWearTagId));
            fabWearingImgInfo.setName(requestData.getName());
            fabWearingImgInfo.setProductId(requestData.getProductId());
            fabWearingImgInfo.setImages(requestData.getUrls());
            fabWearingImgInfo.setOperators(requestData.getOperates());
            fabWearingImgInfo.setCreateTime(new Date());
            fabWearingImgInfo.setUpdateTime(new Date());
            isInsert = true;
        } else {
            fabWearingImgInfo.setUpdateTime(new Date());
            fabWearingImgInfo.setOperators(requestData.getOperates());
            fabWearingImgInfo.setImages(requestData.getUrls());
        }

        // 维护日志
        FabWearingImgInfoLog log = new FabWearingImgInfoLog();
        BeanUtils.copyProperties(fabWearingImgInfo, log);
        log.setId(IdLeaf.getId(fabWearTagId));
        log.setFabWearingImgInfoId(fabWearingImgInfo.getId());
        log.setCreateTime(new Date());
        log.setUpdateTime(new Date());

        Boolean finalIsInsert = isInsert;
        FabWearingImgInfo finalFabWearingImgInfo = fabWearingImgInfo;
        template.execute(v -> {
            if (finalIsInsert) {
                fabWearingImgInfoMapper.insert(finalFabWearingImgInfo);
            } else {
                fabWearingImgInfoMapper.updateById(finalFabWearingImgInfo);
            }
            fabWearingImgInfoLogMapper.insert(log);
            return true;
        });
        return true;
    }

    @Override
    public FabWearingImgInfo selectWearingByProductIdAndName(String productId, String name) {
        LambdaQueryWrapper<FabWearingImgInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FabWearingImgInfo::getProductId, productId);
        wrapper.eq(FabWearingImgInfo::getName, name);
        return fabWearingImgInfoMapper.selectOne(wrapper);
    }
}
