package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2024/4/1 14:39
 */
@Data
@TableName(value = "FAB_MATCHING_SUG_INFO_LOG")
public class FabMatchingSugInfoLog {

    @TableId(value = "ID")
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField(value = "PRODUCT_ID")
    @ApiModelProperty(value = "商品id")
    private String productId;

    @TableField(value = "NAME")
    @ApiModelProperty(value = "商品款号")
    private String name;

    @TableField(value = "MATCHING_SUG")
    @ApiModelProperty(value = "搭配建议信息")
    private String matchingSug;

    @TableField(value = "OPERATORS")
    @ApiModelProperty(value = "操作人")
    private String operators;

    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "IS_DELETED")
    @ApiModelProperty(value = "0正常 1已删除")
    private Integer isDeleted;

    @TableField(value = "FAB_SUG_INFO_ID")
    @ApiModelProperty(value = "主键")
    private String fabSugInfoId;
}
