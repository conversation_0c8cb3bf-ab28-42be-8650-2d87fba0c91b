<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.PackageChangeFilterInfoMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.PackageChangeFilterInfo">
            <result property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="field" column="FIELD" jdbcType="VARCHAR"/>
            <result property="code" column="CODE" jdbcType="VARCHAR"/>
            <result property="codeValue" column="CODE_VALUE" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,FIELD,CODE,
        CODE_VALUE,IS_DELETED,CREATE_TIME,
        UPDATE_TIME
    </sql>
    <select id="selectByFieldAndParam" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM PACKAGE_CHANGE_FILTER_INFO
        WHERE FIELD = #{field} AND IS_DELETED = 0
        AND CODE_VALUE like concat(concat('%',#{param}),'%')
    </select>


</mapper>
