package org.springcenter.background.modules.entity.productFab;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springcenter.background.modules.model.product.PatentInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/9/5 19:24
 */
@Data
public class ProductBatchPatentEntity {

    private List<PatentInfo> baseDataList;

    private List<AllPatentInfo> errorDataList;


    private List<AllPatentInfo> allDataList;

    @Data
    public static class ErrorData {

        @ApiModelProperty(value = "款号")
        private String name;

        @ApiModelProperty(value = "色号")
        private String skcNo;

        @ApiModelProperty(value = "原因")
        private String reason;

        @ApiModelProperty(value = "专利号")
        private String patentNo;

        @ApiModelProperty(value = "专利描述")
        private String patentDesc;

        @ApiModelProperty(value = "商品id")
        private Long productId;
    }

    @Data
    public static class AllPatentInfo {

        @ApiModelProperty(value = "款号")
        private String name;

        @ApiModelProperty(value = "色号")
        private String skcNo;

        @ApiModelProperty(value = "专利号")
        private String patentNo;

        @ApiModelProperty(value = "专利描述")
        private String patentDesc;

        @ApiModelProperty(value = "原因")
        private String reason;

        @ApiModelProperty(value = "类型 0正常 1异常")
        private Integer type;

        @ApiModelProperty(value = "商品id")
        private Long productId;
    }

}
