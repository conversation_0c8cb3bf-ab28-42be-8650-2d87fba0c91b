package org.springcenter.background.modules.mapper.bojun;

import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.bojun.CStore;
import org.springcenter.product.api.dto.FabTaskIsFilterDataResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 10:49
 */

public interface CStoreMapper {

    CStore selectById(@Param("id") Long cStoreId);

    List<FabTaskIsFilterDataResp> selectRegions();

    List<FabTaskIsFilterDataResp> selectCity(@Param("cAreaId") Long param);

    List<FabTaskIsFilterDataResp> selectStore(@Param("cCityId") Long param);
}
