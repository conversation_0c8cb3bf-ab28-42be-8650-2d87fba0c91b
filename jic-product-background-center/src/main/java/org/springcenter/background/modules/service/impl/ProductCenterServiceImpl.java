package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jnby.common.CommonRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.enums.ProductCenterSearchCenterEnum;
import org.springcenter.background.modules.service.CommonDataRuleService;
import org.springcenter.background.modules.service.IProductCenterService;
import org.springcenter.product.api.dto.ProductCenterParamsSearchResp;
import org.springcenter.product.api.dto.SizeNoParamResp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/6/17 10:00
 */
@Service
@Slf4j
@RefreshScope
public class ProductCenterServiceImpl implements IProductCenterService {

    @Value("${search.product.attr}")
    private String productSizeNos;

    private final static String BRAND = "品牌";

    @Autowired
    private CommonDataRuleService commonDataRuleService;
    @Override
    public List<ProductCenterParamsSearchResp> searchParams() {
        List<ProductCenterParamsSearchResp> rets = new ArrayList<>();
        Arrays.stream(ProductCenterSearchCenterEnum.values()).forEach(v -> {
            ProductCenterParamsSearchResp searchResp = new ProductCenterParamsSearchResp();
            BeanUtils.copyProperties(v, searchResp);
            rets.add(searchResp);
        });
        return rets;
    }

    @Override
    public JSONArray getSizeNos(CommonRequest request) {
        List<SizeNoParamResp> data = JSONObject.parseArray(productSizeNos, SizeNoParamResp.class);
        List<Long> brandIds = data.stream().filter(v -> Objects.equals(v.getName(), BRAND))
                .map(SizeNoParamResp::getChildren).findFirst().orElse(Collections.emptyList())
                .stream().map(v -> Long.valueOf(Objects.toString(v.getCode()))).collect(Collectors.toList());
        if (StringUtils.isNotBlank(request.getComponent())) {
            List<Long> brands = commonDataRuleService.getAllowBrandIdRule(request.getComponent(), brandIds);
            log.info("============返回品牌信息：{}", JSONObject.toJSONString(brands));
            if (CollectionUtils.isEmpty(brands)) {
                return JSONArray.parseArray(JSON.toJSONString(data));
            }
            List<SizeNoParamResp> filterBrand = data.stream().filter(v -> Objects.equals(v.getName(), BRAND))
                    .map(SizeNoParamResp::getChildren).findFirst().orElse(Collections.emptyList())
                    .stream().filter(v -> brands.contains(Long.valueOf(Objects.toString(v.getCode())))).collect(Collectors.toList());

            data.stream().filter(v -> Objects.equals(v.getName(), BRAND)).findFirst().orElse(null).setChildren(filterBrand);
            return JSONArray.parseArray(JSON.toJSONString(data));
        }

        return JSONArray.parseArray(JSON.toJSONString(data));
    }
}
