package org.springcenter.background.modules.remote.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.modules.remote.ISendMessageRemoteApi;
import org.springcenter.background.modules.remote.entity.JicBaseResp;
import org.springcenter.background.modules.remote.entity.SendMsgCpEntity;
import org.springcenter.background.modules.remote.service.IMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

/**
 * <AUTHOR>
 * @Date:2024/3/18 15:49
 */
@Service
@Slf4j
public class MessageServiceImpl implements IMessageService {

    @Autowired
    private ISendMessageRemoteApi sendMessageRemoteApi;

    @Override
    public Boolean sendMsgCp(SendMsgCpEntity sendMsgCpEntity) {
        log.info("sendMsgCp = {}", JSONObject.toJSONString(sendMsgCpEntity));
        try {
            Response<JicBaseResp> result = sendMessageRemoteApi.sendMsgCp(sendMsgCpEntity).execute();
            if (!result.isSuccessful()) {
                log.error("发送企业微信消息失败!");
            }
        }catch (Exception e){
            log.error("发送企业微信消息异常",e);
        }
        return true;
    }
}
