package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2024/7/25 13:40
 */
@TableName(value = "PACKAGE_FILTER_SETTING")
@Data
public class PackageFilterSetting {
    /**
     *
     */
    @TableField(value = "ID")
    private Integer id;

    /**
     * 筛选条件名称
     */
    @TableField(value = "FILTER_NAME")
    private String filterName;

    /**
     * 过滤字段
     */
    @TableField(value = "FILTER_FIELD")
    private String filterField;

    /**
     * 筛选条件元素
     */
    @TableField(value = "FILTER_PARAM")
    private String filterParam;

    /**
     * 筛选类型 0常用条件 1更多条件
     */
    @TableField(value = "FILTER_TYPE")
    private Integer filterType;

    /**
     * 筛选框类型 0单选 1层级筛选
     */
    @TableField(value = "FILTER_FIELD_TYPE")
    private Integer filterFieldType;

    /**
     * 排序
     */
    @TableField(value = "SORT")
    private Integer sort;

    /**
     * 0正常 1删除
     */
    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;
}
