package org.springcenter.background.modules.mapper.product;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.CustomsDeclaration;
import org.springcenter.product.api.dto.CustomsDeclarationReq;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【CUSTOMS_DECLARATION(报关表)】的数据库操作Mapper
* @createDate 2024-07-02 13:52:14
* @Entity generator.domain.CustomsDeclaration
*/
public interface CustomsDeclarationMapper extends BaseMapper<CustomsDeclaration> {


    List<CustomsDeclaration> selectByNeIdAndNsid(@Param("neid") String neid, @Param("nsid")String nsid);

    List<CustomsDeclaration> selectByParams(CustomsDeclarationReq requestData);

    List<CustomsDeclaration> selectByNames(@Param("names") List<String> names);
}
