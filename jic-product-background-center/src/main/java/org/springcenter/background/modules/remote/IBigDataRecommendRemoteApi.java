package org.springcenter.background.modules.remote;

import org.springcenter.background.modules.remote.entity.BigDataComprehensiveReq;
import org.springcenter.background.modules.remote.entity.BigDataComprehensiveResponse;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * <AUTHOR>
 * @Date:2025/1/21 9:37
 */

public interface IBigDataRecommendRemoteApi {

    /**
     * 综合排序  包含的商品进行综合排序
     * @param bigDataComprehensiveReq 入参
     * @return 返回
     */
    @POST("/ProdPersonalized/v1/jnby/app")
    Call<BigDataComprehensiveResponse> searchComprehensiveList(@Body BigDataComprehensiveReq bigDataComprehensiveReq);
}
