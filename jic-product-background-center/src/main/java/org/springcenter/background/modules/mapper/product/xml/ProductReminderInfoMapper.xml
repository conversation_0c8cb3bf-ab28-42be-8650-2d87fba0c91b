<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.ProductReminderInfoMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.ProductReminderInfo">
        <id column="ID" property="id" />
        <result column="TYPE" property="type" />
        <result column="TYPE_NAME" property="typeName" />
        <result column="TEMPLATE_CODE" property="templateCode" />
        <result column="TEMPLATE_TITLE" property="templateTitle" />
        <result column="TEMPLATE_CONCAT" property="templateConcat" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="REMINDER_TIME" property="reminderTime" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, TYPE, TYPE_NAME, TEMPLATE_CODE, TEMPLATE_TITLE, TEMPLATE_CONCAT, CREATE_TIME, UPDATE_TIME, IS_DELETED, REMINDER_TIME
    </sql>

    <select id="selectReminderListByParams" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
            FROM PRODUCT_REMINDER_INFO
        WHERE IS_DELETED = 0
        <if test="type != null">
            AND TYPE = #{type}
        </if>
        ORDER BY UPDATE_TIME DESC
    </select>


    <update id="delReminderInfoById">
        UPDATE PRODUCT_REMINDER_INFO
        SET IS_DELETED = 1 ,
            UPDATE_TIME = sysdate
        WHERE ID = #{id}
    </update>

    <select id="selectByType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
            FROM PRODUCT_REMINDER_INFO
        WHERE IS_DELETED = 0
        <if test="type != null">
            AND TYPE = #{type}
        </if>
        ORDER BY UPDATE_TIME DESC
    </select>

</mapper>
