<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.GioActionEvtStatMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.GioActionEvtStat">
        <result column="VISIT_USER_ID" property="visitUserId" />
        <result column="SESSION_ID" property="sessionId" />
        <result column="REQUEST_TYPE" property="requestType" />
        <result column="DOMAIN" property="domain" />
        <result column="PAGE" property="page" />
        <result column="HREF" property="href" />
        <result column="REQUEST_VALUE" property="requestValue" />
        <result column="INDEX" property="index" />
        <result column="TIME" property="time" />
        <result column="SEND_TIME" property="sendTime" />
        <result column="PAGE_REQUEST_ID" property="pageRequestId" />
        <result column="CREATE_DATE" property="createDate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CREATE_DATE,VISIT_USER_ID, SESSION_ID, REQUEST_TYPE, DOMAIN, PAGE, HREF, REQUEST_VALUE, INDEX, TIME, SEND_TIME, PAGE_REQUEST_ID
    </sql>

</mapper>