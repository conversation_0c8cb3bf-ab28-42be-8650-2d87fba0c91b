package org.springcenter.background.modules.remote.service;

import org.springcenter.background.modules.remote.entity.AddFeedbackReqEntity;
import org.springcenter.background.modules.remote.entity.DistributeTaskReqEntity;

/**
 * <AUTHOR>
 * @Date:2024/3/14 13:25
 */
public interface ITaskCenterService {

    /**
     * 调用分发任务
     * @param entity 分发实体
     */
    Boolean distributeTask(DistributeTaskReqEntity entity);


    /**
     * 调用分发任务
     * @param taskItemId 任务id
     */
    Boolean finishTask(Long taskItemId);


    /**
     * 调用增加反馈
     * @param addFeedbackReq 参数
     */
    Boolean addTaskFeedback(AddFeedbackReqEntity addFeedbackReq);

}
