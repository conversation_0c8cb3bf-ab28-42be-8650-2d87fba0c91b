<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.FabVolumeDistributeTaskLogMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.FabVolumeDistributeTaskLog">
        <id column="ID" property="id" />
        <result column="FAB_VOLUME_ID" property="fabVolumeId" />
        <result column="FAB_VOLUME_NAME" property="fabVolumeName" />
        <result column="STORE_PAK_ID" property="storePakId" />
        <result column="BRAND_ID" property="brandId" />
        <result column="TASK_TYPE" property="taskType" />
        <result column="NEXT_DISTRIBUTE_TIME" property="nextDistributeTime" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="TASK_TEMPLATE_ID" property="taskTemplateId" />
        <result column="TASK_URL" property="taskUrl" />
        <result column="TASK_CHECK_TYPE" property="taskCheckType" />
        <result column="TASK_FEED_BACK" property="taskFeedBack" />
        <result column="CAMPAIGN_ID" property="campaignId" />
        <result column="TASK_VALID_PERIOD" property="taskValidPeriod" />
        <result column="PERIOD_UNIT" property="periodUnit" />
        <result column="FAB_TYPE" property="fabType" />
    </resultMap>

    <sql id="Base_Column_List">
        ID,FAB_VOLUME_ID,FAB_VOLUME_NAME,STORE_PAK_ID,BRAND_ID,TASK_TYPE,NEXT_DISTRIBUTE_TIME,CREATE_TIME,
        UPDATE_TIME,IS_DELETED,TASK_TEMPLATE_ID,TASK_URL,TASK_CHECK_TYPE,TASK_FEED_BACK,CAMPAIGN_ID,
        TASK_VALID_PERIOD,PERIOD_UNIT, FAB_TYPE
    </sql>

    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO FAB_VOLUME_DISTRIBUTE_TASK_LOG
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test = "item.id != null">
                    ID,
                </if>
                <if test = "item.fabVolumeId != null">
                    FAB_VOLUME_ID,
                </if>
                <if test = "item.fabVolumeName != null">
                    FAB_VOLUME_NAME,
                </if>
                <if test = "item.storePakId != null">
                    STORE_PAK_ID,
                </if>
                <if test = "item.brandId != null">
                    BRAND_ID,
                </if>
                <if test = "item.taskType != null">
                    TASK_TYPE,
                </if>
                <if test = "item.nextDistributeTime != null">
                    NEXT_DISTRIBUTE_TIME,
                </if>
                <if test = "item.createTime != null">
                    CREATE_TIME,
                </if>
                <if test = "item.updateTime != null">
                    UPDATE_TIME,
                </if>
                <if test = "item.isDeleted != null">
                    IS_DELETED,
                </if>
                <if test = "item.taskTemplateId != null">
                    TASK_TEMPLATE_ID,
                </if>
                <if test = "item.taskUrl != null">
                    TASK_URL,
                </if>
                <if test = "item.taskCheckType != null">
                    TASK_CHECK_TYPE,
                </if>
                <if test = "item.taskFeedBack != null">
                    TASK_FEED_BACK,
                </if>
                <if test = "item.campaignId != null">
                    CAMPAIGN_ID,
                </if>
                <if test = "item.taskValidPeriod != null">
                    TASK_VALID_PERIOD,
                </if>
                <if test = "item.periodUnit != null">
                    PERIOD_UNIT,
                </if>
                <if test = "item.fabType != null">
                    FAB_TYPE,
                </if>
            </trim>
            values
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test = "item.id != null">
                    #{item.id,jdbcType=VARCHAR},
                </if>
                <if test = "item.fabVolumeId != null">
                    #{item.fabVolumeId,jdbcType=VARCHAR},
                </if>
                <if test = "item.fabVolumeName != null">
                    #{item.fabVolumeName,jdbcType=VARCHAR},
                </if>
                <if test = "item.storePakId != null">
                    #{item.storePakId,jdbcType=DECIMAL},
                </if>
                <if test = "item.brandId != null">
                    #{item.brandId,jdbcType=DECIMAL},
                </if>
                <if test = "item.taskType != null">
                    #{item.taskType,jdbcType=DECIMAL},
                </if>
                <if test = "item.nextDistributeTime != null">
                    #{item.nextDistributeTime,jdbcType=TIMESTAMP},
                </if>
                <if test = "item.createTime != null">
                    #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test = "item.updateTime != null">
                    #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test = "item.isDeleted != null">
                    #{item.isDeleted,jdbcType=DECIMAL},
                </if>
                <if test = "item.taskTemplateId != null">
                    #{item.taskTemplateId,jdbcType=VARCHAR},
                </if>
                <if test = "item.taskUrl != null">
                    #{item.taskUrl,jdbcType=VARCHAR},
                </if>
                <if test = "item.taskCheckType != null">
                    #{item.taskCheckType,jdbcType=DECIMAL},
                </if>
                <if test = "item.taskFeedBack != null">
                    #{item.taskFeedBack,jdbcType=DECIMAL},
                </if>
                <if test = "item.campaignId != null">
                    #{item.campaignId,jdbcType=VARCHAR},
                </if>
                <if test = "item.taskValidPeriod != null">
                    #{item.taskValidPeriod,jdbcType=DECIMAL},
                </if>
                <if test = "item.periodUnit != null">
                    #{item.periodUnit,jdbcType=VARCHAR},
                </if>
                <if test = "item.fabType != null">
                    #{item.fabType,jdbcType=DECIMAL},
                </if>
            </trim>
        </foreach>
        SELECT 1 FROM DUAL
    </insert>


    <select id="selectDisTaskIsToday" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM FAB_VOLUME_DISTRIBUTE_TASK_LOG
        WHERE IS_DELETED = 0 AND TASK_TYPE = 0
          AND to_char(NEXT_DISTRIBUTE_TIME,'yyyyMMdd') = to_char(sysdate,'yyyyMMdd')
    </select>

    <select id="selectFabInfo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM FAB_VOLUME_DISTRIBUTE_TASK_LOG
        WHERE IS_DELETED = 0 AND TASK_TYPE = 0
        AND FAB_VOLUME_ID = #{fabVolumeId}
    </select>

    <select id="selectByBrowseNextDay" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM FAB_VOLUME_DISTRIBUTE_TASK_LOG
        WHERE IS_DELETED = 0 AND TASK_TYPE = 0
        AND FAB_VOLUME_ID = #{fabVolumeId}
        AND rownum = 1
    </select>


</mapper>
