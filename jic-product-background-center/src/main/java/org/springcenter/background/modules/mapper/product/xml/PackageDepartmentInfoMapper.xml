<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.PackageDepartmentInfoMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.PackageDepartmentInfo">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="pacId" column="PAC_ID" jdbcType="VARCHAR"/>
            <result property="departmentId" column="DEPARTMENT_ID" jdbcType="DECIMAL"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PAC_ID,IS_DELETED,CREATE_TIME,
        UPDATE_TIME,DEPARTMENT_ID
    </sql>
    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO PACKAGE_DEPARTMENT_INFO
            (ID, PAC_ID, DEPARTMENT_ID, CREATE_TIME, UPDATE_TIME) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.pacId,jdbcType=VARCHAR},
            #{item.departmentId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>
    <update id="updateByPacId">
        UPDATE PACKAGE_DEPARTMENT_INFO
        set IS_DELETED = 1, update_time = sysdate
        WHERE PAC_ID = #{pacId,jdbcType=VARCHAR} AND IS_DELETED = 0
    </update>

    <select id="selectByPacId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM PACKAGE_DEPARTMENT_INFO
        WHERE PAC_ID = #{pacId,jdbcType=VARCHAR} AND IS_DELETED = 0
    </select>
    <select id="selectByDepartmentIds" resultType="java.lang.String">
        SELECT DISTINCT PAC_ID
        FROM PACKAGE_DEPARTMENT_INFO
        WHERE IS_DELETED = 0
        <if test="list != null and list != '' and list.size > 0">
            AND DEPARTMENT_ID IN
            <foreach collection="list" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
