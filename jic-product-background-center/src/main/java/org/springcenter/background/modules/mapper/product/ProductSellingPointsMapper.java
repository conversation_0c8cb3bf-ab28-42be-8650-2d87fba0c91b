package org.springcenter.background.modules.mapper.product;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.ProductSellingPoints;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 16:47
 */

public interface ProductSellingPointsMapper extends BaseMapper<ProductSellingPoints> {

    List<ProductSellingPoints> selectByProductIds(@Param("ids") List<String> requestData);

    void updateByProductId(@Param("productId") String productId);

    void insertBatch(@Param("list") List<ProductSellingPoints> productSellingPoints);
}
