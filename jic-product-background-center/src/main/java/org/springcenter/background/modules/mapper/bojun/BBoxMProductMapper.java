package org.springcenter.background.modules.mapper.bojun;

import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.bojun.BoxMProduct;
import org.springcenter.product.api.dto.SameSpuProductResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 10:48
 */

public interface BBoxMProductMapper {

    List<SameSpuProductResp> getSameSpuProduct(@Param("spu") String spu);

    List<BoxMProduct> selectListByNos(@Param("nos") List<String> requestData);

    List<BoxMProduct> selectByNames(@Param("list") List<String> nameList);


    List<BoxMProduct> selectByIds(@Param("list") List<Long> idList);
}
