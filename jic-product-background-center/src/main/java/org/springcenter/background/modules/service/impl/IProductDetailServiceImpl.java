package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.background.modules.mapper.bojun.MProductListMapper;
import org.springcenter.background.modules.mapper.product.ProductCollocaNetDiskImgMapper;
import org.springcenter.background.modules.mapper.product.ProductDetailNetDiskImgMapper;
import org.springcenter.background.modules.mapper.product.ProductOverseasNetDiskImgMapper;
import org.springcenter.background.modules.mapper.product.SampleClothNetDiskImgMapper;
import org.springcenter.background.modules.model.product.ProductCollocaNetDiskImg;
import org.springcenter.background.modules.model.product.ProductDetailNetDiskImg;
import org.springcenter.background.modules.model.product.ProductOverseasNetDiskImg;
import org.springcenter.background.modules.model.product.SampleClothNetDiskImg;
import org.springcenter.background.modules.service.IProductDetailService;
import org.springcenter.background.modules.service.LianXiangYunFilezService;
import org.springcenter.background.modules.util.DateUtil;
import org.springcenter.background.modules.util.FileParseUtil;
import org.springcenter.core.tool.utils.SpringUtil;
import org.springcenter.product.api.constant.IdConstant;
import org.springcenter.product.api.constant.ProductDetailNetDiskConstant;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.api.lenovo.LenovoFileReq;
import org.springcenter.product.api.lenovo.LenovoFileResp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IProductDetailServiceImpl implements IProductDetailService {


    @Autowired
    private LianXiangYunFilezService lianXiangYunFilezService;

    @Autowired
    private ProductDetailNetDiskImgMapper productDetailNetDiskImgMapper;

    @Autowired
    private ProductCollocaNetDiskImgMapper productCollocaNetDiskImgMapper;

    @Autowired
    private ProductOverseasNetDiskImgMapper productOverseasNetDiskImgMapper;

    @Autowired
    private SampleClothNetDiskImgMapper sampleClothNetDiskImgMapper;

    @Autowired
    private QiniuUtil qiniuUtil;

    @Autowired
    private MProductListMapper mProductListMapper;

    @Override
    public String importImgPathExcel(String url) {
        List<Map<String,String>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {

            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,String> result = new HashMap<>();
                    // 类型   0 网盘   1  电商
                    result.put("type",integerStringMap.get(0));
                    // 路径
                    result.put("imgPath",integerStringMap.get(1));
                    //图片类型 是 哪部分的图片
                    result.put("imgType",integerStringMap.get(2));
                    importData.add(result);
                }
            }
        });
        // 通过联想网盘进行数据落库  通过excel 图片网盘地址信息
        syncLianxiangImgByExcel(importData);
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
        return "成功";
    }

    @Override
    public String importBrandExcelCollocationReleation(String url) {
        List<Map<String,String>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {

            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,String> result = new HashMap<>();
                    //搭配号
                    result.put("collocationCode",integerStringMap.get(0));
                    //款色号
                    result.put("skc",integerStringMap.get(1));
                    importData.add(result);
                }
            }
        });
        // 数据整合
        mergeBrandDataToDataBase(importData);
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
        return "成功";
    }

    @Override
    public String importEComExcelCollocationReleation(String url) {
        // 导入电商部的搭配关系excel 进行落库    上传  A款 + B款 +C款 等 根据 A款 B款  C款去查询  标记他们是同一个
        List<Map<String,String>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {

            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,String> result = new HashMap<>();
                    //搭配号
                    result.put("name",integerStringMap.get(0));
                    //款色号 用英文逗号分隔的
                    result.put("skcs",integerStringMap.get(1));
                    importData.add(result);
                }
            }
        });
        // 数据整合 电商部
        mergeEComDataToDataBase(importData);
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
        return "成功";
    }

    @Override
    public List<FindProductDetailImgResp> findProductDetailImg(FindProductDetailImgReq requestData) {
        List<FindProductDetailImgResp> findProductDetailImgResps = new ArrayList<>();
        if(CollectionUtils.isEmpty(requestData.getProductCodes())){
            return findProductDetailImgResps;
        }

        // 查询主图
        Map<String, List<FindProductDetailImgResp.ImgData>> mainImgListGroupByProductCode = new HashMap<>();
        List<ProductDetailNetDiskImg> mainImgList = productDetailNetDiskImgMapper.selectByProductCodesAndType(requestData.getProductCodes(),ProductDetailNetDiskConstant.MAIN_TYPE);
        if(CollectionUtils.isNotEmpty(mainImgList)){
            mainImgListGroupByProductCode = mainImgList.stream().filter(r->r.getOverseaFlag().equals(0)).map(r -> {
                FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
                BeanUtils.copyProperties(r, imgData);
                return imgData;
            }).collect(Collectors.groupingBy(r -> r.getProductCode()));
        }


        // 查询详情图
        Map<String, List<FindProductDetailImgResp.ImgData>> detailsGroupByProductCode = new HashMap<>();
        List<ProductDetailNetDiskImg> detailNetDiskImgs = productDetailNetDiskImgMapper.selectByProductCodesAndType(requestData.getProductCodes(),ProductDetailNetDiskConstant.DETAIL_TYPE);
        if(CollectionUtils.isNotEmpty(detailNetDiskImgs)){
            detailsGroupByProductCode = detailNetDiskImgs.stream().filter(r->r.getOverseaFlag().equals(0)).map(r -> {
                FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
                BeanUtils.copyProperties(r, imgData);
                return imgData;
            }).collect(Collectors.groupingBy(r -> r.getProductCode()));
        }


        // 查询一览图
        Map<String, List<FindProductDetailImgResp.ImgData>> pansGroup = new HashMap<>();
        List<ProductDetailNetDiskImg> pngs = productDetailNetDiskImgMapper.selectByProductCodesAndType(requestData.getProductCodes(),ProductDetailNetDiskConstant.CATE_TYPE);
        if(CollectionUtils.isNotEmpty(pngs)){
            pansGroup = pngs.stream().filter(r->r.getOverseaFlag().equals(0)).map(r -> {
                FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
                BeanUtils.copyProperties(r, imgData);
                return imgData;
            }).collect(Collectors.groupingBy(r -> r.getProductCode()));
        }


        // 查询搭配图  搭配图 需要先根据款号多个查询到搭配，多个搭配 拿到 搭配号  再去查询一遍， 拿出来 根据款号分组，然后存储
        Map<String, List<FindProductDetailImgResp.ImgData>> collcationGroupByProductCode = new HashMap<>();
        // 根据搭配号分组
        Map<String, List<FindProductDetailImgResp.ImgData>> collcationGroupByCollocationCode = new HashMap<>();
        // 无搭配号的搭配图
        Map<String, List<FindProductDetailImgResp.ImgData>> notHaveCollectionCode = new HashMap<>();

        List<ProductCollocaNetDiskImg> productCollocaNetDiskImgs = productCollocaNetDiskImgMapper.selectByProductCodesAndType(requestData.getProductCodes(),ProductDetailNetDiskConstant.COLLOCATION_TYPE);
        if(CollectionUtils.isNotEmpty(productCollocaNetDiskImgs)){
            // 拥有搭配号的  搭配号
            List<String> collocationCodes = productCollocaNetDiskImgs.stream().filter(r -> StringUtils.isNotBlank(r.getProductCode()) && StringUtils.isNotBlank(r.getCollocationCode()))
                    .map(r -> r.getCollocationCode()).collect(Collectors.toList());
            // 没有搭配号的
            notHaveCollectionCode= productCollocaNetDiskImgs.stream().filter(r -> StringUtils.isNotBlank(r.getProductCode()) && StringUtils.isBlank(r.getCollocationCode()))
                    .map(r -> {
                        FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
                        BeanUtils.copyProperties(r, imgData);
                        return imgData;
                    }).collect(Collectors.groupingBy(r->r.getProductCode()));

            // 根据搭配号再去查一遍
            if(CollectionUtils.isNotEmpty(collocationCodes)){
                List<ProductCollocaNetDiskImg> findByCollcationCodes = productCollocaNetDiskImgMapper.selectByCollocationCodes(collocationCodes);
                if(CollectionUtils.isNotEmpty(findByCollcationCodes)){
                    collcationGroupByProductCode = findByCollcationCodes.stream().map(r -> {
                        FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
                        BeanUtils.copyProperties(r, imgData);
                        return imgData;
                    }).filter(r->StringUtils.isNotBlank(r.getProductCode())).collect(Collectors.groupingBy(r -> r.getProductCode()));


                    collcationGroupByCollocationCode = findByCollcationCodes.stream().map(r -> {
                        FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
                        BeanUtils.copyProperties(r, imgData);
                        return imgData;
                    }).filter(r->StringUtils.isNotBlank(r.getCollocationCode())).collect(Collectors.groupingBy(r -> r.getCollocationCode()));

                }
            }
        }

        for (String productCode : requestData.getProductCodes()) {
            FindProductDetailImgResp findProductDetailImgResp = new FindProductDetailImgResp();
            findProductDetailImgResp.setProductCode(productCode);
            findProductDetailImgResp.setMainImgs(mainImgListGroupByProductCode.get(productCode));
            findProductDetailImgResp.setDetailsImgs(detailsGroupByProductCode.get(productCode));
            findProductDetailImgResp.setPngs(pansGroup.get(productCode));
            List<FindProductDetailImgResp.ImgData> imgData = collcationGroupByProductCode.get(productCode);
            List<FindProductDetailImgResp.ImgCollocationData> resImg = new ArrayList<>();
            Set<String> hashSet = new HashSet<>();
            if(CollectionUtils.isNotEmpty(imgData)){
                for (FindProductDetailImgResp.ImgData imgDatum : imgData) {
                    if(StringUtils.isNotBlank(imgDatum.getCollocationCode())){
                        if(hashSet.contains(imgDatum.getProductCode() + imgDatum.getCollocationCode())){
                            continue;
                        }
                        FindProductDetailImgResp.ImgCollocationData collocationData = new FindProductDetailImgResp.ImgCollocationData();
                        collocationData.setCollocationCode(imgDatum.getCollocationCode());
                        List<FindProductDetailImgResp.ImgData> imgData1 = collcationGroupByCollocationCode.get(imgDatum.getCollocationCode());
                        collocationData.setImgDatas(imgData1);
                        resImg.add(collocationData);
                        hashSet.add(imgDatum.getProductCode()+ imgDatum.getCollocationCode());
                    }
                }
            }
            // 赋值无搭配号的图
            List<FindProductDetailImgResp.ImgData> notHaveCollectionCodeImgData = notHaveCollectionCode.get(productCode);
            if(CollectionUtils.isNotEmpty(notHaveCollectionCodeImgData)){
                int i = 0 ;
                for (FindProductDetailImgResp.ImgData imgDatum : notHaveCollectionCodeImgData) {
                    FindProductDetailImgResp.ImgCollocationData collocationData = new FindProductDetailImgResp.ImgCollocationData();
                    collocationData.setCollocationCode(imgDatum.getProductCode()+"-"+i);
                    List<FindProductDetailImgResp.ImgData> imgData1 = new ArrayList<>();
                    imgData1.add(imgDatum);
                    collocationData.setImgDatas(imgData1);
                    resImg.add(collocationData);
                    i++;
                }
            }

            findProductDetailImgResp.setCollocationImgs(resImg);
            findProductDetailImgResps.add(findProductDetailImgResp);
        }

        return findProductDetailImgResps;
    }

    @Override
    public List<ProductOverseasNetDiskImg> findProductOverseasImg(FindProductDetailImgReq requestData) {
        List<ProductOverseasNetDiskImg> productOverseasNetDiskImgs = productOverseasNetDiskImgMapper.selectByProductCodesAndType(requestData.getProductCodes(), 0);
        return productOverseasNetDiskImgs;
    }

    @Override
    public String importSampleClothNetDiskExcel(String url) {
        // 导入excel 然后同步进行从网盘导入数据到数据库
        // 导入倒数第二层 是名称   样衣号
        List<Map<String,String>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {

            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,String> result = new HashMap<>();
                    // 路径
                    result.put("imgPath",integerStringMap.get(0));
                    importData.add(result);
                }
            }
        });
        // 通过联想网盘进行数据落库  通过excel 图片网盘地址信息
        syncSampleClothLianxiangByExcel(importData);
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
        return "成功";
    }

    @Override
    public void testUploadQiniuByLianxiangNetDisk(String dateFormat) {
        // 查询数据  查询当前时间大于商品上架时间的商品款号   然后查询到细节图  进行上传到七牛云
        if(StringUtils.isBlank(dateFormat)){
            dateFormat = DateUtil.formatToStr(new Date(), "yyyyMMdd");
        }
        List<String> productCodes = mProductListMapper.selectProductCodesByTime(Long.parseLong(dateFormat));
        if(CollectionUtils.isEmpty(productCodes)){
            log.info("上传七牛云图片 未查询到任何可用上传，结束");
            return ;
        }
        // 开始处理  一次 100条
        List<List<String>> partition = Lists.partition(productCodes, 50);
        for (List<String> codes : partition) {
            List<ProductDetailNetDiskImg> productDetailNetDiskImgs = productDetailNetDiskImgMapper.selectShouldUpdateByProductCodes(codes);
            // 开始处理数据  通过 neid分组
            if(CollectionUtils.isEmpty(productDetailNetDiskImgs)){
                continue;
            }
            // group by neid
            Map<String, List<ProductDetailNetDiskImg>> groupByNeid = productDetailNetDiskImgs.stream().collect(Collectors.groupingBy(r -> r.getNeid()));

            // 批量获取预览信息
            LianxiangBatchGetViewReq lianxiangBatchGetViewReq = new LianxiangBatchGetViewReq();
            List<BatchGetViewUrlReq.LianxiangData> file_array = new ArrayList<>();
            for (ProductDetailNetDiskImg productDetailNetDiskImg : productDetailNetDiskImgs) {
                BatchGetViewUrlReq.LianxiangData lianxiangData = new BatchGetViewUrlReq.LianxiangData();
                lianxiangData.setNeid(productDetailNetDiskImg.getNeid());
                lianxiangData.setNsid(productDetailNetDiskImg.getNsid());
                file_array.add(lianxiangData);
            }
            lianxiangBatchGetViewReq.setFile_array(file_array);
            List<LianxiangBatchGetViewResp> lianxiangBatchGetViewResps = lianXiangYunFilezService.batchGetViewUrl(lianxiangBatchGetViewReq);
            for (LianxiangBatchGetViewResp lianxiangBatchGetViewResp : lianxiangBatchGetViewResps) {
                String previewUrl = lianxiangBatchGetViewResp.getPreviewUrl();
                OutputStream os = null;
                InputStream is = null;
                File file = new File("/opt/logs/测试文件.png");
                try {
                    URL url = new URL(previewUrl);
                    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                    // 设置请求方法，默认是GET
                    connection.setRequestMethod("GET");
                    // 连接
                    connection.connect();
                    // 检查响应码
                    int responseCode = connection.getResponseCode();
                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        // 创建BufferedReader读取响应
                        is = connection.getInputStream();
                        // 准备输出流
                        os = new FileOutputStream(file);
                        // 准备一个数组，用来存放读写的数据
                        byte[] b = new byte[1024];
                        int len = 0;
                        // read(b)实现读取操作，数据存入b数组，返回读取长度给len，当所有内容都读取完毕，len=-1
                        while ((len = is.read(b)) != -1) {
                            // 实现写的操作
                            os.write(b, 0, len);
                        }

                        // 断开连接
                        connection.disconnect();

                        String dateStr = DateUtil.formatToStr(new Date(), DateUtil.DATEFORMATE_YYYY_MM_DD);
                        String fileName = lianxiangBatchGetViewResp.getNeid() + "-" + dateStr + ".png";
                        String uploadUrl = qiniuUtil.upload(file.getPath(),  fileName);
                        // 获取数据然后更新
                        log.info("上传完毕  neid = {} , url = {}",lianxiangBatchGetViewResp.getNeid(),uploadUrl);
                        List<ProductDetailNetDiskImg> productDetailNetDiskImgs1 = groupByNeid.get(lianxiangBatchGetViewResp.getNeid());
                        if(CollectionUtils.isNotEmpty(productDetailNetDiskImgs1)){
                            for (ProductDetailNetDiskImg productDetailNetDiskImg : productDetailNetDiskImgs1) {
                                ProductDetailNetDiskImg update = new ProductDetailNetDiskImg();
                                update.setId(productDetailNetDiskImg.getId());
                                update.setQiniuImgPath(uploadUrl);
                                productDetailNetDiskImgMapper.updateByPrimaryKeySelective(update);
                            }
                        }
                    } else {
                        System.out.println("GET request not worked , neid = " + lianxiangBatchGetViewResp.getNeid());
                    }
                } catch (Exception e) {

                } finally {
                    try {
                        if (os != null) {
                            os.close();
                        }
                        if (is != null) {
                            is.close();
                        }
                        if(file != null){
                            file.delete();
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    @Override
    public List<SampleProductDetailImg> findSampleClothNetDiskImgs(FindSampleClothNetDiskImgReq findSampleClothNetDiskImgReq) {
        if(CollectionUtils.isNotEmpty(findSampleClothNetDiskImgReq.getSampleCloths())){
            List<SampleClothNetDiskImg> list = sampleClothNetDiskImgMapper.selectbySampleCloths(findSampleClothNetDiskImgReq.getSampleCloths());
            List<SampleProductDetailImg> rets = new ArrayList<>();
            list.forEach(v -> {
                SampleProductDetailImg img = new SampleProductDetailImg();
                BeanUtils.copyProperties(v, img);
                rets.add(img);
            });
            return rets;
        }
        return new ArrayList<>();
    }

    @Override
    public List<FindProductDetailImgResp> findProductDetailImgForOversea(FindProductDetailImgReq requestData) {
        List<FindProductDetailImgResp> findProductDetailImgResps = new ArrayList<>();
        if(CollectionUtils.isEmpty(requestData.getProductCodes())){
            return findProductDetailImgResps;
        }

        // 查询主图 里面有A1
        Map<String, List<FindProductDetailImgResp.ImgData>> mainImgListGroupByProductCode = new HashMap<>();
        List<ProductDetailNetDiskImg> mainImgList = productDetailNetDiskImgMapper.selectByProductCodesAndType(requestData.getProductCodes(),ProductDetailNetDiskConstant.MAIN_TYPE);
        if(CollectionUtils.isNotEmpty(mainImgList)){
            // 海外的也不要A1
            mainImgListGroupByProductCode = mainImgList.stream().map(r -> {
                FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
                BeanUtils.copyProperties(r, imgData);
                return imgData;
            }).collect(Collectors.groupingBy(r -> r.getProductCode()));
        }


        // 查询详情图
        Map<String, List<FindProductDetailImgResp.ImgData>> detailsGroupByProductCode = new HashMap<>();
        List<ProductDetailNetDiskImg> detailNetDiskImgs = productDetailNetDiskImgMapper.selectByProductCodesAndType(requestData.getProductCodes(),ProductDetailNetDiskConstant.DETAIL_TYPE);
        if(CollectionUtils.isNotEmpty(detailNetDiskImgs)){
            detailsGroupByProductCode = detailNetDiskImgs.stream().map(r -> {
                FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
                BeanUtils.copyProperties(r, imgData);
                return imgData;
            }).collect(Collectors.groupingBy(r -> r.getProductCode()));
        }


        // 查询一览图
        Map<String, List<FindProductDetailImgResp.ImgData>> pansGroup = new HashMap<>();
        List<ProductDetailNetDiskImg> pngs = productDetailNetDiskImgMapper.selectByProductCodesAndType(requestData.getProductCodes(),ProductDetailNetDiskConstant.CATE_TYPE);
        if(CollectionUtils.isNotEmpty(pngs)){
            pansGroup = pngs.stream().map(r -> {
                FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
                BeanUtils.copyProperties(r, imgData);
                return imgData;
            }).collect(Collectors.groupingBy(r -> r.getProductCode()));
        }


        // 查询搭配图  搭配图 需要先根据款号多个查询到搭配，多个搭配 拿到 搭配号  再去查询一遍， 拿出来 根据款号分组，然后存储
        Map<String, List<FindProductDetailImgResp.ImgData>> collcationGroupByProductCode = new HashMap<>();
        // 根据搭配号分组
        Map<String, List<FindProductDetailImgResp.ImgData>> collcationGroupByCollocationCode = new HashMap<>();
        // 无搭配号的搭配图
        Map<String, List<FindProductDetailImgResp.ImgData>> notHaveCollectionCode = new HashMap<>();

        List<ProductCollocaNetDiskImg> productCollocaNetDiskImgs = productCollocaNetDiskImgMapper.selectByProductCodesAndType(requestData.getProductCodes(),ProductDetailNetDiskConstant.COLLOCATION_TYPE);
        if(CollectionUtils.isNotEmpty(productCollocaNetDiskImgs)){
            // 拥有搭配号的  搭配号
            List<String> collocationCodes = productCollocaNetDiskImgs.stream().filter(r -> StringUtils.isNotBlank(r.getProductCode()) && StringUtils.isNotBlank(r.getCollocationCode()))
                    .map(r -> r.getCollocationCode()).collect(Collectors.toList());
            // 没有搭配号的
//            notHaveCollectionCode= productCollocaNetDiskImgs.stream().filter(r -> StringUtils.isNotBlank(r.getProductCode()) && StringUtils.isBlank(r.getCollocationCode()))
//                    .map(r -> {
//                        FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
//                        BeanUtils.copyProperties(r, imgData);
//                        return imgData;
//                    }).collect(Collectors.groupingBy(r->r.getProductCode()));

            //  无搭配号主图  排除A1
            notHaveCollectionCode= mainImgList.stream().filter(r -> StringUtils.isNotBlank(r.getProductCode())  && r.getOverseaFlag().equals(0))
                    .map(r -> {
                        FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
                        BeanUtils.copyProperties(r, imgData);
                        return imgData;
                    }).collect(Collectors.groupingBy(r->r.getProductCode()));


            // 根据搭配号再去查一遍
            if(CollectionUtils.isNotEmpty(collocationCodes)){
                List<ProductCollocaNetDiskImg> findByCollcationCodes = productCollocaNetDiskImgMapper.selectByCollocationCodes(collocationCodes);
                if(CollectionUtils.isNotEmpty(findByCollcationCodes)){
                    collcationGroupByProductCode = findByCollcationCodes.stream().map(r -> {
                        FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
                        BeanUtils.copyProperties(r, imgData);
                        return imgData;
                    }).filter(r->StringUtils.isNotBlank(r.getProductCode())).collect(Collectors.groupingBy(r -> r.getProductCode()));


                    collcationGroupByCollocationCode = findByCollcationCodes.stream().map(r -> {
                        FindProductDetailImgResp.ImgData imgData = new FindProductDetailImgResp.ImgData();
                        BeanUtils.copyProperties(r, imgData);
                        return imgData;
                    }).filter(r->StringUtils.isNotBlank(r.getCollocationCode())).collect(Collectors.groupingBy(r -> r.getCollocationCode()));

                }
            }
        }

        for (String productCode : requestData.getProductCodes()) {
            FindProductDetailImgResp findProductDetailImgResp = new FindProductDetailImgResp();
            findProductDetailImgResp.setProductCode(productCode);
            findProductDetailImgResp.setMainImgs(mainImgListGroupByProductCode.get(productCode));
            findProductDetailImgResp.setDetailsImgs(detailsGroupByProductCode.get(productCode));
            findProductDetailImgResp.setPngs(pansGroup.get(productCode));
            List<FindProductDetailImgResp.ImgData> imgData = collcationGroupByProductCode.get(productCode);
            List<FindProductDetailImgResp.ImgCollocationData> resImg = new ArrayList<>();
            Set<String> hashSet = new HashSet<>();
//            if(CollectionUtils.isNotEmpty(imgData)){
//                for (FindProductDetailImgResp.ImgData imgDatum : imgData) {
//                    if(StringUtils.isNotBlank(imgDatum.getCollocationCode())){
//                        if(hashSet.contains(imgDatum.getProductCode() + imgDatum.getCollocationCode())){
//                            continue;
//                        }
//                        FindProductDetailImgResp.ImgCollocationData collocationData = new FindProductDetailImgResp.ImgCollocationData();
//                        collocationData.setCollocationCode(imgDatum.getCollocationCode());
//                        List<FindProductDetailImgResp.ImgData> imgData1 = collcationGroupByCollocationCode.get(imgDatum.getCollocationCode());
//                        collocationData.setImgDatas(imgData1);
//                        resImg.add(collocationData);
//                        hashSet.add(imgDatum.getProductCode()+ imgDatum.getCollocationCode());
//                    }
//                }
//            }
            // 赋值无搭配号的图
            List<FindProductDetailImgResp.ImgData> notHaveCollectionCodeImgData = notHaveCollectionCode.get(productCode);
            if(CollectionUtils.isNotEmpty(notHaveCollectionCodeImgData)){
                int i = 0 ;
                for (FindProductDetailImgResp.ImgData imgDatum : notHaveCollectionCodeImgData) {
                    FindProductDetailImgResp.ImgCollocationData collocationData = new FindProductDetailImgResp.ImgCollocationData();
                    collocationData.setCollocationCode(imgDatum.getProductCode()+"-"+i);
                    List<FindProductDetailImgResp.ImgData> imgData1 = new ArrayList<>();
                    imgData1.add(imgDatum);
                    collocationData.setImgDatas(imgData1);
                    resImg.add(collocationData);
                    i++;
                }
            }

            findProductDetailImgResp.setCollocationImgs(resImg);
            findProductDetailImgResps.add(findProductDetailImgResp);
        }

        return findProductDetailImgResps;
    }

    private void syncSampleClothLianxiangByExcel(List<Map<String, String>> importData) {
        for (Map<String, String> importDatum : importData) {
            // 获取路径
            if(StringUtils.isBlank(importDatum.get("imgPath"))){
                log.info("syncSampleClothLianxiangByExcel 数据不全= {}", JSONObject.toJSONString(importDatum));
                continue;
            }

            LenovoFileReq lenovoFileReq = new LenovoFileReq();
            lenovoFileReq.setPath(importDatum.get("imgPath"));
            LenovoFileResp lenovoFile = lianXiangYunFilezService.getLenovoFile(lenovoFileReq);
            if(lenovoFile == null || CollectionUtils.isEmpty(lenovoFile.getFileModelList())){
                continue;
            }

            int i = 0;
            boolean firstFlag = true;
            out:while (firstFlag) {
                String imgPath = importDatum.get("imgPath");
                String imgType = importDatum.get("imgType");   // 0  1  2   2是海外

                // 获取图片数据
                LenovoFileResp resp = getLenovoFileResp(imgPath, i);
                if (resp == null || CollectionUtils.isEmpty(resp.getFileModelList())) {
                    break out;
                }
                List<LenovoFileResp.LenovoFileModel> fileModelList = resp.getFileModelList();
                dealSampleClothLianxiangFile(fileModelList,0);
                i++;
            }
        }
    }

    private void dealSampleClothLianxiangFile(List<LenovoFileResp.LenovoFileModel> fileModelList, int pageNo) {
        // 获取正则表达是
        String regex = "^[1-9]\\d*$";
        Pattern pattern = Pattern.compile(regex);

        // 处理样衣号
        List<LenovoFileResp.LenovoFileModel> dealFiles = new ArrayList<>();
        for (LenovoFileResp.LenovoFileModel lenovoFileModel : fileModelList) {
            Boolean dir = lenovoFileModel.getDir();
            pageNo = 0;
            if (dir) {
                int pageNo2  = 0;
                while (true){
                    LenovoFileResp lenovoFileResp = getLenovoFileResp(lenovoFileModel.getPath(), pageNo);
                    if (lenovoFileResp == null || CollectionUtils.isEmpty(lenovoFileResp.getFileModelList())) {
                        break ;
                    }
                    dealSampleClothLianxiangFile(lenovoFileResp.getFileModelList(),pageNo2);
                    pageNo ++;
                }
            }else{
                // 校验文件名是否为纯数字
                try {
                    String path = lenovoFileModel.getPath();
                    String nameAndJpg = path.substring(path.lastIndexOf("/")+1);
                    String name = nameAndJpg.substring(0, nameAndJpg.indexOf("."));
                    if(pattern.matcher(name).matches()){
                        // 匹配
                        dealFiles.add(lenovoFileModel);
                    }
                }catch (Exception e){
                    log.info("dealSampleClothLianxiangFile = {}",JSONObject.toJSONString(lenovoFileModel),e);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(dealFiles)){
            dealAndInsertSampleClothLianxiangFile(dealFiles);
        }
    }

    /**
     * 处理并且插入数据
     * @param dealFiles
     */
    private void dealAndInsertSampleClothLianxiangFile(List<LenovoFileResp.LenovoFileModel> dealFiles) {
        for (LenovoFileResp.LenovoFileModel dealFile : dealFiles) {
            // 查询是否这个数据数据库是否已经有了  如果有了  那么则不处理
            String path = dealFile.getPath();
            String nameAndJpg = path.substring(path.lastIndexOf("/")+1);
            String name = nameAndJpg.substring(0, nameAndJpg.indexOf("."));
            //
            String substring = path.substring(0, path.lastIndexOf("/"));
            String sampleClothCode = substring.substring(substring.lastIndexOf("/")+1);
            List<SampleClothNetDiskImg> list = sampleClothNetDiskImgMapper.selectSampleClothCodeAndName(sampleClothCode,name,IsDeleteEnum.NORMAL.getCode());
            if(CollectionUtils.isNotEmpty(list)){
                continue;
            }
            // 插入数据
            SampleClothNetDiskImg sampleClothNetDiskImg = new SampleClothNetDiskImg();
            sampleClothNetDiskImg.setId(IdLeaf.getId(IdConstant.SAMPLE_CLOTH_NET_DISK_IMG));
            sampleClothNetDiskImg.setSampleClothCode(sampleClothCode);
            sampleClothNetDiskImg.setFileName(name);
            sampleClothNetDiskImg.setNeid(dealFile.getNeid());
            sampleClothNetDiskImg.setNsid(dealFile.getNsid());
            sampleClothNetDiskImg.setIsDel(IsDeleteEnum.NORMAL.getCode());
            sampleClothNetDiskImg.setCreateTime(new Date());
            sampleClothNetDiskImg.setUpdateTime(new Date());
            sampleClothNetDiskImgMapper.insert(sampleClothNetDiskImg);
        }
    }


    private void mergeEComDataToDataBase(List<Map<String, String>> importData) {
        // 每一条去处理
        for (Map<String, String> importDatum : importData) {
            //校验
            String skcs = importDatum.get("skcs");
            if(StringUtils.isBlank(skcs)){
                log.info("mergeEComDataToDataBase 数据不全= {}", JSONObject.toJSONString(importDatum));
                continue;
            }
            // 开始处理数据
            String[] skcsSplit = skcs.split(",");
            // skc 数据
            List<String> list = Arrays.asList(skcsSplit);
            if(CollectionUtils.isEmpty(list)){
                log.info("mergeEComDataToDataBase 切割字符串失败，过滤此条数据 = {}",JSONObject.toJSONString(importDatum));
                continue;
            }
            // 生成搭配编码
            String collocationCode = "COLLOCATION-"+IdLeaf.getId(IdConstant.COLLOCATION_CODE);

            // 批量查询
            List<ProductCollocaNetDiskImg> productCollocaNetDiskImgs = productCollocaNetDiskImgMapper.selectBySkcs(list);
            for (ProductCollocaNetDiskImg productCollocaNetDiskImg : productCollocaNetDiskImgs) {
                // 在一个流程中 是不可以进行替换的  一个流程 10分钟 多个流程中是可以替换的
                Date now = new Date();
                if(StringUtils.isNotBlank(productCollocaNetDiskImg.getCollocationCode()) &&  (now.getTime() - productCollocaNetDiskImg.getUpdateTime().getTime()) / 1000 < 1200){
                    continue;
                }
                ProductCollocaNetDiskImg update = new ProductCollocaNetDiskImg();
                update.setId(productCollocaNetDiskImg.getId());
                update.setCollocationCode(collocationCode);
                update.setUpdateTime(new Date());
                productCollocaNetDiskImgMapper.updateByPrimaryKeySelective(update);
            }
        }
    }

    private void mergeBrandDataToDataBase(List<Map<String, String>> importData) {
        // 品牌部会传递搭配号  进行匹配
        for (Map<String, String> importDatum : importData) {
            //校验
            String collocationCode = importDatum.get("collocationCode");
            String skc = importDatum.get("skc");
            if(StringUtils.isBlank(collocationCode) || StringUtils.isBlank(skc)){
                log.info("mergeBrandDataToDataBase 数据不全= {}", JSONObject.toJSONString(importDatum));
                continue;
            }
        }
        // 分组 根据搭配号
        Map<String, List<Map<String, String>>> collocationCode = importData.stream().filter(r->StringUtils.isNotBlank(r.get("collocationCode"))).collect(Collectors.groupingBy(r -> r.get("collocationCode")));
        for (String collcaCode : collocationCode.keySet()) {
            //落库数据
            List<Map<String, String>> params = collocationCode.get(collcaCode);
            // 查询数据
            List<ProductCollocaNetDiskImg> productCollocaNetDiskImgs = productCollocaNetDiskImgMapper.selectByCollocationCode(collcaCode, IsDeleteEnum.NORMAL.getCode());
            if(CollectionUtils.isNotEmpty(productCollocaNetDiskImgs)){
                //将当前记录进行删除  新增多条
                for (ProductCollocaNetDiskImg detailNetDiskImg : productCollocaNetDiskImgs) {
                    ProductCollocaNetDiskImg update = new ProductCollocaNetDiskImg();
                    update.setId(detailNetDiskImg.getId());
                    update.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
                    productCollocaNetDiskImgMapper.updateByPrimaryKeySelective(update);
                }
                List<ProductCollocaNetDiskImg> insertList = new ArrayList<>();
                // 当前的这个数据
                ProductCollocaNetDiskImg productCollocaNetDiskImg = productCollocaNetDiskImgs.get(0);
                for (Map<String, String> param : params) {
                    ProductCollocaNetDiskImg insertData = new ProductCollocaNetDiskImg();
                    BeanUtils.copyProperties(productCollocaNetDiskImg,insertData);
                    insertData.setId(IdLeaf.getId(IdConstant.PRODUCT_COLLOCA_NET_DISK_IMG));
                    String colorNo = null;
                    String productCode = null;
                    try {
                        String skcCode = param.get("skc");
                        colorNo = skcCode.substring(skcCode.length() - 3);
                        productCode= skcCode.substring(0, skcCode.length() - 3);
                    }catch (Exception e){
                        log.info("mergeBrandDataToDataBase 切割字符串出错 = {}",JSONObject.toJSONString(param),e);
                    }

                    insertData.setProductCode(productCode);
                    insertData.setColorNo(colorNo);
                    insertList.add(insertData);
                }
                if(CollectionUtils.isNotEmpty(insertList)){
                    productCollocaNetDiskImgMapper.batchInsert(insertList);
                }
            }
        }
    }

    private void syncLianxiangImgByExcel(List<Map<String, String>> importData) {
        //遍历处理
        for (Map<String, String> importDatum : importData) {
            if(StringUtils.isBlank(importDatum.get("imgPath"))|| StringUtils.isBlank(importDatum.get("imgType")) || StringUtils.isBlank(importDatum.get("type"))){
                log.info("syncLianxiangImgByExcel 数据不全= {}", JSONObject.toJSONString(importDatum));
                continue;
            }


            LenovoFileReq lenovoFileReq = new LenovoFileReq();
            lenovoFileReq.setPath(importDatum.get("imgPath"));
            LenovoFileResp lenovoFile = lianXiangYunFilezService.getLenovoFile(lenovoFileReq);
            if(lenovoFile == null || CollectionUtils.isEmpty(lenovoFile.getFileModelList())){
                continue;
            }

            int i = 0;
            boolean firstFlag = true;
            out:while (firstFlag) {
                String imgPath = importDatum.get("imgPath");
                String imgType = importDatum.get("imgType");   // 0  1  2   2是海外

                // 获取图片数据
                LenovoFileResp resp = getLenovoFileResp(imgPath, i);
                if (resp == null || CollectionUtils.isEmpty(resp.getFileModelList())) {
                    break out;
                }
                List<LenovoFileResp.LenovoFileModel> fileModelList = resp.getFileModelList();

                if(importDatum.get("type").equals("0")){
                    // 品牌
                    dealBrandImg(fileModelList,imgPath,0,imgType);
                }else if(importDatum.get("type").equals("1")){
                    readLianxiangFile(fileModelList,0,imgPath,imgType,importDatum.get("type"));
                }else {
                    readLianxiangFile(fileModelList,0,imgPath,imgType,importDatum.get("type"));
                }
                i++;
            }
        }
    }

    private void readLianxiangFile(List<LenovoFileResp.LenovoFileModel> fileModelList,int pageNo,String imgPath,String imgType,String type) {
        // 处理电商的
        List<LenovoFileResp.LenovoFileModel> dealFiles = new ArrayList<>();

        //  1-A   // 款号   // 色号  // 电商
        for (LenovoFileResp.LenovoFileModel lenovoFileModel : fileModelList) {
            Boolean dir = lenovoFileModel.getDir();
            pageNo = 0;
            if (dir) {
                int pageNo2  = 0;
                while (true){
                    LenovoFileResp lenovoFileResp = getLenovoFileResp(lenovoFileModel.getPath(), pageNo);
                    if (lenovoFileResp == null || CollectionUtils.isEmpty(lenovoFileResp.getFileModelList())) {
                        break ;
                    }
                    readLianxiangFile(lenovoFileResp.getFileModelList(),pageNo2,imgPath,imgType,type);
                    pageNo ++;
                }
            }else{
                if(type.equals("1")){
                    dealFiles.add(lenovoFileModel);
                }else{
                    // 海外处理
                    dealOverseasImg(lenovoFileModel);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(dealFiles)){
            dealBrandImg(dealFiles,imgPath,1,imgType);
        }
    }

    private void dealOverseasImg(LenovoFileResp.LenovoFileModel lenovoFileModel) {
        // 处理图片
        ProductOverseasNetDiskImg productDetailNetDiskImg = new ProductOverseasNetDiskImg();
        // //企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款/色/文件.jpg
        String path = lenovoFileModel.getPath();
        // 文件名  将款和色处理出来
        String fileName = path.substring(path.lastIndexOf("/") + 1);
        // 生成为
        //企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款/色
        String substring = path.substring(0, path.lastIndexOf("/"));
        // 色
        String colorNo = substring.substring(substring.lastIndexOf("/"));
        // 生成为
        // 企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款
        String productPath = substring.substring(0, path.lastIndexOf("/"));
        // 款
        String productCode = productPath.substring(productPath.lastIndexOf("/"));

        if(!fileName.contains("(")){
            return;
        }
        Integer type = 0;

        // 查询当前这个款+色的 有的数据 进行删除 然后重新添加
        List<ProductOverseasNetDiskImg> list = productOverseasNetDiskImgMapper.selectBySkcAndIsDel(productCode,colorNo,IsDeleteEnum.NORMAL.getCode(),type);
        if(CollectionUtils.isNotEmpty(list)){
            for (ProductOverseasNetDiskImg detailNetDiskImg : list) {
                ProductOverseasNetDiskImg update = new ProductOverseasNetDiskImg();
                update.setId(detailNetDiskImg.getId());
                update.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
                productOverseasNetDiskImgMapper.updateByPrimaryKeySelective(update);
            }
        }
        String neid = lenovoFileModel.getNeid();
        String nsid = lenovoFileModel.getNsid();

        productDetailNetDiskImg.setId(IdLeaf.getId(IdConstant.PRODUCT_DETAIL_NET_DISK_IMG));
        productDetailNetDiskImg.setProductCode(productCode);
        productDetailNetDiskImg.setColorNo(colorNo);
        productDetailNetDiskImg.setIsDel(IsDeleteEnum.NORMAL.getCode());
        productDetailNetDiskImg.setNeid(neid);
        productDetailNetDiskImg.setNsid(nsid);
        productDetailNetDiskImg.setCreateTime(new Date());
        productDetailNetDiskImg.setUpdateTime(new Date());

        // 插入数据
        productOverseasNetDiskImgMapper.insertSelective(productDetailNetDiskImg);
    }


    /**
     *
     * @param fileModelList
     * @param imgPath      导入路径
     * @param importType  导入类型   0 品牌  1 电商
     */
    private void dealBrandImg(List<LenovoFileResp.LenovoFileModel> fileModelList, String imgPath,Integer importType,String imgType) {
        //  2024-03-11 -沟通   沟通结果为    路径中包含  单品   则是 单品图片和细节图片  路径中包含搭配   则为搭配图   需要二次导入
        if(imgType.equals("单品正面") || imgType.equals("细节图")){
            // 获取图片名称
            List<ProductDetailNetDiskImg> insertList  = new ArrayList<>();
            for (LenovoFileResp.LenovoFileModel lenovoFileModel : fileModelList) {
                ProductDetailNetDiskImg productDetailNetDiskImg = null;
                try {
                    productDetailNetDiskImg = getProductDetailNetDiskImg(lenovoFileModel,imgType,importType);
                }catch (Exception e){
                    log.info("getProductDetailNetDiskImg = {}",JSONObject.toJSONString(lenovoFileModel),e);
                }
                if(productDetailNetDiskImg == null){
                    continue;
                }
                insertList.add(productDetailNetDiskImg);
            }
            // 批量插入
            if(CollectionUtils.isNotEmpty(insertList)){
                if(insertList.size()>900){
                    List<List<ProductDetailNetDiskImg>> partition = Lists.partition(insertList, 800);
                    for (List<ProductDetailNetDiskImg> productCollocaNetDiskImgs : partition) {
                        productDetailNetDiskImgMapper.batchInsert(productCollocaNetDiskImgs);
                    }
                }else{
                    productDetailNetDiskImgMapper.batchInsert(insertList);
                }
            }
        }else if(imgType.equals("搭配图")){
            List<ProductCollocaNetDiskImg> insertList  = new ArrayList<>();
            for (LenovoFileResp.LenovoFileModel lenovoFileModel : fileModelList) {
                ProductCollocaNetDiskImg productCollocaNetDiskImg = null;
                try {
                    productCollocaNetDiskImg = getProductCollocationNetDiskImg(lenovoFileModel,importType);
                }catch (Exception e){
                    log.info("getProductCollocationNetDiskImg = {}",JSONObject.toJSONString(lenovoFileModel),e);
                }

                if(productCollocaNetDiskImg == null){
                    continue;
                }
                insertList.add(productCollocaNetDiskImg);
            }
            if(CollectionUtils.isNotEmpty(insertList)){
                if(insertList.size()>900){
                    List<List<ProductCollocaNetDiskImg>> partition = Lists.partition(insertList, 800);
                    for (List<ProductCollocaNetDiskImg> productCollocaNetDiskImgs : partition) {
                        productCollocaNetDiskImgMapper.batchInsert(productCollocaNetDiskImgs);
                    }
                }else{
                    productCollocaNetDiskImgMapper.batchInsert(insertList);
                }
            }
        }
    }

    private ProductCollocaNetDiskImg getProductCollocationNetDiskImg(LenovoFileResp.LenovoFileModel lenovoFileModel, Integer importType) {
        ProductCollocaNetDiskImg productCollocaNetDiskImg = new ProductCollocaNetDiskImg();
        String path = lenovoFileModel.getPath();
        // 文件名
        String fileName = path.substring(path.lastIndexOf("/") + 1);
        if(!fileName.contains("(") && !fileName.contains("全身图_")){
            return null;
        }
        // 搭配号 品牌为 搭配号  电商为 款色号
        //根据搭配号进行查询
        String collocationName = "";
        String colorNo = "";
        String productCode = "";
        if(importType.equals(0)){
            collocationName = fileName.substring(0, fileName.indexOf("("));
            if(fileName.contains("(1)")){
                // 品牌
                List<ProductCollocaNetDiskImg> list = productCollocaNetDiskImgMapper.selectByNeId(lenovoFileModel.getNeid(),IsDeleteEnum.NORMAL.getCode());
                if(CollectionUtils.isNotEmpty(list)){
                    // 不处理
                    return null;
//                    for (ProductCollocaNetDiskImg detailNetDiskImg : list) {
//                        ProductCollocaNetDiskImg update = new ProductCollocaNetDiskImg();
//                        update.setId(detailNetDiskImg.getId());
//                        update.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
//                        productCollocaNetDiskImgMapper.updateByPrimaryKeySelective(update);
//                    }
                }
            }
        }else if(importType.equals(1)){
            // 电商

            if(!fileName.contains("全身图_")){
                //不处理
                return null;
            }

            if(fileName.contains("(")){
                //不处理  包含括号 则不读取
                return null;
            }
            //企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款/色
            String substring = path.substring(0, path.lastIndexOf("/"));
            // 色
            colorNo = substring.substring(substring.lastIndexOf("/")+1);
            if(colorNo.length()> 3){
                colorNo = colorNo.substring(0,3);
            }
            // 生成为
            // 企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款
            String productPath = substring.substring(0, substring.lastIndexOf("/"));
            // 款
            productCode = productPath.substring(productPath.lastIndexOf("/")+1);
            //分别拿到款和色
            List<ProductCollocaNetDiskImg> list = productCollocaNetDiskImgMapper.selectByNeId(lenovoFileModel.getNeid(),IsDeleteEnum.NORMAL.getCode());
            if(CollectionUtils.isNotEmpty(list)){
                return null;
            }

//            List<ProductCollocaNetDiskImg> list = productCollocaNetDiskImgMapper.selectByProductCodeAndColorNo(productCode,colorNo,IsDeleteEnum.NORMAL.getCode());
//            if(CollectionUtils.isNotEmpty(list)){
//                for (ProductCollocaNetDiskImg detailNetDiskImg : list) {
//                    ProductCollocaNetDiskImg update = new ProductCollocaNetDiskImg();
//                    update.setId(detailNetDiskImg.getId());
//                    update.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
//                    productCollocaNetDiskImgMapper.updateByPrimaryKeySelective(update);
//                }
//            }
        }
        String neid = lenovoFileModel.getNeid();
        String nsid = lenovoFileModel.getNsid();
        productCollocaNetDiskImg.setId(IdLeaf.getId(IdConstant.PRODUCT_COLLOCA_NET_DISK_IMG));
        if(importType.equals(0)){
            productCollocaNetDiskImg.setCollocationCode(collocationName);
        }else if(importType.equals(1)){
            productCollocaNetDiskImg.setProductCode(productCode);
            productCollocaNetDiskImg.setColorNo(colorNo);
        }
        productCollocaNetDiskImg.setIsDel(IsDeleteEnum.NORMAL.getCode());
        productCollocaNetDiskImg.setNeid(neid);
        productCollocaNetDiskImg.setNsid(nsid);
        productCollocaNetDiskImg.setCreateTime(new Date());
        productCollocaNetDiskImg.setUpdateTime(new Date());
        productCollocaNetDiskImg.setType(ProductDetailNetDiskConstant.COLLOCATION_TYPE);
        return productCollocaNetDiskImg;

    }

    private ProductDetailNetDiskImg getProductDetailNetDiskImg(LenovoFileResp.LenovoFileModel lenovoFileModel, String imgType, Integer importType) {
        ProductDetailNetDiskImg productDetailNetDiskImg = new ProductDetailNetDiskImg();
        String path = lenovoFileModel.getPath();
        // 文件名
        String fileName = path.substring(path.lastIndexOf("/") + 1);
        if(!fileName.contains("(") &&
                !fileName.contains("正面图") && !fileName.contains("_细节图_")
                && !fileName.contains("_全身图_")
        &&  !fileName.contains("a1") && !fileName.contains("A1")){
            return null;
        }

        String colorNo = "";
        String productCode = "";
        Integer type = null;
        Integer overseaFlag = 0;

        //文件名包含  (1)  则是单品主图   (2) 则是细节图
        if(importType.equals(1)){
            // 电商处理文件名

            // //企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款/色/文件.jpg
            // 文件名  将款和色处理出来
            // 生成为
            //企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款/色
            String substring = path.substring(0, path.lastIndexOf("/"));
            // 色
            colorNo = substring.substring(substring.lastIndexOf("/") + 1);
            if(colorNo.length()> 3){
                colorNo = colorNo.substring(0,3);
            }
            // 生成为
            // 企业文件/JNBYGROUP/电商运营中心/图片与视频/01.JNBY/01.详情页产品资料/FY24—23AW、24SS/商品图片/1A-/款
            String productPath = substring.substring(0, substring.lastIndexOf("/"));
            // 款
            productCode = productPath.substring(productPath.lastIndexOf("/")+1);

            // 电商  电商按照文件名来处理  正面图是最后一位
            if((fileName.contains("(正面图)") && fileName.substring(fileName.indexOf(")")+1,fileName.indexOf(")")+2).equals("."))){
                 // 主图
                productDetailNetDiskImg.setType(ProductDetailNetDiskConstant.MAIN_TYPE);
                type = ProductDetailNetDiskConstant.MAIN_TYPE;
            }else if(fileName.contains("yk(")){
                // yk(1).jpg 读取   yk(1)(1).jpg 不读取
                int i = fileName.indexOf(")");
                String realFileName = fileName.substring(i + 1);
                if(realFileName.toUpperCase().equals(".JPG") || realFileName.toUpperCase().equals(".PNG")){
                    // 读取 其他则不读取
                    //细节图
                    productDetailNetDiskImg.setType(ProductDetailNetDiskConstant.DETAIL_TYPE);
                    type = ProductDetailNetDiskConstant.DETAIL_TYPE;
                }else {
                    return null;
                }
            }else if(fileName.contains("_细节图_") || fileName.contains("_全身图_")) {
                productDetailNetDiskImg.setType(ProductDetailNetDiskConstant.DETAIL_TYPE);
                type = ProductDetailNetDiskConstant.DETAIL_TYPE;
                overseaFlag = 1;
            }else if(fileName.substring(0,fileName.indexOf(".")).equals("a1")
                    || fileName.substring(0,fileName.indexOf(".")).equals("A1")){
                productDetailNetDiskImg.setType(ProductDetailNetDiskConstant.MAIN_TYPE);
                type = ProductDetailNetDiskConstant.MAIN_TYPE;
                overseaFlag = 1;
            }else{
                return null;
            }
        }else{
            // 款色号
            String skcName = fileName.substring(0, fileName.indexOf("("));

            //分别拿到款和色  品牌的处理
             colorNo = skcName.substring(skcName.length() - 3);
             productCode = skcName.substring(0, skcName.length() - 3);

            // 品牌处理 imgType.equals("单品正面")
            if(fileName.contains("(1)") && (!fileName.contains(".png") && !fileName.contains(".PNG"))){
                productDetailNetDiskImg.setType(ProductDetailNetDiskConstant.MAIN_TYPE);
                type = ProductDetailNetDiskConstant.MAIN_TYPE;
                //imgType.equals("细节图")
            }else if(fileName.contains("(3)")  || (fileName.contains("(4)")) || (fileName.contains("(5)")) || (fileName.contains("(6)"))){
                productDetailNetDiskImg.setType(ProductDetailNetDiskConstant.DETAIL_TYPE);
                type = ProductDetailNetDiskConstant.DETAIL_TYPE;
            }else if(fileName.contains("(1)") && (fileName.contains(".png") || fileName.contains(".PNG"))){
                productDetailNetDiskImg.setType(ProductDetailNetDiskConstant.CATE_TYPE);
                type = ProductDetailNetDiskConstant.CATE_TYPE;
            }else {
                return null;
            }
        }
        if(StringUtils.isBlank(colorNo) || StringUtils.isBlank(productCode)){
            return null;
        }

        // 根据文件id进行查询 如果能查询到  则不更新  如果查询不到 则新增
        List<ProductDetailNetDiskImg> list = productDetailNetDiskImgMapper.selectByNeId(lenovoFileModel.getNeid());
        if(CollectionUtils.isNotEmpty(list)){
            return null;
        }
//        List<ProductDetailNetDiskImg> list = productDetailNetDiskImgMapper.selectBySkcAndIsDel(productCode,colorNo,IsDeleteEnum.NORMAL.getCode(),type);
//        if(CollectionUtils.isNotEmpty(list)){
//            for (ProductDetailNetDiskImg detailNetDiskImg : list) {
//                ProductDetailNetDiskImg update = new ProductDetailNetDiskImg();
//                update.setId(detailNetDiskImg.getId());
//                update.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
//                productDetailNetDiskImgMapper.updateByPrimaryKeySelective(update);
//            }
//        }
        String neid = lenovoFileModel.getNeid();
        String nsid = lenovoFileModel.getNsid();

        productDetailNetDiskImg.setId(IdLeaf.getId(IdConstant.PRODUCT_DETAIL_NET_DISK_IMG));
        productDetailNetDiskImg.setProductCode(productCode);
        productDetailNetDiskImg.setColorNo(colorNo);
        productDetailNetDiskImg.setIsDel(IsDeleteEnum.NORMAL.getCode());
        productDetailNetDiskImg.setNeid(neid);
        productDetailNetDiskImg.setNsid(nsid);
        productDetailNetDiskImg.setCreateTime(new Date());
        productDetailNetDiskImg.setUpdateTime(new Date());
        productDetailNetDiskImg.setOverseaFlag(overseaFlag);
        return productDetailNetDiskImg;
    }


    private LenovoFileResp getLenovoFileResp(String path,int pageNum) {
        try {
            Thread.sleep(610);
        }catch (Exception e){
            log.info("睡500毫秒");
        }
        LenovoFileReq params = new LenovoFileReq();
        params.setPath(path);
        params.setPage_num(pageNum+"");
        LenovoFileResp resp = lianXiangYunFilezService.getLenovoFile(params);
        return resp;
    }



}
