package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2025/5/16 10:44
 */
@Data
@TableName(value = "PRODUCT_REMINDER_INFO")
public class ProductReminderInfo {

    @TableId
    private String id;

    @TableField(value = "TYPE")
    private Integer type;

    @TableField(value = "TYPE_NAME")
    private String typeName;

    @TableField(value = "TEMPLATE_CODE")
    private String templateCode;

    @TableField(value = "TEMPLATE_TITLE")
    private String templateTitle;

    @TableField(value = "TEMPLATE_CONCAT")
    private String templateConcat;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    @TableField(value = "IS_DELETED")
    private String isDeleted;


    @TableField(value = "REMINDER_TIME")
    private Date reminderTime;
}
