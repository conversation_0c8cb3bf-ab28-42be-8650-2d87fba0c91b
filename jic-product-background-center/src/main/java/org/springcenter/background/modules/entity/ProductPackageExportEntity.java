package org.springcenter.background.modules.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jnby.common.RemotingSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date:2024/7/31 11:19
 */
@Data
public class ProductPackageExportEntity extends RemotingSerializable implements Serializable {

    @ExcelProperty("商品id")
    @ApiModelProperty(value = "商品id")
    private Long id;

    @ExcelProperty("商品款号")
    @ApiModelProperty(value = "商品款号 -- 接口获取款号信息不反回")
    private String name;
}
