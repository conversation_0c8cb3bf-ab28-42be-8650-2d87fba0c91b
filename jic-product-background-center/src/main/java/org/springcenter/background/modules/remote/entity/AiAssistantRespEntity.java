package org.springcenter.background.modules.remote.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/9/19 14:09
 */
@Data
public class AiAssistantRespEntity implements Serializable {

    private String request_id;

    private String code;

    private String result;

    private ResultInfo result_info;

    private Source source;

    private Info info;

    private Double cost_time;

    private Long timestamp;

    @Data
    public static class Info {
        private String msg;
        private String error_msg;
    }

    @Data
    public static class Source {
        private String mode;

        private List<AiAssistantReqEntity.InputContent> input;
        private Double temperature;
        private Integer top_p;
        private Integer frequency_penalty;
        private Integer present_penalty;
        private Integer request_timeout;
        private Integer max_tokens;
    }

    @Data
    public static class ResultInfo {
        private Usage usage;
    }

    @Data
    public static class Usage {
        private Long prompt_tokens;

        private Long completion_tokens;

        private Long total_tokens;
    }
}
