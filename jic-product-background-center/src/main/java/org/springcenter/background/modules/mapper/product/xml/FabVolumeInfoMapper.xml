<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.FabVolumeInfoMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.FabVolumeInfo">
        <id column="ID" property="id" />
        <result column="BRAND_CODE" property="brandCode" />
        <result column="BRAND_NAME" property="brandName" />
        <result column="BAND_ID" property="bandId" />
        <result column="BAND_NAME" property="bandName" />
        <result column="YEAR" property="year" />
        <result column="SEASON_GOODS" property="seasonGoods" />
        <result column="DISPLAY_TIME" property="displayTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="OPERATOR" property="operator" />
        <result column="CLOB_JSON" property="clobJson" />
        <result column="FAB_NAME" property="fabName" />
        <result column="BAND_ORDER" property="bandOrder"/>
        <result column="TYPE" property="type"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, BRAND_CODE, BRAND_NAME, BAND_ID, BAND_NAME, YEAR, SEASON_GOODS, DISPLAY_TIME,
        IS_DELETED, CREATE_TIME, UPDATE_TIME, OPERATOR, CLOB_JSON, FAB_NAME, BAND_ORDER, TYPE
    </sql>







    <select id="selectDisplayIsToday" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM FAB_VOLUME_INFO
        WHERE IS_DELETED = 0
          AND to_char(DISPLAY_TIME,'yyyyMMdd') = to_char(sysdate,'yyyyMMdd')
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM FAB_VOLUME_INFO
        <if test="list != null and list.size() > 0">
            WHERE id in
            <foreach collection="list" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>


    <update id="updateParamsById">
        UPDATE FAB_VOLUME_INFO
        set OPERATOR = #{info.operator},
        <if test="info.clobJson != null and info.clobJson != ''">
            CLOB_JSON = #{info.clobJson},
        </if>
        <if test="info.displayTime != null">
            DISPLAY_TIME = #{info.displayTime},
        </if>
        <if test="info.isDeleted != null">
            IS_DELETED = #{info.isDeleted},
        </if>
        UPDATE_TIME = sysdate
        WHERE ID = #{info.id}
    </update>

    <select id="selectByParam" resultMap="BaseResultMap">
        SELECT ID, BRAND_CODE, BRAND_NAME, BAND_ID, BAND_NAME, YEAR, SEASON_GOODS, DISPLAY_TIME,
        IS_DELETED, CREATE_TIME, UPDATE_TIME, OPERATOR, CLOB_JSON, FAB_NAME, BAND_ORDER, TYPE,
        case when DISPLAY_TIME > sysdate then 1 else 0 end AS status
        FROM FAB_VOLUME_INFO
        WHERE IS_DELETED = 0
        <if test="item.fabType != null">
            AND TYPE = #{item.fabType}
        </if>
        <if test="item.years != null and item.years.size() > 0">
            and YEAR in
            <foreach collection="item.years" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="item.brandIds != null and item.brandIds.size() > 0">
            and BRAND_CODE in
            <foreach collection="item.brandIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="item.bandIds != null and item.bandIds.size() > 0">
            and BAND_ID in
            <foreach collection="item.bandIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="item.type != null and item.type != ''">
            and DISPLAY_TIME is not null
        </if>
        <if test="item.goodSeasons != null and item.goodSeasons.size() > 0">
            and SEASON_GOODS in
            <foreach collection="item.goodSeasons" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="item.brandNames != null and item.brandNames.size() > 0">
            and BRAND_NAME in
            <foreach collection="item.brandNames" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="item.fabName != null and item.fabName != ''">
            and FAB_NAME like concat(concat('%',#{item.fabName}),'%')
        </if>
        <if test="item.type != null and item.type != ''">
            order by status asc, YEAR desc, BRAND_CODE desc, BRAND_NAME desc, BAND_ORDER desc
        </if>
        <if test="item.type == null or item.type == ''">
            order by YEAR desc, BRAND_CODE desc, BRAND_NAME desc, BAND_ORDER desc
        </if>
    </select>

</mapper>
