package org.springcenter.background.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.StoreGoodSpuResp;
import org.springcenter.product.api.dto.StoreGoodsForZhReq;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/8/13 21:00
 */
public interface GetStoreSpuService {

    /**
     * 获取门店下商品的数据
     * @param requestData 入参
     * @param page 分页
     * @return 返回
     */
    List<StoreGoodSpuResp> searchGoodsByStore(StoreGoodsForZhReq requestData, Page page);
}
