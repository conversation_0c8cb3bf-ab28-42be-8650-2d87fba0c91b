<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.CustomsDeclarationMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.CustomsDeclaration">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="season" column="SEASON" jdbcType="VARCHAR"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="neid" column="NEID" jdbcType="VARCHAR"/>
            <result property="nsid" column="NSID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NAME,SEASON,
        IS_DEL,CREATE_TIME,UPDATE_TIME,
        NEID,NSID
    </sql>
    <select id="selectByNeIdAndNsid"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from CUSTOMS_DECLARATION
        where NEID = #{neid} and nsid = #{nsid} and IS_DEL = 0
    </select>
    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="org.springcenter.product.api.dto.CustomsDeclarationReq">
        select <include refid="Base_Column_List"></include> from CUSTOMS_DECLARATION
        where is_del = 0
        <if test="name != null and name != '' ">
            and name  like concat('%',concat(#{name},'%'))
        </if>
        <if test="season != null and season != '' ">
            and season = #{season}
        </if>
    </select>
    <select id="selectByNames"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from CUSTOMS_DECLARATION
        where is_del = 0 and name in
                             <foreach collection="names" item="item" separator="," open="(" close=")">
                                 #{item}
                             </foreach>
    </select>
</mapper>
