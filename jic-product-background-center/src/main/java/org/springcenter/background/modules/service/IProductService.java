package org.springcenter.background.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.ProductSkcResp;
import org.springcenter.product.api.dto.QueryGoodsReq;

import java.util.List;

/**
 * 产品service
 *
 * <AUTHOR>
 * @version 1.0
 * @date 3/15/21 11:16 AM
 */
public interface IProductService {

    /**
     * 查询SKC商品信息
     *
     * @param context
     * @param page
     * @return
     */
    List<ProductSkcResp> searchGoodsSkc(QueryGoodsReq context, Page page, String component);


    /**
     * 查询商品SKC详情，含有标签
     * @param id name+colorno
     * @return
     */
    List<ProductSkcResp> findGoodsSkcDetailByIds(List<String> id);
}
