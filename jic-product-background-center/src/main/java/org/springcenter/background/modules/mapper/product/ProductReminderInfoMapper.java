package org.springcenter.background.modules.mapper.product;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.ProductReminderInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/10 16:47
 */

public interface ProductReminderInfoMapper extends BaseMapper<ProductReminderInfo> {

    List<ProductReminderInfo> selectReminderListByParams(@Param("type") Integer type);

    void delReminderInfoById(@Param("id") String requestData);

    List<ProductReminderInfo> selectByType(@Param("type") int type);
}
