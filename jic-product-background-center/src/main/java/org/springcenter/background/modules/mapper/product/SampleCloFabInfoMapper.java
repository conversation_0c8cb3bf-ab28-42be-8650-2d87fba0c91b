package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.SampleCloFabInfo;
import org.springcenter.product.api.dto.SampleProductAutoNameReq;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date:2023/12/8 11:05
 */
public interface SampleCloFabInfoMapper extends BaseMapper<SampleCloFabInfo> {

    void batchInsert(@Param("list") List<SampleCloFabInfo> partBatchInsert);

    void updateByParam(@Param("data") SampleProductAutoNameReq requestData);

    @MapKey(value = "sampleCode")
    Map<String, SampleCloFabInfo> selectBySampleCodes(@Param("list") List<String> collect);
}
