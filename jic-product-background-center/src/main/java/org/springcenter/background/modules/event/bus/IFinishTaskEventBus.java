package org.springcenter.background.modules.event.bus;

import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.background.modules.event.BaseEventBus;
import org.springcenter.background.modules.event.IFinishTaskEvent;
import org.springcenter.background.modules.event.handler.IFinishTaskEventHandler;
import org.springcenter.background.modules.service.IBusEventsService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date:2024/3/14 15:30
 */
@Service
@Slf4j
public class IFinishTaskEventBus implements InitializingBean, BaseEventBus<IFinishTaskEvent> {


    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private IFinishTaskEventHandler finishTaskEventHandler;

    @Autowired
    private IBusEventsService iBusEventsService;



    @Override
    public void post(IFinishTaskEvent finishTaskEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(finishTaskEvent);
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(finishTaskEventHandler);
    }
}
