package org.springcenter.background.modules.entity.productFab;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2024/7/8 13:53
 */
@Data
public class CellSizeEntity {

    @ApiModelProperty(value = "对外名称")
    private String outsideFiled;

    @ApiModelProperty(value = "对内名称")
    private String systemFiled;

    @ApiModelProperty(value = "测量说明")
    private String clff;

    @ApiModelProperty(value = "是否有两个")
    private Boolean isDouble;

    @ApiModelProperty(value = "小类配置id")
    private Integer smallClassId;

}
