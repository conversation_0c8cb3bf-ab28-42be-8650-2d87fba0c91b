package org.springcenter.background.modules.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.background.modules.entity.ProductPackageExportEntity;
import org.springcenter.background.modules.enums.RedisKeysEnum;
import org.springcenter.background.modules.model.product.PackageFilterInfo;
import org.springcenter.background.modules.model.product.PackageImportInfo;
import org.springcenter.background.modules.model.product.PackageTemplate;
import org.springcenter.background.modules.service.IProductPackageCacheAsyncService;
import org.springcenter.background.modules.service.IProductPackageService;
import org.springcenter.background.modules.util.DateUtil;
import org.springcenter.background.modules.util.RedisConstantUtil;
import org.springcenter.background.modules.util.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/9/23 15:09
 */
@Slf4j
@Service
@RefreshScope
@EnableAsync
public class ProductPackageCacheAsyncServiceImpl implements IProductPackageCacheAsyncService {

    @Autowired
    private IProductPackageService productPackageService;

    @Autowired
    private RedisService redisService;


    @Override
    @Async
    public void cashPackageInfoByPacId(PackageTemplate template, List<PackageFilterInfo> filterInfos, List<PackageImportInfo> importInfos) {
        log.info("=========异步开始：{}",  DateUtil.currentDate());
        List<ProductPackageExportEntity> entities = productPackageService.dealEsInfo(template, filterInfos, importInfos);

        // 如果只有导入的数据 需要根据导入的数据排序
        List<Long> pacProductIds = new ArrayList<>();
        PackageImportInfo whiteImportInfo = importInfos.stream()
                .filter(u -> Objects.equals(u.getLinkType(), 0)).findFirst().orElse(null);
        if (whiteImportInfo != null && CollectionUtils.isEmpty(filterInfos) && StringUtils.isNotBlank(whiteImportInfo.getLinkData())) {
            HashMap<String, Long> map = entities.stream()
                    .collect(HashMap::new, (k1, v1) -> k1.put(v1.getName(), v1.getId()), HashMap::putAll);
            List<Long> finalPacProductIds = pacProductIds;
            Arrays.stream(whiteImportInfo.getLinkData().split(",")).collect(Collectors.toList())
                    .forEach(x -> {
                if (MapUtils.isNotEmpty(map) && map.get(x) != null) {
                    finalPacProductIds.add(map.get(x));
                }
            });
        } else {
            pacProductIds = entities.stream().map(ProductPackageExportEntity::getId).collect(Collectors.toList());
        }

        redisService.set(RedisKeysEnum.PACKAGE_INFO.join(template.getPackageId()), StringUtils.join(pacProductIds, ","));

        log.info("=========异步结束：{}",  DateUtil.currentDate());
    }
}
