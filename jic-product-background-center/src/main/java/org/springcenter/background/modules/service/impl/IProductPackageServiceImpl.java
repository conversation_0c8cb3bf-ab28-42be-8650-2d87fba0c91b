package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.common.util.excel.IWriteDataExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsSetQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.background.config.exception.ProductException;
import org.springcenter.background.modules.entity.CommonProductPackageReq;
import org.springcenter.background.modules.entity.ProductPackageExportEntity;
import org.springcenter.background.modules.enums.*;
import org.springcenter.background.modules.mapper.product.*;
import org.springcenter.background.modules.model.product.*;
import org.springcenter.background.modules.service.IProductPackageService;
import org.springcenter.background.modules.util.DateUtil;
import org.springcenter.background.modules.util.EsUtil;
import org.springcenter.background.modules.util.RedisConstantUtil;
import org.springcenter.background.modules.util.RedisService;
import org.springcenter.product.api.dto.ProductPackageResp;
import org.springcenter.product.api.dto.background.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import strman.Strman;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @Date:2024/7/25 14:14
 */
@Service
@Slf4j
@RefreshScope
public class IProductPackageServiceImpl implements IProductPackageService {

    @Autowired
    private PackageFilterSettingMapper packageFilterSettingMapper;

    @Autowired
    private PackageRegularFilterInfoMapper packageRegularFilterInfoMapper;

    @Autowired
    private PackageFilterInfoMapper packageFilterInfoMapper;

    @Autowired
    private PackageImportInfoMapper packageImportInfoMapper;

    @Autowired
    private PackageTemplateMapper packageTemplateMapper;

    @Autowired
    private PackageTemplateLogMapper packageTemplateLogMapper;

    @Autowired
    private CommonDataRuleServiceImpl commonDataRuleService;

    private static final String PRODUCT_PACKAGE = "PP";

    @Value(value = "${product.package.leaf.tag}")
    private String productPackageLeafTag;

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate transactionTemplate;

    @Autowired
    private QiniuUtil qiniuUtil;

    @Autowired
    private EsUtil esUtil;

    @Value(value = "${product.package.es.index}")
    private String productPacEsIndex;

    @Autowired
    private PackageDepartmentInfoMapper packageDepartmentInfoMapper;

    @Autowired
    private PackageChangeFilterInfoMapper packageChangeFilterInfoMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ProductPackageCacheAsyncServiceImpl productPackageCacheAsyncService;

    @Override
    public ProductPackageResp getFilters() {
        // 查询所有字段和字段值
        List<PackageFilterSetting> packageSettings = packageFilterSettingMapper.selectAllValid();
        if (CollectionUtils.isEmpty(packageSettings)) {
            return null;
        }

        // 查询所有变量和常量值
        List<PackageRegularFilterInfo> regularFilterInfos = packageRegularFilterInfoMapper.selectRegularAndChangeAllValid();
        Map<String, List<PackageRegularFilterInfo>> filterInfosMap = regularFilterInfos.stream().collect(Collectors.groupingBy(PackageRegularFilterInfo::getField));

        List<ProductPackageResp.FilterData> filterDataList = new ArrayList<>();
        packageSettings.forEach(v -> {
            ProductPackageResp.FilterData data = new ProductPackageResp.FilterData();
            data.setData(v.getFilterParam());
            data.setField(v.getFilterField());
            data.setFieldName(v.getFilterName());
            data.setFilterBoxType(v.getFilterFieldType());
            data.setType(v.getFilterType());
            List<ProductPackageResp.SmallFilterData> valueDatas = new ArrayList<>();
            if (MapUtils.isNotEmpty(filterInfosMap) && CollectionUtils.isNotEmpty(filterInfosMap.get(v.getFilterField()))) {
                List<PackageRegularFilterInfo> infos = filterInfosMap.get(v.getFilterField());
                infos.forEach(x -> {
                    ProductPackageResp.SmallFilterData valueData = new ProductPackageResp.SmallFilterData();
                    valueData.setCode(x.getCode());
                    valueData.setCodeValue(x.getCodeValue());
                    valueDatas.add(valueData);
                });
                data.setFieldValues(valueDatas);
            }
            filterDataList.add(data);
        });

        if (CollectionUtils.isNotEmpty(filterDataList)) {
            ProductPackageResp productPackageResp = new ProductPackageResp();
            // 更多筛选
            productPackageResp.setMoreFilterList(filterDataList.stream()
                    .filter(v -> Objects.equals(v.getType(), 1)).collect(Collectors.toList()));
            // 常规筛选
            productPackageResp.setRoutineFilterList(filterDataList.stream()
                    .filter(v -> Objects.equals(v.getType(), 0)).collect(Collectors.toList()));
            return productPackageResp;
        }

        return null;
    }

    @Override
    public AddProductPackageResp addProductPackage(AddProductPackageReq requestData) {
        log.info("================开始：{}", DateUtil.currentDate());
        // 第一步校验参数
        PackageTemplate template = new PackageTemplate();
        CommonProductPackageReq commonProductPackageReq = new CommonProductPackageReq();
        BeanUtils.copyProperties(requestData, commonProductPackageReq);
        checkParams(commonProductPackageReq, template);
        // 判断当前时间是否大于等于当前时间
        Date startDate = DateUtil.formatToDate(requestData.getStartDate(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS);
        if (!startDate.after(DateUtil.parseDatetoDate(DateUtil.currentDate(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS))) {
            log.info("startDate:{}, 当前时间：{}", startDate, DateUtil.parseDatetoDate(DateUtil.currentDate(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS));
            throw new ProductException("开始时间不能小于当前时间");
        }

        // 第二步处理数据
        BeanUtils.copyProperties(requestData, template);
        Long pacId = packageTemplateMapper.getPacId();
        template.setPackageId(PRODUCT_PACKAGE + pacId);
        template.setInvalidType(requestData.getValidType());
        template.setCreateTime(new Date());
        template.setUpdateTime(new Date());
        template.setId(IdLeaf.getId(productPackageLeafTag));

        List<PackageFilterInfo> filterInfos = new ArrayList<>();
        if (Objects.equals(requestData.getHasFilter(), 1)) {
            requestData.getFilterInfoDataList().forEach(v -> {
                PackageFilterInfo filterInfo = new PackageFilterInfo();
                BeanUtils.copyProperties(v, filterInfo);
                filterInfo.setId(IdLeaf.getId(productPackageLeafTag));
                filterInfo.setData(JSONObject.toJSONString(v.getCodes()));
                filterInfo.setCreateTime(new Date());
                filterInfo.setUpdateTime(new Date());
                filterInfo.setPacId(template.getId());
                filterInfos.add(filterInfo);
            });
        }

        List<PackageImportInfo> importInfos = new ArrayList<>();
        if (Objects.equals(requestData.getHasImport(), 1)) {
            requestData.getImportInfoDataList().forEach(v -> {
                PackageImportInfo importInfo = new PackageImportInfo();
                BeanUtils.copyProperties(v, importInfo);
                importInfo.setCreateTime(new Date());
                importInfo.setUpdateTime(new Date());
                importInfo.setPacId(template.getId());
                importInfo.setId(IdLeaf.getId(productPackageLeafTag));
                importInfo.setLinkData(v.getLinkContent().replace("，", ","));
                importInfos.add(importInfo);
            });
        }

        // 部门数据
        List<PackageDepartmentInfo> infos = new ArrayList<>();
        Arrays.stream(requestData.getDepartment().split(",")).forEach(v -> {
            PackageDepartmentInfo info = new PackageDepartmentInfo();
            info.setId(IdLeaf.getId(productPackageLeafTag));
            info.setCreateTime(new Date());
            info.setUpdateTime(new Date());
            info.setPacId(template.getId());
            info.setDepartmentId(v);
            infos.add(info);
        });

        // 备份数据
        PackageTemplateLog templateLog = new PackageTemplateLog();
        BeanUtils.copyProperties(template, templateLog);
        templateLog.setId(IdLeaf.getId(productPackageLeafTag));
        templateLog.setFilterJson(JSONObject.toJSONString(filterInfos));
        templateLog.setImportJson(JSONObject.toJSONString(importInfos));
        templateLog.setOperateType(PackageOperateEnum.ADD.getCode());
        templateLog.setPacMainId(template.getId());

        // 第三步保存数据
        transactionTemplate.execute(transactionStatus -> {
            packageTemplateMapper.insert(template);
            if (CollectionUtils.isNotEmpty(filterInfos)) {
                packageFilterInfoMapper.batchInsert(filterInfos);
            }
            if (CollectionUtils.isNotEmpty(importInfos)) {
                packageImportInfoMapper.batchInsert(importInfos);
            }
            if (CollectionUtils.isNotEmpty(infos)) {
                packageDepartmentInfoMapper.batchInsert(infos);
            }
            packageTemplateLogMapper.insert(templateLog);
            return transactionStatus;
        });

        AddProductPackageResp resp = new AddProductPackageResp();
        resp.setId(template.getId());
        resp.setPacName(template.getPackageName());
        resp.setPacId(template.getPackageId());
        log.info("================结束1：{}", DateUtil.currentDate());
        productPackageCacheAsyncService.cashPackageInfoByPacId(template, filterInfos, importInfos);
        log.info("================结束2：{}", DateUtil.currentDate());
        return resp;
    }

    @Override
    public ProductPackageDetailResp searchProductPackageDetail(ProductPackageDetailReq requestData) {
        PackageTemplate template = packageTemplateMapper.selectById(requestData.getId());
        if (template == null) {
            return null;
        }

        List<PackageFilterInfo> packageFilterInfos = new ArrayList<>();
        if (Objects.equals(template.getHasFilter(), 1)) {
            packageFilterInfos = packageFilterInfoMapper.selectByPacId(template.getId());
        }

        List<PackageImportInfo> packageImportInfos = new ArrayList<>();
        if (Objects.equals(template.getHasImport(), 1)) {
            packageImportInfos = packageImportInfoMapper.selectByPacId(template.getId());
        }

        List<PackageDepartmentInfo> infos = packageDepartmentInfoMapper.selectByPacId(template.getId());

        ProductPackageDetailResp resp = new ProductPackageDetailResp();
        BeanUtils.copyProperties(template, resp);
        resp.setValidType(template.getInvalidType());
        resp.setEndDate(template.getEndDate() == null ? "" : DateUtil.parseDate(template.getEndDate(), DateUtil.DATE_FORMAT_YMDHM));
        resp.setStartDate(DateUtil.parseDate(template.getStartDate(), DateUtil.DATE_FORMAT_YMDHM));
        if (CollectionUtils.isNotEmpty(packageFilterInfos)) {
            List<AddProductPackageReq.FilterInfoData> filterInfoDataList = new ArrayList<>();
            packageFilterInfos.forEach(v -> {
                AddProductPackageReq.FilterInfoData data = new AddProductPackageReq.FilterInfoData();
                BeanUtils.copyProperties(v, data);
                List<String> list = JSONObject.parseArray(v.getData(), String.class);
                data.setCodes(list);
                filterInfoDataList.add(data);
            });
            resp.setFilterInfoDataList(filterInfoDataList);
        }

        if (CollectionUtils.isNotEmpty(packageImportInfos)) {
            List<AddProductPackageReq.ImportInfoData> importInfoDataList = new ArrayList<>();
            packageImportInfos.forEach(v -> {
                AddProductPackageReq.ImportInfoData data = new AddProductPackageReq.ImportInfoData();
                data.setLinkType(v.getLinkType());
                data.setLinkContent(v.getLinkData());
                importInfoDataList.add(data);
            });
            resp.setImportInfoDataList(importInfoDataList);
        }
        resp.setDepartment(infos.stream().map(PackageDepartmentInfo::getDepartmentId).collect(Collectors.joining(",")));

        return resp;
    }

    @Override
    public Boolean updateProductPackageDetail(UpdateProductPackageReq requestData) {
        // 查询当前商品包
        PackageTemplate packageTemplate = packageTemplateMapper.selectById(requestData.getId());
        if (packageTemplate == null) {
            throw new ProductException("商品包不存在");
        }

        // copy参数
        BeanUtils.copyProperties(requestData, packageTemplate);
        packageTemplate.setUpdateTime(new Date());
        packageTemplate.setInvalidType(requestData.getValidType());

        CommonProductPackageReq commonProductPackageReq = new CommonProductPackageReq();
        BeanUtils.copyProperties(requestData, commonProductPackageReq);

        // 检验参数
        checkParams(commonProductPackageReq, packageTemplate);

        // 处理数据
        List<PackageFilterInfo> filterInfos = new ArrayList<>();
        if (Objects.equals(requestData.getHasFilter(), 1)) {
            requestData.getFilterInfoDataList().forEach(v -> {
                PackageFilterInfo filterInfo = new PackageFilterInfo();
                filterInfo.setPacId(packageTemplate.getId());
                BeanUtils.copyProperties(v, filterInfo);
                filterInfo.setData(JSONObject.toJSONString(v.getCodes()));
                filterInfo.setCreateTime(new Date());
                filterInfo.setUpdateTime(new Date());
                filterInfos.add(filterInfo);
            });
        }

        List<PackageImportInfo> importInfos = new ArrayList<>();
        if (Objects.equals(requestData.getHasImport(), 1)) {
            requestData.getImportInfoDataList().forEach(v -> {
                PackageImportInfo importInfo = new PackageImportInfo();
                importInfo.setPacId(packageTemplate.getId());
                BeanUtils.copyProperties(v, importInfo);
                importInfo.setCreateTime(new Date());
                importInfo.setUpdateTime(new Date());
                importInfo.setId(IdLeaf.getId(productPackageLeafTag));
                importInfo.setLinkData(v.getLinkContent().replace("，", ","));
                importInfos.add(importInfo);
            });
        }

        // 部门数据
        List<PackageDepartmentInfo> infos = new ArrayList<>();
        Arrays.stream(requestData.getDepartment().split(",")).forEach(v -> {
            PackageDepartmentInfo info = new PackageDepartmentInfo();
            info.setId(IdLeaf.getId(productPackageLeafTag));
            info.setCreateTime(new Date());
            info.setUpdateTime(new Date());
            info.setPacId(packageTemplate.getId());
            info.setDepartmentId(v);
            infos.add(info);
        });

        // bakedata
        PackageTemplateLog templateLog = new PackageTemplateLog();
        BeanUtils.copyProperties(packageTemplate, templateLog);
        templateLog.setId(IdLeaf.getId(productPackageLeafTag));
        templateLog.setFilterJson(JSONObject.toJSONString(filterInfos));
        templateLog.setImportJson(JSONObject.toJSONString(importInfos));
        templateLog.setOperateType(PackageOperateEnum.UPDATE.getCode());
        templateLog.setCreateTime(new Date());
        templateLog.setUpdateTime(new Date());
        templateLog.setPacMainId(packageTemplate.getId());

        transactionTemplate.execute(transactionStatus -> {
            packageTemplateMapper.updateById(packageTemplate);
            if (packageTemplate.getEndDate() == null) {
                packageTemplateMapper.updateEndDateTime(packageTemplate.getId());
            }
            // 更新过滤条件和导入的数据
            packageFilterInfoMapper.updateByPacId(packageTemplate.getId());
            packageImportInfoMapper.updateByPacId(packageTemplate.getId());
            packageDepartmentInfoMapper.updateByPacId(packageTemplate.getId());
            if (CollectionUtils.isNotEmpty(filterInfos)) {
                packageFilterInfoMapper.batchInsert(filterInfos);
            }
            if (CollectionUtils.isNotEmpty(importInfos)) {
                packageImportInfoMapper.batchInsert(importInfos);
            }
            if (CollectionUtils.isNotEmpty(infos)) {
                packageDepartmentInfoMapper.batchInsert(infos);
            }
            packageTemplateLogMapper.insert(templateLog);
            return transactionStatus;
        });
        productPackageCacheAsyncService.cashPackageInfoByPacId(packageTemplate, filterInfos, importInfos);
        return true;
    }

    @Override
    public List<ProdPackageListResp> getProductPackageList(ProdPackageListReq requestData, Page page, String component) {
        if (StringUtils.isBlank(component)) {
            throw new ProductException("组件component不能为空");
        }
        // 第一步判断当前用户权限
        List<String> allowRule = commonDataRuleService.getAllowRuleData(component, DataRuleEnum.DEPARTMENT_RULE.getRuleType());
        List<String> selfAllow = Arrays.stream(requestData.getDepartment().split(",")).collect(Collectors.toList());
        // todo 上线后 空数组则是没有权限查询
        /*  selfAllow.addAll(allowRule);
        }*/

        if (CollectionUtils.isEmpty(selfAllow)) {
            return Collections.emptyList();
        }

        // 判断当前用户权限
        List<String> allowPacIds = packageDepartmentInfoMapper.selectByDepartmentIds(selfAllow);
        if (CollectionUtils.isEmpty(allowPacIds)) {
            return Collections.emptyList();
        }

        com.github.pagehelper.Page<PackageTemplate> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<PackageTemplate> packageTemplates = packageTemplateMapper.selectByParams(requestData.getPackageName(),
                requestData.getPacId(), requestData.getIsOpen(), allowPacIds);

        // 根据对应参数返回列表 状态需自己判断
        List<ProdPackageListResp> rets = buildRets(packageTemplates);

        PageInfo<ProdPackageListResp> pageInfo = new PageInfo(hPage);
        pageInfo.setList(rets);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    private List<ProdPackageListResp> buildRets(List<PackageTemplate> packageTemplates) {
        if (CollectionUtils.isEmpty(packageTemplates)) {
            return Collections.emptyList();
        }

        List<ProdPackageListResp> rets = new ArrayList<>();
        packageTemplates.forEach(v -> {
            ProdPackageListResp resp = new ProdPackageListResp();
            BeanUtils.copyProperties(v, resp);
            resp.setPacId(v.getPackageId());
            resp.setPacName(v.getPackageName());
            resp.setUpdateTime(DateUtil.parseDate(v.getUpdateTime(), DateUtil.DATE_FORMAT_YMDHM));
            resp.setEndDate(v.getEndDate() == null ? "" : DateUtil.parseDate(v.getEndDate(), DateUtil.DATE_FORMAT_YMDHM));
            resp.setStartDate(DateUtil.parseDate(v.getStartDate(), DateUtil.DATE_FORMAT_YMDHM));
            //  0未生效 1启用 2停用 3已过期
            Date nowDate = new Date();
            if (Objects.equals(PackageOperateStatusEnum.CLOSE.getCode(), v.getIsOpen())) {
                // 如果已停用 则失效
                resp.setStatus(PackageStatusEnum.STOP.getStatus());
            } else if ((!nowDate.after(v.getStartDate()))) {
                // 如果当前时间在开始时间之前 则未生效
                resp.setStatus(PackageStatusEnum.NOT_VALID.getStatus());
            } else if (nowDate.after(v.getStartDate())
                    && Objects.equals(PackageOperateStatusEnum.OPEN.getCode(), v.getIsOpen())) {
                // 如果已到开始时间 且 长期有效 则有效中
                if (Objects.equals(v.getInvalidType(), 0)) {
                    resp.setStatus(PackageStatusEnum.OPEN.getStatus());
                } else {
                // 如果已到开始时间 且 已过结束时间 则已过期
                    if (nowDate.after(v.getEndDate())) {
                        resp.setStatus(PackageStatusEnum.EXPIRED.getStatus());
                    } else {
                // 如果已到开始时间 且 没过结束时间 则有效中
                        resp.setStatus(PackageStatusEnum.OPEN.getStatus());
                    }
                }
            }
            rets.add(resp);
        });

        return rets;
    }

    @Override
    public Boolean switchProductPackageStatus(SwitchPackageStatusReq requestData) {
        // 商品包是否存在
        PackageTemplate packageTemplate = packageTemplateMapper.selectByValidId(requestData.getId());
        if (packageTemplate == null) {
            throw new ProductException("当前id的商品包不存在");
        }

        packageTemplate.setUpdator(requestData.getOperator());
        PackageTemplateLog log = new PackageTemplateLog();
        BeanUtils.copyProperties(packageTemplate, log);
        log.setId(IdLeaf.getId(productPackageLeafTag));
        log.setPacMainId(packageTemplate.getId());

        // 判断当前商品包的状态是否与更改的冲突 || 判断是否可改该状态
        if (Objects.equals(PackageOperateStatusEnum.OPEN.getCode(), packageTemplate.getIsOpen())) {
            if (Objects.equals(packageTemplate.getIsOpen(), PackageOperateStatusEnum.CLOSE.getCode())) {
                throw new ProductException("当前商品包已停用，不能重复停用");
            }

            // 停用
            packageTemplate.setIsOpen(PackageOperateStatusEnum.CLOSE.getCode());
            packageTemplate.setUpdateTime(new Date());

            log.setOperateType(PackageOperateEnum.STOP.getCode());
        } else {
            // 启用
            if (Objects.equals(packageTemplate.getIsOpen(), PackageOperateStatusEnum.OPEN.getCode())) {
                throw new ProductException("当前商品包已启用，不能重复启用");
            }
            packageTemplate.setIsOpen(PackageOperateStatusEnum.OPEN.getCode());
            packageTemplate.setUpdateTime(new Date());

            log.setOperateType(PackageOperateEnum.ENABLE.getCode());
        }


        log.setCreateTime(new Date());
        log.setUpdateTime(new Date());

        List<PackageFilterInfo> packageFilterInfos = packageFilterInfoMapper.selectByPacId(packageTemplate.getId());
        List<PackageImportInfo> packageImportInfos = packageImportInfoMapper.selectByPacId(packageTemplate.getId());
        log.setFilterJson(JSONObject.toJSONString(packageFilterInfos));
        log.setImportJson(JSONObject.toJSONString(packageImportInfos));

        // 记录更新状态操作日志
        transactionTemplate.execute(transactionStatus -> {
            packageTemplateMapper.updateById(packageTemplate);
            packageTemplateLogMapper.insert(log);
            return transactionStatus;
        });
        return true;
    }

    @Override
    public String downloadProductPackageStatus(String requestData) {
        if (StringUtils.isBlank(requestData)) {
            throw new ProductException("请求参数不能为空");
        }
        PackageTemplate template = packageTemplateMapper.selectByValidId(requestData);
        if (template == null) {
            throw new RuntimeException("请求参数为空");
        }
        List<PackageFilterInfo> filterInfos = packageFilterInfoMapper.selectByPacId(template.getId());
        List<PackageImportInfo> importInfos = packageImportInfoMapper.selectByPacId(template.getId());

        // 具体查询
        List<ProductPackageExportEntity> rets = dealEsInfo(template, filterInfos, importInfos);


        String fileName = "商品包:" + template.getPackageName() + System.currentTimeMillis() +  ".xlsx";
        EasyExcelUtil.write(fileName, ProductPackageExportEntity.class, new IWriteDataExcel<ProductPackageExportEntity>() {
            public List<ProductPackageExportEntity> getData() {
                return rets;
            }
        });
        File file = new File(fileName);
        String exportUrl = qiniuUtil.upload(file.getPath(), fileName);
        file.delete();
        return exportUrl;
    }

    public List<ProductPackageExportEntity> dealEsInfo(PackageTemplate template, List<PackageFilterInfo> filterInfos,
                                          List<PackageImportInfo> importInfos) {
        // 1、处理导入数据
        List<ProductPackageExportEntity> filterRets = new ArrayList<>();
        List<String> includeNames = new ArrayList<>();
        List<String> excludeNames = new ArrayList<>();
        if (Objects.equals(template.getHasImport(), 1) && CollectionUtils.isNotEmpty(importInfos)) {
            List<String> oldWhite = new ArrayList<>();
            importInfos.forEach(v -> {
                if (Objects.equals(v.getLinkType(), 0)) {
                    List<String> includes = Arrays.stream(v.getLinkData().split(",")).collect(Collectors.toList());
                    oldWhite.addAll(includes);
                } else {
                    List<String> excludes = Arrays.stream(v.getLinkData().split(",")).collect(Collectors.toList());
                    excludeNames.addAll(excludes);
                }
            });

            // 如果黑白名单同时存在，且黑名单存在白名单的数据 该数据则为黑名单数据
            if (oldWhite.size() > 0 && excludeNames.size() > 0) {
                oldWhite.forEach(v -> {
                    if (!excludeNames.contains(v)) {
                        includeNames.add(v);
                    }
                });
            } else {
                includeNames.addAll(oldWhite);
            }
        }

        filterRets = searchInEsByInfo(includeNames, excludeNames, filterInfos);


        // 两个处理集合合并
        return filterRets.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<ProdPackageListResp> queryPacListByPacId(List<String> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            throw new ProductException("商品包id不能为空");
        }

        List<PackageTemplate> packageTemplates = packageTemplateMapper.selectByPacIds(requestData);

        // 根据对应参数返回列表 状态需自己判断
        List<ProdPackageListResp> rets = buildRets(packageTemplates);
        return rets;
    }

    private List<ProductPackageExportEntity> searchInEsByInfo(List<String> includeNames, List<String> excludeNames,
                                                              List<PackageFilterInfo> filterInfos) {
        List<ProductPackageExportEntity> list = new ArrayList<>();

        // 处理es数据
        SearchRequest request = new SearchRequest();
        request.indices(productPacEsIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        BoolQueryBuilder smallQueryBuilder1 = QueryBuilders.boolQuery();
        BoolQueryBuilder smallQueryBuilder2 = QueryBuilders.boolQuery();

        Boolean isAddFirstShould = false;
        Boolean isAddSecShould = false;
        //query create
        if (CollectionUtils.isNotEmpty(includeNames)) {
            isAddFirstShould = true;
            smallQueryBuilder1.must(QueryBuilders.termsQuery("name.keyword", includeNames));
        }



        BoolQueryBuilder smallQueryBuilder3 = QueryBuilders.boolQuery();
        BoolQueryBuilder smallQueryBuilder4 = QueryBuilders.boolQuery();
        BoolQueryBuilder smallQueryBuilder5 = QueryBuilders.boolQuery();
        if (CollectionUtils.isNotEmpty(filterInfos)) {
            isAddSecShould = true;

            if (CollectionUtils.isNotEmpty(excludeNames)) {
                smallQueryBuilder5.mustNot(QueryBuilders.termsQuery("name.keyword", excludeNames));
            }

            filterInfos.forEach(v -> {
                if (Objects.equals(v.getCondition(), 0)) {
                    // 正选
                    PacFilterEsTypeEnum filterTypeEnum = PacFilterEsTypeEnum.getFilterType(v.getField());
                    if (filterTypeEnum == null) {
                        return;
                    }
                    if (filterTypeEnum.getFilterType() == 1) {
                        smallQueryBuilder3.must(QueryBuilders.termsQuery(filterTypeEnum.getFilterField(), JSONObject.parseArray(v.getData())));
                    } else if (filterTypeEnum.getFilterType() == 2) {
                        smallQueryBuilder3.must(QueryBuilders.matchQuery(filterTypeEnum.getFilterField(), v.getData()));
                    } else if (filterTypeEnum.getFilterType() == 3) {
                        TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder(filterTypeEnum.getFilterField(),
                                Arrays.stream(JSONObject.parseArray(v.getData()).stream().toArray())
                                        .map(x -> Objects.toString(x).toLowerCase())
                                        .map(item -> Strman.replace(item, "-", "_", true))
                                .collect(Collectors.toList()));
                        termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(1)));
                        smallQueryBuilder3.must(termsSetQueryBuilder);
                    }

                } else {
                    // 反选
                    PacFilterEsTypeEnum filterTypeEnum = PacFilterEsTypeEnum.getFilterType(v.getField());
                    if (filterTypeEnum == null) {
                        return;
                    }
                    if (filterTypeEnum.getFilterType() == 1) {
                        smallQueryBuilder4.mustNot(QueryBuilders.termsQuery(filterTypeEnum.getFilterField(), JSONObject.parseArray(v.getData())));
                    } else if (filterTypeEnum.getFilterType() == 2) {
                        smallQueryBuilder4.mustNot(QueryBuilders.matchQuery(filterTypeEnum.getFilterField(), v.getData()));
                    } else if (filterTypeEnum.getFilterType() == 3) {
                        TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder(filterTypeEnum.getFilterField(),
                                Arrays.stream(JSONObject.parseArray(v.getData()).stream().toArray())
                                        .map(x -> Objects.toString(x).toLowerCase())
                                        .map(item -> Strman.replace(item, "-", "_", true))
                                        .collect(Collectors.toList()));
                        termsSetQueryBuilder.setMinimumShouldMatchScript(new Script(String.valueOf(1)));
                        smallQueryBuilder4.mustNot(termsSetQueryBuilder);
                    }
                }
            });
        }


        smallQueryBuilder2.must(smallQueryBuilder3);
        smallQueryBuilder2.must(smallQueryBuilder4);
        smallQueryBuilder2.must(smallQueryBuilder5);
        if (isAddFirstShould) {
            queryBuilder.should(smallQueryBuilder1);
        }
        if (isAddSecShould) {
            queryBuilder.should(smallQueryBuilder2);
        }

        sourceBuilder.fetchSource(new String[]{"name", "id"}, new String[]{});
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(10000);
        sourceBuilder.sort("id", SortOrder.DESC);
        request.source(sourceBuilder);
        log.info("查询商品包es入参：{}", request.source().toString());
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }

            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                list.add(ProductPackageExportEntity.fromJson(hits[i].getSourceAsString(), ProductPackageExportEntity.class));
            }
        } catch (IOException e) {
            log.error("查询商品包数据异常{}", e);
            return new ArrayList<>();
        }


        return list;

    }

    @Override
    public List<ProductPackageOperatorsListResp> getProductPackageOperators(String requestData, Page page) {
        if (StringUtils.isBlank(requestData)) {
            throw new ProductException("请求参数不能为空");
        }

        com.github.pagehelper.Page<PackageTemplate> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<PackageTemplateLog> logs = packageTemplateLogMapper.selectByPacMainId(requestData);

        List<ProductPackageOperatorsListResp> rets = buildOperateRet(logs);
        PageInfo<ProductPackageOperatorsListResp> pageInfo = new PageInfo(hPage);
        pageInfo.setList(rets);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    private List<ProductPackageOperatorsListResp> buildOperateRet(List<PackageTemplateLog> logs) {
        if (CollectionUtils.isEmpty(logs)) {
            return Collections.emptyList();
        }

        List<ProductPackageOperatorsListResp> reps = new ArrayList<>();
        logs.forEach(v -> {
            ProductPackageOperatorsListResp resp = new ProductPackageOperatorsListResp();
            BeanUtils.copyProperties(v, resp);
            resp.setOperator(v.getUpdator());
            resp.setOperatorTime(DateUtil.parseDate(v.getCreateTime(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS));
            reps.add(resp);
        });

        return reps;
    }

    @Override
    public List<ProductPackageExportEntity> getProductPackageForc(String requestData) {
        // 获取启用且在有效期的商品包
        PackageTemplate template = packageTemplateMapper.selectByPackIdValid(requestData);
        if (template == null) {
            log.error("===============未找到当前门店包id的数据,门店包失效或停用：{}", requestData);
            return Collections.emptyList();
        }

        Date date = new Date();
        // 判断当前时间是否在有限期
        if (template.getInvalidType() == 0 && date.before(template.getStartDate())) {
            log.error("===================当前商品包未生效：{}", JSONObject.toJSONString(template));
            return Collections.emptyList();
        }

        if (template.getInvalidType() == 1 &&
                !(date.after(template.getStartDate()) && date.before(template.getEndDate()))) {
            log.error("===================当前商品包未在有效期内：{}，开始时间：{},结束时间：{}", JSONObject.toJSONString(template),
                    DateUtil.formatToStr(template.getStartDate(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS),
                    DateUtil.formatToStr(template.getEndDate(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS));
            return Collections.emptyList();
        }

        // 先查询缓存
        Object o = redisService.get(RedisKeysEnum.PACKAGE_INFO.join(template.getPackageId()));
        if (o == null) {
            // 具体查询
            List<PackageFilterInfo> filterInfos = packageFilterInfoMapper.selectByPacId(template.getId());
            List<PackageImportInfo> importInfos = packageImportInfoMapper.selectByPacId(template.getId());
            List<ProductPackageExportEntity> rets = dealEsInfo(template, filterInfos, importInfos);

            // 如果只有导入数据按照导入数据顺序返回
            PackageImportInfo whiteImportInfo = importInfos.stream()
                    .filter(u -> Objects.equals(u.getLinkType(), 0)).findFirst().orElse(null);
            if (CollectionUtils.isEmpty(filterInfos) && whiteImportInfo != null && StringUtils.isNotBlank(whiteImportInfo.getLinkData())) {
                HashMap<String, ProductPackageExportEntity> map = rets.stream()
                        .collect(HashMap::new, (k, v) -> k.put(v.getName(), v), HashMap::putAll);
                List<ProductPackageExportEntity> rets2 = new ArrayList<>();

                Arrays.stream(whiteImportInfo.getLinkData().split(",")).collect(Collectors.toList())
                        .forEach(v -> {
                    if (MapUtils.isNotEmpty(map) && map.get(v) != null) {
                        rets2.add(map.get(v));
                    }
                });
                return rets2;
            } else {
                return rets;
            }
        }

        if (StringUtils.isBlank(o.toString())) {
            return Collections.emptyList();
        }

        List<Long> pacProductIds = Arrays.stream(o.toString().split(",")).map(po -> Long.valueOf(po))
                .collect(Collectors.toList());

        List<ProductPackageExportEntity> entities = new ArrayList<>();
        pacProductIds.forEach(v -> {
            ProductPackageExportEntity entity = new ProductPackageExportEntity();
            entity.setId(v);
            entities.add(entity);
        });
        return entities;
    }

    @Override
    public List<ProductPackageResp.SmallFilterData> getParamByField(SearchPacParamReq requestData) {
        List<PackageChangeFilterInfo> filterInfos = packageChangeFilterInfoMapper
                .selectByFieldAndParam(requestData.getField(), requestData.getParam());
        if (CollectionUtils.isEmpty(filterInfos)) {
            return Collections.emptyList();
        }
        List<ProductPackageResp.SmallFilterData> rets = new ArrayList<>();
        filterInfos.forEach(v -> {
            ProductPackageResp.SmallFilterData resp = new ProductPackageResp.SmallFilterData();
            resp.setCode(v.getCode());
            resp.setCodeValue(v.getCodeValue());
            rets.add(resp);
        });
        return rets;
    }

    @Override
    public List<SearchProductIdInPacResp> getPackageProductIdsForc(SearchProductIdInPacReq requestData) {
        List<SearchProductIdInPacResp> rets = new ArrayList<>();
        List<PackageTemplate> templates = packageTemplateMapper.selectByPackIds(requestData.getPacIds());
        if (CollectionUtils.isEmpty(templates)) {
            emptySearchInfoRet(requestData, rets);
            return rets;
        }

        // 判断商品包的时间是否在有效期
        Date date = new Date();
        templates.forEach(v -> {
            if (v.getIsDeleted() == 1 || v.getIsOpen() == 0) {
                log.error("===================当前商品包停用或被删除：{}", JSONObject.toJSONString(v));
                SearchProductIdInPacResp resp = new SearchProductIdInPacResp();
                resp.setPacId(v.getPackageId());
                resp.setIsValidated(0);
                rets.add(resp);
                return;
            }

            // 判断当前时间是否在有限期
            if (v.getInvalidType() == 0 && date.before(v.getStartDate())) {
                log.error("===================当前商品包未生效：{}", JSONObject.toJSONString(v));
                SearchProductIdInPacResp resp = new SearchProductIdInPacResp();
                resp.setPacId(v.getPackageId());
                resp.setIsValidated(0);
                rets.add(resp);
                return;
            }

            if (v.getInvalidType() == 1 &&
                    !(date.after(v.getStartDate()) && date.before(v.getEndDate()))) {
                log.error("===================当前商品包未在有效期内：{}，开始时间：{},结束时间：{}", JSONObject.toJSONString(v),
                        DateUtil.formatToStr(v.getStartDate(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS),
                        DateUtil.formatToStr(v.getEndDate(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS));
                SearchProductIdInPacResp resp = new SearchProductIdInPacResp();
                resp.setPacId(v.getPackageId());
                resp.setIsValidated(0);
                rets.add(resp);
                return;
            }

            SearchProductIdInPacResp resp = new SearchProductIdInPacResp();
            resp.setPacId(v.getPackageId());
            resp.setIsValidated(1);
            rets.add(resp);
        });

        if (CollectionUtils.isEmpty(rets)) {
            emptySearchInfoRet(requestData, rets);
            return rets;
        }

        HashMap<String, PackageTemplate> pacTemplateMap = templates.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getPackageId(), v), HashMap::putAll);
        rets.forEach(v -> {
            List<Long> pacProductIds = new ArrayList<>();
            List<Long> conformProductIds = new ArrayList<>();
            log.info("在redis中获取数据");
            Object o = redisService.get(RedisKeysEnum.PACKAGE_INFO.join(v.getPacId()));
            log.info("在redis中获取数据 end：{}", Objects.toString(o));
            if (o == null) {
                if (MapUtils.isEmpty(pacTemplateMap) || pacTemplateMap.get(v.getPacId()) == null) {
                    return;
                }
                PackageTemplate template = pacTemplateMap.get(v.getPacId());
                // 具体查询
                List<PackageFilterInfo> filterInfos = packageFilterInfoMapper.selectByPacId(template.getId());
                List<PackageImportInfo> importInfos = packageImportInfoMapper.selectByPacId(template.getId());
                List<ProductPackageExportEntity> productInfos = dealEsInfo(template, filterInfos, importInfos);
                pacProductIds = productInfos.stream().map(ProductPackageExportEntity::getId).collect(Collectors.toList());
            } else {
                if (StringUtils.isBlank(o.toString())) {
                    pacProductIds = new ArrayList<>();
                } else {
                    pacProductIds = Arrays.stream(o.toString().split(",")).map(po -> Long.valueOf(po))
                            .collect(Collectors.toList());
                }
            }

            List<Long> finalPacProductIds = pacProductIds;
            requestData.getProductIds().forEach(v1 -> {
                if (finalPacProductIds.contains(v1)) {
                    conformProductIds.add(v1);
                }
            });
            v.setProductIds(conformProductIds);
        });

        if (CollectionUtils.isEmpty(rets)) {
            emptySearchInfoRet(requestData, rets);
            return rets;
        }
        List<SearchProductIdInPacResp> dealRets = new ArrayList<>();
        HashMap<String, SearchProductIdInPacResp> retPacMap = rets.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getPacId(), v), HashMap::putAll);
        requestData.getPacIds().forEach(v -> {
            if (MapUtils.isEmpty(retPacMap) || retPacMap.get(v) == null) {
                SearchProductIdInPacResp resp = new SearchProductIdInPacResp();
                resp.setIsValidated(0);
                resp.setPacId(v);
                resp.setProductIds(Collections.emptyList());
                dealRets.add(resp);
            } else {
                dealRets.add(retPacMap.get(v));
            }
        });

        return dealRets;
    }

    private static void emptySearchInfoRet(SearchProductIdInPacReq requestData, List<SearchProductIdInPacResp> rets) {
        requestData.getPacIds().forEach(v -> {
            SearchProductIdInPacResp resp = new SearchProductIdInPacResp();
            resp.setPacId(v);
            resp.setIsValidated(0);
            resp.setProductIds(Collections.emptyList());
            rets.add(resp);
        });
    }

    @Override
    public List<SearchProductIdIsExistResp> getPackageIsExistProductIdsForc(SearchProductIdInPacReq requestData) {
        List<SearchProductIdIsExistResp> rets = new ArrayList<>();
        List<PackageTemplate> templates = packageTemplateMapper.selectByPackIds(requestData.getPacIds());
        if (CollectionUtils.isEmpty(templates)) {
            getNoProductInfos(requestData, rets);
            return rets;
        }

        // 获取有效的商品包
        List<String> validatedPacIds = new ArrayList<>();
        Date date = new Date();
        templates.forEach(v -> {
            if (v.getIsDeleted() == 1 || v.getIsOpen() == 0) {
                log.error("===================当前商品包停用或被删除：{}", JSONObject.toJSONString(v));
                return;
            }

            // 判断当前时间是否在有限期
            if (v.getInvalidType() == 0 && date.before(v.getStartDate())) {
                log.error("===================当前商品包未生效：{}", JSONObject.toJSONString(v));
                return;
            }

            if (v.getInvalidType() == 1 &&
                    !(date.after(v.getStartDate()) && date.before(v.getEndDate()))) {
                log.error("===================当前商品包未在有效期内：{}，开始时间：{},结束时间：{}", JSONObject.toJSONString(v),
                        DateUtil.formatToStr(v.getStartDate(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS),
                        DateUtil.formatToStr(v.getEndDate(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS));
                return;
            }
            validatedPacIds.add(v.getPackageId());
        });

        if (CollectionUtils.isEmpty(validatedPacIds)) {
            getNoProductInfos(requestData, rets);
            return rets;
        }

        HashMap<String, PackageTemplate> pacTemplateMap = templates.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getPackageId(), v), HashMap::putAll);
        requestData.getPacIds().forEach(v -> {
            List<Long> pacProductIds = new ArrayList<>();
            Object o = redisService.get(RedisKeysEnum.PACKAGE_INFO.join(v));
            if (o == null) {
                if (MapUtils.isEmpty(pacTemplateMap) || pacTemplateMap.get(v) == null) {
                    return;
                }
                // 具体查询
                PackageTemplate template = pacTemplateMap.get(v);
                List<PackageFilterInfo> filterInfos = packageFilterInfoMapper.selectByPacId(template.getId());
                List<PackageImportInfo> importInfos = packageImportInfoMapper.selectByPacId(template.getId());
                List<ProductPackageExportEntity> productInfos = dealEsInfo(template, filterInfos, importInfos);
                pacProductIds = productInfos.stream().map(ProductPackageExportEntity::getId).collect(Collectors.toList());
            } else {
                if (StringUtils.isBlank(o.toString())) {
                    pacProductIds = new ArrayList<>();
                } else {
                    pacProductIds = Arrays.stream(o.toString().split(",")).map(po -> Long.valueOf(po))
                            .collect(Collectors.toList());
                }
            }

            List<Long> finalPacProductIds = pacProductIds;
            requestData.getProductIds().forEach(x -> {
                if (finalPacProductIds.contains(x)) {
                    SearchProductIdIsExistResp resp = new SearchProductIdIsExistResp();
                    resp.setProductId(x);
                    resp.setIsExist(1);
                    rets.add(resp);
                }
            });
        });

        HashMap<Long, SearchProductIdIsExistResp> respExistMap = rets.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getProductId(), v), HashMap::putAll);
        if (MapUtils.isEmpty(respExistMap)) {
            getNoProductInfos(requestData, rets);
            return rets;
        }
        requestData.getProductIds().forEach(v -> {
            if (respExistMap.get(v) == null) {
                SearchProductIdIsExistResp resp = new SearchProductIdIsExistResp();
                resp.setProductId(v);
                resp.setIsExist(0);
                rets.add(resp);
            }
        });

        return rets.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public void cashPackageInfoJob(String pacId) {
        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        // 查询开始时间小于当前时间 且状态是未开始状态 获取总数
        packageTemplateMapper.getAllPacTemplate();
        PageInfo<PackageTemplate> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();
        if (total == 0) {
            log.info("暂无商品包数据");
            return;
        }

        long pageTotal = 0;
        int pageSize = 200;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }

        for(int i = 1 ; i <= pageTotal; i++) {
            com.github.pagehelper.Page<Object> page = PageHelper.startPage(i, 200);
            // 查询所有商品包
            List<PackageTemplate> allPacTemplate = packageTemplateMapper.getAllPacTemplate();
            if (CollectionUtils.isNotEmpty(allPacTemplate)) {
                allPacTemplate.forEach(v -> {
                    // 具体查询
                    List<PackageFilterInfo> filterInfos = packageFilterInfoMapper.selectByPacId(v.getId());
                    List<PackageImportInfo> importInfos = packageImportInfoMapper.selectByPacId(v.getId());
                    List<ProductPackageExportEntity> productInfos = dealEsInfo(v, filterInfos, importInfos);

                    // 如果只有导入的数据 需要根据导入的数据排序
                    PackageImportInfo whiteImportInfo = importInfos.stream()
                            .filter(u -> Objects.equals(u.getLinkType(), 0)).findFirst().orElse(null);
                    List<Long> pacProductIds = new ArrayList<>();
                    if (whiteImportInfo != null && CollectionUtils.isEmpty(filterInfos) && StringUtils.isNotBlank(whiteImportInfo.getLinkData())) {
                        HashMap<String, Long> map = productInfos.stream()
                                .collect(HashMap::new, (k1, v1) -> k1.put(v1.getName(), v1.getId()), HashMap::putAll);
                        List<Long> finalPacProductIds = pacProductIds;
                        Arrays.stream(whiteImportInfo.getLinkData().split(",")).collect(Collectors.toList())
                                .forEach(x -> {
                            if (MapUtils.isNotEmpty(map) && map.get(x) != null) {
                                finalPacProductIds.add(map.get(x));
                            }
                        });
                    } else {
                        pacProductIds = productInfos.stream().map(ProductPackageExportEntity::getId).collect(Collectors.toList());
                    }
                    redisService.set(RedisKeysEnum.PACKAGE_INFO.join(v.getPackageId()), StringUtils.join(pacProductIds, ","));
                });
            }
        }
    }

    private static void getNoProductInfos(SearchProductIdInPacReq requestData, List<SearchProductIdIsExistResp> rets) {
        requestData.getProductIds().forEach(v -> {
            SearchProductIdIsExistResp resp = new SearchProductIdIsExistResp();
            resp.setProductId(v);
            resp.setIsExist(0);
            rets.add(resp);
        });
    }

    private void checkParams(CommonProductPackageReq requestData, PackageTemplate template) {
        // 1、筛选条件与导入条件不能同时为空
        if (Objects.equals(requestData.getHasFilter(), 0) && Objects.equals(requestData.getHasImport(), 0)) {
            throw new ProductException("筛选条件与导入条件不能同时为空");
        }

        // 2、如果有筛选条件，则筛选条件不能为空
        if (Objects.equals(requestData.getHasFilter(), 1)) {
            if (CollectionUtils.isEmpty(requestData.getFilterInfoDataList())) {
                throw new ProductException("筛选条件不能为空");
            }
            requestData.getFilterInfoDataList().forEach(v -> {
                if (v.getCondition() == null) {
                    throw new ProductException("筛选条件的正反选条件不能为空");
                }
            });
        }

        // 3、如果有导入条件，则导入条件不能为空
        if (Objects.equals(requestData.getHasImport(), 1)) {
            if (CollectionUtils.isEmpty(requestData.getImportInfoDataList())) {
                throw new ProductException("导入条件不能为空");
            }
            requestData.getImportInfoDataList().forEach(v -> {
                if (v.getLinkType() == null) {
                    throw new ProductException("填写类型不能为空");
                }

                if (StringUtils.isBlank(v.getLinkContent())) {
                    throw new ProductException("填写内容不能为空");
                }
            });
            long whiteCount = requestData.getImportInfoDataList().stream().filter(v -> Objects.equals(v.getLinkType(), 0)).count();
            long blackCount = requestData.getImportInfoDataList().stream().filter(v -> Objects.equals(v.getLinkType(), 1)).count();
            if (whiteCount > 1 || blackCount > 1) {
                throw new ProductException("白名单和黑名单填写条件不能超过2个");
            }
        }


        // 4、如果有效时间为定期有效时，则开始时间和结束时间不能为空，且开始时间要小于结束时间
        Date startDate = DateUtil.formatToDate(requestData.getStartDate(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS);
        template.setStartDate(startDate);
        if (Objects.equals(requestData.getValidType(), 1)) {
            if (StringUtils.isBlank(requestData.getEndDate())) {
                throw new ProductException("结束时间不能为空");
            }
            Date endDate = DateUtil.formatToDate(requestData.getEndDate(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS);
            template.setEndDate(endDate);
            if (!startDate.before(endDate)) {
                throw new ProductException("结束时间不能大于开始时间");
            }
        } else {
            template.setEndDate(null);
        }



    }
}
