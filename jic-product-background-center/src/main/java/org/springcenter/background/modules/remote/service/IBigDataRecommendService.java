package org.springcenter.background.modules.remote.service;

import org.apache.commons.lang3.tuple.Pair;
import org.springcenter.background.modules.remote.entity.BigDataComprehensiveData;
import org.springcenter.background.modules.remote.entity.BigDataComprehensiveReq;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/1/21 9:44
 */

public interface IBigDataRecommendService {

    /**
     * 查询综合排序
     * @param bigDataComprehensiveReq 入参
     * @return 返回
     */
    Pair<List<BigDataComprehensiveData>, Integer> searchComprehensiveList(BigDataComprehensiveReq bigDataComprehensiveReq);
}
