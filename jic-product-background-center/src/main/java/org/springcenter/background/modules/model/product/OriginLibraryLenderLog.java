package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@TableName(value = "ORIGIN_LIBRARY_LENDER_LOG")
@Data
@ApiModel(value="OriginLibraryLenderLog", description="原样库出借记录表")
public class OriginLibraryLenderLog {
    @TableId(value = "ID")
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_BY")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "UPDATE_BY")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "部门")
    @TableField(value = "SYS_ORG_CODE")
    private String sysOrgCode;

    @ApiModelProperty(value = "原样库id")
    @TableField(value = "ORIGIN_LIBRARY_ID")
    private String originLibraryId;

    @ApiModelProperty(value = "出借人")
    @TableField(value = "LENDER_NAME")
    private String lenderName;

    @ApiModelProperty(value = "当前借用人电话")
    @TableField(value = "LENDER_PHONE")
    private String lenderPhone;

    @ApiModelProperty(value = "归还时间")
    @TableField(value = "RETURN_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnTime;
}
