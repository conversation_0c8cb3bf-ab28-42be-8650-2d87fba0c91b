package org.springcenter.background.modules.service;

import org.springcenter.product.api.dto.background.ProductInfoByCode;
import org.springcenter.product.api.dto.background.SampleCodeSpuInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/9/26 14:47
 */
public interface ProductInfoService {
    /**
     * 根据商品条码获取商品款号
     * @param requestData 商品条码
     * @return 返回
     */
    ProductInfoByCode getSpuByProductCode(String requestData);


    /**
     * 批量查询样衣的款色信息
     * @param requestData 样衣号
     * @return 返回
     */
    List<SampleCodeSpuInfo> getSkcInfoBySampleCode(List<String> requestData);
}
