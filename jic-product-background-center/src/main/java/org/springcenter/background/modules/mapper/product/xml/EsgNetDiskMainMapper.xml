<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.EsgNetDiskMainMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.EsgNetDiskMain">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="season" column="SEASON" jdbcType="VARCHAR"/>
            <result property="brand" column="BRAND" jdbcType="VARCHAR"/>
            <result property="series" column="SERIES" jdbcType="VARCHAR"/>
            <result property="craft" column="CRAFT" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="path" column="PATH" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,SEASON,BRAND,
        SERIES,CRAFT,CREATE_TIME,
        UPDATE_TIME,IS_DEL,UPDATE_BY,
        PATH
    </sql>
</mapper>
