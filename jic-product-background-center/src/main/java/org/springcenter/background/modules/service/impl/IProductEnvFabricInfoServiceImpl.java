package org.springcenter.background.modules.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.background.config.exception.ProductException;
import org.springcenter.background.modules.mapper.product.EnvironmentalFabricSettingMapper;
import org.springcenter.background.modules.mapper.product.ProductEnvFabricInfoMapper;
import org.springcenter.background.modules.mapper.product.ProductSellingPointsMapper;
import org.springcenter.background.modules.model.product.EnvironmentalFabricSetting;
import org.springcenter.background.modules.model.product.ProductEnvFabricInfo;
import org.springcenter.background.modules.model.product.ProductSellingPoints;
import org.springcenter.background.modules.service.IProductEnvFabricInfoService;
import org.springcenter.background.modules.service.IProductFabService;
import org.springcenter.background.modules.util.EsUtil;
import org.springcenter.product.api.dto.background.ProductSellingPointsEntity;
import org.springcenter.product.api.dto.background.ProductSellingPointsReq;
import org.springcenter.product.api.dto.background.fab.ProductEnvInfoEsResp;
import org.springcenter.product.api.dto.background.fab.ProductSellingPointEsResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2025/1/8 14:14
 */
@Service
@Slf4j
public class IProductEnvFabricInfoServiceImpl implements IProductEnvFabricInfoService {


    @Autowired
    private ProductEnvFabricInfoMapper productEnvFabricInfoMapper;

    @Value("${product.env.fabric.id}")
    private String envFabricId;

    @Value("${fab.skc.env.info.index}")
    private String fabEnvInfoIndex;

    @Autowired
    private EsUtil esUtil;
    
    @Autowired
    private EnvironmentalFabricSettingMapper environmentalFabricSettingMapper;

    @Override
    public void generateEnvironmentFabricInfo(String name) {
        // scdl 产品大类  sjcf 成分面料  sc_origin_area 环保
        // 删除表内数据
        List<String> names = new ArrayList<>();
        if (StringUtils.isNotBlank(name)) {
            names = Arrays.stream(StringUtils.split(name, ",")).distinct().collect(Collectors.toList());
        }
        productEnvFabricInfoMapper.updateAll(names);


        // 1、获取总数
        Integer totalNum = searchTotalNumInSellingPointEs(names, fabEnvInfoIndex);
        if (totalNum == 0) {
            return;
        }



        long pageTotal = 1;
        int pageSize = 5000;
        if(totalNum % pageSize == 0){
            pageTotal = totalNum / pageSize;
        }else{
            pageTotal = totalNum / pageSize + 1;
        }

        List<EnvironmentalFabricSetting> environmentalFabricSettings = environmentalFabricSettingMapper.selectAllInfos();
        List<ProductEnvFabricInfo> inserts = new ArrayList<>();
        for (int i = 0; i < pageTotal; i++) {
            List<ProductEnvInfoEsResp> respList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(names)) {
                Page page = new Page(1, 100);
                respList = searchDataForEnviroFabricInEs(names, page, fabEnvInfoIndex);
            } else {
                respList = searchDataForEnviroInfoNameInEsForNum(i, 5000, fabEnvInfoIndex);
            }

            if (CollectionUtils.isEmpty(respList)) {
                continue;
            }
            respList.forEach(v -> {
                if (StringUtils.isBlank(v.getScdl())) {
                    return;
                }

                String prefix = "";
                if (v.getScdl().contains("针织") || v.getScdl().contains("梭织") || v.getScdl().contains("牛仔")
                        || v.getScdl().contains("家纺") || v.getScdl().contains("芝麻实验室")) {
                    prefix = "主面料含：";
                } else if (v.getScdl().contains("毛纱")) {
                    prefix = "主纱线含：";
                }
                if (StringUtils.isBlank(prefix) || CollectionUtils.isEmpty(v.getEnvist())) {
                    log.info("商品id：{}， 商品款号：{}", v.getId(), v.getStyle_id());
                    return;
                }
                
                String sjcfInfo = dealSjcfInfo(prefix, v.getEnvist(), environmentalFabricSettings);
                if (StringUtils.isNotBlank(sjcfInfo)) {
                    ProductEnvFabricInfo info = new ProductEnvFabricInfo();
                    info.setProductId(Long.valueOf(v.getProduct_id()));
                    info.setName(v.getStyle_id());
                    info.setSkcNo(v.getStyle_color_id());
                    info.setEnvFabricInfo(sjcfInfo);
                    info.setCreateTime(new Date());
                    info.setUpdateTime(new Date());
                    info.setId(IdLeaf.getId(envFabricId));
                    inserts.add(info);
                }
            });
        }
        
        if (CollectionUtils.isNotEmpty(inserts)) {
            List<List<ProductEnvFabricInfo>> partition = com.google.common.collect.Lists.partition(inserts, 1000);
            partition.forEach(v -> {
                productEnvFabricInfoMapper.insertBatch(v);
            });
           
        }
    }

    @Autowired
    private IProductFabService productFabService;

    @Data
    public class SmallData {
        private String skcNo;

        private String envFabricInfo;
    }

    @Override
    public String queryFabDetailEnvInfo(String requestData) {

        if (StringUtils.isBlank(requestData)) {
            return null;
        }
        List<ProductEnvFabricInfo> productEnvFabricInfos = productEnvFabricInfoMapper.selectByProductId(requestData);
        if (CollectionUtils.isEmpty(productEnvFabricInfos)) {
            return "";
        }
        // 查询当前环保信息
        ProductSellingPointEsResp resp = productFabService.queryFabDetailSellingPoint(requestData);
        if (resp == null) {
            return "";
        }

        if (Objects.equals(resp.getSc_origin_area(), "N;Y")) {
            List<String> list = new ArrayList<>();
            productEnvFabricInfos.forEach(v -> {

                list.add(v.getSkcNo() + "色" + v.getEnvFabricInfo());
            });

            return String.join(";", list);
        } else {
            List<String> list = productEnvFabricInfos.stream()
                    .map(ProductEnvFabricInfo::getEnvFabricInfo).distinct().collect(Collectors.toList());
            return CollectionUtils.isEmpty(list) ? "" : list.get(0);
        }

    }

    @Autowired
    private ProductSellingPointsMapper productSellingPointsMapper;

    @Override
    public List<ProductSellingPointsEntity> batchQuerySellingPoints(List<String> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            throw new ProductException("参数不能为空");
        }
        List<ProductSellingPoints> lists = productSellingPointsMapper.selectByProductIds(requestData);
        if (CollectionUtils.isEmpty(lists)) {
            return new ArrayList<>();
        }

        List<ProductSellingPointsEntity> rets = new ArrayList<>();
        lists.forEach(v -> {
            ProductSellingPointsEntity entity = new ProductSellingPointsEntity();
            entity.setProductId(v.getProductId());
            entity.setSellingPoints(v.getSellingPoints());
            rets.add(entity);
        });
        return rets;
    }

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Override
    public void addProductSellingPoints(ProductSellingPointsReq requestData) {
        if (requestData == null || CollectionUtils.isEmpty(requestData.getSellingPoints())) {
            throw new ProductException("参数不能为空");
        }
        List<ProductSellingPoints> productSellingPoints = new ArrayList<>();
        requestData.getSellingPoints().forEach(v -> {
            ProductSellingPoints points = new ProductSellingPoints();
            points.setProductId(requestData.getProductId());
            points.setSellingPoints(v);
            points.setCreateTime(new Date());
            points.setUpdateTime(new Date());
            points.setId(IdLeaf.getId(envFabricId));
            productSellingPoints.add(points);
        });

        List<ProductSellingPoints> points = productSellingPointsMapper.selectByProductIds(Lists.newArrayList(requestData.getProductId()));

        template.execute(action -> {
            if (CollectionUtils.isNotEmpty(points)) {
                productSellingPointsMapper.updateByProductId(requestData.getProductId());
            }
            productSellingPointsMapper.insertBatch(productSellingPoints);
            return true;
        });

    }


    private String dealSjcfInfo(String prefix, List<String> sjcf, List<EnvironmentalFabricSetting> environmentalFabricSettings) {
        if (CollectionUtils.isEmpty(environmentalFabricSettings)) {
            log.info("环境面料设置表为空");
            return "";
        }
        HashMap<String, String> map = environmentalFabricSettings.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getSystemName(), v.getOutsideName()), HashMap::putAll);



        List<String> mlList = new ArrayList<>();
        for (int i = 0; i < sjcf.size(); i++) {
            if (MapUtils.isEmpty(map)) {
                return "";
            }

            String s = map.get(sjcf.get(i));
            if (StringUtils.isNotBlank(s)) {
                mlList.add(s);
            }
        }

        if (CollectionUtils.isEmpty(mlList)) {
            return "";
        }

        String collect = mlList.stream().collect(Collectors.joining("、"));
        if (StringUtils.isNotBlank(collect)) {
            return prefix + collect;
        }
        return "";
    }

    private Integer searchTotalNumInSellingPointEs(List<String> names, String fabSellingPointInfoIndex) {
        if (CollectionUtils.isNotEmpty(names)) {
            return names.size();
        }

        SearchRequest request = new SearchRequest();
        request.indices(fabSellingPointInfoIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("sc_origin_area", "Y", "N;Y"));
        queryBuilder.must(QueryBuilders.existsQuery("product_id"));
        sourceBuilder.query(queryBuilder);

        sourceBuilder.trackTotalHits(true);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品SELLING_POINT_TOTAL {}", request.source().toString());
        int entities = 0;
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return 0;
            }

            SearchHits hits = response.getHits();
            entities = Math.toIntExact(hits.getTotalHits().value);

        } catch (IOException e) {
            log.error("查询商品SELLING_POINT_TOTAL异常e = {}", e.getMessage());
            return 0;
        }
        return entities;
    }



    private List<ProductEnvInfoEsResp> searchDataForEnviroFabricInEs(List<String> names, Page page, String fabSellingPointInfoIndex) {


        SearchRequest request = new SearchRequest();
        request.indices(fabSellingPointInfoIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("sc_origin_area", "Y", "N;Y"));
        queryBuilder.must(QueryBuilders.existsQuery("product_id"));
        if (CollectionUtils.isNotEmpty(names)) {
            queryBuilder.must(QueryBuilders.termsQuery("style_id", names));
        }
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from((page.getPageNo() - 1) * page.getPageSize());
        sourceBuilder.size(page.getPageSize());


        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品SELLING_POINT_INFO {}", request.source().toString());
        List<ProductEnvInfoEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }

            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                ProductEnvInfoEsResp resp = ProductEnvInfoEsResp.fromJson(hit.getSourceAsString(), ProductEnvInfoEsResp.class);
                resp.buildEnvList();
                entities.add(resp);
            }

        } catch (IOException e) {
            log.error("查询商品SELLING_POINT_TOTAL异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }


    public Integer searchMinAndMaxNumInEs(SortOrder sortOrder, String index) {
        SearchRequest request = new SearchRequest();
        request.indices(index);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("sc_origin_area", "Y", "N;Y"));
        queryBuilder.must(QueryBuilders.existsQuery("product_id"));

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(1);
        sourceBuilder.sort("id", sortOrder);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品searchMinNumInEs {}", request.source().toString());
        Integer mNum = 0;
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return 0;
            }
            SearchHit[] hits = response.getHits().getHits();
            ProductSellingPointEsResp entity = ProductSellingPointEsResp.fromJson(hits[0].getSourceAsString(), ProductSellingPointEsResp.class);
            mNum = Integer.valueOf(entity.getId());
        } catch (IOException e) {
            log.error("查询商品searchMinNumInEs异常e = {}", e.getMessage());
            return 0;
        }
        return mNum;
    }


    public List<ProductEnvInfoEsResp> searchDataForEnviroInfoNameInEsForNum(int from, int size, String index) {
        SearchRequest request = new SearchRequest();
        request.indices(index);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("sc_origin_area", "Y", "N;Y"));
        queryBuilder.must(QueryBuilders.existsQuery("product_id"));

        sourceBuilder.sort("id", SortOrder.ASC);
        sourceBuilder.from(from * size);
        sourceBuilder.size(size);
        sourceBuilder.query(queryBuilder);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品EnviroInfo:{}", request.source().toString());
        List<ProductEnvInfoEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                ProductEnvInfoEsResp resp = ProductEnvInfoEsResp.fromJson(hit.getSourceAsString(), ProductEnvInfoEsResp.class);
                resp.buildEnvList();
                entities.add(resp);
            }
        } catch (IOException e) {
            log.error("查询商品EnviroInfo异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }
}
