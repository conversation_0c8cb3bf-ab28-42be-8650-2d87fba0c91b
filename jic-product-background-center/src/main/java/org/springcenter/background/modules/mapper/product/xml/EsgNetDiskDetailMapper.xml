<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.EsgNetDiskDetailMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.EsgNetDiskDetail">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="esgNetDiskMainId" column="ESG_NET_DISK_MAIN_ID" jdbcType="VARCHAR"/>
            <result property="qiniuPath" column="QINIU_PATH" jdbcType="VARCHAR"/>
            <result property="neid" column="NEID" jdbcType="VARCHAR"/>
            <result property="nsid" column="NSID" jdbcType="VARCHAR"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="type" column="TYPE" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ESG_NET_DISK_MAIN_ID,QINIU_PATH,
        NEID,NSID,IS_DEL,
        CREATE_TIME,UPDATE_TIME,TYPE
    </sql>
</mapper>
