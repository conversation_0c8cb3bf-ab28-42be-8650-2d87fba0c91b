package org.springcenter.background.modules.service;

import com.jnby.common.Page;
import org.springcenter.background.modules.model.product.ProductReminderInfo;
import org.springcenter.product.api.dto.background.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2025/5/15 16:08
 */
public interface IProductReminderService {
    /**
     * 获取用户提醒类型分类
     * @return 返回
     */
    List<ReminderTypeListResp> getReminderTypeList();

    /**
     * 根据分页查询提醒列表
     * @param requestData 入参
     * @param page 分页
     * @return 返回
     */
    List<ReminderListResp> getReminderList(ReminderListReq requestData, Page page);

    /**
     * 根据id删除提醒信息
     * @param requestData id
     * @return 返回
     */
    Boolean delReminderInfo(String requestData);


    /**
     * 增加提醒信息
     * @param requestData 参数
     * @return 返回
     */
    Boolean addReminderInfo(ReminderAddInfoReq requestData);


    /**
     * 修改提醒信息
     * @param requestData 参数
     * @return 返回
     */
    Boolean updateReminderInfo(ReminderUpdateInfoReq requestData);

    /**
     * 获取用户提醒信息
     * @param requestData id
     * @return 返回
     */
    ProductReminderInfo getReminderInfo(String requestData);

    /**
     * 手动发送提醒
     * @param requestData 入参
     * @return 返回
     */
    Boolean manualReminder(ReminderInfoReq requestData);


    /**
     * 定时任务
     * @return 返回
     */
    Boolean fabReminderJob();
}
