package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.PackageTemplate;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【PACKAGE_TEMPLATE(商品模板)】的数据库操作Mapper
* @createDate 2024-07-25 13:28:59
* @Entity generator.domain.PackageTemplate
*/
public interface PackageTemplateMapper extends BaseMapper<PackageTemplate> {

    Long getPacId();

    List<PackageTemplate> selectByParams(@Param("pacName") String packageName, @Param("pacId") String pacId,
                                         @Param("isOpen") Integer isOpen, @Param("list") List<String> allowRule);

    PackageTemplate selectByValidId(@Param("id") String id);

    PackageTemplate selectByPackIdValid(@Param("pacId") String requestData);

    List<PackageTemplate> selectByPackIds(@Param("list") List<String> pacIds);

    List<PackageTemplate> getAllPacTemplate();

    void updateEndDateTime(@Param("id") String id);

    List<PackageTemplate> selectByPacIds(@Param("list") List<String> requestData);
}
