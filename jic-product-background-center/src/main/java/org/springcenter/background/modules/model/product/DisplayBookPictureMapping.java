package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 陈列册图片映射
 */
@TableName(value = "DISPLAY_BOOK_PICTURE_MAPPING")
@Data
public class DisplayBookPictureMapping {

    @TableId(value = "ID")
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField(value = "CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 0正常 1已删除")
    private Integer isDeleted;

    @TableField(value = "DISPLAY_BOOK_FILE_MAPPING_ID")
    @ApiModelProperty(value = "映射列表id")
    private String displayBookFileMappingId;

    @TableField(value = "BRAND_CODE")
    @ApiModelProperty(value = "品牌id")
    private Long brandCode;

    @TableField(value = "BRAND_NAME")
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @TableField(value = "YEAR")
    @ApiModelProperty(value = "年份")
    private String year;

    @TableField(value = "SEASON_GOODS")
    @ApiModelProperty(value = "货季")
    private String seasonGoods;

    @TableField(value = "CHANNEL")
    @ApiModelProperty(value = "渠道:直营/经销")
    private String channel;

    @TableField(value = "OPERATOR")
    @ApiModelProperty(value = "操作人")
    private String operator;

    /**
     * 枚举
     * {@see org.springcenter.product.api.enums.DisplayBookPictureEnum }
     */
    @TableField(value = "PICTURE_TYPE")
    @ApiModelProperty(value = "图片类型:1人台图;2货杆图")
    private Integer pictureType;

    @TableField(value = "PICTURE_ID")
    @ApiModelProperty(value = "图片ID")
    private String pictureId;

    @TableField(value = "PICTURE_URL")
    @ApiModelProperty(value = "图片地址")
    private String pictureUrl;

    @TableField(value = "BAND_ID")
    @ApiModelProperty(value = "波段id")
    private Long bandId;

    @TableField(value = "BAND_NAME")
    @ApiModelProperty(value = "波段名称")
    private String bandName;

    @TableField(value = "GROUP_NUMBER")
    @ApiModelProperty(value = "分组编号:人台图时为人台编号、货杆图时为分组编号")
    private Integer groupNumber;

    @TableField(value = "SKC_CODE")
    @ApiModelProperty(value = "款色号")
    private String skcCode;

    @TableField(value = "SKC_SMALL_CLASS_NAME")
    @ApiModelProperty(value = "SKC小类名称")
    private String skcSmallClassName;

    @TableField(value = "SKC_IMG_URL")
    @ApiModelProperty(value = "SKC预览图片")
    private String skcImgUrl;

    @TableField(value = "STATUS")
    @ApiModelProperty(value = "状态 0=正常 1=异常")
    private Integer status;

    @TableField(value = "ERROR_MSG")
    @ApiModelProperty(value = "SKC异常原因")
    private String errorMsg;

}
