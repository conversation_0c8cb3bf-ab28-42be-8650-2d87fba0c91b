package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.DisplayBookPictureMapping;

import java.util.List;

public interface DisplayBookPictureMappingMapper extends BaseMapper<DisplayBookPictureMapping> {

    void insertBatch(@Param("list") List<DisplayBookPictureMapping> dbList);
}
