package org.springcenter.background.modules.remote.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.modules.remote.IFabricRemoteApi;
import org.springcenter.background.modules.remote.entity.FabricInfoRespEntity;
import org.springcenter.background.modules.remote.entity.JicBaseResp;
import org.springcenter.background.modules.remote.service.IFabricInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date:2024/6/19 15:40
 */
@Service
@Slf4j
public class FabricInfoServiceImpl implements IFabricInfoService {
    @Autowired
    private IFabricRemoteApi fabricRemoteApi;

    @Override
    public FabricInfoRespEntity getBatchFabricInfo(String code) {
        try {
            // 组装触发任务的实体
            Response<JicBaseResp<FabricInfoRespEntity>> response = fabricRemoteApi.getBatchFabricInfo(code).execute();
            if (response.isSuccessful()){
                return response.body().getData();
            }
            log.error("===========调用获取面料克重失败， 入参：{}", JSONObject.toJSONString(code));
            throw new RuntimeException("===========调用获取面料克重失败");
        } catch (IOException e) {
            log.error("===========调用获取面料克重失败  e:{} message:{}", e, e.getMessage());
            return null;
        }
    }

}
