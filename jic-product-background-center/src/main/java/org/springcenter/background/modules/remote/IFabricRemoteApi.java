package org.springcenter.background.modules.remote;

import org.springcenter.background.modules.remote.entity.FabricInfoRespEntity;
import org.springcenter.background.modules.remote.entity.JicBaseResp;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @Date:2024/6/19 15:16
 */
public interface IFabricRemoteApi {


    @GET("/api/gateway/scm-material/sample-cloth/find")
    Call<JicBaseResp<FabricInfoRespEntity>> getBatchFabricInfo(@Query("sccode") String sccode);
}
