<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.AfterSaleProductLabelMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.AfterSaleProductLabel">
        <id property="id" column="ID" />
        <result property="productId" column="PRODUCT_ID" />
        <result property="name" column="NAME" />
        <result property="skcCode" column="SKC_CODE" />
        <result property="firstLabelCode" column="FIRST_LABEL_CODE" />
        <result property="secondLabelCode" column="SECOND_LABEL_CODE" />
        <result property="thirdLabelCode" column="THIRD_LABEL_CODE" />
        <result property="type" column="TYPE" />
        <result property="createTime" column="CREATE_TIME" />
        <result property="updateTime" column="UPDATE_TIME" />
        <result property="isDeleted" column="IS_DELETED" />
        <result property="updater" column="UPDATER" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, PRODUCT_ID, NAME, SKC_CODE, FIRST_LABEL_CODE, SECOND_LABEL_CODE,
        THIRD_LABEL_CODE, TYPE, CREATE_TIME, UPDATE_TIME, IS_DELETED, UPDATER
    </sql>

    <update id="updateByProInfo">
        update after_sale_product_label set is_deleted = 1 ,
                                            update_time = sysdate
        where product_id = #{productId}
          AND FIRST_LABEL_CODE = #{firstCode}
          AND SKC_CODE = #{skcCode}
          AND IS_DElETED = 0
    </update>
    <select id="selectByProductId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM after_sale_product_label
        WHERE product_id = #{productId} AND IS_DElETED = 0
    </select>

    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO after_sale_product_label
            (ID, PRODUCT_ID, NAME, SKC_CODE, FIRST_LABEL_CODE, SECOND_LABEL_CODE,
            THIRD_LABEL_CODE, TYPE, CREATE_TIME, UPDATE_TIME, IS_DELETED, UPDATER) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.productId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
            #{item.skcCode,jdbcType=VARCHAR}, #{item.firstLabelCode,jdbcType=VARCHAR}, #{item.secondLabelCode,jdbcType=VARCHAR},
            #{item.thirdLabelCode,jdbcType=VARCHAR}, #{item.type,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=DECIMAL}, #{item.updater,jdbcType=VARCHAR})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

</mapper>
