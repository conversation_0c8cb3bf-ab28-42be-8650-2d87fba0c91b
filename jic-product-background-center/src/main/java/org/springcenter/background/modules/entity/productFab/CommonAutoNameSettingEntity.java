package org.springcenter.background.modules.entity.productFab;

import lombok.Data;
import org.springcenter.background.modules.model.product.FabAnDynamicFieldSetting;
import org.springcenter.background.modules.model.product.FabAutoNameSetting;


import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/1/17 14:57
 */
@Data
public class CommonAutoNameSettingEntity {

    private List<FabAutoNameSetting> partSortedList;

    private List<FabAnDynamicFieldSetting> fabAnDynamicFieldSettings;

    private List<String> forbiddenList;

    private Long pageTotal;
}
