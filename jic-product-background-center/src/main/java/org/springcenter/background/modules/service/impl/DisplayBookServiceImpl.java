package org.springcenter.background.modules.service.impl;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.EasyExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.background.config.exception.ProductException;
import org.springcenter.background.modules.entity.DisplayBookHangerPictureEntity;
import org.springcenter.background.modules.entity.DisplayBookMannequinPictureEntity;
import org.springcenter.background.modules.entity.DisplayBookPictureEntity;
import org.springcenter.background.modules.mapper.bojun.CStoreMapper;
import org.springcenter.background.modules.mapper.product.DisplayBookFileMappingMapper;
import org.springcenter.background.modules.mapper.product.DisplayBookInfoMapper;
import org.springcenter.background.modules.mapper.product.DisplayBookPictureMappingMapper;
import org.springcenter.background.modules.model.bojun.CStore;
import org.springcenter.background.modules.model.product.DisplayBookFileMapping;
import org.springcenter.background.modules.model.product.DisplayBookInfo;
import org.springcenter.background.modules.model.product.DisplayBookPictureMapping;
import org.springcenter.background.modules.service.CommonDataRuleService;
import org.springcenter.background.modules.service.IDisplayBookService;
import org.springcenter.background.modules.service.IProductService;
import org.springcenter.background.modules.service.impl.excel.DisplayBookHangerPictureListener;
import org.springcenter.background.modules.service.impl.excel.DisplayBookMannequinPictureListener;
import org.springcenter.background.modules.util.DateUtil;
import org.springcenter.background.modules.util.FileParseUtil;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.ChannelTypeEnum;
import org.springcenter.product.api.enums.DisplayBookPictureEnum;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DisplayBookServiceImpl implements IDisplayBookService {
    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;
    @Autowired
    private DisplayBookInfoMapper displayBookInfoMapper;
    @Autowired
    private DisplayBookFileMappingMapper displayBookFileMappingMapper;
    @Autowired
    private DisplayBookPictureMappingMapper displayBookPictureMappingMapper;
    @Autowired
    private QiniuUtil qiniuUtil;
    @Autowired
    private IProductService iProductService;
    @Autowired
    private CommonDataRuleService commonDataRuleService;
    @Autowired
    private CStoreMapper cStoreMapper;

    // 陈列册相关表的分布式id
    @Value("${display.book.leaf.tag}")
    private String displayBookLeafTag;

    // 陈列册货季-波段映射
    @Value("${season.bandName.config}")
    private String seasonBandNameConfig;

    // 陈列册波段-货季映射
    @Value("${bandName.season.config}")
    private String bandNameSeasonConfig;

    // 波段str配置
    @Value("${bands.str}")
    private String bandsStr;


    @Override
    public String saveInfo(DisplayBookInfoAddReq req) {
        // 1、判断当前年份波段品牌是否已存在
        LambdaQueryWrapper<DisplayBookInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisplayBookInfo::getBrandCode, req.getBrandCode());
        queryWrapper.eq(DisplayBookInfo::getBrandName, req.getBrandName());
        queryWrapper.eq(DisplayBookInfo::getYear, req.getYear());
        queryWrapper.eq(DisplayBookInfo::getChannel, req.getChannel());
        queryWrapper.eq(DisplayBookInfo::getType, req.getType());
        queryWrapper.eq(DisplayBookInfo::getIsDeleted, IsDeleteEnum.NORMAL.getCode());

        // type=2时使用月份判断，type=1时使用波段判断
        if (Objects.equals(req.getType(), 2)) {
            queryWrapper.eq(DisplayBookInfo::getBandNameMonth, req.getBandNameMonth());
        } else {
            queryWrapper.eq(DisplayBookInfo::getBandId, req.getBandId());
            queryWrapper.eq(DisplayBookInfo::getBandName, req.getBandName());
        }

        DisplayBookInfo one = displayBookInfoMapper.selectOne(queryWrapper);
        if (one != null) {
            if (Objects.equals(req.getType(), 2)) {
                throw new ProductException("当前月份已有陈列指引保存，请编辑已保存的陈列指引");
            } else {
                throw new ProductException("当前波段已有陈列册保存，请编辑已保存的陈列册");
            }
        }

        // 2、进行保存
        DisplayBookInfo displayBookInfo = new DisplayBookInfo();
        BeanUtils.copyProperties(req, displayBookInfo);
        String id = IdLeaf.getId(displayBookLeafTag);
        displayBookInfo.setId(id);
        displayBookInfo.setCreateTime(new Date());
        displayBookInfo.setUpdateTime(new Date());

        // 设置默认type
        if (displayBookInfo.getType() == null) {
            displayBookInfo.setType(1);
        }

        // type=1时才需要拆分波段存储
        if (Objects.equals(displayBookInfo.getType(), 1) && StringUtils.isNotBlank(req.getBandName())) {
            // 按照下划线拆分，例如 1-A，拆分成 1, A
            String[] bands = req.getBandName().split("-");
            displayBookInfo.setBandNameMonth(Integer.valueOf(bands[0]));
            displayBookInfo.setBandNameLetter(bands[1]);
        }

        displayBookInfoMapper.insert(displayBookInfo);
        return id;
    }

    @Override
    public List<DisplayBookListQueryResp> listInfos(DisplayBookListQueryReq req, Page page, String component) {
        log.info("陈列册管理-查询列表入参:{}", JSON.toJSONString(req));
        // 获取品牌权限
        List<Long> brandIds = req.getBrandIds();
        List<Long> allowBrandIds = commonDataRuleService.getAllowBrandIdRule(component, brandIds);
        List<String> channelList = null;
        if (StringUtils.isNotBlank(req.getChannel())) {
            channelList = Lists.newArrayList(req.getChannel());
        } else {
            channelList = commonDataRuleService.getAllowChannel(component);
        }

        LambdaQueryWrapper<DisplayBookInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DisplayBookInfo.class, item -> !item.getColumn().equals("CLOB_JSON"));
        queryWrapper.in(CollectionUtils.isNotEmpty(allowBrandIds), DisplayBookInfo::getBrandCode, allowBrandIds);
        queryWrapper.in(CollectionUtils.isNotEmpty(req.getBrandNames()), DisplayBookInfo::getBrandName, req.getBrandNames());
        queryWrapper.in(CollectionUtils.isNotEmpty(req.getBandIds()), DisplayBookInfo::getBandId, req.getBandIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(req.getYears()), DisplayBookInfo::getYear, req.getYears());
        queryWrapper.in(CollectionUtils.isNotEmpty(channelList), DisplayBookInfo::getChannel, channelList);
        queryWrapper.eq(req.getType() != null, DisplayBookInfo::getType, req.getType());
        queryWrapper.eq(DisplayBookInfo::getIsDeleted, IsDeleteEnum.NORMAL.getCode());
        queryWrapper.orderByDesc(DisplayBookInfo::getYear);
        queryWrapper.orderByDesc(DisplayBookInfo::getBandNameMonth);
        queryWrapper.orderByDesc(DisplayBookInfo::getBandNameLetter);
        queryWrapper.orderByDesc(DisplayBookInfo::getCreateTime);

        log.info("查询数据库入参:{}", JSON.toJSONString(queryWrapper));
        com.github.pagehelper.Page<DisplayBookInfo> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<DisplayBookInfo> displayBookInfos = displayBookInfoMapper.selectList(queryWrapper);
        List<DisplayBookListQueryResp> respList = Optional.ofNullable(displayBookInfos).orElse(Lists.newArrayList())
                .stream().map(this::buildDisplayBookListQueryResp).collect(Collectors.toList());
        PageInfo<DisplayBookListQueryResp> pageInfo = new PageInfo(hPage);
        pageInfo.setList(respList);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    @Override
    public List<DisplayBookListForPosQueryResp> listInfosForPos(DisplayBookListForPosQueryReq req, Page page, String component) {
        // 根据门店获取品牌
        CStore cStore = cStoreMapper.selectById(req.getStoreId());
        if (Objects.isNull(cStore)) {
            throw new ProductException("获取门店品牌失败");
        }
        String channel = ChannelTypeEnum.DISTRIBUTION.getDesc();
        if (cStore.getCCustomerId() == 176) {
            channel = ChannelTypeEnum.DIRECT.getDesc();
        }
        LambdaQueryWrapper<DisplayBookInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DisplayBookInfo.class, item -> !item.getColumn().equals("CLOB_JSON"));
        queryWrapper.in(CollectionUtils.isNotEmpty(req.getBrandIds()), DisplayBookInfo::getBrandCode, req.getBrandIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(req.getBrandNames()), DisplayBookInfo::getBrandName, req.getBrandNames());
        queryWrapper.in(CollectionUtils.isNotEmpty(req.getYears()), DisplayBookInfo::getYear, req.getYears());
        queryWrapper.eq(StringUtils.isNotBlank(req.getSeasonGoods()), DisplayBookInfo::getSeasonGoods, req.getSeasonGoods());
        queryWrapper.eq(req.getType() != null, DisplayBookInfo::getType, req.getType());
        queryWrapper.eq(DisplayBookInfo::getIsDeleted, IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq(DisplayBookInfo::getChannel, channel);
        queryWrapper.isNotNull(DisplayBookInfo::getDisplayTime);
        queryWrapper.orderByDesc(DisplayBookInfo::getYear);
        queryWrapper.orderByDesc(DisplayBookInfo::getBandNameMonth);
        queryWrapper.orderByDesc(DisplayBookInfo::getBandNameLetter);
        queryWrapper.orderByDesc(DisplayBookInfo::getCreateTime);

        com.github.pagehelper.Page<DisplayBookInfo> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());

        List<DisplayBookInfo> displayBookInfos = displayBookInfoMapper.selectList(queryWrapper);
        List<DisplayBookListForPosQueryResp> respList = Optional.ofNullable(displayBookInfos).orElse(Lists.newArrayList())
                .stream().map(this::buildDisplayBookListForPosQueryResp).collect(Collectors.toList());
        PageInfo<DisplayBookListForPosQueryResp> pageInfo = new PageInfo(hPage);
        pageInfo.setList(respList);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    @Override
    public DisplayBookInfoQueryResp getInfo(String id) {
        DisplayBookInfo info = getDisplayBookInfoFromDb(id);
        DisplayBookInfoQueryResp resp = new DisplayBookInfoQueryResp();
        BeanUtils.copyProperties(info, resp);

        // 如果是陈列指引(type=2),用月份覆盖波段名称,直接返回数据，无需从陈列册图片映射查询数据了
        if (Objects.equals(info.getType(), 2)) {
            resp.setBandName(String.valueOf(info.getBandNameMonth()));
            return resp;
        }

        // 如果为空，则需要从陈列册图片映射查询是否有数据、有则需要解析成前端需要的格式返回
        String clobJson = resp.getClobJson();
        if (StringUtils.isBlank(clobJson)) {
            // 根据品牌、货季、波段获取当前陈列册映射列表
            Long brandCode = info.getBrandCode();
            String seasonGoods = info.getSeasonGoods();
            String bandName = info.getBandName();
            String channel = info.getChannel();
            String brandName = info.getBrandName();

            LambdaQueryWrapper<DisplayBookFileMapping> fileMappingQuery = new LambdaQueryWrapper<>();
            fileMappingQuery.eq(DisplayBookFileMapping::getBrandCode, brandCode);
            fileMappingQuery.eq(DisplayBookFileMapping::getBrandName, brandName);
            fileMappingQuery.eq(DisplayBookFileMapping::getSeasonGoods, seasonGoods);
            fileMappingQuery.eq(DisplayBookFileMapping::getChannel, channel);
            fileMappingQuery.eq(DisplayBookFileMapping::getIsDeleted, IsDeleteEnum.NORMAL.getCode());
            DisplayBookFileMapping displayBookFileMapping = displayBookFileMappingMapper.selectOne(fileMappingQuery);
            if (Objects.isNull(displayBookFileMapping)) {
                return resp;
            }

            LambdaQueryWrapper<DisplayBookPictureMapping> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DisplayBookPictureMapping::getDisplayBookFileMappingId, displayBookFileMapping.getId());
            queryWrapper.eq(DisplayBookPictureMapping::getIsDeleted, IsDeleteEnum.NORMAL.getCode());
            queryWrapper.eq(DisplayBookPictureMapping::getBrandCode, brandCode);
            queryWrapper.eq(DisplayBookPictureMapping::getBrandName, brandName);
            queryWrapper.eq(DisplayBookPictureMapping::getSeasonGoods, seasonGoods);
            queryWrapper.eq(DisplayBookPictureMapping::getBandName, bandName);
            queryWrapper.eq(DisplayBookPictureMapping::getChannel, channel);

            int pageNo = 1;
            int pageSize = 100;
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<DisplayBookPictureMapping> hPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNo, pageSize);
            IPage<DisplayBookPictureMapping> pages = displayBookPictureMappingMapper.selectPage(hPage, queryWrapper);
            if (pages.getTotal() > 0) {
                List<DisplayBookPictureMapping> records = pages.getRecords();
                Page page = new Page(pageNo, pageSize);
                page.setCount(pages.getTotal());
                // 循环翻页查询所有数据
                while (page.getPages() >= pageNo) {
                    hPage.setCurrent(++pageNo);
                    pages = displayBookPictureMappingMapper.selectPage(hPage, queryWrapper);
                    if (CollectionUtils.isNotEmpty(pages.getRecords())) {
                        records.addAll(pages.getRecords());
                    }
                }
                // 人台图:图片id-groupId-skc
                resp.setMannequinList(convertMannequinList(records));
                // 货杆图:groupId-图片Id-skc
                resp.setHangerList(convertHangerList(records));
            }
        }
        return resp;
    }

    private List<DisplayBookInfoQueryResp.MannequinOrHangerInfo> convertHangerList(List<DisplayBookPictureMapping> records) {
        List<DisplayBookInfoQueryResp.MannequinOrHangerInfo> hangerList = Lists.newArrayList();

        AtomicReference<Integer> hangerManTitle = new AtomicReference<>(1);
        Optional.ofNullable(records).orElseGet(Lists::newArrayList).stream()
                .filter(record -> Objects.equals(DisplayBookPictureEnum.PICTURE_HANGER.getCode(), record.getPictureType()))
                .collect(Collectors.groupingBy(DisplayBookPictureMapping::getGroupNumber,
                        Collectors.groupingBy(DisplayBookPictureMapping::getPictureId, Collectors.toList()))
                ).forEach((groupNumber, pictureGroup) -> {
                    DisplayBookInfoQueryResp.MannequinOrHangerInfo mannequinInfo = new DisplayBookInfoQueryResp.MannequinOrHangerInfo();
                    hangerList.add(mannequinInfo);

                    List<DisplayBookInfoQueryResp.SingleListData> singleList = Lists.newArrayList();
                    List<String> pictureUrlList = Lists.newArrayList();
                    mannequinInfo.setOutName("货杆图" + hangerManTitle.getAndSet(hangerManTitle.get() + 1));
                    mannequinInfo.setSingleList(singleList);
                    mannequinInfo.setPictureList(pictureUrlList);

                    AtomicReference<Integer> hangerSubTitle = new AtomicReference<>(1);
                    pictureGroup.forEach((pictureId, skcGroup) -> {
                        DisplayBookInfoQueryResp.SingleListData singleListData = new DisplayBookInfoQueryResp.SingleListData();
                        singleList.add(singleListData);

                        List<DisplayBookInfoQueryResp.Skc> skcList = Lists.newArrayList();
                        if (pictureGroup.size() > 1) {
                            singleListData.setOutName("货杆" + hangerSubTitle.getAndSet(hangerSubTitle.get() + 1) + "单品");
                        } else {
                            singleListData.setOutName("货杆单品");
                        }
                        singleListData.setSkcList(skcList);

                        AtomicReference<String> picUrl = new AtomicReference<>();

                        skcGroup.forEach(skcFromDb -> {
                            if (Objects.nonNull(skcFromDb) && StringUtils.isNotBlank(skcFromDb.getSkcCode())) {
                                DisplayBookInfoQueryResp.Skc skc = new DisplayBookInfoQueryResp.Skc();
                                skcList.add(skc);
                                skc.setSkcCode(skcFromDb.getSkcCode());
                                skc.setSkcSmallClassName(skcFromDb.getSkcSmallClassName());
                                skc.setSkcImgUrl(skcFromDb.getSkcImgUrl());
                                skc.setStatus(skcFromDb.getStatus());
                            }
                            if (StringUtils.isBlank(picUrl.get())) {
                                picUrl.set(skcFromDb.getPictureUrl());
                            }
                        });

                        pictureUrlList.add(picUrl.get());
                    });
                });
        return hangerList;
    }

    private List<DisplayBookInfoQueryResp.MannequinOrHangerInfo> convertMannequinList(List<DisplayBookPictureMapping> records) {
        List<DisplayBookInfoQueryResp.MannequinOrHangerInfo> mannequinList = Lists.newArrayList();
        AtomicReference<Integer> manTitle = new AtomicReference<>(1);
        Optional.ofNullable(records).orElseGet(Lists::newArrayList).stream()
                .filter(record -> Objects.equals(DisplayBookPictureEnum.PICTURE_MANNEQUIN.getCode(), record.getPictureType()))
                .collect(Collectors.groupingBy(DisplayBookPictureMapping::getPictureId,
                            Collectors.groupingBy(DisplayBookPictureMapping::getGroupNumber, Collectors.toList()))
                ).forEach((pictureId, pictureGroup) -> {
                    DisplayBookInfoQueryResp.MannequinOrHangerInfo mannequinInfo = new DisplayBookInfoQueryResp.MannequinOrHangerInfo();
                    mannequinList.add(mannequinInfo);

                    List<DisplayBookInfoQueryResp.SingleListData> singleList = Lists.newArrayList();
                    AtomicReference<String> picUrl = new AtomicReference<>();
                    mannequinInfo.setOutName("人台图" + manTitle.getAndSet(manTitle.get() + 1));
                    mannequinInfo.setSingleList(singleList);

                    AtomicReference<Integer> subTitle = new AtomicReference<>(1);
                    pictureGroup.forEach((groupNumber, skcGroup) -> {
                        DisplayBookInfoQueryResp.SingleListData singleListData = new DisplayBookInfoQueryResp.SingleListData();
                        singleList.add(singleListData);

                        List<DisplayBookInfoQueryResp.Skc> skcList = Lists.newArrayList();
                        singleListData.setOutName("人台" + subTitle.getAndSet(subTitle.get() + 1) + "单品");
                        singleListData.setSkcList(skcList);

                        skcGroup.forEach(skcFromDb -> {
                            if (Objects.nonNull(skcFromDb) && StringUtils.isNotBlank(skcFromDb.getSkcCode())) {
                                DisplayBookInfoQueryResp.Skc skc = new DisplayBookInfoQueryResp.Skc();
                                skcList.add(skc);
                                skc.setSkcCode(skcFromDb.getSkcCode());
                                skc.setSkcSmallClassName(skcFromDb.getSkcSmallClassName());
                                skc.setSkcImgUrl(skcFromDb.getSkcImgUrl());
                                skc.setStatus(skcFromDb.getStatus());
                            }
                            if (StringUtils.isBlank(picUrl.get())) {
                                picUrl.set(skcFromDb.getPictureUrl());
                            }
                        });
                    });

                    mannequinInfo.setPictureList(Lists.newArrayList(picUrl.get()));

                });
        return mannequinList;
    }


    private DisplayBookInfo getDisplayBookInfoFromDb(String id) {
        Preconditions.checkArgument(StringUtils.isNotBlank(id), "ID不能为空");
        LambdaQueryWrapper<DisplayBookInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisplayBookInfo::getId, id);
        queryWrapper.eq(DisplayBookInfo::getIsDeleted, IsDeleteEnum.NORMAL.getCode());
        DisplayBookInfo info = displayBookInfoMapper.selectOne(queryWrapper);
        if (info == null) {
            throw new ProductException("未找到该id的陈列册数据");
        }
        return info;
    }

    private DisplayBookFileMapping getDisplayBookFileMappingInfoFromDb(String id) {
        Preconditions.checkArgument(StringUtils.isNotBlank(id), "ID不能为空");
        LambdaQueryWrapper<DisplayBookFileMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisplayBookFileMapping::getId, id);
        queryWrapper.eq(DisplayBookFileMapping::getIsDeleted, IsDeleteEnum.NORMAL.getCode());
        DisplayBookFileMapping info = displayBookFileMappingMapper.selectOne(queryWrapper);
        if (info == null) {
            throw new ProductException("未找到该id的陈列册映射数据");
        }
        return info;
    }

    @Override
    public Boolean updateInfo(DisplayBookInfoUpdateReq requestData) {
        DisplayBookInfoUpdateReq.OperateType operateType = requestData.getOperateType();
        switch (operateType) {
            case UPDATE_DETAIL:
                return updateDetail(requestData);
            case UPDATE_TIME:
                return updateTime(requestData);
            case PUBLISH:
                return publish(requestData);
            default:
                throw new ProductException("未知的操作类型");
        }
    }

    @Override
    public Boolean delete(DisplayBookInfoUpdateReq requestData) {
        Preconditions.checkArgument(StringUtils.isNotBlank(requestData.getId()), "ID不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(requestData.getOperator()), "操作人不能为空");

        getDisplayBookInfoFromDb(requestData.getId());

        DisplayBookInfo updateInfo = new DisplayBookInfo();
        updateInfo.setId(requestData.getId());
        updateInfo.setOperator(requestData.getOperator());
        updateInfo.setUpdateTime(new Date());
        updateInfo.setIsDeleted(IsDeleteEnum.IS_DELETED.getCode());
        displayBookInfoMapper.updateById(updateInfo);
        return true;
    }

    @Override
    public String saveFileMappingInfo(DisplayBookFileMappingInfoAddReq req) {
        // 1、判断当前年份波段品牌是否已存在
        String assembleSeasonGoods = req.getYear() + req.getSeasonGoods();
        LambdaQueryWrapper<DisplayBookFileMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisplayBookFileMapping::getBrandCode, req.getBrandCode());
        queryWrapper.eq(DisplayBookFileMapping::getBrandName, req.getBrandName());
        queryWrapper.eq(DisplayBookFileMapping::getYear, req.getYear());
        queryWrapper.eq(DisplayBookFileMapping::getSeasonGoods, assembleSeasonGoods);
        queryWrapper.eq(DisplayBookFileMapping::getChannel, req.getChannel());
        queryWrapper.eq(DisplayBookFileMapping::getIsDeleted, IsDeleteEnum.NORMAL.getCode());
        DisplayBookFileMapping one = displayBookFileMappingMapper.selectOne(queryWrapper);
        if (one != null) {
            throw new ProductException("本年份本货季本渠道已存在陈列册映射，请刷新页面");
        }

        // 2、进行保存
        DisplayBookFileMapping displayBookFileMapping = new DisplayBookFileMapping();
        BeanUtils.copyProperties(req, displayBookFileMapping);
        String id = IdLeaf.getId(displayBookLeafTag);
        displayBookFileMapping.setId(id);
        displayBookFileMapping.setCreateTime(new Date());
        displayBookFileMapping.setUpdateTime(new Date());
        displayBookFileMapping.setSeasonGoods(assembleSeasonGoods);
        displayBookFileMappingMapper.insert(displayBookFileMapping);
        return id;
    }

    @Override
    public List<DisplayBookFileMappingListQueryResp> listFileMappingInfos(DisplayBookFileMappingListQueryReq req, Page page, String component) {
        // 获取品牌权限
        List<Long> brandIds = req.getBrandIds();
        List<Long> allowBrandIds = commonDataRuleService.getAllowBrandIdRule(component, brandIds);
        List<String> allowChannelList = commonDataRuleService.getAllowChannel(component);

        LambdaQueryWrapper<DisplayBookFileMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(allowBrandIds), DisplayBookFileMapping::getBrandCode, allowBrandIds);
        queryWrapper.in(CollectionUtils.isNotEmpty(req.getBrandNames()), DisplayBookFileMapping::getBrandName, req.getBrandNames());
        queryWrapper.in(CollectionUtils.isNotEmpty(req.getYears()), DisplayBookFileMapping::getYear, req.getYears());
        queryWrapper.in(CollectionUtils.isNotEmpty(allowChannelList), DisplayBookFileMapping::getChannel, allowChannelList);
        queryWrapper.eq(DisplayBookFileMapping::getIsDeleted, IsDeleteEnum.NORMAL.getCode());
        queryWrapper.orderByDesc(DisplayBookFileMapping::getUpdateTime);

        com.baomidou.mybatisplus.extension.plugins.pagination.Page<DisplayBookFileMapping> hPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page.getPageNo(), page.getPageSize());
        IPage<DisplayBookFileMapping> pages = displayBookFileMappingMapper.selectPage(hPage, queryWrapper);

        List<DisplayBookFileMappingListQueryResp> respList = Optional.ofNullable(pages.getRecords()).orElse(Lists.newArrayList())
                .stream().map(this::buildDisplayBookFileMappingListQueryResp).collect(Collectors.toList());
        PageInfo<DisplayBookFileMappingListQueryResp> pageInfo = new PageInfo(respList);
        page.setCount(pages.getTotal());
        page.setPages(pageInfo.getPages());
        return respList;
    }

    @Override
    public String analysisMannequinPictureFile(DisplayBookPictureMappingAddReq req) {
        String resultUrl = "";
        String operator = req.getOperator();
        String url = req.getUrl();
        Preconditions.checkArgument(StringUtils.isNotBlank(operator), "操作人不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(url), "url不能为空");
        DisplayBookFileMapping fileMappingInfoFromDb = getDisplayBookFileMappingInfoFromDb(req.getId());
        log.info("ID为[{}]的陈列册映射关系详情:{}", req.getId(), JSON.toJSONString(fileMappingInfoFromDb));
        // 货季:SS/AW
        String season = fileMappingInfoFromDb.getSeasonGoods().substring(4);
        // 判断是否可以重传
        String mannequinExcelUrl = fileMappingInfoFromDb.getMannequinExcelUrl();
        log.info("当前人台图地址: {}", mannequinExcelUrl);
        if (Objects.equals(mannequinExcelUrl, url)) {
            log.info("当前数据库的人台图地址与导入的地址一致，认为重复消费，不处理");
            return resultUrl;
        }

        // 波段-货季映射关系
        HashMap<String, String> bandNameSeasonConfigMap = JSON.parseObject(bandNameSeasonConfig, HashMap.class);
        // 波段-ID映射
        Map<String, Long> bandName2BandIdMap = JSONObject.parseArray(bandsStr, AttrResp.class).stream()
                .collect(Collectors.toMap(AttrResp::getAttribname, AttrResp::getId, (existing, replacement) -> existing));

        // 下载七牛云的文件到本地，filePath为本地文件路径
        String filePath = FileParseUtil.downLoadExcel(url);
        log.info("下载七牛云文件到本地: {}", filePath);
//        String filePath = "C:\\Users\\<USER>\\Desktop\\wanyu1.xlsx";

        // 具体解析实现逻辑放在listener
        try {
            DisplayBookMannequinPictureListener listener = new DisplayBookMannequinPictureListener(bandNameSeasonConfigMap, season);
            EasyExcel.read(filePath, DisplayBookMannequinPictureEntity.class, listener).sheet().doRead();
            List<DisplayBookMannequinPictureEntity> importDataList = listener.getImportDataList();
            List<DisplayBookMannequinPictureEntity> errorList = listener.getErrorList();

            log.info("导入的总数: {}", importDataList.size());
            log.info("初次解析出错的数量: {}", errorList.size());

            // 解析文件完成后，如果有任意一条失败的记录，就中断流程
            if (CollectionUtils.isNotEmpty(errorList)) {
                log.info("存在出错的数据，提前中断");
                resultUrl = interruptMannequinPicture(errorList);
                return resultUrl;
            }
            if (CollectionUtils.isEmpty(importDataList)) {
                log.info("导入的数据是空的，无法处理");
                return resultUrl;
            }

            // 处理文件解析时无法处理的逻辑
            // 待入库的数据
            List<DisplayBookPictureMapping> dbList = Lists.newArrayList();

            log.info("开始分组处理");
            doProcessMannequinPicture(req, importDataList, errorList, bandName2BandIdMap, fileMappingInfoFromDb, operator, dbList);
            log.info("完成分组处理");

            // 如果有出错的提前中断
            if (CollectionUtils.isNotEmpty(errorList)) {
                log.info("存在出错的数据，提前中断");
                resultUrl = interruptMannequinPicture(errorList);
                return resultUrl;
            }
            // 把之前的数据全部逻辑删除
            log.info("开始把之前存在的人台图图映射数据全部逻辑删除");
            req.setType(DisplayBookPictureEnum.PICTURE_MANNEQUIN);
            updateData(req);

            // 没有出错的数据，全量数据分批入库
            save2Db(req, dbList, fileMappingInfoFromDb, operator);
        } catch (Exception e) {
            if (e instanceof ProductException) {
                DisplayBookMannequinPictureEntity entity = new DisplayBookMannequinPictureEntity();
                entity.setErrorMsg(((ProductException) e).getBizMessage());
                List<DisplayBookMannequinPictureEntity> errorList = Lists.newArrayList(entity);
                resultUrl = interruptMannequinPicture(errorList);
            } else {
                log.error("处理人台图异常:" + e.getMessage());
                DisplayBookMannequinPictureEntity entity = new DisplayBookMannequinPictureEntity();
                entity.setErrorMsg("未知异常，请联系技术人员");
                List<DisplayBookMannequinPictureEntity> errorList = Lists.newArrayList(entity);
                resultUrl = interruptMannequinPicture(errorList);
            }
        } finally {
            if (StringUtils.isNotBlank(filePath)) {
                try {
                    Files.deleteIfExists(Paths.get(filePath));
                } catch (IOException e) {
                    log.error("删除导入时的临时文件异常",e);
                }
            }
        }
        return resultUrl;
    }

    private void doProcessMannequinPicture(DisplayBookPictureMappingAddReq req, List<DisplayBookMannequinPictureEntity> importDataList, List<DisplayBookMannequinPictureEntity> errorList, Map<String, Long> bandName2BandIdMap, DisplayBookFileMapping fileMappingInfoFromDb, String operator, List<DisplayBookPictureMapping> dbList) {
        // 分组处理，先根据照片ID分组，然后根据波段分组，再根据分组编号分组
        Map<String, Map<String, Map<Integer, List<DisplayBookMannequinPictureEntity>>>> pictureIdGroup =
                Optional.ofNullable(importDataList).orElseGet(Lists::newArrayList).stream().collect(
                        Collectors.groupingBy(DisplayBookMannequinPictureEntity::getPictureId,
                                Collectors.groupingBy(DisplayBookMannequinPictureEntity::getBandName,
                                        Collectors.groupingBy(DisplayBookMannequinPictureEntity::getGroupNumber,
                                                Collectors.toList()))));
        // 照片ID分组数据
        pictureIdGroup.forEach((pictureId, bandNameGroup) -> {
            // 调用远程服务获取图片,失败则记录所有数据去error
//            String picUrl = null;
            Map<String, String> partitionId2UrlMap = commonDataRuleService.listFileUrl(Lists.newArrayList(pictureId));
            String picUrl = partitionId2UrlMap.get(pictureId);
            if (StringUtils.isBlank(picUrl)) {
                bandNameGroup.forEach((bandName, groupNumberGroup) -> {
                    groupNumberGroup.forEach((groupNumber, skcGroup) -> {
                        skcGroup.forEach(entity -> {
                            entity.setErrorMsg("图片获取有误，请检查图片ID");
                            errorList.add(entity);
                        });
                    });
                });
                return;
            }

            // 波段分组数据
            bandNameGroup.forEach((bandName, groupNumberGroup) -> {
                // 编号分组数据
                groupNumberGroup.forEach((groupNumber, skcGroup) -> {
                    if (skcGroup.size() > 9) {
                        // SKC级别
                        skcGroup.forEach(entity -> {
                            entity.setErrorMsg("单个人台编号的人台款色号不能超过9个");
                            errorList.add(entity);
                        });
                        // 处理完该编号分组下所有SKC数据，提前终止
                        return;
                    }
                    // 校验SKC信息
                    List<String> skcCodeList = skcGroup.stream().map(DisplayBookMannequinPictureEntity::getSkcCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                    // 导入没有SKC的时候直接不存储SKC信息
                    if (CollectionUtils.isEmpty(skcCodeList)) {
                        skcGroup.forEach(entity -> {
                            DisplayBookPictureEntity build = DisplayBookPictureEntity.builder()
                                    .pictureId(pictureId).pictureType(req.getType().getCode())
                                    .bandId(bandName2BandIdMap.get(bandName)).bandName(bandName)
                                    .groupNumber(groupNumber).skcCode(entity.getSkcCode()).build();
                            DisplayBookPictureMapping mapping = buildPictureMapping(build, fileMappingInfoFromDb, operator, null, null);
                            dbList.add(mapping);
                        });
                        return;
                    }
                    // 导入有SKC的时候需要填充数据
                    QueryGoodsReq context = new QueryGoodsReq();
                    context.setSkcIds(skcCodeList);
                    Page page = new Page(1, 40);
                    String component = null;
                    List<ProductSkcResp> productSkcResps = iProductService.searchGoodsSkc(context, page, component);
                    if (CollectionUtils.isEmpty(productSkcResps)) {
                        // SKC异常，记录到数据库
                        skcGroup.forEach(entity -> {
                            DisplayBookPictureEntity build = DisplayBookPictureEntity.builder()
                                    .pictureId(pictureId).pictureType(req.getType().getCode()).picUrl(picUrl)
                                    .bandId(bandName2BandIdMap.get(bandName)).bandName(bandName)
                                    .groupNumber(groupNumber).skcCode(entity.getSkcCode()).build();
                            DisplayBookPictureMapping mapping = buildPictureMapping(build, fileMappingInfoFromDb, operator, null, "获取商品SKC信息失败");
                            dbList.add(mapping);
                        });
                    } else {
                        Map<String, ProductSkcResp> skcCode2SkcMap = productSkcResps.stream().collect(Collectors.toMap(ProductSkcResp::getId, skc -> skc, (existing, replacement) -> existing));
                        skcGroup.forEach(entity -> {
                            ProductSkcResp productSkcResp = skcCode2SkcMap.get(entity.getSkcCode());
                            String errorMsg = null;
                            if (productSkcResp == null) {
                                errorMsg = "SKC信息不存在";
                            }
                            DisplayBookPictureEntity build = DisplayBookPictureEntity.builder()
                                    .pictureId(pictureId).pictureType(req.getType().getCode()).picUrl(picUrl)
                                    .bandId(bandName2BandIdMap.get(bandName)).bandName(bandName)
                                    .groupNumber(groupNumber).skcCode(entity.getSkcCode()).build();
                            DisplayBookPictureMapping mapping = buildPictureMapping(build, fileMappingInfoFromDb, operator, productSkcResp, errorMsg);
                            dbList.add(mapping);
                        });
                    }
                });
            });
        });
    }

    @Override
    public String analysisHangerPictureFile(DisplayBookPictureMappingAddReq req) {
        String resultUrl = "";
        String operator = req.getOperator();
        String url = req.getUrl();
        Preconditions.checkArgument(StringUtils.isNotBlank(operator), "操作人不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(url), "url不能为空");
        DisplayBookFileMapping fileMappingInfoFromDb = getDisplayBookFileMappingInfoFromDb(req.getId());
        log.info("ID为[{}]的陈列册映射关系详情:{}", req.getId(), JSON.toJSONString(fileMappingInfoFromDb));
        // 货季:SS/AW
        String season = fileMappingInfoFromDb.getSeasonGoods().substring(4);
        // 检查是否可以重传
        String hangerExcelUrl = fileMappingInfoFromDb.getHangerExcelUrl();
        log.info("当前货杆图地址: {}", hangerExcelUrl);
        if (Objects.equals(hangerExcelUrl, url)) {
            log.info("当前数据库的货杆图地址与导入的地址一致，认为重复消费，不处理");
            return resultUrl;
        }

        // 波段-货季映射关系
        HashMap<String, String> bandNameSeasonConfigMap = JSON.parseObject(bandNameSeasonConfig, HashMap.class);
        // 波段-ID映射
        Map<String, Long> bandName2BandIdMap = JSONObject.parseArray(bandsStr, AttrResp.class).stream()
                .collect(Collectors.toMap(AttrResp::getAttribname, AttrResp::getId, (existing, replacement) -> existing));

        // 下载七牛云的文件到本地，filePath为本地文件路径
        String filePath = FileParseUtil.downLoadExcel(url);
        log.info("下载七牛云文件到本地: {}", filePath);

        try {
            // 具体解析实现逻辑放在listener
            DisplayBookHangerPictureListener listener = new DisplayBookHangerPictureListener(bandNameSeasonConfigMap, season);
            EasyExcel.read(filePath, DisplayBookHangerPictureEntity.class, listener).sheet().doRead();
            List<DisplayBookHangerPictureEntity> importDataList = listener.getImportDataList();
            List<DisplayBookHangerPictureEntity> errorList = listener.getErrorList();

            log.info("导入的总数: {}", importDataList.size());
            log.info("初次解析出错的数量: {}", errorList.size());

            // 解析文件完成后，如果有任意一条失败的记录，就中断流程
            if (CollectionUtils.isNotEmpty(errorList)) {
                log.info("存在出错的数据，提前中断");
                resultUrl = interruptHangerPicture(errorList);
                return resultUrl;
            }
            if (CollectionUtils.isEmpty(importDataList)) {
                log.info("导入的数据是空的，无法处理");
                return resultUrl;
            }

            // 待入库的数据
            List<DisplayBookPictureMapping> dbList = Lists.newArrayList();
            // 后续处理文件解析时无法处理的逻辑
            log.info("开始分组处理");
            doProcessHangerPicture(req, importDataList, errorList, bandName2BandIdMap, fileMappingInfoFromDb, operator, dbList);
            log.info("完成分组处理");

            // 如果有出错的提前中断
            if (CollectionUtils.isNotEmpty(errorList)) {
                log.info("存在出错的数据，提前中断");
                resultUrl = interruptHangerPicture(errorList);
                return resultUrl;
            }

            // 把之前的数据全部逻辑删除
            log.info("开始把之前存在的货杆图映射数据全部逻辑删除");
            req.setType(DisplayBookPictureEnum.PICTURE_HANGER);
            updateData(req);

            // 没有出错的数据，全量数据分批入库
            save2Db(req, dbList, fileMappingInfoFromDb, operator);
        } catch (Exception e) {
            if (e instanceof ProductException) {
                DisplayBookHangerPictureEntity entity = new DisplayBookHangerPictureEntity();
                entity.setErrorMsg(((ProductException) e).getBizMessage());
                List<DisplayBookHangerPictureEntity> errorList = Lists.newArrayList(entity);
                resultUrl = interruptHangerPicture(errorList);
            } else {
                log.error("处理货杆图异常:" + e.getMessage());
                DisplayBookHangerPictureEntity entity = new DisplayBookHangerPictureEntity();
                entity.setErrorMsg("未知异常，请联系技术人员");
                List<DisplayBookHangerPictureEntity> errorList = Lists.newArrayList(entity);
                resultUrl = interruptHangerPicture(errorList);
            }
        } finally {
            if (StringUtils.isNotBlank(filePath)) {
                try {
                    Files.deleteIfExists(Paths.get(filePath));
                } catch (IOException e) {
                    log.error("删除导入时的临时文件异常 ",e);
                }
            }
        }

        return resultUrl;
    }

    @Override
    public List<String> getChannel(String component) {
        return commonDataRuleService.getAllowChannel(component);
    }

    @Override
    public Boolean checkFileExist(DisplayBookInfoAddCheckReq requestData) {
        QueryWrapper<DisplayBookFileMapping> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("BRAND_CODE", requestData.getBrandCode());
        queryWrapper.eq("BRAND_NAME", requestData.getBrandName());
        queryWrapper.eq("YEAR", requestData.getYear());
        queryWrapper.eq("SEASON_GOODS", requestData.getSeasonGoods());
        queryWrapper.eq("CHANNEL", requestData.getChannel());
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        DisplayBookFileMapping info = displayBookFileMappingMapper.selectOne(queryWrapper);
        // url地址都为空，则认为没有关联数据
        if (Objects.isNull(info)) {
            return false;
        }
        if (StringUtils.isBlank(info.getHangerExcelUrl()) && StringUtils.isBlank(info.getMannequinExcelUrl())) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean checkInfoExist(String id) {
        DisplayBookFileMapping fileMappingInfoFromDb = getDisplayBookFileMappingInfoFromDb(id);
        log.info("ID为[{}]的陈列册映射关系详情:{}", id, JSON.toJSONString(fileMappingInfoFromDb));
        // 货季:SS/AW
        String season = fileMappingInfoFromDb.getSeasonGoods().substring(4);
        try {
            checkRepeatImport(fileMappingInfoFromDb, season);
        } catch (Exception e) {
            log.error("陈列册映射-上传前检验异常", e);
            return true;
        }
        return false;
    }

    /**
     * 检查是否可以上传：
     * 1、如果 同品牌-年份-货季-渠道 的陈列册存在，则不可以上传
     * @param fileMappingInfoFromDb
     * @param season
     */
    private void checkRepeatImport(DisplayBookFileMapping fileMappingInfoFromDb, String season) {
        // 货季-波段映射
        HashMap<String, List<String>> seasonBandNameConfigMap = JSON.parseObject(seasonBandNameConfig, HashMap.class);

        // 判断 品牌-年份-货季-渠道 是否有已经生成的陈列册，如果有了不可以再次上传，如果没有则可以覆盖。
        LambdaQueryWrapper<DisplayBookInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DisplayBookInfo::getBandName, seasonBandNameConfigMap.get(season));
        queryWrapper.eq(DisplayBookInfo::getBrandCode, fileMappingInfoFromDb.getBrandCode());
        queryWrapper.eq(DisplayBookInfo::getBrandName, fileMappingInfoFromDb.getBrandName());
        queryWrapper.eq(DisplayBookInfo::getYear, fileMappingInfoFromDb.getYear());
        queryWrapper.eq(DisplayBookInfo::getChannel, fileMappingInfoFromDb.getChannel());
        queryWrapper.eq(DisplayBookInfo::getIsDeleted, IsDeleteEnum.NORMAL.getCode());
        queryWrapper.last("AND ROWNUM <= 1");

        DisplayBookInfo displayBookInfo = displayBookInfoMapper.selectOne(queryWrapper);
        log.info("同品牌-年份-货季-渠道,陈列册查询结果:{}", JSON.toJSONString(displayBookInfo));
        // 如果没有陈列册，允许上传
        if (Objects.nonNull(displayBookInfo)) {
            throw new ProductException("本品牌本货季已经存在陈列册，无法再次上传。");
        }
    }

    private void save2Db(DisplayBookPictureMappingAddReq req, List<DisplayBookPictureMapping> dbList,
                         DisplayBookFileMapping fileMappingInfoFromDb, String operator) {
        if (CollectionUtils.isNotEmpty(dbList)) {
            DisplayBookFileMapping updateInfo = new DisplayBookFileMapping();
            updateInfo.setId(fileMappingInfoFromDb.getId());
            updateInfo.setOperator(operator);
            updateInfo.setUpdateTime(new Date());
            DisplayBookPictureEnum type = req.getType();
            if (DisplayBookPictureEnum.PICTURE_MANNEQUIN == type) {
                updateInfo.setMannequinExcelUrl(req.getUrl());
            } else if (DisplayBookPictureEnum.PICTURE_HANGER == type) {
                updateInfo.setHangerExcelUrl(req.getUrl());
            }

            // 将dbList分成100条sql一组，然后批量插入一次
            List<List<DisplayBookPictureMapping>> splitList = Lists.partition(dbList, 100);
            log.info("待数据处理成功后更新的陈列册映射excel地址: {}", req.getUrl());
            template.execute(action->{
                try {
                    splitList.forEach(list -> displayBookPictureMappingMapper.insertBatch(list));
                    displayBookFileMappingMapper.updateById(updateInfo);
                    return true;
                } catch (Exception e) {
                    log.error("新增数据入库异常", e);
                    throw new ProductException("新增数据入库异常，请稍后重试");
                }
            });
        }
    }

    private void updateData(DisplayBookPictureMappingAddReq req) {
        LambdaQueryWrapper<DisplayBookPictureMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DisplayBookPictureMapping::getId);
        queryWrapper.eq(DisplayBookPictureMapping::getDisplayBookFileMappingId, req.getId());
        queryWrapper.eq(DisplayBookPictureMapping::getIsDeleted, IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq(DisplayBookPictureMapping::getPictureType, req.getType().getCode());
        log.info("逻辑删除要覆盖的数据，查询数据入参:{}", JSON.toJSONString(queryWrapper));
        Integer count = displayBookPictureMappingMapper.selectCount(queryWrapper);
        log.info("逻辑删除要覆盖的数据，查询到总数量:{}", count);
        if (count == 0) {
            return;
        }
        // 根据 count 计算翻页数量
        int pageSize = 100;
        int pages = count / pageSize;
        if (count % pageSize > 0) {
            pages += 1;
        }

        // 循环取出数据
        List<String> idList = Lists.newArrayList();
        for (int pageNum = 1; pageNum <= pages; pageNum++) {
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<DisplayBookPictureMapping> myPages =
                    new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize, false);
            IPage<DisplayBookPictureMapping> pagesForUpdate = displayBookPictureMappingMapper.selectPage(myPages, queryWrapper);
            idList.addAll(
                Optional.ofNullable(pagesForUpdate.getRecords())
                        .orElse(Lists.newArrayList())
                        .stream().map(DisplayBookPictureMapping::getId).distinct().collect(Collectors.toList())
            );
        }
        // 循环提交数据
        if (CollectionUtils.isNotEmpty(idList)) {
            log.info("逻辑删除要覆盖的数据，批量提交数据");
            DisplayBookPictureMapping updateEntity = new DisplayBookPictureMapping();
            updateEntity.setIsDeleted(IsDeleteEnum.IS_DELETED.getCode());
            updateEntity.setUpdateTime(new Date());
            updateEntity.setOperator(req.getOperator());
            List<List<String>> idListPartition = Lists.partition(idList, pageSize);
            // 事务控制
            template.execute(a->{
                try {
                    for (List<String> ids : idListPartition) {
                        LambdaQueryWrapper<DisplayBookPictureMapping> updateQueryWrapper = new LambdaQueryWrapper<>();
                        updateQueryWrapper.in(DisplayBookPictureMapping::getId, ids);
                        updateQueryWrapper.eq(DisplayBookPictureMapping::getIsDeleted, IsDeleteEnum.NORMAL.getCode());
                        int updateCount = displayBookPictureMappingMapper.update(updateEntity, updateQueryWrapper);
                        log.info("逻辑删除要覆盖的数据，当前处理成功数量:{}", updateCount);
                    }
                    return true;
                } catch (Exception e) {
                    log.error("覆盖的数据处理异常", e);
                    throw new ProductException("覆盖的数据处理异常，请稍后重试");
                }
            });
        }


    }

    private void doProcessHangerPicture(DisplayBookPictureMappingAddReq req, List<DisplayBookHangerPictureEntity> importDataList,
                                        List<DisplayBookHangerPictureEntity> errorList, Map<String, Long> bandName2BandIdMap,
                                        DisplayBookFileMapping fileMappingInfoFromDb, String operator, List<DisplayBookPictureMapping> dbList) {
        // 分组处理，先根据波段分组，然后根据货杆分组，再根据货杆图ID分组
        Map<String, Map<Integer, Map<String, List<DisplayBookHangerPictureEntity>>>> bandNameGroup =
                Optional.ofNullable(importDataList).orElseGet(Lists::newArrayList).stream().collect(
                        Collectors.groupingBy(DisplayBookHangerPictureEntity::getBandName,
                                Collectors.groupingBy(DisplayBookHangerPictureEntity::getGroupNumber,
                                        Collectors.groupingBy(DisplayBookHangerPictureEntity::getPictureId,
                                                Collectors.toList()))));
        // 波段分组数据
        bandNameGroup.forEach((bandName, groupNumberGroup) -> {

            // 货杆分组数据
            groupNumberGroup.forEach((groupNumber, pictureGroup) -> {
                if (pictureGroup.size() > 2) {
                    // 图片分组数据
                    pictureGroup.forEach((pictureId, skcGroup) -> {
                        // SKC级别
                        skcGroup.forEach(entity -> {
                            entity.setErrorMsg("同一个货干分组内不可超过2个货杆图ID");
                        });
                        errorList.addAll(skcGroup);
                    });
                    // 处理完该图片分组下的所有SKC数据，提前终止
                    return;
                }

                // 图片分组数据
                pictureGroup.forEach((pictureId, skcGroup) -> {
                    // 调用远程服务获取图片,失败则记录所有数据去error
                    Map<String, String> partitionId2UrlMap = commonDataRuleService.listFileUrl(Lists.newArrayList(pictureId));
                    String picUrl = partitionId2UrlMap.get(pictureId);
                    if (StringUtils.isBlank(picUrl)) {
                        skcGroup.forEach(entity -> {
                            entity.setErrorMsg("图片获取有误，请检查图片ID");
                        });
                        errorList.addAll(skcGroup);
                        // 处理完该图片分组下的所有SKC数据，提前终止
                        return;
                    }

                    if (skcGroup.size() > 30) {
                        // SKC级别
                        skcGroup.forEach(entity -> {
                            entity.setErrorMsg("单个货杆图ID的货杆款色号不能超过30个");
                        });
                        errorList.addAll(skcGroup);
                        // 处理完该图片分组下的所有SKC数据，提前终止
                        return;
                    }
                    // 校验SKC信息
                    List<String> skcCodeList = skcGroup.stream().map(DisplayBookHangerPictureEntity::getSkcCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                    // 上传没有SKC，直接组装数据
                    if (CollectionUtils.isEmpty(skcCodeList)) {
                        skcGroup.forEach(entity -> {
                            DisplayBookPictureEntity build = DisplayBookPictureEntity.builder()
                                    .pictureId(pictureId).pictureType(req.getType().getCode()).picUrl(picUrl)
                                    .bandId(bandName2BandIdMap.get(bandName)).bandName(bandName)
                                    .groupNumber(groupNumber).skcCode(entity.getSkcCode()).build();
                            DisplayBookPictureMapping mapping = buildPictureMapping(build, fileMappingInfoFromDb, operator, null, null);
                            dbList.add(mapping);
                        });
                        return;
                    }
                    // 有SKC，需要组装SKC信息
                    QueryGoodsReq context = new QueryGoodsReq();
                    context.setSkcIds(skcCodeList);
                    Page page = new Page(1, 40);
                    String component = null;
                    log.info("获取SKC信息的入参: {}", JSON.toJSONString(context));
                    List<ProductSkcResp> productSkcResps = iProductService.searchGoodsSkc(context, page, component);
                    if (CollectionUtils.isEmpty(productSkcResps)) {
                        // SKC异常，记录到数据库
                        skcGroup.forEach(entity -> {
                            DisplayBookPictureEntity build = DisplayBookPictureEntity.builder()
                                    .pictureId(pictureId).pictureType(req.getType().getCode()).picUrl(picUrl)
                                    .bandId(bandName2BandIdMap.get(bandName)).bandName(bandName)
                                    .groupNumber(groupNumber).skcCode(entity.getSkcCode()).build();
                            DisplayBookPictureMapping mapping = buildPictureMapping(build, fileMappingInfoFromDb, operator, null, "获取商品SKC信息失败");
                            dbList.add(mapping);
                        });
                    } else {
                        Map<String, ProductSkcResp> skcCode2SkcMap = productSkcResps.stream().collect(Collectors.toMap(ProductSkcResp::getId, skc -> skc, (existing, replacement) -> existing));
                        skcGroup.forEach(entity -> {
                            ProductSkcResp productSkcResp = skcCode2SkcMap.get(entity.getSkcCode());
                            String errorMsg = null;
                            if (productSkcResp == null) {
                                errorMsg = "SKC信息不存在";
                            }
                            DisplayBookPictureEntity build = DisplayBookPictureEntity.builder()
                                    .pictureId(pictureId).pictureType(req.getType().getCode()).picUrl(picUrl)
                                    .bandId(bandName2BandIdMap.get(bandName)).bandName(bandName)
                                    .groupNumber(groupNumber).skcCode(entity.getSkcCode()).build();
                            DisplayBookPictureMapping mapping = buildPictureMapping(build, fileMappingInfoFromDb, operator, productSkcResp, errorMsg);
                            dbList.add(mapping);
                        });
                    }
                });
            });
        });
    }

    /**
     * 组装图片信息
     *
     * @param entity                临时实体
     * @param fileMappingInfoFromDb 文件映射记录数据库信息
     * @param operator              操作人
     * @param skcResp               skc详情
     * @return 新的图片记录实体
     */
    private DisplayBookPictureMapping buildPictureMapping(DisplayBookPictureEntity entity,
                                                          DisplayBookFileMapping fileMappingInfoFromDb,
                                                          String operator, ProductSkcResp skcResp, String errorMsg) {
        DisplayBookPictureMapping mapping = new DisplayBookPictureMapping();
        mapping.setId(IdLeaf.getId(displayBookLeafTag));
        Date now = new Date();
        mapping.setCreateTime(now);
        mapping.setUpdateTime(now);
        mapping.setIsDeleted(IsDeleteEnum.NORMAL.getCode());
        mapping.setDisplayBookFileMappingId(fileMappingInfoFromDb.getId());
        mapping.setBrandCode(fileMappingInfoFromDb.getBrandCode());
        mapping.setBrandName(fileMappingInfoFromDb.getBrandName());
        mapping.setYear(fileMappingInfoFromDb.getYear());
        mapping.setSeasonGoods(fileMappingInfoFromDb.getSeasonGoods());
        mapping.setChannel(fileMappingInfoFromDb.getChannel());
        mapping.setOperator(operator);
        mapping.setPictureType(entity.getPictureType());
        mapping.setPictureId(entity.getPictureId());
        mapping.setPictureUrl(entity.getPicUrl());
        mapping.setBandId(entity.getBandId());
        mapping.setBandName(entity.getBandName());
        mapping.setGroupNumber(entity.getGroupNumber());
        mapping.setSkcCode(entity.getSkcCode());
        mapping.setStatus(0);
        if (Objects.nonNull(skcResp)) {
            mapping.setSkcImgUrl(skcResp.getImgurl());
            mapping.setSkcSmallClassName(skcResp.getM_small_category());
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            mapping.setStatus(1);
            mapping.setErrorMsg(errorMsg);
        }
        return mapping;
    }

    private String interruptMannequinPicture(List<DisplayBookMannequinPictureEntity> errorList) {
        String fileName = "人台图导入异常excel-" + System.currentTimeMillis() + ".xlsx";
        File file = new File(fileName);
        EasyExcelUtil.write(fileName, DisplayBookMannequinPictureEntity.class, () -> errorList);
        // 数据导入到七牛云
        String exportUrl = qiniuUtil.upload(file.getPath(), fileName);
        log.info("人台图导入异常存储的文件路径exportUrl: {}",exportUrl);
        if (StringUtils.isNotBlank(fileName)) {
            try {
                file.delete();
            } catch (Exception e) {
                log.error("删除人台图异常表格出错",e);
            }
        }
        return exportUrl;
    }


    private String interruptHangerPicture(List<DisplayBookHangerPictureEntity> errorList) {
        String fileName = "货杆图导入异常excel-" + System.currentTimeMillis() + ".xlsx";
        File file = new File(fileName);
        EasyExcelUtil.write(fileName, DisplayBookHangerPictureEntity.class, () -> errorList);
        // 数据导入到七牛云
        String exportUrl = qiniuUtil.upload(file.getPath(), fileName);
        log.info("货杆图导入异常存储的文件路径exportUrl: {}",exportUrl);
        if (StringUtils.isNotBlank(fileName)) {
            try {
                file.delete();
            } catch (Exception e) {
                log.error("删除货杆图异常表格出错",e);
            }
        }
        return exportUrl;
    }


    private Boolean publish(DisplayBookInfoUpdateReq requestData) {
        DisplayBookInfo updateInfo = new DisplayBookInfo();
        updateInfo.setId(requestData.getId());
        updateInfo.setOperator(requestData.getOperator());
        Date now = new Date();
        updateInfo.setUpdateTime(now);
        updateInfo.setDisplayTime(now);
        displayBookInfoMapper.updateById(updateInfo);
        return true;
    }

    private Boolean updateTime(DisplayBookInfoUpdateReq requestData) {
        if (Objects.isNull(requestData.getDisplayTime())) {
            throw new ProductException("时间不能为空");
        }
        if (new Date().after(requestData.getDisplayTime())) {
            throw new ProductException("只能设置未来时间");
        }

        DisplayBookInfo info = getDisplayBookInfoFromDb(requestData.getId());
        if (info.getDisplayTime() != null &&
                (info.getDisplayTime().before(new Date()) || info.getDisplayTime().equals(new Date()))) {
            throw new ProductException("已展示的不允许修改");
        }

        DisplayBookInfo updateInfo = new DisplayBookInfo();
        updateInfo.setId(requestData.getId());
        updateInfo.setOperator(requestData.getOperator());
        updateInfo.setUpdateTime(new Date());
        updateInfo.setDisplayTime(requestData.getDisplayTime());
        displayBookInfoMapper.updateById(updateInfo);
        return true;
    }

    /**
     * 编辑详情
     */
    private Boolean updateDetail(DisplayBookInfoUpdateReq requestData) {
        Preconditions.checkArgument(StringUtils.isNotBlank(requestData.getClobJson()), "json详情不能为空");

        getDisplayBookInfoFromDb(requestData.getId());

        DisplayBookInfo updateInfo = new DisplayBookInfo();
        updateInfo.setId(requestData.getId());
        updateInfo.setOperator(requestData.getOperator());
        updateInfo.setUpdateTime(new Date());
        updateInfo.setClobJson(requestData.getClobJson());
        if (StringUtils.isNotBlank(requestData.getBookName())) {
            updateInfo.setBookName(requestData.getBookName());
        }

        displayBookInfoMapper.updateById(updateInfo);
        return true;
    }

    private DisplayBookListQueryResp buildDisplayBookListQueryResp(DisplayBookInfo v) {
        DisplayBookListQueryResp infoResp = new DisplayBookListQueryResp();
        BeanUtils.copyProperties(v, infoResp);

        // 未设置时间：FAB已经保存，展示时间为空；
        // 待展示：FAB已保存，展示时间有值，未到展示时间；
        // 已展示：FAB已保存，展示时间有值，已到展示时间
        if (v.getDisplayTime() == null) {
            infoResp.setStatus(0);
        } else if (v.getDisplayTime().before(new Date()) || v.getDisplayTime().equals(new Date())) {
            infoResp.setStatus(2);
        } else {
            infoResp.setStatus(1);
        }

        // 时间处理
        infoResp.setUpdateTime(DateUtil.parseDate(v.getUpdateTime(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMM));
        if (v.getDisplayTime() != null) {
            infoResp.setDisplayTime(DateUtil.parseDate(v.getDisplayTime(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMM));
        }

        // 如果是陈列指引(type=2),用月份覆盖波段名称
        if (Objects.equals(v.getType(), 2)) {
            infoResp.setBandName(String.valueOf(v.getBandNameMonth()));
        }

        return infoResp;
    }

    private DisplayBookListForPosQueryResp buildDisplayBookListForPosQueryResp(DisplayBookInfo v) {
        DisplayBookListForPosQueryResp infoResp = new DisplayBookListForPosQueryResp();
        BeanUtils.copyProperties(v, infoResp);

        // 时间在未来则未解锁
        if (Objects.nonNull(v.getDisplayTime()) && v.getDisplayTime().before(new Date())) {
            infoResp.setStatus(1);
        } else {
            infoResp.setStatus(0);
            if (Objects.nonNull(v.getDisplayTime())) {
                infoResp.setDay(DateUtil.diffDate(v.getDisplayTime(), new Date()));
            }
        }

        // 时间处理
        infoResp.setUpdateTime(DateUtil.parseDate(v.getUpdateTime(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMM));
        if (Objects.nonNull(v.getDisplayTime())) {
            infoResp.setDisplayTime(DateUtil.parseDate(v.getDisplayTime(), DateUtil.DATEFORMATE_YYYY_MM_DD_HHMM));
        }
        // 如果是陈列指引(type=2),用月份覆盖波段名称
        if (Objects.equals(v.getType(), 2)) {
            infoResp.setBandName(String.valueOf(v.getBandNameMonth()));
        }
        return infoResp;
    }

    private DisplayBookFileMappingListQueryResp buildDisplayBookFileMappingListQueryResp(DisplayBookFileMapping v) {
        DisplayBookFileMappingListQueryResp infoResp = new DisplayBookFileMappingListQueryResp();
        BeanUtils.copyProperties(v, infoResp);
        return infoResp;
    }

}
