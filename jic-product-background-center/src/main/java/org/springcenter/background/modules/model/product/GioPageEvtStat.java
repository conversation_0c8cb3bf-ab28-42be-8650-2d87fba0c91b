package org.springcenter.background.modules.model.product;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: yuanxiaozhong
 * @Date: 2023-02-08 16:43:24
 * @Description: gio页面浏览事件数据
 */
@TableName("GIO_PAGE_EVT_STAT")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="GioPageEvtStat对象", description="gio页面浏览事件数据")
public class GioPageEvtStat implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "访问用户ID，BOX导购为工号、小程序为unionid")
    @TableField("VISIT_USER_ID")
    @ExcelProperty({"visitUserId"})
    private String visitUserId;
    @ApiModelProperty(value = "登录用户")
    @TableField("SESSION_ID")
    @ExcelProperty({"sessionId"})
    private String sessionId;
    @ApiModelProperty(value = "访问所属平台，可能值为 iOS / Android / Web 等")
    @TableField("PLATFORM")
    @ExcelProperty({"platform"})
    private String platform;
    @ApiModelProperty(value = "应用分类；")
    @TableField(value = "\"DOMAIN\"")
    @ExcelProperty({"domain"})
    private String domain;
    @ApiModelProperty(value = "访问的路径")
    @TableField("PAGE")
    @ExcelProperty({"page"})
    private String page;
    @ApiModelProperty(value = "当前网站页面URL中的查询参数。")
    @TableField("QUERY_PARAMETERS")
    @ExcelProperty({"queryParameters"})
    private String queryParameters;
    @ApiModelProperty(value = "当前页面浏览的引荐来源。")
    @TableField("REFERRER")
    @ExcelProperty({"referrer"})
    private String referrer;
    @ApiModelProperty(value = "移动应用的来源页面。")
    @TableField("REFERRER_PAGE")
    @ExcelProperty({"referrerPage"})
    private String referrerPage;
    @ApiModelProperty(value = "页面标题")
    @TableField("TITLE")
    @ExcelProperty({"title"})
    private String title;
    @ApiModelProperty(value = "请求在用户端发生的时间戳。")
    @TableField(value = "\"TIME\"")
    @ExcelProperty({"time"})
    private Long time;
    @ApiModelProperty(value = "请求在SDK发送的时间戳。")
    @TableField("SEND_TIME")
    @ExcelProperty({"sendTime"})
    private Long sendTime;
    @ApiModelProperty(value = "页面访问时间")
    @TableField("PAGE_TIME")
    @ExcelProperty({"pageTime"})
    private Long pageTime;
    @ApiModelProperty(value = "登录用户ID，推荐使用那些不能定位到个人的ID信息，通常为企业内部使用的CRM ID。")
    @TableField("LOGIN_USER_ID")
    @ExcelProperty({"loginUserId"})
    private String loginUserId;
    @ApiModelProperty(value = "页面分组")
    @TableField("PAGE_GROUP")
    @ExcelProperty({"pageGroup"})
    private String pageGroup;
    @TableField("PAGE_REQUEST_ID")
    @ExcelProperty({"pageRequestId"})
    private String pageRequestId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_DATE")
    private Date createDate;

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getVisitUserId() {
        return visitUserId;
    }

    public GioPageEvtStat setVisitUserId(String visitUserId) {
        this.visitUserId = visitUserId;
        return this;
    }

    public String getSessionId() {
        return sessionId;
    }

    public GioPageEvtStat setSessionId(String sessionId) {
        this.sessionId = sessionId;
        return this;
    }

    public String getPlatform() {
        return platform;
    }

    public GioPageEvtStat setPlatform(String platform) {
        this.platform = platform;
        return this;
    }

    public String getDomain() {
        return domain;
    }

    public GioPageEvtStat setDomain(String domain) {
        this.domain = domain;
        return this;
    }

    public String getPage() {
        return page;
    }

    public GioPageEvtStat setPage(String page) {
        this.page = page;
        return this;
    }

    public String getQueryParameters() {
        return queryParameters;
    }

    public GioPageEvtStat setQueryParameters(String queryParameters) {
        this.queryParameters = queryParameters;
        return this;
    }

    public String getReferrer() {
        return referrer;
    }

    public GioPageEvtStat setReferrer(String referrer) {
        this.referrer = referrer;
        return this;
    }

    public String getReferrerPage() {
        return referrerPage;
    }

    public GioPageEvtStat setReferrerPage(String referrerPage) {
        this.referrerPage = referrerPage;
        return this;
    }

    public String getTitle() {
        return title;
    }

    public GioPageEvtStat setTitle(String title) {
        this.title = title;
        return this;
    }

    public Long getTime() {
        return time;
    }

    public GioPageEvtStat setTime(Long time) {
        this.time = time;
        return this;
    }

    public Long getSendTime() {
        return sendTime;
    }

    public GioPageEvtStat setSendTime(Long sendTime) {
        this.sendTime = sendTime;
        return this;
    }

    public Long getPageTime() {
        return pageTime;
    }

    public GioPageEvtStat setPageTime(Long pageTime) {
        this.pageTime = pageTime;
        return this;
    }

    public String getLoginUserId() {
        return loginUserId;
    }

    public GioPageEvtStat setLoginUserId(String loginUserId) {
        this.loginUserId = loginUserId;
        return this;
    }

    public String getPageGroup() {
        return pageGroup;
    }

    public GioPageEvtStat setPageGroup(String pageGroup) {
        this.pageGroup = pageGroup;
        return this;
    }

    public String getPageRequestId() {
        return pageRequestId;
    }

    public GioPageEvtStat setPageRequestId(String pageRequestId) {
        this.pageRequestId = pageRequestId;
        return this;
    }

    @Override
    public String toString() {
        return "GioPageEvtStatModel{" +
            "visitUserId=" + visitUserId +
            ", sessionId=" + sessionId +
            ", platform=" + platform +
            ", domain=" + domain +
            ", page=" + page +
            ", queryParameters=" + queryParameters +
            ", referrer=" + referrer +
            ", referrerPage=" + referrerPage +
            ", title=" + title +
            ", time=" + time +
            ", sendTime=" + sendTime +
            ", pageTime=" + pageTime +
            ", loginUserId=" + loginUserId +
            ", pageGroup=" + pageGroup +
            ", pageRequestId=" + pageRequestId +
            "}";
    }
}
