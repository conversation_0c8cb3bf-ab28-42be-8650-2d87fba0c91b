package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 货不对版  商品
 * @TableName WRONG_GOODS_PRODUCTS
 */
@TableName(value ="WRONG_GOODS_PRODUCTS")
@Data
public class WrongGoodsProducts implements Serializable {
    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 款号
     */
    @TableField("PRODUCT_CODE")
    private String productCode;

    /**
     * 色号
     */
    @TableField("color_No")
    private String colorNo;

    /**
     * 搭配号
     */
    @TableField("styling_Code")
    private String stylingCode;

    /**
     * 创建时间
     */
    @TableField("create_Time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_Time")
    private Date updateTime;

    /**
     * 0 未删除  1 已删除
     */
    @TableField("is_Del")
    private Integer isDel;

    /**
     * 款色
     */
    @TableField("skc")
    private String skc;

    @TableField("reason")
    private String reason;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}