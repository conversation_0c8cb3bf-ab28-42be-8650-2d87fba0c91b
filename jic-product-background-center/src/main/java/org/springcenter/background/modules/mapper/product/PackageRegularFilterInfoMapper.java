package org.springcenter.background.modules.mapper.product;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springcenter.background.modules.model.product.PackageRegularFilterInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【PACKAGE_REGULAR_FILTER_INFO(商品包固定筛选参数数据)】的数据库操作Mapper
* @createDate 2024-07-25 14:58:22
* @Entity generator.domain.PackageRegularFilterInfo
*/
public interface PackageRegularFilterInfoMapper extends BaseMapper<PackageRegularFilterInfo> {


    List<PackageRegularFilterInfo> selectRegularAndChangeAllValid();
}
