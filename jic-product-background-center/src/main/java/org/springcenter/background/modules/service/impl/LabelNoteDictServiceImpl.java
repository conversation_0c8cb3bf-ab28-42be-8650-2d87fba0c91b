package org.springcenter.background.modules.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.background.config.exception.ProductException;
import org.springcenter.background.modules.mapper.product.LabelNoteDictMapper;
import org.springcenter.background.modules.model.product.LabelNoteDict;
import org.springcenter.background.modules.service.ILabelNoteDictService;
import org.springcenter.product.api.dto.back.label.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/10/9 10:19
 */
@Slf4j
@Service
@RefreshScope
public class LabelNoteDictServiceImpl implements ILabelNoteDictService {

    @Autowired
    private LabelNoteDictMapper labelNoteDictMapper;

    @Value("${label.note.dict.tag.id}")
    private String labelNoteDictTagId;

    @Autowired
    @Qualifier("productTransactionTemplate")
    private TransactionTemplate template;

    @Override
    public Boolean addNote(AddLabelNoteReq requestData) {
        // 检查参数
        checkParam(requestData);

        // 判断当前的标签是否已经存在
        LabelNoteDict labelNoteDict = labelNoteDictMapper.selectByLabelCode(requestData.getLabelCode());
        if (labelNoteDict != null) {
            throw new ProductException("当前标签已经存在");
        }

        LabelNoteDict data = new LabelNoteDict();
        data.setId(IdLeaf.getId(labelNoteDictTagId));
        data.setInternalNote(requestData.getInternalNote());
        data.setLabelCode(requestData.getLabelCode());
        data.setExternalNote(requestData.getExternalNote());
        data.setCreateTime(new Date());
        data.setUpdateTime(new Date());
        data.setMcPic(requestData.getMcUrl());
        data.setWebPic(requestData.getWebUrl());
        data.setSubId(requestData.getSubLabelId());
        data.setSubLabelCode(requestData.getSubLabelCode());
        labelNoteDictMapper.insert(data);
        return true;
    }

    @Override
    public Boolean updateNote(UpdateLabelNoteReq requestData) {
        LabelNoteDict labelNoteDict = Preconditions.checkNotNull(labelNoteDictMapper.selectById(requestData.getId()),
                "未找到当前标签信息");
        labelNoteDict.setExternalNote(requestData.getExternalNote());
        labelNoteDict.setInternalNote(requestData.getInternalNote());
        labelNoteDict.setWebPic(requestData.getWebUrl());
        labelNoteDict.setMcPic(requestData.getMcUrl());
        labelNoteDictMapper.updateInfoById(labelNoteDict);

        return true;
    }

    @Override
    public Boolean delNote(String requestData) {
        if (StringUtils.isBlank(requestData)) {
            throw new ProductException("传入的主键不能为空");
        }

        template.execute(a -> {
            labelNoteDictMapper.delNoteById(requestData);
            labelNoteDictMapper.delNoteBySubId(requestData);
           return true;
        });

        return true;
    }

    @Override
    public LabelNoteDetailResp getNoteDetail(String requestData) {
        if (StringUtils.isBlank(requestData)) {
            throw new ProductException("传入的主键不能为空");
        }

        LabelNoteDict labelNoteDict = Preconditions.checkNotNull(labelNoteDictMapper.selectById(requestData),
                "查询到当前实体为空");
        LabelNoteDetailResp detailResp = new LabelNoteDetailResp();
        BeanUtils.copyProperties(labelNoteDict, detailResp);
        detailResp.setWebUrl(labelNoteDict.getWebPic());
        detailResp.setMcUrl(labelNoteDict.getMcPic());
        return detailResp;
    }

    @Override
    public List<LabelNoteListResp> getNoteList(SearchLabelListReq requestData, Page page) {
        if (StringUtils.isNotBlank(requestData.getLabelCode()) && StringUtils.isNotBlank(requestData.getSubId())) {
            throw new ProductException("标签id和标签子级code不能同时存在");
        }

        List<LabelNoteDict> searchRets = new ArrayList<>();
        com.github.pagehelper.Page<LabelNoteDict> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        // 一级列表
        if ((StringUtils.isBlank(requestData.getLabelCode()) && StringUtils.isBlank(requestData.getSubId()))
            || StringUtils.isNotBlank(requestData.getLabelCode())) {
            searchRets = labelNoteDictMapper.selectFirstList(requestData.getLabelCode());


        } else if (StringUtils.isNotBlank(requestData.getSubId())){
            // 二级列表
            searchRets = labelNoteDictMapper.selectSecondList(requestData.getSubId());
        }

        if (CollectionUtils.isEmpty(searchRets)) {
            return Collections.emptyList();
        }

        List<LabelNoteListResp> rets = searchRets.stream().map(item -> {
            LabelNoteListResp resp = new LabelNoteListResp();
            BeanUtils.copyProperties(item, resp);
            resp.setWebUrl(item.getWebPic());
            resp.setMcUrl(item.getMcPic());
            return resp;
        }).collect(Collectors.toList());
        PageInfo<LabelNoteListResp> pageInfo = new PageInfo(hPage);
        pageInfo.setList(rets);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();

    }

    private void checkParam(AddLabelNoteReq requestData) {
        // 如果是三级的 上级id和code必填
        if (requestData.getLevelType() == 2) {
            if (StringUtils.isBlank(requestData.getSubLabelId()) || StringUtils.isBlank(requestData.getSubLabelCode())) {
                throw new ProductException("三级标签的父级id和code必填");
            }
        } else {
            if (StringUtils.isNotBlank(requestData.getSubLabelId())) {
                throw new ProductException("不应该有上级标签id");
            }

        }
    }
}
