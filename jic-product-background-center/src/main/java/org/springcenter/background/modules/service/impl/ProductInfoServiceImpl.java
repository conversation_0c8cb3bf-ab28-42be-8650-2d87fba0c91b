package org.springcenter.background.modules.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springcenter.background.modules.mapper.bojun.BBoxMProductMapper;
import org.springcenter.background.modules.mapper.bojun.MProductMapper;
import org.springcenter.background.modules.model.bojun.BoxMProduct;
import org.springcenter.background.modules.service.ProductInfoService;
import org.springcenter.background.modules.util.EsUtil;
import org.springcenter.product.api.dto.ProductSkcResp;
import org.springcenter.product.api.dto.background.ProductInfoByCode;
import org.springcenter.product.api.dto.background.SampleCodeSpuInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import strman.Strman;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/9/26 14:47
 */
@Service
@Slf4j
@RefreshScope
public class ProductInfoServiceImpl implements ProductInfoService {

    @Autowired
    private BBoxMProductMapper boxMProductMapper;
    @Autowired
    private MProductMapper mProductMapper;

    @Value("${product.skc.index}")
    private String PRODUCT_SKC_INDEX;

    @Autowired
    private EsUtil esUtil;

    @Override
    public ProductInfoByCode getSpuByProductCode(String requestData) {
        if (StringUtils.isBlank(requestData)) {
            throw new RuntimeException("入参不能为空");
        }
        List<BoxMProduct> boxMProducts = boxMProductMapper.selectListByNos(Lists.newArrayList(requestData));
        if (CollectionUtils.isEmpty(boxMProducts)) {
            return null;
        }

        BoxMProduct boxMProduct = boxMProducts.get(0);
        return buildProductInfoByCode(boxMProduct);
    }

    @Override
    public List<SampleCodeSpuInfo> getSkcInfoBySampleCode(List<String> requestData) {
        if (CollectionUtils.isEmpty(requestData)) {
            throw new RuntimeException("入参不能为空");
        }

        SearchRequest request = new SearchRequest();
        request.indices(PRODUCT_SKC_INDEX);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        queryBuilder.must(QueryBuilders.termsQuery("sample_code", requestData));
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(10000);
        sourceBuilder.fetchSource(new String[]{"_id", "sample_code"},
                new String[]{});
        request.source(sourceBuilder);

        List<SampleCodeSpuInfo> resps = new ArrayList<>();
        try {
            SearchResponse search = esUtil.search(request);
            if (search.getHits().getTotalHits().value == 0){
                return Collections.emptyList();
            }
            SearchHit[] hits = search.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                ProductSkcResp entity = ProductSkcResp.fromJson(hits[i].getSourceAsString(), ProductSkcResp.class);
                entity.setId(hits[i].getId());
                SampleCodeSpuInfo info = new SampleCodeSpuInfo();
                info.setSampleCode(entity.getSample_code());
                info.setSkc(entity.getId());
                resps.add(info);
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品异常e = {}", e.getMessage());
        }
        return resps;
    }

    private ProductInfoByCode buildProductInfoByCode(BoxMProduct boxMProduct) {
        ProductInfoByCode productInfoByCode = new ProductInfoByCode();
        productInfoByCode.setId(boxMProduct.getId());
        productInfoByCode.setName(boxMProduct.getName());
        productInfoByCode.setColor_no(boxMProduct.getColorName());
        productInfoByCode.setC_arcbrand_id(boxMProduct.getCArcbrandId());
        productInfoByCode.setM_small_category_id(boxMProduct.getSmallclassid());
        productInfoByCode.setColor_size(boxMProduct.getSizeName());
        productInfoByCode.setM_small_category(boxMProduct.getSmallClass());
        productInfoByCode.setPrice(boxMProduct.getPrice());
        return productInfoByCode;
    }
}
