<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.bojun.MProductListMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.bojun.MProductList">
        <id column="ID" jdbcType="DECIMAL" property="id" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="M_PRODUCT_ID" property="mProductId" />
        <result column="DATE_LIST" property="dateList" />
        <result column="DATE_FOUR" property="dateFour" />
        <result column="DATE_EIGHT" property="dateEight" />
        <result column="OWNERID"  property="ownerid" />
        <result column="MODIFIERID"  property="modifierid" />
        <result column="ISACTIVE"  property="isactive" />
        <result column="CREATIONDATE"  property="creatiodate" />
        <result column="MODIFIEDDATE"  property="modifieddate" />

    </resultMap>
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, M_PRODUCT_ID, DATE_LIST, DATE_FOUR, DATE_EIGHT, OWNERID,
    MODIFIERID, ISACTIVE, CREATIONDATE, MODIFIEDDATE
    </sql>


    <select id="selectProductCodesByTime" resultType="java.lang.String">
        select distinct (mp.name) from M_PRODUCT_LIST mpl left join M_PRODUCT  mp on mpl.M_PRODUCT_ID = mp.id
        where #{date} >= mpl.DATE_LIST and mpl.ISACTIVE = 'Y' and mp.ISACTIVE = 'Y'
    </select>

    <select id="selectByProductId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        FROM M_PRODUCT_LIST
        WHERE ISACTIVE = 'Y' AND M_PRODUCT_ID = #{productId}
    </select>
</mapper>