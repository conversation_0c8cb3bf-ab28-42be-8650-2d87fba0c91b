package org.springcenter.background.modules.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.background.modules.enums.WscBrandEnum;
import org.springcenter.background.modules.model.bojun.CStore;
import org.springcenter.product.api.dto.back.screen.StoreRecommendGoodsResp;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/5/15 15:30
 */
@Data
public class BigDataComprehensiveReq {

    @ApiModelProperty(value = "需要排除的款号")
    private List<Integer> excludeData;

    @ApiModelProperty(value = "需要沉底的款号")
    private List<Integer> sinkData;

    @ApiModelProperty(value = "目标ID 枚举：m_product_id  goodsId")
    private String tarIdType;

    @ApiModelProperty(value = "用户unionid 如未知, 使用[-1|None]")
    private String unionId;

    @ApiModelProperty(value = "对应微商城的商城ID   -1: 全商城(含所有奥莱和正价商品)" +
                                                 "5: 仅保留正价商品" +
                                                 "11: 仅保留奥莱商品")
    // 2738574294，2822095692，2504948039，15，6924108367，4，8348044436，5，77，11，
    //-1，5，12
    // 单品牌门店按照上面品牌枚举 如果是复核品牌5 奥莱传11
    private String weid;

    @ApiModelProperty(value = "微商城门店ID 如未知, 使用[-1|None]")
    private String vId;

    @ApiModelProperty(value = "请求的traceId, 用于跟踪全链路情况")
    private String traceId;

    @ApiModelProperty(value = "测试参数")
    private boolean pTest = false;

    @ApiModelProperty(value = "请求来源 productListOfSmartScreen: 智慧大屏商品列表")
    private String requestSource;

    @ApiModelProperty(value = "需要计算的伯俊款号")
    private List<Integer> spuIds;

    public static BigDataComprehensiveReq build(List<StoreRecommendGoodsResp> list, CStore cStore,
                                                String unionId, String traceId, List<Long> aolaiList) {
        BigDataComprehensiveReq bigDataComprehensiveReq = new BigDataComprehensiveReq();
        bigDataComprehensiveReq.setVId("-1");
        bigDataComprehensiveReq.setTraceId(traceId);
        if (StringUtils.isBlank(unionId)) {
            bigDataComprehensiveReq.setUnionId("-1");
        } else {
            bigDataComprehensiveReq.setUnionId(unionId);
        }
        List<Integer> productIds = list.stream().map(StoreRecommendGoodsResp::getM_product_id)
                .map(v -> Integer.valueOf(Objects.toString(v)))
                .collect(Collectors.toList());
        bigDataComprehensiveReq.setSpuIds(productIds);
        WscBrandEnum wscBrandEnum = WscBrandEnum.getWscBrandEnum(cStore.getCArcbrandId());
        // 先判断是否奥莱
        if (wscBrandEnum == null) {
            bigDataComprehensiveReq.setWeid("-1");
        } else if (cStore.getCCustomerId() == 176L && Objects.equals(cStore.getDefault05(), "是")) {
            bigDataComprehensiveReq.setWeid("11");
        } else if (cStore.getCCustomerId() != 176L && aolaiList.contains(cStore.getCStoreAttrib9Id())) {
            bigDataComprehensiveReq.setWeid("11");
        } else if (cStore.getCUnionstoreId() != null) {
            // 判断是否多品牌
            bigDataComprehensiveReq.setWeid("5");
        } else {
            bigDataComprehensiveReq.setWeid(Objects.toString(wscBrandEnum.getWscBrandId()));
        }
        bigDataComprehensiveReq.setRequestSource("productListOfSmartScreen");
        bigDataComprehensiveReq.setTarIdType("m_product_id");

        // 处理单品牌
        return bigDataComprehensiveReq;
    }
}
