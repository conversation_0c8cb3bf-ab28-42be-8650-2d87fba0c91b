<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.PackageFilterSettingMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.PackageFilterSetting">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="filterName" column="FILTER_NAME" jdbcType="VARCHAR"/>
            <result property="filterField" column="FILTER_FIELD" jdbcType="VARCHAR"/>
            <result property="filterParam" column="FILTER_PARAM" jdbcType="VARCHAR"/>
            <result property="filterType" column="FILTER_TYPE" jdbcType="DECIMAL"/>
            <result property="filterFieldType" column="FILTER_FIELD_TYPE" jdbcType="DECIMAL"/>
            <result property="sort" column="SORT" jdbcType="DECIMAL"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,FILTER_NAME,FILTER_FIELD,
        FILTER_PARAM,FILTER_TYPE,FILTER_FIELD_TYPE,
        SORT,IS_DELETED,CREATE_TIME,
        UPDATE_TIME
    </sql>
    <select id="selectAllValid" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM PACKAGE_FILTER_SETTING
            WHERE IS_DELETED = 0
        ORDER BY SORT ASC
    </select>
</mapper>
