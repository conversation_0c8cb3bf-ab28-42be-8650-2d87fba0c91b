package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.PackageFilterInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【PACKAGE_FILTER_INFO(商品包过滤条件)】的数据库操作Mapper
* @createDate 2024-07-25 13:28:59
* @Entity generator.domain.PackageFilterInfo
*/
public interface PackageFilterInfoMapper extends BaseMapper<PackageFilterInfo> {



    void batchInsert(@Param("list") List<PackageFilterInfo> filterInfos);

    List<PackageFilterInfo> selectByPacId(@Param("pacId") String id);

    void updateByPacId(@Param("pacId") String id);
}
