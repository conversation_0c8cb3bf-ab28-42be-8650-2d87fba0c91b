<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.LabelNoteDictMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.LabelNoteDict">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="subId" column="SUB_ID" jdbcType="VARCHAR"/>
            <result property="subLabelCode" column="SUB_LABEL_CODE" jdbcType="VARCHAR"/>
            <result property="internalNote" column="INTERNAL_NOTE" jdbcType="VARCHAR"/>
            <result property="externalNote" column="EXTERNAL_NOTE" jdbcType="VARCHAR"/>
            <result property="webPic" column="WEB_PIC" jdbcType="VARCHAR"/>
            <result property="mcPic" column="MC_PIC" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="labelCode" column="LABEL_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,SUB_ID,SUB_LABEL_CODE,
        INTERNAL_NOTE,EXTERNAL_NOTE,WEB_PIC,
        MC_PIC,IS_DELETED,CREATE_TIME,
        UPDATE_TIME, LABEL_CODE
    </sql>
    <update id="updateInfoById">
        UPDATE LABEL_NOTE_DICT
        set INTERNAL_NOTE = #{item.internalNote},
        EXTERNAL_NOTE = #{item.externalNote},
        WEB_PIC = #{item.webPic},
        MC_PIC = #{item.mcPic},
        UPDATE_TIME = sysdate
        WHERE ID = #{item.id}
    </update>



    <select id="selectByLabelCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM LABEL_NOTE_DICT
        WHERE IS_DELETED = 0 AND LABEL_CODE = #{labelCode}
    </select>


    <update id="delNoteById">
        UPDATE LABEL_NOTE_DICT
        set IS_DELETED = 1,
            UPDATE_TIME = sysdate
        WHERE ID = #{id}
    </update>

    <update id="delNoteBySubId">
        UPDATE LABEL_NOTE_DICT
        set IS_DELETED = 1,
            UPDATE_TIME = sysdate
        WHERE SUB_ID = #{subId}
    </update>

    <select id="selectFirstList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM LABEL_NOTE_DICT
        WHERE IS_DELETED = 0 AND SUB_ID IS NULL
        <if test="labelCode != null and labelCode != '' ">
            AND SUB_LABEL_CODE = #{labelCode}
        </if>
        ORDER by UPDATE_TIME DESC
    </select>

    <select id="selectSecondList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM LABEL_NOTE_DICT
        WHERE IS_DELETED = 0 AND SUB_ID = #{subId}
        ORDER BY UPDATE_TIME DESC
    </select>
</mapper>
