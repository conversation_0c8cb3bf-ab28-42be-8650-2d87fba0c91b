<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.PackageFilterInfoMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.PackageFilterInfo">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="pacId" column="PAC_ID" jdbcType="VARCHAR"/>
            <result property="fieldName" column="FIELD_NAME" jdbcType="VARCHAR"/>
            <result property="field" column="FIELD" jdbcType="VARCHAR"/>
            <result property="condition" column="CONDITION" jdbcType="DECIMAL"/>
            <result property="data" column="DATA" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PAC_ID,FIELD_NAME,
        FIELD,CONDITION,DATA,
        CREATE_TIME,UPDATE_TIME,IS_DELETED
    </sql>

    <insert id="batchInsert">
        INSERT ALL
        <foreach item="item" index="index" collection="list">
            INTO PACKAGE_FILTER_INFO
            (ID, PAC_ID, FIELD_NAME, FIELD, CONDITION, DATA, CREATE_TIME, UPDATE_TIME) VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.pacId,jdbcType=VARCHAR}, #{item.fieldName,jdbcType=VARCHAR},
            #{item.field,jdbcType=VARCHAR}, #{item.condition,jdbcType=DECIMAL}, #{item.data,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>
    <update id="updateByPacId">
        UPDATE PACKAGE_FILTER_INFO
        set IS_DELETED = 1, update_time = sysdate
        WHERE PAC_ID = #{pacId,jdbcType=VARCHAR} AND IS_DELETED = 0
    </update>

    <select id="selectByPacId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
            FROM PACKAGE_FILTER_INFO
        WHERE PAC_ID = #{pacId,jdbcType=VARCHAR} AND IS_DELETED = 0
    </select>
</mapper>
