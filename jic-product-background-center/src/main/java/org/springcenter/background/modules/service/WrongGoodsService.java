package org.springcenter.background.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.background.ChangeStatusReq;
import org.springcenter.product.api.dto.background.WrongGoodsMainReq;
import org.springcenter.product.api.dto.background.WrongGoodsMainResp;

import java.util.List;

public interface WrongGoodsService {
    /**
     * 同步街拍图片 从网盘同步到数据库  并且同步商品  商品在网盘中的excel中
     * @param netDiskPath
     * @param wrongGoodsMainId  主表主键id  如果有的话 则仅同步这个  为空则同步所有
     */
    void syncStreetPhotoImg(String netDiskPath,String wrongGoodsMainId);

    void syncEcomImg(String netDiskPath);

    String importStreetPhotoRelation(String filePath,String keys);

    List<WrongGoodsMainResp> list(WrongGoodsMainReq requestData, Page page);

    void changeStatus(ChangeStatusReq requestData);

    void reSyncStreetPhoto(String wrongGoodsMainId);

    String importWrongReason(String filePath, Object o);
}
