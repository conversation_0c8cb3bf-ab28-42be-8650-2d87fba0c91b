package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/7/1 15:23
 */
@TableName(value = "FAB_WEARING_IMG_INFO_LOG")
@Data
public class FabWearingImgInfoLog {

    @TableId(value = "ID")
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField(value = "PRODUCT_ID")
    @ApiModelProperty(value = "商品id")
    private String productId;

    @TableField(value = "NAME")
    @ApiModelProperty(value = "商品款号")
    private String name;

    @TableField(value = "IMAGES")
    @ApiModelProperty(value = "商品图片 用,分割")
    private String images;

    @TableField(value = "OPERATORS")
    @ApiModelProperty(value = "操作人")
    private String operators;

    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "IS_DELETED")
    @ApiModelProperty(value = "0正常 1已删除")
    private Integer isDeleted;

    @TableField(value = "FAB_WEARING_IMG_INFO_ID")
    @ApiModelProperty(value = "fab穿着方式图片信息id")
    private String fabWearingImgInfoId;
}
