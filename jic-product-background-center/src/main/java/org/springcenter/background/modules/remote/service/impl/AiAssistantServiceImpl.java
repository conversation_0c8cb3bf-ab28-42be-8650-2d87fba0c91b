package org.springcenter.background.modules.remote.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.modules.remote.IAiAssistantRemoteApi;
import org.springcenter.background.modules.remote.entity.AiAssistantReqEntity;
import org.springcenter.background.modules.remote.entity.AiAssistantRespEntity;
import org.springcenter.background.modules.remote.service.IAiAssistantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date:2023/9/19 15:21
 */
@Service
@Slf4j
public class AiAssistantServiceImpl implements IAiAssistantService {

    @Autowired
    private IAiAssistantRemoteApi aiAssistantRemoteApi;

    @Override
    public AiAssistantRespEntity invokeAiAssistant(AiAssistantReqEntity req) {
        try {
            log.info("调用ai助手， 入参：{}", JSONObject.toJSONString(req));
            Response<AiAssistantRespEntity> response = aiAssistantRemoteApi.invokeThirdAi(req).execute();
            if (response.isSuccessful()){
                return response.body();
            }
            log.error("调用ai助手异常， 入参：{}", JSONObject.toJSONString(req));
            throw new RuntimeException("调用ai助手异常");
        } catch (IOException e) {
            log.error("调用ai助手异常  e:{} message:{}", e, e.getMessage());
            throw new RuntimeException("调用ai助手异常");
        }
    }
}
