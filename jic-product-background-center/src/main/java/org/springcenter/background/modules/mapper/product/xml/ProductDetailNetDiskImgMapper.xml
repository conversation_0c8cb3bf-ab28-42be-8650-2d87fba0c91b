<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.ProductDetailNetDiskImgMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.ProductDetailNetDiskImg">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode" />
    <result column="COLOR_NO" jdbcType="VARCHAR" property="colorNo" />
    <result column="IS_DEL" jdbcType="DECIMAL" property="isDel" />
    <result column="NEID" jdbcType="VARCHAR" property="neid" />
    <result column="NSID" jdbcType="VARCHAR" property="nsid" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="TYPE" jdbcType="DECIMAL" property="type" />
    <result column="QINIU_IMG_PATH" jdbcType="VARCHAR" property="qiniuImgPath" />
    <result column="OVERSEA_FLAG" jdbcType="DECIMAL" property="overseaFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, PRODUCT_CODE, COLOR_NO, IS_DEL, NEID, NSID, CREATE_TIME, UPDATE_TIME, "TYPE", 
    QINIU_IMG_PATH,OVERSEA_FLAG
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PRODUCT_DETAIL_NET_DISK_IMG
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectBySkcAndIsDel" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PRODUCT_DETAIL_NET_DISK_IMG
    where PRODUCT_CODE = #{productCode} and IS_DEL = #{isDel} and COLOR_NO = #{colorNo}
    <if test="type != null">
      and "TYPE" = #{type}
    </if>
  </select>
    <select id="selectByProductCodesAndType"
            resultMap="BaseResultMap">

      select
      <include refid="Base_Column_List" />
      from PRODUCT_DETAIL_NET_DISK_IMG
      where
      PRODUCT_CODE in
      <foreach collection="list" separator="," close=")" open="(" item="item">
        #{item}
      </foreach>
      and IS_DEL = 0 and "TYPE" = #{type}

    </select>
  <select id="selectShouldUpdateByProductCodes"  resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from PRODUCT_DETAIL_NET_DISK_IMG
    where type = 1 and IS_DEL = 0   and NEID is not null  and QINIU_IMG_PATH is null and PRODUCT_CODE in
                                                  <foreach collection="productCodes" close=")" open="(" item="item" separator=",">
                                                    #{item}
                                                  </foreach>

  </select>
  <select id="selectByNeId" resultMap="BaseResultMap"
          parameterType="java.lang.String">
    select <include refid="Base_Column_List"></include> from PRODUCT_DETAIL_NET_DISK_IMG where is_del = 0 and neid = #{neid}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from PRODUCT_DETAIL_NET_DISK_IMG
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.springcenter.background.modules.model.product.ProductDetailNetDiskImg">
    insert into PRODUCT_DETAIL_NET_DISK_IMG (ID, PRODUCT_CODE, COLOR_NO, 
      IS_DEL, NEID, NSID, 
      CREATE_TIME, UPDATE_TIME, "TYPE", 
      QINIU_IMG_PATH)
    values (#{id,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, #{colorNo,jdbcType=VARCHAR}, 
      #{isDel,jdbcType=DECIMAL}, #{neid,jdbcType=VARCHAR}, #{nsid,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{type,jdbcType=DECIMAL}, 
      #{qiniuImgPath,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.springcenter.background.modules.model.product.ProductDetailNetDiskImg">
    insert into PRODUCT_DETAIL_NET_DISK_IMG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="productCode != null">
        PRODUCT_CODE,
      </if>
      <if test="colorNo != null">
        COLOR_NO,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="neid != null">
        NEID,
      </if>
      <if test="nsid != null">
        NSID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="type != null">
        "TYPE",
      </if>
      <if test="qiniuImgPath != null">
        QINIU_IMG_PATH,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="colorNo != null">
        #{colorNo,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="neid != null">
        #{neid,jdbcType=VARCHAR},
      </if>
      <if test="nsid != null">
        #{nsid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=DECIMAL},
      </if>
      <if test="qiniuImgPath != null">
        #{qiniuImgPath,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert">
    INSERT ALL
    <foreach item="item" index="index" collection="list">
      into PRODUCT_DETAIL_NET_DISK_IMG
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          ID,
        </if>
        <if test="item.productCode != null">
          PRODUCT_CODE,
        </if>
        <if test="item.colorNo != null">
          COLOR_NO,
        </if>
        <if test="item.isDel != null">
          IS_DEL,
        </if>
        <if test="item.neid != null">
          NEID,
        </if>
        <if test="item.nsid != null">
          NSID,
        </if>
        <if test="item.createTime != null">
          CREATE_TIME,
        </if>
        <if test="item.updateTime != null">
          UPDATE_TIME,
        </if>
        <if test="item.type != null">
          "TYPE",
        </if>
        <if test="item.qiniuImgPath != null">
          QINIU_IMG_PATH,
        </if>
        <if test="item.overseaFlag != null">
          OVERSEA_FLAG,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=VARCHAR},
        </if>
        <if test="item.productCode != null">
          #{item.productCode,jdbcType=VARCHAR},
        </if>
        <if test="item.colorNo != null">
          #{item.colorNo,jdbcType=VARCHAR},
        </if>
        <if test="item.isDel != null">
          #{item.isDel,jdbcType=DECIMAL},
        </if>
        <if test="item.neid != null">
          #{item.neid,jdbcType=VARCHAR},
        </if>
        <if test="item.nsid != null">
          #{item.nsid,jdbcType=VARCHAR},
        </if>
        <if test="item.createTime != null">
          #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updateTime != null">
          #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.type != null">
          #{item.type,jdbcType=DECIMAL},
        </if>
        <if test="item.qiniuImgPath != null">
          #{item.qiniuImgPath,jdbcType=VARCHAR},
        </if>
        <if test="item.overseaFlag != null">
          #{item.overseaFlag},
        </if>
      </trim>
    </foreach>
    SELECT 1 FROM DUAL
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.springcenter.background.modules.model.product.ProductDetailNetDiskImg">
    update PRODUCT_DETAIL_NET_DISK_IMG
    <set>
      <if test="productCode != null">
        PRODUCT_CODE = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="colorNo != null">
        COLOR_NO = #{colorNo,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="neid != null">
        NEID = #{neid,jdbcType=VARCHAR},
      </if>
      <if test="nsid != null">
        NSID = #{nsid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        "TYPE" = #{type,jdbcType=DECIMAL},
      </if>
      <if test="qiniuImgPath != null">
        QINIU_IMG_PATH = #{qiniuImgPath,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.springcenter.background.modules.model.product.ProductDetailNetDiskImg">
    update PRODUCT_DETAIL_NET_DISK_IMG
    set PRODUCT_CODE = #{productCode,jdbcType=VARCHAR},
      COLOR_NO = #{colorNo,jdbcType=VARCHAR},
      IS_DEL = #{isDel,jdbcType=DECIMAL},
      NEID = #{neid,jdbcType=VARCHAR},
      NSID = #{nsid,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      "TYPE" = #{type,jdbcType=DECIMAL},
      QINIU_IMG_PATH = #{qiniuImgPath,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>


  <select id="selectDetailListByName" resultType="java.lang.String">
    SELECT QINIU_IMG_PATH
    FROM PRODUCT_DETAIL_NET_DISK_IMG
    WHERE IS_DEL = 0 AND TYPE = 1 AND PRODUCT_CODE = #{name}
  </select>
</mapper>