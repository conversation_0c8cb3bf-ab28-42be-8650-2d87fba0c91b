package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.ProductFabricInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/9/5 14:13
 */
public interface ProductFabricInfoMapper extends BaseMapper<ProductFabricInfo> {

    void batchUpdateByIds(@Param("list") List<ProductFabricInfo> v);

    void batchInsert(@Param("list") List<ProductFabricInfo> v);

    List<ProductFabricInfo> selectByProductId(@Param("productId") String id);
}
