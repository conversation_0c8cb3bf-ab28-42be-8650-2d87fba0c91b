package org.springcenter.background.modules.service;

import org.springcenter.background.modules.model.product.ProductOverseasNetDiskImg;
import org.springcenter.background.modules.model.product.SampleClothNetDiskImg;
import org.springcenter.product.api.dto.*;

import java.util.List;

public interface IProductDetailService {
    /**
     * 导入网盘图片路径excel
     * @param url
     * @return
     */
    String importImgPathExcel(String url);

    /**
     * 导入品牌部   搭配和款号的关系
     * @param filePath
     * @return
     */
    String importBrandExcelCollocationReleation(String filePath);

    /**
     *  导入 电商部   搭配和款号的对应关系
     * @param filePath
     * @return
     */
    String importEComExcelCollocationReleation(String filePath);

    /**
     * 根据商品款号 查询  主图  细节图  搭配图  以及搭配图关联所有搭配
     * @param requestData
     * @return
     */
    List<FindProductDetailImgResp> findProductDetailImg(FindProductDetailImgReq requestData);

    /**
     * 根据款号查询多个海外的图片
     * @param requestData
     * @return
     */
    List<ProductOverseasNetDiskImg> findProductOverseasImg(FindProductDetailImgReq requestData);

    /**
     * 导入 样衣号的网盘 地址， 进行网盘同步数据库
     */
    String importSampleClothNetDiskExcel(String filePath);

    /**
     * 通过联想网盘获取的预览图进行上传七牛云地址
     * @param id
     */
    void testUploadQiniuByLianxiangNetDisk(String id);



    List<SampleProductDetailImg> findSampleClothNetDiskImgs(FindSampleClothNetDiskImgReq findSampleClothNetDiskImgReq);

    List<FindProductDetailImgResp> findProductDetailImgForOversea(FindProductDetailImgReq requestData);
}
