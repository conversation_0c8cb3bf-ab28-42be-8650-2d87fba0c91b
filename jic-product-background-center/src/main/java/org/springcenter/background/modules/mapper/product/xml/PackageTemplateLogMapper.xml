<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.PackageTemplateLogMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.PackageTemplateLog">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="packageId" column="PACKAGE_ID" jdbcType="VARCHAR"/>
            <result property="pacMainId" column="PAC_MAIN_ID" jdbcType="VARCHAR"/>
            <result property="packageName" column="PACKAGE_NAME" jdbcType="VARCHAR"/>
            <result property="hasFilter" column="HAS_FILTER" jdbcType="DECIMAL"/>
            <result property="hasImport" column="HAS_IMPORT" jdbcType="DECIMAL"/>
            <result property="updator" column="UPDATOR" jdbcType="VARCHAR"/>
            <result property="invalidType" column="INVALID_TYPE" jdbcType="DECIMAL"/>
            <result property="startDate" column="START_DATE" jdbcType="TIMESTAMP"/>
            <result property="endDate" column="END_DATE" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
            <result property="department" column="DEPARTMENT" jdbcType="VARCHAR"/>
            <result property="filterJson" column="FILTER_JSON" jdbcType="VARCHAR"/>
            <result property="importJson" column="IMPORT_JSON" jdbcType="VARCHAR"/>
            <result property="operateType" column="OPERATE_TYPE" jdbcType="DECIMAL"/>
            <result property="isOpen" column="IS_OPEN" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PACKAGE_ID,PACKAGE_NAME,
        HAS_FILTER,HAS_IMPORT,UPDATOR,
        INVALID_TYPE,START_DATE,END_DATE,
        UPDATE_TIME,CREATE_TIME,IS_DELETED,
        DEPARTMENT,FILTER_JSON,IMPORT_JSON,
        OPERATE_TYPE,PACKAGE_INTRODUCTION, PAC_MAIN_ID, IS_OPEN
    </sql>
    <select id="selectByPacMainId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM PACKAGE_TEMPLATE_LOG
            WHERE PAC_MAIN_ID = #{pacMainId} AND IS_DELETED = 0
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>
