package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.common.system.vo.SysCategoryModel;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.QiniuUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springcenter.background.modules.mapper.product.EsgNetDiskDetailMapper;
import org.springcenter.background.modules.mapper.product.EsgNetDiskMainMapper;
import org.springcenter.background.modules.model.product.EsgNetDiskDetail;
import org.springcenter.background.modules.model.product.EsgNetDiskMain;
import org.springcenter.background.modules.model.product.ProductDetailNetDiskImg;
import org.springcenter.background.modules.service.EsgService;
import org.springcenter.background.modules.service.LianXiangYunFilezService;
import org.springcenter.background.modules.util.DateUtil;
import org.springcenter.product.api.EsgParamResp;
import org.springcenter.product.api.constant.IdConstant;
import org.springcenter.product.api.dto.BatchGetViewUrlReq;
import org.springcenter.product.api.dto.LianxiangBatchGetViewReq;
import org.springcenter.product.api.dto.LianxiangBatchGetViewResp;
import org.springcenter.product.api.dto.background.EsgNetDiskDetailResp;
import org.springcenter.product.api.dto.background.EsgNetDiskMainResp;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.product.api.lenovo.LenovoFileResp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class EsgServiceImpl implements EsgService {


    @Autowired
    private EsgNetDiskMainMapper esgNetDiskMainMapper;

    @Autowired
    private EsgNetDiskDetailMapper esgNetDiskDetailMapper;

    @Autowired
    private LianXiangYunFilezService lianXiangYunFilezService;

    @Autowired
    QiniuUtil qiniuUtil;


    @Override
    public List<EsgNetDiskMainResp> list(Page page) {
        List<EsgNetDiskMainResp> resps = new ArrayList<>();

        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        QueryWrapper<EsgNetDiskMain> queryWrapper = new QueryWrapper();
        queryWrapper.eq("IS_DEL",0);
        List<EsgNetDiskMain> esgNetDiskMains = esgNetDiskMainMapper.selectList(queryWrapper);
        PageInfo<EsgNetDiskMain> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<EsgNetDiskMain> list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            // 批量获取子类
            QueryWrapper<EsgNetDiskDetail> esgNetDiskDetailQueryWrapper = new QueryWrapper<>();
            esgNetDiskDetailQueryWrapper.in("ESG_NET_DISK_MAIN_ID",list.stream().map(r->r.getId()).collect(Collectors.toList()));
            esgNetDiskDetailQueryWrapper.eq("IS_DEL",0);
            List<EsgNetDiskDetail> esgNetDiskDetails = esgNetDiskDetailMapper.selectList(esgNetDiskDetailQueryWrapper);
            //分组
            Map<String, List<EsgNetDiskDetail>> collect = esgNetDiskDetails.stream().collect(Collectors.groupingBy(r -> r.getEsgNetDiskMainId()));
            //
            for (EsgNetDiskMain esgNetDiskMain : list) {
                EsgNetDiskMainResp esgNetDiskMainResp = new EsgNetDiskMainResp();
                BeanUtils.copyProperties(esgNetDiskMain,esgNetDiskMainResp);
                List<EsgNetDiskDetail> esgNetDiskDetails1 = collect.get(esgNetDiskMain.getId());
                if(CollectionUtils.isNotEmpty(esgNetDiskDetails1)){
                    String jsonString = JSONObject.toJSONString(esgNetDiskDetails1);
                    esgNetDiskMainResp.setList(JSONObject.parseArray(jsonString, EsgNetDiskDetailResp.class));
                }
                resps.add(esgNetDiskMainResp);
            }
        }
        return resps;
    }

    @Override
    public void createOrUpdate(EsgNetDiskMain createOrUpdate,String userId) {
        if(createOrUpdate == null){
            return ;
        }
        String id = createOrUpdate.getId();
        if(StringUtils.isNotBlank(id)){
            // 编辑
            EsgNetDiskMain update = new EsgNetDiskMain();
            BeanUtils.copyProperties(createOrUpdate,update);
            update.setUpdateTime(new Date());
            esgNetDiskMainMapper.updateById(update);
        }else{
            id = IdLeaf.getId(IdConstant.ESG_NET_DISK_MAIN);
            EsgNetDiskMain update = new EsgNetDiskMain();
            BeanUtils.copyProperties(createOrUpdate,update);
            update.setId(id);
            update.setIsDel(0);
            update.setCreateTime(new Date());
            update.setUpdateTime(new Date());
            esgNetDiskMainMapper.insert(update);
        }
    }

    @Override
    public void reSyncData(String esgNetDiskMainId, String userId) {
        // 更新信息
        EsgNetDiskMain update = new EsgNetDiskMain();
        update.setId(esgNetDiskMainId);
        update.setUpdateTime(new Date());
        update.setUpdateBy(userId);
        esgNetDiskMainMapper.updateById(update);

        // 删除数据u 软删除
        QueryWrapper<EsgNetDiskDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ESG_NET_DISK_MAIN_ID",esgNetDiskMainId);
        EsgNetDiskDetail updateEsg = new EsgNetDiskDetail();
        updateEsg.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
        esgNetDiskDetailMapper.update(updateEsg,queryWrapper);

        // 重新同步
        EsgNetDiskMain esgNetDiskMain = esgNetDiskMainMapper.selectById(esgNetDiskMainId);
        if(esgNetDiskMain == null || StringUtils.isBlank(esgNetDiskMain.getPath())){
            return ;
        }

        loadAndInsertData(esgNetDiskMain.getPath(),esgNetDiskMain.getId());
    }


    @Value("${esg.goodseanson}")
    private String goodSeasonList;

    @Value("${esg.brand}")
    private String brandList;


    @Override
    public EsgParamResp getParams(CommonRequest request) {
        List<String> goodSeasons = Arrays.stream(goodSeasonList.split(";")).collect(Collectors.toList());
        List<String> brandsList = Arrays.stream(brandList.split(";")).collect(Collectors.toList());



        EsgParamResp resp = new EsgParamResp();
        resp.setBrandList(brandsList);
        resp.setGoodSeasonList(goodSeasons);
        return resp;
    }

    private void loadAndInsertData(String netDiskPath,String esgNetDiskMainId) {
        if(StringUtils.isBlank(netDiskPath)){
            log.info("syncStreetPhotoImg 传递的路径为空 ，请重新传递!");
            return ;
        }
        // 同步数据处理
        int i = 0;
        while (true){
            LenovoFileResp resp = lianXiangYunFilezService.getLenovoFileResp(netDiskPath, i);
            if (resp == null || CollectionUtils.isEmpty(resp.getFileModelList())) {
                break ;
            }
            //一直读取到最后
            List<LenovoFileResp.LenovoFileModel> fileModelList = resp.getFileModelList();
            readEsg(fileModelList,0,0,esgNetDiskMainId);
            i++;
        }
    }

    private void readEsg(List<LenovoFileResp.LenovoFileModel> fileModelList,
                         int pageNo,
                         int type,
                         String esgNetDiskMainId) {

        for (LenovoFileResp.LenovoFileModel lenovoFileModel : fileModelList) {
            Boolean dir = lenovoFileModel.getDir();
            pageNo = 0;
            if (dir) {
                int pageNo2  = 0;
                while (true){
                    LenovoFileResp lenovoFileResp = lianXiangYunFilezService.getLenovoFileResp(lenovoFileModel.getPath(), pageNo);
                    if (lenovoFileResp == null || CollectionUtils.isEmpty(lenovoFileResp.getFileModelList())) {
                        break ;
                    }
                    readEsg(lenovoFileResp.getFileModelList(),pageNo2,type,esgNetDiskMainId);
                    pageNo ++;
                }
            }else{
                //  不是文件夹   开始处理数据
                try {
                    dealEsgDetail(lenovoFileModel,esgNetDiskMainId);
                }catch (Exception e){
                    log.info("dealEsgDetail = {}", JSONObject.toJSONString(lenovoFileModel),e);
                }
            }
        }
    }

    private void dealEsgDetail(LenovoFileResp.LenovoFileModel lenovoFileModel, String esgNetDiskMainId) {
        //处理数据
        String path = lenovoFileModel.getPath();
        // 识别数据  只需要识别  轮播  视频   系列说明图
        //企业文件/JNBYGROUP/图片管理库/大片及营销制片/速写_LESS/LESS/25SS/精工/吊染/1106LESS精工21575_轮播.jpg
        //企业文件/JNBYGROUP/图片管理库/大片及营销制片/速写_LESS/LESS/25SS/精工/吊染/1106LESS精工21632_系列说明图.jpg
        //企业文件/JNBYGROUP/图片管理库/大片及营销制片/速写_LESS/LESS/25SS/精工/吊染/【30】LESS吊染_1920x1080_视频.mp4
        Integer type = null ;
        //1106LESS精工21575_轮播.jpg
        String substring = path.substring(path.lastIndexOf("/") + 1);
        //_轮播.jpg
        String substring1 = substring.substring(substring.lastIndexOf("_"));
        // _轮播
        String substring2 = substring1.substring(0, substring1.lastIndexOf("."));
        if("_轮播".equals(substring2)){
            type = 0;
        }else if("_系列说明图".equals(substring2)){
            type = 2;
        }else if("_视频".equals(substring2)){
            type = 1;
        }
        if(type == null){
            return ;
        }
        // 插入数据
        EsgNetDiskDetail esgNetDiskDetail = new EsgNetDiskDetail();
        String id = IdLeaf.getId(IdConstant.ESG_NET_DISK_DETAIL);
        esgNetDiskDetail.setId(id);
        esgNetDiskDetail.setUpdateTime(new Date());
        esgNetDiskDetail.setCreateTime(new Date());
        esgNetDiskDetail.setNeid(lenovoFileModel.getNeid());
        esgNetDiskDetail.setNsid(lenovoFileModel.getNsid());
        esgNetDiskDetail.setType(type);
        esgNetDiskDetail.setIsDel(IsDeleteEnum.NORMAL.getCode());
        esgNetDiskDetail.setEsgNetDiskMainId(esgNetDiskMainId);
        esgNetDiskDetail.setFilePath(lenovoFileModel.getPath());
        esgNetDiskDetailMapper.insert(esgNetDiskDetail);

        //  准备上传七牛云
        // 批量获取预览信息
        LianxiangBatchGetViewReq lianxiangBatchGetViewReq = new LianxiangBatchGetViewReq();
        List<BatchGetViewUrlReq.LianxiangData> file_array = new ArrayList<>();
        BatchGetViewUrlReq.LianxiangData lianxiangData = new BatchGetViewUrlReq.LianxiangData();
        lianxiangData.setNeid(lenovoFileModel.getNeid());
        lianxiangData.setNsid(lenovoFileModel.getNsid());
        lianxiangData.setModelType(type);
        lianxiangData.setSuff(lenovoFileModel.getPath().substring(lenovoFileModel.getPath().lastIndexOf(".")));

        file_array.add(lianxiangData);
        lianxiangBatchGetViewReq.setFile_array(file_array);

        // 上传七牛云   key 为  neid   value 为 七牛云值
        Map<String,String> map = uploadQiniu(lianxiangBatchGetViewReq);
        EsgNetDiskDetail updateEsg = new EsgNetDiskDetail();
        updateEsg.setId(id);
        updateEsg.setQiniuPath(map.get(lenovoFileModel.getNeid()));
        esgNetDiskDetailMapper.updateById(updateEsg);
    }

    private Map<String,String> uploadQiniu(LianxiangBatchGetViewReq lianxiangBatchGetViewReq) {
        List<BatchGetViewUrlReq.LianxiangData> fileArray = lianxiangBatchGetViewReq.getFile_array();
        // 根据neid分组
        Map<String, List<BatchGetViewUrlReq.LianxiangData>> collect = fileArray.stream().collect(Collectors.groupingBy(r -> r.getNeid()));

        Map<String,String> resultMap  = new HashMap<>();
        List<LianxiangBatchGetViewResp> lianxiangBatchGetViewResps = lianXiangYunFilezService.batchGetViewUrl(lianxiangBatchGetViewReq);
        for (LianxiangBatchGetViewResp lianxiangBatchGetViewResp : lianxiangBatchGetViewResps) {
            String previewUrl = lianxiangBatchGetViewResp.getPreviewUrl();
            OutputStream os = null;
            InputStream is = null;
            List<BatchGetViewUrlReq.LianxiangData> lianxiangData = collect.get(lianxiangBatchGetViewResp.getNeid());
            // 视频
            File file = new File("/opt/logs/测试文件2.png");
            if(CollectionUtils.isNotEmpty(lianxiangData)){
                file = new File("/opt/logs/测试文件2" + lianxiangData.get(0).getSuff());
            }
            try {
                URL url = new URL(previewUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                // 设置请求方法，默认是GET
                connection.setRequestMethod("GET");
                // 连接
                connection.connect();
                // 检查响应码
                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // 创建BufferedReader读取响应
                    is = connection.getInputStream();
                    // 准备输出流
                    os = new FileOutputStream(file);
                    // 准备一个数组，用来存放读写的数据
                    byte[] b = new byte[1024];
                    int len = 0;
                    // read(b)实现读取操作，数据存入b数组，返回读取长度给len，当所有内容都读取完毕，len=-1
                    while ((len = is.read(b)) != -1) {
                        // 实现写的操作
                        os.write(b, 0, len);
                    }
                    // 断开连接
                    connection.disconnect();

                    String dateStr = DateUtil.formatToStr(new Date(), DateUtil.DATEFORMATE_YYYY_MM_DD);
                    String fileName = lianxiangBatchGetViewResp.getNeid() + "-" + dateStr  ;
                    if(CollectionUtils.isNotEmpty(lianxiangData)){
                        fileName = fileName + lianxiangData.get(0).getSuff();
                    }

                    String uploadUrl = qiniuUtil.upload(file.getPath(),  fileName);
                    // 获取数据然后更新
                    log.info("上传完毕  neid = {} , url = {}",lianxiangBatchGetViewResp.getNeid(),uploadUrl);
                    resultMap.put(lianxiangBatchGetViewResp.getNeid(),uploadUrl);
                } else {
                    log.info("GET request not worked , neid = {}" + lianxiangBatchGetViewResp.getNeid());
                }
            } catch (Exception e) {

            } finally {
                try {
                    if (os != null) {
                        os.close();
                    }
                    if (is != null) {
                        is.close();
                    }
                    if(file != null){
                        file.delete();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return resultMap;

    }
}
