package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.AfterSaleProductLabel;

import java.util.List;

public interface AfterSaleProductLabelMapper extends BaseMapper<AfterSaleProductLabel> {

    void updateByProInfo(@Param("productId") String productId, @Param("firstCode") String firstCode, @Param("skcCode") String skcCode);

    void batchInsert(@Param("list") List<AfterSaleProductLabel> inserts);

    List<AfterSaleProductLabel> selectByProductId(@Param("productId") String requestData);
}
