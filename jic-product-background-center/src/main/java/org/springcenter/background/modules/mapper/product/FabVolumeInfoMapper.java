package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.entity.productFab.FabSearchContext;
import org.springcenter.background.modules.model.product.FabVolumeInfo;

import java.util.List;

public interface FabVolumeInfoMapper extends BaseMapper<FabVolumeInfo> {


    List<FabVolumeInfo> selectDisplayIsToday();

    List<FabVolumeInfo> selectByIds(@Param("list") List<String> collect);

    void updateParamsById(@Param("info") FabVolumeInfo fabVolumeInfo);

    List<FabVolumeInfo> selectByParam(@Param("item") FabSearchContext context);
}
