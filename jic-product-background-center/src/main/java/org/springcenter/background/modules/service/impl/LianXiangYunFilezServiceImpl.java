package org.springcenter.background.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.background.modules.service.LianXiangYunFilezService;
import org.springcenter.background.modules.util.HttpUtils;
import org.springcenter.background.modules.util.RedisService;
import org.springcenter.background.modules.util.RedissonUtil;
import org.springcenter.product.api.constant.RedisKeyConstant;
import org.springcenter.product.api.dto.BatchGetViewUrlReq;
import org.springcenter.product.api.dto.LianxiangBatchGetViewReq;
import org.springcenter.product.api.dto.LianxiangBatchGetViewResp;
import org.springcenter.product.api.dto.UploadLianxiangDto;
import org.springcenter.product.api.lenovo.LenovoFileReq;
import org.springcenter.product.api.lenovo.LenovoFileResp;
import org.springcenter.product.api.lenovo.LianXiangTokenDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
@Slf4j
@RefreshScope
public class LianXiangYunFilezServiceImpl implements LianXiangYunFilezService {

    @Autowired
    private RedissonUtil redissonUtil;
    @Autowired
    private RedisService redisService;

    @Value("${lianxiang.appkey}")
    private String appkey;

    @Value("${lianxiang.appSecret}")
    private String appSecret;

    @Value("${lianxiang.token.url}")
    private String tokenUrl;


    @Value("${lianxiang.sync.url}")
    private String syncUrl;

    @Value("${lianxiang.view.url}")
    private String viewUrl;


    @Value("${lianxiang.batchview.url}")
    private String batchViewUrl;


    @Value("${lianxiang.download.url}")
    private String downloadUrl;


    @Value("${lianxiang.upload.url}")
    private String uploadUrl;


    @Override
    public String getAccessToken() {
        Object accessToken = redisService.get(RedisKeyConstant.REDIS_LIANXIANG_ACCESSTOKEN);
        if(accessToken != null){
            return accessToken.toString();
        }
        // 加锁
        boolean flag = redissonUtil.tryLock(RedisKeyConstant.REDIS_LIANXIANG_LOCK);
        if(!flag){
            throw new RuntimeException("未能获取联想云的锁");
        }
        try {
            String headerApp = appkey + ":" + appSecret;
            byte[] encodedBytes = Base64.getEncoder().encode(headerApp.getBytes(StandardCharsets.UTF_8));
            String encodedString = new String(encodedBytes, StandardCharsets.UTF_8);

            // 拼装参数
            Map<String,Object> params = new HashMap<>();
            params.put("grant_type","client_with_su");
            params.put("scope","all");
            params.put("slug","admin");

            Headers headers = new Headers.Builder()
                    .add("Content-Type","application/x-www-form-urlencoded")
                    .add("Authorization","Basic "+encodedString).build();

            String json = HttpUtils.postForm(tokenUrl, params, headers);
            if(StringUtils.isNotBlank(json)){
                // 获取到数据
                LianXiangTokenDto lianXiangTokenDto = JSONObject.parseObject(json, LianXiangTokenDto.class);
                if(StringUtils.isNotBlank(lianXiangTokenDto.getAccess_token())){
                    String expires_in = lianXiangTokenDto.getExpires_in();
                    String value = lianXiangTokenDto.getToken_type() + " " + lianXiangTokenDto.getAccess_token();
                    redisService.set(RedisKeyConstant.REDIS_LIANXIANG_ACCESSTOKEN,
                            value,
                            Long.parseLong(expires_in));
                    return value;
                }
            }
        } finally {
            redissonUtil.unlock(RedisKeyConstant.REDIS_LIANXIANG_LOCK);
        }
        return null;
    }


    public LenovoFileResp getLenovoFileResp(String path,int pageNum) {
        try {
            Thread.sleep(1000);
        }catch (Exception e){
            log.info("睡1000毫秒");
        }
        LenovoFileReq params = new LenovoFileReq();
        params.setPath(path);
        params.setPage_num(pageNum+"");
        LenovoFileResp resp = getLenovoFile(params);
        return resp;
    }

    @Override
    public List<String> batchDownload(List<BatchGetViewUrlReq.LianxiangData> requestData) {
        List<String> result = new ArrayList<>();
        try {
            for (BatchGetViewUrlReq.LianxiangData requestDatum : requestData) {
                Map<String,String> params = new HashMap<>();
                params.put("nsid",requestDatum.getNsid());
                params.put("neid",requestDatum.getNeid());
                Headers headers = new Headers.Builder()
                        .add("Authorization",getAccessToken()).build();

                String finalUrl = HttpUtils.postFormDownLoad(downloadUrl, params, headers);
                result.add(finalUrl);
            }
            return result;  //将以byte数组返回输出流中的有效内容
        }catch (Exception e){
            log.info("batchDownload e = {}",e);
            return null;
        }
    }

    @Override
    public LenovoFileResp getLenovoFile(LenovoFileReq lenovoFileReq) {
        try {
            String jsonString = JSONObject.toJSONString(lenovoFileReq);
            Map<String,Object> params = JSONObject.parseObject(jsonString, Map.class);

            Headers headers = new Headers.Builder()
                    .add("Authorization",getAccessToken()).build();

            String json = HttpUtils.postForm(syncUrl, params, headers);
            if(StringUtils.isNotBlank(json)){
                LenovoFileResp lenovoFileResp = JSONObject.parseObject(json, LenovoFileResp.class);
                return lenovoFileResp;
            }
        }catch (Exception e){
            log.info("getLenovoFile e = {}",e);
             return null;
        }
        return null;
    }

    @Override
    public String getPdfView(String neid, String nsid) {
        try {
            Map<String,Object> params = new HashMap<>();
            params.put("nsid",nsid);

            Headers headers = new Headers.Builder()
                    .add("Authorization",getAccessToken()).build();

            String finalUrl = viewUrl + "/" + neid;

            String json = HttpUtils.postForm(finalUrl, params, headers);
            if(StringUtils.isNotBlank(json)){
                Map<String,String> lenovoFileResp = JSONObject.parseObject(json, Map.class);
                return lenovoFileResp.get("previewUrl");
            }
        }catch (Exception e){
            log.info("getPdfView e = {}",e);
            return null;
        }
        return null;
    }

    @Override
    public String download(String neid, String nsid) {
        try {
            Map<String,String> params = new HashMap<>();
            params.put("nsid",nsid);
            params.put("neid",neid);

            Headers headers = new Headers.Builder()
                    .add("Authorization",getAccessToken()).build();

            String finalUrl = HttpUtils.postFormDownLoad(downloadUrl, params, headers);

            return finalUrl;  //将以byte数组返回输出流中的有效内容
        }catch (Exception e){
            log.info("download e = {}",e);
            return null;
        }
    }

    @Override
    public List<LianxiangBatchGetViewResp> batchGetViewUrl(LianxiangBatchGetViewReq lianxiangBatchGetViewReq) {

        try {
            lianxiangBatchGetViewReq.setPreview_type("original");
            String jsonString = JSONObject.toJSONString(lianxiangBatchGetViewReq);
            Map params = JSONObject.parseObject(jsonString, Map.class);

            Headers headers = new Headers.Builder()
                    .add("Authorization",getAccessToken()).build();

            String finalUrl = batchViewUrl;

            String json = HttpUtils.postForm(finalUrl, params, headers);
            if(StringUtils.isNotBlank(json)){
                Map<String,Object> lenovoFileResp = JSONObject.parseObject(json, Map.class);
                Object list = lenovoFileResp.get("list");
                String jsonString1 = JSONObject.toJSONString(list);
                List<LianxiangBatchGetViewResp> lianxiangBatchGetViewResps = JSONObject.parseArray(jsonString1, LianxiangBatchGetViewResp.class);
                // 去除水印配置
                for (LianxiangBatchGetViewResp lianxiangBatchGetViewResp : lianxiangBatchGetViewResps) {
                    String previewUrl = lianxiangBatchGetViewResp.getPreviewUrl();
                    previewUrl = previewUrl.replaceAll("addWatermark=true", "addWatermark=false");
                    boolean havePng = previewUrl.contains("extension=.png") || previewUrl.contains("extension=.PNG");
                    if(havePng){
                        previewUrl = previewUrl.replaceAll("preview_type=stream", "preview_type=original");
                    }
                    lianxiangBatchGetViewResp.setPreviewUrl(previewUrl);
                }
                return lianxiangBatchGetViewResps;
            }
        }catch (Exception e){
            log.info("batchGetPdfView e = {}",e);
            return null;
        }
        return null;
    }

    @Override
    public UploadLianxiangDto upload(MultipartFile multipartFile, String path) {
        try {

            Map<String,String> params = new HashMap();
            Headers headers = new Headers.Builder()
                    .add("Authorization",getAccessToken())
                    .add("Content-Type","multipart/form-data")
                    .build();
            params.put("path",path);
            params.put("path_type","ent");

            String json = HttpUtils.postFormUpLoad(uploadUrl, params, headers,multipartFile);

            if(StringUtils.isNotBlank(json)){
                UploadLianxiangDto uploadLianxiangDto = JSONObject.parseObject(json, UploadLianxiangDto.class);
                return uploadLianxiangDto;
            }
        }catch (Exception e){
            log.info("upload e = {}",e);
            return null;
        }
        return null;
    }
}
