package org.springcenter.background.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.background.ProductPackageExportEntity;
import org.springcenter.background.modules.model.product.PackageFilterInfo;
import org.springcenter.background.modules.model.product.PackageImportInfo;
import org.springcenter.background.modules.model.product.PackageTemplate;
import org.springcenter.product.api.dto.ProductPackageResp;
import org.springcenter.product.api.dto.background.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/7/25 14:14
 */
public interface IProductPackageService {
    /**
     * 返回筛选条件数据
     * @return 返回
     */
    ProductPackageResp getFilters();

    /**
     * 增加商品包
     * @param requestData 入参
     * @return 返回
     */
    AddProductPackageResp addProductPackage(AddProductPackageReq requestData);

    /**
     * 查询商品包详情
     * @param requestData 商品包详情的id
     * @return 返回
     */
    ProductPackageDetailResp searchProductPackageDetail(ProductPackageDetailReq requestData);

    /**
     * 更新商品包详情
     * @param requestData 入参
     * @return 返回
     */
    Boolean updateProductPackageDetail(UpdateProductPackageReq requestData);


    /**
     * 获取门店包列表
     * @param requestData 入参
     * @param page 分页
     * @return 返回
     */
    List<ProdPackageListResp> getProductPackageList(ProdPackageListReq requestData, Page page, String component);

    /**
     * 停用or启用商品包
     * @param requestData 操作
     * @return 返回msg
     */
    Boolean switchProductPackageStatus(SwitchPackageStatusReq requestData);

    /**
     * 根据商品包主键下载商品包
     * @param requestData 商品包主键
     * @return 返回
     */
    String downloadProductPackageStatus(String requestData);

    /**
     * 根据商品主键id
     * @param requestData 商品主键id
     * @return 操作记录
     */
    List<ProductPackageOperatorsListResp> getProductPackageOperators(String requestData, Page page);

    /**
     * 根据商品包id给c获取商品包的商品
     * @param requestData 商品包id
     * @return 返回有效数据
     */
    List<ProductPackageExportEntity> getProductPackageForc(String requestData);

    /**
     * 根据参数去查找模糊匹配的数据
     * @param requestData 入参
     * @return 返回
     */
    List<ProductPackageResp.SmallFilterData> getParamByField(SearchPacParamReq requestData);

    /**
     * 根据商品包和商品 判断商品包包含哪些商品
     * @param requestData 入参
     * @return 返回
     */
    List<SearchProductIdInPacResp> getPackageProductIdsForc(SearchProductIdInPacReq requestData);

    /**
     * 查询商品id是否存在入参的商品包中
     * @param requestData 入参
     * @return 返回
     */
    List<SearchProductIdIsExistResp> getPackageIsExistProductIdsForc(SearchProductIdInPacReq requestData);

    /**
     * 缓存商品包信息
     */
    void cashPackageInfoJob(String pacId);

    /**
     * 获取商品包信息
     * @param template 商品模板
     * @param filterInfos 过滤信息
     * @param importInfos 导入信息
     * @return 返回
     */
    List<ProductPackageExportEntity> dealEsInfo(PackageTemplate template, List<PackageFilterInfo> filterInfos,
                                                       List<PackageImportInfo> importInfos);

    /**
     * 根据商品包id查询商品包名称
     * @param requestData 商品包id
     * @return 返回
     */
    List<ProdPackageListResp> queryPacListByPacId(List<String> requestData);
}
