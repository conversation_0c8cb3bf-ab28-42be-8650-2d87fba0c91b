package org.springcenter.background.modules.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 人台图
 */
@Data
public class DisplayBookMannequinPictureEntity {

    @ApiModelProperty(value = "照片ID")
    @ExcelProperty("照片ID")
    private String pictureId;

    @ApiModelProperty(value = "照片波段")
    @ExcelProperty("照片波段")
    private String bandName;

    @ApiModelProperty(value = "人台编号")
    @ExcelProperty("人台编号")
    private Integer groupNumber;

    @ApiModelProperty(value = "人台款色号")
    @ExcelProperty("人台款色号")
    private String skcCode;

    @ApiModelProperty(value = "错误信息")
    @ExcelProperty("错误信息")
    private String errorMsg;
}
