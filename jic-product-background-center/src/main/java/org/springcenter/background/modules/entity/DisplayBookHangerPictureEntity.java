package org.springcenter.background.modules.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 货杆图
 */
@Data
public class DisplayBookHangerPictureEntity {

    @ApiModelProperty(value = "货杆分组")
    @ExcelProperty(value = "货杆分组")
    private Integer groupNumber;

    @ApiModelProperty(value = "照片波段")
    @ExcelProperty(value = "照片波段")
    private String bandName;

    @ApiModelProperty(value = "货杆图ID")
    @ExcelProperty(value = "货杆图ID")
    private String pictureId;

    @ApiModelProperty(value = "货杆款色号")
    @ExcelProperty(value = "货杆款色号")
    private String skcCode;

    @ApiModelProperty(value = "错误信息")
    @ExcelProperty("错误信息")
    private String errorMsg;
}
