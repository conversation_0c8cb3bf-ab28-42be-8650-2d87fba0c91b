package org.springcenter.background.modules.entity.productFab;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2022/12/6 11:07
 */
@Data
public class ProductVersionEntity {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "图片")
    private String picUrl;

    @ApiModelProperty(value = "样衣号")
    private String sampleNo;

    @ApiModelProperty(value = "版型号")
    private String modelNumber;

    @ApiModelProperty(value = "合体度名称")
    private String degree;

    @ApiModelProperty(value = "引用次数")
    private String citationNumber;

    @ApiModelProperty(value = "类目")
    private String category;

    @ApiModelProperty(value = "小品名")
    private String smallName;
}
