package org.springcenter.background.modules.model.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PRODUCT_DETAIL_NET_DISK_IMG
 * <AUTHOR>
@Data
public class ProductDetailNetDiskImg implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 款号
     */
    private String productCode;

    /**
     * 颜色编码
     */
    private String colorNo;

    /**
     * 是否删除  0 正常  1 删除
     */
    private Integer isDel;

    /**
     * NEID
     */
    private String neid;

    /**
     * NSID
     */
    private String nsid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 类型  0 单品主图   1 细节图  2 商品类目图
     */
    private Integer type;

    /**
     * 七牛图片存储地址
     */
    private String qiniuImgPath;

    @ApiModelProperty(value = "0 非海外  1 海外")
    private Integer overseaFlag;

    private static final long serialVersionUID = 1L;
}