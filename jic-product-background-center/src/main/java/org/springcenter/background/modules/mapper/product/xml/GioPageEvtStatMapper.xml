<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.GioPageEvtStatMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.GioPageEvtStat">
        <result column="VISIT_USER_ID" property="visitUserId" />
        <result column="SESSION_ID" property="sessionId" />
        <result column="PLATFORM" property="platform" />
        <result column="DOMAIN" property="domain" />
        <result column="PAGE" property="page" />
        <result column="QUERY_PARAMETERS" property="queryParameters" />
        <result column="REFERRER" property="referrer" />
        <result column="REFERRER_PAGE" property="referrerPage" />
        <result column="TITLE" property="title" />
        <result column="TIME" property="time" />
        <result column="SEND_TIME" property="sendTime" />
        <result column="PAGE_TIME" property="pageTime" />
        <result column="LOGIN_USER_ID" property="loginUserId" />
        <result column="PAGE_GROUP" property="pageGroup" />
        <result column="PAGE_REQUEST_ID" property="pageRequestId" />
        <result column="CREATE_DATE" property="createDate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CREATE_DATE,VISIT_USER_ID, SESSION_ID, PLATFORM, DOMAIN, PAGE, QUERY_PARAMETERS, REFERRER, REFERRER_PAGE, TITLE, TIME, SEND_TIME, PAGE_TIME, LOGIN_USER_ID, PAGE_GROUP, PAGE_REQUEST_ID
    </sql>

</mapper>