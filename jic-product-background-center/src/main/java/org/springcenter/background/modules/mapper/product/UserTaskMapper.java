package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.entity.UserTaskEntity;
import org.springcenter.background.modules.model.product.FabVolumeInfo;
import org.springcenter.product.api.dto.FabTaskCheckListResp;

import java.util.List;

public interface UserTaskMapper extends BaseMapper<UserTaskEntity> {

    List<UserTaskEntity> getTaskIdInfo(@Param("hrIds") List<Long> hrId, @Param("storeIds") List<Long> storeId,
                                       @Param("volumeIds") List<String> volumeId);

    List<FabTaskCheckListResp> getCheckTaskList(@Param("brandId") Long brandId, @Param("regionId") Long regionId,
                                                @Param("cityId") Long cityId, @Param("storeId") Long storeId);

    String selectByIdInData(@Param("id") long l);

    String selectTaskCheckReport(@Param("taskId") Long taskId);

    String selectMainItemIdById(@Param("id") Long taskId);
}
