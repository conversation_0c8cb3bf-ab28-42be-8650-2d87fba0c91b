package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.entity.productFab.KeyInfoParamsEntity;
import org.springcenter.background.modules.entity.productFab.KeyInfoSpuEntity;
import org.springcenter.background.modules.model.product.KeyCombinations;

import java.util.List;

public interface KeyCombinationsMapper extends BaseMapper<KeyCombinations> {


    /**
     * 根据款号查询套装号
     * @param name 款号
     * @return 返回套装数据
     */
    List<KeyInfoParamsEntity> selectKeyCombinationsBySuit(@Param("name") String name);

    /**
     * 根据套装相关信息查询整套相关搭配信息
     * @param suitNumbers 品牌、货季、套装
     * @return 返回
     */
    List<KeyInfoSpuEntity> selectByBrandAndGoodSeasonAndSuit(@Param("list") List<KeyInfoParamsEntity> suitNumbers,
                                                             @Param("name") String name);

    /**
     * 根据款号来查询当前的重点搭配
     * @param nameList 款号
     * @return 返回
     */
    List<KeyInfoParamsEntity> selectKeyCombinationsByNames(@Param("list") List<String> nameList);
}
