package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2024/7/15 13:57
 */
@Data
@TableName(value = "AFTER_SALE_PRODUCT_LABEL")
public class AfterSaleProductLabel {

    @TableField(value = "ID")
    private String id;

    @TableField(value = "PRODUCT_ID")
    private String productId;

    @TableField(value = "NAME")
    private String name;

    @TableField(value = "SKC_CODE")
    private String skcCode;

    @TableField(value = "FIRST_LABEL_CODE")
    private String firstLabelCode;

    @TableField(value = "SECOND_LABEL_CODE")
    private String secondLabelCode;

    @TableField(value = "THIRD_LABEL_CODE")
    private String thirdLabelCode;

    @TableField(value = "TYPE")
    @ApiModelProperty(value = "0导入 1手动 2自动")
    private Integer type;

    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 0正常 1已删除")
    private Integer isDeleted;

    @TableField(value = "UPDATER")
    private String updater;
}
