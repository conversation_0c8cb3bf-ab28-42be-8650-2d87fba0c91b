package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2025/1/8 14:24
 */
@Data
@TableName(value = "ENVIRONMENTAL_FABRIC_SETTING")
public class EnvironmentalFabricSetting {

    @TableId(value = "ID")
    private Integer id;

    @TableField(value = "SYSTEM_NAME")
    private String systemName;

    @TableField(value = "OUTSIDE_NAME")
    private String outsideName;

    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;
}
