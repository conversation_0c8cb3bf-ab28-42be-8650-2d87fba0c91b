package org.springcenter.background.modules.mapper.bojun;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.bojun.BoxMProduct;
import org.springcenter.background.modules.model.bojun.MProduct;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/3/11 13:26
 * @description
 */
public interface MProductMapper extends BaseMapper<BoxMProduct> {

    List<MProduct> selectByNames(@Param("list") List<String> nameList);

    MProduct selectByOrigName(@Param("name") String name);
}
