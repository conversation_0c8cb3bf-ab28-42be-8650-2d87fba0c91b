package org.springcenter.background.modules.remote.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springcenter.background.modules.remote.IBigDataRecommendRemoteApi;
import org.springcenter.background.modules.remote.entity.BigDataComprehensiveData;
import org.springcenter.background.modules.remote.entity.BigDataComprehensiveReq;
import org.springcenter.background.modules.remote.entity.BigDataComprehensiveResponse;
import org.springcenter.background.modules.remote.service.IBigDataRecommendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2025/1/21 9:45
 */
@Service
@Slf4j
public class IBigDataRecommendServiceImpl implements IBigDataRecommendService {

    @Autowired
    private IBigDataRecommendRemoteApi bigDataHttpApi;

    @Override
    public Pair<List<BigDataComprehensiveData>, Integer> searchComprehensiveList(BigDataComprehensiveReq bigDataComprehensiveReq) {
        log.info("======================请求大数据综合排序接口：{}", JSONObject.toJSONString(bigDataComprehensiveReq));
        List<BigDataComprehensiveData> entities = new ArrayList<>();
        try {
            Response<BigDataComprehensiveResponse> responseCall = bigDataHttpApi.searchComprehensiveList(bigDataComprehensiveReq).execute();
            log.info("=======================返回参数：{}", JSONObject.toJSONString(responseCall));
            if (!responseCall.isSuccessful()) {
                log.error("=============================请求大数据综合排序接口报错，第一步：{}", JSONObject.toJSONString(responseCall));
                return Pair.of(entities, 0);
            }
            if (!Objects.equals(responseCall.body().getCode(), 200)) {
                log.error("=============================请求大数据综合排序接口报错，第二步", JSONObject.toJSONString(responseCall));
                return Pair.of(entities, 0);
            }
            return Pair.of(responseCall.body().getResult(), responseCall.body().getTotal());
        } catch (Exception e) {
            log.error("============================请求大数据接口报错，e:{}", e);
            return Pair.of(entities, 0);
        }
    }
}
