package org.springcenter.background.modules.service;

import org.springcenter.product.api.dto.AfterSalesAddTagReq;
import org.springcenter.product.api.dto.AfterSalesResp;
import org.springcenter.product.api.dto.AfterSalesTagResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/6/25 16:33
 */
public interface IAfterSaleService {
    /**
     * 根据款号查询售后信息
     * @param requestData 款号
     * @return 返回
     */
    AfterSalesResp searchAfterSaleInfo(String requestData);

    /**
     * 增加skc的售后标签信息
     * @param requestData 入参
     * @return 返回
     */
    Boolean addSkcSaleTagInfo(List<AfterSalesAddTagReq> requestData);

    /**
     * 根据商品id查询商品标签
     * @param requestData 商品id
     * @return 返回
     */
    List<AfterSalesTagResp> searchSkcSaleTagInfo(String requestData);
}
