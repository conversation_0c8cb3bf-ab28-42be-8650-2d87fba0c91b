package org.springcenter.background.modules.mapper.product;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.SampleClothNetDiskImg;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【SAMPLE_CLOTH_NET_DISK_IMG】的数据库操作Mapper
* @createDate 2024-04-22 15:07:50
* @Entity generator.domain.SampleClothNetDiskImg
*/
public interface SampleClothNetDiskImgMapper extends BaseMapper<SampleClothNetDiskImg> {


    List<SampleClothNetDiskImg> selectSampleClothCodeAndName(@Param("sampleClothCode") String sampleClothCode,
                                                             @Param("name") String name,
                                                             @Param("isDel") Integer isDel);

    List<SampleClothNetDiskImg> selectbySampleCloths(@Param("sampleCloths") List<String> sampleCloths);
}
