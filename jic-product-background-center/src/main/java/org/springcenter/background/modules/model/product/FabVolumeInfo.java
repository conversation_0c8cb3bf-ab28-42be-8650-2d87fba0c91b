package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/11/14 9:34
 */
@TableName(value = "FAB_VOLUME_INFO")
@Data
public class FabVolumeInfo {

    @TableId(value = "ID")
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField(value = "BRAND_CODE")
    @ApiModelProperty(value = "品牌id")
    private Long brandCode;

    @TableField(value = "BRAND_NAME")
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @TableField(value = "BAND_ID")
    @ApiModelProperty(value = "波段id")
    private Long bandId;

    @TableField(value = "BAND_NAME")
    @ApiModelProperty(value = "波段名称")
    private String bandName;

    @TableField(value = "SEASON_GOODS")
    @ApiModelProperty(value = "货季")
    private String seasonGoods;

    @TableField(value = "YEAR")
    @ApiModelProperty(value = "年份")
    private String year;

    @TableField(value = "DISPLAY_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "展示时间")
    private Date displayTime;

    @TableField(value = "OPERATOR")
    @ApiModelProperty(value = "操作人")
    private String operator;

    @TableField(value = "UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 0正常 1已删除")
    private Integer isDeleted;

    @TableField(value = "CLOB_JSON")
    @ApiModelProperty(value = "JSON")
    private String clobJson;

    @TableField(value = "FAB_NAME")
    @ApiModelProperty(value = "产品册名称")
    private String fabName;

    @TableField(value = "BAND_ORDER")
    @ApiModelProperty(value = "波段排序")
    private Integer bandOrder;

    @TableField(value = "TYPE")
    @ApiModelProperty(value = "产品册类型 0直营 1经销")
    private Integer type;
}
