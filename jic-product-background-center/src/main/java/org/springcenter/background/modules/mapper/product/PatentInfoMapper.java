package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.PatentInfo;

import java.util.List;


public interface PatentInfoMapper extends BaseMapper<PatentInfo> {


    void batchUpdateByIds(@Param("list") List<PatentInfo> list);

    void batchInsert(@Param("list") List<PatentInfo> list);

    List<PatentInfo> selectByProductId(@Param("productId") String requestData);
}
