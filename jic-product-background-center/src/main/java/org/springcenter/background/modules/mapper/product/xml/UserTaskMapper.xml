<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.UserTaskMapper">


    <select id="getTaskIdInfo" resultType="org.springcenter.background.modules.entity.UserTaskEntity">
        select b.Id as taskId, b.STOREID as cStoreId, b.DEALERID as hrId,
        b.TASKDEALER as hrName, a.MEDIACONTENT as volumeId, c.UNIQUEID as campaignId
            from USERTASK.TASKMEDIADETAIL a
                 LEFT JOIN USERTASK.TASKALLOCATE b  ON a.TASKDEFINEID = b.TASKDEFINEID
                 LEFT JOIN USERTASK.TASKDEFINE  c ON a.TASKDEFINEID = c.ID
        WHERE c.OPENSTATUS = 0 AND c.TASKTYPE = 14
        <if test="storeIds != null and storeIds.size() > 0">
            and b.STOREID in
            <foreach collection="storeIds" item="storeId" open="(" close=")" separator=",">
                #{storeId}
            </foreach>
        </if>
        <if test="hrIds != null and hrIds.size() > 0">
            and b.DEALERID in
            <foreach collection="hrIds" item="hrId" open="(" close=")" separator=",">
                #{hrId}
            </foreach>
        </if>
        <if test="volumeIds != null and volumeIds.size() > 0">
            and a.MEDIACONTENT in
            <foreach collection="volumeIds" item="volumeId" open="(" close=")" separator=",">
                #{volumeId}
            </foreach>
        </if>
          AND a.MediaType = 9
    </select>

    <select id="getCheckTaskList" resultType="org.springcenter.product.api.dto.FabTaskCheckListResp">
        SELECT
            a.id AS id,
            c.C_ARCBRAND_ID AS cArcBrandId,
            c.C_AREA_ID as regionId,
            c.C_CITY_ID as cityId,
            c.ID as cStoreId,
            c.NAME as storeName,
            a.TASKDEALER as saleName,
            a.DEALERID as hrId,
            t.FEEDBACKDATE as feedbackTime,
            t.FEEDBACKUSER as feedbackPeople,
            t.MATERIALS as feedbackJson,
            t.FEEDBACKCONTENT as feedbackSuggestion,
            d.FAB_VOLUME_ID as volumeId
        FROM
                FAB_VOLUME_DISTRIBUTE_TASK_LOG d
                LEFT JOIN usertask.taskdefine b ON d.CAMPAIGN_ID = b.UNIQUEID
                LEFT JOIN usertask.taskallocate a ON a.taskdefineid = b.id
                LEFT JOIN usertask.TASKCHECKREPORT t ON t.TASKID = a.id
                LEFT JOIN c_store@bojun c ON a.STOREID = c.ID
        WHERE
            d.TASK_TYPE = 1
          AND a.STARTDATE IS NOT NULL
          AND b.id IS NOT NULL
          AND b.openstatus = 0
          AND b.TASKTYPE = 15
          AND c.ISACTIVE = 'Y'
        <if test="brandId != null and brandId !=''">
            AND c.C_ARCBRAND_ID = #{brandId}
        </if>
        <if test="regionId != null and regionId !=''">
            AND c.C_AREA_ID = #{regionId}
        </if>
        <if test="cityId != null and cityId !=''">
            AND c.C_CITY_ID = #{cityId}
        </if>
        <if test="storeId != null and storeId !=''">
            AND c.ID = #{storeId}
        </if>
        order by d.CREATE_TIME desc
    </select>
    <select id="selectByIdInData" resultType="java.lang.String">
        SELECT ID FROM usertask.taskdefine where id = #{id}
    </select>


    <select id="selectTaskCheckReport" resultType="java.lang.String">
        select ID from usertask.TASKCHECKREPORT where TASKID = #{taskId}
    </select>

    <select id="selectMainItemIdById" resultType="java.lang.String">
        select TASKDEFINEID from usertask.taskallocate where id = #{id}
    </select>
</mapper>



