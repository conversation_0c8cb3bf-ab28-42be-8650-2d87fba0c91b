package org.springcenter.background.modules.service.impl.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.background.config.exception.ProductException;
import org.springcenter.background.modules.entity.DisplayBookMannequinPictureEntity;

import java.util.*;

@Slf4j
public class DisplayBookMannequinPictureListener implements ReadListener<DisplayBookMannequinPictureEntity> {

    private HashMap<String, String> bandNameSeasonConfigMap;
    private String season;

    private List<DisplayBookMannequinPictureEntity> importDataList = new ArrayList<>();
    private List<DisplayBookMannequinPictureEntity> errorList = new ArrayList<>();

    public List<DisplayBookMannequinPictureEntity> getImportDataList() {
        return importDataList;
    }

    public List<DisplayBookMannequinPictureEntity> getErrorList() {
        return errorList;
    }

    public DisplayBookMannequinPictureListener(HashMap<String, String> bandNameSeasonConfigMap, String season) {
        this.bandNameSeasonConfigMap = bandNameSeasonConfigMap;
        this.season = season;
    }

    /**
     * 表头处理
     */
    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
//        log.info("headMap= {}", headMap);
        if (headMap.size() != 4) {
            throw new ProductException("表头校验出错，请使用模版文件内设定的格式上传！");
        }
        if (!Objects.equals("照片ID", headMap.get(0).getStringValue())) {
            throw new ProductException("第1列必须为照片ID，请使用模版文件内设定的格式上传！");
        }
        if (!Objects.equals("照片波段", headMap.get(1).getStringValue())) {
            throw new ProductException("第2列必须为照片波段，请使用模版文件内设定的格式上传！");
        }
        if (!Objects.equals("人台编号", headMap.get(2).getStringValue())) {
            throw new ProductException("第3列必须为人台编号，请使用模版文件内设定的格式上传！");
        }
        if (!Objects.equals("人台款色号", headMap.get(3).getStringValue())) {
            throw new ProductException("第4列必须为人台款色号，请使用模版文件内设定的格式上传！");
        }
    }

    /**
     * 按行处理
     */
    @Override
    public void invoke(DisplayBookMannequinPictureEntity data, AnalysisContext analysisContext) {
        // 过滤空行
        if (StringUtils.isBlank(data.getPictureId()) && StringUtils.isBlank(data.getBandName()) && Objects.isNull(data.getGroupNumber()) && Objects.isNull(data.getSkcCode())) {
            return;
        }
        if (StringUtils.isBlank(data.getPictureId()) || StringUtils.isBlank(data.getBandName()) || Objects.isNull(data.getGroupNumber())) {
            data.setErrorMsg("照片ID、照片波段、人台编号均不能为空");
            errorList.add(data);
            return;
        }
        // 清除前后空格
        data.setPictureId(data.getPictureId().trim());
        data.setBandName(data.getBandName().trim());
        data.setSkcCode(StringUtils.isNotBlank(data.getSkcCode()) ? data.getSkcCode().trim() : null);
        if (Objects.isNull(bandNameSeasonConfigMap.get(data.getBandName()))) {
            data.setErrorMsg("照片波段不合法，仅支持1-A至12-C中的36个波段");
            errorList.add(data);
            return;
        }
        if (!Objects.equals(bandNameSeasonConfigMap.get(data.getBandName()), season)) {
            data.setErrorMsg("照片波段不在当前货季");
            errorList.add(data);
            return;
        }
        if (data.getGroupNumber() < 1 || data.getGroupNumber() > 4) {
            data.setErrorMsg("单个照片人台编号不能超过4");
            errorList.add(data);
            return;
        }
        importDataList.add(data);
    }

    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        ReadListener.super.extra(extra, context);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        return ReadListener.super.hasNext(context);
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        throw exception;
    }
}
