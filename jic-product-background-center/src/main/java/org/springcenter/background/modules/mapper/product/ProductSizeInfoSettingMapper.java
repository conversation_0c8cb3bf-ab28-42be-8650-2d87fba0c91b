package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.ProductSizeInfoSetting;

import java.util.List;


public interface ProductSizeInfoSettingMapper extends BaseMapper<ProductSizeInfoSetting> {


    List<ProductSizeInfoSetting> selectBrandAndSmallClassInfo(@Param("brandId") Integer c_arcbrand_id, @Param("smallClassId") Integer small_class_id);

    List<ProductSizeInfoSetting> selectBrandIdAndCategory(@Param("brandIds") List<Long> brandIds);
}
