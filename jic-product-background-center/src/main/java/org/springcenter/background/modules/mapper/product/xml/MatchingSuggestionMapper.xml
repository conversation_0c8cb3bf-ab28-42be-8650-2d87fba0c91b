<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.MatchingSuggestionMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.MatchingSuggestion">
        <id column="ID" property="id" />
        <result column="OLD_SUGGESTION" property="oldSuggestion" />
        <result column="NEW_SUGGESTION" property="newSuggestion" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, OLD_SUGGESTION, NEW_SUGGESTION, CREATE_TIME, UPDATE_TIME, IS_DELETED
    </sql>
    <select id="selectByMatch" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM MATCHING_SUGGESTION
        WHERE IS_DELETED = 0 AND to_char(OLD_SUGGESTION) = #{match}
    </select>
</mapper>
