package org.springcenter.background.modules.service.impl.autoName;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.beust.jcommander.internal.Lists;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.background.modules.entity.productFab.CommonAutoNameSettingEntity;
import org.springcenter.background.modules.enums.AutoNameEnum;
import org.springcenter.background.modules.enums.CpxfEnum;
import org.springcenter.background.modules.mapper.product.FabAnDynamicFieldSettingMapper;
import org.springcenter.background.modules.mapper.product.FabAutoNameSettingMapper;
import org.springcenter.background.modules.model.product.FabAnDynamicFieldSetting;
import org.springcenter.background.modules.model.product.FabAutoNameSetting;
import org.springcenter.background.modules.service.CommonAutoName;
import org.springcenter.background.modules.service.FabVolumeInfoService;
import org.springcenter.background.modules.util.EsUtil;
import org.springcenter.product.api.dto.FabSpuForAutoNameEsResp;
import org.springcenter.product.api.dto.FabSpuForAutoNameResp;
import org.springcenter.background.modules.entity.productFab.CommonAutoNameGenerateEntity;
import org.springcenter.background.modules.entity.productFab.CommonSearchByPageAndIdEntity;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/1/17 14:53
 */
@Service
@Slf4j
public class CommonAutoNameImpl implements CommonAutoName {

    @Autowired
    private FabAutoNameSettingMapper fabAutoNameSettingMapper;

    @Autowired
    private FabAnDynamicFieldSettingMapper fabAnDynamicFieldSettingMapper;

    @Autowired
    private FabVolumeInfoService fabVolumeInfoService;

    @Autowired
    private EsUtil esUtil;

    @Override
    public CommonAutoNameSettingEntity getAutoNameSettingInfo(Integer code) {
        CommonAutoNameSettingEntity commonAutoNameSettingEntity = new CommonAutoNameSettingEntity();
        QueryWrapper<FabAutoNameSetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", 0);
        queryWrapper.eq("AUTO_TYPE", code);
        List<FabAutoNameSetting> fabAutoNameSettings = fabAutoNameSettingMapper.selectList(queryWrapper);
        List<FabAutoNameSetting> partSortedList = fabAutoNameSettings.stream()
                .sorted(Comparator.comparing(FabAutoNameSetting::getAssemblePartOrder)).collect(Collectors.toList());

        QueryWrapper<FabAnDynamicFieldSetting> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("IS_DELETED", 0);
        queryWrapper1.eq("AUTO_TYPE", code);
        List<FabAnDynamicFieldSetting> fabAnDynamicFieldSettings = fabAnDynamicFieldSettingMapper.selectList(queryWrapper1);

        // 违禁词查询
        List<String> forbiddenList = fabVolumeInfoService.queryFabProhibitedWords();

        commonAutoNameSettingEntity.setFabAnDynamicFieldSettings(fabAnDynamicFieldSettings);
        commonAutoNameSettingEntity.setForbiddenList(forbiddenList);
        commonAutoNameSettingEntity.setPartSortedList(partSortedList);
        return commonAutoNameSettingEntity;
    }

    @Override
    public void getAutoGenerate(CommonAutoNameGenerateEntity entity, String tag) {
        entity.getPartSortedList().forEach(x -> {
            // 样衣自动品名特殊处理 去除系列、品牌、货季  大货自动品名需要
            if (StringUtils.isNotBlank(tag) &&
                    (Objects.equals("file1_name", x.getField()) ||
                            Objects.equals("d_pp", x.getField()) ||
                            Objects.equals("good_season", x.getField()))) {
                return;
            }
            String fieldValue = getFieldValue(x.getField(), entity.getResp());
            if (StringUtils.isBlank(fieldValue)) {
                return;
            }

            // 微商城的当季往季需要特殊处理 当季小类加| 往季品牌加|
            if (Objects.equals(entity.getIsInCycle(), "Y") &&
                    (entity.getAutoNameEnum() == AutoNameEnum.WSC) && Objects.equals(x.getFieldName(), "小类")) {
                fieldValue = fieldValue + "｜";
            }

            if (Objects.equals(entity.getIsInCycle(), "N") &&
                    (entity.getAutoNameEnum() == AutoNameEnum.WSC) && Objects.equals(x.getField(), "d_pp")) {
                fieldValue = fieldValue + "｜";
            }


            // 判断当前是动态字段还是静态字段
            List<FabSpuForAutoNameResp> dynamicOrStaticList = new ArrayList<>();
            if (Objects.equals(x.getType(), 1)) {
                String finalFieldValue = fieldValue;
                List<FabAnDynamicFieldSetting> fabAnDynamicFieldSettingList = entity.getFabAnDynamicFieldSettings()
                        .stream()
                        .filter(u -> Objects.equals(u.getFabAutoNameId(), x.getId()))
                        .filter(u -> Objects.equals(u.getBelongAttr(), finalFieldValue))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(fabAnDynamicFieldSettingList) && !Objects.equals(finalFieldValue, "空")) {
                    if (entity.getAutoNameEnum() == AutoNameEnum.WSC || entity.getAutoNameEnum() == AutoNameEnum.DS) {
                        fabAnDynamicFieldSettingList = entity.getFabAnDynamicFieldSettings()
                                .stream()
                                .filter(u -> Objects.equals(u.getFabAutoNameId(), x.getId()))
                                .filter(u -> StringUtils.isBlank(u.getBelongAttr()))
                                .collect(Collectors.toList());
                    } else {
                        log.error("==============未找到该字段的动态字段：{}, 值为：{}", JSONObject.toJSONString(x), finalFieldValue);
                        return;
                    }
                }

                fabAnDynamicFieldSettingList.forEach(u -> {
                    if (Objects.equals(u.getType(), 1)) {
                        String fieldValue1 = getFieldValue(u.getField(), entity.getResp());
                        if (StringUtils.isBlank(fieldValue1)) {
                            return;
                        }
                        // 处理cfjx
                        if (Objects.equals(u.getField(), "cfjx") && StringUtils.isBlank(tag)) {
                            fieldValue1 = getCfjxValue(fieldValue1);
                        }
                        FabSpuForAutoNameResp fabSpuForAutoNameResp = new FabSpuForAutoNameResp();
                        BeanUtils.copyProperties(x, fabSpuForAutoNameResp);
                        fabSpuForAutoNameResp.setDataValue(fieldValue1);
                        fabSpuForAutoNameResp.setAssembleWholeOrder(u.getWholeOrder());
                        fabSpuForAutoNameResp.setAssemblePartOrder(u.getPartOrder());
                        fabSpuForAutoNameResp.setField(u.getField());
                        dynamicOrStaticList.add(fabSpuForAutoNameResp);
                    } else {
                        FabSpuForAutoNameResp fabSpuForAutoNameResp = new FabSpuForAutoNameResp();
                        BeanUtils.copyProperties(x, fabSpuForAutoNameResp);
                        fabSpuForAutoNameResp.setDataValue(u.getFieldName());
                        fabSpuForAutoNameResp.setAssembleWholeOrder(u.getWholeOrder());
                        fabSpuForAutoNameResp.setAssemblePartOrder(u.getPartOrder());
                        dynamicOrStaticList.add(fabSpuForAutoNameResp);
                    }
                });

            } else {
                FabSpuForAutoNameResp fabSpuForAutoNameResp = new FabSpuForAutoNameResp();
                BeanUtils.copyProperties(x, fabSpuForAutoNameResp);
                // 处理cfjx
                if (Objects.equals(x.getField(), "cfjx") && StringUtils.isBlank(tag)) {
                    fabSpuForAutoNameResp.setDataValue(getCfjxValue(fieldValue));
                } else {
                    fabSpuForAutoNameResp.setDataValue(fieldValue);
                }
                fabSpuForAutoNameResp.setAssembleWholeOrder(x.getAssembleWholeOrder());
                fabSpuForAutoNameResp.setAssemblePartOrder(x.getAssemblePartOrder());
                fabSpuForAutoNameResp.setField(x.getField());
                dynamicOrStaticList.add(fabSpuForAutoNameResp);
            }

            if (CollectionUtils.isEmpty(dynamicOrStaticList)) {
                return;
            }


            dynamicOrStaticList.forEach(u -> {
                // 图案（属于动态字段组合）和品牌字段值重合，体型（属于动态字段组合）和品名字段值重合
                if (Objects.equals(u.getField(), "d_pp")) {
                    entity.getDpp().set(u.getDataValue());
                }
                if (Objects.equals(u.getField(), "adjust_name")) {
                    entity.getDesignName().set(u.getDataValue());
                }

                if (Objects.equals(u.getField(), "pattern_name") && StringUtils.isNotBlank(entity.getDpp().get())
                        && entity.getDpp().get().contains(u.getDataValue())) {
                    return;
                }
                if (Objects.equals(u.getField(), "tixing") && StringUtils.isNotBlank(entity.getDesignName().get())
                        && entity.getDesignName().get().contains(u.getDataValue())) {
                    return;
                }

                // 违禁词 处理
                if (entity.getForbiddenList().contains(u.getDataValue())) {
                    return;
                }
                if (StringUtils.isBlank(u.getDataValue()) || Objects.equals("null", u.getDataValue())) {
                    return;
                }

                int fieldChar = u.getDataValue().length();
                List<String> partValues = entity.getPartList().stream().map(FabSpuForAutoNameResp::getDataValue)
                        .collect(Collectors.toList());
                List<String> wholeValues = entity.getWholeList().stream().map(FabSpuForAutoNameResp::getDataValue)
                        .collect(Collectors.toList());
                if (entity.getCharLen()[0] + fieldChar <= entity.getSize()) {
                    if (partValues.contains(u.getDataValue()) || wholeValues.contains(u.getDataValue())) {
                        return;
                    }
                    entity.getPartList().add(u);
                    entity.getWholeList().add(u);
                    entity.getCharLen()[0] = entity.getCharLen()[0] + fieldChar;
                } else {
                    if (wholeValues.contains(u.getDataValue())) {
                        return;
                    }
                    entity.getWholeList().add(u);
                }
            });
        });
    }

    /*public static void main(String[] args) {
        System.out.println(getCfjxValue("面料:脚面:粘纤72%聚酯纤维25.6%氨纶2.4%"));
    }*/

    private String getCfjxValue(String fieldValue1) {
        if (StringUtils.isBlank(fieldValue1)) {
            return "";
        }

        // 如果不存在数字 且不存在面料二字
        List<String> list = Lists.newArrayList("全棉", "全麻", "竹纤维", "全羊毛", "全羊绒", "全兔毛", "全羊驼毛", "全马海毛", "全驼绒", "全牦牛毛", "全真丝",
                "全柞蚕丝", "人丝", "莫代尔", "莱赛尔", "铜氨", "醋酸", "竹纤维", "全貂绒", "全浣熊绒", "全貉绒", "牛皮革", "羊皮革", "羊毛皮革",
                "绵羊皮革", "羊毛皮", "兔毛皮", "羊毛革", "巴素兰羊毛", "美丽诺羊毛");
        if (list.contains(fieldValue1)) {
            return fieldValue1;
        }

        // 存在数字的 提取数字
        Pattern p = Pattern.compile("\\d+");
        Matcher m = p.matcher(fieldValue1);
        List<Integer> dataList = new ArrayList<>();
        while (m.find()) {
            String group = m.group();
            Integer num = Integer.valueOf(group);
            dataList.add(num);
        }

        String[] split = StringUtils.split(fieldValue1, ":");
        if (split.length == 1) {
            return "";
        }

        String[] splitPer = StringUtils.split(split[split.length -1], "%");
        List<String> dataValueList = new ArrayList<>();
        for (int i = 0; i < splitPer.length; i++) {
            Pattern p1 = Pattern.compile("(.*?)(?:\\d+)");
            Matcher m1 = p1.matcher(splitPer[i]);
            while (m1.find()) {
                dataValueList.add(m1.group(1));
            }
        }

        List<EntityValue> entityValues = new ArrayList<>();
        try {
            for (int i = 0; i < dataList.size(); i++) {
                if (dataList.get(i) >= 30) {
                    EntityValue value = new EntityValue();
                    String byCode = CpxfEnum.getDescByCode(dataValueList.get(i));
                    value.setField(StringUtils.isBlank(byCode) ? "混纺" : byCode);
                    value.setFieldData(dataList.get(i));
                    entityValues.add(value);
                }
            }
        } catch (Exception e) {
            log.error("===e:{}, fieldValue1:{}", e, fieldValue1);
            throw new RuntimeException(e);
        }

        if (CollectionUtils.isEmpty(entityValues)) {
            return "";
        }

        entityValues.stream().forEach(v -> {
            if (Objects.equals(v.getField(), "丝")) {
                v.setFieldData(100);
            }

            if (Objects.equals(v.getField(), "羊绒")) {
                v.setFieldData(99);
            }

            if (Objects.equals(v.getField(), "羊毛")) {
                v.setFieldData(98);
            }

            if (Objects.equals(v.getField(), "混纺")) {
                v.setFieldData(0);
            }
        });

        int i = 0;
        final String[] ss = {""};
        entityValues.stream().distinct().sorted(Comparator.comparing(EntityValue::getFieldData).reversed()).forEach(v -> {
            if (i > 2) {
                return;
            }
            ss[0] = ss[0] + v.getField();
        });
        return ss[0];
    }

    @Data
    public class EntityValue {
        private String field;
        private Integer fieldData;
    }

    private static String getFieldValue(String fieldName, FabSpuForAutoNameEsResp info) {
        try {
            Field field = info.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return Objects.toString(field.get(info));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }



    public Integer searchMinAndMaxNumInEs(SortOrder sortOrder, String index) {
        SearchRequest request = new SearchRequest();
        request.indices(index);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(1);
        sourceBuilder.sort("id", sortOrder);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品searchMinNumInEs {}", request.source().toString());
        Integer minNum = 0;
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return 0;
            }
            SearchHit[] hits = response.getHits().getHits();
            FabSpuForAutoNameEsResp entity = FabSpuForAutoNameEsResp.fromJson(hits[0].getSourceAsString(), FabSpuForAutoNameEsResp.class);
            minNum = Integer.valueOf(entity.getId());
        } catch (IOException e) {
            log.error("查询商品searchMinNumInEs异常e = {}", e.getMessage());
            return 0;
        }
        return minNum;
    }

    @Override
    public List<FabSpuForAutoNameEsResp> searchDataForAutoNameInEsForNum(CommonSearchByPageAndIdEntity entity) {
        SearchRequest request = new SearchRequest();
        request.indices(entity.getIndex());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        int minPage = entity.getMinNum();
        int maxPage = entity.getMinNum() + entity.getPageSize();
        if (entity.getI() != 0) {
            minPage = entity.getMinNum() + entity.getI() * entity.getPageSize();
            maxPage = minPage + entity.getPageSize();
            if (maxPage == entity.getMaxNum()) {
                maxPage = entity.getMaxNum() + 1;
            }
        }
        queryBuilder.must(QueryBuilders.rangeQuery("id").gte(minPage).lt(maxPage));
        sourceBuilder.sort("id", SortOrder.ASC);
        sourceBuilder.size(10000);
        sourceBuilder.query(queryBuilder);

        // 使用主分片查询
        request.preference("primary");
        request.source(sourceBuilder);
        log.info("查询商品FAB_FOR_AUTO_NAME:{}", request.source().toString());
        List<FabSpuForAutoNameEsResp> entities = new ArrayList<>();
        try {
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                FabSpuForAutoNameEsResp resp = FabSpuForAutoNameEsResp.fromJson(hit.getSourceAsString(), FabSpuForAutoNameEsResp.class);
                resp.setId(Integer.valueOf(hit.getId()));
                entities.add(resp);
            }
        } catch (IOException e) {
            log.error("查询商品FAB_FOR_AUTO_NAME异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }
}
