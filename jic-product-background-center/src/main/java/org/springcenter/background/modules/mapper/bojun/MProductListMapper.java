package org.springcenter.background.modules.mapper.bojun;


import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.bojun.MProductList;

import java.util.List;

public interface MProductListMapper {

    /**
     * 根据上架时间查询商品code
     * @return
     */
    List<String> selectProductCodesByTime(@Param("date") Long date);


    MProductList selectByProductId(@Param("productId") String productId);

}
