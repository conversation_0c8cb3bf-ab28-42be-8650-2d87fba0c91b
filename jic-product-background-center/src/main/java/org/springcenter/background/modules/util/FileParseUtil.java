package org.springcenter.background.modules.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

@Slf4j
public class FileParseUtil {

    public static List<String> readExcelByUrl(String fileUrl) {
        String path = downLoadExcel(fileUrl);
        // 文件解析
        List<String> dataList = POIUtil.readExcel(path);
        if(ObjectUtils.isEmpty(dataList)){
            log.error("EXCEL解析数据为空,url={}",fileUrl);
            throw new RuntimeException("EXCEL解析数据为空");
        }
        return dataList;
    }

    public static List<Map<String,Object>> readExcelByUrl(List<String> attrNameList, String fileUrl) {
        String path = downLoadExcel(fileUrl);
        // 文件解析
        List<Map<String,Object>> maps = POIUtil.readExcel(attrNameList, path);
        if(ObjectUtils.isEmpty(maps)){
            log.error("EXCEL解析数据为空,url={}",fileUrl);
            throw new RuntimeException("EXCEL解析数据为空");
        }
        return maps;
    }

    public static String downLoadExcel(String fileUrl){
        String path = StrUtil.getUUID();
        if(fileUrl.endsWith(".xls")){
            path += ".xls";
        }else if(fileUrl.endsWith(".xlsx")){
            path += ".xlsx";
        }
        // 下载文件
        FileUtil.downloadByUrl(fileUrl,path);
        return path;
    }
}
