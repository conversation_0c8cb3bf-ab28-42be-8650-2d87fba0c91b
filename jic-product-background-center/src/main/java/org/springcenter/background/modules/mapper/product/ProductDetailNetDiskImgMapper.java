package org.springcenter.background.modules.mapper.product;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.ProductDetailNetDiskImg;

import java.util.List;

public interface ProductDetailNetDiskImgMapper {
    int deleteByPrimaryKey(String id);

    int insert(ProductDetailNetDiskImg record);

    int insertSelective(ProductDetailNetDiskImg record);

    ProductDetailNetDiskImg selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(ProductDetailNetDiskImg record);

    int updateByPrimaryKey(ProductDetailNetDiskImg record);

    List<ProductDetailNetDiskImg> selectBySkcAndIsDel(@Param("productCode") String productCode,
                                                      @Param("colorNo") String colorNo,
                                                      @Param("isDel") Integer isDel,
                                                      @Param("type") Integer type);

    void batchInsert(@Param("list") List<ProductDetailNetDiskImg> insertList);

    List<ProductDetailNetDiskImg> selectByProductCodesAndType(@Param("list") List<String> productCodes, @Param("type") Integer type);

    List<ProductDetailNetDiskImg> selectShouldUpdateByProductCodes(@Param("productCodes") List<String> productCodes);

    List<String> selectDetailListByName(@Param("name") String name);

    List<ProductDetailNetDiskImg> selectByNeId(@Param("neid") String neid);
}