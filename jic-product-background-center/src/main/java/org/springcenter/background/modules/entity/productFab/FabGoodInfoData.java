package org.springcenter.background.modules.entity.productFab;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import org.springcenter.product.api.dto.ProductSpuFabResp;

/**
 * <AUTHOR>
 * @Date:2024/6/5 23:30
 */
@Data
public class FabGoodInfoData {
    private List<GoodList> goodList;
    private FabSkusData.ScopedSlots scopedSlots;
    private String assembly;
    private String outName;
    private String id;
    private String type;
    private String key;

    @Data
    public static class GoodList implements Serializable {
        private String name;
        private Long productId;
        private List<SubGood> goodsList;
        private String outName;
    }

    @Data
    public static class SubGood implements Serializable {
        private Boolean flag;
        private Long id;
        private String key;
        private Long productId;

        private FabSkusData.ScopedSlots scopedSlots;

        private Object children;

        private Integer sort;

        private String assembly;

        private String outName;

        private ProductSpuFabResp productInfo;
    }


}
