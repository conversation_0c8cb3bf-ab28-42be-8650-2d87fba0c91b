package org.springcenter.background.modules.remote.entity;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2024/5/15 15:18
 */
@Data
public class BigDataComprehensiveData {

    @ApiModelProperty(value = "id")
    @SerializedName("idx")
    private Integer idx;

    @SerializedName("m_product_id")
    private Long m_product_id;

    @SerializedName("goodsId")
    private String goodsId;


}
