package org.springcenter.background.modules.remote;

import org.springcenter.background.modules.remote.entity.AiAssistantReqEntity;
import org.springcenter.background.modules.remote.entity.AiAssistantRespEntity;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * <AUTHOR>
 * @Date:2023/9/19 14:07
 */
public interface IAiAssistantRemoteApi {

    //@POST("/nlp/ChatGPT/chat")
    @POST("/nlp/v1/chat")
    Call<AiAssistantRespEntity> invokeThirdAi(@Body AiAssistantReqEntity req);
}
