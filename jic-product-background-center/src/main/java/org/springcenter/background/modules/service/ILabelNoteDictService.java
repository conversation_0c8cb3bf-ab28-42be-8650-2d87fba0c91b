package org.springcenter.background.modules.service;

import com.jnby.common.Page;
import org.springcenter.product.api.dto.back.label.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/10/9 10:18
 */
public interface ILabelNoteDictService {
    /**
     * 添加标签注释
     * @param requestData 请求参数
     * @return 返回
     */
    Boolean addNote(AddLabelNoteReq requestData);

    /**
     * 修改标签信息
     * @param requestData 入参
     * @return 返回
     */
    Boolean updateNote(UpdateLabelNoteReq requestData);

    /**
     * 删除标签
     * @param requestData 主键
     * @return 返回
     */
    Boolean delNote(String requestData);

    /**
     * 根据商品id查询详情
     * @param requestData 主键
     * @return 返回
     */
    LabelNoteDetailResp getNoteDetail(String requestData);

    /**
     * 获取列表信息
     * @param requestData 请求参数
     * @param page 分页
     * @return 返回
     */
    List<LabelNoteListResp> getNoteList(SearchLabelListReq requestData, Page page);
}
