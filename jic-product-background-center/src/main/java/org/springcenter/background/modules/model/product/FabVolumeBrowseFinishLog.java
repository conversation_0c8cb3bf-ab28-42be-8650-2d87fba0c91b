package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2024/3/14 16:21
 */
@Data
@TableName(value = "FAB_VOLUME_BROWSE_FINISH_LOG")
public class FabVolumeBrowseFinishLog {

    @TableId
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField(value = "C_STORE_ID")
    @ApiModelProperty(value = "门店id")
    private Long cStoreId;

    @TableField(value = "HR_ID")
    @ApiModelProperty(value = "导购id")
    private Long hrId;

    @TableField(value = "FAB_VOLUME_ID")
    @ApiModelProperty(value = "产品册id")
    private String fabVolumeId;

    @TableField(value = "STORE_BRAND_ID")
    @ApiModelProperty(value = "品牌id")
    private Long storeBrandId;

    @TableField(value = "CAMPAIGN_ID")
    @ApiModelProperty(value = "场景id 任务方的主任务id")
    private String campaignId;

    @TableField(value = "TASK_ITEM_ID")
    @ApiModelProperty(value = "自任务id")
    private String taskItemId;

    @TableField(value = "SEND_TYPE")
    @ApiModelProperty(value = "发送状态 0未发送 1已发送")
    private Integer sendType;

    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "IS_DELETED")
    @ApiModelProperty(value = "0正常 1已删除")
    private Integer isDeleted;


}
