package org.springcenter.background.modules.service.impl;

import com.google.common.collect.Lists;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.common.util.excel.IWriteDataExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.background.modules.entity.productFab.ProductBatchPatentEntity;
import org.springcenter.background.modules.mapper.bojun.BBoxMProductMapper;
import org.springcenter.background.modules.mapper.bojun.MProductMapper;
import org.springcenter.background.modules.mapper.product.PatentInfoMapper;
import org.springcenter.background.modules.model.bojun.BoxMProduct;
import org.springcenter.background.modules.model.bojun.MProduct;
import org.springcenter.background.modules.model.product.PatentInfo;
import org.springcenter.background.modules.service.IProductPatentService;
import org.springcenter.background.modules.util.FileParseUtil;
import org.springcenter.product.api.dto.background.fab.PatentInfoResp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2025/1/7 15:41
 */
@Service
@Slf4j
public class IProductPatentServiceImpl implements IProductPatentService {
    
    @Autowired
    private PatentInfoMapper patentInfoMapper;

    @Autowired
    private QiniuUtil qiniuUtil;

    @Autowired
    private RedisPoolUtil redisPoolUtil;

    @Autowired
    private MProductMapper mProductMapper;

    @Autowired
    private BBoxMProductMapper bBoxMProductMapper;

    @Value("${product.patent.id}")
    private String patentTagId;
    
    @Override
    public String importProductPatentInfo(String url, String keys, String type) {
        List<PatentInfo> patentInfos = new ArrayList<>();
        List<ProductBatchPatentEntity.AllPatentInfo> errorDataList = new ArrayList<>();
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {
            @Override
            protected void saveData() {
                ProductBatchPatentEntity entity = dealBatchImportProductPatentInfo(this.cachedDataList, type);
                //保存数据

                if (CollectionUtils.isNotEmpty(entity.getBaseDataList())) {
                    patentInfos.addAll(entity.getBaseDataList().stream().distinct().collect(Collectors.toList()));
                }

                if(CollectionUtils.isNotEmpty(entity.getErrorDataList())){
                    errorDataList.addAll(entity.getErrorDataList());
                }
            }
        });

        // 导入对应数据 删除数据原来的数据
        if (CollectionUtils.isNotEmpty(patentInfos)) {
            Lists.partition(patentInfos, 1000)
                    .stream()
                    .forEach(v -> {
                        // 删除信息
                        patentInfoMapper.batchUpdateByIds(v);

                        // 添加信息
                        patentInfoMapper.batchInsert(v);
                    });
        }

        String param = "";
        if (CollectionUtils.isNotEmpty(errorDataList)) {
            String fileName = System.currentTimeMillis() + ".xlsx";
            EasyExcelUtil.write(fileName, ProductBatchPatentEntity.ErrorData.class, new IWriteDataExcel<ProductBatchPatentEntity.ErrorData>() {
                @Override
                public List<ProductBatchPatentEntity.ErrorData> getData() {
                    List<ProductBatchPatentEntity.ErrorData> rets = errorDataList.stream().map(item -> {
                        ProductBatchPatentEntity.ErrorData data = new ProductBatchPatentEntity.ErrorData();
                        data.setName(item.getName());
                        data.setSkcNo(item.getSkcNo());
                        data.setReason(item.getReason());
                        return data;
                    }).distinct().collect(Collectors.toList());
                    return rets;
                }
            });
            File file = new File(fileName);
            param = qiniuUtil.upload(file.getPath(), "异常商品专利信息"+System.currentTimeMillis() + ".xlsx");
            file.delete();
            log.info("redis的key{}", param);
            RedisTemplateUtil.setex(redisPoolUtil, keys, param,60);

        }
        log.info("是否存在脏数据：{}, params:{}", errorDataList.size(), param);
        return param;
    }

    @Override
    public PatentInfoResp queryFabDetailPatentInfo(String requestData) {


        if (StringUtils.isBlank(requestData)) {
            throw new RuntimeException("商品id不能为空");
        }
        List<PatentInfo> patentInfos = patentInfoMapper.selectByProductId(requestData);
        if (CollectionUtils.isEmpty(patentInfos)) {
            return null;
        }

        String patentDesc = patentInfos.stream().map(PatentInfo::getPatentDesc)
                .distinct().collect(Collectors.joining(";"));
        String patentNo = patentInfos.stream().map(PatentInfo::getPatentNo)
                .distinct().collect(Collectors.joining(";"));

        PatentInfoResp infoResp = new PatentInfoResp();
        infoResp.setPatentName(StringUtils.isBlank(patentDesc) || Objects.equals(patentDesc, "null") ? "" : patentDesc);
        infoResp.setPatentNo(StringUtils.isBlank(patentNo) || Objects.equals(patentNo, "null") ? "" : patentNo);
        return infoResp;
    }

    private ProductBatchPatentEntity dealBatchImportProductPatentInfo(List<Map<Integer, String>> cachedDataList, String operate) {
        ProductBatchPatentEntity entity = new ProductBatchPatentEntity();
        List<ProductBatchPatentEntity.AllPatentInfo> oldPatentInfos = new ArrayList<>();
        for (Map<Integer, String> batchPatentMap : cachedDataList) {
            if (StringUtils.isBlank(batchPatentMap.get(0)) && StringUtils.isBlank(batchPatentMap.get(1))) {
                continue;
            }
            ProductBatchPatentEntity.AllPatentInfo allPatentInfo = new ProductBatchPatentEntity.AllPatentInfo();
            allPatentInfo.setName(batchPatentMap.get(0));
            allPatentInfo.setSkcNo(batchPatentMap.get(1));

            if (StringUtils.isBlank(batchPatentMap.get(2)) && StringUtils.isBlank(batchPatentMap.get(3))) {
                allPatentInfo.setReason("专利信息不能全为空");
                allPatentInfo.setType(1);
            } else {
                allPatentInfo.setPatentNo(batchPatentMap.get(2));
                allPatentInfo.setPatentDesc(batchPatentMap.get(3));
                allPatentInfo.setType(0);
            }
            oldPatentInfos.add(allPatentInfo);
        }

        // 判断当前款号是否存在
        List<String> nameList = oldPatentInfos.stream()
                .map(ProductBatchPatentEntity.AllPatentInfo::getName)
                .filter(v -> StringUtils.isNotBlank(v))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nameList)) {
            oldPatentInfos.forEach(v -> {
                v.setReason("款号不存在");
                v.setType(1);
            });
            /*entity.setErrorDataList(oldPatentInfos);
            entity.setAllDataList(oldPatentInfos);*/
            return entity;
        }


        List<MProduct> boxMProducts = mProductMapper.selectByNames(nameList);
        Map<String, List<MProduct>> map = boxMProducts.stream().collect(Collectors.groupingBy(MProduct::getMProductOrig));
        Map<Long, List<String>> skcMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(boxMProducts)) {
            List<Long> ids = boxMProducts.stream().map(MProduct::getId).collect(Collectors.toList());
            List<BoxMProduct> products = bBoxMProductMapper.selectByIds(ids);
            if (CollectionUtils.isNotEmpty(products)) {
                skcMap = products.stream().collect(Collectors.groupingBy(BoxMProduct::getId, Collectors.mapping(BoxMProduct::getColorno, Collectors.toList())));
            }
        }

        List<ProductBatchPatentEntity.AllPatentInfo> newAllPatentInfos = new ArrayList<>();
        Map<Long, List<String>> finalSkcMap = skcMap;
        oldPatentInfos.forEach(v -> {
            if (Objects.equals(v.getType(), 1)) {
                newAllPatentInfos.add(v);
                return;
            }

            List<MProduct> boxMProduct = map.get(v.getName());
            if (boxMProduct == null) {
                ProductBatchPatentEntity.AllPatentInfo data = new ProductBatchPatentEntity.AllPatentInfo();
                BeanUtils.copyProperties(v, data);
                data.setReason("款号有误");
                data.setType(1);
                newAllPatentInfos.add(data);
            } else {
                List<Long> ids = boxMProduct.stream().map(MProduct::getId).collect(Collectors.toList());
                // 如果是home的则将所有商品id都放入 任意一个匹配上skc则匹配上
                List<ProductBatchPatentEntity.AllPatentInfo> subPatentInfos = new ArrayList<>();
                ids.forEach(x -> {
                    ProductBatchPatentEntity.AllPatentInfo data = new ProductBatchPatentEntity.AllPatentInfo();
                    BeanUtils.copyProperties(v, data);
                    data.setProductId(x);
                    subPatentInfos.add(data);
                });

                // 如果home多个商品id都未找到色号才添加
                AtomicReference<Boolean> isExist = new AtomicReference<>(false);
                subPatentInfos.forEach(x -> {
                    if (MapUtils.isNotEmpty(finalSkcMap) && finalSkcMap.containsKey(x.getProductId())) {
                        List<String> list = finalSkcMap.get(x.getProductId());
                        if (list.contains(x.getSkcNo()) && !isExist.get()) {
                            isExist.set(true);
                        }
                    }
                });

                if (isExist.get()) {
                    subPatentInfos.forEach(x -> {
                        x.setType(0);
                        newAllPatentInfos.add(x);
                    });
                } else {
                    subPatentInfos.forEach(x -> {
                        x.setReason("色号有误");
                        x.setType(1);
                        newAllPatentInfos.add(x);
                    });
                }

            }
        });



        List<ProductBatchPatentEntity.AllPatentInfo> errorList = newAllPatentInfos.stream()
                .filter(v -> Objects.equals(v.getType(), 1)).collect(Collectors.toList());
        entity.setErrorDataList(errorList);
        entity.setAllDataList(newAllPatentInfos);
        List<PatentInfo> baseDataList = new ArrayList<>();
        newAllPatentInfos.stream()
                .filter(v -> Objects.equals(v.getType(), 0))
                .distinct()
                .forEach(v -> {
                    PatentInfo patentInfo = new PatentInfo();
                    patentInfo.setSkcNo(v.getSkcNo());
                    patentInfo.setPatentNo(v.getPatentNo());
                    patentInfo.setPatentDesc(v.getPatentDesc());
                    patentInfo.setName(v.getName());
                    patentInfo.setProductId(Objects.toString(v.getProductId()));
                    patentInfo.setCreateTime(new Date());
                    patentInfo.setUpdateTime(new Date());
                    patentInfo.setId(IdLeaf.getId(patentTagId));
                    patentInfo.setOperators(operate);
                    baseDataList.add(patentInfo);
                });
        entity.setBaseDataList(baseDataList);
        return entity;
    }
}
