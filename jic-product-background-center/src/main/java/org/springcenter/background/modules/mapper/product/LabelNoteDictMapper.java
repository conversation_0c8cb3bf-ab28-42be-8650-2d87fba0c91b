package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.LabelNoteDict;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【LABEL_NOTE_DICT(标签字典)】的数据库操作Mapper
* @createDate 2024-10-09 10:10:41
* @Entity generator.domain.LabelNoteDict
*/
public interface LabelNoteDictMapper extends BaseMapper<LabelNoteDict> {

    LabelNoteDict selectByLabelCode(@Param("labelCode") String labelCode);

    void updateInfoById(@Param("item") LabelNoteDict labelNoteDict);

    void delNoteById(@Param("id") String requestData);

    void delNoteBySubId(@Param("subId") String requestData);

    List<LabelNoteDict> selectFirstList(@Param("labelCode") String labelCode);

    List<LabelNoteDict> selectSecondList(@Param("subId") String subId);
}
