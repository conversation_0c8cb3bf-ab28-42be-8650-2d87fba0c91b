package org.springcenter.background.modules.model.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/12/21 16:48
 */
@Data
@TableName(value = "PRODUCT_SIZE_INFO_CELL_SETTING")
public class ProductSizeInfoCellSetting {

    @TableId
    private Integer id;

    @TableField(value = "OUTSIDE_FILED")
    private String outsideFiled;

    @TableField(value = "SYSTEM_FIELD")
    private String systemFiled;

    @TableField(value = "BRAND_SETTING_ID")
    @ApiModelProperty(value = "品牌配置id")
    private Integer brandSettingId;

    @TableField(value = "SMALL_CLASS_ID")
    @ApiModelProperty(value = "小类配置id")
    private Integer smallClassId;

    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    @TableField(value = "SORT")
    private Integer sort;
}
