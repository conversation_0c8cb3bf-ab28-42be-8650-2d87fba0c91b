<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.product.FabAutoNameSettingMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.product.FabAutoNameSetting">
        <id column="ID" property="id" />
        <result column="FIELD_NAME" property="fieldName" />
        <result column="FIELD" property="field" />
        <result column="ASSEMBLE_PART_ORDER" property="assemblePartOrder" />
        <result column="ASSEMBLE_WHOLE_ORDER" property="assembleWholeOrder" />
        <result column="TYPE" property="type" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="AUTO_TYPE" property="autoType" />
        <result column="IS_IN_CYCLE" property="isInCycle" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, FIELD_NAME, FIELD, ASSEMBLE_PART_ORDER, TYPE, ASSEMBLE_WHOLE_ORDER,
            CREATE_TIME, UPDATE_TIME, IS_DELETED, AUTO_TYPE, IS_IN_CYCLE
    </sql>

</mapper>
