package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.ProductInspirationInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/9/5 14:13
 */
public interface ProductInspirationInfoMapper extends BaseMapper<ProductInspirationInfo> {

    void batchUpdateByIds(@Param("list") List<ProductInspirationInfo> v);

    void batchInsert(@Param("list") List<ProductInspirationInfo> v);

    List<ProductInspirationInfo> selectByProductId(@Param("productId") String id);
}
