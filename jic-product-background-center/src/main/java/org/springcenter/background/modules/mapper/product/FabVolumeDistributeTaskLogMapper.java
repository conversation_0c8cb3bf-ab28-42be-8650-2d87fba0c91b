package org.springcenter.background.modules.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.background.modules.model.product.FabVolumeDistributeTaskLog;

import java.util.List;

public interface FabVolumeDistributeTaskLogMapper extends BaseMapper<FabVolumeDistributeTaskLog> {


    void batchInsert(@Param("list") List<FabVolumeDistributeTaskLog> logs);

    List<FabVolumeDistributeTaskLog> selectDisTaskIsToday();

    /**
     * 根据产品册的id查询场景
     * @param fabVolumeId 产品册id
     * @return 返回
     */
    List<FabVolumeDistributeTaskLog> selectFabInfo(@Param("fabVolumeId") String fabVolumeId);

    FabVolumeDistributeTaskLog selectByBrowseNextDay(@Param("fabVolumeId") String fabVolumeId);
}
