<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.background.modules.mapper.bojun.CStoreMapper">
    <resultMap id="BaseResultMap" type="org.springcenter.background.modules.model.bojun.CStore">
        <id column="ID" jdbcType="DECIMAL" property="id" />
        <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
        <result column="C_CUSTOMER_ID" jdbcType="DECIMAL" property="cCustomerId" />
        <result column="CODE" jdbcType="VARCHAR" property="code" />
        <result column="C_UNIONSTORE_ID" jdbcType="DECIMAL" property="cUnionstoreId" />
        <result column="C_ARCBRAND_ID" jdbcType="DECIMAL" property="cArcbrandId" />
        <result column="isnegative" jdbcType="VARCHAR" property="isNegative" />
        <result column="mobil" jdbcType="VARCHAR" property="mobil"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="CALCULATION" jdbcType="DECIMAL" property="calculation"/>
        <result column="C_PROVINCE_ID" jdbcType="DECIMAL" property="cProvinceId"/>
        <result column="C_CITY_ID" jdbcType="DECIMAL" property="cCityId"/>
        <result column="C_DISTRICT_ID" jdbcType="DECIMAL" property="cDistrictId"/>

        <result column="mobil" jdbcType="VARCHAR" property="mobil"/>
        <result column="LONGITUDE" jdbcType="DECIMAL" property="longitude"/>
        <result column="LATITUDE" jdbcType="DECIMAL" property="latitude"/>
        <result column="ZT_MONTHCODE" jdbcType="VARCHAR" property="ztMonthcode"/>
        <result column="C_AREA_ID"  jdbcType="DECIMAL" property="cAreaId"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID, ISACTIVE,  NAME, DESCRIPTION, C_CUSTOMER_ID, CODE,C_UNIONSTORE_ID,C_ARCBRAND_ID,isnegative,mobil,phone,
            CALCULATION,LONGITUDE,	LATITUDE, ZT_MONTHCODE,C_PROVINCE_ID,C_CITY_ID,C_DISTRICT_ID, C_AREA_ID
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
            FROM C_STORE
        WHERE ISACTIVE = 'Y' and ID = #{id}
    </select>

    <select id="selectRegions" resultType="org.springcenter.product.api.dto.FabTaskIsFilterDataResp">
        SELECT a.C_AREA_ID as code, b.NAME as name FROM
        (SELECT DISTINCT C_AREA_ID FROM C_STORE WHERE ISACTIVE = 'Y')  a
        LEFT JOIN C_AREA b ON a.C_AREA_ID = b.ID
        WHERE b.ISACTIVE = 'Y'
    </select>

    <select id="selectCity" resultType="org.springcenter.product.api.dto.FabTaskIsFilterDataResp">
        SELECT a.C_CITY_ID as code, b.NAME as name FROM
        (SELECT DISTINCT C_CITY_ID FROM C_STORE WHERE ISACTIVE = 'Y'
        <if test="cAreaId != null and cAreaId != ''">
            AND C_AREA_ID = #{cAreaId}
        </if>
        )  a
        LEFT JOIN C_CITY b ON a.C_CITY_ID = b.ID
        WHERE b.ISACTIVE = 'Y'
    </select>


    <select id="selectStore" resultType="org.springcenter.product.api.dto.FabTaskIsFilterDataResp">
        SELECT DISTINCT ID as code, name as name
        FROM C_STORE
        WHERE ISACTIVE = 'Y'
        <if test="cCityId != null and cCityId != ''">
            AND C_CITY_ID = #{cCityId}
        </if>

    </select>

</mapper>
