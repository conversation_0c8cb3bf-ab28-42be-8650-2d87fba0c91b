package org.springcenter.background.modules.service.impl;

import brave.Tracer;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Preconditions;
import com.jnby.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsSetQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springcenter.background.modules.mapper.bojun.CStoreMapper;
import org.springcenter.background.modules.mapper.product.ProductDetailNetDiskImgMapper;
import org.springcenter.background.modules.model.bojun.CStore;
import org.springcenter.background.modules.remote.entity.BigDataComprehensiveData;
import org.springcenter.background.modules.remote.entity.BigDataComprehensiveReq;
import org.springcenter.background.modules.remote.service.IBigDataRecommendService;
import org.springcenter.background.modules.service.IProductSizeInfoService;
import org.springcenter.background.modules.service.ISmartScreenInfoService;
import org.springcenter.background.modules.util.EsUtil;
import org.springcenter.product.api.dto.AttrResp;
import org.springcenter.product.api.dto.SampleProductSkcResp;
import org.springcenter.product.api.dto.StoreGoodSpuResp;
import org.springcenter.product.api.dto.back.GoodSmartScrrenDetailEntity;
import org.springcenter.product.api.dto.back.SmartScreenProductInfoResp;
import org.springcenter.product.api.dto.back.SmartScreenSkcInfoResp;
import org.springcenter.product.api.dto.back.screen.StoreRecommendGoodsReq;
import org.springcenter.product.api.dto.back.screen.StoreRecommendGoodsResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import strman.Strman;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2024/9/29 20:12
 */
@Slf4j
@Service
@RefreshScope
public class SmartScreenInfoServiceImpl implements ISmartScreenInfoService {

    @Value("${product.scrren.info.index}")
    private String productScreenInfoIndex;

    @Autowired
    private EsUtil esUtil;

    @Autowired
    private IProductSizeInfoService productSizeInfoService;

    @Autowired
    private ProductDetailNetDiskImgMapper productDetailNetDiskImgMapper;

    @Value("${product.screen.skc.sort}")
    private String productScreenSkcSort;

    @Value("${life.style}")
    private String lifeStyle;

    @Override
    public List<SmartScreenSkcInfoResp> searchProductSkcInfo(String productId) {
        if (StringUtil.isBlank(productId)) {
            throw new RuntimeException("商品id不能为空");
        }
        GoodSmartScrrenDetailEntity detail = getProductDetail(productId);
        if (detail == null || CollectionUtils.isEmpty(detail.getSkus())) {
            return Collections.emptyList();
        }
        List<SmartScreenSkcInfoResp> rets = new ArrayList<>();
        detail.getSkus().forEach(sku -> {
            SmartScreenSkcInfoResp resp = new SmartScreenSkcInfoResp();
            resp.setColor(sku.getColor_name());
            resp.setImgUrl(sku.getImgurl());
            resp.setSize(sku.getSize_name());
            resp.setSkuId(Long.valueOf(sku.getId()));
            rets.add(resp);
        });

        // 进行排序
        List<String> sortList = Arrays.stream(StringUtils.split(productScreenSkcSort, ',')).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sortList)) {
            return rets;
        }
        List<SmartScreenSkcInfoResp> sortRets = new ArrayList<>();

        // 排序
        Map<String, List<SmartScreenSkcInfoResp>> map = rets.stream().collect(Collectors.groupingBy(SmartScreenSkcInfoResp::getColor));
        map.values().forEach(v -> {
            sortList.forEach(sort -> {
                Optional<SmartScreenSkcInfoResp> optional = v.stream().filter(x -> Objects.equals(x.getSize(), sort)).findFirst();
                if (optional.isPresent()) {
                    sortRets.add(optional.get());
                }
            });
        });

        // 避免有不一样的尺码
        if (CollectionUtils.isNotEmpty(sortRets)) {
            return sortRets;
        } else {
            return rets;
        }
    }

    @Override
    public SmartScreenProductInfoResp searchProductInfo(String productId) {
        if (StringUtil.isBlank(productId)) {
            throw new RuntimeException("商品id不能为空");
        }


        /*CompletableFuture[] a = new CompletableFuture[2];
        a[0] = (CompletableFuture.supplyAsync(() -> {
            // 获取商品信息
            GoodSmartScrrenDetailEntity detail = getProductDetail(productId);
            if (detail == null) {
                return null;
            }
            return detail;

        }));

        a[1] = (CompletableFuture.supplyAsync(() -> {
            // 组装尺码信息
            ProductFabInfoSizeReq sizeReq = new ProductFabInfoSizeReq();
            sizeReq.setProductId(productId);
            sizeReq.setIsNeedCliff(false);
            ProductFabInfoSizeResp sizeResp = productSizeInfoService.queryFabSizeInfoNew(sizeReq);
            return sizeResp.getSizeInfos();
        }));


        // 组合两个异步任务的结果
        Map<String, Object> result = new HashMap<>();
        CompletableFuture<Map<String, Object>> combinedFuture = CompletableFuture.allOf(a)
                .thenApply(v -> {
                    for (int i = 0; i < a.length; i++) {
                        result.put("res" + i, a[i].join());
                    }
                    return result;
                });

        if (MapUtils.isEmpty(result)) {
            return null;
        }*/

        // 获取商品信息
        GoodSmartScrrenDetailEntity detail = getProductDetail(productId);
        if (detail == null) {
            return null;
        }

        List<List<String>> sizeResp = productSizeInfoService.queryDistinctSizeInfoByProductId(productId);
        SmartScreenProductInfoResp resp = new SmartScreenProductInfoResp();
        SmartScreenProductInfoResp.BaseInfo baseInfo = new SmartScreenProductInfoResp.BaseInfo();
        SmartScreenProductInfoResp.ParamInfo paramInfo = new SmartScreenProductInfoResp.ParamInfo();
        SmartScreenProductInfoResp.VideoInfo videoInfo = new SmartScreenProductInfoResp.VideoInfo();
        //GoodSmartScrrenDetailEntity detail = (GoodSmartScrrenDetailEntity) result.get("res0");
        baseInfo.setProductId(Objects.toString(detail.getId()));
        baseInfo.setCombName(detail.getMall_title());
        baseInfo.setProductName(detail.getName());

        baseInfo.setPrice(new BigDecimal(Objects.toString(detail.getPrice())));
        baseInfo.setCover_img(detail.getCover_imgs());
        List<String> productLabel = new ArrayList<>();
        if (Objects.equals(detail.getIsNew(), 1)) {
            productLabel.add("新品");
        }
        if (StringUtils.isNotBlank(detail.getTag())) {
            Arrays.stream(detail.getTag().split(","))
                    .forEach(v -> {
                productLabel.add(v);
            });
        }
        if (CollectionUtils.isNotEmpty(productLabel)) {
            baseInfo.setProductLabel(productLabel);
        }
        resp.setBaseInfo(baseInfo);

        paramInfo.setCategory(detail.getCcchr4());
        paramInfo.setFitness(detail.getWei_du());
        paramInfo.setFabric(detail.getFabric());
        paramInfo.setMaterial(detail.getMaterial());
        paramInfo.setThick(detail.getThick());
        paramInfo.setMarketTime(detail.getMarketTime());
        paramInfo.setStyleName(detail.getStyle_name());
        paramInfo.setFab(detail.getFab());
        resp.setParamInfo(paramInfo);

        videoInfo.setVideoImg(detail.getVideo_img());
        videoInfo.setVideoUrl(detail.getVideo_url());
        resp.setVideoInfo(videoInfo);

        resp.setBannerImgs(detail.getBanner_imgs() == null ? Collections.emptyList() :
                Arrays.stream(detail.getBanner_imgs().split(",")).collect(Collectors.toList()));
        List<String> detais = productDetailNetDiskImgMapper.selectDetailListByName(detail.getName());
        resp.setDetailImgs(detais.stream().filter(v -> StringUtils.isNotBlank(v)).collect(Collectors.toList()));

        resp.setSizeInfo(sizeResp);

        return resp;
    }

    @Value("${screen.product.info}")
    private String screenProductInfo;

    @Override
    public JSONArray searchParamInfo() {
        JSONArray data = JSONObject.parseArray(screenProductInfo);
        return data;
    }

    @Value("${es.index.split.store.goods}")
    private String storeSplitGoodsIndex;

    @Autowired
    private CStoreMapper cStoreMapper;

    @Value("${es.index.split.store.goods.mode}")
    private Long storeSplitGoodsIndexMode;

    @Autowired
    private IBigDataRecommendService bigDataRecommendService;

    @Resource
    private Tracer tracer;

    @Value("${product.aolai.radius}")
    private String productAolaiRadius;

    @Override
    public List<StoreRecommendGoodsResp> screenRecommendProductList(StoreRecommendGoodsReq context, Page page) {
        CStore cStore = Preconditions.checkNotNull(cStoreMapper.selectById(Long.valueOf(context.getStoreIds().get(0))),
                "未找到当前门店");
        log.info("screenRecommendProductList es查询");
        List<StoreRecommendGoodsResp> list = getEsList(context, cStore);
        log.info("screenRecommendProductList es查询 end");
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        // 查询大数据返回顺序
        log.info("screenRecommendProductList bigdata查询");
        List<Long> aolaiList = Arrays.stream(productAolaiRadius.split(","))
                                     .map(v -> Long.valueOf(v)).collect(Collectors.toList());
        Pair<List<BigDataComprehensiveData>, Integer> comprehensiveList = bigDataRecommendService.searchComprehensiveList(BigDataComprehensiveReq.build(list, cStore,
                context.getUnionId(), tracer.currentSpan().context().traceIdString(), aolaiList));
        log.info("screenRecommendProductList bigdata查询 end");
        if (comprehensiveList.getRight() == 0) {
            page.setCount(list.size());
            page.setPages(list.size() % page.getPageSize() == 0 ?
                    list.size() / page.getPageSize() :
                    list.size() / page.getPageSize() + 1);
            return list.stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());
        }

        log.info("screenRecommendProductList 数据处理查询");
        HashMap<Long, StoreRecommendGoodsResp> map = list.stream()
                .collect(HashMap::new, (k, v) -> k.put(Long.valueOf(v.getM_product_id()), v), HashMap::putAll);
        List<BigDataComprehensiveData> data = comprehensiveList.getLeft().stream()
                .skip((page.getPageNo() - 1) * page.getPageSize())
                .limit(page.getPageSize())
                .collect(Collectors.toList());

        List<StoreRecommendGoodsResp> rets = new ArrayList<>();
        data.forEach(v -> {
            if (MapUtils.isEmpty(map) || !map.containsKey(v.getM_product_id())) {
                return;
            }
            StoreRecommendGoodsResp goodsResp = map.get(v.getM_product_id());
            rets.add(goodsResp);
        });
        page.setCount(comprehensiveList.getLeft().size());
        page.setPages(comprehensiveList.getLeft().size() % page.getPageSize() == 0 ?
                comprehensiveList.getLeft().size() / page.getPageSize() :
                comprehensiveList.getLeft().size() / page.getPageSize() + 1);
        List<GoodSmartScrrenDetailEntity> detail = getBatchProductDetail(rets.stream()
                .map(StoreRecommendGoodsResp::getM_product_id).collect(Collectors.toList()));
        HashMap<Long, String> titleMap = detail.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getId(), v.getMall_title()), HashMap::putAll);
        rets.forEach(v -> {
            if (MapUtils.isNotEmpty(titleMap) && titleMap.containsKey(Long.valueOf(v.getM_product_id()))
                    && StringUtils.isNotBlank(titleMap.get(Long.valueOf(v.getM_product_id())))) {
                v.setMall_title(titleMap.get(Long.valueOf(v.getM_product_id())));
            }
        });
        log.info("screenRecommendProductList 数据处理查询 end");
        return rets;
    }

    private List<StoreRecommendGoodsResp> getEsList(StoreRecommendGoodsReq context, CStore cStore) {
        List<String> lifes = Arrays.stream(lifeStyle.split(",")).collect(Collectors.toList());
        Long yuShu = null;
        if (cStore.getCUnionstoreId() == null) {
            yuShu = cStore.getId() % storeSplitGoodsIndexMode;
        } else {
            yuShu = cStore.getCUnionstoreId() % storeSplitGoodsIndexMode;
        }
        if (yuShu == null) {
            throw new RuntimeException("未计算到余数");
        }

        SearchRequest request = new SearchRequest();
        request.indices(StringUtils.replace(storeSplitGoodsIndex, "mode", Objects.toString(yuShu)));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();


        if (CollectionUtils.isNotEmpty(context.getMBandIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("m_band_id", context.getMBandIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getStoreIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("c_store_id", context.getStoreIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getMSmallCategoryIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("m_small_category_id", context.getMSmallCategoryIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getMSmallSeasonIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("small_season_id", context.getMSmallSeasonIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getMBrandIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("c_arcbrand_id", context.getMBrandIds()));
        }


        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if (context.isQty()) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("qty").gt(0));
        }
        if (context.isMallQty()) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("mall_qty").gt(0));
        }
        if (context.isEbQty()) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("eb_qty").gt(0));
        }
        if (context.isQwQty()) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("qw_qty").gt(0));
        }
        if (context.isCityQty()) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("city_qty").gt(0));
        }
        queryBuilder.filter(boolQueryBuilder);

        if (CollectionUtils.isNotEmpty(context.getMustLabels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("labels", context.getMustLabels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script("1"));
            queryBuilder.filter(termsSetQueryBuilder);
        }

        if (context.getIsDisplay() != null) {
            queryBuilder.filter(QueryBuilders.termQuery("chen_lie", context.getIsDisplay()));
        }

        // 过滤商品重复问题 【微定制出现LESS出现jnby商品】
        queryBuilder.filter(QueryBuilders.termQuery("is_display", 1));

        // 默认按照生命周期排序
        sourceBuilder.sort("lifestyle_tag", SortOrder.ASC)
                .sort("year", SortOrder.DESC);

        sourceBuilder.fetchSource(new String[]{"cover_imgs", "mall_title", "name", "m_small_category_id",
                "small_season_id", "year", "c_arcbrand_id", "m_band_id", "m_band", "labels", "m_product_id",
                "display", "life_style", "price", "lifestyle_tag", "chen_lie", "value"}, new String[]{});
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(2999);
        request.source(sourceBuilder);
        List<StoreRecommendGoodsResp> entities = new ArrayList<>();
        try {
            log.info("列表 screenRecommendProductList sourceBuilder：{}", sourceBuilder);
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }


            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                StoreRecommendGoodsResp entity = StoreRecommendGoodsResp.fromJson(hits[i].getSourceAsString(), StoreRecommendGoodsResp.class);
                if (lifes.contains(entity.getLifestyle_tag())) {
                    entity.setIsNew(1);
                } else {
                    entity.setIsNew(0);
                }
                if (StringUtils.isBlank(entity.getMall_title())) {
                    entity.setMall_title(entity.getValue() + entity.getName());
                }
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("查询商品异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }


    @Override
    public List<StoreRecommendGoodsResp> screenRecommendScrollProductList(StoreRecommendGoodsReq context, Page page) {
        CStore cStore = Preconditions.checkNotNull(cStoreMapper.selectById(Long.valueOf(context.getStoreIds().get(0))),
                "未找到当前门店");
        log.info("screenRecommendProductList es查询");
        List<StoreRecommendGoodsResp> list = getScrollEsList(context, cStore);
        log.info("screenRecommendProductList es查询 end");
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        // 查询大数据返回顺序
        log.info("screenRecommendProductList bigdata查询");
        List<Long> aolaiList = Arrays.stream(productAolaiRadius.split(","))
                .map(v -> Long.valueOf(v)).collect(Collectors.toList());
        Pair<List<BigDataComprehensiveData>, Integer> comprehensiveList = bigDataRecommendService.searchComprehensiveList(BigDataComprehensiveReq.build(list, cStore,
                context.getUnionId(), tracer.currentSpan().context().traceIdString(), aolaiList));
        log.info("screenRecommendProductList bigdata查询 end");
        if (comprehensiveList.getRight() == 0) {
            page.setCount(list.size());
            page.setPages(list.size() % page.getPageSize() == 0 ?
                    list.size() / page.getPageSize() :
                    list.size() / page.getPageSize() + 1);
            return list.stream()
                    .skip((page.getPageNo() - 1) * page.getPageSize())
                    .limit(page.getPageSize())
                    .collect(Collectors.toList());
        }

        log.info("screenRecommendProductList 数据处理查询");
        HashMap<Long, StoreRecommendGoodsResp> map = list.stream()
                .collect(HashMap::new, (k, v) -> k.put(Long.valueOf(v.getM_product_id()), v), HashMap::putAll);
        List<BigDataComprehensiveData> data = comprehensiveList.getLeft().stream()
                .skip((page.getPageNo() - 1) * page.getPageSize())
                .limit(page.getPageSize())
                .collect(Collectors.toList());

        List<StoreRecommendGoodsResp> rets = new ArrayList<>();
        data.forEach(v -> {
            if (MapUtils.isEmpty(map) || !map.containsKey(v.getM_product_id())) {
                return;
            }
            StoreRecommendGoodsResp goodsResp = map.get(v.getM_product_id());
            rets.add(goodsResp);
        });
        page.setCount(comprehensiveList.getLeft().size());
        page.setPages(comprehensiveList.getLeft().size() % page.getPageSize() == 0 ?
                comprehensiveList.getLeft().size() / page.getPageSize() :
                comprehensiveList.getLeft().size() / page.getPageSize() + 1);
        List<GoodSmartScrrenDetailEntity> detail = getBatchProductDetail(rets.stream()
                .map(StoreRecommendGoodsResp::getM_product_id).collect(Collectors.toList()));
        HashMap<Long, String> titleMap = detail.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getId(), v.getMall_title()), HashMap::putAll);
        rets.forEach(v -> {
            if (MapUtils.isNotEmpty(titleMap) && titleMap.containsKey(Long.valueOf(v.getM_product_id()))) {
                v.setMall_title(titleMap.get(Long.valueOf(v.getM_product_id())));
            }
        });
        log.info("screenRecommendProductList 数据处理查询 end");
        return rets;
    }

    @Autowired
    private ObjectMapper objectMapper;

    private List<StoreRecommendGoodsResp> getScrollEsList(StoreRecommendGoodsReq context, CStore cStore) {
        List<String> lifes = Arrays.stream(lifeStyle.split(",")).collect(Collectors.toList());
        Long yuShu = null;
        if (cStore.getCUnionstoreId() == null) {
            yuShu = cStore.getId() % storeSplitGoodsIndexMode;
        } else {
            yuShu = cStore.getCUnionstoreId() % storeSplitGoodsIndexMode;
        }
        if (yuShu == null) {
            throw new RuntimeException("未计算到余数");
        }

        SearchRequest request = new SearchRequest();
        request.indices(StringUtils.replace(storeSplitGoodsIndex, "mode", Objects.toString(yuShu)));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();


        if (CollectionUtils.isNotEmpty(context.getMBandIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("m_band_id", context.getMBandIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getStoreIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("c_store_id", context.getStoreIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getMSmallCategoryIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("m_small_category_id", context.getMSmallCategoryIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getMSmallSeasonIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("small_season_id", context.getMSmallSeasonIds()));
        }

        if (CollectionUtils.isNotEmpty(context.getMBrandIds())){
            queryBuilder.filter(QueryBuilders.termsQuery("c_arcbrand_id", context.getMBrandIds()));
        }


        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if (context.isQty()) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("qty").gt(0));
        }
        if (context.isMallQty()) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("mall_qty").gt(0));
        }
        if (context.isEbQty()) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("eb_qty").gt(0));
        }
        if (context.isQwQty()) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("qw_qty").gt(0));
        }
        if (context.isCityQty()) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("city_qty").gt(0));
        }
        queryBuilder.filter(boolQueryBuilder);

        if (CollectionUtils.isNotEmpty(context.getMustLabels())){
            TermsSetQueryBuilder termsSetQueryBuilder = new TermsSetQueryBuilder("labels", context.getMustLabels().stream().map(item -> item.toLowerCase()).map(item -> Strman.replace(item, "-", "_", true)).collect(Collectors.toList()));
            termsSetQueryBuilder.setMinimumShouldMatchScript(new Script("1"));
            queryBuilder.filter(termsSetQueryBuilder);
        }

        if (context.getIsDisplay() != null) {
            queryBuilder.filter(QueryBuilders.termQuery("chen_lie", context.getIsDisplay()));
        }

        // 过滤商品重复问题 【微定制出现LESS出现jnby商品】
        queryBuilder.filter(QueryBuilders.termQuery("is_display", 1));

        // 默认按照生命周期排序
        sourceBuilder.sort("lifestyle_tag", SortOrder.ASC)
                .sort("year", SortOrder.DESC);

        sourceBuilder.fetchSource(new String[]{"cover_imgs", "mall_title", "name", "m_small_category_id",
                "small_season_id", "year", "c_arcbrand_id", "m_band_id", "m_band", "labels", "m_product_id",
                "display", "life_style", "price", "lifestyle_tag", "chen_lie"}, new String[]{});
        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(2999);
        request.source(sourceBuilder);
        List<StoreRecommendGoodsResp> entities = new ArrayList<>();
        try {
            log.info("列表 screenRecommendProductList sourceBuilder：{}", sourceBuilder);

            List<Object> list = esUtil.scrollSearch(request);
            if (CollectionUtils.isEmpty(list)){
                return new ArrayList<>();
            }

            for (Object obj : list) {
                if (obj instanceof SearchHit) {
                    SearchHit hit = (SearchHit) obj;
                    try {
                        StoreRecommendGoodsResp resp = objectMapper.readValue(hit.getSourceAsString(), StoreRecommendGoodsResp.class);
                        if (lifes.contains(resp.getLifestyle_tag())) {
                            resp.setIsNew(1);
                        } else {
                            resp.setIsNew(0);
                        }
                        entities.add(resp);
                    } catch (IOException e) {
                        // 处理异常，例如记录日志
                        e.printStackTrace();
                    }
                }
            }


        } catch (IOException e) {
            log.error("查询商品异常e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }


    private GoodSmartScrrenDetailEntity getProductDetail(String productId) {
        GetRequest request = new GetRequest(productScreenInfoIndex, productId);
        GoodSmartScrrenDetailEntity entity =null;
        try {
            GetResponse response = esUtil.getIndex(request);
            if (response.isExists()){
                entity = GoodSmartScrrenDetailEntity.fromJson(response.getSourceAsString(), GoodSmartScrrenDetailEntity.class);
                entity.buildSku();

            }
            return entity;
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询商品异常e = {}", e.getMessage());
        }
        return entity;
    }


    private List<GoodSmartScrrenDetailEntity> getBatchProductDetail(List<Long> productIds) {
        SearchRequest request = new SearchRequest();
        request.indices(productScreenInfoIndex);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("_id", productIds));

        sourceBuilder.query(queryBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(100);
        request.source(sourceBuilder);

        List<GoodSmartScrrenDetailEntity> entities = new ArrayList<>();
        try {
            log.info("getBatchProductDetail 索引入参sourceBuilder：{}", sourceBuilder);
            SearchResponse response = esUtil.search(request);
            if (response.getHits().getTotalHits().value == 0){
                return new ArrayList<>();
            }
        
            SearchHit[] hits = response.getHits().getHits();
            for (int i = 0; i < hits.length; i++) {
                GoodSmartScrrenDetailEntity entity = GoodSmartScrrenDetailEntity.fromJson(hits[i].getSourceAsString(), GoodSmartScrrenDetailEntity.class);
                entities.add(entity);
            }
        } catch (IOException e) {
            log.error("getBatchProductDetail e = {}", e.getMessage());
            return new ArrayList<>();
        }
        return entities;
    }
}
