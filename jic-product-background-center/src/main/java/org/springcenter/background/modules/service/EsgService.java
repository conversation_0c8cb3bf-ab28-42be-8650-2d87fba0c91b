package org.springcenter.background.modules.service;

import com.alibaba.fastjson.JSONArray;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import org.springcenter.background.modules.model.product.EsgNetDiskMain;
import org.springcenter.product.api.EsgParamResp;
import org.springcenter.product.api.dto.background.EsgNetDiskMainResp;

import java.util.List;

public interface EsgService {

    List<EsgNetDiskMainResp> list(Page page);

    void createOrUpdate(EsgNetDiskMain requestData,String userId);

    /**
     * 重新同步
     * @param requestData
     * @param userId
     */
    void reSyncData(String requestData, String userId);

    EsgParamResp getParams(CommonRequest request);
}
