package org.springcenter.background.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.killbill.bus.api.BusEvent;
import org.springcenter.background.modules.model.product.BusEvents;

import java.util.UUID;

/**
 * @auther yuan<PERSON><PERSON><PERSON>
 * @create 2022-03-22 15:24:26
 * @describe 事件表服务类
 */
public interface IBusEventsService extends IService<BusEvents> {

    /**
     * 插入消息表事件
     * @param event
     */
    void createBusEvent(final BusEvent event);

    void createBusEvent(final BusEvent event,boolean reTry);

    Integer getCurrentNumByToken(UUID token);

    void setCurrentNum(UUID token, Integer currentNum);

    boolean retryCompletion(UUID token, Integer totNum);

    void removeToken(UUID token);

}
