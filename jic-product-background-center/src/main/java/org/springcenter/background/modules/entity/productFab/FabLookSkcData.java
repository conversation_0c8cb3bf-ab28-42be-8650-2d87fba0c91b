package org.springcenter.background.modules.entity.productFab;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/6/5 23:19
 */
@Data
public class FabLookSkcData {
    private List<ContentSku> contentSku;
    private FabSkusData.ScopedSlots scopedSlots;
    private String assembly;
    private String outName;
    private String id;
    private String type;
    private String checkUrl;
    private String key;
    private String collocation;
    private String illustrate;
    private Boolean cationType;
    private List<LifPhoto> newLifePhotos;
    private String name;
    private List cationList;
    private Integer value;
    private String direction;
    private String introduce;


    @Data
    public static class LifPhoto implements Serializable {

        private List<String> xhr;
        private String lastModifiedDate;
        private String type;
        private Integer percent;
        private String url;
        private OrgiData originFileObj;
        private String uid;
        private Integer size;
        private ResponseData response;
    }

    @Data
    public static class OrgiData implements Serializable {

        private String uid;
    }

    @Data
    public static class ResponseData implements Serializable {

        private String msg;
        private String code;
        private String data;
        private Boolean success;
    }

    @Data
    public static class ContentSku implements Serializable {

        private String colorName;
        private String smallClassName;
        private String img;
        private String price;
        private String name;
        private boolean falg;
        private String colorCode;
        private String skcCode;
    }

}
