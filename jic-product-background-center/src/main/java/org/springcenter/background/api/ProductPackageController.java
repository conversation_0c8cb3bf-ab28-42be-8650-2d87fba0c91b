package org.springcenter.background.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.product.api.dto.background.ProductPackageExportEntity;
import org.springcenter.background.modules.service.IProductPackageService;
import org.springcenter.product.api.dto.ProductPackageResp;
import org.springcenter.product.api.dto.background.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/7/24 13:16
 */
@Slf4j
@RestController
@RequestMapping("product/package/api")
@Api(value = "ProductPackageFabApi",tags = "商品包相关接口")
public class ProductPackageController {

    @Autowired
    private IProductPackageService productPackageService;

    // 筛选条件获取
    @ResponseBody
    @PostMapping("/getFilters")
    @ApiOperation(value = "获取商品包的筛选条件信息")
    public ResponseResult<ProductPackageResp> getFilters(){
        return ResponseResult.success(productPackageService.getFilters());
    }


    // 增加商品包
    @ResponseBody
    @PostMapping("/add/product/package")
    @ApiOperation(value = "增加商品包")
    public ResponseResult<AddProductPackageResp> addProductPackage(@RequestBody @Validated CommonRequest<AddProductPackageReq> request){
        return ResponseResult.success(productPackageService.addProductPackage(request.getRequestData()));
    }


    // 查看商品包详情
    @ResponseBody
    @PostMapping("/search/product/package/detail")
    @ApiOperation(value = "查看商品包详情")
    public ResponseResult<ProductPackageDetailResp> searchProductPackageDetail(@RequestBody @Validated CommonRequest<ProductPackageDetailReq> request){
        return ResponseResult.success(productPackageService.searchProductPackageDetail(request.getRequestData()));
    }

    // 编辑商品包详情
    @ResponseBody
    @PostMapping("/update/product/package/detail")
    @ApiOperation(value = "编辑商品包详情")
    public ResponseResult<Boolean> updateProductPackageDetail(@RequestBody @Validated CommonRequest<UpdateProductPackageReq> request){
        return ResponseResult.success(productPackageService.updateProductPackageDetail(request.getRequestData()));
    }

    // 商品包列表 提供出去
    @ResponseBody
    @PostMapping("/product/package/list")
    @ApiOperation(value = "获取商品包列表")
    public ResponseResult<List<ProdPackageListResp>> getProductPackageList(@RequestBody @Validated CommonRequest<ProdPackageListReq> request){
        return ResponseResult.success(
                productPackageService.getProductPackageList(request.getRequestData(), request.getPage(), request.getComponent()),
                request.getPage());
    }

    // 操作停用启用
    @ResponseBody
    @PostMapping("/switch/product/package/status")
    @ApiOperation(value = "停用or启用商品包")
    public ResponseResult<Boolean> switchProductPackageStatus(@RequestBody @Validated CommonRequest<SwitchPackageStatusReq> request){
        return ResponseResult.success(productPackageService.switchProductPackageStatus(request.getRequestData()));
    }

    // 下载商品包
    @ResponseBody
    @PostMapping("/download/product/package")
    @ApiOperation(value = "根据商品包主键下载商品包")
    public ResponseResult<String> downloadProductPackageStatus(@RequestBody @Validated CommonRequest<String> request){
        return ResponseResult.success(productPackageService.downloadProductPackageStatus(request.getRequestData()));
    }


    // 操作人列表
    @ResponseBody
    @PostMapping("/product/package/operators")
    @ApiOperation(value = "根据商品包主键id查询商品包操作列表")
    public ResponseResult<List<ProductPackageOperatorsListResp>> getProductPackageOperators(@RequestBody @Validated CommonRequest<String> request){
        return ResponseResult.success(
                productPackageService.getProductPackageOperators(request.getRequestData(), request.getPage()),
                request.getPage());
    }

    // 提供c端包的接口 提供出去
    @ResponseBody
    @PostMapping("/product/package/forc")
    @ApiOperation(value = "根据商品包id给c获取商品包的商品")
    public ResponseResult<List<ProductPackageExportEntity>> getProductPackageForc(@RequestBody @Validated CommonRequest<String> request){
        log.info("getProductPackageForc根据商品包id给c获取商品包的商品 requestData:{}", request.getRequestData());
        List<ProductPackageExportEntity> productPackageForc = productPackageService.getProductPackageForc(request.getRequestData());
        log.info("getProductPackageForc根据商品包id给c获取商品包的商品 返回");

        return ResponseResult.success(productPackageForc);
    }

    @ResponseBody
    @PostMapping("/getParamByField")
    @ApiOperation(value = "模糊匹配数据")
    public ResponseResult<List<ProductPackageResp.SmallFilterData>> getParamByField(@RequestBody @Validated CommonRequest<SearchPacParamReq> request){
        return ResponseResult.success(productPackageService.getParamByField(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/product/package/pac/product/forc")
    @ApiOperation(value = "查询商品包中包含哪些商品id")
    public ResponseResult<List<SearchProductIdInPacResp>> getPackageProductIdsForc(@RequestBody @Validated CommonRequest<SearchProductIdInPacReq> request){
        log.info("getPackageProductIdsForc查询商品包中包含哪些商品id requestData:{}", request.getRequestData());
        List<SearchProductIdInPacResp> packageProductIdsForc = productPackageService.getPackageProductIdsForc(request.getRequestData());
        log.info("getPackageProductIdsForc查询商品包中包含哪些商品id返回");
        return ResponseResult.success(packageProductIdsForc);
    }

    @ResponseBody
    @PostMapping("/product/package/product/exist/forc")
    @ApiOperation(value = "查询商品id是否存在入参的商品包中")
    public ResponseResult<List<SearchProductIdIsExistResp>> getPackageIsExistProductIdsForc(@RequestBody @Validated CommonRequest<SearchProductIdInPacReq> request){
        log.info("getPackageIsExistProductIdsForc查询商品id是否存在入参的商品包中 requestData:{}", request.getRequestData());
        List<SearchProductIdIsExistResp> productIdsForc = productPackageService.getPackageIsExistProductIdsForc(request.getRequestData());
        log.info("getPackageIsExistProductIdsForc查询商品id是否存在入参的商品包中返回");
        return ResponseResult.success(productIdsForc);
    }


    @ResponseBody
    @PostMapping("test/pac/job")
    @ApiOperation(value = "商品包job任务")
    public ResponseResult<Boolean> testPackageJob(@RequestBody CommonRequest<String> request){
        productPackageService.cashPackageInfoJob(request.getRequestData());
        return ResponseResult.success(true);
    }


    @ResponseBody
    @PostMapping("query/pac/name")
    @ApiOperation(value = "根据商品包id查询商品包名称")
    public ResponseResult<List<ProdPackageListResp>> queryPacListByPacId(@RequestBody CommonRequest<List<String>> request){
        return ResponseResult.success(productPackageService.queryPacListByPacId(request.getRequestData()));
    }

}
