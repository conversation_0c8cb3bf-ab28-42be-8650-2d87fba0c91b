package org.springcenter.background.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.modules.enums.AutoNameEnum;
import org.springcenter.background.modules.model.product.ProductSellingPoints;
import org.springcenter.background.modules.service.*;
import org.springcenter.product.api.dto.FabricWeightResp;
import org.springcenter.product.api.dto.background.ProductSellingPointsEntity;
import org.springcenter.product.api.dto.background.ProductSellingPointsReq;
import org.springcenter.product.api.dto.background.fab.*;
import org.springcenter.product.api.dto.back.ProductFabInfoSizeReq;
import org.springcenter.product.api.dto.back.ProductFabInfoSizeResp;
import org.springcenter.product.api.dto.fab.HomeWashInfoEsResp;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.dto.fab.WpPicInfoResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/6/19 15:02
 */
@Slf4j
@RestController
@RequestMapping("product/fab/api")
@Api(value = "ProductFabApi",tags = "商品中心产品册接口")
public class ProductFabController {

    @Autowired
    private IProductFabService productFabService;

    @Autowired
    private IProductEnvFabricInfoService productEnvFabricInfoService;

    @Autowired
    private IProductPatentService productPatentService;

    @ResponseBody
    @PostMapping("/getFabricWeight")
    @ApiOperation(value = "根据面料号获取面料克重")
    public ResponseResult<List<FabricWeightResp>> getFabricWeightByFabricNo(@RequestBody CommonRequest<List<String>> request) {
        return ResponseResult.success(productFabService.getFabricWeightByFabricNo(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/getHomeWashInfo")
    @ApiOperation(value = "根据商品款号获取洗涤说明 只有小类id：2649 c_arcbrand_id: 17调用该接口")
    public ResponseResult<List<HomeWashInfoEsResp>> getHomeWashInfo(@RequestBody CommonRequest<String> request) {
        return ResponseResult.success(productFabService.getHomeWashInfo(request.getRequestData()));
    }


    //---------------------------------尺码信息 start-----------------------------------------------------

    @Autowired
    private IProductSizeInfoService productSizeInfoService;

    @ResponseBody
    @PostMapping("/query/fab/size/info")
    @ApiOperation(value = "查询商品的尺码信息")
    public ResponseResult<List<ProductSizeInfoResp>> queryFabSizeInfo(@RequestBody @Validated CommonRequest<ProductFabInfoReq> request) {
        return ResponseResult.success(productSizeInfoService.queryFabSizeInfo(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/query/fab/size/info/new")
    @ApiOperation(value = "查询商品的尺码信息-横竖切换")
    public ResponseResult<ProductFabInfoSizeResp> queryFabSizeInfoNew(@RequestBody @Validated CommonRequest<ProductFabInfoSizeReq> request) {
        return ResponseResult.success(productSizeInfoService.queryFabSizeInfoNew(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/export/size/info/data")
    @ApiOperation(value = "导出尺码信息数据")
    public ResponseResult<Boolean> exportSizeInfoData(@RequestBody @Validated CommonRequest<ExportSizeInfoDataReq> request) {
        productSizeInfoService.exportSizeInfoData("1", request.getRequestData());
        return ResponseResult.success(true);
    }

    //---------------------------------尺码信息 end-----------------------------------------------------


    // -------------------------上新表格 start ----------------------------------------------------
    @ResponseBody
    @PostMapping("/export/excel/test")
    @ApiOperation(value = "测试导出上新表格")
    public ResponseResult testExport(@RequestBody CommonRequest<QueryGoodsFabEsReq> request) {
        productFabService.exportProductFabNewArrivalInfo("1", request.getRequestData());
        return ResponseResult.success();
    }
    // -------------------------上新表格 end ----------------------------------------------------


    // -----------------------------查询款号的对应图片----------------------------------------
    @ResponseBody
    @PostMapping("/wp/original/pic")
    @ApiOperation(value = "获取网盘原始图片")
    public ResponseResult<List<WpPicInfoResp>> getWpOriginalPis(@RequestBody CommonRequest<String> request) {
        List<WpPicInfoResp> resps = productFabService.getWpOriginalPis(request.getRequestData());
        return ResponseResult.success(resps);
    }


    // -------------------------导入商品fab信息 start ----------------------------------------------------
    @ResponseBody
    @PostMapping("/import/product/fab")
    @ApiOperation(value = "导入商品的fab信息")
    public ResponseResult<String> testImportFabInfo(@RequestBody CommonRequest<String> request) {
        String url = productFabService.importProductFabInfo(request.getRequestData(), "1", "system");
        return ResponseResult.success(url);
    }

    @ResponseBody
    @PostMapping("judge/import/product/fab")
    @ApiOperation(value = "判断导入的表格数据是否超行数 true可下一步 false超两千行，不能下一步")
    public ResponseResult<Boolean> judgeImportFabInfo(@RequestBody CommonRequest<String> request) {
        Boolean isCanStep = productFabService.judgeImportProductFabInfo(request.getRequestData());
        return ResponseResult.success(isCanStep);
    }
    // -------------------------导入商品fab信息 end ----------------------------------------------------



    @Autowired
    private FabAutoNameInfoService fabAutoNameInfoService;

    @ResponseBody
    @PostMapping("/query/fab/wsc/auto/name")
    @ApiOperation(value = "fab查询微商城自动品名 不传参是全部，传参按照款号,分割")
    public ResponseResult<Boolean> generateWscAutoName(@RequestBody CommonRequest<String> request) {
        fabAutoNameInfoService.generateAutoName(request.getRequestData(), AutoNameEnum.WSC);
        return ResponseResult.success(true);
    }

    @ResponseBody
    @PostMapping("/query/fab/ds/auto/name")
    @ApiOperation(value = "fab查询电商自动品名 不传参是全部，传参按照款号,分割")
    public ResponseResult<Boolean> generateDsAutoName(@RequestBody CommonRequest<String> request) {
        fabAutoNameInfoService.generateAutoName(request.getRequestData(), AutoNameEnum.DS);
        return ResponseResult.success(true);
    }


    // -------------------------fab查询商品信息 start ----------------------------------------------------
    @ResponseBody
    @PostMapping("/query/fab/detail/base/info")
    @ApiOperation(value = "查询fab详情信息-基本信息")
    public ResponseResult<ProductSpuFabBaseInfoEsResp> queryFabDetailBaseInfo(@RequestBody CommonRequest<String> request) {
        ProductSpuFabBaseInfoEsResp resp = productFabService.queryFabDetailBaseInfo(request.getRequestData());
        return ResponseResult.success(resp);
    }


    @ResponseBody
    @PostMapping("/query/fab/detail/selling/point")
    @ApiOperation(value = "查询fab详情信息-卖点信息")
    public ResponseResult<ProductSellingPointEsResp> queryFabDetailSellingPoint(@RequestBody CommonRequest<String> request) {
        ProductSellingPointEsResp resp = productFabService.queryFabDetailSellingPoint(request.getRequestData());
        return ResponseResult.success(resp);
    }

    @ResponseBody
    @PostMapping("/query/fab/detail/match/info")
    @ApiOperation(value = "查询fab详情信息-搭配信息")
    public ResponseResult<ProductMatchEsResp> queryFabDetailMatchInfo(@RequestBody CommonRequest<String> request) {
        ProductMatchEsResp resp = productFabService.queryFabDetailMatchInfo(request.getRequestData());
        return ResponseResult.success(resp);
    }


    @ResponseBody
    @PostMapping("/query/fab/detail/wash/info")
    @ApiOperation(value = "查询fab详情信息-洗涤信息")
    public ResponseResult<ProductWashInfoEsResp> queryFabDetailWashInfo(@RequestBody CommonRequest<String> request) {
        ProductWashInfoEsResp resp = productFabService.queryFabDetailWashInfo(request.getRequestData());
        return ResponseResult.success(resp);
    }

    //-------------------------查询环保信息-------------------------------------
    @ResponseBody
    @PostMapping("/query/fab/detail/env/info")
    @ApiOperation(value = "查询fab详情信息-环保信息")
    public ResponseResult<String> queryFabDetailEnvInfo(@RequestBody CommonRequest<String> request) {
        return ResponseResult.success(productEnvFabricInfoService.queryFabDetailEnvInfo(request.getRequestData()));
    }

    //-------------------------查询专利信息-------------------------------------
    @ResponseBody
    @PostMapping("/query/fab/detail/patent/info")
    @ApiOperation(value = "查询fab详情信息-专利信息")
    public ResponseResult<PatentInfoResp> queryFabDetailPatentInfo(@RequestBody CommonRequest<String> request) {
        return ResponseResult.success(productPatentService.queryFabDetailPatentInfo(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/test/export/patent/info")
    @ApiOperation(value = "测试导入专利信息")
    public ResponseResult<String> testExportPatentInfo(@RequestBody CommonRequest<String> request) {
        return ResponseResult.success(productPatentService.importProductPatentInfo(request.getRequestData(), "1", "system"));
    }


    @ResponseBody
    @PostMapping("/test/generate/evn/info")
    @ApiOperation(value = "测试生成环保信息")
    public ResponseResult<Boolean> testGenerateEvnInfo(@RequestBody CommonRequest<String> request) {
        productEnvFabricInfoService.generateEnvironmentFabricInfo(request.getRequestData());
        return ResponseResult.success(true);
    }


    @ResponseBody
    @PostMapping("/query/fab/bom/info")
    @ApiOperation(value = "商品面料信息 -- 根据款号")
    public ResponseResult<ProductSpuFabEsResp> queryFabBomInfo(@RequestBody CommonRequest<String> request) {
        ProductSpuFabEsResp resp = productFabService.queryFabBomInfo(request.getRequestData());
        return ResponseResult.success(resp);
    }


    @ResponseBody
    @PostMapping("/batch/query/selling/points")
    @ApiOperation(value = "根据商品id批量查询商品卖点信息")
    public ResponseResult<List<ProductSellingPointsEntity>> batchQuerySellingPoints(@RequestBody CommonRequest<List<String>> request) {
        return ResponseResult.success(productEnvFabricInfoService.batchQuerySellingPoints(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/add/product/selling/points")
    @ApiOperation(value = "添加商品卖点")
    public ResponseResult<Boolean> addProductSellingPoints(@RequestBody @Validated CommonRequest<ProductSellingPointsReq> request) {
        productEnvFabricInfoService.addProductSellingPoints(request.getRequestData());
        return ResponseResult.success(true);
    }


    @ResponseBody
    @PostMapping("/import/product/fabric/info")
    @ApiOperation(value = "导入商品的面料故事信息")
    public ResponseResult<String> importProductFabricStory(@RequestBody CommonRequest<String> request) {
        String url = productFabService.importProductFabricStory(request.getRequestData(), "1", "system");
        return ResponseResult.success(url);
    }


    @ResponseBody
    @PostMapping("/import/product/pattern")
    @ApiOperation(value = "导入商品的图案灵感说明")
    public ResponseResult<String> importProductPattern(@RequestBody CommonRequest<String> request) {
        String url = productFabService.importProductPattern(request.getRequestData(), "2", "system");
        return ResponseResult.success(url);
    }

    //------------------------- 提供批量接口 ----------------------
    @ResponseBody
    @PostMapping("/batch/query/fab/detail/selling/points")
    @ApiOperation(value = "批量获取商品卖点信息 - 不返回查库等品名信息")
    public ResponseResult<List<ProductSellingPointEsResp>> batchQueryFabDetailSellingPoints(@RequestBody CommonRequest<List<String>> request) {
        List<ProductSellingPointEsResp> resps = productFabService.batchQueryFabDetailSellingPoints(request.getRequestData());
        return ResponseResult.success(resps);
    }


    @ResponseBody
    @PostMapping("batch/query/fab/detail/wash/infos")
    @ApiOperation(value = "批量获取洗涤信息")
    public ResponseResult<List<ProductWashInfoEsResp>> batchQueryFabDetailWashInfos(@RequestBody CommonRequest<List<String>> request) {
        List<ProductWashInfoEsResp> resp = productFabService.batchQueryFabDetailWashInfos(request.getRequestData());
        return ResponseResult.success(resp);
    }
}
