package org.springcenter.background.api;

import com.alibaba.fastjson.JSONArray;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.modules.service.ISmartScreenInfoService;
import org.springcenter.product.api.dto.SampleProductReq;
import org.springcenter.product.api.dto.SampleProductSkcResp;
import org.springcenter.product.api.dto.back.SmartScreenProductInfoResp;
import org.springcenter.product.api.dto.back.SmartScreenSkcInfoResp;
import org.springcenter.product.api.dto.back.screen.StoreRecommendGoodsReq;
import org.springcenter.product.api.dto.back.screen.StoreRecommendGoodsResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2024/9/29 20:01
 */
@Slf4j
@RestController
@RequestMapping("/product/smart/screen/api")
@Api(value = "SmartScreenApiController",tags = "智慧大屏商品接口")
public class SmartScreenApiController {

    @Autowired
    private ISmartScreenInfoService smartScreenInfoService;

    @ResponseBody
    @PostMapping("/search/skc")
    @ApiOperation(value = "查询商品skc信息")
    public ResponseResult<List<SmartScreenSkcInfoResp>> searchProductSkcInfo(@RequestBody CommonRequest<String> request){
        return ResponseResult.success(smartScreenInfoService.searchProductSkcInfo(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/search/product/info")
    @ApiOperation(value = "查询商品基本信息")
    public ResponseResult<SmartScreenProductInfoResp> searchProductInfo(@RequestBody CommonRequest<String> request){
        return ResponseResult.success(smartScreenInfoService.searchProductInfo(request.getRequestData()));
    }



    @ResponseBody
    @PostMapping("/search/param/info")
    @ApiOperation(value = "筛选参数查询")
    public ResponseResult<JSONArray> searchParamInfo(@RequestBody CommonRequest request){
        return ResponseResult.success(smartScreenInfoService.searchParamInfo());
    }



    @ResponseBody
    @PostMapping("/screen/recommend/product/list")
    @ApiOperation(value = "推荐商品列表")
    public ResponseResult<List<StoreRecommendGoodsResp>> screenRecommendProductList(@RequestBody CommonRequest<StoreRecommendGoodsReq> request){
        log.info("推荐商品列表 入参：{}", JSONArray.toJSONString(request));
        List<StoreRecommendGoodsResp> resp = smartScreenInfoService.screenRecommendProductList(request.getRequestData(), request.getPage());
        return ResponseResult.success(resp, request.getPage());
    }


    @ResponseBody
    @PostMapping("/screen/recommend/scroll/product/list")
    @ApiOperation(value = "推荐商品列表")
    public ResponseResult<List<StoreRecommendGoodsResp>> screenRecommendScrollProductList(@RequestBody CommonRequest<StoreRecommendGoodsReq> request){
        log.info("推荐商品列表 入参：{}", JSONArray.toJSONString(request));
        List<StoreRecommendGoodsResp> resp = smartScreenInfoService.screenRecommendScrollProductList(request.getRequestData(), request.getPage());
        return ResponseResult.success(resp, request.getPage());
    }

}
