package org.springcenter.background.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.config.exception.ProductException;
import org.springcenter.background.modules.service.IDisplayBookService;
import org.springcenter.product.api.dto.*;
import org.springcenter.product.api.enums.DisplayBookPictureEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/product/display/book/api")
@Api(value = "DisplayBookApi", tags = "商品陈列册")
public class DisplayBookController {

    @Autowired
    private IDisplayBookService iDisplayBookService;

    // ------------------------------------ 陈列册管理 start ---------------------------------------

    @ResponseBody
    @PostMapping("/info/save")
    @ApiOperation(value = "陈列册管理-新建记录")
    public ResponseResult<String> saveInfo(@RequestBody CommonRequest<DisplayBookInfoAddReq> request) {
        return ResponseResult.success(iDisplayBookService.saveInfo(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/info/list")
    @ApiOperation(value = "陈列册管理-查询列表")
    public ResponseResult<List<DisplayBookListQueryResp>> listInfos(@RequestBody @Validated CommonRequest<DisplayBookListQueryReq> request) {
        return ResponseResult.success(
                iDisplayBookService.listInfos(request.getRequestData(), request.getPage(), request.getComponent()),
                request.getPage());
    }


    @ResponseBody
    @PostMapping("/info/get")
    @ApiOperation(value = "陈列册管理-获取详情")
    public ResponseResult<DisplayBookInfoQueryResp> getInfo(@RequestBody @Validated CommonRequest<String> request) {
        return ResponseResult.success(iDisplayBookService.getInfo(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/info/update")
    @ApiOperation(value = "陈列册管理-修改信息")
    public ResponseResult<Boolean> updateInfo(@RequestBody @Validated CommonRequest<DisplayBookInfoUpdateReq> request) {
        return ResponseResult.success(iDisplayBookService.updateInfo(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/info/delete")
    @ApiOperation(value = "陈列册管理-删除记录")
    public ResponseResult<Boolean> delete(@RequestBody @Validated CommonRequest<DisplayBookInfoUpdateReq> request) {
        return ResponseResult.success(iDisplayBookService.delete(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/info/list/for/pos")
    @ApiOperation(value = "移动端-查询列表")
    public ResponseResult<List<DisplayBookListForPosQueryResp>> listInfosForPos(@RequestBody @Validated CommonRequest<DisplayBookListForPosQueryReq> request) {
        return ResponseResult.success(
                iDisplayBookService.listInfosForPos(request.getRequestData(), request.getPage(), request.getComponent()),
                request.getPage());
    }
    @ResponseBody
    @PostMapping("/info/get/for/pos")
    @ApiOperation(value = "移动端-查询详情")
    public ResponseResult<DisplayBookInfoQueryResp> getInfoForPos(@RequestBody @Validated CommonRequest<String> request) {
        return ResponseResult.success(iDisplayBookService.getInfo(request.getRequestData()));

    }

    @ResponseBody
    @PostMapping("/getChannel")
    @ApiOperation(value = "陈列册管理-获取渠道")
    public ResponseResult<List<String>> getChannel(@RequestBody @Validated CommonRequest<String> request) {
        return ResponseResult.success(iDisplayBookService.getChannel(request.getComponent()));
    }

    @ResponseBody
    @PostMapping("/info/checkFileExist")
    @ApiOperation(value = "陈列册管理-新建前检验", notes = "是否需要提示[没有上传图片和关联款，保存后将无法再次上传]。false=不存在映射关系")
    public ResponseResult<Boolean> checkFileExist(@RequestBody @Validated CommonRequest<DisplayBookInfoAddCheckReq> request) {
        return ResponseResult.success(iDisplayBookService.checkFileExist(request.getRequestData()));
    }

    // ------------------------------------ 陈列册管理 end ---------------------------------------


    // ------------------------------------ 陈列册映射 start ---------------------------------------
    @ResponseBody
    @PostMapping("/file/mapping/info/save")
    @ApiOperation(value = "陈列册映射-新建记录")
    public ResponseResult<String> saveFileMappingInfo(@RequestBody CommonRequest<DisplayBookFileMappingInfoAddReq> request) {
        return ResponseResult.success(iDisplayBookService.saveFileMappingInfo(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/file/mapping/info/list")
    @ApiOperation(value = "陈列册映射-列表查询")
    public ResponseResult<List<DisplayBookFileMappingListQueryResp>> listFileMappingInfos(@RequestBody CommonRequest<DisplayBookFileMappingListQueryReq> request) {
        return ResponseResult.success(
                iDisplayBookService.listFileMappingInfos(request.getRequestData(), request.getPage(), request.getComponent()),
                request.getPage());
    }


    @ResponseBody
    @PostMapping("/file/checkInfoExist")
    @ApiOperation(value = "陈列册映射-上传前检验", notes = "是否需要提示[本品牌本货季已经存在陈列册，无法再次上传]。false=不存在陈列册")
    public ResponseResult<Boolean> checkInfoExist(@RequestBody @Validated CommonRequest<String> request) {
        return ResponseResult.success(iDisplayBookService.checkInfoExist(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/file/mapping/info/testImportFile")
    @ApiOperation(value = "测试解析文档")
    public ResponseResult<String> importFile(@RequestBody CommonRequest<DisplayBookPictureMappingAddReq> request) {
        DisplayBookPictureEnum type = request.getRequestData().getType();
        switch (type) {
            case PICTURE_MANNEQUIN:
                return ResponseResult.success(iDisplayBookService.analysisMannequinPictureFile(request.getRequestData()));
            case PICTURE_HANGER:
                return ResponseResult.success(iDisplayBookService.analysisHangerPictureFile(request.getRequestData()));
            default:
                throw new ProductException("无法识别的类型");
        }
    }

    // ------------------------------------ 陈列册映射 end ---------------------------------------

}
