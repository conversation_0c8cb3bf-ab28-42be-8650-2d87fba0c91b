package org.springcenter.background.job;

import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.modules.enums.AutoNameEnum;
import org.springcenter.background.modules.service.FabAutoNameInfoService;
import org.springcenter.background.modules.service.IProductReminderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 * @Date:2025/5/19 11:19
 */
@Slf4j
@Component
public class ProductReminderInfoJob  extends IJobHandler {

    @Autowired
    private IProductReminderService productReminderService;

    @Override
    @XxlJob("productFabReminderJob")
    public void execute() throws Exception {
        log.info("===================发送消息提醒");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("productReminderInfoJob开始");
        productReminderService.fabReminderJob();
        stopWatch.stop();
        log.info("===========发送消息提醒" + stopWatch.prettyPrint());
        log.info("===================结束发送消息提醒");
    }
}