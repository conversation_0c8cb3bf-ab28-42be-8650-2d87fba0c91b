package org.springcenter.background.job;

import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.modules.enums.AutoNameEnum;
import org.springcenter.background.modules.service.FabAutoNameInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 * @Date:2023/12/8 10:31
 */
@Slf4j
@Component
public class ProductDsAutoNameJob extends IJobHandler {

    @Autowired
    private FabAutoNameInfoService fabAutoNameInfoService;

    @Override
    @XxlJob("productDsAutoNameJob")
    public void execute() throws Exception {
        log.info("===================开始计算电商自动品名");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("productAutoNameJob开始");
        fabAutoNameInfoService.generateAutoName("", AutoNameEnum.DS);
        stopWatch.stop();
        log.info("===========电商自动品名" + stopWatch.prettyPrint());
        log.info("===================结束计算电商自动品名");
    }
}
