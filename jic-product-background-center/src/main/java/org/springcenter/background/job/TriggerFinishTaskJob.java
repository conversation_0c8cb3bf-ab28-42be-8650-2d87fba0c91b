package org.springcenter.background.job;

import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.background.modules.service.IFabTaskCenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 * @Date:2023/12/8 10:31
 */
@Slf4j
@Component
public class TriggerFinishTaskJob extends IJobHandler {


    @Autowired
    private IFabTaskCenterService fabTaskCenterService;

    @Override
    @XxlJob("triggerFinishTaskJob")
    public void execute() throws Exception {
        log.info("===================开始触发完成任务");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("triggerFinishTaskJob开始");
        fabTaskCenterService.tryFinishTask();
        stopWatch.stop();
        log.info("===========开始触发完成任务" + stopWatch.prettyPrint());
        log.info("===================开始触发完成任务");
    }
}
