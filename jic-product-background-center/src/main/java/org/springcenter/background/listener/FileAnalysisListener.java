package org.springcenter.background.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.Message;
import org.springcenter.background.config.exception.ProductException;
import org.springcenter.background.modules.service.*;
import org.springcenter.background.modules.util.RedissonUtil;
import org.springcenter.product.api.dto.DisplayBookPictureMappingAddReq;
import org.springcenter.product.api.dto.ExportSizeInfoDataReq;
import org.springcenter.product.api.dto.QueryGoodsFabEsReq;
import org.springcenter.product.api.enums.DisplayBookPictureEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Description:监听需要解析的文件
 * @Author: brian
 * @Date: 2021/8/30 15:09
 */
@Component
@Slf4j
public class FileAnalysisListener implements IMessageListener {

    @Value("${mq.upload.analysis.topic}")
    private String topic;

    @Value("${mq.upload.analysis.tags}")
    private String tags;

    @Autowired
    private RedisPoolUtil redisPoolUtil;

    @Autowired
    private IProductDetailService productDetailService;

    @Autowired
    private IDisplayBookService displayBookService;

    @Autowired
    private RedissonUtil redissonUtil;

    @Autowired
    private IProductFabService productFabService;

    @Autowired
    private IProductSizeInfoService productSizeInfoService;

    @Autowired
    private IProductPatentService productPatentService;

    @Override
    public String getTopic() {
        return topic;
    }

    @Override
    public String getTags() {
        return tags;
    }


    private static final String CHECK_REPORT_TAG = "CHECK_REPORT_TAG";

    // 品牌部映射关系
    private static final String IMPORT_BRAND_RELATION = "IMPORT_BRAND_RELATION";

    // 电商部映射关系
    private static final String IMPORT_ECOM_RELATION = "IMPORT_ECOM_RELATION";

    // 样衣导入excel
    private static final String IMPORT_SAMPLE_CLOTH = "IMPORT_SAMPLE_CLOTH";

    // 人台图导入Excel
    private static final String IMPORT_MANNEQUIN_PICTURE = "IMPORT_MANNEQUIN_PICTURE";
    // 货杆图导入Excel
    private static final String IMPORT_HANGER_PICTURE = "IMPORT_HANGER_PICTURE";

    // 上新表格
    private static final String PRODUCT_NEW_ARRIVAL_TAG = "PRODUCT_NEW_ARRIVAL_TAG";
    // 成衣尺码导出
    private static final String FAB_SIZE_INFO_TAG = "FAB_SIZE_INFO_TAG";

    // 商品fab的导入
    private static final String IMPORT_PRODUCT_FAB = "IMPORT_PRODUCT_FAB";


    // 商品专利的导入
    private static final String IMPORT_PRODUCT_PATENT = "IMPORT_PRODUCT_PATENT";

    // 面料故事的导入
    private static final String IMPORT_PRODUCT_FABRIC_STORY = "IMPORT_PRODUCT_FABRIC_STORY";

    // 图案灵感说明的导入
    private static final String IMPORT_PRODUCT_PATTERN = "IMPORT_PRODUCT_PATTERN";



    private static final String SYNC_STREET_PHOTO = "SYNC_STREET_PHOTO";
    private static final String SYNC_ECOM_PHOTO = "SYNC_ECOM_PHOTO";
    private static final String IMPORT_STREET_PRODUCT_RELATION = "IMPORT_STREET_PRODUCT_RELATION";

    @Autowired
    private WrongGoodsService wrongGoodsService;


    @Override
    public ConsumeConcurrentlyStatus consume(Message msg) {
        String keys = msg.getKeys();
        String msgTags = msg.getTags();
        List<String> confTags = Arrays.asList(tags.split(","));

        if (!confTags.contains(msgTags)){
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        String para = null;
        try {
            para = new String(msg.getBody(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(para);
            if(CHECK_REPORT_TAG.equals(msg.getTags())){
//                String url = jsonObject.getString("url");
//                String message = checkReportService.importCheckReport(url);
//                if(StringUtils.isNotBlank(message)){
//                    // 返回的url数据
//                    RedisTemplateUtil.setex(redisPoolUtil, keys, message, 600);
//                }
            } else if (IMPORT_BRAND_RELATION.equals(msg.getTags())) {
                // 导入品牌部的关系
                String url = jsonObject.getString("url");
                String respUrl = productDetailService.importBrandExcelCollocationReleation(url);
                if(StringUtils.isNotBlank(respUrl)){
                    RedisTemplateUtil.setex(redisPoolUtil, keys, respUrl, 600);
                }
            }else if (IMPORT_ECOM_RELATION.equals(msg.getTags())) {
                // 导入电商部的关系
                String url = jsonObject.getString("url");
                String respUrl = productDetailService.importEComExcelCollocationReleation(url);
                if(StringUtils.isNotBlank(respUrl)){
                    RedisTemplateUtil.setex(redisPoolUtil, keys, respUrl, 600);
                }
            }else if(IMPORT_SAMPLE_CLOTH.equals(msg.getTags())){
                // 导入样衣 此项不作操作  是从网盘读取到数据库中， 非读取excel

            } else if (IMPORT_MANNEQUIN_PICTURE.equals(msg.getTags())){
                log.info("开始消费导入的人台图excel，keys[{}]，消息内容:{}", keys, JSON.toJSONString(jsonObject));
                String lockKey = "IMPORT_MANNEQUIN_PICTURE:" + keys;
                // 加锁5分钟处理
                if (!redissonUtil.tryLock(lockKey, 1, 300)) {
                    // 人台图/货杆图文件解析幂等校验时如果是重复消费的则直接不处理，也不报错，报错可能导致后台正在执行第一个任务，但是第二个任务抛出异常导致页面提示错误。
                    log.error("重复消费人台图，终止执行");
                } else {
                    try {
                        // 人台图
                        DisplayBookPictureMappingAddReq build = buildParam(jsonObject, DisplayBookPictureEnum.PICTURE_MANNEQUIN);
                        String respUrl = displayBookService.analysisMannequinPictureFile(build);
                        log.info("处理完导入的货杆图excel，keys[{}]，处理后异常的url:{}", keys, respUrl);
                        RedisTemplateUtil.setex(redisPoolUtil, keys, respUrl, 600);
                    } finally {
                        redissonUtil.unlock(lockKey);
                    }
                }
            } else if (IMPORT_HANGER_PICTURE.equals(msg.getTags())){
                log.info("开始消费导入的货杆图excel，keys[{}]，消息内容:{}", keys, JSON.toJSONString(jsonObject));
                String lockKey = "IMPORT_HANGER_PICTURE:" + keys;
                // 加锁5分钟处理
                if (!redissonUtil.tryLock(lockKey, 1, 300)) {
                    log.error("重复消费货杆图导入，终止执行");
                } else {
                    try {
                        // 货杆图
                        DisplayBookPictureMappingAddReq build = buildParam(jsonObject, DisplayBookPictureEnum.PICTURE_HANGER);
                        String respUrl = displayBookService.analysisHangerPictureFile(build);
                        log.info("处理完导入的货杆图excel，keys[{}]，是否异常[{}]，如果异常，url为:{}", keys, StringUtils.isNotBlank(respUrl), respUrl);
                        RedisTemplateUtil.setex(redisPoolUtil, keys, respUrl, 600);
                    } finally {
                        redissonUtil.unlock(lockKey);
                    }
                }
            } else if (Objects.equals(PRODUCT_NEW_ARRIVAL_TAG, msg.getTags())) {
                // 导出商品的上新表格
                //*String param = jsonObject.getString("outId");*//*
                log.info("导出上新表格入参：{}", jsonObject);
                QueryGoodsFabEsReq req = JSONObject.parseObject(String.valueOf(jsonObject), QueryGoodsFabEsReq.class);
                productFabService.exportProductFabNewArrivalInfo(keys, req);
            } else if (Objects.equals(FAB_SIZE_INFO_TAG, msg.getTags())) {
                log.info("========导出尺码表入参：{}", jsonObject);
                ExportSizeInfoDataReq req = JSONObject.parseObject(String.valueOf(jsonObject), ExportSizeInfoDataReq.class);
                productSizeInfoService.exportSizeInfoData(keys, req);
            } else if (Objects.equals(IMPORT_PRODUCT_FAB, msg.getTags())) {
                log.info("商品fab批量导入:{}", jsonObject);
                String url = jsonObject.getString("url");
                String type = jsonObject.getString("outId");
                String respUrl = productFabService.importProductFabInfo(url, keys, type);
                RedisTemplateUtil.setex(redisPoolUtil, keys, respUrl, 600);
            }else if (Objects.equals(SYNC_STREET_PHOTO, msg.getTags())) {
                log.info("商品街拍同步网盘:{}", jsonObject);
                String url = jsonObject.getString("url");
                wrongGoodsService.syncStreetPhotoImg(url,null);
            }else if (Objects.equals(SYNC_ECOM_PHOTO, msg.getTags())) {
                log.info("商品街拍同步网盘:{}", jsonObject);
                String url = jsonObject.getString("url");
                wrongGoodsService.syncEcomImg(url);
            }
            else if (Objects.equals(IMPORT_STREET_PRODUCT_RELATION, msg.getTags())) {
                log.info("商品街拍商品同步 excel:{}", jsonObject);
                String url = jsonObject.getString("url");
                String respUrl = wrongGoodsService.importStreetPhotoRelation(url, keys);
                RedisTemplateUtil.setex(redisPoolUtil, keys, respUrl, 600);
            }  else if (Objects.equals(IMPORT_PRODUCT_PATENT, msg.getTags())) {
                log.info("商品专利信息批量导入:{}", jsonObject);
                String url = jsonObject.getString("url");
                String type = jsonObject.getString("outId");
                String respUrl = productPatentService.importProductPatentInfo(url, keys, type);
                RedisTemplateUtil.setex(redisPoolUtil, keys, respUrl, 600);
            }   else if (Objects.equals(IMPORT_PRODUCT_FABRIC_STORY, msg.getTags())) {
                log.info("商品面料故事信息批量导入:{}", jsonObject);
                String url = jsonObject.getString("url");
                String type = jsonObject.getString("outId");
                String respUrl = productFabService.importProductFabricStory(url, keys, type);
                RedisTemplateUtil.setex(redisPoolUtil, keys, respUrl, 600);
            }  else if (Objects.equals(IMPORT_PRODUCT_PATTERN, msg.getTags())) {
                log.info("商品图案灵感说明批量导入:{}", jsonObject);
                String url = jsonObject.getString("url");
                String type = jsonObject.getString("outId");
                String respUrl = productFabService.importProductPattern(url, keys, type);
                RedisTemplateUtil.setex(redisPoolUtil, keys, respUrl, 600);
            }

        } catch (Exception e) {
            log.error("监听上传下载文件处理异常,tags={},param={}",msg.getTags(),para,e);
            if(!RedisTemplateUtil.exists(redisPoolUtil,keys+"error")) {
                // 缓存1分钟
                RedisTemplateUtil.setex(redisPoolUtil, keys + "error", msg.getBody()+"文件处理异常", 60);
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private static DisplayBookPictureMappingAddReq buildParam(JSONObject jsonObject, DisplayBookPictureEnum pictureMannequin) {
        String url = jsonObject.getString("url");
        // id,操作人
        String outId = jsonObject.getString("outId");
        String operator = null;
        String id = null;
        if (StringUtils.isNotBlank(outId) && outId.contains(",")) {
            String[] split = outId.split(",");
            id = split[0];
            operator = split[1];
        }

        DisplayBookPictureMappingAddReq build = DisplayBookPictureMappingAddReq.builder()
                .url(url).id(id).operator(operator).type(pictureMannequin).build();
        return build;
    }

}
