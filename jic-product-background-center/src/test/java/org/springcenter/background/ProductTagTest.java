package org.springcenter.background;

import com.alibaba.excel.annotation.ExcelProperty;

public class ProductTagTest {
    static class Tag{
        @ExcelProperty({"标签名称"})
        private String name;

        @ExcelProperty({"标签编码"})
        private String code;

        @ExcelProperty({"标签层级"})
        private String level;

        public Tag() {
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getLevel() {
            return level;
        }

        public void setLevel(String level) {
            this.level = level;
        }
    }
}
