package org.springcenter.background;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.Map.Entry;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.util.excel.BaseAbstractReaderListener;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.common.util.excel.IWriteDataExcel;
import lombok.Data;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springcenter.background.modules.mapper.bojun.HrEmployeeMapper;
import org.springcenter.background.modules.remote.entity.SendMsgCpEntity;
import org.springcenter.background.modules.remote.service.IMessageService;
import org.springcenter.background.modules.service.IFabTaskCenterService;
import org.springcenter.background.modules.util.HttpUtils;
import org.springcenter.product.api.dto.FabTaskIsFinishReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date:2024/3/19 14:38
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={JicBackgroundProductApplication.class})
public class TaskFabTest {

    @Autowired
    private IFabTaskCenterService taskCenterService;

    @Test
    public void testCreateTask() {
        // 创建浏览任务
        taskCenterService.createBrowseTask();

        // 创建考核任务
        taskCenterService.createAssessTask();
    }

    @Test
    public void isFinish() {
        FabTaskIsFinishReq req = new FabTaskIsFinishReq();
        req.setFabVolumeId("99");
        req.setHrId(277684L);
        req.setCStoreId(401395L);
        Boolean finishTask = taskCenterService.isFinishTask(req);
        System.out.println("========是否完成：{}" + finishTask);
    }


    @Test
    public void finishTask() {
        FabTaskIsFinishReq req = new FabTaskIsFinishReq();
        req.setFabVolumeId("99");
        req.setHrId(277684L);
        req.setCStoreId(401395L);
        Boolean finishTask = taskCenterService.finishTask(req);
        System.out.println("========是否完成：{}" + finishTask);
    }

    @Autowired
    private HrEmployeeMapper hrEmployeeMapper;

    @Autowired
    private IMessageService messageService;

    @Test
    public void testSendMsg() {
        // 调用企业微信
        SendMsgCpEntity cpEntity = new SendMsgCpEntity();
        cpEntity.setMsgId(211);
        Long employeeBaseId = hrEmployeeMapper.selectEmployeeBaseById(378243L);
        if (employeeBaseId == null) {
            return;
        }
        SendMsgCpEntity.SendMsgCpInner cpInner = new SendMsgCpEntity.SendMsgCpInner();
        String name = "111-B";
        cpInner.setTitleParams(name);
        cpInner.setToUser(Objects.toString(employeeBaseId));
        cpInner.setPageParams("67253"+ ",2582");
        cpInner.setContentParams(name + ",线上id反馈测试啦啦啦啦1111");
        cpEntity.setParams(Lists.newArrayList(cpInner));

        messageService.sendMsgCp(cpEntity);
    }

    //java版计算signature签名
        public static void main(String[] args) throws UnsupportedEncodingException,
                NoSuchAlgorithmException {
            //{"status":0,"result":{"location":{"lng":116.3077394657582,"lat":40.05694432054287},"precise":0,"confidence":75,"comprehension":0,"level":"商务大厦"}},解析json取出location节点的lat和lng的数值
            String readExcelUrl = "/Users/<USER>/Downloads/供应商地址（已匹配目前合作工厂）.xlsx";
            List<Company> companyList = new ArrayList<>();
            EasyExcelUtil.read(readExcelUrl, new BaseNoModelDataAbstractListener() {
                @Override
                protected void saveData() {
                    this.cachedDataList.forEach(data -> {
                        Company company = new Company();
                        Map<Integer, String> column = data;
                        company.setId(column.get(0));
                        company.setCompanyCode(column.get(1));
                        company.setCompanyName(column.get(2));
                        company.setCompanyFullName(column.get(3));
                        company.setAddress(column.get(4));
                        company.setProvince(column.get(5));
                        //此处调用经纬度接口
                        String result = getLangitudeAndLatitude(company.getProvince(), company.getAddress(), company.getCompanyName());
                        if (!result.equals("")) {
                            String[] lngat = result.split(",");
                            company.setLng(lngat[0]);
                            company.setLat(lngat[1]);
                        }
                        companyList.add(company);
                    });
                }
            });

            EasyExcelUtil.write("/Users/<USER>/Downloads/abc12356.xlsx", Company.class, new IWriteDataExcel<Company>() {
                @Override
                public List<Company> getData() {
                    return companyList;
                }
            });
        }

        public static String getLangitudeAndLatitude(String province, String address, String company) {
            //将address中包含的province替换掉
            if (Objects.nonNull(province)) {
                address = address.replace(province, "");
            }
            if (Objects.nonNull(company)) {
                address = address.replace(company, "");
            }
            String detailAddress = (Objects.isNull(province) ? "" : province) + (Objects.isNull(address) ? "" : address) + company;
            String url = "https://api.map.baidu.com/geocoding/v3/?address=%s&output=json&ak=rd5XNNJJV0kCdwIplOtadTwGZnmnba6X&sn=%s";
            Map paramsMap = new LinkedHashMap<String, String>();
            paramsMap.put("address", detailAddress);
            paramsMap.put("output", "json");
            paramsMap.put("ak", "rd5XNNJJV0kCdwIplOtadTwGZnmnba6X");
            String paramsStr = null;
            try {
                paramsStr = toQueryString(paramsMap);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            String wholeStr = new String("/geocoding/v3/?" + paramsStr + "UtK12FVlLKTmAb9Qtlme0UED7tN7lEoC");
            // 对上面wholeStr再作utf8编码
            String tempStr = null;
            try {
                tempStr = MD5(URLEncoder.encode(wholeStr, "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            String body = HttpUtils.get(String.format(url, detailAddress, tempStr));
            //{"status":0,"result":{"location":{"lng":116.3077394657582,"lat":40.05694432054287},"precise":0,"confidence":75,"comprehension":0,"level":"商务大厦"}}
            //如果status为0，则解析json取出location节点的lat和lng的数值
            System.out.println(company+"======"+detailAddress + "=======" + body);
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.containsKey("status") && jsonObject.getInteger("status") == 0) {
                JSONObject result = jsonObject.getJSONObject("result");
                JSONObject location = result.getJSONObject("location");
                return location.getString("lng") + "," + location.getString("lat");
            }
            return "";
        }
        // 对Map内所有value作utf8编码，拼接返回结果
        public static String toQueryString(Map<?, ?> data)
                throws UnsupportedEncodingException {
            StringBuffer queryString = new StringBuffer();
            for (Entry<?, ?> pair : data.entrySet()) {
                queryString.append(pair.getKey() + "=");
                queryString.append(URLEncoder.encode((String) pair.getValue(),
                        "UTF-8") + "&");
            }
            if (queryString.length() > 0) {
                queryString.deleteCharAt(queryString.length() - 1);
            }
            return queryString.toString();
        }

        // 来自stackoverflow的MD5计算方法，调用了MessageDigest库函数，并把byte数组结果转换成16进制
        public static String MD5(String md5) {
            try {
                java.security.MessageDigest md = java.security.MessageDigest
                        .getInstance("MD5");
                byte[] array = md.digest(md5.getBytes());
                StringBuffer sb = new StringBuffer();
                for (int i = 0; i < array.length; ++i) {
                    sb.append(Integer.toHexString((array[i] & 0xFF) | 0x100)
                            .substring(1, 3));
                }
                return sb.toString();
            } catch (java.security.NoSuchAlgorithmException e) {
            }
            return null;
        }

        static class Company {
            @ExcelProperty({"序号"})
            private String id;

            @ExcelProperty({"供应商编号"})
            private String companyCode;

            @ExcelProperty({"供应商简称"})
            private String companyName;

            @ExcelProperty({"供应商全称"})
            private String companyFullName;

            @ExcelProperty({"地址"})
            private String address;

            @ExcelProperty({"所在省"})
            private String province;

            @ExcelProperty({"经度"})
            private String lng;

            @ExcelProperty({"维度"})
            private String lat;

            public Company() {
            }

            public String getLng() {
                return lng;
            }

            public void setLng(String lng) {
                this.lng = lng;
            }

            public String getLat() {
                return lat;
            }

            public void setLat(String lat) {
                this.lat = lat;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getCompanyCode() {
                return companyCode;
            }

            public void setCompanyCode(String companyCode) {
                this.companyCode = companyCode;
            }

            public String getCompanyName() {
                return companyName;
            }

            public void setCompanyName(String companyName) {
                this.companyName = companyName;
            }

            public String getCompanyFullName() {
                return companyFullName;
            }

            public void setCompanyFullName(String companyFullName) {
                this.companyFullName = companyFullName;
            }

            public String getAddress() {
                return address;
            }

            public void setAddress(String address) {
                this.address = address;
            }

            public String getProvince() {
                return province;
            }

            public void setProvince(String province) {
                this.province = province;
            }
        }
}
