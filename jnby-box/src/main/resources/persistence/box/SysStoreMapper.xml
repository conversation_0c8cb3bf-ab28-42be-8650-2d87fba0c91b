<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.SysStoreMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.SysStore">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="C_STORE_ID"  property="cStoreId" />
    <result column="C_STORE_CODE" jdbcType="VARCHAR" property="cStoreCode" />
    <result column="C_STORE_NAME" jdbcType="VARCHAR" property="cStoreName" />
    <result column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="PROVINCE" jdbcType="VARCHAR" property="province" />
    <result column="CITY" jdbcType="VARCHAR" property="city" />
    <result column="DISTRICT" jdbcType="VARCHAR" property="district" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="CONTACT" jdbcType="VARCHAR" property="contact" />
    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="BRAND" jdbcType="VARCHAR" property="brand" />
    <result column="EB" jdbcType="DECIMAL" property="eb" />
    <result column="C_CUSTOMER_ID" jdbcType="DECIMAL" property="cCustomerId"/>
    <result column="C_UNIONSTORE_ID" jdbcType="VARCHAR" property="cUnionstoreId" />
    <result column="DEL_FLAG" jdbcType="DECIMAL" property="delFlag" />

    <result column="AREA_ID" jdbcType="DECIMAL" property="areaId" />
  </resultMap>

  <resultMap id="BaseResultMapJoin" type="com.jnby.infrastructure.box.model.SysStore">
    <id column="ss.ID" jdbcType="VARCHAR" property="id" />
    <result column="ss.C_STORE_ID"  property="cStoreId" />
    <result column="ss.C_STORE_CODE" jdbcType="VARCHAR" property="cStoreCode" />
    <result column="ss.C_STORE_NAME" jdbcType="VARCHAR" property="cStoreName" />
    <result column="ss.CODE" jdbcType="VARCHAR" property="code" />
    <result column="ss.PROVINCE" jdbcType="VARCHAR" property="province" />
    <result column="ss.CITY" jdbcType="VARCHAR" property="city" />
    <result column="ss.DISTRICT" jdbcType="VARCHAR" property="district" />
    <result column="ss.ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="ss.CONTACT" jdbcType="VARCHAR" property="contact" />
    <result column="ss.PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="ss.CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="ss.UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ss.BRAND" jdbcType="VARCHAR" property="brand" />
    <result column="ss.EB" jdbcType="DECIMAL" property="eb" />
    <result column="ss.C_CUSTOMER_ID" jdbcType="DECIMAL" property="cCustomerId"/>
  </resultMap>
  <sql id="Base_Column_List">
    ID, C_STORE_ID, C_STORE_CODE, C_STORE_NAME, CODE, PROVINCE, CITY, DISTRICT, ADDRESS,
    CONTACT, PHONE, CREATE_TIME, UPDATE_TIME, BRAND, EB,C_CUSTOMER_ID,C_UNIONSTORE_ID,DEL_FLAG,AREA_ID
  </sql>
  <sql id="Base_Column_List_JOIN">
    ss.ID, ss.C_STORE_ID, ss.C_STORE_CODE, ss.C_STORE_NAME, ss.CODE, ss.PROVINCE, ss.CITY, ss.DISTRICT, ss.ADDRESS,
    ss.CONTACT, ss.PHONE, ss.CREATE_TIME, ss.UPDATE_TIME, ss.BRAND, ss.EB, ss.C_CUSTOMER_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from SYS_STORE
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from SYS_STORE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.jnby.infrastructure.box.model.SysStore">
    insert INTO SYS_STORE (ID, C_STORE_ID, C_STORE_CODE,
      C_STORE_NAME, CODE, PROVINCE,
      CITY, DISTRICT, ADDRESS,
      CONTACT, PHONE, CREATE_TIME,
      UPDATE_TIME, BRAND, EB
      )
    values (#{id,jdbcType=VARCHAR}, #{cStoreId,jdbcType=DECIMAL}, #{cStoreCode,jdbcType=VARCHAR},
      #{cStoreName,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR},
      #{city,jdbcType=VARCHAR}, #{district,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
      #{contact,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{brand,jdbcType=VARCHAR}, #{eb,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jnby.infrastructure.box.model.SysStore">
    insert INTO SYS_STORE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="cStoreId != null">
        C_STORE_ID,
      </if>
      <if test="cStoreCode != null">
        C_STORE_CODE,
      </if>
      <if test="cStoreName != null">
        C_STORE_NAME,
      </if>
      <if test="code != null">
        CODE,
      </if>
      <if test="province != null">
        PROVINCE,
      </if>
      <if test="city != null">
        CITY,
      </if>
      <if test="district != null">
        DISTRICT,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="contact != null">
        CONTACT,
      </if>
      <if test="phone != null">
        PHONE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="brand != null">
        BRAND,
      </if>
      <if test="eb != null">
        EB,
      </if>
      <if test="cUnionstoreId != null">
        C_UNIONSTORE_ID,
      </if>
      <if test="cCustomerId != null">
        C_CUSTOMER_ID,
      </if>
      <if test="areaId != null">
        AREA_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="cStoreId != null">
        #{cStoreId,jdbcType=DECIMAL},
      </if>
      <if test="cStoreCode != null">
        #{cStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="cStoreName != null">
        #{cStoreName,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        #{district,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="eb != null">
        #{eb,jdbcType=DECIMAL},
      </if>
      <if test="cUnionstoreId != null">
        #{cUnionstoreId,jdbcType=VARCHAR},
      </if>
      <if test="cCustomerId != null">
         #{cCustomerId,jdbcType=DECIMAL},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.box.model.SysStore">
    update SYS_STORE
    <set>
      <if test="cStoreId != null">
        C_STORE_ID = #{cStoreId,jdbcType=DECIMAL},
      </if>
      <if test="cStoreCode != null">
        C_STORE_CODE = #{cStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="cStoreName != null">
        C_STORE_NAME = #{cStoreName,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        CODE = #{code,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        PROVINCE = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        CITY = #{city,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        DISTRICT = #{district,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        CONTACT = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        PHONE = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="brand != null">
        BRAND = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="eb != null">
        EB = #{eb,jdbcType=DECIMAL},
      </if>
      <if test="cUnionstoreId != null">
        C_UNIONSTORE_ID = #{cUnionstoreId,jdbcType=VARCHAR},
      </if>
      <if test="cCustomerId != null">
        C_CUSTOMER_ID = #{cCustomerId,jdbcType=DECIMAL},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=DECIMAL},
      </if>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jnby.infrastructure.box.model.SysStore">
    update SYS_STORE
    set C_STORE_ID = #{cStoreId,jdbcType=DECIMAL},
      C_STORE_CODE = #{cStoreCode,jdbcType=VARCHAR},
      C_STORE_NAME = #{cStoreName,jdbcType=VARCHAR},
      CODE = #{code,jdbcType=VARCHAR},
      PROVINCE = #{province,jdbcType=VARCHAR},
      CITY = #{city,jdbcType=VARCHAR},
      DISTRICT = #{district,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      CONTACT = #{contact,jdbcType=VARCHAR},
      PHONE = #{phone,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      BRAND = #{brand,jdbcType=VARCHAR},
      EB = #{eb,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectBySelective"  resultMap="BaseResultMap" parameterType="com.jnby.infrastructure.box.model.SysStore">
    select
    <include refid="Base_Column_List" />
    from SYS_STORE
    <where>
      <if test="cStoreId != null">
        and  C_STORE_ID = #{cStoreId,jdbcType=DECIMAL}
      </if>
      <if test="cStoreCode != null">
        and C_STORE_CODE = #{cStoreCode,jdbcType=VARCHAR}
      </if>
      <if test="cStoreName != null">
        and C_STORE_NAME = #{cStoreName,jdbcType=VARCHAR}
      </if>
      <if test="code != null">
        and  CODE = #{code,jdbcType=VARCHAR}
      </if>
      <if test="province != null">
        and PROVINCE = #{province,jdbcType=VARCHAR}
      </if>
      <if test="city != null">
        and  CITY = #{city,jdbcType=VARCHAR}
      </if>
      <if test="district != null">
        and DISTRICT = #{district,jdbcType=VARCHAR}
      </if>
      <if test="address != null">
        and ADDRESS = #{address,jdbcType=VARCHAR}
      </if>
      <if test="contact != null">
        and CONTACT = #{contact,jdbcType=VARCHAR}
      </if>
      <if test="phone != null">
        and PHONE = #{phone,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        and  UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="brand != null">
        and  BRAND = #{brand,jdbcType=VARCHAR}
      </if>
      <if test="eb != null">
        and EB = #{eb,jdbcType=DECIMAL}
      </if>
      <if test="cUnionstoreId != null">
        and C_UNIONSTORE_ID = #{cUnionstoreId,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <select id="findSysStoreByCStoreId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sys_store
    where c_store_id in
    <foreach collection="storeIds" item="storeId" open="(" close=")" separator=",">
      #{storeId}
    </foreach>
  </select>
  <select id="selectByCodes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sys_store
    where C_STORE_CODE in
    <foreach collection="codes" item="code" open="(" close=")" separator=",">
      #{code}
    </foreach>
  </select>

  <select id="findStoreIdByBoxSn" resultType="java.lang.Integer">
      select max(a.c_store_id)
            from sys_store a,
            sys_user b,fashioner c,box d
            where a.id = b.store_id
            and b.id = c.user_id
            and c.id = d.create_fas_id
            and d.box_sn =#{boxSn}
  </select>
    <select id="selectByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from sys_store
          where id in
          <foreach collection="ids" item="item" separator="," close=")" open="(">
            #{item}
          </foreach>
    </select>

  <select id="selectDistributeInfo" resultMap="BaseResultMap" parameterType="String">
    select
    ss.C_STORE_ID,ss.C_STORE_NAME,ss.C_CUSTOMER_ID
    FROM BOX b, FASHIONER f , SYS_USER su, SYS_STORE ss
    WHERE trim(f.ID) = trim(b.CREATE_FAS_ID) and trim(su.ID) = trim(f.USER_ID) and trim(ss.id) = trim(su.STORE_ID) and trim(b.ID) = #{boxId}
  </select>

  <select id="selectAll" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from sys_store
  </select>

  <select id="selectByName" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from sys_store
    WHERE
    C_STORE_NAME like concat('%', #{cStoreName}, '%')
  </select>


  <select id="selectAllSysStore" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from SYS_STORE
  </select>


</mapper>
