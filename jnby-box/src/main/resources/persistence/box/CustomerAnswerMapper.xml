<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CustomerAnswerMapper">
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.CustomerAnswer">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="UNIONID" jdbcType="VARCHAR" property="unionid"/>
        <result column="SURVEY_ID" jdbcType="VARCHAR" property="surveyId"/>
        <result column="PAGE_ID" jdbcType="VARCHAR" property="pageId"/>
        <result column="QUESTION_ID" jdbcType="VARCHAR" property="questionId"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="ANSWER_FLAG" jdbcType="DECIMAL" property="answerFlag"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.jnby.infrastructure.box.model.CustomerAnswer">
        <result column="DATA"  property="data"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, UNIONID, SURVEY_ID, PAGE_ID, QUESTION_ID, CREATE_TIME, UPDATE_TIME,ANSWER_FLAG
  </sql>
    <sql id="Blob_Column_List">
    DATA
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from CUSTOMER_ANSWER
        where ID = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectListBySelective" parameterType="com.jnby.infrastructure.box.model.CustomerAnswer"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CUSTOMER_ANSWER
        <where>
        <if test="unionid != null">
            and UNIONID = #{unionid,jdbcType=VARCHAR}
        </if>
        <if test="surveyId != null">
            and SURVEY_ID = #{surveyId,jdbcType=VARCHAR}
        </if>
        <if test="pageId != null">
            and PAGE_ID = #{pageId,jdbcType=VARCHAR}
        </if>
        <if test="questionId != null">
            and QUESTION_ID = #{questionId,jdbcType=VARCHAR}
        </if>
        </where>
        and ANSWER_FLAG=1
    </select>
    <select id="selectCustomerAttr" resultType="com.jnby.base.entity.CustomerAttrEntity">
    select cq.relation,ca.data,cq.title
    from customer_answer ca
    left join customer_question  cq
    ON cq.id=ca.question_id
    where
     ca.unionid= #{unionId}
  </select>


    <select id="getAnswer" resultMap="ResultMapWithBLOBs">

        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from CUSTOMER_ANSWER
        where UNIONID = #{unionid,jdbcType=VARCHAR}

        and QUESTION_ID = #{questionId,jdbcType=VARCHAR}


    </select>

    <select id="getAnswers" resultMap="ResultMapWithBLOBs">

        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from CUSTOMER_ANSWER
        where UNIONID = #{unionid,jdbcType=VARCHAR}

        and QUESTION_ID in
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <select id="getAnswerForUpdate" resultMap="ResultMapWithBLOBs">

        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from CUSTOMER_ANSWER
        <where>
        <if test="unionid != null">
            UNIONID = #{unionid,jdbcType=VARCHAR}
        </if>
        <if test="questionId != null">
            AND QUESTION_ID = #{questionId,jdbcType=VARCHAR}
        </if>
        </where>
    </select>


    <select id="getAnswerForCount" resultType="java.lang.Integer">

        select
        count(1)
        from CUSTOMER_ANSWER
        where  UNIONID = #{unionid,jdbcType=VARCHAR}
        and  QUESTION_ID = #{questionId,jdbcType=VARCHAR}
        and  ANSWER_FLAG=1

    </select>


    <select id="getAnswerForAward" resultMap="ResultMapWithBLOBs">

        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from CUSTOMER_ANSWER
        where UNIONID = #{unionid,jdbcType=VARCHAR}
        and QUESTION_ID = #{questionId,jdbcType=VARCHAR}
        and ANSWER_FLAG=1
        and DATA is not null

    </select>


    <select id="getAnswerListForCount" resultType="java.lang.Integer">

        select
        count(1)
        from CUSTOMER_ANSWER
        where UNIONID = #{unionid,jdbcType=VARCHAR}
        and QUESTION_ID in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>

        and ANSWER_FLAG=1


    </select>


    <select id="getAnswerListForAward" resultMap="ResultMapWithBLOBs">


        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from CUSTOMER_ANSWER
        where UNIONID = #{unionid,jdbcType=VARCHAR}
        and QUESTION_ID in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and ANSWER_FLAG=1
        and DATA is not null


    </select>


    <select id="getAnswerForAwardByQuestionIds" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from CUSTOMER_ANSWER
        where UNIONID = #{unionid,jdbcType=VARCHAR}
        and ANSWER_FLAG=1
        and DATA is not null
        and QUESTION_ID in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>

    </select>


    <select id="getAnswerGoodCount" resultType="java.lang.Integer">


        select
        COUNT(1)
        from CUSTOMER_ANSWER
        where UNIONID = #{unionid,jdbcType=VARCHAR}
        and ANSWER_FLAG=1
        and `DATA` is not null
        and QUESTION_ID in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>


    </select>


    <select id="getAwardGoodCount" resultMap="ResultMapWithBLOBs">

       select
       <include refid="Base_Column_List"/>
       ,
       <include refid="Blob_Column_List"/>
       from CUSTOMER_ANSWER
       where SURVEY_ID = #{surveyId,jdbcType=VARCHAR}
       and PAGE_ID = #{pageId,jdbcType=VARCHAR}
       and UNIONID = #{unionid,jdbcType=VARCHAR}
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CUSTOMER_ANSWER
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.jnby.infrastructure.box.model.CustomerAnswer">
    insert INTO CUSTOMER_ANSWER (ID, UNIONID, SURVEY_ID,
      PAGE_ID, QUESTION_ID, CREATE_TIME,
      UPDATE_TIME, DATA)
    values (#{id,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, #{surveyId,jdbcType=VARCHAR},
      #{pageId,jdbcType=VARCHAR}, #{questionId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{data})
  </insert>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="false">
        INSERT INTO CUSTOMER_ANSWER
        (ID, UNIONID, SURVEY_ID, PAGE_ID, QUESTION_ID, CREATE_TIME, UPDATE_TIME, ANSWER_FLAG, DATA) VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.unionid,jdbcType=VARCHAR}, #{item.surveyId,jdbcType=VARCHAR},
            #{item.pageId,jdbcType=VARCHAR}, #{item.questionId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.answerFlag,jdbcType=DECIMAL}, #{item.data})
        </foreach>
    </insert>

    <insert id="insertSelective" parameterType="com.jnby.infrastructure.box.model.CustomerAnswer">
        insert INTO CUSTOMER_ANSWER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="unionid != null">
                UNIONID,
            </if>
            <if test="surveyId != null">
                SURVEY_ID,
            </if>
            <if test="pageId != null">
                PAGE_ID,
            </if>
            <if test="questionId != null">
                QUESTION_ID,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="data != null">
                DATA,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="unionid != null">
                #{unionid,jdbcType=VARCHAR},
            </if>
            <if test="surveyId != null">
                #{surveyId,jdbcType=VARCHAR},
            </if>
            <if test="pageId != null">
                #{pageId,jdbcType=VARCHAR},
            </if>
            <if test="questionId != null">
                #{questionId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="data != null">
                #{data},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.box.model.CustomerAnswer">
        update CUSTOMER_ANSWER
        <set>
            <if test="unionid != null">
                UNIONID = #{unionid,jdbcType=VARCHAR},
            </if>
            <if test="surveyId != null">
                SURVEY_ID = #{surveyId,jdbcType=VARCHAR},
            </if>
            <if test="pageId != null">
                PAGE_ID = #{pageId,jdbcType=VARCHAR},
            </if>
            <if test="questionId != null">
                QUESTION_ID = #{questionId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>

            <if test="answerFlag != null">
                ANSWER_FLAG = #{answerFlag,jdbcType=DECIMAL},
            </if>

            DATA = #{data},
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.jnby.infrastructure.box.model.CustomerAnswer">
    update CUSTOMER_ANSWER
    set UNIONID = #{unionid,jdbcType=VARCHAR},
      SURVEY_ID = #{surveyId,jdbcType=VARCHAR},
      PAGE_ID = #{pageId,jdbcType=VARCHAR},
      QUESTION_ID = #{questionId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DATA = #{data}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
    <update id="updateByPrimaryKey" parameterType="com.jnby.infrastructure.box.model.CustomerAnswer">
    update CUSTOMER_ANSWER
    set UNIONID = #{unionid,jdbcType=VARCHAR},
      SURVEY_ID = #{surveyId,jdbcType=VARCHAR},
      PAGE_ID = #{pageId,jdbcType=VARCHAR},
      QUESTION_ID = #{questionId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>


    <update id="updateAnswerFlagForWrite" parameterType="java.lang.String">

     update CUSTOMER_ANSWER
     set ANSWER_FLAG = 1
     where ID = #{id,jdbcType=VARCHAR}

    </update>


</mapper>
