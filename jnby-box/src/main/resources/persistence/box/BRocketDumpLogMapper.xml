<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BRocketDumpLogMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BRocketDumpLog">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <id property="buinessId" column="BUINESS_ID" jdbcType="VARCHAR"/>
            <result property="tags" column="TAGS" jdbcType="VARCHAR"/>
            <result property="topic" column="TOPIC" jdbcType="VARCHAR"/>
            <result property="keys" column="KEYS" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDeal" column="IS_DEAL" jdbcType="DECIMAL"/>

            <result property="encryptionBody" column="ENCRYPTION_BODY" jdbcType="VARCHAR"/>
            <result property="notEncryptionBody" column="NOT_ENCRYPTION_BODY" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,BUINESS_ID,TAGS,
        TOPIC,KEYS,CREATE_TIME,
        UPDATE_TIME,IS_DEAL,ENCRYPTION_BODY,
        NOT_ENCRYPTION_BODY
    </sql>
    <select id="selectByBuinessId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from B_ROCKET_DUMP_LOG
        where BUINESS_ID = #{buinessId}
    </select>
    <select id="selectByIsDeal" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from B_ROCKET_DUMP_LOG
        where IS_DEAL = #{isDeal}
    </select>

    <select id="updateById" >
        update B_ROCKET_DUMP_LOG set IS_DEAL = #{isDeal} where ID = #{id}
    </select>

    <insert id="insertSelective" parameterType="com.jnby.infrastructure.box.model.BRocketDumpLog">
        insert INTO B_ROCKET_DUMP_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="buinessId != null">
                BUINESS_ID,
            </if>
            <if test="notEncryptionBody != null">
                NOT_ENCRYPTION_BODY,
            </if>
            <if test="encryptionBody != null">
                ENCRYPTION_BODY,
            </if>
            <if test="tags != null">
                TAGS,
            </if>
            <if test="topic != null">
                TOPIC,
            </if>
            <if test="keys != null">
                KEYS,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="isDeal != null">
                IS_DEAL,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="buinessId != null">
                #{buinessId},
            </if>
            <if test="notEncryptionBody != null">
                #{notEncryptionBody},
            </if>
            <if test="encryptionBody != null">
                #{encryptionBody},
            </if>
            <if test="tags != null">
                #{tags},
            </if>
            <if test="topic != null">
                #{topic},
            </if>
            <if test="keys != null">
                #{keys},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isDeal != null">
                #{isDeal},
            </if>
        </trim>
    </insert>
</mapper>
