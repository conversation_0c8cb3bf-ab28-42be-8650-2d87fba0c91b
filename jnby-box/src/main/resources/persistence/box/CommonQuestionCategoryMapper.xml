<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CommonQuestionCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.CommonQuestionCategory">
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="SORT" property="sort" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IMG" property="img" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, SORT, CREATE_TIME, UPDATE_TIME, IMG
    </sql>


    <!-- 通用查询映射结果 -->
    <resultMap id="QuestioBaseResultMap" type="com.jnby.infrastructure.box.model.Question">
        <id column="ID" property="id" />
        <result column="category_name" property="categoryName" />
        <result column="IMG" property="img" />
        <result column="sort" property="sort" />
        <result column="question" property="question" />
        <result column="answer" property="answer" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="no" property="no" />
    </resultMap>


    <select id="getQuestionList" resultMap="QuestioBaseResultMap">
        SELECT
        cq.id,
        cqc.name category_name,
        cqc.img img,
        cqc.sort,
        cq.question,
        cq.answer,
        cq.create_time,
        cq.no
        FROM
        common_question_category cqc
        LEFT JOIN common_question cq ON cqc.id = cq.category_id
        WHERE
        cq.id IS NOT NULL
        AND cq.show = 1
        <if test="search != null">
            AND regexp_like ( cq.question, #{search} )
        </if>
        ORDER BY
        cqc.sort ASC,
        cq.NO ASC

    </select>

</mapper>