<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BSubLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BSubLog">
        <id column="ID" property="id" />
        <result column="SUB_ID" property="subId" />
        <result column="OPERATE_TYPE" property="operateType" />
        <result column="OPERATE_PEOPLE" property="operatePeople" />

        <result column="DETAIL_TYPE" property="detailType" />

        <result column="SOURCE" property="source" />
        <result column="TARGET" property="target" />
        <result column="CREATE_BY" property="createBy" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SUB_ID, OPERATE_TYPE, OPERATE_PEOPLE, DETAIL_TYPE,SOURCE, TARGET, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>


    <select id="subLogListBySubId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM
        B_SUB_LOG
        where
        DEL_FLAG=0 and
        SUB_ID = #{subId}
        order by CREATE_TIME desc
    </select>
</mapper>