<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.TryAfterBuySettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.TryAfterBuySetting">
        <id column="ID" property="id" />
        <result column="APP_NAME" property="appName" />
        <result column="MASTER_SWITCH" property="masterSwitch" />
        <result column="AWARD_SWITCH" property="awardSwitch" />
        <result column="AWARD_ID" property="awardId" />
        <result column="RECEIVER" property="receiver" />
        <result column="PHONE" property="phone" />
        <result column="PROVINCE" property="province" />
        <result column="CITY" property="city" />
        <result column="DISTRICT" property="district" />
        <result column="ADDRESS" property="address" />
        <result column="RETURN_STATEMENT" property="returnStatement" />
        <result column="PROTOCOL" property="protocol" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, APP_NAME, MASTER_SWITCH, AWARD_SWITCH, AWARD_ID, RECEIVER, PHONE, PROVINCE, CITY, DISTRICT, ADDRESS, RETURN_STATEMENT, PROTOCOL, CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>

</mapper>