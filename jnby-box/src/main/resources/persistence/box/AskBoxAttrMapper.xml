<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.AskBoxAttrMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.AskBoxAttr">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="askBoxId" column="ASK_BOX_ID" jdbcType="VARCHAR"/>
            <result property="attrValueId" column="ATTR_VALUE_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ASK_BOX_ID,ATTR_VALUE_ID,
        CREATE_TIME,UPDATE_TIME
    </sql>
    <insert id="insertSelective">
        insert INTO ASK_BOX_ATTR
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="askBoxId != null">
                ASK_BOX_ID,
            </if>
            <if test="attrValueId != null">
                ATTR_VALUE_ID,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="askBoxId != null">
                #{askBoxId,jdbcType=VARCHAR},
            </if>
            <if test="attrValueId != null">
                #{attrValueId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>

        </trim>
    </insert>
    <delete id="delByCustomerAskId">

        delete from ASK_BOX_ATTR where ask_box_id = #{askBoxId}

    </delete>
    <select id="selectByAskBoxId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from ASK_BOX_ATTR where ASK_BOX_ID = #{askBoxId}

    </select>


</mapper>
