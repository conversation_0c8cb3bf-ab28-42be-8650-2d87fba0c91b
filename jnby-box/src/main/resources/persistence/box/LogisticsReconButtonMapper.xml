<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.LogisticsReconButtonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.LogisticsReconButton">
        <result column="ID" property="id" />
        <result column="IS_OPEN" property="isOpen" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, IS_OPEN, CREATE_TIME, UPDATE_TIME, IS_DELETED
    </sql>
    <select id="selectButtonById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
            FROM LOGISTICS_RECON_BUTTON where id = #{id}
    </select>


</mapper>