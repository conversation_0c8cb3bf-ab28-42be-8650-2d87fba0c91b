<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BoxRefundDiffLogMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BoxRefundDiffLog">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="boxRefundDetailId" column="BOX_REFUND_DETAIL_ID" jdbcType="VARCHAR"/>
            <result property="ret" column="RET" jdbcType="VARCHAR"/>
            <result property="ebNo" column="EB_NO" jdbcType="VARCHAR"/>
            <result property="errMsg" column="ERR_MSG" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,BOX_REFUND_DETAIL_ID,RET,
        EB_NO,ERR_MSG,CREATE_TIME,
        UPDATE_TIME
    </sql>
    <insert id="insertSelective">
        insert INTO BOX_REFUND_DIFF_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="boxRefundDetailId != null">
                BOX_REFUND_DETAIL_ID,
            </if>
            <if test="ret != null">
                RET,
            </if>
            <if test="ebNo != null">
                EB_NO,
            </if>
            <if test="errMsg != null">
                ERR_MSG,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="boxRefundDetailId != null">
                #{boxRefundDetailId},
            </if>
            <if test="ret != null">
                #{ret},
            </if>
            <if test="ebNo != null">
                #{ebNo},
            </if>
            <if test="errMsg != null">
                #{errMsg},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>

    </insert>
    <select id="selectByBoxRefundDetailIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from BOX_REFUND_DIFF_LOG where BOX_REFUND_DETAIL_ID in
        <foreach collection="boxRefundDetailIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
