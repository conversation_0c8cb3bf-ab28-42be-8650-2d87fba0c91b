<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BMiniNewsAccessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BMiniNewsAccess">
        <id column="ID" property="id" />
        <result column="TEMPLATE_ID" property="templateId" />
        <result column="UNIONID" property="unionid" />
        <result column="OPENID" property="openid" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, TEMPLATE_ID, UNIONID, OPENID, CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>
    <select id="selectByUnionIdAndTemplateId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        FROM B_MINI_NEWS_ACCESS
        where DEL_FLAG=0 and UNIONID = #{unionId} and TEMPLATE_ID = #{templateId}

    </select>

</mapper>