<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BCustomerZhimaCreditMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BCustomerZhimaCredit">
        <id column="ID" property="id" />
        <result column="USER_ID" property="userId" />
        <result column="ALI_OPEN_ID" property="aliOpenId" />
        <result column="AGREEMENT_STATUS" property="agreementStatus" />
        <result column="CREDIT_AGREEMENT_ID" property="creditAgreementId" />
        <result column="OUT_AGREEMENT_NO" property="outAgreementNo" />
        <result column="STATUS_CHANGE_TIME" property="statusChangeTime" />
        <result column="OPEN_CHANNEL" property="openChannel" />
        <result column="ALIPAY_USER_ID" property="alipayUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DAYSTR" property="daystr" />
        <result column="SERIAL_NUMBER" property="serialNumber" />
        <result column="IS_DEL" property="isDel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, USER_ID, ALI_OPEN_ID, AGREEMENT_STATUS, CREDIT_AGREEMENT_ID, OUT_AGREEMENT_NO, STATUS_CHANGE_TIME, OPEN_CHANNEL, ALIPAY_USER_ID, CREATE_TIME, UPDATE_TIME, DAYSTR, SERIAL_NUMBER,IS_DEL
    </sql>

</mapper>