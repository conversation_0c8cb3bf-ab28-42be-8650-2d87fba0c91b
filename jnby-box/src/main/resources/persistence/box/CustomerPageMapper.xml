<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CustomerPageMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.CustomerPage">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SURVEY_ID" jdbcType="VARCHAR" property="surveyId" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="PAGE_NO" jdbcType="DECIMAL" property="pageNo" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="STYLE_FLAG" jdbcType="DECIMAL" property="styleFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, SURVEY_ID, NAME, PAGE_NO, CREATE_TIME, UPDATE_TIME,STYLE_FLAG
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CUSTOMER_PAGE
    where ID = #{id,jdbcType=VARCHAR}
  </select>

  <select id="selectListBySelective" parameterType="com.jnby.infrastructure.box.model.CustomerPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CUSTOMER_PAGE
    <where>
    <if test="surveyId != null">
      and SURVEY_ID = #{surveyId,jdbcType=VARCHAR}
    </if>
    <if test="name != null">
      and NAME = #{name,jdbcType=VARCHAR}
    </if>
    <if test="pageNo != null">
      and PAGE_NO = #{pageNo,jdbcType=DECIMAL}
    </if>
    </where>
  </select>

  <select id="selectListBySelectiveForSecond" parameterType="com.jnby.infrastructure.box.model.CustomerPage" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from CUSTOMER_PAGE
    <where>
    <if test="surveyId != null">
      and SURVEY_ID = #{surveyId,jdbcType=VARCHAR}
    </if>
    <if test="name != null">
      and NAME = #{name,jdbcType=VARCHAR}
    </if>
    <if test="pageNo != null">
      and PAGE_NO = #{pageNo,jdbcType=DECIMAL}
    </if>
    <if test="styleFlag != null">
      and STYLE_FLAG = #{styleFlag,jdbcType=DECIMAL}
    </if>
    </where>

  </select>


    <select id="selectCountForQuestion" parameterType="java.util.List" resultMap="BaseResultMap">

      select
      <include refid="Base_Column_List" />
      from CUSTOMER_PAGE
      where STYLE_FLAG in
      <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>

      <if test="surveyId != null">
        and SURVEY_ID = #{surveyId,jdbcType=VARCHAR}
      </if>


    </select>


  <select id="queryMyLoveForMyDream" resultType="java.lang.String">

     select b.PAGE_ID from CUSTOMER_PAGE a
     left join CUSTOMER_QUESTION b on a.ID=b.PAGE_ID
     where a.SURVEY_ID= #{surveyId,jdbcType=VARCHAR}
     and a.STYLE_FLAG=2 and b.TITLE like '%喜爱的搭配风格有 (多选)%'

    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CUSTOMER_PAGE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.jnby.infrastructure.box.model.CustomerPage">
    insert INTO CUSTOMER_PAGE (ID, SURVEY_ID, NAME,
      PAGE_NO, CREATE_TIME, UPDATE_TIME
      )
    values (#{id,jdbcType=VARCHAR}, #{surveyId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
      #{pageNo,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jnby.infrastructure.box.model.CustomerPage">
    insert INTO CUSTOMER_PAGE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="surveyId != null">
        SURVEY_ID,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="pageNo != null">
        PAGE_NO,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="surveyId != null">
        #{surveyId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="pageNo != null">
        #{pageNo,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.box.model.CustomerPage">
    update CUSTOMER_PAGE
    <set>
      <if test="surveyId != null">
        SURVEY_ID = #{surveyId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="pageNo != null">
        PAGE_NO = #{pageNo,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jnby.infrastructure.box.model.CustomerPage">
    update CUSTOMER_PAGE
    set SURVEY_ID = #{surveyId,jdbcType=VARCHAR},
      NAME = #{name,jdbcType=VARCHAR},
      PAGE_NO = #{pageNo,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
