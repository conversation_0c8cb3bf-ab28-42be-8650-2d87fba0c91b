<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BCustomerExpandMapper">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.module.customer.expand.entity.BCustomerExpand">
                <id column="USER_ID" property="userId" />
                <result column="TOT_ASK_BOX" property="totAskBox" />
                <result column="LATE_RIGHTS_ID" property="lateRightsId" />
                <result column="CREATE_TIME" property="createTime" />
                <result column="UPDATE_TIME" property="updateTime" />
                <result column="SUB_STATUS" property="subStatus" />
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        USER_ID, TOT_ASK_BOX, LATE_RIGHTS_ID, CREATE_TIME, UPDATE_TIME, SUB_STATUS
    </sql>

    <!-- 获取订阅过期且非大客户用户列表 -->
    <select id="getExpireSubUserList" resultMap="BaseResultMap">
        SELECT
            USER_ID,
            TOT_ASK_BOX,
            LATE_RIGHTS_ID,
            CREATE_TIME,
            UPDATE_TIME,
            SUB_STATUS
        FROM
            B_CUSTOMER_EXPAND bce
        WHERE
            bce.MEMBER_POINTS &gt;= 3888 AND bce.TOT_180DAYCONSUME_CNT &lt; 3  AND bce.SUB_STATUS != 1
    </select>
</mapper>