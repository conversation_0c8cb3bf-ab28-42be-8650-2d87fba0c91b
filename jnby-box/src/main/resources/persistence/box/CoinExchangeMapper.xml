<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CoinExchangeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.CoinExchange">
        <id column="ID" property="id" />
        <result column="TYPE" property="type" />
        <result column="EFFECTIVE_DATE" property="effectiveDate" />
        <result column="INVALID_DATE" property="invalidDate" />
        <result column="SOURCE_ID" property="sourceId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, TYPE, EFFECTIVE_DATE, INVALID_DATE, SOURCE_ID, CREATE_TIME, UPDATE_TIME
    </sql>

</mapper>