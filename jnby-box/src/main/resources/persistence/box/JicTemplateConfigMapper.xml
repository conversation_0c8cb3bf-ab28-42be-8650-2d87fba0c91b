<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.JicTemplateConfigMapper">

    <resultMap id="BaseResuleMap" type="com.jnby.infrastructure.box.model.JicTemplateConfig">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="MINI_APP_ID" jdbcType="VARCHAR" property="miniAppId" />
        <result column="OFF_APP_ID" jdbcType="VARCHAR" property="offAppId" />
        <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
        <result column="WEID" jdbcType="VARCHAR" property="weid" />
        <result column="OFF_TEMPLATE_CODE" jdbcType="VARCHAR" property="offTemplateCode" />
        <result column="SMS_TEMPLATE_CODE" jdbcType="VARCHAR" property="smsTemplateCode" />
        <result column="OFF_PATH" jdbcType="VARCHAR" property="offPath" />
        <result column="SMS_PATH" jdbcType="VARCHAR" property="smsPath" />
        <result column="TYPE" jdbcType="NUMERIC" property="type" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="IS_DELETED" jdbcType="NUMERIC" property="isDeleted" />
    </resultMap>

    <sql id="Base_Column">
        ID,MINI_APP_ID,OFF_APP_ID,BRAND_NAME,CREATE_TIME,UPDATE_TIME,IS_DELETED,OFF_TEMPLATE_CODE,SMS_TEMPLATE_CODE,
        OFF_PATH,TYPE,WEID,SMS_PATH
    </sql>

    <select id="selectByAppIdAndType" resultMap="BaseResuleMap">
        SELECT
        <include refid="Base_Column"/>
        FROM JIC_TEMPLATE_CONFIG WHERE MINI_APP_ID = #{appId}
                                   and TYPE = #{type}
                                   and IS_DELETED = 0
    </select>
</mapper>
