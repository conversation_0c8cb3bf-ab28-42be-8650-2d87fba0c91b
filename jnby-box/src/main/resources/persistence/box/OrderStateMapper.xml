<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.OrderStateMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.OrderState">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId" />
    <result column="STATE" jdbcType="DECIMAL" property="state" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UDPATE_TIME" jdbcType="TIMESTAMP" property="udpateTime" />
    <result column="ORDER_SN" jdbcType="VARCHAR" property="orderSn" />
    <result column="ERR_MSG" jdbcType="VARCHAR" property="errMsg" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, ORDER_ID, STATE, CREATE_TIME, UDPATE_TIME, ORDER_SN,ERR_MSG
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ORDER_STATE
    where ID = #{id,jdbcType=VARCHAR}
  </select>

  <select id="selectByOrderSn" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ORDER_STATE
    where ORDER_SN = #{orderSn,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from ORDER_STATE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.jnby.infrastructure.box.model.OrderState">
    insert INTO ORDER_STATE (ID, ORDER_ID, STATE, 
      CREATE_TIME, UDPATE_TIME, ORDER_SN
      )
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{state,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{udpateTime,jdbcType=TIMESTAMP}, #{orderSn,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jnby.infrastructure.box.model.OrderState">
    insert INTO ORDER_STATE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="udpateTime != null">
        UDPATE_TIME,
      </if>
      <if test="orderSn != null">
        ORDER_SN,
      </if>
      <if test="errMsg != null">
        ERR_MSG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="udpateTime != null">
        #{udpateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSn != null">
        #{orderSn,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        #{errMsg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.box.model.OrderState">
    update ORDER_STATE
    <set>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        STATE = #{state,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="udpateTime != null">
        UDPATE_TIME = #{udpateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSn != null">
        ORDER_SN = #{orderSn,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        ERR_MSG = #{errMsg,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jnby.infrastructure.box.model.OrderState">
    update ORDER_STATE
    set ORDER_ID = #{orderId,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UDPATE_TIME = #{udpateTime,jdbcType=TIMESTAMP},
      ORDER_SN = #{orderSn,jdbcType=VARCHAR},
      ERR_MSG = #{errMsg,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

    <resultMap id="OrderStateEntityResultMap" type="com.jnby.module.order.entity.OrderStateEntity">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId" />
        <result column="STATE" jdbcType="DECIMAL" property="state" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UDPATE_TIME" jdbcType="TIMESTAMP" property="udpateTime" />
        <result column="ORDER_SN" jdbcType="VARCHAR" property="orderSn" />
        <result column="CUSTOMER_ID" jdbcType="VARCHAR" property="customerId" />
       <result column="ERR_MSG" jdbcType="VARCHAR" property="errMsg" />
    </resultMap>

    <select id="getUnFinishOrderByType" resultMap="OrderStateEntityResultMap">
        select a.ID, a.ORDER_ID, a.STATE, a.CREATE_TIME, a.UDPATE_TIME, a.ORDER_SN,b.CUSTOMER_ID,a.ERR_MSG
        from ORDER_STATE a,order_ b
        where a.order_id = b.id
        and a.state = 0
        and b.type in
        <foreach collection="typeList" item="type" open="(" close=")" separator=",">
            ${type}
        </foreach>
        and a.create_time &lt; #{outTime}
    </select>

    <select id="selectBySelective" parameterType="com.jnby.infrastructure.box.model.OrderState" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from ORDER_STATE
      where STATE = 0
      <if test="orderSn != null">
        and order_sn = #{orderSn,jdbcType=VARCHAR}
      </if>
    </select>
</mapper>