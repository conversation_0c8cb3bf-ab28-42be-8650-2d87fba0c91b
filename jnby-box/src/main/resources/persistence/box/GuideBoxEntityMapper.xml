<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.GuideBoxEntityMapper">
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.GuideBoxEntity">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="BOX_SN" jdbcType="VARCHAR" property="boxSn"/>
        <result column="SALES" jdbcType="VARCHAR" property="sales"/>
        <result column="SALES_NAME" jdbcType="VARCHAR" property="salesName"/>
        <result column="UNIONID" jdbcType="VARCHAR" property="unionId"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="CUSTOMER_NAME" jdbcType="VARCHAR" property="customerName"/>
        <result column="CUSTOMER_HEAD_URL" jdbcType="VARCHAR" property="customerHeadUrl"/>
        <result column="CUSTOMER_PHONE" jdbcType="VARCHAR" property="customerPhone"/>
        <result column="CUSTOMER_ID" jdbcType="VARCHAR" property="customerId"/>
        <result column="IS_EVAL" jdbcType="VARCHAR" property="isEval"/>
        <result column="CREATE_TIME" jdbcType="VARCHAR" property="createTime"/>
        <result column="CANCEL_REASON" jdbcType="VARCHAR" property="cancelReason"/>
        <result column="FASHIONER_NAME" jdbcType="VARCHAR" property="fashionerName"/>
        <result column="FASHIONER_ID" jdbcType="VARCHAR" property="fashionerId"/>

        <result column="create_fas_id" jdbcType="VARCHAR" property="createFasId"/>

        <result column="SOURCE_CODE" jdbcType="VARCHAR" property="sourceCode"/>
        <result column="EXTEND" jdbcType="VARCHAR" property="extend"/>
        <result column="SOURCE_TYPE" jdbcType="DECIMAL" property="sourceType"/>
        <result column="USE_CSC" jdbcType="DECIMAL" property="useCsc"/>
        <result column="USE_SEND_BOX" jdbcType="DECIMAL" property="useSendBox"/>
        <result column="OPEN_EB_SWITCH" jdbcType="DECIMAL" property="openEbSwitch"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID,BOX_SN,SALES,SALES_NAME,UNIONID,STATUS,CUSTOMER_NAME,CUSTOMER_HEAD_URL,CUSTOMER_PHONE
    </sql>

    <select id="selectBySearch" parameterType="com.jnby.module.order.context.GuideBoxEntityContext"
            resultMap="BaseResultMap">
        SELECT
        t.ID,
        t.BOX_SN,
        t.SALES,
        t.IS_EVAL,
        fa.NAME as SALES_NAME,
        cd.id as CUSTOMER_ID,
        cd.NICK_NAME as CUSTOMER_NAME,
        cd.HEAD_URL as CUSTOMER_HEAD_URL,
        cd.PHONE as CUSTOMER_PHONE,
        t.UNIONID,
        t.STATUS,
        t.CREATE_TIME,
        t.CANCEL_REASON,
        t.SOURCE_CODE,
        t.create_fas_id,
        t.extend,
        t.source_type,
        t.USE_CSC,
        t.USE_SEND_BOX,
        t.OPEN_EB_SWITCH
        FROM
        BOX t
        LEFT JOIN FASHIONER fa ON t.SALES = fa.ID
        LEFT JOIN CUSTOMER_DETAILS cd ON t.UNIONID = cd.UNIONID
        <where>
            <if test="fashionerIds != null and fashionerIds.size() > 0">
               and
                    t.create_fas_id in
                <foreach collection="fashionerIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>

            </if>
            <if test="statusList != null and statusList.size() > 0">
                and t.status in
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test='search != null and search != ""'>
                and
                <choose>
                    <when test="search.startsWith('BX')">
                        t.BOX_SN = #{search}
                    </when>
                    <otherwise>
                        cd.phone = #{search}
                    </otherwise>
                </choose>
            </if>

            order by t.update_time desc
        </where>

    </select>



    <resultMap id="BaseDetailsResultMap" type="com.jnby.infrastructure.box.model.BoxDetailsWithBLOBs">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="BOX_ID" jdbcType="VARCHAR" property="boxId"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="SUPPLY_ID" jdbcType="VARCHAR" property="supplyId"/>
        <result column="REFUND_STATUS" jdbcType="VARCHAR" property="refundStatus"/>
        <result column="ORDER_STATUS" jdbcType="VARCHAR" property="orderStatus"/>
        <result column="EXPRESS_ID" jdbcType="VARCHAR" property="expressId"/>
        <result column="IMG_URL"  property="imgUrl" />
    </resultMap>

    <sql id="Base_BoxDetails_Column_List">
        ID, BOX_ID, STATUS,TYPE,REFUND_STATUS,ORDER_STATUS,EXPRESS_ID,AFTER_SALE_ID,SUPPLY_ID,IMG_URL
    </sql>

    <select id="selectBoxDetailsByBoxId" resultMap="BaseDetailsResultMap">
        select
        <include refid="Base_BoxDetails_Column_List" />
        from BOX_DETAILS
        <where>
            <if test="ids != null and ids.size() > 0">
                and BOX_ID in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>




    <select id="selectByIds"  resultMap="BaseResultMap">
        SELECT
        t.ID,
        t.BOX_SN,
        t.SALES,
        t.IS_EVAL,
        fa.NAME as SALES_NAME,
        cd.id as CUSTOMER_ID,
        cd.NICK_NAME as CUSTOMER_NAME,
        cd.HEAD_URL as CUSTOMER_HEAD_URL,
        cd.PHONE as CUSTOMER_PHONE,
        t.UNIONID,
        t.STATUS,
        t.CANCEL_REASON,
        t.USE_CSC,
        t.USE_SEND_BOX,
        t.OPEN_EB_SWITCH,
        t.extend
        FROM
        BOX t
        LEFT JOIN FASHIONER fa ON t.SALES = fa.ID
        LEFT JOIN CUSTOMER_DETAILS cd ON t.UNIONID = cd.UNIONID
        <where>
            <if test="ids != null and ids.size() > 0">
                and  t.ID in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and t.status in
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            order by t.status,t.update_time desc
        </where>

    </select>
    <select id="fashionerBoxList" parameterType="com.jnby.module.order.context.GuideBoxEntityContext"
            resultMap="BaseResultMap">
            SELECT
            t.ID,
            t.BOX_SN,
        cab.SUBMIT_SALES_FASHIONER_ID as SALES,
            t.IS_EVAL,
            fa.NAME as SALES_NAME,
            cd.id as CUSTOMER_ID,
            cd.NICK_NAME as CUSTOMER_NAME,
            cd.HEAD_URL as CUSTOMER_HEAD_URL,
            cd.PHONE as CUSTOMER_PHONE,
            t.UNIONID,
            t.STATUS,
            t.CREATE_TIME,
            t.CANCEL_REASON,
            t.SOURCE_CODE,
            t.USE_CSC,
            t.USE_SEND_BOX,
            t.OPEN_EB_SWITCH,
            t.FASHIONER as FASHIONER_ID
            FROM
            BOX t
            inner join CUSTOMER_ASK_BOX  cab on t.id =cab.BOX_ID
            LEFT JOIN CUSTOMER_DETAILS cd ON t.UNIONID = cd.UNIONID
            LEFT JOIN FASHIONER fa ON cab.SUBMIT_SALES_FASHIONER_ID = fa.ID
            <where>
                <if test="fashionerIds != null and fashionerIds.size() > 0">
                    and (
                    cab.SUBMIT_SALES_FASHIONER_ID in
                    <foreach collection="fashionerIds" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                    )
                </if>
                <if test="statusList != null and statusList.size() > 0">
                    and t.status in
                    <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                        #{status}
                    </foreach>
                </if>
                <if test='search != null and search != ""'>
                    and (
                    t.BOX_SN like concat(#{search},'%')
                    or cd.phone like concat(#{search},'%')
                    or cd.NICK_NAME like concat(#{search},'%')
                    )
                </if>
                order by t.update_time desc
            </where>



    </select>

</mapper>
