<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BCustomerPreferenceInfoV2Mapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BCustomerPreferenceInfoV2">
    <id column="WXOPENID" jdbcType="VARCHAR" property="wxopenid" />
    <result column="PRO_SMALL_TYPE_QTY" jdbcType="VARCHAR" property="proSmallTypeQty" />
    <result column="CMATERTYPE_QTY" jdbcType="VARCHAR" property="cmatertypeQty" />
    <result column="MARK_STYLE_QTY" jdbcType="VARCHAR" property="markStyleQty" />
    <result column="COLORS_QTY" jdbcType="VARCHAR" property="colorsQty" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="WEID" jdbcType="VARCHAR" property="weid" />
  </resultMap>
  <sql id="Base_Column_List">
    WXOPENID, PRO_SMALL_TYPE_QTY, CMATERTYPE_QTY, MARK_STYLE_QTY, COLORS_QTY, CREATE_TIME, 
    UPDATE_TIME, WEID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from B_CUSTOMER_PREFERENCE_INFO_V2
    where WXOPENID = #{wxopenid,jdbcType=VARCHAR}
  </select>
  <select id="selectByOpenIdsAndWeid" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from B_CUSTOMER_PREFERENCE_INFO_V2
    where WXOPENID  in
    <foreach collection="openIds" open="(" close=")" item="item" separator=",">
      #{item}
    </foreach>
    <if test="weid != null and weid != '' ">
      and weid = #{weid}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from B_CUSTOMER_PREFERENCE_INFO_V2
    where WXOPENID = #{wxopenid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.jnby.infrastructure.box.model.BCustomerPreferenceInfoV2">
    insert INTO B_CUSTOMER_PREFERENCE_INFO_V2 (WXOPENID, PRO_SMALL_TYPE_QTY, CMATERTYPE_QTY, 
      MARK_STYLE_QTY, COLORS_QTY, CREATE_TIME, 
      UPDATE_TIME, WEID)
    values (#{wxopenid,jdbcType=VARCHAR}, #{proSmallTypeQty,jdbcType=VARCHAR}, #{cmatertypeQty,jdbcType=VARCHAR}, 
      #{markStyleQty,jdbcType=VARCHAR}, #{colorsQty,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{weid,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jnby.infrastructure.box.model.BCustomerPreferenceInfoV2">
    insert INTO B_CUSTOMER_PREFERENCE_INFO_V2
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wxopenid != null">
        WXOPENID,
      </if>
      <if test="proSmallTypeQty != null">
        PRO_SMALL_TYPE_QTY,
      </if>
      <if test="cmatertypeQty != null">
        CMATERTYPE_QTY,
      </if>
      <if test="markStyleQty != null">
        MARK_STYLE_QTY,
      </if>
      <if test="colorsQty != null">
        COLORS_QTY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="weid != null">
        WEID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wxopenid != null">
        #{wxopenid,jdbcType=VARCHAR},
      </if>
      <if test="proSmallTypeQty != null">
        #{proSmallTypeQty,jdbcType=VARCHAR},
      </if>
      <if test="cmatertypeQty != null">
        #{cmatertypeQty,jdbcType=VARCHAR},
      </if>
      <if test="markStyleQty != null">
        #{markStyleQty,jdbcType=VARCHAR},
      </if>
      <if test="colorsQty != null">
        #{colorsQty,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="weid != null">
        #{weid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.box.model.BCustomerPreferenceInfoV2">
    update B_CUSTOMER_PREFERENCE_INFO_V2
    <set>
      <if test="proSmallTypeQty != null">
        PRO_SMALL_TYPE_QTY = #{proSmallTypeQty,jdbcType=VARCHAR},
      </if>
      <if test="cmatertypeQty != null">
        CMATERTYPE_QTY = #{cmatertypeQty,jdbcType=VARCHAR},
      </if>
      <if test="markStyleQty != null">
        MARK_STYLE_QTY = #{markStyleQty,jdbcType=VARCHAR},
      </if>
      <if test="colorsQty != null">
        COLORS_QTY = #{colorsQty,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="weid != null">
        WEID = #{weid,jdbcType=VARCHAR},
      </if>
    </set>
    where WXOPENID = #{wxopenid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jnby.infrastructure.box.model.BCustomerPreferenceInfoV2">
    update B_CUSTOMER_PREFERENCE_INFO_V2
    set PRO_SMALL_TYPE_QTY = #{proSmallTypeQty,jdbcType=VARCHAR},
      CMATERTYPE_QTY = #{cmatertypeQty,jdbcType=VARCHAR},
      MARK_STYLE_QTY = #{markStyleQty,jdbcType=VARCHAR},
      COLORS_QTY = #{colorsQty,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      WEID = #{weid,jdbcType=VARCHAR}
    where WXOPENID = #{wxopenid,jdbcType=VARCHAR}
  </update>
</mapper>