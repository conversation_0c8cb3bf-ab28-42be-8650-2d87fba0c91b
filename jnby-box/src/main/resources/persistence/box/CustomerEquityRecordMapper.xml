<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CustomerEquityRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.CustomerEquityRecord">
        <id column="ID" property="id" />
        <result column="CUSTOMER_ID" property="customerId" />
        <result column="EQUITY_ID" property="equityId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="USE_ID" property="useId" />
        <result column="STATUS" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CUSTOMER_ID, EQUITY_ID, CREATE_TIME, UPDATE_TIME, USE_ID, STATUS
    </sql>

</mapper>