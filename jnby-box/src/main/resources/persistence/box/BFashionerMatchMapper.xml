<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BFashionerMatchMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BFashionerMatch">
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="FASHIONER_ID" property="fashionerId" />
        <result column="YEAR" property="year" />
        <result column="SEASON" property="season" />
        <result column="MONTH" property="month" />
        <result column="MATCH_BAND" property="matchBand" />
        <result column="LOOK_GENDER" property="lookGender" />
        <result column="BRAND_ID" property="brandId" />
        <result column="MATCH_ABOUT" property="matchAbout" />
        <result column="LOOK_ID" property="lookId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DEL" property="isDel" />
        <result column="STATUS" property="status" />
        <result column="THEME_ID" property="themeId" />
        <result column="FAVORITE_NUM" property="favoriteNum" />
        <result column="EXTEND_JSON" property="extendJson" />

        <result column="PUT_IN_END_TIME" property="putInEndTime" />
        <result column="PUT_IN_START_TIME" property="putInStartTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, FASHIONER_ID, YEAR, SEASON, MONTH, MATCH_BAND, LOOK_GENDER, BRAND_ID, MATCH_ABOUT,
        LOOK_ID, CREATE_TIME, UPDATE_TIME, IS_DEL, STATUS,THEME_ID,FAVORITE_NUM,PUT_IN_END_TIME,PUT_IN_START_TIME
    </sql>

    <sql id="Base_Column_List_alien">
        a.ID, a.NAME, a.FASHIONER_ID, a.YEAR, a.SEASON, a.MONTH, a.MATCH_BAND, a.LOOK_GENDER, a.BRAND_ID, a.MATCH_ABOUT,
        a.LOOK_ID, a.CREATE_TIME, a.UPDATE_TIME, a.IS_DEL, a.STATUS,a.THEME_ID,a.FAVORITE_NUM,a.EXTEND_JSON,a.PUT_IN_START_TIME,a.PUT_IN_END_TIME
    </sql>

    <update id="updateCasById">
        update B_FASHIONER_MATCH
        set FAVORITE_NUM =
        <if test="num == -1">
            FAVORITE_NUM - 1
        </if>
        <if test="num == 1">
            FAVORITE_NUM + 1
        </if>
         where id = #{id} and FAVORITE_NUM = #{oldNum}
    </update>

    <select id="selectNoYearMatch" resultType="com.jnby.module.marketing.match.fashionerMatch.entity.NoYearMatchEntity">
        select b.match_id as matchId,SUBSTRING(GROUP_CONCAT(c.name),1,2) year,SUBSTRING(GROUP_CONCAT(c.name),3,2) season
        from b_fashioner_match a,b_fashioner_match_label b,tag c
        where a.id = b.match_id and b.label_id = c.id
        and c.group_id = (select id from tag_group where type = 0 and name = '年份季节')
        and c.is_del = 0
        and a.year is null
        group by match_id
    </select>
    <select id="selectByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from B_FASHIONER_MATCH
        where id  in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <resultMap id="StylingMap" type="com.jnby.module.marketing.styling.entity.StylingEntity">
        <id column="ID" jdbcType="VARCHAR" property="fashionerMatchId" />
        <result column="EXTEND_JSON" jdbcType="VARCHAR" property="backgroundImg" />
        <result column="MATCH_ABOUT" jdbcType="VARCHAR" property="remark" />
        <result column="PHOTO" jdbcType="VARCHAR" property="photo" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="STYLE" jdbcType="VARCHAR" property="style" />
        <result column="THEME_ID" jdbcType="VARCHAR" property="id" />
    </resultMap>

    <select id="selectStylingEntityByIds" resultMap="StylingMap">
        select a.id,a.EXTEND_JSON ,a.MATCH_ABOUT,b.photo,b.name,a.THEME_ID
        from B_FASHIONER_MATCH a,fashioner b
        where a.FASHIONER_ID = b.id
        and a.STATUS = 1
        <if test="ids != null">
            and a.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="sort == 1">
            order by a.create_time desc
        </if>
        <if test="sort == 2">
            order by a.UPDATE_TIME desc
        </if>
    </select>



    <select id="getBfashionerMatchList" resultMap="StylingMap">
        select a.id,a.CREATE_TIME
        from B_FASHIONER_MATCH a
        <if test="isSales == 0">
            left join fashioner b on a.FASHIONER_ID = b.id
        </if>
        left join B_FASHIONER_MATCH_LABEL c on a.ID = c.MATCH_ID
        where
        a.STATUS = 1
        <if test="dpsex != null and dpsex.size() > 0">
            and a.LOOK_GENDER in
            <foreach collection="dpsex" item="sex" open="(" close=")" separator=",">
                #{sex}
            </foreach>
        </if>

        <if test="brands != null  and brands.size() > 0">
            and a.brand_id in
            <foreach collection="brands" item="brand" open="(" close=")" separator=",">
                #{brand}
            </foreach>
        </if>

        <if test="fashioners != null and fashioners.size() > 0">
            and a.FASHIONER_ID in
            <foreach collection="fashioners" item="fashioner" open="(" close=")" separator=",">
                #{fashioner}
            </foreach>
        </if>
        <if test="labelIds != null  and labelIds.size()>0">
            and c.label_id in
            <foreach collection="labelIds" item="labelId" open="(" close=")" separator=",">
                #{labelId}
            </foreach>
        </if>
        <if test="search != null">
            and (a.name like '%${search}%')
        </if>
        group by a.id ,a.create_time
        order by
        a.create_time
        desc
    </select>
    <select id="getBfashionerMatchListForSales"
            resultMap="StylingMap">
        select a.ID,a.UPDATE_TIME
        from B_FASHIONER_MATCH a
        left join B_FASHIONER_MATCH_LABEL c on a.ID = c.MATCH_ID
        WHERE a.STATUS = 1

        <if test="labelIds != null and labelIds.size()>0">
            and c.label_id in
            <foreach collection="labelIds" item="labelId" open="(" close=")" separator=",">
                #{labelId}
            </foreach>
        </if>
        <if test="stylingIds != null and stylingIds.size()>0">
            and a.ID in
            <foreach collection="stylingIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="lookIds != null  and lookIds.size()>0">
            and a.look_id in
            <foreach collection="lookIds" item="lookId" open="(" close=")" separator=",">
                #{lookId}
            </foreach>
        </if>

        <if test="brandIds != null and brandIds.size()>0">
            and a.BRAND_ID in
            <foreach collection="brandIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        group by a.id ,a.UPDATE_TIME
        order by a.UPDATE_TIME desc
    </select>
    <select id="selectFavorRecordList" resultMap="BaseResultMap">
            select distinct(a.id) as id
             from favorite_record fe left join
              B_FASHIONER_MATCH a on fe.B_FASHIONER_MATCH_ID = a.id
              left join b_fashioner_match_label  c on a.ID = c.MATCH_ID
             where
             fe.unionid = #{unionId} and fe.status = 1 and a.id is not null
             <if test="labelIds != null and labelIds.size() > 0">
                 and c.LABEL_ID in
                 <foreach collection="labelIds" open="(" close=")" separator="," item="item">
                     #{item}
                 </foreach>
             </if>
    </select>
    <select id="selectFavorRecordListByIds" resultMap="BaseResultMap">
            select <include refid="Base_Column_List_alien"></include>
            from favorite_record fe left join
            B_FASHIONER_MATCH a on fe.B_FASHIONER_MATCH_ID = a.id
            where fe.unionid = #{unionId}
            <if test="ids != null and ids.size() > 0">
                and a.id in
                <foreach collection="ids" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            order by fe.update_time desc
    </select>
    <select id="selectThemeIdIsNotNullByEndDate"
            resultMap="BaseResultMap">

        select <include refid="Base_Column_List"></include> from B_FASHIONER_MATCH where #{endDate} > create_time and theme_id is not null

    </select>
    <select id="selectByLookId" resultMap="BaseResultMap">

        select <include refid="Base_Column_List"></include> from B_FASHIONER_MATCH where LOOK_ID = #{lookId}

    </select>
    <select id="selectListOrderByCreateTime" resultMap="BaseResultMap">

        select <include refid="Base_Column_List"></include> from B_FASHIONER_MATCH where LOOK_ID is not null order by CREATE_TIME desc


    </select>
    <select id="getFashionerMatchByListIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from B_FASHIONER_MATCH
        where id in
        <foreach collection="ids" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectByShouldUpStatusData" resultMap="BaseResultMap">
        select <include refid="Base_Column_List_alien"></include> from B_FASHIONER_MATCH a
        where a.PUT_IN_END_TIME is not null and a.PUT_IN_START_TIME is not null and #{now} between a.PUT_IN_START_TIME and a.PUT_IN_END_TIME
        and a.status = #{status} and a.is_del = 0
    </select>
    <select id="selectByShouldDownStatusData" resultMap="BaseResultMap">
        select <include refid="Base_Column_List_alien"></include> from B_FASHIONER_MATCH  a
        where a.PUT_IN_END_TIME is not null
          and a.PUT_IN_START_TIME is not null
          and #{now} not between a.PUT_IN_START_TIME and a.PUT_IN_END_TIME
        and a.status = #{status} and a.is_del = 0
    </select>

</mapper>
