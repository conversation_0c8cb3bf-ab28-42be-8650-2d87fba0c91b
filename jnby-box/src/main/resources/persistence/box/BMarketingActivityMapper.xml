<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.MarketingActivityMapper">

    <insert id="insert">
        insert INTO B_ACTIVITY_BASIS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="triggerCondition != null">
                TRIGGER_CONDITION,
            </if>
            <if test="activityName != null">
                ACTIVITY_NAME,
            </if>
            <if test="validityType != null">
                VALIDITY_TYPE,
            </if>
            <if test="startTime != null">
                START_TIME,
            </if>
            <if test="endTime != null">
                END_TIME,
            </if>
            <if test="userType != null">
                USER_TYPE,
            </if>
            <if test="activityType != null">
                ACTIVITY_TYPE,
            </if>
            <if test="isFlag != null">
                IS_FLAG,
            </if>
            <if test="flagName != null">
                FLAG_NAME,
            </if>
            <if test="flagColour != null">
                FLAG_COLOUR,
            </if>
            <if test="status != null">
                STATUS,
            </if>
            <if test="rewardNumber != null">
                REWARD_NUMBER,
            </if>
            <if test="everydayRewardNumber != null">
                EVERYDAY_REWARD_NUMBER,
            </if>
            CREATE_TIME,
            UPDATE_TIME,
            <if test="keyWord != null">
                KEY_WORD,
            </if>
            <if test="matchPerson != null">
                MATCH_PERSON,
            </if>
            <if test="materialId != null">
                MATERIAL_ID,
            </if>
            <if test="transmitFriend != null">
                TRANSMIT_FRIEND,
            </if>
            <if test="shareFriend != null">
                SHARE_FRIEND,
            </if>
            <if test="activityCode != null">
                ACTIVITY_CODE,
            </if>
            IS_DEL,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="triggerCondition != null">
                #{triggerCondition,jdbcType=DECIMAL},
            </if>
            <if test="activityName != null">
                #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="validityType != null">
                #{validityType,jdbcType=DECIMAL},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="userType != null">
                #{userType,jdbcType=DECIMAL},
            </if>
            <if test="activityType != null">
                #{activityType,jdbcType=DECIMAL},
            </if>
            <if test="isFlag != null">
                #{isFlag,jdbcType=DECIMAL},
            </if>
            <if test="flagName != null">
                #{flagName,jdbcType=VARCHAR},
            </if>
            <if test="flagColour != null">
                #{flagColour,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=DECIMAL},
            </if>
            <if test="rewardNumber != null">
                #{rewardNumber,jdbcType=DECIMAL},
            </if>
            <if test="everydayRewardNumber != null">
                #{everydayRewardNumber,jdbcType=DECIMAL},
            </if>
            NOW(),
            NOW(),
            <if test="keyWord != null">
                #{keyWord,jdbcType=VARCHAR},
            </if>
            <if test="matchPerson != null">
                #{matchPerson,jdbcType=VARCHAR},
            </if>
            <if test="materialId != null">
                #{materialId,jdbcType=VARCHAR},
            </if>
            <if test="transmitFriend != null">
                #{transmitFriend,jdbcType=DECIMAL},
            </if>
            <if test="shareFriend != null">
                #{shareFriend,jdbcType=DECIMAL},
            </if>
            <if test="activityCode != null">
                #{activityCode,jdbcType=VARCHAR},
            </if>
            0,
        </trim>
    </insert>
    <update id="updateById">
        update B_ACTIVITY_BASIS
        <set>
            <if test="triggerCondition != null">
                TRIGGER_CONDITION = #{triggerCondition,jdbcType=DECIMAL},
            </if>
            <if test="activityName != null">
                ACTIVITY_NAME = #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="validityType != null">
                VALIDITY_TYPE = #{validityType,jdbcType=DECIMAL},
            </if>
            <if test="startTime != null">
                START_TIME = #{startTime},
            </if>
            <if test="endTime != null">
                END_TIME = #{endTime},
            </if>
            <if test="userType != null">
                USER_TYPE = #{userType,jdbcType=DECIMAL},
            </if>
            <if test="activityType != null">
                ACTIVITY_TYPE = #{activityType,jdbcType=DECIMAL},
            </if>
            <if test="isFlag != null">
                IS_FLAG = #{isFlag,jdbcType=DECIMAL},
            </if>
            <if test="flagName != null">
                FLAG_NAME = #{flagName,jdbcType=VARCHAR},
            </if>
            <if test="flagColour != null">
                FLAG_COLOUR = #{flagColour,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=DECIMAL},
            </if>
            <if test="rewardNumber != null">
                REWARD_NUMBER = #{rewardNumber,jdbcType=DECIMAL},
            </if>
            <if test="everydayRewardNumber != null">
                EVERYDAY_REWARD_NUMBER = #{everydayRewardNumber,jdbcType=DECIMAL},
            </if>
            UPDATE_TIME = NOW(),
            <if test="keyWord != null">
                KEY_WORD = #{keyWord,jdbcType=VARCHAR},
            </if>
            <if test="matchPerson != null">
                MATCH_PERSON = #{matchPerson,jdbcType=VARCHAR},
            </if>
            <if test="materialId != null">
                MATERIAL_ID = #{materialId,jdbcType=VARCHAR},
            </if>
            <if test="transmitFriend != null">
                TRANSMIT_FRIEND = #{transmitFriend,jdbcType=DECIMAL},
            </if>
            <if test="shareFriend != null">
                SHARE_FRIEND = #{shareFriend,jdbcType=DECIMAL},
            </if>
            <if test="scenesId != null">
                SCENES_ID = #{scenesId,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="delete">
        update B_ACTIVITY_BASIS
        set IS_DEL = 1
        where ID = #{id}
    </update>
    <update id="updateBatchActivityStateByTime">
        update B_ACTIVITY_BASIS
        set STATUS = #{status,jdbcType=DECIMAL}
        where IS_DEL = 0
        <if test="status == 2">
            <![CDATA[and START_TIME <= NOW() and NOW() <= END_TIME]]>
        </if>
        <if test="status == 3">
            <![CDATA[and END_TIME <= NOW()]]>
        </if>
    </update>
    <resultMap id="bActivityBasisResp" type="com.jnby.application.admin.dto.response.BActivityBasisResp">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="triggerCondition" jdbcType="DECIMAL" property="triggerCondition"/>
        <result column="activityName" jdbcType="VARCHAR" property="activityName"/>
        <result column="validityType" jdbcType="DECIMAL" property="validityType"/>
        <result column="startTime" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="endTime" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="userType" jdbcType="DECIMAL" property="userType"/>
        <result column="activityType" jdbcType="DECIMAL" property="activityType"/>
        <result column="isFlag" jdbcType="DECIMAL" property="isFlag"/>
        <result column="flagName" jdbcType="VARCHAR" property="flagName"/>
        <result column="flagColour" jdbcType="VARCHAR" property="flagColour"/>
        <result column="status" jdbcType="DECIMAL" property="status"/>
        <result column="rewardNumber" jdbcType="DECIMAL" property="rewardNumber"/>
        <result column="everydayRewardNumber" jdbcType="DECIMAL" property="everydayRewardNumber"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="keyWord" jdbcType="VARCHAR" property="keyWord"/>
        <result column="matchPerson" jdbcType="VARCHAR" property="matchPerson"/>
        <result column="materialId" jdbcType="VARCHAR" property="materialId"/>
        <result column="transmitFriend" jdbcType="DECIMAL" property="transmitFriend"/>
        <result column="shareFriend" jdbcType="DECIMAL" property="shareFriend"/>
        <result column="scenesId" jdbcType="DECIMAL" property="scenesId"/>
        <collection property="bActivityRewardList" javaType="list"
                    ofType="com.jnby.infrastructure.box.model.BActivityReward">
            <id column="rId" jdbcType="VARCHAR" property="id"/>
            <result column="ACTIVITY_ID" jdbcType="VARCHAR" property="activityId"/>
            <result column="REWARD_TYPE" jdbcType="DECIMAL" property="rewardType"/>
            <result column="ENTITY_TYPE" jdbcType="DECIMAL" property="entityType"/>
            <result column="VOUCHER_ID" jdbcType="VARCHAR" property="voucherId"/>
            <result column="VOUCHER_NUMBER" jdbcType="VARCHAR" property="voucherNumber"/>
            <result column="ENTITY_NAME" jdbcType="VARCHAR" property="entityName"/>
            <result column="IMAGE" jdbcType="VARCHAR" property="image"/>
            <result column="SEQ" jdbcType="VARCHAR" property="seq"/>
            <result column="TOTAL_NUMBER" jdbcType="DECIMAL" property="totalNumber"/>
            <result column="OPEN_NUMBER" jdbcType="DECIMAL" property="openNumber"/>
            <result column="REWARD_METHOD" jdbcType="DECIMAL" property="rewardMethod"/>
            <result column="IS_DEL" jdbcType="DECIMAL" property="isDel"/>
            <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        </collection>
    </resultMap>
    <select id="selectById" resultMap="bActivityBasisResp">
        select b.ID as id,  b.TRIGGER_CONDITION as triggerCondition, b.ACTIVITY_NAME as activityName,  b.VALIDITY_TYPE as validityType,
                 b.START_TIME as startTime,  b.END_TIME as endTime, b.USER_TYPE as userType,  b.ACTIVITY_TYPE as activityType,
                 b.IS_FLAG as isFlag,  b.FLAG_NAME as flagName, b.FLAG_COLOUR as flagColour,  b.STATUS as status,b.SCENES_ID as scenesId,
                 b.REWARD_NUMBER as rewardNumber,  b.EVERYDAY_REWARD_NUMBER as everydayRewardNumber, b.CREATE_TIME as createTime,  b.UPDATE_TIME as updateTime,
                 b.KEY_WORD as keyWord,  b.MATCH_PERSON as matchPerson,  b.MATERIAL_ID as materialId,  b.TRANSMIT_FRIEND as transmitFriend, b.SHARE_FRIEND as shareFriend,
                r.ID as rId, r.ACTIVITY_ID, r.REWARD_TYPE, r.ENTITY_TYPE, r.VOUCHER_ID, r.VOUCHER_NUMBER, r.ENTITY_NAME,
                r.IMAGE, r.SEQ, r.TOTAL_NUMBER, r.OPEN_NUMBER, r.REWARD_METHOD, r.IS_DEL, r.CREATE_TIME, r.UPDATE_TIME
        from B_ACTIVITY_BASIS b
        left join B_ACTIVITY_REWARD r on r.ACTIVITY_ID = b.id
        where b.IS_DEL = 0
        and r.IS_DEL = 0
        and b.ID = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectList" resultType="com.jnby.application.admin.dto.response.BActivityBasisResp">
        select ID as id, TRIGGER_CONDITION as triggerCondition, ACTIVITY_NAME as activityName, VALIDITY_TYPE as
        validityType,
        START_TIME as startTime, END_TIME as endTime, USER_TYPE as userType, ACTIVITY_TYPE as activityType,
        IS_FLAG as isFlag, FLAG_NAME as flagName, FLAG_COLOUR as flagColour, STATUS as status,
        REWARD_NUMBER as rewardNumber, EVERYDAY_REWARD_NUMBER as everydayRewardNumber, CREATE_TIME as createTime,
        UPDATE_TIME as updateTime,
        KEY_WORD as keyWord, MATCH_PERSON as matchPerson, MATERIAL_ID as materialId, TRANSMIT_FRIEND as transmitFriend,
        SHARE_FRIEND as shareFriend
        from B_ACTIVITY_BASIS
        where IS_DEL = 0
        <if test="keyWord != null">
            <bind name="pattern" value="'%' + keyWord + '%'"/>
            AND ACTIVITY_NAMECONCAT(FLAG_NAME LIKE #{pattern}
        </if>
        <if test="status != null">
            AND STATUS = #{status}
        </if>
        <if test="startTime != null">
            <![CDATA[and STR_TO_DATE(#{startTime},'%Y-%m-%d %H:%i:%s') >= START_TIME]]>
        </if>
        <if test="endTime != null">
            <![CDATA[and STR_TO_DATE(#{endTime},'%Y-%m-%d %H:%i:%s') <= END_TIME]]>
        </if>
        <if test="activityType != null">
            AND ACTIVITY_TYPE = #{activityType}
        </if>
        <if test="triggerCondition != null">
            AND TRIGGER_CONDITION = #{triggerCondition}
        </if>
        order by CREATE_TIME desc
    </select>
    <select id="selectByCondition" resultType="com.jnby.application.admin.dto.response.BActivityBasisResp">
        select ID as id, TRIGGER_CONDITION as triggerCondition, ACTIVITY_NAME as activityName, VALIDITY_TYPE as
        validityType,
        START_TIME as startTime, END_TIME as endTime, USER_TYPE as userType, ACTIVITY_TYPE as activityType,
        IS_FLAG as isFlag, FLAG_NAME as flagName, FLAG_COLOUR as flagColour, STATUS as status,
        REWARD_NUMBER as rewardNumber, EVERYDAY_REWARD_NUMBER as everydayRewardNumber, CREATE_TIME as createTime,
        UPDATE_TIME as updateTime,
        KEY_WORD as keyWord, MATCH_PERSON as matchPerson, MATERIAL_ID as materialId, TRANSMIT_FRIEND as transmitFriend,
        SHARE_FRIEND as shareFriend
        from B_ACTIVITY_BASIS
        where IS_DEL = 0
        <if test="status != null">
            and STATUS = #{status}
        </if>
        <if test="activityType != null">
            AND ACTIVITY_TYPE = #{activityType}
        </if>
        <if test="triggerCondition != null">
            AND TRIGGER_CONDITION = #{triggerCondition}
        </if>
        <if test="id != null">
            AND ID = #{id}
        </if>
        order by CREATE_TIME desc
    </select>
</mapper>