<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BoxWorkbenchTaskDetailMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BoxWorkbenchTaskDetail">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="WORKBENCH_TASK_TYPE" jdbcType="VARCHAR" property="workbenchTaskType" />
    <result column="OUT_NO" jdbcType="VARCHAR" property="outNo" />
    <result column="FASHIONER_ID" jdbcType="VARCHAR" property="fashionerId" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="USER_PHONE" jdbcType="VARCHAR" property="userPhone" />
    <result column="NICK_NAME" jdbcType="VARCHAR" property="nickName" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="IDX_DATE" jdbcType="TIMESTAMP" property="idxDate" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CITY_NAME" jdbcType="VARCHAR" property="cityName" />
    <result column="STORE_ID" jdbcType="INTEGER" property="storeId" />
    <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName" />
    <result column="C_CUSTOMER_ID" jdbcType="INTEGER" property="cCustomerId" />
    <result column="LINK_ID" jdbcType="INTEGER" property="linkId" />
    <result column="VERSION" jdbcType="INTEGER" property="version" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, WORKBENCH_TASK_TYPE, OUT_NO, FASHIONER_ID, UNIONID, USER_PHONE, NICK_NAME, STATUS,
    IDX_DATE, CREATE_TIME, UPDATE_TIME,CITY_NAME,STORE_ID,STORE_NAME,C_CUSTOMER_ID,
    LINK_ID,VERSION
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from BOX_WORKBENCH_TASK_DETAIL
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectListBySelective" parameterType="com.jnby.infrastructure.box.model.BoxWorkbenchTaskDetail" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from BOX_WORKBENCH_TASK_DETAIL
      <where>
        <if test="workbenchTaskType != null">
          and WORKBENCH_TASK_TYPE = #{workbenchTaskType,jdbcType=VARCHAR}
        </if>
        <if test="outNo != null">
          and OUT_NO = #{outNo,jdbcType=VARCHAR}
        </if>
        <if test="fashionerId != null">
          and FASHIONER_ID = #{fashionerId,jdbcType=VARCHAR}
        </if>
        <if test="unionid != null">
          and UNIONID = #{unionid,jdbcType=VARCHAR}
        </if>
      </where>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from BOX_WORKBENCH_TASK_DETAIL
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.jnby.infrastructure.box.model.BoxWorkbenchTaskDetail">
    insert INTO BOX_WORKBENCH_TASK_DETAIL (ID, WORKBENCH_TASK_TYPE, OUT_NO,
      FASHIONER_ID, UNIONID, USER_PHONE,
      NICK_NAME, STATUS, IDX_DATE,
      CREATE_TIME, UPDATE_TIME,CITY_NAME,STORE_ID,STORE_NAME,C_CUSTOMER_ID,LINK_ID)
    values (#{id,jdbcType=VARCHAR}, #{workbenchTaskType,jdbcType=VARCHAR}, #{outNo,jdbcType=VARCHAR},
      #{fashionerId,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, #{userPhone,jdbcType=VARCHAR},
      #{nickName,jdbcType=VARCHAR}, #{status,jdbcType=DECIMAL}, #{idxDate,jdbcType=TIMESTAMP},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{cityName,jdbcType=VARCHAR},#{storeId,jdbcType=INTEGER},#{storeName,jdbcType=VARCHAR},
      #{cCustomerId,jdbcType=INTEGER},#{linkId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.jnby.infrastructure.box.model.BoxWorkbenchTaskDetail">
    insert INTO BOX_WORKBENCH_TASK_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="workbenchTaskType != null">
        WORKBENCH_TASK_TYPE,
      </if>
      <if test="outNo != null">
        OUT_NO,
      </if>
      <if test="fashionerId != null">
        FASHIONER_ID,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="userPhone != null">
        USER_PHONE,
      </if>
      <if test="nickName != null">
        NICK_NAME,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="idxDate != null">
        IDX_DATE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="cityName != null">
        CITY_NAME,
      </if>
      <if test="storeId != null">
        STORE_ID,
      </if>
      <if test="storeName != null">
        STORE_NAME,
      </if>
      <if test="cCustomerId != null">
        C_CUSTOMER_ID,
      </if>
      <if test="linkId != null">
        LINK_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="workbenchTaskType != null">
        #{workbenchTaskType,jdbcType=VARCHAR},
      </if>
      <if test="outNo != null">
        #{outNo,jdbcType=VARCHAR},
      </if>
      <if test="fashionerId != null">
        #{fashionerId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="idxDate != null">
        #{idxDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=INTEGER},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="cCustomerId != null">
        #{cCustomerId,jdbcType=INTEGER},
      </if>
      <if test="linkId != null">
        #{linkId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.box.model.BoxWorkbenchTaskDetail">
    update BOX_WORKBENCH_TASK_DETAIL
    <set>
      <if test="workbenchTaskType != null">
        WORKBENCH_TASK_TYPE = #{workbenchTaskType,jdbcType=VARCHAR},
      </if>
      <if test="outNo != null">
        OUT_NO = #{outNo,jdbcType=VARCHAR},
      </if>
      <if test="fashionerId != null">
        FASHIONER_ID = #{fashionerId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        USER_PHONE = #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        NICK_NAME = #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="idxDate != null">
        IDX_DATE = #{idxDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cityName != null">
        CITY_NAME = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        STORE_ID = #{storeId,jdbcType=INTEGER},
      </if>
      <if test="storeName != null">
        STORE_NAME = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="cCustomerId != null">
        C_CUSTOMER_ID = #{cCustomerId,jdbcType=INTEGER},
      </if>
      <if test="linkId != null">
        LINK_ID = #{linkId,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jnby.infrastructure.box.model.BoxWorkbenchTaskDetail">
    update BOX_WORKBENCH_TASK_DETAIL
    set WORKBENCH_TASK_TYPE = #{workbenchTaskType,jdbcType=VARCHAR},
      OUT_NO = #{outNo,jdbcType=VARCHAR},
      FASHIONER_ID = #{fashionerId,jdbcType=VARCHAR},
      UNIONID = #{unionid,jdbcType=VARCHAR},
      USER_PHONE = #{userPhone,jdbcType=VARCHAR},
      NICK_NAME = #{nickName,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=DECIMAL},
      IDX_DATE = #{idxDate,jdbcType=TIMESTAMP},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      CITY_NAME = #{cityName,jdbcType=VARCHAR},
      STORE_ID = #{storeId,jdbcType=INTEGER},
      STORE_NAME = #{storeName,jdbcType=VARCHAR},
      C_CUSTOMER_ID = #{cCustomerId,jdbcType=INTEGER},
      LINK_ID = #{linkId,jdbcType=INTEGER}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <update id="updateStatus">
    update BOX_WORKBENCH_TASK_DETAIL
    set STATUS = #{newStatus,jdbcType=DECIMAL},
    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR} and STATUS = #{oldStatus,jdbcType=DECIMAL}
  </update>
  <update id="updateCasById">
    update BOX_WORKBENCH_TASK_DETAIL
    <set>
      <if test="workbenchTaskType != null">
        WORKBENCH_TASK_TYPE = #{workbenchTaskType,jdbcType=VARCHAR},
      </if>
      <if test="outNo != null">
        OUT_NO = #{outNo,jdbcType=VARCHAR},
      </if>
      <if test="fashionerId != null">
        FASHIONER_ID = #{fashionerId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        USER_PHONE = #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        NICK_NAME = #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="idxDate != null">
        IDX_DATE = #{idxDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cityName != null">
        CITY_NAME = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        STORE_ID = #{storeId,jdbcType=INTEGER},
      </if>
      <if test="storeName != null">
        STORE_NAME = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="cCustomerId != null">
        C_CUSTOMER_ID = #{cCustomerId,jdbcType=INTEGER},
      </if>
      <if test="linkId != null">
        LINK_ID = #{linkId,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = version + 1,
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
    and VERSION = #{version,jdbcType=INTEGER}
  </update>
  <update id="updateGrabOrder">
    update BOX_WORKBENCH_TASK_DETAIL set CITY_NAME = null ,STORE_ID = null, STORE_NAME = null, C_CUSTOMER_ID = null, LINK_ID= null     where ID = #{id,jdbcType=VARCHAR} and VERSION = #{version,jdbcType=INTEGER}
  </update>

  <resultMap id="BaseCollectMap" type="com.jnby.base.entity.PushCollectEntiry">
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="collect" jdbcType="INTEGER" property="collect" />
  </resultMap>

  <select id="getCityData" resultMap="BaseCollectMap">
    select
    city_name name,status,count(1) collect
    from box_workbench_task_detail
    where c_customer_id = 176
    <if test="month == null" >
      and status in (1,2)
    </if>
    <if test="month != null and month == 1">
      and  DATE_FORMAT(update_time,'%Y%m') = DATE_FORMAT(NOW(),'%Y%m')
      and status in (3,4)
    </if>
    <if test="month != null and month == 2">
      and  DATE_FORMAT(update_time,'%Y%m%d') = DATE_FORMAT(NOW(),'%Y%m%d')
      and status in (3,4)
    </if>
    <if test="citys != null and citys.size() > 0">
      and city_name in
      <foreach collection="citys" item="city" open="(" close=")" separator=",">
        #{city}
      </foreach>
    </if>
    group by city_name,status
  </select>

  <select id="getStoreData" resultMap="BaseCollectMap">
    select
      store_name name,status,count(1) collect
    from box_workbench_task_detail
    where c_customer_id = 176
    <if test="month == null" >
      and status in (1,2)
    </if>
    <if test="month != null and month == 1">
      and  DATE_FORMAT(update_time,'%Y%m') = DATE_FORMAT(NOW(),'%Y%m')
      and status in (3,4)
    </if>
    <if test="month != null and month == 2">
      and  DATE_FORMAT(update_time,'%Y%m%d') = DATE_FORMAT(NOW(),'%Y%m%d')
      and status in (3,4)
    </if>
    <if test="stores != null and stores.size() > 0">
      and store_name in
      <foreach collection="stores" item="store" open="(" close=")" separator=",">
        #{store}
      </foreach>
    </if>
    group by store_name,status
  </select>


  <select id="getSalesData" resultMap="BaseCollectMap">
    select
    CAST(link_id AS CHAR) name,status,count(1) collect
    from box_workbench_task_detail
    where c_customer_id = 176
    <if test="month == null" >
      and status in (1,2)
    </if>
    <if test="month != null and month == 1">
      and  DATE_FORMAT(update_time,'%Y%m') = DATE_FORMAT(NOW(),'%Y%m')
      and status in (3,4)
    </if>
    <if test="month != null and month == 2">
      and  DATE_FORMAT(update_time,'%Y%m%d') = DATE_FORMAT(NOW(),'%Y%m%d')
      and status in (3,4)
    </if>
    <if test="linkIds != null and linkIds.size() > 0">
      and link_id in
      <foreach collection="linkIds" item="linkId" open="(" close=")" separator=",">
        #{linkId}
      </foreach>
    </if>
    group by link_id,status
  </select>
  <select id="countByStatusAndFashionerId" resultType="java.lang.Long">
        select count(*) from box_workbench_task_detail
        <where>
          <if test="id != null">
            and fashioner_id = #{id}
          </if>
          <if test="lessTime != null">
            and IDX_DATE > #{lessTime}
          </if>
          <if test="sql != null and sql != ''">
              and ${sql}
          </if>
        </where>
  </select>


  <resultMap id="workListResultMap" type="com.jnby.base.entity.WorkCustomerAskBoxListEntity">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="LOGISTICS_ID" jdbcType="VARCHAR" property="logisticsId" />
    <result column="CREATE_FAS_ID" jdbcType="VARCHAR" property="createFasId" />
    <result column="SCENE_TAG" jdbcType="VARCHAR" property="sceneTag" />
    <result column="GOODS_TAG" jdbcType="VARCHAR" property="goodsTag" />
    <result column="WEIGHT" jdbcType="VARCHAR" property="weight" />
    <result column="CENNECT_PHONE" jdbcType="VARCHAR" property="cennectPhone" />
    <result column="WECHAT_NUMBER" jdbcType="VARCHAR" property="wechatNumber" />
    <result column="IMGS" jdbcType="VARCHAR" property="imgs" />
    <result column="MSG" jdbcType="VARCHAR" property="msg" />
    <result column="BOX_ID" jdbcType="VARCHAR" property="boxId" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="HEIGHT" jdbcType="VARCHAR" property="height" />
    <result column="BOX_SN" jdbcType="VARCHAR" property="boxSn" />
    <result column="CUS_GENDER" jdbcType="DECIMAL" property="cusGender" />
    <result column="ATTR_VALUE_IDS" jdbcType="VARCHAR" property="attrValueIds" />
    <result column="CONTACT_TYPE" jdbcType="DECIMAL" property="contactType" />
    <result column="SKIP" jdbcType="DECIMAL" property="skip" />
    <result column="PRODUCT_SIZE" jdbcType="VARCHAR" property="productSize" />
    <result column="THEME_ATTR_ID" jdbcType="VARCHAR" property="themeAttrId" />
    <result column="TOT_BUY_AMOUNT" jdbcType="DECIMAL" property="totBuyAmount" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="channel_id" jdbcType="VARCHAR" property="channelId"/>

    <result column="workId" jdbcType="VARCHAR" property="workId" />
    <result column="workStatus" jdbcType="DECIMAL" property="workStatus" />
    <result column="nickName" jdbcType="VARCHAR" property="nickName" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="cusId" jdbcType="VARCHAR" property="cusId" />
    <result column="sub_id" property="subId" />
    <result column="sub_plan_id" property="subPlanId" />
    <result column="ask_type" property="askType" />
  </resultMap>


  <select id="workCustomerAskBoxList" resultMap="workListResultMap">
    select a.id workId,a.status workStatus,a.NICK_NAME nickName ,a.create_time ctime,cd.id cusId,
    b.ID, b.UNIONID, b.LOGISTICS_ID, b.CREATE_FAS_ID, b.SCENE_TAG, b.GOODS_TAG, b.WEIGHT, b.CENNECT_PHONE,
    b.WECHAT_NUMBER, b.IMGS, b.MSG, b.BOX_ID, b.STATUS, b.CREATE_TIME, b.UPDATE_TIME, b.HEIGHT, b.BOX_SN,
    b.CUS_GENDER, b.ATTR_VALUE_IDS, b.CONTACT_TYPE, b.PRODUCT_SIZE,b.THEME_ATTR_ID,b.TOT_BUY_AMOUNT,b.REMARK,b.CHANNEL_ID
    from BOX_WORKBENCH_TASK_DETAIL a,customer_ask_box b,customer_details cd
    where a.out_no = b.id
    and b.unionid = cd.unionid
    <if test="lessTime != null">
      and a.idx_date > #{lessTime}
    </if>

    <if test="fashionerId != null">
      and a.fashioner_id = #{fashionerId}
    </if>

    <if test="type != null">
      and a.workbench_task_type = #{type}
    </if>

    <if test="search != null and search != '' ">
      and ( a.nick_name like '%${search}%' or   a.user_phone like  '%${search}%')
    </if>
    <if test="messageAdd != null and messageAdd != '' ">
      and ${messageAdd}
    </if>

    order by a.update_time desc
  </select>


  <resultMap id="workCustomerListResultMap" type="com.jnby.base.entity.WorkCustomerListEntity">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="OUT_ID" jdbcType="DECIMAL" property="outId" />
    <result column="OPENID" jdbcType="VARCHAR" property="openid" />
    <result column="NICK_NAME" jdbcType="VARCHAR" property="nickName" />
    <result column="HEAD_URL" jdbcType="VARCHAR" property="headUrl" />
    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="COIN" jdbcType="VARCHAR" property="coin" />
    <result column="WEIGHT" jdbcType="VARCHAR" property="weight" />
    <result column="HEIGHT" jdbcType="VARCHAR" property="height" />
    <result column="SKIN" jdbcType="VARCHAR" property="skin" />
    <result column="BODY" jdbcType="VARCHAR" property="body" />
    <result column="GENDER" jdbcType="DECIMAL" property="gender" />
    <result column="TROUSERS" jdbcType="VARCHAR" property="trousers" />
    <result column="SHOES" jdbcType="VARCHAR" property="shoes" />
    <result column="COAT" jdbcType="VARCHAR" property="coat" />
    <result column="SHIRT" jdbcType="VARCHAR" property="shirt" />
    <result column="UNDERWEAR" jdbcType="VARCHAR" property="underwear" />
    <result column="BRAND" jdbcType="VARCHAR" property="brand" />
    <result column="TOP_CLOTHING" jdbcType="VARCHAR" property="topClothing" />
    <result column="UNDER_CLOTHING" jdbcType="VARCHAR" property="underClothing" />
    <result column="SUB_EXPIRE_TIME" jdbcType="TIMESTAMP" property="subExpireTime" />
    <result column="FASHIONER_ID" jdbcType="VARCHAR" property="fashionerId" />
    <result column="SURVEY_ID" jdbcType="VARCHAR" property="surveyId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="HAS_ANSWER" jdbcType="DECIMAL" property="hasAnswer" />
    <result column="SHOW_ADDR_NOTICE" jdbcType="DECIMAL" property="showAddrNotice" />
    <result column="REG_FROM" jdbcType="VARCHAR" property="regFrom" />
    <result column="REG_TYPE" jdbcType="DECIMAL" property="regType" />
    <result column="CATEGORY_ID" jdbcType="VARCHAR" property="categoryId" />
    <result column="PUSH_MEMO" jdbcType="VARCHAR" property="pushMemo" />
    <result column="UDESK_ID" jdbcType="DECIMAL" property="udeskId" />
    <result column="SHOULDER_WIDTH" jdbcType="VARCHAR" property="shoulderWidth" />
    <result column="BUST" jdbcType="VARCHAR" property="bust" />
    <result column="HIPLINE" jdbcType="VARCHAR" property="hipline" />
    <result column="WAISTLINE" jdbcType="VARCHAR" property="waistline" />
    <result column="INVITE_CODE" jdbcType="VARCHAR" property="inviteCode" />
    <result column="MIN_QRCODE" jdbcType="VARCHAR" property="minQrcode" />
    <result column="CREDIT_STATUS" jdbcType="DECIMAL" property="creditStatus" />
    <result column="BODY_FEATURE" jdbcType="VARCHAR" property="bodyFeature" />
    <result column="BASE_COLOR" jdbcType="VARCHAR" property="baseColor" />
    <result column="COLOR" jdbcType="VARCHAR" property="color" />
    <result column="FABRIC" jdbcType="VARCHAR" property="fabric" />
    <result column="CONSTELLATION" jdbcType="VARCHAR" property="constellation" />
    <result column="BIRTHDAY" jdbcType="VARCHAR" property="birthday" />
    <result column="CUSTOMER_TYPE_ID" jdbcType="VARCHAR" property="customerTypeId" />
    <result column="EQUITY_TYPE" jdbcType="DECIMAL" property="equityType" />
    <result column="REFERRER_ID" jdbcType="VARCHAR" property="referrerId" />
    <result column="CASH" jdbcType="FLOAT" property="cash" />
    <result column="PARTNER_ID" jdbcType="VARCHAR" property="partnerId" />
    <result column="EQUITY_ID" jdbcType="VARCHAR" property="equityId" />
    <result column="VIPTYPE_ID" jdbcType="DECIMAL" property="viptypeId"/>
    <result column="VIPTYPE_NAME" jdbcType="VARCHAR" property="viptypeName"/>
    <result column="STYLES" jdbcType="VARCHAR" property="styles"/>
    <result column="JNBY_CARDNO" jdbcType="VARCHAR" property="jnbyCardNo"/>
    <result column="BG_REMARK" jdbcType="VARCHAR" property="bgRemark"/>
    <result column="HEAD" jdbcType="VARCHAR" property="head"/>
    <result column="GB" jdbcType="VARCHAR" property="gb"/>
    <result column="YAO" jdbcType="VARCHAR" property="yao"/>
    <result column="LEG" jdbcType="VARCHAR" property="leg"/>
    <result column="workStatus" jdbcType="VARCHAR" property="workStatus"/>
    <result column="category" jdbcType="VARCHAR" property="category"/>
  </resultMap>

  <select id="workCustomerOverList" resultMap="workCustomerListResultMap">
    select  a.status workStatus,cd.ID, cd.UNIONID, cd.OUT_ID, cd.OPENID, cd.NICK_NAME, cd.HEAD_URL, cd.PHONE, cd.COIN, cd.WEIGHT, cd.HEIGHT, cd.SKIN,
    cd.BODY, cd.GENDER, cd.TROUSERS, cd.SHOES, cd.COAT, cd.SHIRT, cd.UNDERWEAR, cd.BRAND, cd.TOP_CLOTHING, cd.UNDER_CLOTHING,
    cd.SUB_EXPIRE_TIME, cd.FASHIONER_ID, cd.SURVEY_ID, cd.CREATE_TIME, cd.UPDATE_TIME, cd.HAS_ANSWER, cd.SHOW_ADDR_NOTICE,
    cd.REG_FROM, cd.REG_TYPE, cd.CATEGORY_ID, cd.PUSH_MEMO, cd.UDESK_ID, cd.SHOULDER_WIDTH, cd.BUST, cd.HIPLINE,
    cd.WAISTLINE, cd.INVITE_CODE, cd.MIN_QRCODE, cd.CREDIT_STATUS, cd.BODY_FEATURE, cd.BASE_COLOR, cd.COLOR,
    cd.FABRIC, cd.CONSTELLATION,cd.BIRTHDAY, cd.CUSTOMER_TYPE_ID, cd.EQUITY_TYPE, cd.REFERRER_ID, cd.CASH,
    cd.PARTNER_ID, cd.EQUITY_ID,cd.VIPTYPE_ID,cd.VIPTYPE_NAME,cd.STYLES,cd.JNBY_CARDNO,cd.BG_REMARK,cd.head,cd.gb,cd.yao,cd.leg  from
    BOX_WORKBENCH_TASK_DETAIL a,customer_details cd
    where a.out_no = cd.id
    <if test="lessTime != null">
      and a.idx_date > #{lessTime}
    </if>

    <if test="fashionerId != null">
      and a.fashioner_id = #{fashionerId}
    </if>
    <if test="type != null">
      and a.workbench_task_type = #{type}
    </if>


    <if test="search != null and search != '' ">
      and ( a.nick_name like '%${search}%' or   a.user_phone like  '%${search}%')
    </if>
    <if test="messageAdd != null and messageAdd != '' ">
      and ${messageAdd}
    </if>

    order by a.update_time desc

  </select>
    <select id="findNumByStatusAndFashionerId"
            resultMap="BaseResultMap">

     select <include refid="Base_Column_List"></include>
     from BOX_WORKBENCH_TASK_DETAIL
        <where>
           <if test="lessTime != null">
             and IDX_DATE > #{lessTime}
           </if>

            <if test="fashionerIds != null and fashionerIds.size() > 0 ">
              and fashioner_id in
              <foreach collection="fashionerIds" item="item" open="(" close=")" separator=",">
                #{item}
              </foreach>
            </if>

            <if test="statusList != null and statusList.size() > 0 ">
              and status in
              <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                #{item}
              </foreach>
            </if>

            <if test="dateTimeFormat != null">
              and DATE_FORMAT(update_time, ${dateTimeFormat}) =  DATE_FORMAT(NOW(), ${dateTimeFormat})
            </if>
        </where>
    </select>


  <select id="findNumDetail" resultType="java.lang.Integer">
    select  count(*) from BOX_WORKBENCH_TASK_DETAIL
    <where>
      <if test="lessTime != null">
        and idx_date > #{lessTime}
      </if>

      <if test="fashionerId != null">
        and fashioner_id = #{fashionerId}
      </if>
      <if test="type != null">
        and workbench_task_type = #{type}
      </if>


    <if test="sql != null and sql != '' ">
        and ${sql}
    </if>

    </where>
  </select>

  <select id="findFinishedNumDetail" resultType="java.lang.Integer">
    select  count(*) from BOX_WORKBENCH_TASK_DETAIL
    <where>
      <if test="lessTime != null">
        and idx_date > #{lessTime}
      </if>

      <if test="fashionerId != null">
        and fashioner_id = #{fashionerId}
      </if>
      <if test="type != null">
        and workbench_task_type = #{type}
      </if>
      <if test='status != null and status == "3"'>
        and (status = 3 or status = 4) and  DATE_FORMAT(update_time,'%Y%m') >=  DATE_FORMAT(#{lessTime},'%Y%m')
      </if>
      <if test='status != null and status == "1"'>
        and status in (1)
      </if>
      <if test='status != null and status == "2"'>
        and  status = #{status}
      </if>
    </where>
  </select>


  <resultMap id="ResultFindList" type="com.jnby.application.admin.dto.response.ShoppingGuideOvertimeListResp">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionId" />
    <result column="NICK_NAME" jdbcType="VARCHAR" property="nickName" />
    <result column="CUS_GENDER" jdbcType="INTEGER" property="cusGender" />
    <result column="contact_type" jdbcType="INTEGER" property="contactType" />
    <result column="cennect_phone" jdbcType="VARCHAR" property="cennectPhone" />
    <result column="wechat_number" jdbcType="VARCHAR" property="wechatNumber" />
    <result column="create_fas_id" jdbcType="VARCHAR" property="fashionerId" />
    <result column="name" jdbcType="VARCHAR" property="fashionerName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="theme_attr_id" jdbcType="VARCHAR" property="themeAttrId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="box_sn" jdbcType="VARCHAR" property="boxSn" />
    <result column="status" jdbcType="VARCHAR" property="status"/>
    <result column="channel_id" jdbcType="VARCHAR" property="channelId"/>
    <result column="last_fashioner_id" jdbcType="VARCHAR" property="lastFashionerId"/>
    <result column="last_fashioner_name" jdbcType="VARCHAR" property="lastFashionerName"/>
  </resultMap>

    <select id="getShoppingGuideOvertimeList"
            resultMap="ResultFindList">
      select
      cab.id,cab.unionid,cab.cus_gender,cab.contact_type,cab.cennect_phone,
      cab.wechat_number,cab.create_fas_id,cab.create_time,cab.theme_attr_id,cab.remark,cab.box_sn, cab.channel_id,
      bwtd.fashioner_id,cab.last_fashioner_id,cab.last_fashioner_name
       from BOX_WORKBENCH_TASK_DETAIL bwtd
      inner join CUSTOMER_ASK_BOX cab on bwtd.OUT_NO = cab.ID
      <where>
        and bwtd.STATUS = 2 and bwtd.WORKBENCH_TASK_TYPE = 1
        <if test="unionIds != null and unionIds.size()>0 ">
            and cab.unionid in
            <foreach collection="unionIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="linkId != null">
          and  (bwtd.LINK_ID = #{linkId})
        </if>
        <if test='fashionerType != null and fashionerType != ""'>
          and  cab.is_sales = #{fashionerType}
        </if>
        <if test='lastFashionerId != null and lastFashionerId != ""'>
          and  cab.last_fashioner_id = #{lastFashionerId}
        </if>

        <if test="searchStartDate != null">
          and DATE_FORMAT(cab.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{searchStartDate},'%Y-%m-%d')
        </if>

        <if test="searchEndDate != null">
          and DATE_FORMAT(#{searchEndDate},'%Y-%m-%d') >= DATE_FORMAT(cab.create_time,'%Y-%m-%d')
        </if>
      </where>
      order by cab.create_time desc

    </select>



  <select id="newWorkCustomerAskBoxList" resultMap="workListResultMap">
    select a.id workId,a.status workStatus,a.NICK_NAME nickName ,a.create_time ctime,cd.id cusId,
    b.ID, b.UNIONID, b.LOGISTICS_ID, b.CREATE_FAS_ID, b.SCENE_TAG, b.GOODS_TAG, b.WEIGHT, cd.phone as "CENNECT_PHONE",
    b.WECHAT_NUMBER, b.IMGS, b.MSG, b.BOX_ID, b.STATUS, b.CREATE_TIME, b.UPDATE_TIME, b.HEIGHT, b.BOX_SN,
    b.CUS_GENDER, b.ATTR_VALUE_IDS, b.CONTACT_TYPE, b.PRODUCT_SIZE,b.THEME_ATTR_ID,b.TOT_BUY_AMOUNT,b.REMARK,b.CHANNEL_ID,
    b.sub_id,b.sub_plan_id,b.ask_type
    from BOX_WORKBENCH_TASK_DETAIL a,customer_ask_box b,customer_details cd
    where a.out_no = b.id
    and b.unionid = cd.unionid
    <if test="lessTime != null">
      and a.idx_date > #{lessTime}
    </if>

    <if test="fashionerId != null">
      and a.fashioner_id = #{fashionerId}
    </if>

    <if test="type != null">
      and a.workbench_task_type = #{type}
    </if>

    <if test="search != null and search != '' ">
      and ( a.nick_name like '%${search}%' or   a.user_phone like  '%${search}%')
    </if>

    <!--已完结-->
    <if test='status != null and status == "3"'>
      and (a.status = 3 or a.status = 4)
      and DATE_FORMAT(a.update_time,'%Y%m') >=  DATE_FORMAT(#{lessTime},'%Y%m')
    </if>
    <if test='status != null and status != "3" '>
      and a.status = #{status}
    </if>
    order by a.update_time desc
  </select>

</mapper>
