<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BoxReturnTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BoxReturnTag">
        <result column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CLASS_LIMIT" property="classLimit" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, GROUP_ID, CLASS_LIMIT, CREATE_TIME, UPDATE_TIME
    </sql>

</mapper>