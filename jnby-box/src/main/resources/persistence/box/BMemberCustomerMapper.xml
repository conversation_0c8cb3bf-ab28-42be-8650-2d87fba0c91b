<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BMemberCustomerMapper">
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BMemberCustomer">
        <result column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="MEMBER_ID" jdbcType="VARCHAR" property="memberId"/>
        <result column="PHONE" jdbcType="VARCHAR" property="phone"/>
        <result column="IS_DEL" jdbcType="DECIMAL" property="isDel"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="UNIONID" jdbcType="VARCHAR" property="unionid"/>
    </resultMap>

    <insert id="insert" parameterType="com.jnby.infrastructure.box.model.BMemberCustomer">
    insert INTO B_MEMBER_CUSTOMER (ID, MEMBER_ID, PHONE, 
      IS_DEL, CREATE_TIME, UPDATE_TIME, 
      UNIONID)
    values (#{id,jdbcType=VARCHAR}, #{memberId,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{isDel,jdbcType=DECIMAL}, NOW(),NOW(),
      #{unionid,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.jnby.infrastructure.box.model.BMemberCustomer">
        insert INTO B_MEMBER_CUSTOMER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="memberId != null">
                MEMBER_ID,
            </if>
            <if test="phone != null">
                PHONE,
            </if>
            IS_DEL,
            CREATE_TIME,
            UPDATE_TIME,
            <if test="unionid != null">
                UNIONID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="memberId != null">
                #{memberId,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            0,
            NOW(),
            NOW(),
            <if test="unionid != null">
                #{unionid,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch">
        INSERT INTO B_MEMBER_CUSTOMER (ID, MEMBER_ID, PHONE, IS_DEL, CREATE_TIME, UPDATE_TIME, UNIONID)
        VALUES
        <foreach collection="records" item="item" separator=",">
            (#{item.id}, #{item.memberId}, #{item.phone},
            #{item.isDel}, NOW(), NOW(),
            #{item.unionid})
        </foreach>
    </insert>

    <select id="selectByMemberCustomer" resultMap="BaseResultMap">
        select ID,MEMBER_ID,PHONE,IS_DEL,CREATE_TIME,UPDATE_TIME, UNIONID
        from B_MEMBER_CUSTOMER
        where IS_DEL = 0
        <if test="unionid != null">
            and UNIONID = #{unionid}
        </if>
        <if test="memberId != null">
            and MEMBER_ID = #{memberId}
        </if>
    </select>
</mapper>