<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BoxRefundMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BoxRefund">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="REFUND_SN" jdbcType="VARCHAR" property="refundSn" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
    <result column="TRACKING_NUMBER" jdbcType="VARCHAR" property="trackingNumber" />
    <result column="REFUND_AMOUNT" jdbcType="FLOAT" property="refundAmount" />
    <result column="SEND_LOGISTICS_ID" jdbcType="VARCHAR" property="sendLogisticsId" />
    <result column="GET_DATE" jdbcType="VARCHAR" property="getDate" />
    <result column="SEND_BACK" jdbcType="DECIMAL" property="sendBack" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="REFUND_REMARK" jdbcType="VARCHAR" property="refundRemark" />
    <result column="REFUND_PHOTOS" jdbcType="VARCHAR" property="refundPhotos" />
    <result column="AUTO_AMOUNT" jdbcType="DECIMAL" property="autoAmount" />
    <result column="WEIMO_REFUND_ID" jdbcType="VARCHAR" property="weimoRefundId" />
    <result column="REFUND_BALANCE" jdbcType="DECIMAL" property="refundBalance" />
    <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, REFUND_SN, UNIONID, ORDER_ID, STATUS, MEMO, TRACKING_NUMBER, REFUND_AMOUNT, SEND_LOGISTICS_ID,
    GET_DATE, SEND_BACK, CREATE_TIME, UPDATE_TIME, REFUND_REMARK, REFUND_PHOTOS, AUTO_AMOUNT,WEIMO_REFUND_ID,REFUND_BALANCE,STORE_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from BOX_REFUND
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectBySn" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from BOX_REFUND
    where refund_sn = #{sn,jdbcType=VARCHAR}
  </select>


  <select id="findByBoxSn" parameterType="java.lang.String" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from BOX_REFUND
    where order_id in ( select id from order_ where BOX_SN = #{boxSn,jdbcType=VARCHAR})

  </select>
    <select id="selectUnByOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from BOX_REFUND
        where order_id in
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>
  
  
  <resultMap id="refundListRespResultMap" type="com.jnby.application.admin.dto.response.RefundListResp">
    <result column="REFUND_SN" jdbcType="VARCHAR" property="refundSn" />
    <result column="refund_photos" jdbcType="VARCHAR" property="refundPhotos" />
    <result column="tracking_number" jdbcType="VARCHAR" property="trackingNumber" />
    <result column="get_date" jdbcType="VARCHAR" property="getDate" />
    <result column="status" jdbcType="DECIMAL" property="status" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="type" jdbcType="DECIMAL" property="type" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="REFUND_REMARK" jdbcType="VARCHAR" property="refundRemark" />
    <result column="WEIMO_REFUND_ID" jdbcType="VARCHAR" property="weimoRefundId" />
    <result column="BOX_SN" jdbcType="VARCHAR" property="boxSn" />
  </resultMap>
  

    <select id="selectBoxRefundListByParams"
            resultMap="refundListRespResultMap">
      select a.refund_sn,a.refund_photos, a.tracking_number,a.get_date,a.status ,a.create_time,a.update_time,b.order_sn,b.type ,
        c.nick_name, c.phone ,e.name, a.REFUND_REMARK,a.WEIMO_REFUND_ID,b.box_sn
      FROM box_refund a
      inner join order_ b on a.order_id = b.id
      inner join customer_details c on a.unionid = c.unionid
      left join fashioner e on c.fashioner_id = e.id
      WHERE 1=1
      <if  test="fasIds != null and fasIds.size() > 0">
      and e.id in
        <foreach collection="fasIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
      </if>
      <if test="startTime != null">
        AND #{startTime} &lt;= a.create_time
      </if>
      <if test="endTime != null">
        AND #{endTime} >= a.create_time
      </if>
      <if test="status != null">
        and a.status = #{status}
      </if>
      <if test="isOk != null and isOk == 0 ">
         and a.status in(0,1,2)
      </if>
      <if test="isOk != null and isOk == 1 ">
        and a.status in(3,4,5,6,8)
      </if>
      <if test="type != null and type != '' ">
        and b.type = #{type}
      </if>
      <if test="expressNo != null and expressNo != '' ">
        and a.tracking_number = #{expressNo}
      </if>
      <if test="refundSn != null and refundSn != '' ">
        and a.refund_sn = #{refundSn}
      </if>
      <if test="orderSn != null and orderSn != '' ">
        and b.order_sn = #{orderSn}
      </if>
      <if test="phone != null and phone != '' ">
        and c.phone = #{phone}
      </if>
      order by a.create_time DESC
    </select>
  <select id="selectUnFinshBoxRefundByOrderId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from BOX_REFUND
    where status in (0,1,2) and ORDER_ID = #{orderId}
  </select>
    <select id="selectBoxRefundByWechattransactionId" resultMap="BaseResultMap">
      select
    a.ID, a.REFUND_SN, a.UNIONID, a.ORDER_ID, a.STATUS, a.MEMO, a.TRACKING_NUMBER, a.REFUND_AMOUNT, a.SEND_LOGISTICS_ID,
    a.GET_DATE, a.SEND_BACK, a.CREATE_TIME, a.UPDATE_TIME, a.REFUND_REMARK, a.REFUND_PHOTOS, a.AUTO_AMOUNT,a.WEIMO_REFUND_ID
    from BOX_REFUND a ,order_ b where a.order_id=b.id and b.wechat_transaction_id = #{transactionId}
    </select>
  <select id="selectByMemos" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from BOX_REFUND
    where memo in
    <foreach collection="docNos" separator="," item="item" open="(" close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectByMemoAndRetailItemId" resultMap="BaseResultMap">
    select    br.ID, br.REFUND_SN, br.UNIONID, br.ORDER_ID, br.STATUS, br.MEMO, br.TRACKING_NUMBER, br.REFUND_AMOUNT, br.SEND_LOGISTICS_ID,
    br.GET_DATE, br.SEND_BACK, br.CREATE_TIME, br.UPDATE_TIME, br.REFUND_REMARK, br.REFUND_PHOTOS, br.AUTO_AMOUNT,br.WEIMO_REFUND_ID,br.REFUND_BALANCE
        from BOX_REFUND br left join BOX_REFUND_DETAILS  brd on br.id = brd.BOX_REFUND_ID
    where concat(concat(br.memo,'-'),brd.RETAIL_ITEM_ID) in
    <foreach collection="refundNos" separator="," item="item" open="(" close=")">
      #{item}
    </foreach>
  </select>
  <select id="selectUnFinshBoxRefundByOrderIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from BOX_REFUND
    where status &lt; 3 and ORDER_ID in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
  <select id="findBoxRefundByOrderId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from BOX_REFUND
    where ORDER_ID = #{orderId}
    order by CREATE_TIME DESC
  </select>
    <select id="selectShouldSyncStatus" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"></include> from BOX_REFUND
         where status in (0,1) and tracking_number is not null
    </select>
  <select id="selectByWeimoRefundId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from BOX_REFUND
    where status in (0,1,2) and WEIMO_REFUND_ID = #{weimoRefundId}
  </select>
  <select id="findByWeimoRefundId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from BOX_REFUND
    where  WEIMO_REFUND_ID = #{weimoRefundId}
  </select>
    <select id="selectByTradeNoInfo"  resultType="com.jnby.application.admin.dto.response.RefundDataResp">
      select br.refund_remark as refundRemark,od.product_code as productCode from order_  o
                             inner join box_refund br on o.id = br.order_id
                             inner join order_Detail od on o.id = od.order_id
                             inner join box_refund_details brd on od.id = brd.order_detail_id and br.id = brd.box_refund_id
      where (o.order_sn = #{tradeNo} or
            o.wechat_transaction_id = #{tradeNo})
        and od.product_code in 
        <foreach collection="productCodes" separator="," item="item" close=")" open="(">
          #{item}
        </foreach>
        and br.status = 3
    </select>
    <select id="selectRefundingStatus" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"></include> from BOX_REFUND where status = 8
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from BOX_REFUND
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.jnby.infrastructure.box.model.BoxRefund">
    insert INTO BOX_REFUND (ID, REFUND_SN, UNIONID,
      ORDER_ID, STATUS, MEMO,
      TRACKING_NUMBER, REFUND_AMOUNT, SEND_LOGISTICS_ID,
      GET_DATE, SEND_BACK, CREATE_TIME,
      UPDATE_TIME, REFUND_REMARK, REFUND_PHOTOS,
      AUTO_AMOUNT)
    values (#{id,jdbcType=VARCHAR}, #{refundSn,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR},
      #{orderId,jdbcType=VARCHAR}, #{status,jdbcType=DECIMAL}, #{memo,jdbcType=VARCHAR},
      #{trackingNumber,jdbcType=VARCHAR}, #{refundAmount,jdbcType=FLOAT}, #{sendLogisticsId,jdbcType=VARCHAR},
      #{getDate,jdbcType=VARCHAR}, #{sendBack,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{refundRemark,jdbcType=VARCHAR}, #{refundPhotos,jdbcType=VARCHAR},
      #{autoAmount,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.jnby.infrastructure.box.model.BoxRefund">
    insert INTO BOX_REFUND
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="refundSn != null">
        REFUND_SN,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="memo != null">
        MEMO,
      </if>
      <if test="trackingNumber != null">
        TRACKING_NUMBER,
      </if>
      <if test="refundAmount != null">
        REFUND_AMOUNT,
      </if>
      <if test="sendLogisticsId != null">
        SEND_LOGISTICS_ID,
      </if>
      <if test="getDate != null">
        GET_DATE,
      </if>
      <if test="sendBack != null">
        SEND_BACK,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="refundRemark != null">
        REFUND_REMARK,
      </if>
      <if test="refundPhotos != null">
        REFUND_PHOTOS,
      </if>
      <if test="autoAmount != null">
        AUTO_AMOUNT,
      </if>
      <if test="weimoRefundId != null">
        WEIMO_REFUND_ID,
      </if>
      <if test="refundBalance != null">
        REFUND_BALANCE,
      </if>
      <if test="storeId != null and storeId != '' ">
        STORE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="refundSn != null">
        #{refundSn,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="trackingNumber != null">
        #{trackingNumber,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=FLOAT},
      </if>
      <if test="sendLogisticsId != null">
        #{sendLogisticsId,jdbcType=VARCHAR},
      </if>
      <if test="getDate != null">
        #{getDate,jdbcType=VARCHAR},
      </if>
      <if test="sendBack != null">
        #{sendBack,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundRemark != null">
        #{refundRemark,jdbcType=VARCHAR},
      </if>
      <if test="refundPhotos != null">
        #{refundPhotos,jdbcType=VARCHAR},
      </if>
      <if test="autoAmount != null">
        #{autoAmount,jdbcType=DECIMAL},
      </if>
      <if test="weimoRefundId != null">
        #{weimoRefundId},
      </if>
      <if test="refundBalance != null">
        #{refundBalance},
      </if>
      <if test="storeId != null and storeId != '' ">
        #{storeId},
      </if>
    </trim>
  </insert>
    <insert id="batchInsert">
        INSERT INTO BOX_REFUND
        (ID, UNIONID, REFUND_SN,
        ORDER_ID, STATUS, MEMO,REFUND_AMOUNT,REFUND_REMARK,AUTO_AMOUNT,CREATE_TIME,UPDATE_TIME,REFUND_BALANCE
        ) VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.unionid,jdbcType=VARCHAR},
            #{item.refundSn,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, #{item.status,jdbcType=DECIMAL},
            #{item.memo,jdbcType=VARCHAR}, #{item.refundAmount,jdbcType=FLOAT}, #{item.refundRemark,jdbcType=VARCHAR}, #{item.autoAmount,jdbcType=DECIMAL},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.refundBalance,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.box.model.BoxRefund">
    update BOX_REFUND
    <set>
      <if test="refundSn != null">
        REFUND_SN = #{refundSn,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="memo != null">
        MEMO = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="trackingNumber != null">
        TRACKING_NUMBER = #{trackingNumber,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        REFUND_AMOUNT = #{refundAmount,jdbcType=FLOAT},
      </if>
      <if test="sendLogisticsId != null">
        SEND_LOGISTICS_ID = #{sendLogisticsId,jdbcType=VARCHAR},
      </if>
      <if test="getDate != null">
        GET_DATE = #{getDate,jdbcType=VARCHAR},
      </if>
      <if test="sendBack != null">
        SEND_BACK = #{sendBack,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundRemark != null">
        REFUND_REMARK = #{refundRemark,jdbcType=VARCHAR},
      </if>
      <if test="refundPhotos != null">
        REFUND_PHOTOS = #{refundPhotos,jdbcType=VARCHAR},
      </if>
      <if test="autoAmount != null">
        AUTO_AMOUNT = #{autoAmount,jdbcType=DECIMAL},
      </if>
      <if test="weimoRefundId != null">
        WEIMO_REFUND_ID = #{weimoRefundId},
      </if>
      <if test="storeId != null and storeId != '' ">
        STORE_ID = #{storeId},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jnby.infrastructure.box.model.BoxRefund">
    update BOX_REFUND
    set REFUND_SN = #{refundSn,jdbcType=VARCHAR},
      UNIONID = #{unionid,jdbcType=VARCHAR},
      ORDER_ID = #{orderId,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=DECIMAL},
      MEMO = #{memo,jdbcType=VARCHAR},
      TRACKING_NUMBER = #{trackingNumber,jdbcType=VARCHAR},
      REFUND_AMOUNT = #{refundAmount,jdbcType=FLOAT},
      SEND_LOGISTICS_ID = #{sendLogisticsId,jdbcType=VARCHAR},
      GET_DATE = #{getDate,jdbcType=VARCHAR},
      SEND_BACK = #{sendBack,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      REFUND_REMARK = #{refundRemark,jdbcType=VARCHAR},
      REFUND_PHOTOS = #{refundPhotos,jdbcType=VARCHAR},
      AUTO_AMOUNT = #{autoAmount,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
