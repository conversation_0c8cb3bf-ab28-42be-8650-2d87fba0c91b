<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BElevenActivityUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BElevenActivityUser">
        <id column="ID" property="id" />
        <result column="CARD_NO" property="cardNo" />
        <result column="UNIONID" property="unionid" />
        <result column="STATUS" property="status" />

        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CARD_NO, UNIONID,STATUS, CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>
    <select id="selectListByUnionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from B_ELEVEN_ACTIVITY_USER
        where UNIONID = #{unionId,jdbcType=VARCHAR} and DEL_FLAG=0
    </select>

    <select id="noReceiveList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from B_ELEVEN_ACTIVITY_USER
        where DEL_FLAG=0 and STATUS=0
    </select>

    <update id="updateStatusByIds">
        update B_ELEVEN_ACTIVITY_USER set status=1 , UPDATE_TIME =NOW() where id
        IN
        <foreach collection="ids" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
    </update>
</mapper>