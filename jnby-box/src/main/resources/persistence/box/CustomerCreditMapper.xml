<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CustomerCreditMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.CustomerCredit">
        <id column="ID" property="id" />
        <result column="UNIONID" property="unionid" />
        <result column="WECHAT_ORDER_SN" property="wechatOrderSn" />
        <result column="WECHAT_REFUND_SN" property="wechatRefundSn" />
        <result column="CREDIT_SN" property="creditSn" />
        <result column="DAYSTR" property="daystr" />
        <result column="SERIAL_NUMBER" property="serialNumber" />
        <result column="STATUS" property="status" />
        <result column="CREDIT_TYPE" property="creditType" />
        <result column="FROM_TYPE" property="fromType" />
        <result column="SUBSCRIBE_ORDER_SN" property="subscribeOrderSn" />
        <result column="CREDIT_TIME" property="creditTime" />
        <result column="REFUND_TIME" property="refundTime" />
        <result column="CREDIT_CASH" property="creditCash" />
        <result column="IS_EFFECTIVE" property="isEffective" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="RETURN_TYPE" property="returnType" />
        <result column="RETURN_TIME" property="returnTime" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, UNIONID, WECHAT_ORDER_SN, WECHAT_REFUND_SN, CREDIT_SN, DAYSTR, SERIAL_NUMBER, STATUS, CREDIT_TYPE, FROM_TYPE, SUBSCRIBE_ORDER_SN, CREDIT_TIME, REFUND_TIME, CREDIT_CASH, IS_EFFECTIVE, CREATE_TIME, RETURN_TYPE, RETURN_TIME, UPDATE_TIME
    </sql>

    <select id="selectNumber" resultType="java.lang.String">
    select
      max(serial_number)
    from CUSTOMER_CREDIT
    where daystr = #{daystr,jdbcType=VARCHAR}
  </select>

</mapper>