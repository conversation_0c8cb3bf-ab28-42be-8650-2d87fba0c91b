<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BoxConcatLogMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BoxConcatLog">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="FASHIONER_ID" jdbcType="VARCHAR" property="fashionerId" />
    <result column="CUSTOMER_ID" jdbcType="VARCHAR" property="customerId" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="IF_BOX" jdbcType="DECIMAL" property="ifBox" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, FASHIONER_ID, CUSTOMER_ID, STATUS, IF_BOX, REMARK, CREATE_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from BOX_CONCAT_LOG
    where ID = #{id,jdbcType=VARCHAR}
  </select>


    <select id="selectForTalkLog" parameterType="java.lang.String" resultMap="BaseResultMap">

    select a.*,b.NAME from BOX_CONCAT_LOG a
    left join FASHIONER b on a.fashioner_id = b.id
    where a.CUSTOMER_ID =  #{customerId,jdbcType=VARCHAR} order by a.CREATE_TIME desc

    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from BOX_CONCAT_LOG
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.jnby.infrastructure.box.model.BoxConcatLog">
    insert INTO BOX_CONCAT_LOG (ID, FASHIONER_ID, CUSTOMER_ID, 
      STATUS, IF_BOX, REMARK, 
      CREATE_TIME, UPDATE_TIME)
    values (#{id,jdbcType=VARCHAR}, #{fashionerId,jdbcType=VARCHAR}, #{customerId,jdbcType=VARCHAR}, 
      #{status,jdbcType=DECIMAL}, #{ifBox,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.jnby.infrastructure.box.model.BoxConcatLog">
    insert INTO BOX_CONCAT_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="fashionerId != null">
        FASHIONER_ID,
      </if>
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="ifBox != null">
        IF_BOX,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="fashionerId != null">
        #{fashionerId,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="ifBox != null">
        #{ifBox,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.box.model.BoxConcatLog">
    update BOX_CONCAT_LOG
    <set>
      <if test="fashionerId != null">
        FASHIONER_ID = #{fashionerId,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        CUSTOMER_ID = #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="ifBox != null">
        IF_BOX = #{ifBox,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jnby.infrastructure.box.model.BoxConcatLog">
    update BOX_CONCAT_LOG
    set FASHIONER_ID = #{fashionerId,jdbcType=VARCHAR},
      CUSTOMER_ID = #{customerId,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=DECIMAL},
      IF_BOX = #{ifBox,jdbcType=DECIMAL},
      REMARK = #{remark,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>