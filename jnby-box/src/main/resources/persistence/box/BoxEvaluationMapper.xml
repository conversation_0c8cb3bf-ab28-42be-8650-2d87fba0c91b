<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BoxEvaluationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BoxEvaluation">
        <id column="ID" property="id" />
        <result column="UNIONID" property="unionid" />
        <result column="BOX_ID" property="boxId" />
        <result column="BOX_SCORE" property="boxScore" />
        <result column="FASHIONER_ID" property="fashionerId" />
        <result column="FASHIONER_SCORE" property="fashionerScore" />
        <result column="SIZE_SCORE" property="sizeScore" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CONTENT" property="content" />
        <result column="IS_SHOW" property="isShow" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CUSTOMERSERVICE_SCORE" property="customerserviceScore" />

        <result column="EXPRESS_SCORE" property="expressScore" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, UNIONID, BOX_ID, BOX_SCORE, FASHIONER_ID, FASHIONER_SCORE, SIZE_SCORE, CREATE_TIME, CONTENT, IS_SHOW, UPDATE_TIME, CUSTOMERSERVICE_SCORE,EXPRESS_SCORE
    </sql>
    <select id="getEvaluateList" resultType="com.jnby.application.admin.dto.response.BoxEvaluationResp">
        select be.id ,be.create_time as createTime,be.is_show as isShow,be.fashioner_id as fashionerId, be.content as content, cd.head_url as headUrl, cd.nick_name  as nickName
         from box_evaluation be left join customer_details cd on be.unionid=cd.unionid
        where be.fashioner_id = #{fashionerId}
          <if test="isShow != null">
              and be.IS_SHOW = #{isShow}
          </if>
        order by be.is_show desc,be.create_time desc
    </select>

</mapper>
