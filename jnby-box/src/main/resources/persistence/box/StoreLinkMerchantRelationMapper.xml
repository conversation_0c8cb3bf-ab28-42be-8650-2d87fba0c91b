<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.StoreLinkMerchantRelationMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.StoreLinkMerchantRelation">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="cStoreId" column="C_STORE_ID" jdbcType="VARCHAR"/>
            <result property="linkMerchantCode" column="LINK_MERCHANT_CODE" jdbcType="VARCHAR"/>
            <result property="type" column="TYPE" jdbcType="VARCHAR"/>
        <result property="cashType" column="CASH_TYPE" jdbcType="VARCHAR"/>
        <result property="merchantMarketName" column="MERCHANT_MARKET_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,C_STORE_ID,LINK_MERCHANT_CODE,TYPE,CASH_TYPE,MERCHANT_MARKET_NAME
    </sql>
    <select id="selectByMerchantCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from STORE_LINK_MERCHANT_RELATION
        where LINK_MERCHANT_CODE = #{merchantCode}
    </select>
    <select id="selectByCstoreId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from STORE_LINK_MERCHANT_RELATION
        where C_STORE_ID = #{storeId}
    </select>


</mapper>
