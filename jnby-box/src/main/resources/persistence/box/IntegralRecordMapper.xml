<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.IntegralRecordMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.IntegralRecord">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="INTEGRAL_SN" jdbcType="VARCHAR" property="integralSn" />
    <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber" />
    <result column="DAYSTR" jdbcType="VARCHAR" property="daystr" />
    <result column="POINT" jdbcType="DECIMAL" property="point" />
    <result column="TYPE" jdbcType="DECIMAL" property="type" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="REASON" jdbcType="VARCHAR" property="reason" />
    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, UNIONID, INTEGRAL_SN, SERIAL_NUMBER, DAYSTR, POINT, TYPE, STATUS, REASON, MEMO, 
    CREATE_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from INTEGRAL_RECORD
    where ID = #{id,jdbcType=VARCHAR}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from INTEGRAL_RECORD
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.jnby.infrastructure.box.model.IntegralRecord">
    insert INTO INTEGRAL_RECORD (ID, UNIONID, INTEGRAL_SN, 
      SERIAL_NUMBER, DAYSTR, POINT, 
      TYPE, STATUS, REASON, 
      MEMO, CREATE_TIME, UPDATE_TIME
      )
    values (#{id,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, #{integralSn,jdbcType=VARCHAR}, 
      #{serialNumber,jdbcType=VARCHAR}, #{daystr,jdbcType=VARCHAR}, #{point,jdbcType=DECIMAL}, 
      #{type,jdbcType=DECIMAL}, #{status,jdbcType=DECIMAL}, #{reason,jdbcType=VARCHAR}, 
      #{memo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jnby.infrastructure.box.model.IntegralRecord">
    insert INTO INTEGRAL_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="integralSn != null">
        INTEGRAL_SN,
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER,
      </if>
      <if test="daystr != null">
        DAYSTR,
      </if>
      <if test="point != null">
        POINT,
      </if>
      <if test="type != null">
        TYPE,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="reason != null">
        REASON,
      </if>
      <if test="memo != null">
        MEMO,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="integralSn != null">
        #{integralSn,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="daystr != null">
        #{daystr,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        #{point,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        #{type,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.box.model.IntegralRecord">
    update INTEGRAL_RECORD
    <set>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="integralSn != null">
        INTEGRAL_SN = #{integralSn,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="daystr != null">
        DAYSTR = #{daystr,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        POINT = #{point,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        TYPE = #{type,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="reason != null">
        REASON = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        MEMO = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jnby.infrastructure.box.model.IntegralRecord">
    update INTEGRAL_RECORD
    set UNIONID = #{unionid,jdbcType=VARCHAR},
      INTEGRAL_SN = #{integralSn,jdbcType=VARCHAR},
      SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      DAYSTR = #{daystr,jdbcType=VARCHAR},
      POINT = #{point,jdbcType=DECIMAL},
      TYPE = #{type,jdbcType=DECIMAL},
      STATUS = #{status,jdbcType=DECIMAL},
      REASON = #{reason,jdbcType=VARCHAR},
      MEMO = #{memo,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectNumber" resultType="java.lang.String">
    SELECT max(serial_number)  FROM INTEGRAL_RECORD  WHERE daystr=#{daystr,jdbcType=VARCHAR}
  </select>
  <select id="selectBySelective" parameterType="com.jnby.infrastructure.box.model.IntegralRecord" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from INTEGRAL_RECORD
    <where>
    <if test="id != null">
     and ID = #{id,jdbcType=VARCHAR}
    </if>
    <if test="unionid != null">
     and UNIONID = #{unionid,jdbcType=VARCHAR}
    </if>
    <if test="integralSn != null">
     and INTEGRAL_SN = #{integralSn,jdbcType=VARCHAR}
    </if>
    <if test="serialNumber != null">
     and SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR}
    </if>
    <if test="daystr != null">
      and DAYSTR = #{daystr,jdbcType=VARCHAR}
    </if>
    <if test="point != null">
      and POINT = #{point,jdbcType=DECIMAL}
    </if>
    <if test="type != null">
      and TYPE = #{type,jdbcType=DECIMAL}
    </if>
    <if test="status != null">
      and STATUS = #{status,jdbcType=DECIMAL}
    </if>
    <if test="reason != null">
      and REASON = #{reason,jdbcType=VARCHAR}
    </if>
    <if test="memo != null">
      and MEMO = #{memo,jdbcType=VARCHAR}
    </if>
    <if test="createTime != null">
      and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    </if>
    <if test="updateTime != null">
      and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    </if>
    </where>
  </select>
</mapper>