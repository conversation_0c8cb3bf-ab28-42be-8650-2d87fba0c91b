<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.goods.mapper.ProductStoreSpuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.goods.model.ProductStoreSpu">
        <id column="id" property="id" />
        <result column="c_store_id" property="cStoreId" />
        <result column="qty" property="qty" />
        <result column="sales_num" property="salesNum" />
        <result column="product_id" property="productId" />
        <result column="name" property="name" />
        <result column="value" property="value" />
        <result column="brand_id" property="brandId" />
        <result column="brand" property="brand" />
        <result column="year" property="year" />
        <result column="big_season_id" property="bigSeasonId" />
        <result column="big_season" property="bigSeason" />
        <result column="small_season_id" property="smallSeasonId" />
        <result column="small_season" property="smallSeason" />
        <result column="style" property="style" />
        <result column="band" property="band" />
        <result column="big_class_id" property="bigClassId" />
        <result column="big_class" property="bigClass" />
        <result column="small_class_id" property="smallClassId" />
        <result column="small_class" property="smallClass" />
        <result column="element" property="element" />
        <result column="price" property="price" />
        <result column="band_id" property="bandId" />
        <result column="c_arcbrand_id" property="cArcbrandId" />
        <result column="cover_imgs" property="coverImgs" />
        <result column="detail_imgs" property="detailImgs" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, c_store_id, qty, sales_num, product_id, name, value, brand_id, brand, year, big_season_id, big_season, small_season_id, small_season, style, band, big_class_id, big_class, small_class_id, small_class, element, price, band_id, c_arcbrand_id, cover_imgs, detail_imgs
    </sql>

</mapper>