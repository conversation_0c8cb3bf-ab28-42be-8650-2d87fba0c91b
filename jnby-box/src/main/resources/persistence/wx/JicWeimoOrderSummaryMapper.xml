<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.wx.mapper.JicWeimoOrderSummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.wx.model.JicWeimoOrderSummary">
        <id column="ID" property="id" />
        <result column="ORDER_NO" property="orderNo" />
        <result column="LABEL_TYPE" property="labelType" />
        <result column="ORDER_TIME" property="orderTime" />
        <result column="PAY_STATUS" property="payStatus" />
        <result column="ORDER_STATUS" property="orderStatus" />
        <result column="PRICE_LIST" property="priceList" />
        <result column="QTY" property="qty" />
        <result column="SPU_NAME" property="spuName" />
        <result column="SPU_CODE" property="spuCode" />
        <result column="SKU_CODE" property="skuCode" />
        <result column="SKU_NAME" property="skuName" />
        <result column="PAYMENT_AMT" property="paymentAmt" />
        <result column="PRICE_ACTUAL" property="priceActual" />
        <result column="CARDNO" property="cardno" />
        <result column="PHONE" property="phone" />
        <result column="STORE_CODE" property="storeCode" />
        <result column="STORE_NAME" property="storeName" />
        <result column="CLERK_CODE" property="clerkCode" />
        <result column="CLERK_NAME" property="clerkName" />
        <result column="CLERK_PHONE" property="clerkPhone" />
        <result column="LABEL_ID" property="labelId" />
        <result column="ORDER_ITEMID" property="orderItemid" />
        <result column="PAYMENT_TIME" property="paymentTime" />
        <result column="LABEL_TYPE_NAME" property="labelTypeName" />
        <result column="ORDER_STATUS_NAME" property="orderStatusName" />
        <result column="BRANDID" property="brandid" />
        <result column="PROVINCE_NAME" property="provinceName" />
        <result column="CITY_NAME" property="cityName" />
        <result column="DISTRICT_NAME" property="districtName" />
        <result column="RECEIVER_NAME" property="receiverName" />
        <result column="RECEIVER_PHONE" property="receiverPhone" />
        <result column="RECEIVER_ADDRESS" property="receiverAddress" />
        <result column="PAY_STATUS_NAME" property="payStatusName" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="PRICE_ACTUAL_LIST" property="priceActualList" />
        <result column="OUTER_ORDER_NO" property="outerOrderNo" />
        <result column="PAYMENT_TRADE_NO" property="paymentTradeNo" />
        <result column="MERCHANT_ID" property="merchantId" />
        <result column="WID" property="wid" />
        <result column="VID" property="vid" />
        <result column="VID_NAME" property="vidName" />
        <result column="BIZ_SOURCE_TYPE" property="bizSourceType" />
        <result column="BIZ_SOURCE_TYPE_NAME" property="bizSourceTypeName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, ORDER_NO, LABEL_TYPE, ORDER_TIME, PAY_STATUS, ORDER_STATUS, PRICE_LIST, QTY, SPU_NAME, SPU_CODE, SKU_CODE, SKU_NAME, PAYMENT_AMT, PRICE_ACTUAL, CARDNO, PHONE, STORE_CODE, STORE_NAME, CLERK_CODE, CLERK_NAME, CLERK_PHONE, LABEL_ID, ORDER_ITEMID, PAYMENT_TIME, LABEL_TYPE_NAME, ORDER_STATUS_NAME, BRANDID, PROVINCE_NAME, CITY_NAME, DISTRICT_NAME, RECEIVER_NAME, RECEIVER_PHONE, RECEIVER_ADDRESS, PAY_STATUS_NAME, CREATE_TIME, PRICE_ACTUAL_LIST, OUTER_ORDER_NO, PAYMENT_TRADE_NO, MERCHANT_ID, WID, VID, VID_NAME, BIZ_SOURCE_TYPE, BIZ_SOURCE_TYPE_NAME
    </sql>

</mapper>