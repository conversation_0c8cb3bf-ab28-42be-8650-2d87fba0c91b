<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.wx.mapper.JicWxCpEmpMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.wx.model.JicWxCpEmp">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="EMP_ID" jdbcType="DECIMAL" property="empId" />
    <result column="QR_CODE" jdbcType="VARCHAR" property="qrCode" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="INSERT_DATE" jdbcType="TIMESTAMP" property="insertDate" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="AVATAR" jdbcType="VARCHAR" property="avatar" />
    <result column="THUMB_AVATAR" jdbcType="VARCHAR" property="thumbAvatar" />
    <result column="DEPT_ID" jdbcType="DECIMAL" property="deptId" />
    <result column="SEX" jdbcType="VARCHAR" property="sex" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, EMP_ID, QR_CODE, "NAME", MOBILE, INSERT_DATE, "STATUS", AVATAR, THUMB_AVATAR, 
    DEPT_ID, SEX
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from JIC_WX_CP_EMP
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <select id="selectByEmpId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from JIC_WX_CP_EMP
    where EMP_ID  =  #{empId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal">
    delete from JIC_WX_CP_EMP
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.jnby.infrastructure.wx.model.JicWxCpEmp">
    insert into JIC_WX_CP_EMP (ID, EMP_ID, QR_CODE, 
      "NAME", MOBILE, INSERT_DATE, 
      "STATUS", AVATAR, THUMB_AVATAR, 
      DEPT_ID, SEX)
    values (#{id,jdbcType=DECIMAL}, #{empId,jdbcType=DECIMAL}, #{qrCode,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{insertDate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=DECIMAL}, #{avatar,jdbcType=VARCHAR}, #{thumbAvatar,jdbcType=VARCHAR}, 
      #{deptId,jdbcType=DECIMAL}, #{sex,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jnby.infrastructure.wx.model.JicWxCpEmp">
    insert into JIC_WX_CP_EMP
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="empId != null">
        EMP_ID,
      </if>
      <if test="qrCode != null">
        QR_CODE,
      </if>
      <if test="name != null">
        "NAME",
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="insertDate != null">
        INSERT_DATE,
      </if>
      <if test="status != null">
        "STATUS",
      </if>
      <if test="avatar != null">
        AVATAR,
      </if>
      <if test="thumbAvatar != null">
        THUMB_AVATAR,
      </if>
      <if test="deptId != null">
        DEPT_ID,
      </if>
      <if test="sex != null">
        SEX,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=DECIMAL},
      </if>
      <if test="qrCode != null">
        #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="insertDate != null">
        #{insertDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="avatar != null">
        #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="thumbAvatar != null">
        #{thumbAvatar,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=DECIMAL},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.wx.model.JicWxCpEmp">
    update JIC_WX_CP_EMP
    <set>
      <if test="empId != null">
        EMP_ID = #{empId,jdbcType=DECIMAL},
      </if>
      <if test="qrCode != null">
        QR_CODE = #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "NAME" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="insertDate != null">
        INSERT_DATE = #{insertDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        "STATUS" = #{status,jdbcType=DECIMAL},
      </if>
      <if test="avatar != null">
        AVATAR = #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="thumbAvatar != null">
        THUMB_AVATAR = #{thumbAvatar,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        DEPT_ID = #{deptId,jdbcType=DECIMAL},
      </if>
      <if test="sex != null">
        SEX = #{sex,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jnby.infrastructure.wx.model.JicWxCpEmp">
    update JIC_WX_CP_EMP
    set EMP_ID = #{empId,jdbcType=DECIMAL},
      QR_CODE = #{qrCode,jdbcType=VARCHAR},
      "NAME" = #{name,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      INSERT_DATE = #{insertDate,jdbcType=TIMESTAMP},
      "STATUS" = #{status,jdbcType=DECIMAL},
      AVATAR = #{avatar,jdbcType=VARCHAR},
      THUMB_AVATAR = #{thumbAvatar,jdbcType=VARCHAR},
      DEPT_ID = #{deptId,jdbcType=DECIMAL},
      SEX = #{sex,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>