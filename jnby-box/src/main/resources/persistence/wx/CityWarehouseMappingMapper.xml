<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.wx.mapper.CityWarehouseMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.wx.model.CityWarehouseMapping">
        <id column="ID" property="id" />
        <result column="IS_DELETE" property="isDelete" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="C_STORE_ID" property="cStoreId" />
        <result column="C_STORE_CODE" property="cStoreCode" />
        <result column="C_STORE_NAME" property="cStoreName" />
        <result column="MAPPING_TYPE" property="mappingType" />
        <result column="CITY_STORE_ID" property="cityStoreId" />
        <result column="CITY_STORE_CODE" property="cityStoreCode" />
        <result column="CITY_STORE_NAME" property="cityStoreName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, IS_DELETE, CREATE_TIME, UPDATE_TIME, C_STORE_ID, C_STORE_CODE, C_STORE_NAME, MAPPING_TYPE, CITY_STORE_ID, CITY_STORE_CODE, CITY_STORE_NAME
    </sql>

</mapper>