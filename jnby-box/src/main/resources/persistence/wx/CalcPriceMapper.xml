<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.wx.mapper.CalcPriceMapper">

  <select id="calc" parameterType="Map" statementType="CALLABLE">
    {call custpop.calc(
    #{unionid,jdbcType=VARCHAR,mode=IN},
    #{params,jdbcType=VARCHAR,mode=IN},
    #{vouchers,jdbcType=VARCHAR,mode=IN},
    #{inputdate,jdbcType=VARCHAR,mode=IN},
    #{billno,jdbcType=VARCHAR,mode=INOUT},
    #{goods,jdbcType=INTEGER,mode=IN},
    #{ret,jdbcType=INTEGER,mode=OUT},
    #{msg,jdbcType=VARCHAR,mode=OUT},
    #{outgoodsjson,jdbcType=VARCHAR,mode=OUT},
    #{outdiscountjson,jdbcType=VARCHAR,mode=OUT},
    #{outvoucherlist,jdbcType=VARCHAR,mode=OUT},
    #{integral,jdbcType=VARCHAR,mode=OUT}
    )}
  </select>
</mapper>
