<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.MRetailpayitemMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.bojun.model.MRetailpayitem">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="adClientId" column="AD_CLIENT_ID" jdbcType="DECIMAL"/>
            <result property="adOrgId" column="AD_ORG_ID" jdbcType="DECIMAL"/>
            <result property="isactive" column="ISACTIVE" jdbcType="CHAR"/>
            <result property="creationdate" column="CREATIONDATE" jdbcType="TIMESTAMP"/>
            <result property="ownerid" column="OWNERID" jdbcType="DECIMAL"/>
            <result property="modifieddate" column="MODIFIEDDATE" jdbcType="TIMESTAMP"/>
            <result property="modifierid" column="MODIFIERID" jdbcType="DECIMAL"/>
            <result property="mRetailId" column="M_RETAIL_ID" jdbcType="DECIMAL"/>
            <result property="cPaywayId" column="C_PAYWAY_ID" jdbcType="DECIMAL"/>
            <result property="payamount" column="PAYAMOUNT" jdbcType="DECIMAL"/>
            <result property="description" column="DESCRIPTION" jdbcType="VARCHAR"/>
            <result property="markbaltype" column="MARKBALTYPE" jdbcType="DECIMAL"/>
            <result property="paydate" column="PAYDATE" jdbcType="DECIMAL"/>
            <result property="oldTrace" column="OLD_TRACE" jdbcType="VARCHAR"/>
            <result property="oldAuthcode" column="OLD_AUTHCODE" jdbcType="VARCHAR"/>
            <result property="oldDatetime" column="OLD_DATETIME" jdbcType="TIMESTAMP"/>
            <result property="oldFlag" column="OLD_FLAG" jdbcType="DECIMAL"/>
            <result property="oldRemark" column="OLD_REMARK" jdbcType="VARCHAR"/>
            <result property="rate" column="RATE" jdbcType="DECIMAL"/>
            <result property="totRetamt" column="TOT_RETAMT" jdbcType="DECIMAL"/>
            <result property="ticketno" column="TICKETNO" jdbcType="VARCHAR"/>
            <result property="faceValue" column="FACE_VALUE" jdbcType="DECIMAL"/>
            <result property="discount" column="DISCOUNT" jdbcType="DECIMAL"/>
            <result property="totAmtAcc" column="TOT_AMT_ACC" jdbcType="DECIMAL"/>
            <result property="isVou" column="IS_VOU" jdbcType="CHAR"/>
            <result property="mReceiptsId" column="M_RECEIPTS_ID" jdbcType="DECIMAL"/>
            <result property="distype" column="DISTYPE" jdbcType="VARCHAR"/>
            <result property="disrange" column="DISRANGE" jdbcType="VARCHAR"/>
            <result property="orgMRetailpayitemId" column="ORG_M_RETAILPAYITEM_ID" jdbcType="DECIMAL"/>
            <result property="cCurrencyId" column="C_CURRENCY_ID" jdbcType="DECIMAL"/>
            <result property="basePayamount" column="BASE_PAYAMOUNT" jdbcType="DECIMAL"/>
            <result property="isMartRead" column="IS_MART_READ" jdbcType="DECIMAL"/>
            <result property="posSeq" column="POS_SEQ" jdbcType="VARCHAR"/>
            <result property="sysSeq" column="SYS_SEQ" jdbcType="VARCHAR"/>
            <result property="platformDiscountAmt" column="PLATFORM_DISCOUNT_AMT" jdbcType="VARCHAR"/>
            <result property="bussDiscountAmt" column="BUSS_DISCOUNT_AMT" jdbcType="VARCHAR"/>
            <result property="descriptionPay" column="DESCRIPTION_PAY" jdbcType="VARCHAR"/>
            <result property="cnyPayamount" column="CNY_PAYAMOUNT" jdbcType="DECIMAL"/>
            <result property="realtype" column="REALTYPE" jdbcType="VARCHAR"/>
            <result property="realtypename" column="REALTYPENAME" jdbcType="VARCHAR"/>
            <result property="mobilepayno" column="MOBILEPAYNO" jdbcType="VARCHAR"/>
            <result property="refundno" column="REFUNDNO" jdbcType="VARCHAR"/>
            <result property="paymentype" column="PAYMENTYPE" jdbcType="VARCHAR"/>
            <result property="transactionnumber" column="TRANSACTIONNUMBER" jdbcType="VARCHAR"/>
            <result property="collectionstime" column="COLLECTIONSTIME" jdbcType="VARCHAR"/>
            <result property="posterminalnumber" column="POSTERMINALNUMBER" jdbcType="VARCHAR"/>
            <result property="cardno" column="CARDNO" jdbcType="VARCHAR"/>
            <result property="meorderno" column="MEORDERNO" jdbcType="VARCHAR"/>
            <result property="rrnno" column="RRNNO" jdbcType="VARCHAR"/>
            <result property="payseqid" column="PAYSEQID" jdbcType="VARCHAR"/>
            <result property="cardreqSeq" column="CARDREQ_SEQ" jdbcType="VARCHAR"/>
            <result property="cardend" column="CARDEND" jdbcType="VARCHAR"/>
            <result property="cardnumber" column="CARDNUMBER" jdbcType="VARCHAR"/>
            <result property="yxUniorderid" column="YX_UNIORDERID" jdbcType="VARCHAR"/>
            <result property="yxJson" column="YX_JSON" jdbcType="VARCHAR"/>
            <result property="retrievalrefnum" column="RETRIEVALREFNUM" jdbcType="VARCHAR"/>
            <result property="merchantnumber" column="MERCHANTNUMBER" jdbcType="VARCHAR"/>
            <result property="postracenumber" column="POSTRACENUMBER" jdbcType="VARCHAR"/>
            <result property="yikecode" column="YIKECODE" jdbcType="VARCHAR"/>
            <result property="platformNo" column="PLATFORM_NO" jdbcType="VARCHAR"/>
            <result property="tradeNo" column="TRADE_NO" jdbcType="VARCHAR"/>
            <result property="untransactionnumber" column="UNTRANSACTIONNUMBER" jdbcType="VARCHAR"/>
            <result property="yxThirdpartybuyerid" column="YX_THIRDPARTYBUYERID" jdbcType="VARCHAR"/>
            <result property="sqbYhamt" column="SQB_YHAMT" jdbcType="DECIMAL"/>
            <result property="sqbShamt" column="SQB_SHAMT" jdbcType="DECIMAL"/>
            <result property="outRefundNo" column="OUT_REFUND_NO" jdbcType="VARCHAR"/>
            <result property="merchantcode" column="MERCHANTCODE" jdbcType="VARCHAR"/>
            <result property="terminalcode" column="TERMINALCODE" jdbcType="VARCHAR"/>
            <result property="payseqno" column="PAYSEQNO" jdbcType="VARCHAR"/>
            <result property="sqbAmtDiscount" column="SQB_AMT_DISCOUNT" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,AD_CLIENT_ID,AD_ORG_ID,
        ISACTIVE,CREATIONDATE,OWNERID,
        MODIFIEDDATE,MODIFIERID,M_RETAIL_ID,
        C_PAYWAY_ID,PAYAMOUNT,DESCRIPTION,
        MARKBALTYPE,PAYDATE,OLD_TRACE,
        OLD_AUTHCODE,OLD_DATETIME,OLD_FLAG,
        OLD_REMARK,RATE,TOT_RETAMT,
        TICKETNO,FACE_VALUE,DISCOUNT,
        TOT_AMT_ACC,IS_VOU,M_RECEIPTS_ID,
        DISTYPE,DISRANGE,ORG_M_RETAILPAYITEM_ID,
        C_CURRENCY_ID,BASE_PAYAMOUNT,IS_MART_READ,
        POS_SEQ,SYS_SEQ,PLATFORM_DISCOUNT_AMT,
        BUSS_DISCOUNT_AMT,DESCRIPTION_PAY,CNY_PAYAMOUNT,
        REALTYPE,REALTYPENAME,MOBILEPAYNO,
        REFUNDNO,PAYMENTYPE,TRANSACTIONNUMBER,
        COLLECTIONSTIME,POSTERMINALNUMBER,CARDNO,
        MEORDERNO,RRNNO,PAYSEQID,
        CARDREQ_SEQ,CARDEND,CARDNUMBER,
        YX_UNIORDERID,YX_JSON,RETRIEVALREFNUM,
        MERCHANTNUMBER,POSTRACENUMBER,YIKECODE,
        PLATFORM_NO,TRADE_NO,UNTRANSACTIONNUMBER,
        YX_THIRDPARTYBUYERID,SQB_YHAMT,SQB_SHAMT,
        OUT_REFUND_NO,MERCHANTCODE,TERMINALCODE,
        PAYSEQNO,SQB_AMT_DISCOUNT
    </sql>
    <select id="selectSumPayamountByRetailIds" resultMap="BaseResultMap">
        select M_RETAIL_ID ,sum(PAYAMOUNT)  as PAYAMOUNT,c_payway_id
        from m_retailpayitem where m_retail_id in
         <foreach collection="list" open="(" close=")" item="item" separator=",">
             #{item}
         </foreach>
         and c_payway_id IN (62,226)
         group by M_RETAIL_ID,c_payway_id
    </select>
    <select id="selectSumPayamountByRetailIdsForPos"  resultMap="BaseResultMap">
        select M_RETAIL_ID ,sum(PAYAMOUNT)  as PAYAMOUNT
        from m_retailpayitem where m_retail_id in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and c_payway_id=62
        group by M_RETAIL_ID

    </select>
</mapper>
