<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.FaStorageMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.bojun.model.FaStorage">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="AD_CLIENT_ID" jdbcType="DECIMAL" property="adClientId" />
    <result column="AD_ORG_ID" jdbcType="DECIMAL" property="adOrgId" />
    <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
    <result column="MODIFIERID" jdbcType="DECIMAL" property="modifierid" />
    <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
    <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
    <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
    <result column="C_STORE_ID" jdbcType="DECIMAL" property="cStoreId" />
    <result column="M_PRODUCT_ID" jdbcType="DECIMAL" property="mProductId" />
    <result column="M_ATTRIBUTESETINSTANCE_ID" jdbcType="DECIMAL" property="mAttributesetinstanceId" />
    <result column="QTY" jdbcType="DECIMAL" property="qty" />
    <result column="QTYPREOUT" jdbcType="DECIMAL" property="qtypreout" />
    <result column="QTYPREIN" jdbcType="DECIMAL" property="qtyprein" />
    <result column="QTYVALID" jdbcType="DECIMAL" property="qtyvalid" />
    <result column="M_PRODUCTALIAS_ID" jdbcType="DECIMAL" property="mProductaliasId" />
    <result column="QTY_OMS" jdbcType="DECIMAL" property="qtyOms" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, AD_CLIENT_ID, AD_ORG_ID, OWNERID, MODIFIERID, CREATIONDATE, MODIFIEDDATE, ISACTIVE,
    C_STORE_ID, M_PRODUCT_ID, M_ATTRIBUTESETINSTANCE_ID, QTY, QTYPREOUT, QTYPREIN, QTYVALID,
    M_PRODUCTALIAS_ID, QTY_OMS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from FA_STORAGE
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from FA_STORAGE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.jnby.infrastructure.bojun.model.FaStorage">
    insert into FA_STORAGE (ID, AD_CLIENT_ID, AD_ORG_ID,
      OWNERID, MODIFIERID, CREATIONDATE,
      MODIFIEDDATE, ISACTIVE, C_STORE_ID,
      M_PRODUCT_ID, M_ATTRIBUTESETINSTANCE_ID, QTY,
      QTYPREOUT, QTYPREIN, QTYVALID,
      M_PRODUCTALIAS_ID, QTY_OMS)
    values (#{id,jdbcType=DECIMAL}, #{adClientId,jdbcType=DECIMAL}, #{adOrgId,jdbcType=DECIMAL},
      #{ownerid,jdbcType=DECIMAL}, #{modifierid,jdbcType=DECIMAL}, #{creationdate,jdbcType=TIMESTAMP},
      #{modifieddate,jdbcType=TIMESTAMP}, #{isactive,jdbcType=CHAR}, #{cStoreId,jdbcType=DECIMAL},
      #{mProductId,jdbcType=DECIMAL}, #{mAttributesetinstanceId,jdbcType=DECIMAL}, #{qty,jdbcType=DECIMAL},
      #{qtypreout,jdbcType=DECIMAL}, #{qtyprein,jdbcType=DECIMAL}, #{qtyvalid,jdbcType=DECIMAL},
      #{mProductaliasId,jdbcType=DECIMAL}, #{qtyOms,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.jnby.infrastructure.bojun.model.FaStorage">
    insert into FA_STORAGE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="adClientId != null">
        AD_CLIENT_ID,
      </if>
      <if test="adOrgId != null">
        AD_ORG_ID,
      </if>
      <if test="ownerid != null">
        OWNERID,
      </if>
      <if test="modifierid != null">
        MODIFIERID,
      </if>
      <if test="creationdate != null">
        CREATIONDATE,
      </if>
      <if test="modifieddate != null">
        MODIFIEDDATE,
      </if>
      <if test="isactive != null">
        ISACTIVE,
      </if>
      <if test="cStoreId != null">
        C_STORE_ID,
      </if>
      <if test="mProductId != null">
        M_PRODUCT_ID,
      </if>
      <if test="mAttributesetinstanceId != null">
        M_ATTRIBUTESETINSTANCE_ID,
      </if>
      <if test="qty != null">
        QTY,
      </if>
      <if test="qtypreout != null">
        QTYPREOUT,
      </if>
      <if test="qtyprein != null">
        QTYPREIN,
      </if>
      <if test="qtyvalid != null">
        QTYVALID,
      </if>
      <if test="mProductaliasId != null">
        M_PRODUCTALIAS_ID,
      </if>
      <if test="qtyOms != null">
        QTY_OMS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="adClientId != null">
        #{adClientId,jdbcType=DECIMAL},
      </if>
      <if test="adOrgId != null">
        #{adOrgId,jdbcType=DECIMAL},
      </if>
      <if test="ownerid != null">
        #{ownerid,jdbcType=DECIMAL},
      </if>
      <if test="modifierid != null">
        #{modifierid,jdbcType=DECIMAL},
      </if>
      <if test="creationdate != null">
        #{creationdate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifieddate != null">
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="isactive != null">
        #{isactive,jdbcType=CHAR},
      </if>
      <if test="cStoreId != null">
        #{cStoreId,jdbcType=DECIMAL},
      </if>
      <if test="mProductId != null">
        #{mProductId,jdbcType=DECIMAL},
      </if>
      <if test="mAttributesetinstanceId != null">
        #{mAttributesetinstanceId,jdbcType=DECIMAL},
      </if>
      <if test="qty != null">
        #{qty,jdbcType=DECIMAL},
      </if>
      <if test="qtypreout != null">
        #{qtypreout,jdbcType=DECIMAL},
      </if>
      <if test="qtyprein != null">
        #{qtyprein,jdbcType=DECIMAL},
      </if>
      <if test="qtyvalid != null">
        #{qtyvalid,jdbcType=DECIMAL},
      </if>
      <if test="mProductaliasId != null">
        #{mProductaliasId,jdbcType=DECIMAL},
      </if>
      <if test="qtyOms != null">
        #{qtyOms,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.bojun.model.FaStorage">
    update FA_STORAGE
    <set>
      <if test="adClientId != null">
        AD_CLIENT_ID = #{adClientId,jdbcType=DECIMAL},
      </if>
      <if test="adOrgId != null">
        AD_ORG_ID = #{adOrgId,jdbcType=DECIMAL},
      </if>
      <if test="ownerid != null">
        OWNERID = #{ownerid,jdbcType=DECIMAL},
      </if>
      <if test="modifierid != null">
        MODIFIERID = #{modifierid,jdbcType=DECIMAL},
      </if>
      <if test="creationdate != null">
        CREATIONDATE = #{creationdate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifieddate != null">
        MODIFIEDDATE = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="isactive != null">
        ISACTIVE = #{isactive,jdbcType=CHAR},
      </if>
      <if test="cStoreId != null">
        C_STORE_ID = #{cStoreId,jdbcType=DECIMAL},
      </if>
      <if test="mProductId != null">
        M_PRODUCT_ID = #{mProductId,jdbcType=DECIMAL},
      </if>
      <if test="mAttributesetinstanceId != null">
        M_ATTRIBUTESETINSTANCE_ID = #{mAttributesetinstanceId,jdbcType=DECIMAL},
      </if>
      <if test="qty != null">
        QTY = #{qty,jdbcType=DECIMAL},
      </if>
      <if test="qtypreout != null">
        QTYPREOUT = #{qtypreout,jdbcType=DECIMAL},
      </if>
      <if test="qtyprein != null">
        QTYPREIN = #{qtyprein,jdbcType=DECIMAL},
      </if>
      <if test="qtyvalid != null">
        QTYVALID = #{qtyvalid,jdbcType=DECIMAL},
      </if>
      <if test="mProductaliasId != null">
        M_PRODUCTALIAS_ID = #{mProductaliasId,jdbcType=DECIMAL},
      </if>
      <if test="qtyOms != null">
        QTY_OMS = #{qtyOms,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jnby.infrastructure.bojun.model.FaStorage">
    update FA_STORAGE
    set AD_CLIENT_ID = #{adClientId,jdbcType=DECIMAL},
      AD_ORG_ID = #{adOrgId,jdbcType=DECIMAL},
      OWNERID = #{ownerid,jdbcType=DECIMAL},
      MODIFIERID = #{modifierid,jdbcType=DECIMAL},
      CREATIONDATE = #{creationdate,jdbcType=TIMESTAMP},
      MODIFIEDDATE = #{modifieddate,jdbcType=TIMESTAMP},
      ISACTIVE = #{isactive,jdbcType=CHAR},
      C_STORE_ID = #{cStoreId,jdbcType=DECIMAL},
      M_PRODUCT_ID = #{mProductId,jdbcType=DECIMAL},
      M_ATTRIBUTESETINSTANCE_ID = #{mAttributesetinstanceId,jdbcType=DECIMAL},
      QTY = #{qty,jdbcType=DECIMAL},
      QTYPREOUT = #{qtypreout,jdbcType=DECIMAL},
      QTYPREIN = #{qtyprein,jdbcType=DECIMAL},
      QTYVALID = #{qtyvalid,jdbcType=DECIMAL},
      M_PRODUCTALIAS_ID = #{mProductaliasId,jdbcType=DECIMAL},
      QTY_OMS = #{qtyOms,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <!-- 修改占用库存 -->
  <update id="releaseOccupiedQty">
        update fa_storage
        set qtypreout = #{destQty}
        where id = #{id}
        and qtypreout = #{origQty}
  </update>

  <!-- 查询库存 -->
  <select id="getStorage" resultType="java.lang.Long">
    select nvl(sum(qty - greatest(qtypreout,0)),0) as qty
    from fa_storage
    where c_store_id in
    <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    and M_PRODUCTALIAS_ID = #{mProductaliasId,jdbcType=DECIMAL} AND QTY > 0
  </select>

  <!-- 根据no查询总仓库存 -->
  <select id="getStorageByNo" parameterType="java.lang.String" resultType="java.lang.Long">
    select nvl(sum(qty - greatest(qtypreout,0)),0) as qty
    from v_fa_storage
    where c_store_id in (31066,417608)
    and no = #{no,jdbcType=VARCHAR} AND QTY > 0
  </select>

  <resultMap id="ProductStockSkuResultMap" type="com.jnby.module.product.entity.ProductStockEntity">
    <result column="QTY" jdbcType="DECIMAL" property="qty" />
    <result column="NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="skuid" jdbcType="DECIMAL" property="skuId" />
  </resultMap>
  <select id="getStoreStorageBySku" resultMap="ProductStockSkuResultMap">
    select n.NO, nvl(sum(m.QTY - greatest(m.qtypreout,0)),0) as QTY,n.id as skuid
    from v_fa_storage m, M_PRODUCT_ALIAS n
    where m.m_productalias_id = n.id
      and m.c_store_id = #{storeId,jdbcType=DECIMAL}
      and n.no in
    <foreach collection="skuNo" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    group by n.no,n.id
  </select>


  <resultMap id="ProductStockResultMap" type="com.jnby.module.product.entity.ProductStockEntity">
    <result column="QTY" jdbcType="DECIMAL" property="qty" />
    <result column="SKUID" jdbcType="DECIMAL" property="skuId" />
  </resultMap>
  <select id="getStorageList" resultMap="ProductStockResultMap">
    select nvl(sum(qty - greatest(qtypreout,0)),0) as QTY,m_productalias_id  as SKUID
    from v_fa_storage
    where c_store_id in
    <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    and m_productalias_id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    AND nvl(qty - greatest(qtypreout,0),0) > #{safeStock}
    group by m_productalias_id
  </select>
  <select id="getJNBYGatherStock" resultMap="ProductStockResultMap">
    select     (case
    when sum(QTY-greatest(QTYPREOUT,0)) &lt;=0
    then 0
    else 1 end) as QTY,m_productalias_id  as SKUID
    from v_fa_storage
    where c_store_id in
    <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    and m_productalias_id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    AND QTY > 3
    group by m_productalias_id
    union
    select     (case
    when sum(QTY-greatest(QTYPREOUT,0)) &lt;=0
    then 0
    else 1 end) as QTY,m_productalias_id  as SKUID
    from v_fa_storage
    where c_store_id in (417608,31066,416427,422321)
    and m_productalias_id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    AND QTY > 0
    group by m_productalias_id
  </select>
  <resultMap id="SpuStockResultMap" type="com.jnby.module.product.entity.SpuStockEntity">
    <result column="QTY" jdbcType="DECIMAL" property="qty" />
    <result column="M_PRODUCT_ID" jdbcType="DECIMAL" property="productId" />
  </resultMap>
  <select id="getSpuStorageList" resultMap="SpuStockResultMap">
    select
    M_PRODUCT_ID,
    (case
    when sum(QTY-greatest(QTYPREOUT,0)) &lt;=0
    then 0
    else 1 end) as QTY  from v_FA_STORAGE
    where c_store_id in
    <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
     and M_PRODUCT_ID in
    <foreach collection="productIds" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    group by M_PRODUCT_ID
  </select>

  <resultMap id="SkcStockResultMap" type="com.jnby.module.product.entity.SkcStockEntity">
    <result column="QTY" jdbcType="DECIMAL" property="qty" />
    <result column="M_PRODUCT_ID" jdbcType="DECIMAL" property="productId" />
    <result column="COLORNO" jdbcType="VARCHAR" property="colorno" />
    <result column="NAME" jdbcType="VARCHAR" property="name"/>
  </resultMap>
  <select id="querySkcStorageList" resultMap="SkcStockResultMap">
    select a.M_PRODUCT_ID,b.NAME,b.COLORNO,
    (case
    when sum(a.QTY-greatest(a.QTYPREOUT,0)) &lt;=0
    then 0
    else 1 end) as QTY from v_FA_STORAGE a
    left join box_m_product b
    on a.M_PRODUCT_ID = b.ID and a.M_PRODUCTALIAS_ID = b.CODEID
    where b.ID=#{productId,jdbcType=DECIMAL}
    and a.C_STORE_ID = #{storeId,jdbcType=DECIMAL} AND QTY > 0
    group by a.M_PRODUCT_ID,b.NAME,b.COLORNO
  </select>

  <select id="queryBLJnbypSkcStorageList" resultMap="SkcStockResultMap">
    select a.M_PRODUCT_ID,b.NAME,b.COLORNO,
           (case
              when sum(a.QTY-greatest(a.QTYPREOUT,0)) &lt;=0
                then 0
              else 1 end) as QTY
    from v_FA_STORAGE a
    left join box_m_product b
    on a.M_PRODUCT_ID = b.ID and a.M_PRODUCTALIAS_ID = b.CODEID
    where b.ID=#{productId,jdbcType=DECIMAL}
    and a.C_STORE_ID in
    <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    AND a.QTY > 3
    group by a.M_PRODUCT_ID,b.NAME,b.COLORNO
    union
    select a.M_PRODUCT_ID,b.NAME,b.COLORNO,
    (case
    when sum(a.QTY-greatest(a.QTYPREOUT,0)) &lt;=0
    then 0
    else 1 end) as QTY
    from v_FA_STORAGE a
    left join box_m_product b
    on a.M_PRODUCT_ID = b.ID and a.M_PRODUCTALIAS_ID = b.CODEID
    where b.ID=#{productId,jdbcType=DECIMAL}
    and a.C_STORE_ID in(417608,31066,416427,422321)
    AND a.QTY > 0
    group by a.M_PRODUCT_ID,b.NAME,b.COLORNO
  </select>

  <select id="queryLjBoxSkcStorage" resultMap="SkcStockResultMap">
    select a.M_PRODUCT_ID,b.NAME,b.COLORNO,
    (case
    when sum(a.QTY-greatest(a.QTYPREOUT,0)) &lt;=0
    then 0
    else 1 end) as QTY from v_FA_STORAGE a
    left join box_m_product b
    on a.M_PRODUCT_ID = b.ID and a.M_PRODUCTALIAS_ID = b.CODEID
    where b.ID=#{productId,jdbcType=DECIMAL} and a.C_STORE_ID in (417608) AND QTY > 0
    group by a.M_PRODUCT_ID,b.NAME,b.COLORNO
    UNION
    select a.M_PRODUCT_ID,b.NAME,b.COLORNO,
    (case
    when sum(a.QTY-greatest(a.QTYPREOUT,0)) &lt;=0
    then 0
    else 1 end) as QTY from v_FA_STORAGE a
    left join box_m_product b
    on a.M_PRODUCT_ID = b.ID and a.M_PRODUCTALIAS_ID = b.CODEID
    where b.ID=#{productId,jdbcType=DECIMAL} and a.C_STORE_ID in (31066) AND QTY > 0
    group by a.M_PRODUCT_ID,b.NAME,b.COLORNO
  </select>

  <select id="getEbStorageBySkuIds" resultType="com.jnby.base.entity.EbStorageSkuEntity">
    select m_productalias_id as skuId,nvl(a.QTY,0) as qty
    from
    eb_wxmall_storage a
    where a.M_PRODUCTALIAS_ID in
    <foreach collection="skuIds" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>

  <select id="getBoxEbStorageBySkuIds" resultType="com.jnby.base.entity.EbStorageSkuEntity">
    select m_productalias_id as skuId,nvl(a.QTY,0) as qty
    from
    BOX_EB_WXMALL_STORAGE a
    where a.M_PRODUCTALIAS_ID in
    <foreach collection="skuIds" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>

  <select id="getStockByStoreAndSkuIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from v_fa_storage
    where c_store_id = #{storeId}
    and m_productalias_id in
    <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
      #{skuId}
    </foreach>
  </select>
  <select id="getStorageStoreIds" resultType="java.lang.Long">
    select
    C_STORE_ID
    from v_FA_STORAGE
    where qty - greatest(qtypreout,0) > 0 and c_store_id in
    <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    and M_PRODUCT_ID in
    <foreach collection="productIds" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>
  <resultMap id="VFaStorageMap" type="com.jnby.module.eb.entity.StoreNoStock">
    <result column="c_store_id" jdbcType="DECIMAL" property="cStoreId" />
    <result column="no" jdbcType="VARCHAR" property="sku" />
    <result column="skuid" jdbcType="DECIMAL" property="skuId" />
    <result column="qty" jdbcType="DECIMAL" property="qty" />
  </resultMap>

  <select id="selectHaveStockStore" resultMap="VFaStorageMap">
    select c_store_id,no,m_productalias_id as skuid,(qty-qtypreout) qty
    from v_fa_storage
    where isactive = 'Y'
    and c_store_id in
    <foreach collection="storeList" item="storeId" open="(" close=")" separator=",">
      #{storeId}
    </foreach>
    and no in
    <foreach collection="skuList" item="sku" open="(" close=")" separator=",">
      #{sku}
    </foreach>
    <if test="safetyStock != null">
      and (qty-qtypreout) > #{safetyStock}
    </if>
  </select>
  <select id="getStorageByStoreAndProduct" resultType="com.jnby.infrastructure.bojun.model.ProductStoreQty">
    select M_PRODUCT_ID as productId, sum(qtycan) as qty, c_store_id as storeId
    from v_fa_storage
    where isactive = 'Y'  and
        M_PRODUCT_ID in
    <foreach collection="productIdList" item="productId" open="(" close=")" separator=",">
      #{productId}
    </foreach>
      and c_store_id in
    <foreach collection="storeList" item="storeId" open="(" close=")" separator=",">
      #{storeId}
    </foreach>
    group by M_PRODUCT_ID, c_store_id
  </select>

</mapper>
