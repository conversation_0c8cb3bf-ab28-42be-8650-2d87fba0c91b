<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.MInitemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.bojun.model.MInitem">
        <result column="ID" property="id" />
        <result column="REAL_ID" property="realId" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="ISACTIVE" property="isactive" />
        <result column="CREATIONDATE" property="creationdate" />
        <result column="OWNERID" property="ownerid" />
        <result column="MODIFIEDDATE" property="modifieddate" />
        <result column="MODIFIERID" property="modifierid" />
        <result column="M_IN_ID" property="mInId" />
        <result column="ORDERNO" property="orderno" />
        <result column="M_PRODUCT_ID" property="mProductId" />
        <result column="M_ATTRIBUTESETINSTANCE_ID" property="mAttributesetinstanceId" />
        <result column="QTY" property="qty" />
        <result column="QTYOUT" property="qtyout" />
        <result column="QTYIN" property="qtyin" />
        <result column="QTYDIFF" property="qtydiff" />
        <result column="PRICELIST" property="pricelist" />
        <result column="PRICEACTUAL" property="priceactual" />
        <result column="DISCOUNT" property="discount" />
        <result column="DESCRIPTION" property="description" />
        <result column="TOT_AMT_LIST" property="totAmtList" />
        <result column="TOT_AMT_ACTUAL" property="totAmtActual" />
        <result column="TOT_AMTOUT_LIST" property="totAmtoutList" />
        <result column="TOT_AMTOUT_ACTUAL" property="totAmtoutActual" />
        <result column="TOT_AMTIN_LIST" property="totAmtinList" />
        <result column="TOT_AMTIN_ACTUAL" property="totAmtinActual" />
        <result column="STATUS" property="status" />
        <result column="PRECOST" property="precost" />
        <result column="TOT_AMT_PRECOST" property="totAmtPrecost" />
        <result column="TOT_AMTIN_PRECOST" property="totAmtinPrecost" />
        <result column="M_PRODUCTALIAS_ID" property="mProductaliasId" />
        <result column="BILLTYPE" property="billtype" />
        <result column="REAL_M_IN_ID" property="realMInId" />
        <result column="M_IN_BILLTYPE" property="mInBilltype" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, REAL_ID, AD_CLIENT_ID, AD_ORG_ID, ISACTIVE, CREATIONDATE, OWNERID, MODIFIEDDATE, MODIFIERID, M_IN_ID, ORDERNO, M_PRODUCT_ID, M_ATTRIBUTESETINSTANCE_ID, QTY, QTYOUT, QTYIN, QTYDIFF, PRICELIST, PRICEACTUAL, DISCOUNT, DESCRIPTION, TOT_AMT_LIST, TOT_AMT_ACTUAL, TOT_AMTOUT_LIST, TOT_AMTOUT_ACTUAL, TOT_AMTIN_LIST, TOT_AMTIN_ACTUAL, STATUS, PRECOST, TOT_AMT_PRECOST, TOT_AMTIN_PRECOST, M_PRODUCTALIAS_ID, BILLTYPE, REAL_M_IN_ID, M_IN_BILLTYPE
    </sql>

</mapper>