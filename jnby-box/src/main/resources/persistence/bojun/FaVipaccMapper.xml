<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.FaVipaccMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.bojun.model.FaVipacc">
        <id column="ID" property="id" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="OWNERID" property="ownerid" />
        <result column="MODIFIERID" property="modifierid" />
        <result column="CREATIONDATE" property="creationdate" />
        <result column="MODIFIEDDATE" property="modifieddate" />
        <result column="ISACTIVE" property="isactive" />
        <result column="C_VIP_ID" property="cVipId" />
        <result column="TOT_AMT_ACTUAL" property="totAmtActual" />
        <result column="TOT_AMT_LIST" property="totAmtList" />
        <result column="AVG_DISCOUNT" property="avgDiscount" />
        <result column="TIMES" property="times" />
        <result column="TOTQTY" property="totqty" />
        <result column="AVG_TIME_ACTUAL" property="avgTimeActual" />
        <result column="AVG_QTY_ACTUAL" property="avgQtyActual" />
        <result column="LASTDATE" property="lastdate" />
        <result column="LAST_AMT_ACTUAL" property="lastAmtActual" />
        <result column="AMOUNT" property="amount" />
        <result column="DESCRIPTION" property="description" />
        <result column="INTEGRAL" property="integral" />
        <result column="AMT_ORDER" property="amtOrder" />
        <result column="FIRSTDATE" property="firstdate" />
        <result column="FIRST_AMT_ACTUAL" property="firstAmtActual" />
        <result column="INTEGRAL_RANK" property="integralRank" />
        <result column="INTEGRAL_UP" property="integralUp" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, OWNERID, MODIFIERID, CREATIONDATE, MODIFIEDDATE, ISACTIVE, C_VIP_ID, TOT_AMT_ACTUAL, TOT_AMT_LIST, AVG_DISCOUNT, TIMES, TOTQTY, AVG_TIME_ACTUAL, AVG_QTY_ACTUAL, LASTDATE, LAST_AMT_ACTUAL, AMOUNT, DESCRIPTION, INTEGRAL, AMT_ORDER, FIRSTDATE, FIRST_AMT_ACTUAL, INTEGRAL_RANK, INTEGRAL_UP
    </sql>
    <select id="findDiscountByCVipId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
            from FA_VIPACC
        where C_VIP_ID = #{cVipId}
    </select>


    <select id="selectByVipIdsOrderTotAmtDesc"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from FA_VIPACC
        where ISACTIVE = 'Y' and C_VIP_ID in
        <foreach collection="vipIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by TOT_AMT_ACTUAL DESC
    </select>

</mapper>
