<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.MRetailDisMapper">

    <!-- 调用门店转换  -->
    <select id="boxTransferStore" parameterType="Map" statementType="CALLABLE">
        { #{l_ret,jdbcType=VARCHAR,mode=OUT} = call JNBYCOMM.fgetstoretype(
                #{p_storeid,jdbcType=DECIMAL,mode=IN}
        )}
    </select>

    <!-- 调用商品转换  -->
    <select id="boxTransferProduct" parameterType="Map" statementType="CALLABLE">
        { #{l_ret,jdbcType=VARCHAR,mode=OUT} = call JNBYCOMM.fgetlifecycle(
                #{p_productid,jdbcType=DECIMAL,mode=IN}
         )}
    </select>

    <select id="isExcludeProduct" resultType="java.lang.Integer">
        select count(1) from m_product b
             left join m_dim e
             on b.m_dim1_id = e.ID
        where b.id = #{productId}
          and e.attribname not like '%工服%'
          and e.attribname not like '%样衣%'
          and e.attribname not like '%宣传物料%'
          and e.attribname not like '%道具%'
          and e.attribname not like '%用品%'
    </select>

    <select id="isInLifeCycle" resultType="java.lang.Integer">
        select count(1)
        from c_lifecycle_def t, c_store d, m_product b
        where b.id = #{productId}
          and d.id = #{storeId}
          and t.lifecycle = #{transferProduct}
          and t.c_arcbrand_id = b.c_arcbrand_id
          and t.c_depart_id = d.c_depart_id
          and t.storetype = #{transferStore}
          and (t.qd is null or t.qd = 'BOX')
          and t.isunsale = 'Y' and t.isactive = 'Y'
    </select>

    <select id="isInMRetailDis" resultType="java.lang.Integer">
        select count(1)
            from m_retail_dis d
            left join m_retail_disstoreitem e on d.id = e.m_retail_dis_id
            left join     m_retail_disproductitem f on d.id = f.m_retail_dis_id
        where
            d.datebegin <![CDATA[ <= ]]> to_number(to_char(sysdate, 'yyyymmdd'))
            and d.dateend >= to_number(to_char(sysdate, 'yyyymmdd'))
            and d.status = 2
            and (f.m_product_id = #{productId} or f.m_product_id is null)
            and (e.c_store_id = #{storeId} or e.c_store_id is null)
    </select>

    <select id="getProductStore" parameterType="Map" statementType="CALLABLE">
        { #{lstoreid,jdbcType=NUMERIC,mode=OUT} = call JNBYCOMM.fgetitemtostore(
                #{p_unionstoreid,jdbcType=DECIMAL,mode=IN},
                #{p_product_id,jdbcType=DECIMAL,mode=IN},
                #{p_saleman,jdbcType=DECIMAL,mode=IN}
            )}
    </select>
</mapper>
