<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.DimMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.bojun.model.Dim">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="M_DIMDEF_ID" jdbcType="DECIMAL" property="mDimdefId" />
    <result column="ATTRIBCODE" jdbcType="VARCHAR" property="attribcode" />
    <result column="ATTRIBNAME" jdbcType="VARCHAR" property="attribname" />
    <result column="DIMFLAG" jdbcType="VARCHAR" property="dimflag" />
    <result column="AD_CLIENT_ID" jdbcType="DECIMAL" property="adClientId" />
    <result column="AD_ORG_ID" jdbcType="DECIMAL" property="adOrgId" />
    <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
    <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
    <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
    <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
    <result column="MODIFIERID" jdbcType="DECIMAL" property="modifierid" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="C_CORP_ID" jdbcType="DECIMAL" property="cCorpId" />
    <result column="ORDER_CAP" jdbcType="DECIMAL" property="orderCap" />
    <result column="CCKU_QTY" jdbcType="DECIMAL" property="cckuQty" />
    <result column="ATTRIB_ENAME" jdbcType="VARCHAR" property="attribEname" />
    <result column="M_DIMDL_ID" jdbcType="DECIMAL" property="mDimdlId" />
    <result column="RATE" jdbcType="DECIMAL" property="rate" />
    <result column="IS_TO_BPOS" jdbcType="CHAR" property="isToBpos" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, M_DIMDEF_ID, ATTRIBCODE, ATTRIBNAME, DIMFLAG, AD_CLIENT_ID, AD_ORG_ID, ISACTIVE,
    CREATIONDATE, OWNERID, MODIFIEDDATE, MODIFIERID, DESCRIPTION, C_CORP_ID, ORDER_CAP,
    CCKU_QTY, ATTRIB_ENAME, M_DIMDL_ID, RATE, IS_TO_BPOS
  </sql>

  <!-- 根据色号批量获取颜色 -->
  <select id="selectColorByValue" resultType="com.jnby.infrastructure.bojun.model.MColor">
    select value,name
    from m_color
    where value in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from M_DIM
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <select id="selectListBySelective" parameterType="com.jnby.infrastructure.bojun.model.Dim" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from M_DIM
    where 1=1 and ISACTIVE = 'Y'
    <if test="mDimdefId != null">
     and M_DIMDEF_ID = #{mDimdefId,jdbcType=DECIMAL}
    </if>
    <if test="attribcode != null">
      and ATTRIBCODE = #{attribcode,jdbcType=VARCHAR}
    </if>
    <if test="attribname != null">
      and ATTRIBNAME = #{attribname,jdbcType=VARCHAR}
    </if>
    <if test="dimflag != null">
      and DIMFLAG = #{dimflag,jdbcType=VARCHAR}
    </if>
    <if test="adClientId != null">
      and AD_CLIENT_ID = #{adClientId,jdbcType=DECIMAL}
    </if>
    <if test="adOrgId != null">
      and AD_ORG_ID = #{adOrgId,jdbcType=DECIMAL}
    </if>
    <if test="creationdate != null">
      and CREATIONDATE = #{creationdate,jdbcType=TIMESTAMP}
    </if>
    <if test="ownerid != null">
      and OWNERID = #{ownerid,jdbcType=DECIMAL}
    </if>
    <if test="modifieddate != null">
      and MODIFIEDDATE = #{modifieddate,jdbcType=TIMESTAMP}
    </if>
    <if test="modifierid != null">
      and MODIFIERID = #{modifierid,jdbcType=DECIMAL}
    </if>
    <if test="cCorpId != null">
      and C_CORP_ID = #{cCorpId,jdbcType=DECIMAL}
    </if>
    <if test="orderCap != null">
      and ORDER_CAP = #{orderCap,jdbcType=DECIMAL}
    </if>
    <if test="cckuQty != null">
      and CCKU_QTY = #{cckuQty,jdbcType=DECIMAL}
    </if>
    <if test="attribEname != null">
      and ATTRIB_ENAME = #{attribEname,jdbcType=VARCHAR}
    </if>
    <if test="mDimdlId != null">
      and M_DIMDL_ID = #{mDimdlId,jdbcType=DECIMAL}
    </if>
    <if test="rate != null">
      and RATE = #{rate,jdbcType=DECIMAL}
    </if>
    <if test="isToBpos != null">
      and IS_TO_BPOS = #{isToBpos,jdbcType=CHAR}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from M_DIM
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.jnby.infrastructure.bojun.model.Dim">
    insert into M_DIM (ID, M_DIMDEF_ID, ATTRIBCODE,
      ATTRIBNAME, DIMFLAG, AD_CLIENT_ID,
      AD_ORG_ID, ISACTIVE, CREATIONDATE,
      OWNERID, MODIFIEDDATE, MODIFIERID,
      DESCRIPTION, C_CORP_ID, ORDER_CAP,
      CCKU_QTY, ATTRIB_ENAME, M_DIMDL_ID,
      RATE, IS_TO_BPOS)
    values (#{id,jdbcType=DECIMAL}, #{mDimdefId,jdbcType=DECIMAL}, #{attribcode,jdbcType=VARCHAR},
      #{attribname,jdbcType=VARCHAR}, #{dimflag,jdbcType=VARCHAR}, #{adClientId,jdbcType=DECIMAL},
      #{adOrgId,jdbcType=DECIMAL}, #{isactive,jdbcType=CHAR}, #{creationdate,jdbcType=TIMESTAMP},
      #{ownerid,jdbcType=DECIMAL}, #{modifieddate,jdbcType=TIMESTAMP}, #{modifierid,jdbcType=DECIMAL},
      #{description,jdbcType=VARCHAR}, #{cCorpId,jdbcType=DECIMAL}, #{orderCap,jdbcType=DECIMAL},
      #{cckuQty,jdbcType=DECIMAL}, #{attribEname,jdbcType=VARCHAR}, #{mDimdlId,jdbcType=DECIMAL},
      #{rate,jdbcType=DECIMAL}, #{isToBpos,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jnby.infrastructure.bojun.model.Dim">
    insert into M_DIM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="mDimdefId != null">
        M_DIMDEF_ID,
      </if>
      <if test="attribcode != null">
        ATTRIBCODE,
      </if>
      <if test="attribname != null">
        ATTRIBNAME,
      </if>
      <if test="dimflag != null">
        DIMFLAG,
      </if>
      <if test="adClientId != null">
        AD_CLIENT_ID,
      </if>
      <if test="adOrgId != null">
        AD_ORG_ID,
      </if>
      <if test="isactive != null">
        ISACTIVE,
      </if>
      <if test="creationdate != null">
        CREATIONDATE,
      </if>
      <if test="ownerid != null">
        OWNERID,
      </if>
      <if test="modifieddate != null">
        MODIFIEDDATE,
      </if>
      <if test="modifierid != null">
        MODIFIERID,
      </if>
      <if test="description != null">
        DESCRIPTION,
      </if>
      <if test="cCorpId != null">
        C_CORP_ID,
      </if>
      <if test="orderCap != null">
        ORDER_CAP,
      </if>
      <if test="cckuQty != null">
        CCKU_QTY,
      </if>
      <if test="attribEname != null">
        ATTRIB_ENAME,
      </if>
      <if test="mDimdlId != null">
        M_DIMDL_ID,
      </if>
      <if test="rate != null">
        RATE,
      </if>
      <if test="isToBpos != null">
        IS_TO_BPOS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="mDimdefId != null">
        #{mDimdefId,jdbcType=DECIMAL},
      </if>
      <if test="attribcode != null">
        #{attribcode,jdbcType=VARCHAR},
      </if>
      <if test="attribname != null">
        #{attribname,jdbcType=VARCHAR},
      </if>
      <if test="dimflag != null">
        #{dimflag,jdbcType=VARCHAR},
      </if>
      <if test="adClientId != null">
        #{adClientId,jdbcType=DECIMAL},
      </if>
      <if test="adOrgId != null">
        #{adOrgId,jdbcType=DECIMAL},
      </if>
      <if test="isactive != null">
        #{isactive,jdbcType=CHAR},
      </if>
      <if test="creationdate != null">
        #{creationdate,jdbcType=TIMESTAMP},
      </if>
      <if test="ownerid != null">
        #{ownerid,jdbcType=DECIMAL},
      </if>
      <if test="modifieddate != null">
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifierid != null">
        #{modifierid,jdbcType=DECIMAL},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="cCorpId != null">
        #{cCorpId,jdbcType=DECIMAL},
      </if>
      <if test="orderCap != null">
        #{orderCap,jdbcType=DECIMAL},
      </if>
      <if test="cckuQty != null">
        #{cckuQty,jdbcType=DECIMAL},
      </if>
      <if test="attribEname != null">
        #{attribEname,jdbcType=VARCHAR},
      </if>
      <if test="mDimdlId != null">
        #{mDimdlId,jdbcType=DECIMAL},
      </if>
      <if test="rate != null">
        #{rate,jdbcType=DECIMAL},
      </if>
      <if test="isToBpos != null">
        #{isToBpos,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.bojun.model.Dim">
    update M_DIM
    <set>
      <if test="mDimdefId != null">
        M_DIMDEF_ID = #{mDimdefId,jdbcType=DECIMAL},
      </if>
      <if test="attribcode != null">
        ATTRIBCODE = #{attribcode,jdbcType=VARCHAR},
      </if>
      <if test="attribname != null">
        ATTRIBNAME = #{attribname,jdbcType=VARCHAR},
      </if>
      <if test="dimflag != null">
        DIMFLAG = #{dimflag,jdbcType=VARCHAR},
      </if>
      <if test="adClientId != null">
        AD_CLIENT_ID = #{adClientId,jdbcType=DECIMAL},
      </if>
      <if test="adOrgId != null">
        AD_ORG_ID = #{adOrgId,jdbcType=DECIMAL},
      </if>
      <if test="isactive != null">
        ISACTIVE = #{isactive,jdbcType=CHAR},
      </if>
      <if test="creationdate != null">
        CREATIONDATE = #{creationdate,jdbcType=TIMESTAMP},
      </if>
      <if test="ownerid != null">
        OWNERID = #{ownerid,jdbcType=DECIMAL},
      </if>
      <if test="modifieddate != null">
        MODIFIEDDATE = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifierid != null">
        MODIFIERID = #{modifierid,jdbcType=DECIMAL},
      </if>
      <if test="description != null">
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="cCorpId != null">
        C_CORP_ID = #{cCorpId,jdbcType=DECIMAL},
      </if>
      <if test="orderCap != null">
        ORDER_CAP = #{orderCap,jdbcType=DECIMAL},
      </if>
      <if test="cckuQty != null">
        CCKU_QTY = #{cckuQty,jdbcType=DECIMAL},
      </if>
      <if test="attribEname != null">
        ATTRIB_ENAME = #{attribEname,jdbcType=VARCHAR},
      </if>
      <if test="mDimdlId != null">
        M_DIMDL_ID = #{mDimdlId,jdbcType=DECIMAL},
      </if>
      <if test="rate != null">
        RATE = #{rate,jdbcType=DECIMAL},
      </if>
      <if test="isToBpos != null">
        IS_TO_BPOS = #{isToBpos,jdbcType=CHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jnby.infrastructure.bojun.model.Dim">
    update M_DIM
    set M_DIMDEF_ID = #{mDimdefId,jdbcType=DECIMAL},
      ATTRIBCODE = #{attribcode,jdbcType=VARCHAR},
      ATTRIBNAME = #{attribname,jdbcType=VARCHAR},
      DIMFLAG = #{dimflag,jdbcType=VARCHAR},
      AD_CLIENT_ID = #{adClientId,jdbcType=DECIMAL},
      AD_ORG_ID = #{adOrgId,jdbcType=DECIMAL},
      ISACTIVE = #{isactive,jdbcType=CHAR},
      CREATIONDATE = #{creationdate,jdbcType=TIMESTAMP},
      OWNERID = #{ownerid,jdbcType=DECIMAL},
      MODIFIEDDATE = #{modifieddate,jdbcType=TIMESTAMP},
      MODIFIERID = #{modifierid,jdbcType=DECIMAL},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      C_CORP_ID = #{cCorpId,jdbcType=DECIMAL},
      ORDER_CAP = #{orderCap,jdbcType=DECIMAL},
      CCKU_QTY = #{cckuQty,jdbcType=DECIMAL},
      ATTRIB_ENAME = #{attribEname,jdbcType=VARCHAR},
      M_DIMDL_ID = #{mDimdlId,jdbcType=DECIMAL},
      RATE = #{rate,jdbcType=DECIMAL},
      IS_TO_BPOS = #{isToBpos,jdbcType=CHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="getCustomerTopClass" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT name
      FROM (select k.attribname name,count(*) num
      from m_retail a
      inner join m_retailitem b on a.id = b.m_retail_id
      inner join c_client_vip c on a.c_vip_id = c.id
      inner join m_product d on b.M_PRODUCT_ID = d.id
      left join (select * from m_dim where dimflag = 'DIM7') k on k.id =
      d.M_DIM7_ID
      where a.status = 2
      and a.isActive = 'Y'
      and a.is_virtual = 'N'
      and c.unionid = #{unionId}
      and a.tot_amt_actual > 0
      group by k.attribname
      order by num desc)
    where ROWNUM &lt;= 6
  </select>

  <select id="findDimsByTypeAndIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from m_dim
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    <if test="dimFlag != null">
      and dimflag = #{dimFlag,jdbcType=VARCHAR}
    </if>
  </select>


</mapper>
