<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.EbExpressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.bojun.model.EbExpress">
        <id column="ID" property="id" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="OWNERID" property="ownerid" />
        <result column="MODIFIERID" property="modifierid" />
        <result column="CREATIONDATE" property="creationdate" />
        <result column="MODIFIEDDATE" property="modifieddate" />
        <result column="ISACTIVE" property="isactive" />
        <result column="CODE" property="code" />
        <result column="NAME" property="name" />
        <result column="DESCRIPTION" property="description" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, OWNERID, MODIFIERID, CREATIONDATE, MODIFIEDDATE, ISACTIVE, CODE, NAME, DESCRIPTION
    </sql>
    <select id="selectNameById" resultType="java.lang.String">
        select name from eb_express where id =  #{id,jdbcType=DECIMAL}
    </select>
    <select id="selectByPrimaryId" resultMap="BaseResultMap">
        select ID,NAME from eb_express where id = #{id}
    </select>

</mapper>