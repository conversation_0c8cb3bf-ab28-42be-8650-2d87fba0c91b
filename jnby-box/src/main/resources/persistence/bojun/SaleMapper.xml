<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.SaleMapper">
  <resultMap id="BaseResultMap" type="com.jnby.base.entity.SaleInfoEntity">
    <result column="HR_ID" jdbcType="VARCHAR" property="hrId" />
    <result column="BRANDIMGURL" jdbcType="VARCHAR" property="brandImgUrl" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="QR_CODE" jdbcType="VARCHAR" property="qrCode" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionId" />
    <result column="MOBIL" jdbcType="VARCHAR" property="mobil" />
    <result column="C_CUSTOMER_ID" jdbcType="VARCHAR" property="cCustomerId" />
    <result column="C_VIPTYPE_ID" jdbcType="VARCHAR" property="cVipTypeId" />
  </resultMap>
  <sql id="Base_Column_List">
    BRANDIMGURL,DESCRIPTION,QR_CODE,MOBILE,NAME,UNIONID,MOBIL,C_CUSTOMER_ID,C_VIPTYPE_ID,HR_ID
  </sql>

  <select id="selectByParam" parameterType="com.jnby.base.entity.SaleInfoEntity" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from boxcustomerguide
    where  unionid = #{unionId}
     <if test="description != null">
       and description = #{description}
     </if>
    <if test="mobile != null">
       and mobile = #{mobile}
    </if>
    <if test="hrId != null">
      and hr_id = #{hrId}
    </if>
    and rownum = 1
  </select>


  <!-- 获取商品最低折扣限制 -->
  <select id="getAllowDis" parameterType="Map" statementType="CALLABLE">
    {#{result,jdbcType=DECIMAL,mode=OUT}=call fgetAllowDisNew(
            #{p_product_id,jdbcType=DECIMAL,mode=IN},
            #{p_store_id,jdbcType=DECIMAL,mode=IN},
            #{p_vip_id,jdbcType=DECIMAL,mode=IN}
      )}
  </select>

</mapper>
