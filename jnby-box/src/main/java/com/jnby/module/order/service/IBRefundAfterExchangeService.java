package com.jnby.module.order.service;

import com.jnby.infrastructure.box.model.BRefundAfterExchange;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @auther wangchun
 * @create 2023-11-10 10:15:23
 * @describe 服务类
 */
public interface IBRefundAfterExchangeService extends IService<BRefundAfterExchange> {

     /**
      * 同步前一天的换货后进行退货的单据
      */
     void syncRefundAfterExchangeOrder(Integer date);


     /**
      * 标记为已处理
      */
     void processing(String id,String refundId);

}
