package com.jnby.module.order.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.killbill.bus.api.BusEvent;

import java.util.UUID;

/**
 * @<PERSON> <PERSON><PERSON><PERSON>
 * @Date 2022/4/20 3:24 下午
 * @Version 1.0
 */
public class ProducerMqEvent implements BusEvent {

    private byte[] message;
    private String topic;
    private String tags;
    private final Long searchKey1;
    private final Long searchKey2;
    private final UUID userToken;

    @JsonCreator
    public ProducerMqEvent(@JsonProperty("message") final byte[] message,
                           @JsonProperty("topic") final String topic,
                           @JsonProperty("tags") final String tags,
                          @JsonProperty("searchKey1") final Long searchKey1,
                          @JsonProperty("searchKey2") final Long searchKey2,
                          @JsonProperty("userToken") final UUID userToken){
        this.searchKey1 = searchKey1;
        this.searchKey2 = searchKey2;
        this.userToken = userToken;
        this.message = message;
        this.topic = topic;
        this.tags = tags;
    }

    @Override
    public Long getSearchKey1() {
        return searchKey1;
    }

    @Override
    public Long getSearchKey2() {
        return searchKey2;
    }

    @Override
    public UUID getUserToken() {
        return userToken;
    }


    public byte[] getMessage() {
        return message;
    }

    public String getTopic() {
        return topic;
    }

    public String getTags() {
        return tags;
    }

    public void setMessage(byte[] message) {
        this.message = message;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }
}
