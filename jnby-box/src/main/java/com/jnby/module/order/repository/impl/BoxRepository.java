package com.jnby.module.order.repository.impl;
import com.jnby.application.corp.dto.request.WorkListReq;
import com.jnby.base.context.BoxListContext;
import com.jnby.base.entity.BoxCountEntity;
import com.jnby.base.entity.CustomerBoxListEntity;
import com.jnby.base.entity.WorkBoxListEntity;
import com.jnby.common.enums.StatusEnum;
import com.jnby.infrastructure.box.mapper.BoxDetailsMapper;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.jnby.application.admin.dto.request.OrderForSetContext;
import com.jnby.application.admin.dto.response.*;
import com.jnby.base.repository.ICStoreRepository;
import com.jnby.base.repository.ICustomerVipRepository;
import com.jnby.common.CommonConstant;
import com.jnby.common.Page;
import com.jnby.common.enums.UltimaBoxDetailsStatusEnum;
import com.jnby.infrastructure.bojun.mapper.CCustomerMapper;
import com.jnby.infrastructure.bojun.model.CStore;
import com.jnby.infrastructure.bojun.model.CclientVip;
import com.jnby.infrastructure.box.mapper.*;
import com.jnby.infrastructure.box.model.*;
import com.jnby.infrastructure.box.model.BoxDetailsWithBLOBs;
import com.jnby.infrastructure.box.model.BoxWithBLOBs;
import com.jnby.infrastructure.wx.mapper.PromoMallItemMapper;
import com.jnby.module.marketing.styling.repository.IStylingRepository;
import com.jnby.module.order.entity.*;
import com.jnby.module.order.enums.BoxRefundStatusEnum;
import com.jnby.module.order.repository.IBoxRepository;
import com.jnby.infrastructure.box.mapper.BoxMapper;
import com.jnby.module.order.service.box.IBoxDetailsService;
import org.apache.commons.lang3.StringUtils;
import com.jnby.module.order.repository.IBoxSupplyRepository;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2/22/21 9:17 AM
 */
@Slf4j
@Repository
public class BoxRepository implements IBoxRepository {

    @Autowired
    private PromoMallItemMapper promoMallItemMapper;

    @Autowired
    private IBoxSupplyRepository iBoxSupplyRepository;

    @Autowired
    private IStylingRepository iStylingRepository;

    @Autowired
    private CCustomerMapper cCustomerMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private CustomerDetailsMapper customerDetailsMapper;

    @Autowired
    BoxMapper boxMapper;

    @Autowired
    BoxDetailsMapper boxDetailsMapper;

    @Autowired
    private ICustomerVipRepository customerVipRepository;

    @Autowired
    private ICStoreRepository icStoreRepository;

    @Resource
    private ExpressMapper expressMapper;
    @Autowired
    private BoxRefundMapper boxRefundMapper;

    @Autowired
    private BoxRefundDetailsMapper boxRefundDetailsMapper;

    @Autowired
    private OrderDetailMapper orderDetailMapper;


    @Autowired
    private StylingBaseMapper stylingBaseMapper;

    @Resource
    private IBoxDetailsService boxDetailsService;

    @Override
    public BoxWithBLOBs findById(String boxId) {
        return boxMapper.selectByPrimaryKey(boxId);
    }

    @Override
    public BoxEntity findBoxEntity(String boxSn) {
        return boxMapper.selectBoxEntityByBoxSn(boxSn);
    }

    @Override
    public List<BoxDetails> selectListBoxDetailBySkc(UserBoxSkc userBoxSkc) {
        return boxDetailsMapper.selectListBySkc(userBoxSkc);
    }

    @Override
    public List<BoxDetailsWithBLOBs> selectBoxDetailsByIds(List<String> ids) {
        return boxDetailsMapper.selectBoxDetailsByIds(ids);
    }

    @Override
    public void deleteBoxDetailsByBoxId(String boxId) {
        boxDetailsMapper.deleteByBoxId(boxId);
    }

    @Override
    public void batchInsertBoxDetails(List<BoxDetailsWithBLOBs> list) {
        boxDetailsService.saveBatch(list);
    }

    @Override
    public void insertBox(BoxWithBLOBs box) {
        boxMapper.insertSelective(box);
    }

    @Override
    public int updateBoxStatus(String id, long destStatus, long srcStatus,Date tryOutTime) {
        return boxMapper.updateStatus(id, destStatus, srcStatus, tryOutTime);
    }

    @Override
    public int batchUpdateBoxDetails(BoxDetailIds boxDetailIds) {
        return boxDetailsMapper.updateByPrimaryKeysSelective(boxDetailIds);
    }

    @Override
    public List<BoxEntity> findBoxEntityList(BoxListContext context){
        return boxMapper.selectBoxEntity(context);
    }

    @Override
    public List<YsBoxEntity> findYsBoxEntityList(BoxListContext context) {
        return boxMapper.selectYsBoxEntity(context);
    }

    @Override
    public List<OrigTheme> findOrigThemeByIds(List<String> ids) {
        return boxMapper.selectOrigThemeByIds(ids);
    }

    @Override
    public List<String> selectBoxIds(BoxListContext context) {
        return boxMapper.selectBoxIds(context);
    }

    @Override
    public List<BoxEntity> selectBoxEntityByIds(List<String> ids) {
        return boxMapper.selectBoxEntityByIds(ids);
    }

    @Override
    public List<BoxWithBLOBs> findOnGoingBoxByUnionId(String unionId) {
        return boxMapper.findOnGoingBoxByUnionId(unionId);
    }

    @Override
    public int findStylingBoxCount(String unionId) {
        return boxMapper.findStylingBoxCount(unionId);
    }

    @Override
    public int checkFirstBoxByCreate(String unionId) {
        return boxMapper.selectBoxCreateCount(unionId);
    }

    @Override
    public String getMaxSerialNumByDayStr(String dayStr) {
        return boxMapper.getMaxSerialNumByDayStr(dayStr);
    }

    @Override
    public long getCountByBoxPaid(String boxId) {
        return boxDetailsMapper.selectCountByBoxPaid(boxId);
    }

    @Override
    public int updateByPrimaryKeySelective(BoxWithBLOBs record) {
        return boxMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public BoxWithBLOBs selectByBoxSn(String boxSn) {
        return boxMapper.selectByBoxSn(boxSn);
    }

    @Override
    public List<BoxDetailsWithBLOBs> selectDetailsListByBoxId(String boxId) {
        return boxDetailsMapper.selectListByBoxId(boxId);
    }

    @Override
    public BoxDetailsWithBLOBs findDetailById(String id) {
        return boxDetailsMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateDetailById(BoxDetailsWithBLOBs details) {
        return boxDetailsMapper.updateByPrimaryKeySelective(details);
    }

    @Override
    public int batchUpdateDetailById(List<BoxDetails> list) {
        List<BoxDetailsWithBLOBs> updateList = list.stream().map(e -> {
            BoxDetailsWithBLOBs boxDetailsWithBLOBs = new BoxDetailsWithBLOBs();
            BeanUtils.copyProperties(e, boxDetailsWithBLOBs);
            return boxDetailsWithBLOBs;
        }).collect(Collectors.toList());

        return boxDetailsService.updateBatchById(updateList)? 1:0;

    }

    @Override
    public List<BoxWithBLOBs> selectBySelective(BoxWithBLOBs box) {
        return boxMapper.selectBySelective(box);
    }

    @Override
    public List<CustomerBoxListEntity> selectCustomerBox(String unionId) {
        return boxMapper.selectCustomerBox(unionId);
    }

    @Override
    public List<BoxDetailsWithBLOBs> selectDetailByIds(List<String> boxIds) {
        return boxDetailsMapper.selectByIds(boxIds);
    }

    @Override
    public Integer getGenerateCount(String unionid) {
        return boxDetailsMapper.getGenerateCount(unionid);
    }

    @Override
    public Integer getBuyCount(String unionid) {
        return boxDetailsMapper.getBuyCount(unionid);
    }

    @Override
    public List<WorkBoxListEntity> workBoxList(WorkListReq requestData) {
        String status = requestData.getStatus();
        String sql = "";
        if(StringUtils.isNotBlank(status)){
            if("-1".equals(status)){
                sql = " (d.status = 3 or d.status = 4) and DATE(d.update_time) = CURDATE()";
            }else if("-2".equals(status)){
                sql = " (d.status = 3 or d.status = 4) and DATE_FORMAT(d.update_time,'%Y%m') =  DATE_FORMAT(now(),'yyyyMM')";
            }else if("1".equals(status)){
                sql  = " d.status in (1,2)";
            }else{
                sql = " d.status = " + status;
            }
        }
        requestData.setMessageAdd(
                sql
        );
        return boxMapper.workBoxList(requestData);
    }

    @Override
    public List<BoxDetailsWithBLOBs> findBoxDetialByBoxIdAndOtherParams(String boxId) {
        return boxDetailsMapper.findBoxDetialByBoxIdAndOtherParams(boxId);
    }

    @Override
    public List<BoxDetails> selectBoxByUnionIdAndProductColorAndCodeAndStatus(String unionId, Integer status,List<String> productCodes) {
        return boxDetailsMapper.selectBoxByUnionIdAndProductColorAndCodeAndStatus(unionId,status,productCodes);
    }


    @Override
    public List<BoxServiceModel> selectListByBox(OrderForSetContext box) {
        return boxMapper.selectListByBox(box);
    }



    @Override
    public List<BoxDetails> selectForDetail(List<String> ids) {
        //selectForDetail
        return boxDetailsMapper.selectForDetail(ids);
    }

    @Override
    public List<BoxWithBLOBs> selectBoxByUnionId(String unionId) {
        return boxMapper.selectBoxByUnionId(unionId);
    }

    @Override
    public List<BoxWithBLOBs> selectBoxByBoxSnList(List<String> boxSnList) {
        return boxMapper.selectBoxByBoxSnList(boxSnList);
    }

    @Override
    public List<XcxBoxListEntity> getBoxList(XcxBoxListParaEntity paraEntity) {
        return boxMapper.getBoxList(paraEntity);
    }


    @Override
    public BoxMajModelForAdmin getBoxForDetail(String boxId) {
        List<BoxMajModelForAdmin> forBoxDetails = boxMapper.findForBoxDetails(boxId);
        BoxMajModelForAdmin box = forBoxDetails.stream().findFirst().orElse(null);
        if (box == null) {
            return null;
        }
        String unionid = box.getUnionid();
        CustomerDetails customerDetails = new CustomerDetails();
        customerDetails.setUnionid(unionid);
        List<CustomerDetails> customerList = customerDetailsMapper.selectListBySelective(customerDetails);
        CustomerDetails details = customerList.stream().findFirst().orElse(null);
        if (details != null) {
            box.setNickName(details.getNickName());
            box.setMobile(details.getPhone());
            box.setCustomerId(details.getId());
        }
        return box;
    }

    @Override
    public List<BoxDetailsWithBLOBs> getProducts(String boxId) {
        return boxDetailsMapper.getProducts(boxId);

    }


    /**
     * box内商品数量
     */
    @Override
    public OrderDetailsForCount getBoxDetailsCount(String boxId) {
        List<BoxDetailsWithBLOBs> detailsWithBLOBs = boxDetailsMapper.selectListByBoxId(boxId);
        List<BoxDetailsWithBLOBs> aNotDetailCount = detailsWithBLOBs.stream().filter(item -> {
            Long status = item.getStatus();
            Long type = item.getType();
            return status != 0 && type != 30;
        }).collect(Collectors.toList());
        List<BoxDetailsWithBLOBs> buyDetailCount = detailsWithBLOBs.stream().filter(item -> {
            Long status = item.getStatus();
            return status == 3;
        }).collect(Collectors.toList());
        List<BoxDetailsWithBLOBs> retailForCount = detailsWithBLOBs.stream().filter(item -> {
            Long status = item.getStatus();
            return ((status == 2) || (status == 1));
        }).collect(Collectors.toList());
        List<BoxDetailsWithBLOBs> sendForCount = detailsWithBLOBs.stream().filter(item -> {
            Long status = item.getStatus();
            return status == 8;
        }).collect(Collectors.toList());

        List<String> integers = new ArrayList<>();
        //取出成交金额：实付金额——有成功支付的金额
        integers.add(boxId);
        List<BoxDetailsForAdmin> boxForActualMoney = boxDetailsMapper.selectForActualMoney(integers);
        BoxDetailsForAdmin oneBoxDetailMs = boxForActualMoney.stream().findFirst().orElse(null);
        OrderDetailsForCount record = new OrderDetailsForCount();
        if (oneBoxDetailMs!=null){
            String priceActual = oneBoxDetailMs.getPriceActual();
            BigDecimal bigDecimalForOne = new BigDecimal(priceActual);
            record.setActualMoneyCount(bigDecimalForOne);
        }
        record.setANotDetailCount(aNotDetailCount.size());
        record.setBuyDetailCount(buyDetailCount.size());
        record.setRetailForCount(retailForCount.size());
        record.setSendForCount(sendForCount.size());
        return record;
    }

    public List<String> selectNinetyReminder() {
        return boxMapper.selectNinetyDayReminder();
    }


    @Data
    public class SupplyObj{
        @ApiModelProperty(value = "补货原因")
        private String reason;
        @ApiModelProperty(value = "补货单id")
        private String supplyId;
        @ApiModelProperty(value = "补货列表")
        private List<BoxDetailForAdminResp> boxDetailsWithBLOBs;
        @ApiModelProperty(value = "发货物流")
        private String logistics;
    }


    /**
     * 获取商品信息
     * @param boxId
     */
    @Override
    public GetProductInfoByBoxIdResp getProductInfoByBoxId(String boxId) {
        BoxWithBLOBs box = Preconditions.checkNotNull(boxMapper.selectByPrimaryKey(boxId),"服务单不存在");
        List<BoxDetailsWithBLOBs> boxDetailsWithBLOBsList = boxDetailsMapper.selectListByBoxId(boxId).stream().sorted(Comparator.comparing(BoxDetailsWithBLOBs::getProductNo)).collect(Collectors.toList());
        Order searchOrder = new Order();
        searchOrder.setBoxSn(box.getBoxSn());
        searchOrder.setOrderStatus(Long.valueOf(1));
        List<Order> orderList = orderMapper.selectListBySelective(searchOrder);
        List<BoxSupply> boxSupplyList = iBoxSupplyRepository.findByBoxId(boxId);
        boxSupplyList.sort(Comparator.comparing(BoxSupply::getCreateTime));

        // 补货字典表
        Map<String, BoxSupply> supplyMap = new LinkedHashMap<>();
        Multimap<String, BoxDetailsWithBLOBs> multiMap = ArrayListMultimap.create();
        Map<String,Express> expressMap = getExpressMapByBoxId(boxId);

        for (BoxSupply boxSupply : boxSupplyList) {
            supplyMap.put(boxSupply.getId(), boxSupply);
        }

        GetProductInfoByBoxIdResp getProductInfoByBoxIdResp = new GetProductInfoByBoxIdResp();
        int buyCount = 0;
        int backCount = 0;
        int sendCount = 0;
        BigDecimal dealPrice = new BigDecimal(0);

        for (Order order : orderList) {
            dealPrice = dealPrice.add(new BigDecimal(order.getPaidAmount()));
        }

        // 搭盒
        List<BoxDetailsWithBLOBs> createBoxList = new ArrayList<>();
        // 补货
        List<BoxDetailsWithSupplyEntity> supplyRespList = new ArrayList<>();
        // 换货
        List<BoxDetailsWithChangeEntity> changeCodeList = new ArrayList<>();
        // 待发货 删款 取消
        Set<Long> sendSet = new HashSet<>();
        sendSet.add(UltimaBoxDetailsStatusEnum.DELETE.getCode().longValue());
        sendSet.add(UltimaBoxDetailsStatusEnum.CANCEL.getCode().longValue());
        sendSet.add(UltimaBoxDetailsStatusEnum.UNSEND.getCode().longValue());


        for (BoxDetailsWithBLOBs temp : boxDetailsWithBLOBsList) {
            // 补货
            if (StringUtils.isNotEmpty(temp.getSupplyId()) && !temp.getType().equals(Long.valueOf(30))) {
                multiMap.put(temp.getSupplyId(), temp);
            }
            // 搭盒
            if (StringUtils.isEmpty(temp.getSupplyId()) && !Long.valueOf(30).equals(temp.getType())) {
                createBoxList.add(temp);
            }
            // 换货（已购买）
            if (Long.valueOf(30).equals(temp.getType()) && ( Long.valueOf(UltimaBoxDetailsStatusEnum.PAY.getCode()).equals(temp.getStatus())|| Long.valueOf(CommonConstant.STR_ONE).equals(temp.getOrderStatus()))) {
                BoxDetailsWithChangeEntity changeEntity = new BoxDetailsWithChangeEntity();
                BeanUtils.copyProperties(temp, changeEntity);
                Express express = expressMap.get(temp.getExpressId());
                if (temp.getStatus() > 2 && express != null && StringUtils.isNotEmpty(express.getExpressDocno())) {
                    changeEntity.setEbsn(express.getExpressDocno());
                }
                changeCodeList.add(changeEntity);
            }
            // 发货数量 除待发货 删款 取消
            if (!sendSet.contains(temp.getStatus())) {
                sendCount += 1;
            }
            // 退换数量
            if (Long.valueOf(UltimaBoxDetailsStatusEnum.RETURNED.getCode()).equals(temp.getStatus())) {
                backCount += 1;
            }

            if (Long.valueOf(UltimaBoxDetailsStatusEnum.PAY.getCode()).equals(temp.getStatus()) || Long.valueOf(1).equals(temp.getOrderStatus())) {
                buyCount += 1;
            }
        }

        // 补货商品
        Map<String, Collection<BoxDetailsWithBLOBs>> boxDetailsWithSupplyMap = multiMap.asMap();
        // 处理补货 逻辑
        for (String key : supplyMap.keySet()) {
            BoxDetailsWithSupplyEntity temp = new BoxDetailsWithSupplyEntity();
            temp.setBoxSupply(supplyMap.get(key));
            temp.setBoxDetailsWithBLOBsList((List<BoxDetailsWithBLOBs>) boxDetailsWithSupplyMap.get(key));
            supplyRespList.add(temp);
        }

        // 退款个数  退款金额  根据 boxId  获取box   根据  boxsn 获取order  根据 order_id h获取boxRefund
        BoxWithBLOBs box1 = boxMapper.selectByPrimaryKey(boxId);

        List<BoxRefund> boxRefunds = boxRefundMapper.findByBoxSn(box1.getBoxSn());
        List<BoxRefund> collect = new ArrayList<>();

        Map<String, List<BoxRefund>> refundIdsGroupBy = new HashMap<>();
        if(CollectionUtils.isNotEmpty(boxRefunds)){
            collect = boxRefunds.stream().filter(r -> BoxRefundStatusEnum.REFUNDSUCCESS.getCode().intValue() == r.getStatus()).collect(Collectors.toList());
            refundIdsGroupBy = collect.stream().collect(Collectors.groupingBy(r -> r.getId()));
        }
        BigDecimal refundAmount  = new BigDecimal(0);
        int refundCount = 0;
        if(CollectionUtils.isNotEmpty(collect)){
            // 获取退款金额  和 退款个数
            for (BoxRefund boxRefund : collect) {
                refundAmount =  refundAmount.add( new BigDecimal(String.valueOf(boxRefund.getRefundAmount()))) ;
            }

            //
            List<String> refundIds = collect.stream().map(r -> r.getId()).collect(Collectors.toList());
            List<String> orderIds = collect.stream().map(r -> r.getOrderId()).collect(Collectors.toList());

            //所有的退款单详情
            List<BoxRefundDetails> boxRefundDetails = boxRefundDetailsMapper.selectByRefundIds(refundIds);
            Map<String, List<BoxRefundDetails>> groupByOrderDetailsId = new HashMap<>();
            if(CollectionUtils.isNotEmpty(boxRefundDetails)){
                groupByOrderDetailsId = boxRefundDetails.stream().collect(Collectors.groupingBy(r -> r.getOrderDetailId()));
            }

            //查询到所有订单详情
            List<OrderDetail> orderDetails = orderDetailMapper.selectListByOrderIds(orderIds);
            Map<String, List<OrderDetail>> groupByBoxDetalsId = new HashMap<>();
            if(CollectionUtils.isNotEmpty(orderDetails)){
                groupByBoxDetalsId = orderDetails.stream().collect(Collectors.groupingBy(r -> r.getBoxDetailId()));
            }

            //循环遍历
            setBoxDetailWithBlobs(createBoxList,groupByOrderDetailsId,groupByBoxDetalsId,refundIdsGroupBy);
            setChangeBoxDetailWithBlobs(changeCodeList,groupByOrderDetailsId,groupByBoxDetalsId,refundIdsGroupBy);
            for (BoxDetailsWithSupplyEntity boxDetailsWithSupplyEntity : supplyRespList) {
                List<BoxDetailsWithBLOBs> boxDetailsWithBLOBsList1 = boxDetailsWithSupplyEntity.getBoxDetailsWithBLOBsList();
                setBoxDetailWithBlobs(boxDetailsWithBLOBsList1,groupByOrderDetailsId,groupByBoxDetalsId,refundIdsGroupBy);
            }
            refundCount = boxRefundDetails.size();
        }

        getProductInfoByBoxIdResp.setRefundAmount(refundAmount);
        getProductInfoByBoxIdResp.setRefundCount(refundCount);

        getProductInfoByBoxIdResp.setCreateBoxList(createBoxList);
        getProductInfoByBoxIdResp.setChangeCodeList(changeCodeList);
        getProductInfoByBoxIdResp.setSupplyRespList(supplyRespList);

        getProductInfoByBoxIdResp.setAllCount(boxDetailsWithBLOBsList.size());
        getProductInfoByBoxIdResp.setBackCount(backCount);
        getProductInfoByBoxIdResp.setBuyCount(buyCount);
        getProductInfoByBoxIdResp.setSendCount(sendCount);
        getProductInfoByBoxIdResp.setDealPrice(dealPrice);
        return getProductInfoByBoxIdResp;
    }

    private void setChangeBoxDetailWithBlobs(List<BoxDetailsWithChangeEntity> changeCodeList,
                                             Map<String, List<BoxRefundDetails>> groupByOrderDetailsId,
                                             Map<String, List<OrderDetail>> groupByBoxDetalsId,
                                             Map<String, List<BoxRefund>> refundIdsGroupBy) {
        for (BoxDetailsWithBLOBs boxDetailsWithBLOBs : changeCodeList) {
            List<OrderDetail> orderDetailList = groupByBoxDetalsId.get(boxDetailsWithBLOBs.getId());
            if(CollectionUtils.isNotEmpty(orderDetailList)){
                for (OrderDetail orderDetail : orderDetailList) {
                    List<BoxRefundDetails> boxRefundDetails1 = groupByOrderDetailsId.get(orderDetail.getId());
                    if(CollectionUtils.isNotEmpty(boxRefundDetails1)){
                        for (BoxRefundDetails refundDetails : boxRefundDetails1) {
                            // 获取sn
                            List<BoxRefund> refundList = refundIdsGroupBy.get(refundDetails.getBoxRefundId());
                            if(CollectionUtils.isNotEmpty(refundList)){
                                BoxRefund boxRefund = refundList.get(0);
                                boxDetailsWithBLOBs.setBoxRefundRemark(boxRefund.getRefundRemark());
                                boxDetailsWithBLOBs.setBoxRefundSn(boxRefund.getRefundSn());
                            }
                            boxDetailsWithBLOBs.setBoxRefundDetailsStatus(refundDetails.getStatus());
                            boxDetailsWithBLOBs.setBoxRefundId(refundDetails.getBoxRefundId());
                        }
                    }
                }
            }
        }
    }

    private void setBoxDetailWithBlobs(List<BoxDetailsWithBLOBs> boxDetailsWithBLOBsList1,
                                       Map<String, List<BoxRefundDetails>> groupByOrderDetailsId,
                                       Map<String, List<OrderDetail>> groupByBoxDetalsId,
                                       Map<String, List<BoxRefund>> refundIdsGroupBy) {
        for (BoxDetailsWithBLOBs boxDetailsWithBLOBs : boxDetailsWithBLOBsList1) {
            List<OrderDetail> orderDetailList = groupByBoxDetalsId.get(boxDetailsWithBLOBs.getId());
            if(CollectionUtils.isNotEmpty(orderDetailList)){
                for (OrderDetail orderDetail : orderDetailList) {
                    List<BoxRefundDetails> boxRefundDetails1 = groupByOrderDetailsId.get(orderDetail.getId());
                    if(CollectionUtils.isNotEmpty(boxRefundDetails1)){
                        for (BoxRefundDetails refundDetails : boxRefundDetails1) {
                            // 获取sn
                            List<BoxRefund> refundList = refundIdsGroupBy.get(refundDetails.getBoxRefundId());
                            if(CollectionUtils.isNotEmpty(refundList)){
                                BoxRefund boxRefund = refundList.get(0);
                                boxDetailsWithBLOBs.setBoxRefundRemark(boxRefund.getRefundRemark());
                                boxDetailsWithBLOBs.setBoxRefundSn(boxRefund.getRefundSn());
                            }
                            boxDetailsWithBLOBs.setBoxRefundDetailsStatus(refundDetails.getStatus());
                            boxDetailsWithBLOBs.setBoxRefundId(refundDetails.getBoxRefundId());
                        }
                    }
                }
            }
        }
    }


    /**
     * 获取物流
     * @param boxId
     * @return
     */
    public Map<String,Express> getExpressMapByBoxId(String boxId){
        Express condition = new Express();
        condition.setBoxId(boxId);
        List<Express> expressList = expressMapper.selectListBySelective(condition);
        Map<String,Express> map = new HashMap<>();
        for (Express express: expressList){
            map.put(express.getId(),express);
        }
        return map;
    }

    @Override
    public OrderDetailForSort getProductsForSort(String boxId, OrderDetailForSort orderDetailForSort) {
        List<BoxDetailsWithBLOBs> boxDetailsWithBLOBs = boxDetailsMapper.selectListByBoxId(boxId);
        List<String> integers = new ArrayList<>();
        //取出成交金额：实付金额——有成功支付的金额
        integers.add(boxId);
        List<BoxDetailsForAdmin> boxForActualMoney = boxDetailsMapper.selectForActualMoney(integers);
        BoxDetailsForAdmin oneBoxDetailMs = boxForActualMoney.stream().findFirst().orElse(null);
        OrderDetailsForCount record = new OrderDetailsForCount();
        if (oneBoxDetailMs!=null){
            String priceActual = oneBoxDetailMs.getPriceActual();
            BigDecimal bigDecimalForOne = new BigDecimal(priceActual);
            record.setActualMoneyCount(bigDecimalForOne);
        }
        orderDetailForSort.setCount(record);
        if (CollectionUtils.isEmpty(boxDetailsWithBLOBs)){
            return orderDetailForSort;
        }
        //商品搭盒
        List<BoxDetailsWithBLOBs> list = boxDetailsWithBLOBs.stream().filter(item -> {
            Long type = item.getType();
            String supplyId = item.getSupplyId();
            return type != 30 && StringUtils.isBlank(supplyId);
        }).collect(Collectors.toList());
        //补货商品,根据supplyId来进行分组
        //补货商品集合
        List<BoxDetailsWithBLOBs> supplyCount = boxDetailsWithBLOBs.stream().filter(item -> {
            String supplyId = item.getSupplyId();
            return StringUtils.isNotBlank(supplyId);
        }).collect(Collectors.toList());
        Map<String, List<BoxDetailsWithBLOBs>> boxDetailForSupply = boxDetailsWithBLOBs.stream().filter(item -> {
            String supplyId = item.getSupplyId();
            return StringUtils.isNotBlank(supplyId);
        }).collect(Collectors.groupingBy(BoxDetailsWithBLOBs::getSupplyId));
        //换码商品
        List<BoxDetailsWithBLOBs> boxDetailForChange = boxDetailsWithBLOBs.stream().filter(item -> {
            Long type = item.getType();
            return type == 30L;
        }).collect(Collectors.toList());
        BoxWithBLOBs box = findById(boxId);
        orderDetailForSort = makeProList(box, list, orderDetailForSort);
        List<SupplyObj> supplyObjs = new ArrayList<>();
        List<String> strings = new ArrayList<>();
        if (MapUtil.isNotEmpty(boxDetailForSupply)){
            boxDetailForSupply.forEach((key, value) -> {
                List<String> collect = value.stream().map(BoxDetailsWithBLOBs::getBoxId).collect(Collectors.toList());
                strings.addAll(collect);
            });
            HashMap<String, String> stringStringHashMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(strings)){
                List<BoxSupply> byBoxIds = iBoxSupplyRepository.findByBoxIds(strings);
                byBoxIds.forEach(item->{
                    String boxId1 = item.getBoxId();
                    String supplyReason = item.getSupplyReason();
                    stringStringHashMap.put(boxId1,supplyReason);
                });
            }
            //补货发货物流单号
            List<BoxSupply> byBoxId = iBoxSupplyRepository.findByBoxId(boxId);
            boxDetailForSupply.forEach((key, value) -> {
                BoxDetailsWithBLOBs boxDetailsWithBLOBs1 = value.stream().findFirst().orElse(null);
                SupplyObj supplyObj = new SupplyObj();
                supplyObj.setSupplyId(key);
                if (boxDetailsWithBLOBs1!=null){
                    String boxId1 = boxDetailsWithBLOBs1.getBoxId();
                    String reason = stringStringHashMap.get(boxId1);
                    supplyObj.setReason(reason);
                }
                List<BoxDetailForAdminResp> boxDetailForAdminResps = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(value)){
                    value.forEach(yun->{
                        BoxDetailsForAdmin boxDetailsForAdmin = new BoxDetailsForAdmin();
                        BeanUtils.copyProperties(yun,boxDetailsForAdmin);
                        BoxDetailForAdminResp boxDetailForAdminResp = new BoxDetailForAdminResp();
                        BeanUtils.copyProperties(boxDetailsForAdmin,boxDetailForAdminResp);
                        boxDetailForAdminResps.add(boxDetailForAdminResp);
                    });
                }
                if (CollectionUtils.isNotEmpty(byBoxId)){
                    BoxSupply boxSupply = byBoxId.stream().filter(item -> item.getId().equals(key)).findFirst().orElse(null);
                    if (boxSupply!=null){
                        String trackNumber = boxSupply.getTrackNumber();
                        supplyObj.setLogistics(trackNumber);
                    }
                }
                supplyObj.setBoxDetailsWithBLOBs(boxDetailForAdminResps);
                supplyObjs.add(supplyObj);
            });
        }
        //商品总数：搭盒、补货、换码产生的商品总数
        Integer productCount=list.size()+supplyCount.size()+boxDetailForChange.size();
        record.setANotDetailCount(productCount);
        //不为已删款,12;已取消,7则为发货总数
        List<BoxDetailsWithBLOBs> sendProductAmount = new ArrayList<>();
        List<BoxDetailsWithBLOBs> buyProductAmount = new ArrayList<>();
        List<BoxDetailsWithBLOBs> retailProductAmount = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(boxDetailsWithBLOBs)){
            boxDetailsWithBLOBs.forEach(item->{
                Long status = item.getStatus();
                if (status!=12L&&status!=7L){
                    //发货
                    sendProductAmount.add(item);
                    if (status==3L){
                        //购买
                        buyProductAmount.add(item);
                    }else if (status==2L){
                        //已完成
                        retailProductAmount.add(item);
                    }
                }
            });
        }
        //发货总数
        record.setSendForCount(sendProductAmount.size());
        //购买总数：被客户购买的商品——销售状态为已购买3,的商品数
        record.setBuyDetailCount(buyProductAmount.size());
        //还货总数：商品总数中，被还回来的商品数——商品状态为已完成且销售状态为未购买的商品数
        //已完成即为已还货,2
        record.setRetailForCount(retailProductAmount.size());
        //商品补货信息
        orderDetailForSort.setBoxDetailForSupply(supplyObjs);
        if (CollectionUtils.isNotEmpty(boxDetailForChange)){
            List<BoxDetailsForAdmin> boxDetailsForAdmins = new ArrayList<>();
            BeanUtils.copyProperties(boxDetailForChange,boxDetailsForAdmins);
            List<BoxDetailForAdminResp> boxDetailForAdminResps = new ArrayList<>();
            BeanUtils.copyProperties(boxDetailsForAdmins,boxDetailForAdminResps);
            orderDetailForSort.setBoxDetailForChange(boxDetailForAdminResps);
        }

        return orderDetailForSort;
    }

    @Override
    public String getEBSN(String detailsId) {
        String serviceSn = orderMapper.getEBSN(detailsId);
        if (StringUtils.isNotBlank(serviceSn)) {
            String ebsn = cCustomerMapper.bjsql(serviceSn);
            if (StringUtils.isNotBlank(ebsn)) {
                return ebsn;
            }
        }
        return null;
    }


    @Override
    public OrderDetailForSort makeProList(BoxWithBLOBs box, List<BoxDetailsWithBLOBs> list, OrderDetailForSort orderDetailForSort) {
        List<BoxDetailForAdminResp> proList = new ArrayList<>();
        for (BoxDetailsWithBLOBs r : list) {
            BoxDetailsForAdmin boxDetailsForAdmin = new BoxDetailsForAdmin();
            BeanUtils.copyProperties(r,boxDetailsForAdmin);
            if (r.getType() == 30 && r.getStatus() > 2) {
                String ebsn = getEBSN(r.getId());
                boxDetailsForAdmin.setEbsn(ebsn);
            }
            BoxDetailForAdminResp boxDetailForAdminResp = new BoxDetailForAdminResp();
            BeanUtils.copyProperties(boxDetailsForAdmin,boxDetailForAdminResp);
            proList.add(boxDetailForAdminResp);
        }

        orderDetailForSort.setOrderFaStorage(proList);
        return orderDetailForSort;
    }


    @Override
    public List<StylingBaseForOrder> selectForStylingDetails(BoxWithBLOBs byId) {
        String styleOutNo = Optional.ofNullable(byId.getExtend())
                .map(JSON::parseObject).map(item -> item.getString("styleOutNo")).orElse(null);

        List<StylingBaseForOrder> listAdds = new ArrayList<>();

        //查询搭配列表
        if (StringUtils.isBlank(styleOutNo)) {
            return listAdds;
        }
        List<StylingBaseWithBLOBs> stylingBaseDetails = iStylingRepository.selectForStylingByOutNo(styleOutNo);

        StylingBaseWithBLOBs stylingBaseWithBLOBs = new StylingBaseWithBLOBs();
        stylingBaseWithBLOBs.setOutNo(styleOutNo);
        stylingBaseWithBLOBs.setStatus(3L);
        List<StylingBaseWithBLOBs> stylingBaseDetailsV2 = stylingBaseMapper.selectListBySelective(stylingBaseWithBLOBs);
        stylingBaseDetails.addAll(stylingBaseDetailsV2);

        if (CollectionUtils.isEmpty(stylingBaseDetails)) {
            return listAdds;
        }
        stylingBaseDetails.sort(Comparator.comparing(StylingBaseWithBLOBs::getCreateTime));

        stylingBaseDetails.forEach(item -> {
            StylingBaseForOrder stylingBaseFinal = new StylingBaseForOrder();
            BeanUtils.copyProperties(item, stylingBaseFinal);
            //查询搭配细节
            StylingDetail stylingDetail = new StylingDetail();
            stylingDetail.setStylingBaseId(item.getId());
            List<StylingDetail> stylingDetails = iStylingRepository.selectStylingDetailBySelective(stylingDetail);
            List<StylingDetailForDemand> stylingDetailForDemands = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(stylingDetails)) {
                stylingDetails.forEach(obj -> {
                    StylingDetailForDemand stylingDetailForDemand = new StylingDetailForDemand();
                    BeanUtils.copyProperties(obj, stylingDetailForDemand);
                    stylingDetailForDemands.add(stylingDetailForDemand);
                });
            }
            stylingBaseFinal.setStylingDetailForDemand(stylingDetailForDemands);
            listAdds.add(stylingBaseFinal);
        });
        return listAdds;
    }


    @Override
    public List<BoxWithBLOBs> selectByIds(List<String> ids) {
        return boxMapper.selectByIds(ids);
    }

    @Override
    public Long getMaxDetailIdByBoxId(String boxId) {
        List<BoxDetailsWithBLOBs> details = boxDetailsMapper.selectListByBoxId(boxId);
        return details.stream().map(e -> e.getProductNo()).max(Comparator.comparing(Long :: intValue)).orElse(0L);
    }

    @Override
    public int addBoxDetail(BoxDetailsWithBLOBs box) {
        return boxDetailsService.save(box)? 1:0;
    }

    @Override
    public void deleteBoxDetailById(String id) {
        boxDetailsService.removeById(id);
    }


    @Override
    public List<BoxDiscountForResp> selectForDiscountList(Page page, String unionId) {
        // 查询用户绑定门店
        List<CclientVip> vipList = customerVipRepository.findCustomerVipByUnionId(unionId);
        List<Long> storeIds = Optional.ofNullable(vipList)
                .map(vips -> vips.stream().filter(e -> {
                        return e.getcStoreId() != null;
                    }).map(CclientVip::getcStoreId).collect(Collectors.toList()))
                .orElse(new ArrayList<Long>());
        if(storeIds.size() == 0){
            return new ArrayList<>();
        }
        List<CStore> cStores = icStoreRepository.selectCStoreByIds(storeIds);
        Map<Long, List<CStore>> storeMap = cStores.stream().collect(Collectors.groupingBy(CStore::getId));
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        promoMallItemMapper.selectForDiscountList(storeIds);
        PageInfo<BoxDiscountForResp> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        if(pageInfo.getList().size() > 0){
            pageInfo.getList().stream().forEach(a -> {
                String storeName = Optional.ofNullable(storeMap.get(a.getCStoreId()))
                                            .map(stores -> stores.get(0).getName())
                                            .orElse(null);
                a.setName(storeName);
            });
        }
        return pageInfo.getList();
    }

    @Override
    public List<BoxDetailsWithBLOBs> selectDetailByDetailIds(List<String> ids) {
        return boxDetailsMapper.selectBatchIds(ids);
    }

    @Override
    public int updateDetailStatusByBoxId(String boxId, long destStatus, long origStatus) {
        return boxDetailsMapper.updateDetailStatusByBoxId(boxId,destStatus,origStatus);
    }


    public List<BoxWithBLOBs> findCustomerStatusBox(String unionId,List<Long> statusList){
        return boxMapper.findCustomerStatusBox(unionId,statusList);
    }

    @Override
    public List<BoxWithBLOBs> getTheMonthListByStatus(List<Long> typeList,List<Long> statusList) {
        return boxMapper.getTheMonthListByStatus(typeList,statusList);
    }

    @Override
    public Integer unDoneBoxCount(String unionid) {
        return boxMapper.unDoneBoxCount(unionid);
    }

    @Override
    public Integer getBoxCountByUnionid(String unionid) {
        return boxMapper.getBoxCountByUnionid(unionid);
    }

    @Override
    public Long countSuccessByUnionId(String unionid, Integer status) {
        return boxDetailsMapper.countSuccessByUnionId(unionid,status);
    }

    @Override
    public List<BoxCountEntity> countNumByUnionIds(List<String> unionIds, Integer code) {
        return boxMapper.countNumByUnionIds(unionIds);
    }

    @Override
    public List<BoxCountEntity> countSuccessNumByUnionIds(List<String> unionIds, Integer code) {
        return boxDetailsMapper.countSuccessNumByUnionIds(unionIds,code);
    }

    @Override
    public Long selectCountNumByFashionerIdAndDate(String fashionerId, Date startDate, Date endDate) {
        return boxMapper.selectCountNumByFashionerIdAndDate(fashionerId,startDate,endDate);
    }

    @Override
    public List<BoxDetailsWithBLOBs> findDetailByIds(List<String> boxDetailsIds) {
        return boxDetailsMapper.findByIds(boxDetailsIds);
    }

    @Override
    public List<BoxWithBLOBs> getEbSendBox() {
        return boxMapper.getEbSendBox();
    }

    @Override
    public List<BoxWithBLOBs> selectByUnionIdAndCreateTime(String unionid, Date subscriptionMonth) {
        return boxMapper.selectByUnionIdAndCreateTime(unionid,subscriptionMonth);
    }

    @Override
    public void changePrice(BoxDetailsWithBLOBs boxDetailsWithBLOBs){
        boxDetailsMapper.changePrice(boxDetailsWithBLOBs);
    }

    @Override
    public List<WorkBoxListEntity> newWorkBoxList(WorkListReq requestData) {
        return boxMapper.newWorkBoxList(requestData);
    }
    @Override
    public  List<Box> findOnGoingBoxByUnionIdAndAppId(String unionId, String appId) {
        return boxMapper.findOnGoingBoxByUnionIdAndAppId(unionId,appId);
    }

    @Override
    public List<Box> selectSignInByDays(Integer signInOneDay) {
        return boxMapper.selectBySignInByDays(signInOneDay);
    }

    @Override
    public List<Box> getUnReturnBox(Integer signInOneDay) {
        return boxMapper.getUnReturnBox(signInOneDay);
    }

    @Override
    public List<BoxWithBLOBs> selectByWaitReturn(BoxWithBLOBs para) {
        return boxMapper.selectByWaitReturn(para);
    }

    @Override
    public List<BoxWithBLOBs> selectByWaitReturnThreeDays(BoxWithBLOBs para) {
        return boxMapper.selectByWaitReturnThreeDays(para);
    }

    @Override
    public List<BoxWithBLOBs> selectBoxByUnionIdAndBoxType(String unionid) {
        return boxMapper.selectBoxByUnionIdAndBoxType(unionid);
    }


}
