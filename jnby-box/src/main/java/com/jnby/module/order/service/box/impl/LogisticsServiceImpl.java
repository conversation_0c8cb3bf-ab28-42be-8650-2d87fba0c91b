package com.jnby.module.order.service.box.impl;

import com.jnby.common.ResponseCodeEnum;
import com.jnby.common.ResponseResult;
import com.jnby.infrastructure.box.mapper.BoxReturnMapper;
import com.jnby.infrastructure.box.mapper.LogisticsCompanyMapper;
import com.jnby.infrastructure.box.model.Box;
import com.jnby.infrastructure.box.model.BoxReturn;
import com.jnby.infrastructure.box.model.LogisticsCompany;
import com.jnby.module.order.repository.IBoxRepository;
import com.jnby.module.order.service.box.ILogisticsService;
import com.jnby.module.sf.entity.SFQueryRouteRespDto;
import com.jnby.module.sf.entity.SFRespDto;
import com.jnby.module.sf.service.ISFExpressService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: lwz
 * @Date: 2021/10/26 11:09
 * @Description: LogisticsDetailsServieImpl  物流信息查询
 * @Version 1.0.0
 */
@Service
public class LogisticsServiceImpl implements ILogisticsService {
    @Resource
    private IBoxRepository boxRepository;

    @Resource
    private BoxReturnMapper boxReturnMapper;

    @Resource
    private ISFExpressService isfExpressService;

    @Resource
    private LogisticsCompanyMapper logisticsCompanyMapper;

    @Override
    public ResponseResult getLogisticsDetails(String boxId, String type) {
        String trackNumber = "";
        if (StringUtils.isEmpty(type)) {
            Box box = boxRepository.findById(boxId);
            trackNumber = box != null ? box.getTrackNumber() : trackNumber;
        }

        if (StringUtils.isNotEmpty(type)) {
            BoxReturn boxReturn = boxReturnMapper.selectByBoxId(boxId);
            trackNumber = boxReturn != null ? boxReturn.getTrackingNumber() : trackNumber;
        }

        if (StringUtils.isEmpty(trackNumber)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "该服务单没有顺丰快递单号");
        }

        List<String> trackingNumberList = new ArrayList<>();
        trackingNumberList.add(trackNumber);
        SFRespDto<SFQueryRouteRespDto> routeDto = isfExpressService.queryRoute(trackingNumberList);

        if (routeDto != null && routeDto.isSuccess()) {
            List<SFQueryRouteRespDto.RouteResp> routeResp = routeDto.getRespData().getRouteResps();
            return ResponseResult.success(routeResp);
        }
        // 查询失败
        return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), routeDto.getErrorMsg());

    }

    @Override
    public ResponseResult<List<LogisticsCompany>> findLogisticsCompany(){
        List<LogisticsCompany> logisticsCompanyList = logisticsCompanyMapper.selectLogisticsCompanyList();
        return ResponseResult.success(logisticsCompanyList);
    }
}
