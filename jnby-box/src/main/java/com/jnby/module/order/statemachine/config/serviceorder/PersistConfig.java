package com.jnby.module.order.statemachine.config.serviceorder;



import com.jnby.module.order.statemachine.entity.DefinitionEntity;
import com.jnby.module.order.statemachine.entity.DefinitionStateWithEvent;
import com.jnby.module.order.statemachine.tools.HandlerTool;
import com.jnby.module.order.statemachine.constant.StateMachineConstant;
import com.jnby.module.order.statemachine.entity.DefinitionEvent;
import com.jnby.module.order.statemachine.entity.DefinitionState;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.StateMachineBuilder;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@Configuration
public class PersistConfig {

    @Resource
    private ServiceOrderStateMachinePersist orderStateMachinePersist;

    @Resource
    private BeanFactory beanFactory;

    @Resource
    private HandlerTool handlerTool;


    @Bean(name = "serviceOrderPersister")
    public StateMachinePersister<DefinitionState, DefinitionEvent, DefinitionEntity> orderPersister() {
        return new DefaultStateMachinePersister<>(orderStateMachinePersist);
    }

    @Bean(name="serviceOrderStateMachineBuilder")
    public StateMachine<DefinitionState, DefinitionEvent> build() throws Exception {

        StateMachineBuilder.Builder<DefinitionState, DefinitionEvent> builder = StateMachineBuilder.builder();

        // 服务单状态机初始状态
        DefinitionState source = new DefinitionState();
        source.setStateName(StateMachineConstant.SERVICE_ORDER_SOURCE_STATE_NAME);
        source.setStateValue(StateMachineConstant.SERVICE_ORDER_SOURCE_STATE_VALUE);


        builder.configureConfiguration()
                .withConfiguration()
                .machineId(StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID)
                .beanFactory(beanFactory);

        builder.configureStates()
                .withStates()
                .initial(source)
                .states(handlerTool.getDefinitionStates());

        // 事件与状态关系
        List<DefinitionStateWithEvent> list= handlerTool.getDefinitionStateWithEvents();

        for (DefinitionStateWithEvent definitionStateWithEvent:list) {
            DefinitionState sourceTemp = new DefinitionState(definitionStateWithEvent.getSourceStateName(),definitionStateWithEvent.getSourceStateValue());
            DefinitionState targetTemp =  new DefinitionState(definitionStateWithEvent.getTargetStateName(),definitionStateWithEvent.getTargetStateValue());
            DefinitionEvent orderEventTemp = new DefinitionEvent(definitionStateWithEvent.getEventName(),definitionStateWithEvent.getEventValue());

            builder.configureTransitions()
                    .withExternal().source(sourceTemp).target(targetTemp).event(orderEventTemp)
                    .action(handlerTool.getActionByName(definitionStateWithEvent.getActionTrue()),
                            handlerTool.getErrorActionByName(definitionStateWithEvent.getActionFalse()));
        }

        return builder.build();
    }
}