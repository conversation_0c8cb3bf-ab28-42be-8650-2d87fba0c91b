package com.jnby.module.order.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/3/14
 */
public enum QuestionTypeEnum {
    //
    danxuan(0L, "单选"),
    duoxuan(1L, "多选"),
    tiankong(2L, "填空"),
    juzhendan<PERSON>uan(3L, "矩阵单选"),
    juzhenduo<PERSON>uan(4L, "矩阵多选"),
    tup<PERSON>(5L, "图片题"),
    we<PERSON><PERSON>(6L, "文件题"),
    chimanan(7L, "尺码题(男)"),
    chimanv(8L, "尺码题(女)"),
    tupianChoose(9L, "图片单选题"),
    tupianChooseDulp(10L, "图片多选题"),
    ;


    private static final Map<Long, QuestionTypeEnum> LOOKUP = new HashMap<>();

    static {
        for (QuestionTypeEnum s : EnumSet.allOf(QuestionTypeEnum.class)) {
            LOOKUP.put(s.getCode(), s);
        }
    }

    private Long code;
    private String name;

    QuestionTypeEnum(Long code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public Long getCode() {
        return this.code;
    }

    public static String get(Long code) {
        QuestionTypeEnum questionTypeEnum = LOOKUP.get(code);
        return questionTypeEnum.getName();
    }
}
