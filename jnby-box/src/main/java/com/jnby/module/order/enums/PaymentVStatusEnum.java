package com.jnby.module.order.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/3/14
 */
public enum PaymentVStatusEnum {

    /**
     * 商户下单已受理
     */
    CREATED(1, "CREATED"),

    /**
     * 用户成功使用服务
     */
    USER_ACCEPTED(2, "DOING"),
    /**
     * 服务订单完成
     */
    DONE(3, "DONE"),
    /**
     * 商户取消服务订单
     */
    REVOKED(4, "REVOKED"),

    /**
     * 服务订单已失效，"商户已创建服务订单"状态超过30天未变动，则订单失效
     */
    EXPIRED(5, "EXPIRED"),
    ;


    private static final Map<String, PaymentVStatusEnum> LOOKUP = new HashMap<>();

    static {
        for (PaymentVStatusEnum s : EnumSet.allOf(PaymentVStatusEnum.class)) {
            LOOKUP.put(s.getName(), s);
        }
    }

    private Integer code;
    private String name;

    PaymentVStatusEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }

    public static Integer get(String name) {
        PaymentVStatusEnum paymentStatusEnum = LOOKUP.get(name);
        return paymentStatusEnum.getCode();
    }
}
