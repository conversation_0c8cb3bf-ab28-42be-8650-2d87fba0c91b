package com.jnby.module.order.context;

import com.jnby.infrastructure.box.model.*;
import com.jnby.module.order.entity.MallVouInfoEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SplitMRetailContext {

    private BoxWithBLOBs box;

    private List<OrderDetail> orderDetailList;

    private List<OrderPayment> orderPaymentList;

    private CustomerDetails customerDetails;

    private Order order;

    private String ddLy;

    private List<MallVouInfoEntity> mallVouInfoList;

}
