package com.jnby.module.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.jnby.application.admin.dto.request.AllotsBox2StoreReq;
import com.jnby.base.context.MemberQueryContext;
import com.jnby.base.repository.ICStoreRepository;
import com.jnby.base.service.*;
import com.jnby.base.service.enums.BoxSourceTypeEnum;
import com.jnby.common.BoxException;
import com.jnby.common.ErrorConstants;
import com.jnby.common.ProducerUtil;
import com.jnby.common.ResponseResult;
import com.jnby.common.enums.BoxReturnStatusEnum;
import com.jnby.common.enums.PayChannelEnum;
import com.jnby.common.enums.UltimaBoxDetailsStatusEnum;
import com.jnby.common.enums.UltimaBoxStatusEnum;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.common.listener.router.msg.entity.SupplyDeliverMsgEntity;
import com.jnby.common.listener.router.msg.util.MsgTagUtil;
import com.jnby.common.util.DateUtil;
import com.jnby.common.util.ParseBrandUtil;
import com.jnby.common.util.ParseSFXmlUtil;
import com.jnby.infrastructure.bojun.mapper.HrEmployeeMapper;
import com.jnby.infrastructure.bojun.mapper.TransferStorageMapper;
import com.jnby.infrastructure.bojun.model.*;
import com.jnby.infrastructure.box.mapper.BoxMapper;
import com.jnby.infrastructure.box.mapper.LogisticsMapper;
import com.jnby.infrastructure.box.model.*;
import com.jnby.mallasset.api.dto.asset.MallConfigListRespDto;
import com.jnby.module.commonPay.service.IHandlePayService;
import com.jnby.module.credit.entity.OverCreditOrderEntity;
import com.jnby.module.credit.service.IBCustomerZhimaCreditOrderService;
import com.jnby.module.customer.vip.service.IExternalCustomerVipService;
import com.jnby.module.credit.entity.OverCreditOrderEntity;
import com.jnby.module.credit.service.IBCustomerZhimaCreditOrderService;
import com.jnby.module.eb.service.IBBoxEbTransFlowService;
import com.jnby.module.eb.service.IBBoxEbTransTotService;
import com.jnby.module.eb.service.IBBoxTransPoolService;
import com.jnby.module.eb.service.IBoxEbService;
import com.jnby.module.facade.service.IMallAssetService;
import com.jnby.module.jic.entity.PosBalanceSaveReqEntity;
import com.jnby.module.jic.service.IJICBalanceService;
import com.jnby.module.jic.service.IUserVipService;
import com.jnby.module.message.entity.SysTemplateMsgEntity;
import com.jnby.module.message.enums.SysMsgTemplateCodeEnum;
import com.jnby.module.message.service.ISysMessageService;
import com.jnby.module.oauth.oss.service.IUserInfoService;
import com.jnby.module.order.context.CreateEbMallRetContext;
import com.jnby.module.order.context.PushDeliveryPlatformContext;
import com.jnby.module.order.context.SplitMRetailContext;
import com.jnby.module.order.entity.*;
import com.jnby.module.order.enums.*;
import com.jnby.module.order.repository.IBoxRefundRepository;
import com.jnby.module.order.repository.IBoxRepository;
import com.jnby.module.order.repository.IBoxReturnRepository;
import com.jnby.module.order.repository.IOrderRepository;
import com.jnby.module.order.service.*;
import com.jnby.module.order.service.box.*;
import com.jnby.module.pay.repository.IPaymentRepository;
import com.jnby.module.product.entity.ProductStockEntity;
import com.jnby.module.product.service.IMProductAliasService;
import com.jnby.module.product.service.IStockService;
import com.jnby.module.subscribe.service.IBSubscribeInfoService;
import com.jnbyframework.boot.common.system.vo.LoginUser;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.paycenter.api.resp.GetPayOrderResp;
import org.springcenter.paycenter.api.resp.OrderDetailEntity;
import org.springcenter.paycenter.api.resp.OrderEntity;
import org.springcenter.paycenter.api.resp.OrderPaymentEntity;
import org.springcenter.retail.api.IRetailApi;
import org.springcenter.retail.api.dto.req.Push2JSTDTO;
import org.springcenter.retail.api.dto.req.Query2JSTDTO;
import org.springcenter.retail.api.dto.req.Update2JSTDTO;
import org.springcenter.retail.api.dto.resp.Query2JSTRespDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 搭配师搭盒核心流程服务
 */
@Service
@Slf4j
@RefreshScope
public class IFasCoreServiceImpl implements IFasCoreService {
    @Resource
    private TransferStorageMapper transferStorageMapper;
    @Autowired
    private IMTransferService imTransferService;
    @Autowired
    private IRetailService retailService;
    @Autowired
    private IStockService iStockService;
    @Autowired
    private IExpressService expressService;
    @Autowired
    private ICustomerDetailsService customerDetailsService;
    @Autowired
    private ICustomerVipService customerVipService;
    @Autowired
    private IBoxRepository boxRepository;
    @Autowired
    private IOrderRepository orderRepository;
    @Autowired
    private IPaymentRepository paymentRepository;
    @Autowired
    private IMProductService imProductService;
    @Autowired
    private ICStoreRepository icStoreRepository;
    @Autowired
    private IEbMallretService ebMallretService;
    @Autowired
    private IEbMallretitemService ebMallretitemService;
    @Autowired
    private IMProductAliasService imProductAliasService;
    @Autowired
    private IRegionsService regionsService;
    @Autowired
    private BoxMapper boxMapper;
    @Autowired
    private IEbExpressService ebExpressService;
    @Autowired
    private ISyncBoxStatusJobService syncBoxStatusJobService;
    @Autowired
    private IMTransferitemService imTransferitemService;
    @Autowired
    private IBoxDetailsService boxDetailsService;
    @Autowired
    private IBoxEbService boxEbService;
    @Autowired
    private IBoxActionService boxActionService;
    @Autowired
    private IEbMallretPayitemService ebMallretPayitemService;
    @Autowired
    private IBoxReturnRepository boxReturnRepository;
    @Autowired
    private IBoxRefundRepository boxRefundRepository;
    @Autowired
    @Qualifier("bojunTransactionTemplate")
    private TransactionTemplate bojunTemplate;
    @Autowired
    @Qualifier("boxTransactionTemplate")
    private TransactionTemplate boxTemplate;
    // 零拣仓
    @Value("${muster.store}")
    private Long musterStoreId;
    // box仓
    @Value("${box.store}")
    private Long boxStoreId;
    // box在途仓
    @Value("${box.in.transit.store}")
    private Long boxInTransitStoreId;
    // 微商城独立仓
    @Value("${wsc.store}")
    private Long wxStore;
    // 电商独立仓
    @Value("${ds.store}")
    private Long ecStore;

    // 先试后买仓
    @Value("${box.try.after.store}")
    private Long tryAfterStoreId;

    @Autowired
    private IdLeafService idLeaf;
    @Autowired
    private IBReturnMallretService ibReturnMallretService;

    @Autowired
    private IBoxService boxService;

    @Autowired
    private IBoxReturnDetailsService boxReturnDetailsService;

    @Autowired
    private ISyncBoxReturnStatusJobService iSyncBoxReturnStatusJobService;

    @Autowired
    private IBReturnMallService ibReturnMallService;

    @Autowired
    private IBoxSupplyService boxSupplyService;
    @Autowired
    private IBBoxTransPoolService boxTransPoolService;

    @Autowired
    private IBBoxEbTransTotService boxEbTransTotService;

    @Autowired
    private IBBoxEbTransFlowService boxEbTransFlowService;

    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private ICVipTemptransService icVipTemptransService;

    @Autowired
    private ISysMessageService sysMessageService;

    @Autowired
    private IFashionerService fashionerService;

    @Autowired
    private IWeimobCartBaseService weimobCartBaseService;

    @Autowired
    private IMRetailService imRetailService;

    @Autowired
    private IEbMpayItemService ebMpayItemService;

    @Value("${factory.send}")
    private String factorySend;

    @Autowired
    private HrEmployeeMapper hrEmployeeMapper;

    @Autowired
    private ProducerUtil producerUtil;

    @Autowired
    private LogisticsMapper logisticsMapper;

    @Value("${role.kefu.phones}")
    private String kefuPhones;

    @Autowired
    private IMRetailitemService imRetailitemService;



    // 店铺编号
    @Value("${jushuitan.shopId}")
    private Integer shopId;

    @Resource
    private IRetailApi retailApi;

    @Resource
    private ParseSFXmlUtil parseSFXmlUtil;

    @Resource
    private ICustomerAskBoxService customerAskBoxService;

    @Resource
    private IBSubscribeInfoService subscribeInfoService;

    @Resource
    private IOrderBalanceLogService orderBalanceLogService;

    @Resource
    private IJICBalanceService ijicBalanceService;
    @Resource
    private IBCustomerZhimaCreditOrderService ibCustomerZhimaCreditOrderService;

    @Value(value = "${wx.default.mchid}")
    public String wxDefaultMchId;

    @Value(value = "${zfb.default.mchid}")
    public String zfbDefaultMchId;

    @Resource
    private IBOrderPromotionService ibOrderPromotionService;

    @Resource
    private IHandlePayService iHandlerPayService;

    @Resource
    private IMallAssetService mallAssetService;


    @Resource
    private IUserVipService iUserVipService;

    @Override
    public void pushDeliveryPlatform(PushDeliveryPlatformContext pushDeliveryPlatformContext) {
        log.info("进入搭配师新流程wms,boxSn={}",pushDeliveryPlatformContext.getSourceNo());

        // 查询是否已经生成调拨单
        if(StringUtils.isNotBlank(pushDeliveryPlatformContext.getExpressId())){
            Express express = expressService.getById(pushDeliveryPlatformContext.getExpressId());
            if(ObjectUtils.isNotEmpty(express) && StringUtils.isNotBlank(express.getExpressDocno())){
                // 说明调拨单已经生成，直接返回
                log.info("调拨单已生成,无需走下面发货逻辑");
                return;
            }
        }

        // 获取要调货的商品
        Map<Long, List<String>> correctStockMap = this.correctStock(pushDeliveryPlatformContext.getBoxDetailsWithBLOBsList());
        // 创建总部发货参数实体
        DeliverEntity deliverEntity = this.createDeliverEntity(pushDeliveryPlatformContext.getSourceNo(),pushDeliveryPlatformContext.getBoxDetailsWithBLOBsList(),pushDeliveryPlatformContext.getLogistics(),pushDeliveryPlatformContext.getIfVirDelivery());
        AtomicReference<String> transId = new AtomicReference<>();
        try{
            bojunTemplate.execute(action -> {
                // 货品调拨
                if(ObjectUtils.isNotEmpty(correctStockMap)){
                   for(Long storeId: correctStockMap.keySet()){
                       this.musterAllot2Box(correctStockMap.get(storeId),pushDeliveryPlatformContext.getSourceNo(),storeId);
                   }
                }
                // 发货
                transId.set(this.deliver(deliverEntity));
                return action;
            });
        }catch (Exception ex){
            log.error("搭配师服务单发货异常",ex);
            throw ex;
        }
        Express updateExpress = new Express();
        updateExpress.setId(pushDeliveryPlatformContext.getExpressId());
        updateExpress.setExpressDocno(transId.get());
        // 虚拟发货，改为已签收
        if(deliverEntity.getIfVirDelivery() == 1){
            updateExpress.setStatus(ExpressStatusEnum.received.getCode().longValue());
        }
        // 将transId更新到express
        expressService.updateById(updateExpress);
    }


    @Override
    public void pushTryAfterBuyDeliveryPlatform(PushDeliveryPlatformContext pushDeliveryPlatformContext) {
        log.info("进入先试后买流程wms,boxSn={}",pushDeliveryPlatformContext.getSourceNo());
        // 获取要从先试后买（微商城独立仓）调货的商品
        List<String> allotSku = this.correctTryAfterStock(pushDeliveryPlatformContext.getBoxDetailsWithBLOBsList());

        // 创建总部发货参数实体
        DeliverEntity deliverEntity = this.createDeliverEntity(pushDeliveryPlatformContext.getSourceNo(),
                pushDeliveryPlatformContext.getBoxDetailsWithBLOBsList(),pushDeliveryPlatformContext.getLogistics(),pushDeliveryPlatformContext.getIfVirDelivery());

        AtomicReference<String> transId = new AtomicReference<>();
        try{
            bojunTemplate.execute(action -> {
                // 货品调拨
                this.tryAfterAllotToBox(allotSku,pushDeliveryPlatformContext.getSourceNo());
                // 发货
                transId.set(this.tryAfterBuyDeliver(deliverEntity));
                return action;
            });
        }catch (Exception ex){
            log.error("先试后买服务单发货异常",ex);
            throw ex;
        }
        Express updateExpress = new Express();
        updateExpress.setId(pushDeliveryPlatformContext.getExpressId());
        updateExpress.setExpressDocno(transId.get());
        // 将transId更新到express
        expressService.updateById(updateExpress);
    }

    @Override
    public String deliver(DeliverEntity deliverEntity){
        AllotStorageEntity allotStorageEntity = new AllotStorageEntity();
        BeanUtils.copyProperties(deliverEntity,allotStorageEntity);
        // 店仓赋值
        allotStorageEntity.setOrigStoreId(boxStoreId);
        allotStorageEntity.setDestStoreId(boxInTransitStoreId);
        allotStorageEntity.setIfVirDelivery(deliverEntity.getIfVirDelivery());
        return this.allAllotsStorage(allotStorageEntity);
    }


    @Override
    public String tryAfterBuyDeliver(DeliverEntity deliverEntity) {
        AllotStorageEntity allotStorageEntity = new AllotStorageEntity();
        BeanUtils.copyProperties(deliverEntity,allotStorageEntity);
        // 店仓赋值
        allotStorageEntity.setOrigStoreId(tryAfterStoreId);
        allotStorageEntity.setDestStoreId(boxInTransitStoreId);
        return this.allAllotsStorage(allotStorageEntity);
    }

    @Override
    public String allotsStorage(AllotStorageEntity allotStorageEntity) {
        log.info("库存调拨,入参params = {}", allotStorageEntity.toJson());
        HashMap<String, Object> map = new HashMap<>();
        map.put("In_Json",allotStorageEntity.toJson());
        try {
            transferStorageMapper.boxTransferInsertOut(map);
        }catch (RuntimeException e){
            log.error("库存调拨异常 params = {}, error = {}", allotStorageEntity.toJson(), e.getMessage());
            throw new BoxException(ErrorConstants.CONSIGN_CREATE_ERROR.getCode(), e.getMessage());
        }
        BigDecimal code = (BigDecimal)map.get("r_code");
        if (code.intValue() != 0) {
            log.error("库存调拨异常 params = {}, error = {}", allotStorageEntity.toJson(), map.get("r_message"));
            throw new RuntimeException(map.get("r_message").toString());
        }
        // 调拨单id
        return String.valueOf(map.get("r_message"));
    }

    @Override
    public String allAllotsStorage(AllotStorageEntity allotStorageEntity) {
        log.info("库存调拨,入参params = {}", allotStorageEntity.toJson());
        HashMap<String, Object> map = new HashMap<>();
        map.put("In_Json",allotStorageEntity.toJson());
        try {
            // 判断是否已经调拨成功
            QueryWrapper<MTransfer> wrapper = new QueryWrapper<>();
            wrapper.eq("c_orig_id",allotStorageEntity.getOrigStoreId());
            wrapper.eq("c_dest_id",allotStorageEntity.getDestStoreId());
            wrapper.eq("description",allotStorageEntity.getDescription());
            wrapper.eq("wing_docno",allotStorageEntity.getSource());
            wrapper.select("id");
            List<MTransfer> list = imTransferService.list(wrapper);
            if(list.size() > 0){
                return list.get(0).getId().toString();
            };
            transferStorageMapper.boxAllTransferInsertOut(map);
        }catch (RuntimeException e){
            log.error("库存调拨异常 params = {}, error = {}", allotStorageEntity.toJson(), e.getMessage());
            throw new BoxException(ErrorConstants.CONSIGN_CREATE_ERROR.getCode(), e.getMessage());
        }
        BigDecimal code = (BigDecimal)map.get("r_code");
        if (code.intValue() != 0) {
            log.error("库存调拨异常 params = {}, error = {}", allotStorageEntity.toJson(), map.get("r_message"));
            throw new RuntimeException(map.get("r_message").toString());
        }
        // 调拨单id
        return String.valueOf(map.get("r_message"));
    }


    @Override
    public Map<Long,List<String>> correctStock(List<BoxDetailsWithBLOBs> boxDetailsWithBLOBs) {
        List<BoxDetailsWithBLOBs> details = boxDetailsWithBLOBs.stream().filter(e -> ObjectUtils.isEmpty(e.getEb()) || e.getEb().intValue() == 0).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(details)){
            return new HashMap<>();
        }
        List<Long> skuIds = details.stream().map(e -> Long.valueOf(e.getProductId())).collect(Collectors.toList());
        List<Long> storeIds = new ArrayList<>();
        storeIds.add(boxStoreId);
        List<ProductStockEntity> boxStorageList = iStockService.getStorages(skuIds,storeIds);
        // BOX仓库存不足商品
        List<Long> notBoxSkuIds = skuIds.stream().filter(e -> {
            long count = boxStorageList.stream().filter(a -> a.getSkuId() == e.longValue() && a.getQty() > 0).count();
            return count == 0;
        }).collect(Collectors.toList());
        if(notBoxSkuIds.size() == 0){
            return new HashMap<>();
        }
        // 零拣仓库存查询
        Map<Long,List<String>> result = new HashMap<>();
        List<Long> musterNoSkuIds = this.putTransSku(boxDetailsWithBLOBs, notBoxSkuIds, musterStoreId, result);
        if(musterNoSkuIds.size() == 0){
            return result;
        }
        // 微商城独立仓库存查询
        List<Long> wxNoSkuIds = this.putTransSku(boxDetailsWithBLOBs, musterNoSkuIds, wxStore, result);
        if(wxNoSkuIds.size() == 0){
            return result;
        }
        // 电商独立仓库存查询
        List<Long> ecNoSkuIds = this.putTransSku(boxDetailsWithBLOBs, wxNoSkuIds, ecStore, result);
        if(ecNoSkuIds.size() > 0){
            throw new BoxException(ErrorConstants.CONSIGN_CREATE_ERROR.getCode(), JSON.toJSONString(ecNoSkuIds)+"库存不足");
        }
        return result;
    }



    private  List<Long> putTransSku(List<BoxDetailsWithBLOBs> boxDetailsWithBLOBs,List<Long> notBoxSkuIds,Long StoreId,Map<Long,List<String>> result){
        List<ProductStockEntity> musterStorageList = iStockService.getStorages(notBoxSkuIds,Collections.singletonList(StoreId));
        List<Long> musterSkuIds = notBoxSkuIds.stream().filter(e -> {
            long count = musterStorageList.stream().filter(a -> a.getSkuId() == e.longValue() && a.getQty() > 0).count();
            return count > 0;
        }).collect(Collectors.toList());
        List<String> skuList = boxDetailsWithBLOBs.stream().filter(e -> {
            return musterSkuIds.contains(Long.valueOf(e.getProductId()));
        }).map(BoxDetailsWithBLOBs::getSku).collect(Collectors.toList());
        result.put(StoreId,skuList);
        // 无库存商品
        return notBoxSkuIds.stream().filter(e -> {
            long count = musterStorageList.stream().filter(a -> a.getSkuId() == e.longValue() && a.getQty() > 0).count();
            return count == 0;
        }).collect(Collectors.toList());
    }



    @Override
    public List<String> correctTryAfterStock(List<BoxDetailsWithBLOBs> boxDetailsWithBLOBs) {
        List<BoxDetailsWithBLOBs> details = boxDetailsWithBLOBs.stream().filter(e -> ObjectUtils.isEmpty(e.getEb()) || e.getEb().intValue() == 0).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(details)){
            return new ArrayList<>();
        }
        List<Long> skuIds = details.stream().map(e -> Long.valueOf(e.getProductId())).collect(Collectors.toList());
        List<Long> storeIds = new ArrayList<>();
        storeIds.add(tryAfterStoreId );
        List<ProductStockEntity> boxStorageList = iStockService.getStorages(skuIds,storeIds);
        // 仓库存不足商品
        List<Long> notBoxSkuIds = skuIds.stream().filter(e -> {
            long count = boxStorageList.stream().filter(a -> a.getSkuId() == e.longValue() && a.getQty() > 0).count();
            return count == 0;
        }).collect(Collectors.toList());
        if(notBoxSkuIds.size() == 0){
            return new ArrayList<String>();
        }
        // 先试后买 微商城独立仓库存不足取LJ查询
        storeIds.clear();
        storeIds.add(musterStoreId);
        List<ProductStockEntity> musterStorageList = iStockService.getStorages(notBoxSkuIds,storeIds);
        // 微商城独立仓存不足商品
        List<Long> musterSkuIds = notBoxSkuIds.stream().filter(e -> {
            long count = musterStorageList.stream().filter(a -> a.getSkuId() == e.longValue() && a.getQty() > 0).count();
            return count == 0;
        }).collect(Collectors.toList());
        if(musterSkuIds.size() > 0){
            throw new BoxException(ErrorConstants.CONSIGN_CREATE_ERROR.getCode(), JSON.toJSONString(musterSkuIds)+"库存不足");
        }
        return boxDetailsWithBLOBs.stream().filter(e -> {
            return notBoxSkuIds.contains(Long.valueOf(e.getProductId()));
        }).map(BoxDetailsWithBLOBs::getSku).collect(Collectors.toList());
    }


    /**
     * 购买(零售拆单)
     * ·指定品牌
     *  -拆到指定品牌会员卡
     * ·未指定品牌
     *  1.往季商品
     *    - 拆到奥莱卡上
     *  2.当季商品
     *    - 经销卡品牌的商品拆到经销卡
     *    - 用户有江南布衣+卡（绑定门店），拆到此会员卡。结束
     *    - 用户有购买商品对应的直营品品牌卡（绑定门店），拆到此会员卡
     *    - 剩余商品拆单到江南布衣+卡
     */
    @Override
    public BatchCreateMRetailEntity splitMRetail(Order order) {
        // 初始化订单和用户数据
        SplitMRetailContext splitMRetailContext = initOrderInfo(order);
        List<SplitRetailVipEntity> splitVipList = getCustomerVip(splitMRetailContext.getCustomerDetails().getUnionid());
        // 批量生成零售单参数
        BatchCreateMRetailEntity batchRetailList = new BatchCreateMRetailEntity();
        List<CreateMRetailEntity> retailEnList = new ArrayList<>();
        batchRetailList.setBoxSn(order.getBoxSn());
        batchRetailList.setBoxInTransitStoreId(boxInTransitStoreId);
        batchRetailList.setVouchers(order.getVouchersNo());
        if(BoxTypeEnum.FASHIONERBOX.getCode().equals(order.getType())){
            // 组装搭配师零售单参数
            putFashionerRetailist(order, splitMRetailContext, splitVipList, batchRetailList, retailEnList);
        }else if(BoxTypeEnum.TRY_AFTER_BUY.getCode().equals(order.getType())){
            // 组装先试后买零售单参数
            putBnplRetailList(order, splitMRetailContext, splitVipList, batchRetailList, retailEnList);
        }

        // 搭配师联域处理
        boolean ifLy = setFashionLianYu(retailEnList,order);
        // 生成零售单
        String retailNos = createMRetail2BoJun(order, batchRetailList, retailEnList);
        // 同步零售数据
        syncRetail(retailNos,order.getId(),order.getOrderSn());
        if(ifLy) {
            fashionerLYUpdate(retailEnList,order);
        }
        return batchRetailList;

    }


    // 将拆分到联域自收银店的零售单，支付方式微支付改为联域，订单来源改为联域
    private boolean setFashionLianYu(List<CreateMRetailEntity> retailEnList,Order order){
        if(CollectionUtils.isEmpty(retailEnList)){
            return false;
        }
        List<Long> storeIdList = retailEnList.stream().map(CreateMRetailEntity :: getStoreId ).collect(Collectors.toList());
        List<CStore> storeList = icStoreRepository.selectCStoreByIds(storeIdList);
        List<String> storeCodeList = storeList.stream().map(CStore::getCode).collect(Collectors.toList());
        int payChannel;
        if("3".equals(order.getPayChannel())){
            // 微信支付 -- 直连
            payChannel = 1;
        }else{
            // 支付宝 -- 收钱吧
            payChannel = 2;
        }
        // 批量获取门店支付方式
        List<MallConfigListRespDto> mallConfigListRespDtos = mallAssetService.batchGetStoreConfig(storeCodeList, payChannel);
        if(CollectionUtils.isEmpty(mallConfigListRespDtos)){
            return false;
        }
        // 联域且自收银
        List<MallConfigListRespDto> lyList = mallConfigListRespDtos.stream()
                .filter(e -> 1 == e.getCollectionType() && CollectionUtils.isNotEmpty(e.getPayConfigList())
                        && e.getPayConfigList().stream().noneMatch(a -> "DEFAULT".equals(a.getBjStoreId())))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(lyList)){
            return false;
        }
        List<String> lyStoreCodes = lyList.stream().map(MallConfigListRespDto::getStoreId).collect(Collectors.toList());
        List<Long> lYstoreIdList = storeList.stream().filter(e -> lyStoreCodes.contains(e.getCode())).map(CStore :: getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(lYstoreIdList)){
            return false;
        }
        retailEnList.forEach(e -> {
            if(lYstoreIdList.contains(e.getStoreId())){
                e.getPayList().forEach(a -> {
                    if(OrderPaymentPayWayEnum.MONEY.getBjCode().equals(a.getPayWayId())){
                        a.setPayWayId(OrderPaymentPayWayEnum.LIANYU.getBjCode());
                    }
                });
                e.setDdly("LY");
            }
        });
        return true;
    }

    private void fashionerLYUpdate(List<CreateMRetailEntity> retailEnList,Order order){
        List<Long> lySkuIdList = new ArrayList<>();
        retailEnList.forEach(e -> {
            if("LY".equals(e.getDdly())){
                List<Long> skuIds = e.getProductList().stream().map(CreateMRetailEntity.Product::getCodeId).collect(Collectors.toList());
                lySkuIdList.addAll(skuIds);
            }
        });
        // 更新实付中间表
        LambdaQueryWrapper<EbMpayItem> ebMpayItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
        ebMpayItemLambdaQueryWrapper.eq(EbMpayItem :: getOrderNo,order.getOrderSn());
        ebMpayItemLambdaQueryWrapper.eq(EbMpayItem :: getcPaywayId,OrderPaymentPayWayEnum.MONEY.getBjCode());
        ebMpayItemLambdaQueryWrapper.in(EbMpayItem :: getGoodsId,lySkuIdList);
        List<EbMpayItem> list = ebMpayItemService.list(ebMpayItemLambdaQueryWrapper);
        List<EbMpayItem> batchUpdateList = list.stream().map(e -> {
            EbMpayItem updateEbMapItem = new EbMpayItem();
            updateEbMapItem.setId(e.getId());
            updateEbMapItem.setcPaywayId(OrderPaymentPayWayEnum.LIANYU.getBjCode());
            return updateEbMapItem;
        }).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(batchUpdateList)){
            ebMpayItemService.updateBatchById(batchUpdateList);
        }
        // 更新订单表
        Order updateOrder = new Order();
        updateOrder.setId(order.getId());
        updateOrder.setIsUnion(1);
        updateOrder.setMerchantCashType("1");
        orderRepository.updateOrderBySelective(updateOrder);
    }



    @Override
    public void syncRetail(String retailNos,String orderId,String orderNo){
        // 更新实付中间表零售单号
        syncRetail2PayItem(retailNos,orderNo);
        // 更新订单零售单号
        updateRetailNo2Order(orderId,retailNos);
        // 同步储值卡数据到POS+
        syncBalance2Pos(orderId);
    }

    @Override
    public void syncBalance2Pos(String orderId){
        Order order = orderRepository.findById(orderId);
        if(ObjectUtils.isEmpty(order) || !BoxTypeEnum.SALESBOX.getCode().equals(order.getType())){
            return;
        }
        if(ObjectUtils.isEmpty(order.getBalanceAmt()) || BigDecimal.ZERO.compareTo(order.getBalanceAmt()) == 0){
            return;
        }
        // 更新伯俊实付中间表数据
        QueryWrapper<EbMpayItem> wrapper = new QueryWrapper<>();
        wrapper.isNotNull("ret_no");
        wrapper.eq("order_no",order.getOrderSn());
        List<EbMpayItem> list = ebMpayItemService.list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            log.error("储值卡同步到POS+失败,未查询到实付中间表数据,orderSn={},orderId={}",order.getOrderSn(),orderId);
            return;
        }

        // 获取扣减流水号
        LambdaQueryWrapper<OrderBalanceLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderBalanceLog::getOrderId,order.getId());
        queryWrapper.eq(OrderBalanceLog::getType,BalanceTypeEnum.reduce.getCode());
        List<OrderBalanceLog> balanceLogList = orderBalanceLogService.list(queryWrapper);
        if(CollectionUtils.isEmpty(balanceLogList)){
            log.error("储值卡同步到POS+失败,未查询到储值卡扣减记录,orderSn={},orderId={}",order.getOrderSn(),orderId);
            return;
        }
        CustomerDetails customer = customerDetailsService.getById(order.getCustomerId());
        List<OrderDetail> orderDetails = orderRepository.selectOrderDetailListByOrderId(orderId);
        OrderBalanceLog orderBalanceLog = balanceLogList.get(0);
        Map<String, List<EbMpayItem>> map = list.stream().collect(Collectors.groupingBy(EbMpayItem::getRetNo));
        for (String retNo: map.keySet()) {
            List<EbMpayItem> ebMpayItems = map.get(retNo);
            PosBalanceSaveReqEntity posBalanceSaveReqEntity = new PosBalanceSaveReqEntity();
            posBalanceSaveReqEntity.setServiceNo(retNo);
            posBalanceSaveReqEntity.setUnionId(customer.getUnionid());
            posBalanceSaveReqEntity.setTransNo(orderBalanceLog.getRequestId());
            Map<Long, List<EbMpayItem>> productMap = ebMpayItems.stream().collect(Collectors.groupingBy(EbMpayItem::getGoodsId));
            List<PosBalanceSaveReqEntity.PosBalanceDetail> posBalanceDetailList = new ArrayList<>();
            for (Long skuId: productMap.keySet()) {
                OrderDetail orderDetail = orderDetails.stream().filter(e -> skuId.equals(Long.parseLong(e.getProductId()))).findFirst().orElse(null);
                if(ObjectUtils.isNotEmpty(orderDetail)){
                    PosBalanceSaveReqEntity.PosBalanceDetail posBalanceDetail = new PosBalanceSaveReqEntity.PosBalanceDetail();
                    posBalanceDetail.setSku(orderDetail.getSku());
                    posBalanceDetail.setSkuId(skuId);
                    posBalanceDetail.setCapitalAmt(orderDetail.getBalanceAmt());
                    posBalanceDetail.setZkAmount(orderDetail.getBalanceAmt());
                    posBalanceDetail.setPriceActual(BigDecimal.valueOf(orderDetail.getPaidAmount()));
                    posBalanceDetail.setProductPrice(new BigDecimal(orderDetail.getProductPrice()));
                    posBalanceDetailList.add(posBalanceDetail);
                }
            }
            posBalanceSaveReqEntity.setBalanceDetailList(posBalanceDetailList);
            ijicBalanceService.posBalanceSave(posBalanceSaveReqEntity);
        }
    }


    private void putBnplRetailList(Order order, SplitMRetailContext splitMRetailContext, List<SplitRetailVipEntity> splitVipList, BatchCreateMRetailEntity batchRetailList, List<CreateMRetailEntity> retailEnList) {
        // 先试后买,指定会员卡和门店
        if(StringUtils.isBlank(order.getWechatTransactionId())){
            batchRetailList.setOrderNo("BNPL-" + order.getOrderSn());
        }else{
            batchRetailList.setOrderNo(order.getWechatTransactionId());
        }
        batchRetailList.setRefNo(order.getOrderSn());
        BoxWithBLOBs box = boxRepository.selectByBoxSn(order.getBoxSn());
        WeimobCartBase weimobCartBase = weimobCartBaseService.getById(box.getId());
        SplitRetailVipEntity splitRetailVipEntity = splitVipList.stream().filter(e -> {
            return e.getCardNo().equals(weimobCartBase.getCardNo());
        }).findFirst().orElse(null);
        if(ObjectUtils.isEmpty(splitRetailVipEntity)){
            throw new BoxException("未查询到指定的品牌卡："+ splitMRetailContext.getBox().getExplicitBrand());
        }

        /**
         * 逻辑兜底补充
         * 无绑定门店取网店，导购取 工厂发货
         * 无绑定导购取 工厂发货
         * 非网店和工厂发货时校验导购和会员卡上绑定门店是否一致，不一致则不提交
         */

        if(ObjectUtils.isEmpty(splitRetailVipEntity.getStoreId())){
            splitRetailVipEntity.setStoreId(25277L);
            splitRetailVipEntity.setSaleId(factorySend);
        }else if(StringUtils.isBlank(splitRetailVipEntity.getSaleId())){
            splitRetailVipEntity.setSaleId(factorySend);
        }
        if(!splitRetailVipEntity.getStoreId().equals(25277L) && !factorySend.equals(splitRetailVipEntity.getSaleId())){
            // 非网店和工厂发货，校验绑定关系的一致性
            // 获取联系人
            HrEmployee hrEmployee = hrEmployeeMapper.selectByPrimaryKey(Long.valueOf(splitRetailVipEntity.getSaleId()));
            if(ObjectUtils.isEmpty(hrEmployee)){
                throw new BoxException("先试后买未查询到指定的导购信息,hrEmId="+splitRetailVipEntity.getSaleId());
            }
            if(!hrEmployee.getcStoreId().equals(splitRetailVipEntity.getStoreId())){
                throw new BoxException("先试后买,导购绑定门店与用户卡绑定门店不一致,hrEmId="+hrEmployee.getId()+",hrEmStoreId="+hrEmployee.getcStoreId()+",cardStoreId="+splitRetailVipEntity.getStoreId());
            }
        }
        CreateMRetailEntity createMRetailEntity = cCreateMRetailEntity(splitMRetailContext.getOrderDetailList(), order, splitMRetailContext.getOrderPaymentList(),splitRetailVipEntity);
        createMRetailEntity.setChannel("微商城");
        createMRetailEntity.setUploadType("SQB");
        createMRetailEntity.setDescription("由微商城先试后买订单："+ order.getOrderSn()+"生成");
        batchRetailList.setDdly("SQB");
        retailEnList.add(createMRetailEntity);
    }

    private void putFashionerRetailist(Order order, SplitMRetailContext splitMRetailContext, List<SplitRetailVipEntity> splitVipList, BatchCreateMRetailEntity batchRetailList, List<CreateMRetailEntity> retailEnList) {
        if(StringUtils.isBlank(order.getWechatTransactionId())){
            batchRetailList.setOrderNo("BOX" + order.getOrderSn());
        }else{
            batchRetailList.setOrderNo(order.getWechatTransactionId());
        }
        batchRetailList.setRefNo(order.getOrderSn());
        if(StringUtil.isNotEmpty(splitMRetailContext.getBox().getExplicitBrand())){
            // 指定品牌
            SplitRetailVipEntity splitRetailVipEntity = splitVipList.stream().filter(e -> e.getVipBrand().equals(splitMRetailContext.getBox().getExplicitBrand())).findFirst().orElse(null);
            if(ObjectUtils.isEmpty(splitRetailVipEntity)){
                throw new BoxException("未查询到指定的品牌卡："+ splitMRetailContext.getBox().getExplicitBrand());
            }
            CreateMRetailEntity createMRetailEntity = cCreateMRetailEntity(splitMRetailContext.getOrderDetailList(), order, splitMRetailContext.getOrderPaymentList(),splitRetailVipEntity);
            retailEnList.add(createMRetailEntity);
        }else{
            // 未指定品牌
            retailEnList.addAll(splitUnspecifiedGoods(splitMRetailContext, splitVipList, order));
        }
    }

    @Override
    public void updateRetailNo2Order(String orderId,String retailNos){
        // 更新零售单号
        Order updateOrder = new Order();
        updateOrder.setId(orderId);
        updateOrder.setServiceSn(retailNos);
        updateOrder.setFlag(1L);
        orderRepository.updateOrderBySelective(updateOrder);
    }


    @Override
    public String createMRetail2BoJun(Order order,BatchCreateMRetailEntity batchRetailList,List<CreateMRetailEntity> retailEnList){
        if(StringUtil.isNotEmpty(order.getSqbSn()) && StringUtils.isEmpty(batchRetailList.getDdly())){
            // 收钱吧订单
            batchRetailList.setDdly("BOX-SQB");
        }
        // 默认是微信商户号
        if(StringUtils.isNotBlank(order.getMerchantCode())){
            batchRetailList.setCusNo(order.getMerchantCode());
        }else{
            batchRetailList.setCusNo(wxDefaultMchId);
            if(PayChannelEnum.ALIPAY.getCode().toString().equals(order.getPayChannel()) || PayChannelEnum.ALI_CREDIT.getCode().toString().equals(order.getPayChannel())
                    || PayChannelEnum.HB_FQ.getCode().toString().equals(order.getPayChannel())){
                // 支付宝商户号
                batchRetailList.setCusNo(zfbDefaultMchId);
            }
        }
        boolean ifLy = false;
        if(StringUtil.isEmpty(order.getOutPayNo())){
            // 老订单联域
            if(StringUtil.isNotEmpty(order.getMerchantCode())){
                ifLy = true;
            }
        }else{
            // 新订单联域
            if(order.getIsUnion() == 1 && StringUtil.isNotEmpty(order.getMerchantCode())){
                ifLy = true;
            }
        }
        if(ifLy){
            // 联域订单
            batchRetailList.setDdly("LY");
            // 联域,支付方式62改为231
            retailEnList.forEach(e -> {
                e.getPayList().forEach(a -> {
                    if(a.getPayWayId().equals(OrderPaymentPayWayEnum.MONEY.getBjCode())){
                        a.setPayWayId(OrderPaymentPayWayEnum.LIANYU.getBjCode());
                    }
                });
            });
        }
        // 会员临时转移单判断
        List<Long> storeId = retailEnList.stream().map(CreateMRetailEntity::getStoreId).collect(Collectors.toList());
        Map<Long, Long> vipTransMap = icVipTemptransService.checkAndGetVipTrans(storeId);
        if(vipTransMap != null){
            retailEnList.forEach(e -> {
                Long destStoreId = vipTransMap.get(e.getStoreId());
                e.setStoreId(destStoreId);
            });
        }
        // 补充促销策略
        getBoJunPromo(retailEnList,order);
        batchRetailList.setRetailList(retailEnList);
        // 生成零售单
        return retailService.batchCreateMRetail(batchRetailList);
    }

    private void getBoJunPromo(List<CreateMRetailEntity> retailEnList,Order order){
        // 补充促销策略
        List<BOrderPromotion> bOrderPromotions = ibOrderPromotionService.compensateAndGet(order.getId());
        if(CollectionUtils.isNotEmpty(bOrderPromotions)){
            for (CreateMRetailEntity createMRetailEntity: retailEnList) {
                for (CreateMRetailEntity.Product product: createMRetailEntity.getProductList()) {
                    List<BOrderPromotion> productPromoList = bOrderPromotions.stream().filter(e -> product.getCodeId().equals(e.getSkuId())).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(productPromoList)) {
                        List<BoJunPromoEntity> boJunPromoEntities = productPromoList.stream().map(a -> {
                            BoJunPromoEntity boJunPromoEntity = new BoJunPromoEntity();
                            boJunPromoEntity.setPromoId(a.getPromoId());
                            boJunPromoEntity.setPromoType(a.getPromoType());
                            return boJunPromoEntity;
                        }).collect(Collectors.toList());
                        product.setPromoList(boJunPromoEntities);
                    }
                }
            }
        }
    }
    @Override
    public void imitateCreateRetail(Order order,BatchCreateMRetailEntity batchRetailList,List<CreateMRetailEntity> retailEnList){
        if(StringUtil.isNotEmpty(order.getSqbSn()) && StringUtils.isEmpty(batchRetailList.getDdly())){
            // 收钱吧订单
            batchRetailList.setDdly("BOX-SQB");
        }
        // 默认是微信商户号
        batchRetailList.setCusNo(wxDefaultMchId);
        if(PayChannelEnum.ALIPAY.getCode().toString().equals(order.getPayChannel()) || PayChannelEnum.ALI_CREDIT.getCode().toString().equals(order.getPayChannel())){
            // 支付宝商户号
            batchRetailList.setCusNo(zfbDefaultMchId);
        }
        // 会员临时转移单判断
        List<Long> storeId = retailEnList.stream().map(CreateMRetailEntity::getStoreId).collect(Collectors.toList());
        Map<Long, Long> vipTransMap = icVipTemptransService.checkAndGetVipTrans(storeId);
        if(vipTransMap != null){
            retailEnList.forEach(e -> {
                Long destStoreId = vipTransMap.get(e.getStoreId());
                e.setStoreId(destStoreId);
            });
        }
        batchRetailList.setRetailList(retailEnList);
        // 模拟生成零售单
        retailService.imitateBatchCreateMRetail(batchRetailList);
    }

    @Override
    public void syncRetail2PayItem(String retailNos,String orderNo){
        List<String> retailNoList = Arrays.asList(retailNos.split(","));
        LambdaQueryWrapper<MRetail>  mRetailQueryWrapper= new LambdaQueryWrapper<>();
        mRetailQueryWrapper.in(MRetail :: getDocno,retailNoList);
        mRetailQueryWrapper.select(MRetail :: getId,MRetail :: getOrgdocno,MRetail :: getDocno);
        List<MRetail> retailList = imRetailService.list(mRetailQueryWrapper);
        if(CollectionUtils.isEmpty(retailList)){
            return;
        }
        List<MRetail> okRetailNoList = new ArrayList<>();
        retailList.forEach(e -> {
            if(StringUtils.isNotBlank(e.getOrgdocno())){
                okRetailNoList.add(getOrigDocno(e.getOrgdocno()));
            }else{
                okRetailNoList.add(e);
            }
        });
        List<Long> retailIdList = okRetailNoList.stream().map(MRetail::getId).collect(Collectors.toList());
        // 批量获取零售单详情
        LambdaQueryWrapper<MRetailitem>  mRetailItemQueryWrapper= new LambdaQueryWrapper<>();
        mRetailItemQueryWrapper.in(MRetailitem :: getmRetailId,retailIdList);
        mRetailItemQueryWrapper.select(MRetailitem :: getId,MRetailitem :: getmRetailId,MRetailitem :: getmProductaliasId);
        List<MRetailitem> retailItemList = imRetailitemService.list(mRetailItemQueryWrapper);
        if(CollectionUtils.isEmpty(retailItemList)){
            return;
        }
        Map<Long, List<MRetailitem>> map = retailItemList.stream().collect(Collectors.groupingBy(MRetailitem::getmRetailId));
        // 更新伯俊实付中间表数据
        QueryWrapper<EbMpayItem> wrapper = new QueryWrapper<>();
        wrapper.isNull("ret_id");
        wrapper.eq("order_no",orderNo);
        List<EbMpayItem> list = ebMpayItemService.list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<EbMpayItem> updateList = new ArrayList<>();
        for(Long key : map.keySet()){
            List<MRetailitem> thisItems = map.get(key);
            List<Long> skuIds = thisItems.stream().map(MRetailitem::getmProductaliasId).collect(Collectors.toList());
            MRetail mRetail = okRetailNoList.stream().filter(e -> e.getId().equals(key)).findFirst().orElse(null);
            for(EbMpayItem payItem : list){
             if(ObjectUtils.isNotEmpty(mRetail) && skuIds.contains(payItem.getGoodsId())){
                 EbMpayItem ebMpayItem = new EbMpayItem();
                 ebMpayItem.setId(payItem.getId());
                 ebMpayItem.setRetId(mRetail.getId());
                 ebMpayItem.setRetNo(mRetail.getDocno());
                 updateList.add(ebMpayItem);
             }
            }
        }
        ebMpayItemService.updateBatchById(updateList);

        // 同步峰哥ERP实付中间表
        retailService.sync2ErpPayItem(retailIdList);
    }




    private MRetail getOrigDocno(String orgDocno){
        // 集合店单子，找原单
        LambdaQueryWrapper<MRetail> fRetailWrapper = new LambdaQueryWrapper<>();
        fRetailWrapper.eq(MRetail :: getDocno,orgDocno);
        fRetailWrapper.select(MRetail :: getId,MRetail :: getDocno,MRetail :: getOrgdocno);
        List<MRetail> fmRetailList = imRetailService.list(fRetailWrapper);
        return fmRetailList.get(0);
    }

    @Override
    public void sendBack(String boxId,String boxReturnId,String expressNo){
        BoxWithBLOBs box = boxRepository.findById(boxId);
        if (BoxTypeEnum.SALESBOX.getCode().equals(box.getType())){
            // 导购服务单不推送
            return;
        }
        if (box.getIfWms() != 1){
            return;
        }
        BoxReturn boxReturn = boxReturnRepository.selectById(boxReturnId);
        List<BoxReturnDetails> boxReturnDetails = boxReturnRepository.selectByBoxReturnId(boxReturnId);
        List<String> boxDetailIds = boxReturnDetails.stream().map(BoxReturnDetails::getBoxDetailId).collect(Collectors.toList());
        List<BoxDetailsWithBLOBs> returnProduct = boxRepository.findDetailByIds(boxDetailIds);
        this.sendBack(box.getBoxSn(),boxReturn.getOrderSn(),expressNo,returnProduct,boxReturnDetails,boxReturn.getIfVirBack());
    }
    @Override
    public void retryOrderBack2jst(String boxReturnId,List<String> returnDetailIds,List<String> bReturnMallretIds){
        BoxReturn boxReturn = boxReturnRepository.selectById(boxReturnId);
        if (ObjectUtils.isEmpty(boxReturn)){
            return;
        }
        BoxWithBLOBs box = boxRepository.findById(boxReturn.getBoxId());
        if (BoxTypeEnum.SALESBOX.getCode().equals(box.getType())){
            return;
        }
        if (box.getIfWms() != 1){
            return;
        }
        LambdaQueryWrapper<Express> expressLambdaQueryWrapper = new LambdaQueryWrapper<>();
        expressLambdaQueryWrapper.eq(Express::getOrigId,boxReturnId);
        List<Express> expressList = expressService.list(expressLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(expressList)){
            return;
        }
        String expressNo = expressList.get(0).getExpressNo();
        List<BoxReturnDetails> boxReturnDetails = boxReturnRepository.selectByBoxReturnId(boxReturnId);
        boxReturnDetails = boxReturnDetails.stream().filter(e -> returnDetailIds.contains(e.getId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(boxReturnDetails)){
            return;
        }
        List<String> boxDetailIds = boxReturnDetails.stream().map(BoxReturnDetails::getBoxDetailId).collect(Collectors.toList());
        List<BoxDetailsWithBLOBs> returnProduct = boxRepository.findDetailByIds(boxDetailIds);
        CreateEbMallRetContext createEbMallRetContext = new CreateEbMallRetContext();
        createEbMallRetContext.setSourceCode(boxReturn.getOrderSn());
        createEbMallRetContext.setExpressNo(expressNo);
        createEbMallRetContext.setType(MallRetTypeEnum.BOX_BACK.getKey());
        createEbMallRetContext.setTotPrice(new BigDecimal(0));
        createEbMallRetContext.setBoxSn(box.getBoxSn());
        List<String> skuIds = returnProduct.stream().map(BoxDetailsWithBLOBs::getProductId).collect(Collectors.toList());
        List<MProductAlias> mProductAliases = imProductAliasService.listByIds(skuIds);
        List<BoxReturnDetails> finalBoxReturnDetails = boxReturnDetails;
        List<CreateEbMallRetContext.MallRetItem> itemList = returnProduct.stream().map(e -> {
            CreateEbMallRetContext.MallRetItem mallRetItem = new CreateEbMallRetContext.MallRetItem();
            mallRetItem.setSkuId(Long.valueOf(e.getProductId()));
            mallRetItem.setSku(e.getSku());
            mallRetItem.setSpuId(Long.valueOf(e.getOutId()));
            BoxReturnDetails returnDetail = finalBoxReturnDetails.stream().filter(b -> b.getBoxDetailId().equals(e.getId())).findFirst().orElse(null);
            if(ObjectUtils.isEmpty(returnDetail)){
                throw new BoxException("未查询到还货商品");
            }
            MProductAlias mProductAlias = mProductAliases.stream().filter(a -> a.getId().equals(Long.valueOf(e.getProductId()))).findFirst().orElse(null);
            if(ObjectUtils.isEmpty(mProductAlias)){
                throw new BoxException("未查询到商品");
            }
            mallRetItem.setReturnDetailId(returnDetail.getId());
            mallRetItem.setAttrId(mProductAlias.getmAttributesetinstanceId());
            mallRetItem.setQty(Long.valueOf(e.getProductQuantity()));
            mallRetItem.setPayWayList(new ArrayList<>());
            mallRetItem.setPrice(BigDecimal.ZERO);
            return mallRetItem;
        }).collect(Collectors.toList());
        createEbMallRetContext.setMallRetItems(itemList);
        // List<BReturnMallret> mallRetList = createMallRet(createEbMallRetContext);
        if(checkIfPush(createEbMallRetContext)){
            log.info("查询到已存在聚水潭,无需重复推送,boxSn={}",createEbMallRetContext.getBoxSn());
            return;
        }
        push2JST(createEbMallRetContext);
        // 修改监控表渠道为聚水潭
        List<BReturnMallret> updateList = bReturnMallretIds.stream().map(e -> {
            BReturnMallret bReturnMallret = new BReturnMallret();
            bReturnMallret.setId(e);
            bReturnMallret.setChannel(2L);
            return bReturnMallret;
        }).collect(Collectors.toList());
        ibReturnMallretService.updateBatchById(updateList);
    }

    @Override
    public void sendBack(String boxSn,String sourceCode,String expressNo,List<BoxDetailsWithBLOBs> detailList,List<BoxReturnDetails> boxReturnDetailList,Integer ifVirBack){
        CreateEbMallRetContext createEbMallRetContext = new CreateEbMallRetContext();
        createEbMallRetContext.setSourceCode(sourceCode);
        createEbMallRetContext.setExpressNo(expressNo);
        createEbMallRetContext.setType(MallRetTypeEnum.BOX_BACK.getKey());
        if(ifVirBack == 1){
            createEbMallRetContext.setType(MallRetTypeEnum.BOX_XNBACK.getKey());
        }
        createEbMallRetContext.setTotPrice(new BigDecimal(0));
        createEbMallRetContext.setBoxSn(boxSn);
        List<String> skuIds = detailList.stream().map(BoxDetailsWithBLOBs::getProductId).collect(Collectors.toList());
        List<MProductAlias> mProductAliases = imProductAliasService.listByIds(skuIds);
        List<CreateEbMallRetContext.MallRetItem> itemList = detailList.stream().map(e -> {
            CreateEbMallRetContext.MallRetItem mallRetItem = new CreateEbMallRetContext.MallRetItem();
            mallRetItem.setSkuId(Long.valueOf(e.getProductId()));
            mallRetItem.setSku(e.getSku());
            mallRetItem.setSpuId(Long.valueOf(e.getOutId()));
            BoxReturnDetails returnDetail = boxReturnDetailList.stream().filter(b -> b.getBoxDetailId().equals(e.getId())).findFirst().orElse(null);
            if(ObjectUtils.isEmpty(returnDetail)){
                throw new BoxException("未查询到还货商品");
            }
            MProductAlias mProductAlias = mProductAliases.stream().filter(a -> a.getId().equals(Long.valueOf(e.getProductId()))).findFirst().orElse(null);
            if(ObjectUtils.isEmpty(mProductAlias)){
                throw new BoxException("未查询到商品");
            }
            mallRetItem.setReturnDetailId(returnDetail.getId());
            mallRetItem.setAttrId(mProductAlias.getmAttributesetinstanceId());
            mallRetItem.setQty(Long.valueOf(e.getProductQuantity()));
            mallRetItem.setPayWayList(new ArrayList<>());
            mallRetItem.setPrice(BigDecimal.ZERO);
            return mallRetItem;
        }).collect(Collectors.toList());
        createEbMallRetContext.setMallRetItems(itemList);
        BoxReturnDetails boxReturnDetails = boxReturnDetailList.get(0);

       // List<BReturnMallret> mallRetList = createMallRet(createEbMallRetContext);
        // 插入中间表
        batchInsertReturnMallret(createEbMallRetContext,boxReturnDetails);
        // 先查后推，如果已存在，则不推送
        if(checkIfPush(createEbMallRetContext)){
            log.info("查询到已存在聚水潭,无需重复推送,boxSn={}",createEbMallRetContext.getBoxSn());
            return;
        }
        // 推送聚水潭
        push2JST(createEbMallRetContext);
    }

    public void batchInsertReturnMallret(CreateEbMallRetContext createEbMallRetContext,BoxReturnDetails boxReturnDetails ){
        // 查询是否已存在中间表，存在则无需插入
        QueryWrapper<BReturnMallret> queryWrapper = new QueryWrapper();
        queryWrapper.eq("box_return_id",boxReturnDetails.getBoxReturnId());
        queryWrapper.eq("is_del",0);
        List<BReturnMallret> list = ibReturnMallretService.list(queryWrapper);
        if(CollectionUtils.isNotEmpty(list)){
            return;
        }
        Date createTime = new Date();
        List<BReturnMallret> mallRetList = new ArrayList<>();
        for(CreateEbMallRetContext.MallRetItem mallRetItem : createEbMallRetContext.getMallRetItems()) {
            BReturnMallret bReturnMallret = new BReturnMallret();
            bReturnMallret.setBoxReturnDetailId(mallRetItem.getReturnDetailId());
            bReturnMallret.setId(idLeaf.getId());
            bReturnMallret.setBoxReturnId(boxReturnDetails.getBoxReturnId());
            bReturnMallret.setStatus(0L);
            bReturnMallret.setCreateTime(createTime);
            bReturnMallret.setUpdateTime(createTime);
            bReturnMallret.setIsDel(0L);
            bReturnMallret.setChannel(2L);
            mallRetList.add(bReturnMallret);
        }
        if(CollectionUtils.isEmpty(mallRetList)){
            return;
        }
        log.info("保存到box还货中间表");
        // 存入中间状态监控表
        ibReturnMallretService.saveBatch(mallRetList);
    }


    @Override
    public void refund(String boxId,String boxRefundId,String expressNo){
        BoxWithBLOBs box = boxRepository.findById(boxId);
        if (BoxTypeEnum.SALESBOX.getCode().equals(box.getType())){
            // 导购服务单不推送
            return;
        }
        if (box.getIfWms() != 1){
            return;
        }
        BoxRefund boxRefund = boxRefundRepository.findById(boxRefundId);
        List<BoxRefundDetails> refundDetailsList = boxRefundRepository.findDetailsByRefundId(boxRefundId);
        List<OrderDetail> orderDetails = orderRepository.selectOrderDetailListByOrderId(boxRefund.getOrderId());
        refund(box.getBoxSn(),boxRefund.getRefundSn(),expressNo,BigDecimal.valueOf(boxRefund.getRefundAmount()),orderDetails,refundDetailsList);
    }

    @Override
    public void refund(String boxSn,String sourceCode,String expressNo,BigDecimal totPrice,List<OrderDetail> orderDetailList,List<BoxRefundDetails> boxRefundDetails) {
        CreateEbMallRetContext createEbMallRetContext = new CreateEbMallRetContext();
        createEbMallRetContext.setSourceCode(sourceCode);
        createEbMallRetContext.setExpressNo(expressNo);
        createEbMallRetContext.setType(MallRetTypeEnum.BOX_RET.getKey());
        createEbMallRetContext.setBoxSn(boxSn);
        createEbMallRetContext.setTotPrice(totPrice);

        List<String> skuIds = orderDetailList.stream().map(OrderDetail::getProductId).collect(Collectors.toList());
        List<MProductAlias> mProductAliases = imProductAliasService.listByIds(skuIds);
        // 组装退款商品
        List<CreateEbMallRetContext.MallRetItem> itemList = boxRefundDetails.stream().map(e -> {
            OrderDetail orderDetail = orderDetailList.stream().filter(a -> e.getOrderDetailId().equals(a.getId())).findFirst().orElse(new OrderDetail());
            CreateEbMallRetContext.MallRetItem mallRetItem = new CreateEbMallRetContext.MallRetItem();
            mallRetItem.setSkuId(Long.valueOf(orderDetail.getProductId()));
            mallRetItem.setSku(orderDetail.getSku());
            mallRetItem.setSpuId(Long.valueOf(orderDetail.getOutId()));
            MProductAlias mProductAlias = mProductAliases.stream().filter(a -> a.getId().equals(Long.valueOf(orderDetail.getProductId()))).findFirst().orElse(null);
            if(ObjectUtils.isEmpty(mProductAlias)){
                throw new BoxException("未查询到商品");
            }
            mallRetItem.setReturnDetailId(e.getId());
            mallRetItem.setAttrId(mProductAlias.getmAttributesetinstanceId());
            mallRetItem.setQty(Long.valueOf(e.getRefundQty()));
            BigDecimal price = BigDecimal.valueOf(e.getRefundAmount());
            BigDecimal qty = new BigDecimal(e.getRefundQty());
            BigDecimal aPrice = price.divide(qty,2, RoundingMode.HALF_UP);
            mallRetItem.setPrice(aPrice);
            List<CreateEbMallRetContext.PayWay> payWayList = new ArrayList<>();
            CreateEbMallRetContext.PayWay payWay = new CreateEbMallRetContext.PayWay();
            payWay.setPayWayId(62L);
            payWay.setTotPrice(aPrice);
            payWayList.add(payWay);
            if(ObjectUtils.isNotEmpty(orderDetail.getVoucherAmount()) &&
                    BigDecimal.ZERO.compareTo(orderDetail.getVoucherAmount()) < 0){
                // 计算单个商品的券分摊金额
                BigDecimal aVouPrice = orderDetail.getVoucherAmount().divide(new BigDecimal(orderDetail.getProductQuantity()),2, RoundingMode.HALF_UP);
                mallRetItem.setPrice(mallRetItem.getPrice().add(aVouPrice));
                CreateEbMallRetContext.PayWay vouPayWay = new CreateEbMallRetContext.PayWay();
                vouPayWay.setPayWayId(77L);
                vouPayWay.setTotPrice(aVouPrice);
                payWayList.add(vouPayWay);
            }
            // 储值卡
            if(ObjectUtils.isNotEmpty(orderDetail.getBalanceAmt()) &&
                    BigDecimal.ZERO.compareTo(orderDetail.getBalanceAmt()) < 0){
                // 计算单个商品的储值卡分摊金额
                BigDecimal balancePrice = orderDetail.getBalanceAmt().divide(new BigDecimal(orderDetail.getProductQuantity()),2, RoundingMode.HALF_UP);
                mallRetItem.setPrice(mallRetItem.getPrice().add(balancePrice));
                CreateEbMallRetContext.PayWay balancePayWay = new CreateEbMallRetContext.PayWay();
                balancePayWay.setPayWayId(OrderPaymentPayWayEnum.BALANCE.getBjCode());
                balancePayWay.setTotPrice(balancePrice);
                payWayList.add(balancePayWay);
            }
            // 储值卡
            if(ObjectUtils.isNotEmpty(orderDetail.getShopVouAmt()) &&
                    BigDecimal.ZERO.compareTo(orderDetail.getShopVouAmt()) < 0){
                // 计算单个商品的商场代金券分摊金额
                BigDecimal shopVouPrice = orderDetail.getShopVouAmt().divide(new BigDecimal(orderDetail.getProductQuantity()),2, RoundingMode.HALF_UP);
                mallRetItem.setPrice(mallRetItem.getPrice().add(shopVouPrice));
                CreateEbMallRetContext.PayWay shopVouPayWay = new CreateEbMallRetContext.PayWay();
                shopVouPayWay.setPayWayId(OrderPaymentPayWayEnum.SHOP_VOU.getBjCode());
                shopVouPayWay.setTotPrice(shopVouPrice);
                payWayList.add(shopVouPayWay);
            }
            mallRetItem.setPayWayList(payWayList);
            return mallRetItem;
        }).collect(Collectors.toList());
        createEbMallRetContext.setMallRetItems(itemList);

        // createMallRet(createEbMallRetContext);
        push2JST(createEbMallRetContext);
    }


    @Override
    public void syncTransState() {
        // 获取未发货状态的新服务单
        List<BoxWithBLOBs> list = boxMapper.getWmsUnShipList();
        if(list.size() == 0){
            return;
        }
        // 加载快递类型
        List<EbExpress> ebExpressList = ebExpressService.list();
        Map<Long, List<EbExpress>> ebExpressMap = ebExpressList.stream().collect(Collectors.groupingBy(EbExpress::getId));
        // 批量处理
        List<List<BoxWithBLOBs>> partition = Lists.partition(list, 50);
        for(List<BoxWithBLOBs> boxList : partition){
            List<String> boxIds = boxList.stream().map(BoxWithBLOBs::getId).collect(Collectors.toList());
            QueryWrapper<Express> expressQueryWrapper = new QueryWrapper<>();
            expressQueryWrapper.in("orig_id",boxIds);
            expressQueryWrapper.select("id,express_docno as expressDocno,orig_id as origId");
            List<Express> expressList = expressService.list(expressQueryWrapper);
            QueryWrapper<MTransfer> mTransferQueryWrapper = new QueryWrapper<>();
            List<String> mTransferIds = expressList.stream().map(Express::getExpressDocno).collect(Collectors.toList());
            mTransferQueryWrapper.in("id",mTransferIds);
            mTransferQueryWrapper.eq("out_status",2);
            mTransferQueryWrapper.select("id,express,fastno,in_status as inStatus,out_status as outStatus,wing_docno as wingDocno,tot_qty as totQty,tot_qtyout as totQtyout");
            List<MTransfer> mTransferList = imTransferService.list(mTransferQueryWrapper);
            if(mTransferList.size() == 0){
                continue;
            }
            // 全部发货
            List<MTransfer> allSendList = mTransferList.stream().filter(e -> e.getTotQty().equals(e.getTotQtyout())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(allSendList)){
                allSendList.forEach(e -> {
                    BoxWithBLOBs boxWithBLOBs = boxList.stream().filter(a -> e.getWingDocno().equals(a.getBoxSn())).findFirst().orElse(null);
                    if(ObjectUtils.isEmpty(boxWithBLOBs)){
                        return;
                    }
                    if(ObjectUtils.isEmpty(e.getExpress())){
                        return;
                    }
                    List<EbExpress> mTransExpressList = ebExpressMap.get(e.getExpress());
                    if(CollectionUtils.isNotEmpty(mTransExpressList)){
                        try {
                            boxTemplate.execute(action -> {
                                syncBoxStatusJobService.dealAfterShip(boxWithBLOBs, e.getFastno(), "-1", mTransExpressList.get(0).getName(), "WMS发货成功");
                                return action;
                            });
                        }catch(Exception ex){
                            log.error("WMS发货状态同步异常,boxId={}",boxWithBLOBs.getId(),ex);
                        }
                    }
                });
            }
            // 部分发货
            List<MTransfer> partSendList = mTransferList.stream().filter(e -> !e.getTotQty().equals(e.getTotQtyout())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(partSendList)){
                List<Long> transferIds = partSendList.stream().map(MTransfer::getId).collect(Collectors.toList());
                // 获取出库数量为0的商品
                QueryWrapper<MTransferitem> mTransferitemQueryWrapper = new QueryWrapper<>();
                mTransferitemQueryWrapper.in("m_transfer_id",transferIds);
                mTransferitemQueryWrapper.eq("qtyout",0);
                mTransferitemQueryWrapper.select("id,m_transfer_id as mTransferId,m_productalias_id as mProductaliasId,qty,qtyout");
                List<MTransferitem> mTransferitemList = imTransferitemService.list(mTransferitemQueryWrapper);
                List<String> skuIds = mTransferitemList.stream().map(e -> e.getmProductaliasId().toString()).distinct().collect(Collectors.toList());
                Map<Long, List<MTransferitem>> transItemMap = mTransferitemList.stream().collect(Collectors.groupingBy(MTransferitem::getmTransferId));
                // 获取缺货的boxId
                List<Express> partExpressList = expressList.stream().filter(e -> {
                    return partSendList.stream().anyMatch(a -> a.getId().equals(Long.valueOf(e.getExpressDocno())));
                }).collect(Collectors.toList());
                List<String> partBoxIds = partExpressList.stream().map(Express::getOrigId).collect(Collectors.toList());
                QueryWrapper<BoxDetailsWithBLOBs> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("status", UltimaBoxDetailsStatusEnum.UNSEND.getCode());
                queryWrapper.eq("eb",0);
                queryWrapper.in("box_id",partBoxIds);
                queryWrapper.in("product_id",skuIds);
                // 取消商品
                List<BoxDetailsWithBLOBs> detailsListList = boxDetailsService.list(queryWrapper);
                partSendList.forEach(e -> {
                    BoxWithBLOBs boxWithBLOBs = boxList.stream().filter(a -> e.getWingDocno().equals(a.getBoxSn())).findFirst().orElse(null);
                    if(ObjectUtils.isEmpty(boxWithBLOBs)){
                        return;
                    }
                    List<EbExpress> mTransExpressList = ebExpressMap.get(e.getExpress());
                    // 缺货商品
                    List<MTransferitem> lackStockItemList = transItemMap.get(e.getId());
                    List<Long> thePartSkuIds = lackStockItemList.stream().map(MTransferitem::getmProductaliasId).collect(Collectors.toList());
                    List<BoxDetailsWithBLOBs> theBoxDetailList = detailsListList.stream().filter(a -> {
                        return a.getBoxId().equals(boxWithBLOBs.getId()) && thePartSkuIds.contains(Long.valueOf(a.getProductId()));
                    }).collect(Collectors.toList());
                    List<BoxDetailsWithBLOBs> updateBoxDetailList = theBoxDetailList.stream().map(a -> {
                        BoxDetailsWithBLOBs updateDetailsWithBLOBs = new BoxDetailsWithBLOBs();
                        updateDetailsWithBLOBs.setId(a.getId());
                        updateDetailsWithBLOBs.setStatus(UltimaBoxDetailsStatusEnum.CANCEL.getCode().longValue());
                        return updateDetailsWithBLOBs;
                    }).collect(Collectors.toList());
                    String partSku = theBoxDetailList.stream().map(BoxDetailsWithBLOBs::getSku).collect(Collectors.joining(","));
                    String company = CollectionUtils.isNotEmpty(mTransExpressList) ? mTransExpressList.get(0).getName() : "";
                    try {
                        boxTemplate.execute(action -> {
                            // 先将其他商品置为缺货
                            boxDetailsService.updateBatchById(updateBoxDetailList);
                            boxActionService.createBoxAction(boxWithBLOBs.getId(),partSku+"出库数量为0,置为取消",boxWithBLOBs.getStatus(),"-1");
                            // 判断主单express是否需要取消
                            QueryWrapper<BoxDetailsWithBLOBs> theQueryWrapper = new QueryWrapper<>();
                            theQueryWrapper.eq("status", UltimaBoxDetailsStatusEnum.UNSEND.getCode());
                            theQueryWrapper.eq("eb",0);
                            theQueryWrapper.eq("box_id",boxWithBLOBs.getId());
                            List<BoxDetailsWithBLOBs> theNotCancelDetailList = boxDetailsService.list(theQueryWrapper);
                            if(CollectionUtils.isEmpty(theNotCancelDetailList)){
                                QueryWrapper<Express> theExpressQueryWrapper = new QueryWrapper<>();
                                theExpressQueryWrapper.eq("orig_id",boxWithBLOBs.getId());
                                theExpressQueryWrapper.eq("type",10);
                                theExpressQueryWrapper.select("id");
                                Express boxExpress = expressService.getOne(theExpressQueryWrapper);
                                if(ObjectUtils.isNotEmpty(boxExpress)){
                                    Express updateExpress = new Express();
                                    updateExpress.setId(boxExpress.getId());
                                    updateExpress.setStatus(40L);
                                    updateExpress.setUpdateTime(new Date());
                                    expressService.updateById(updateExpress);
                                }
                            }
                            // 检测服务单是否需要取消
                            boxEbService.cancelBox(boxWithBLOBs);
                            BoxWithBLOBs box = boxRepository.findById(boxWithBLOBs.getId());
                            List<BoxDetailsWithBLOBs> details = boxRepository.selectDetailsListByBoxId(boxWithBLOBs.getId());
                            // 获取非内淘商品未取消状态的数量
                            long count = details.stream().filter(de -> {
                                return (de.getEb() == null || de.getEb().intValue() == 0) && !de.getStatus().equals(UltimaBoxDetailsStatusEnum.CANCEL.getCode().longValue());
                            }).count();
                            if(count > 0 && (Long.valueOf(UltimaBoxStatusEnum.WAIT_SEND.getCode()).equals(box.getStatus()) || Long.valueOf(UltimaBoxStatusEnum.SENDING.getCode()).equals(box.getStatus()))){
                                // 发货
                                syncBoxStatusJobService.dealAfterShip(boxWithBLOBs, e.getFastno(), "-1", company, "WMS发货成功");
                            }
                            // 发送缺货消息给搭配师和客服
                            Fashioner fashioner = fashionerService.findById(box.getCreateFasId());
                            SysTemplateMsgEntity sysTemplateMsgEntity = new SysTemplateMsgEntity();
                            sysTemplateMsgEntity.setPhone(fashioner.getPhone() + "," + kefuPhones);
                            sysTemplateMsgEntity.setTemplateCode(SysMsgTemplateCodeEnum.BOX_SEND_ERROR.getCode());
                            HashMap<String, String> params = new HashMap<>();
                            params.put("boxSn",box.getBoxSn());
                            params.put("sku",partSku);
                            params.put("id",box.getId());
                            sysTemplateMsgEntity.setParams(params);
                            sysMessageService.sendTemplateSysMsg2User(sysTemplateMsgEntity);

                            return action;
                        });
                        // 触发部分、全部取消的消息 BOX+JIC
                        producerUtil.send(JSON.toJSONBytes(boxWithBLOBs.getId()), MsgTagUtil.DELIVER_CANCEL_OR_NO_STOCK_MSG_TAG);
                    }catch(Exception ex){
                        log.error("WMS发货状态同步异常,boxId={}",boxWithBLOBs.getId(),ex);
                    }

                });
            }

        }


    }



    @Override
    public void syncTryAfterBuyTransState() {
        // 获取未发货状态的新服务单
        List<BoxWithBLOBs> list = boxMapper.getTryAfterBuyUnShipList();
        if(list.size() == 0){
            return;
        }

        // 验证黑名单然后打印log
//        checkAndLogCustomer(list);
        // 加载快递类型
        List<EbExpress> ebExpressList = ebExpressService.list();
        Map<Long, List<EbExpress>> ebExpressMap = ebExpressList.stream().collect(Collectors.groupingBy(EbExpress::getId));
        // 批量处理
        List<List<BoxWithBLOBs>> partition = Lists.partition(list, 50);
        for(List<BoxWithBLOBs> boxList : partition){
            List<String> boxIds = boxList.stream().map(BoxWithBLOBs::getId).collect(Collectors.toList());
            QueryWrapper<Express> expressQueryWrapper = new QueryWrapper<>();
            expressQueryWrapper.in("orig_id",boxIds);
            expressQueryWrapper.select("id,express_docno as expressDocno,orig_id as origId");
            List<Express> expressList = expressService.list(expressQueryWrapper);
            QueryWrapper<MTransfer> mTransferQueryWrapper = new QueryWrapper<>();
            List<String> mTransferIds = expressList.stream().map(Express::getExpressDocno).collect(Collectors.toList());
            mTransferQueryWrapper.in("id",mTransferIds);
            mTransferQueryWrapper.eq("out_status",2);
            mTransferQueryWrapper.select("id,express,fastno,in_status as inStatus,out_status as outStatus,wing_docno as wingDocno,tot_qty as totQty,tot_qtyout as totQtyout");
            List<MTransfer> mTransferList = imTransferService.list(mTransferQueryWrapper);
            if(mTransferList.size() == 0){
                continue;
            }
            // 全部发货
            List<MTransfer> allSendList = mTransferList.stream().filter(e -> e.getTotQty().equals(e.getTotQtyout())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(allSendList)){
                allSendList.forEach(e -> {
                    BoxWithBLOBs boxWithBLOBs = boxList.stream().filter(a -> e.getWingDocno().equals(a.getBoxSn())).findFirst().orElse(null);
                    if(ObjectUtils.isEmpty(boxWithBLOBs)){
                        return;
                    }
                    if(ObjectUtils.isEmpty(e.getExpress())){
                        return;
                    }
                    List<EbExpress> mTransExpressList = ebExpressMap.get(e.getExpress());
                    if(CollectionUtils.isNotEmpty(mTransExpressList)){
                        try {
                            boxTemplate.execute(action -> {
                                syncBoxStatusJobService.dealAfterShip(boxWithBLOBs, e.getFastno(), "-1", mTransExpressList.get(0).getName(), "WMS发货成功");
                                return action;
                            });
                        }catch(Exception ex){
                            log.error("WMS发货状态同步异常,boxId={}",boxWithBLOBs.getId(),ex);
                        }
                    }
                });
            }
            // 部分发货
            List<MTransfer> partSendList = mTransferList.stream().filter(e -> !e.getTotQty().equals(e.getTotQtyout())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(partSendList)){
                List<Long> transferIds = partSendList.stream().map(MTransfer::getId).collect(Collectors.toList());
                // 获取出库数量为0的商品
                QueryWrapper<MTransferitem> mTransferitemQueryWrapper = new QueryWrapper<>();
                mTransferitemQueryWrapper.in("m_transfer_id",transferIds);
                mTransferitemQueryWrapper.eq("qtyout",0);
                mTransferitemQueryWrapper.select("id,m_transfer_id as mTransferId,m_productalias_id as mProductaliasId,qty,qtyout");
                List<MTransferitem> mTransferitemList = imTransferitemService.list(mTransferitemQueryWrapper);
                List<String> skuIds = mTransferitemList.stream().map(e -> e.getmProductaliasId().toString()).distinct().collect(Collectors.toList());
                Map<Long, List<MTransferitem>> transItemMap = mTransferitemList.stream().collect(Collectors.groupingBy(MTransferitem::getmTransferId));
                // 获取缺货的boxId
                List<Express> partExpressList = expressList.stream().filter(e -> {
                    return partSendList.stream().anyMatch(a -> a.getId().equals(Long.valueOf(e.getExpressDocno())));
                }).collect(Collectors.toList());
                List<String> partBoxIds = partExpressList.stream().map(Express::getOrigId).collect(Collectors.toList());
                QueryWrapper<BoxDetailsWithBLOBs> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("status", UltimaBoxDetailsStatusEnum.UNSEND.getCode());
                queryWrapper.eq("eb",0);
                queryWrapper.in("box_id",partBoxIds);
                queryWrapper.in("product_id",skuIds);
                // 取消商品
                List<BoxDetailsWithBLOBs> detailsListList = boxDetailsService.list(queryWrapper);
                partSendList.forEach(e -> {
                    BoxWithBLOBs boxWithBLOBs = boxList.stream().filter(a -> e.getWingDocno().equals(a.getBoxSn())).findFirst().orElse(null);
                    if(ObjectUtils.isEmpty(boxWithBLOBs)){
                        return;
                    }
                    List<EbExpress> mTransExpressList = ebExpressMap.get(e.getExpress());
                    // 缺货商品
                    List<MTransferitem> lackStockItemList = transItemMap.get(e.getId());
                    List<Long> thePartSkuIds = lackStockItemList.stream().map(MTransferitem::getmProductaliasId).collect(Collectors.toList());
                    List<BoxDetailsWithBLOBs> theBoxDetailList = detailsListList.stream().filter(a -> {
                        return a.getBoxId().equals(boxWithBLOBs.getId()) && thePartSkuIds.contains(Long.valueOf(a.getProductId()));
                    }).collect(Collectors.toList());
                    List<BoxDetailsWithBLOBs> updateBoxDetailList = theBoxDetailList.stream().map(a -> {
                        BoxDetailsWithBLOBs updateDetailsWithBLOBs = new BoxDetailsWithBLOBs();
                        updateDetailsWithBLOBs.setId(a.getId());
                        updateDetailsWithBLOBs.setStatus(UltimaBoxDetailsStatusEnum.CANCEL.getCode().longValue());
                        return updateDetailsWithBLOBs;
                    }).collect(Collectors.toList());
                    String partSku = theBoxDetailList.stream().map(BoxDetailsWithBLOBs::getSku).collect(Collectors.joining(","));
                    String company = CollectionUtils.isNotEmpty(mTransExpressList) ? mTransExpressList.get(0).getName() : "";
                    try {
                        boxTemplate.execute(action -> {
                            // 先将其他商品置为缺货
                            boxDetailsService.updateBatchById(updateBoxDetailList);
                            boxActionService.createBoxAction(boxWithBLOBs.getId(),partSku+"出库数量为0,置为取消",boxWithBLOBs.getStatus(),"-1");
                            // 判断主单express是否需要取消
                            QueryWrapper<BoxDetailsWithBLOBs> theQueryWrapper = new QueryWrapper<>();
                            theQueryWrapper.eq("status", UltimaBoxDetailsStatusEnum.UNSEND.getCode());
                            theQueryWrapper.eq("eb",0);
                            theQueryWrapper.eq("box_id",boxWithBLOBs.getId());
                            List<BoxDetailsWithBLOBs> theNotCancelDetailList = boxDetailsService.list(theQueryWrapper);
                            if(CollectionUtils.isEmpty(theNotCancelDetailList)){
                                QueryWrapper<Express> theExpressQueryWrapper = new QueryWrapper<>();
                                theExpressQueryWrapper.eq("orig_id",boxWithBLOBs.getId());
                                theExpressQueryWrapper.eq("type",10);
                                theExpressQueryWrapper.select("id");
                                Express boxExpress = expressService.getOne(theExpressQueryWrapper);
                                if(ObjectUtils.isNotEmpty(boxExpress)){
                                    Express updateExpress = new Express();
                                    updateExpress.setId(boxExpress.getId());
                                    updateExpress.setStatus(40L);
                                    updateExpress.setUpdateTime(new Date());
                                    expressService.updateById(updateExpress);
                                }
                            }
                            // 检测服务单是否需要取消
                            boxEbService.cancelBox(boxWithBLOBs);
                            BoxWithBLOBs box = boxRepository.findById(boxWithBLOBs.getId());
                            List<BoxDetailsWithBLOBs> details = boxRepository.selectDetailsListByBoxId(boxWithBLOBs.getId());
                            // 获取非内淘商品未取消状态的数量
                            long count = details.stream().filter(de -> {
                                return (de.getEb() == null || de.getEb().intValue() == 0) && !de.getStatus().equals(UltimaBoxDetailsStatusEnum.CANCEL.getCode().longValue());
                            }).count();
                            if(count > 0 && (Long.valueOf(UltimaBoxStatusEnum.WAIT_SEND.getCode()).equals(box.getStatus()) || Long.valueOf(UltimaBoxStatusEnum.SENDING.getCode()).equals(box.getStatus()))){
                                // 发货
                                syncBoxStatusJobService.dealAfterShip(boxWithBLOBs, e.getFastno(), "-1", company, "WMS发货成功");
                            }
                            // 触发部分、全部取消的消息 BOX+JIC
                            producerUtil.send(JSON.toJSONBytes(boxWithBLOBs.getId()), MsgTagUtil.DELIVER_CANCEL_OR_NO_STOCK_MSG_TAG);
                            return action;
                        });
                    }catch(Exception ex){
                        log.error("WMS发货状态同步异常,boxId={}",boxWithBLOBs.getId(),ex);
                    }

                });
            }

        }


    }

    private void checkAndLogCustomer(List<BoxWithBLOBs> list) {
        for (BoxWithBLOBs box : list) {
            CustomerDetails byUnionId = customerDetailsService.findByUnionId(box.getUnionid());

            Boolean isBlack = customerDetailsService.checkCustomerIsBlack(byUnionId.getId());
            if(isBlack){
                log.error("微商城先试后买订单异常提醒,{},用户变为黑名单用户，请及时跟进用户退回货品或者拦截发货",box.getBoxSn());
            }
        }
    }


    @Override
    public void syncSupplyTransState() {
        // 获取未发货的补货单
        List<NotSendSupplyEntity> notSendSupplyList = boxSupplyService.getNotSendSupplyList();
        if(CollectionUtils.isEmpty(notSendSupplyList)){
            return;
        }
        // 加载快递类型
        List<EbExpress> ebExpressList = ebExpressService.list();
        Map<Long, List<EbExpress>> ebExpressMap = ebExpressList.stream().collect(Collectors.groupingBy(EbExpress::getId));
        // 批量处理
        List<List<NotSendSupplyEntity>> partition = Lists.partition(notSendSupplyList, 50);
        for(List<NotSendSupplyEntity> supplyList : partition){
            List<String> boxIds = supplyList.stream().map(NotSendSupplyEntity::getBoxId).distinct().collect(Collectors.toList());
            List<String> mTransferIds = supplyList.stream().map(NotSendSupplyEntity::getMTransferId).collect(Collectors.toList());
            QueryWrapper<MTransfer> mTransferQueryWrapper = new QueryWrapper<>();
            mTransferQueryWrapper.in("id",mTransferIds);
            mTransferQueryWrapper.eq("out_status",2);
            mTransferQueryWrapper.select("id,express,fastno,in_status as inStatus,out_status as outStatus,wing_docno as wingDocno,tot_qty as totQty,tot_qtyout as totQtyout");
            List<MTransfer> mTransferList = imTransferService.list(mTransferQueryWrapper);
            if(mTransferList.size() == 0){
                continue;
            }
            List<BoxWithBLOBs> boxWithBLOBsList = boxRepository.selectByIds(boxIds);
            QueryWrapper<BoxDetailsWithBLOBs> boxDetailsWithBLOBsQueryWrapper = new QueryWrapper<>();
            boxDetailsWithBLOBsQueryWrapper.in("box_id",boxIds);
            boxDetailsWithBLOBsQueryWrapper.eq("status",0);
            boxDetailsWithBLOBsQueryWrapper.isNotNull("supply_id");
            boxDetailsWithBLOBsQueryWrapper.eq("eb",0);
            List<BoxDetailsWithBLOBs> list = boxDetailsService.list(boxDetailsWithBLOBsQueryWrapper);
            // 部分发货
            List<MTransfer> partSendList = mTransferList.stream().filter(e -> !e.getTotQty().equals(e.getTotQtyout())).collect(Collectors.toList());
            List<MTransferitem> mTransferitemList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(partSendList)) {
                List<Long> transferIds = partSendList.stream().map(MTransfer::getId).collect(Collectors.toList());
                // 获取出库数量为0的商品
                QueryWrapper<MTransferitem> mTransferitemQueryWrapper = new QueryWrapper<>();
                mTransferitemQueryWrapper.in("m_transfer_id", transferIds);
                mTransferitemQueryWrapper.eq("qtyout", 0);
                mTransferitemQueryWrapper.select("id,m_transfer_id as mTransferId,m_productalias_id as mProductaliasId,qty,qtyout");
                mTransferitemList = imTransferitemService.list(mTransferitemQueryWrapper);
            }
            final List<MTransferitem> finalMTransferitemList = mTransferitemList;

            for (MTransfer e : mTransferList) {
                NotSendSupplyEntity sendSupplyEntity = supplyList.stream().filter(a -> Long.valueOf(a.getMTransferId()).equals(e.getId())).findFirst().orElse(null);
                if (ObjectUtils.isEmpty(sendSupplyEntity)) {
                    continue;
                }
                List<BoxDetailsWithBLOBs> detailList = list.stream().filter(a -> {
                    return a.getBoxId().equals(sendSupplyEntity.getBoxId()) && a.getSupplyId().equals(sendSupplyEntity.getSupplyId());
                }).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(detailList)) {
                    continue;
                }
                BoxWithBLOBs boxWithBLOBs = boxWithBLOBsList.stream().filter(a -> a.getId().equals(sendSupplyEntity.getBoxId())).findFirst().orElse(null);
                if (ObjectUtils.isEmpty(boxWithBLOBs)) {
                    continue;
                }
                List<BoxDetailsWithBLOBs> updateBoxDetailList = detailList.stream().map(a -> {
                    BoxDetailsWithBLOBs updateDetailsWithBLOBs = new BoxDetailsWithBLOBs();
                    updateDetailsWithBLOBs.setId(a.getId());
                    updateDetailsWithBLOBs.setStatus(UltimaBoxDetailsStatusEnum.SEND.getCode().longValue());
                    // 判断是否0出库
                    if (!e.getTotQty().equals(e.getTotQtyout())) {
                        long count = finalMTransferitemList.stream().filter(b -> {
                            return b.getmTransferId().equals(Long.valueOf(sendSupplyEntity.getMTransferId())) && b.getmProductaliasId().equals(Long.valueOf(a.getProductId()));
                        }).count();
                        if (count > 0) {
                            updateDetailsWithBLOBs.setStatus(UltimaBoxDetailsStatusEnum.CANCEL.getCode().longValue());
                        }
                    }
                    return updateDetailsWithBLOBs;
                }).collect(Collectors.toList());
                long notSendCount = updateBoxDetailList.stream().filter(item -> item.getStatus().equals(UltimaBoxDetailsStatusEnum.CANCEL.getCode().longValue())).count();
                List<EbExpress> mTransExpressList = ebExpressMap.get(e.getExpress());
                Express updateExpress = new Express();
                updateExpress.setId(sendSupplyEntity.getExpressId());
                if (e.getTotQtyout() == 0L) {
                    updateExpress.setStatus(ExpressStatusEnum.cancel.getCode().longValue());
                } else {
                    updateExpress.setStatus(ExpressStatusEnum.shiped.getCode().longValue());
                }
                BoxSupply updateBoxSupply = new BoxSupply();
                if (CollectionUtils.isNotEmpty(mTransExpressList)) {
                    updateExpress.setExpressName(mTransExpressList.get(0).getName());
                    updateExpress.setExpressNo(e.getFastno());
                    updateBoxSupply.setId(sendSupplyEntity.getSupplyId());
                    updateBoxSupply.setTrackNumber(e.getFastno());
                }

                // 查找地址
                Logistics logistics = null;
                if (!StringUtils.isBlank(boxWithBLOBs.getLogisticsId())) {
                    logistics = logisticsMapper.selectByPrimaryKey(boxWithBLOBs.getLogisticsId());
                }

                try {
                    Logistics finalLogistics = logistics;
                    boxTemplate.execute(action -> {
                        // 更新express
                        expressService.updateById(updateExpress);
                        // 更新商品
                        boxDetailsService.updateBatchById(updateBoxDetailList);
                        // 检查主单是否需要更新
                        boxSupplyService.checkAndUpdateSupplyStatus(sendSupplyEntity.getBoxId(), sendSupplyEntity.getSupplyId());
                        if(ObjectUtils.isNotEmpty(updateBoxSupply.getTrackNumber())){
                            boxSupplyService.updateById(updateBoxSupply);
                        }
                        boxActionService.createBoxAction(sendSupplyEntity.getBoxId(),"同步WMS补货单发货状态:"+sendSupplyEntity.getSupplySn(),boxWithBLOBs.getStatus(),"-1");
                        // 发送补货消息
                        if (finalLogistics != null) {
                            producerUtil.send(JSON.toJSONBytes(
                                            SupplyDeliverMsgEntity.build(updateExpress.getExpressName(), updateExpress.getExpressNo(), boxWithBLOBs, finalLogistics.getAddress())),
                                    MsgTagUtil.SUPPLY_PRODUCT_DELIVERY_MSG_TAG);
                        }
                        if (notSendCount > 0) {
                            String skuStr = updateBoxDetailList.stream().filter(item -> item.getStatus().equals(UltimaBoxDetailsStatusEnum.CANCEL.getCode().longValue()))
                                    .map(BoxDetailsWithBLOBs::getSku)
                                    .collect(Collectors.joining(","));
                            Fashioner fashioner = fashionerService.findById(boxWithBLOBs.getCreateFasId());
                            SysTemplateMsgEntity sysTemplateMsgEntity = new SysTemplateMsgEntity();
                            sysTemplateMsgEntity.setTemplateCode(SysMsgTemplateCodeEnum.BOX_SEND_ERROR.getCode());
                            sysTemplateMsgEntity.setPhone(fashioner.getPhone() + "," + kefuPhones);
                            HashMap<String, String> params = new HashMap<>();
                            params.put("boxSn",boxWithBLOBs.getBoxSn());
                            params.put("sku",skuStr);
                            params.put("id", boxWithBLOBs.getId());
                            sysTemplateMsgEntity.setParams(params);
                            sysMessageService.sendTemplateSysMsg2User(sysTemplateMsgEntity);
                        }
                        return action;
                    });
                }catch(Exception ex){
                    log.error("补货WMS发货状态同步异常,boxId={},supplyId={}",sendSupplyEntity.getBoxId(),sendSupplyEntity.getSupplyId(),ex);
                }

            }


        }


    }

    @Override
    public void syncReturnState() {
        log.info("执行微商城退货单同步===>");
        // 获取90天内还货未入库的商品
        QueryWrapper<BReturnMallret> bReturnMallretQueryWrapper = new QueryWrapper<>();
        bReturnMallretQueryWrapper.eq("is_del",0L);
        bReturnMallretQueryWrapper.eq("status",0L);
        // 微商城退货单渠道的
        bReturnMallretQueryWrapper.eq("channel",1);
        bReturnMallretQueryWrapper.gt("create_time",DateUtil.addDate(new Date(),-90));
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, 15000);
        List<BReturnMallret> list = ibReturnMallretService.list(bReturnMallretQueryWrapper);


        List<List<BReturnMallret>> partition = Lists.partition(list, 500);
        // 还货商品
        List<BReturnMallret> upDateBReturnMallretList= new ArrayList<>();
        List<BReturnMallret> pReturnMallretList= new ArrayList<>();
        for(List<BReturnMallret> returnDetailList : partition){
            List<Long> ebMallIds = returnDetailList.stream().map(BReturnMallret::getEbMallretId).collect(Collectors.toList());

            QueryWrapper<EbMallret> ebMallretQueryWrapper = new QueryWrapper<>();
            ebMallretQueryWrapper.in("id",ebMallIds);
            ebMallretQueryWrapper.eq("isactive","Y");
            ebMallretQueryWrapper.eq("status",2);
            List<EbMallret> mallRetList= ebMallretService.list(ebMallretQueryWrapper);
            if(CollectionUtils.isEmpty(mallRetList)){
                continue;
            }
            Date updateTime = new Date();
            for(EbMallret ebMallret : mallRetList){
                BReturnMallret bReturnMallret = returnDetailList.stream().filter(e -> {
                    return ebMallret.getId().equals(e.getEbMallretId());
                }).findFirst().orElse(null);
                if(bReturnMallret != null){
                    pReturnMallretList.add(bReturnMallret);
                    BReturnMallret upDateBReturnMallret = new BReturnMallret();
                    upDateBReturnMallret.setId(bReturnMallret.getId());
                    upDateBReturnMallret.setStatus(1L);
                    upDateBReturnMallret.setUpdateTime(updateTime);
                    upDateBReturnMallretList.add(upDateBReturnMallret);
                }
            }
        }

        if(CollectionUtils.isEmpty(upDateBReturnMallretList)){
            return;
        }
        log.info("更新监控明细表,size={}",upDateBReturnMallretList.size());
        List<String> boxReturnIds = pReturnMallretList.stream().map(BReturnMallret::getBoxReturnId).distinct().collect(Collectors.toList());

        boxDetailReturnOk(upDateBReturnMallretList, boxReturnIds);
    }

    @Override
    public void boxDetailReturnOk(List<BReturnMallret> upDateBReturnMallretList, List<String> boxReturnIds) {
        // 根据退货单状态同步还货状态监控表
        ibReturnMallretService.updateBatchById(upDateBReturnMallretList);

        // 去重合并还货单
        // 更新还货监控表
        syncReturnMall(boxReturnIds);
        for(String returnId : boxReturnIds){
            QueryWrapper<BReturnMall> bReturnMallQueryWrapper = new QueryWrapper<>();
            bReturnMallQueryWrapper.eq("box_return_id",returnId);
            // 无则插入，有则更新
            List<BReturnMall> list = ibReturnMallService.list(bReturnMallQueryWrapper);
            if(CollectionUtils.isNotEmpty(list) && list.get(0).getStatus() == 1L){
                continue;
            }
            this.processingReturnBus(returnId,false);
        }
        log.info("微商城退货单同步完成===>");
    }


    public void processingReturnBus(String returnId, boolean ifWarn){
        QueryWrapper<BReturnMallret> mallretQueryWrapper = new QueryWrapper<>();
        mallretQueryWrapper.eq("is_del",0L);
        mallretQueryWrapper.eq("box_return_id",returnId);
        List<BReturnMallret> theReturnGoodsList = ibReturnMallretService.list(mallretQueryWrapper);
        // 判断是否完成还货，完成还货的提交box还货单
        // 未完成还货的判断最后一次更新时间和当前比是否超过4小时，超过则做预警提交
        long count = theReturnGoodsList.stream().filter(e -> e.getStatus().equals(0L)).count();
        List<String> returnDetailIds = theReturnGoodsList.stream().filter(e -> e.getStatus().equals(1L))
                .map(BReturnMallret::getBoxReturnDetailId).collect(Collectors.toList());
        try {
            boxTemplate.execute(action -> {
                if (count == 0) {
                    // 还货完成
                    this.endBoxReturn(returnId, returnDetailIds, false);
                } else if(ifWarn){
                    // 还货超时异常,预警
                    this.endBoxReturn(returnId, returnDetailIds, true);
                }
                return action;
            });
        }catch (Exception ex){
            log.error("同步还货单异常，returnId={}",returnId,ex);
        }
    }




    /**
     * 还货异常同步
     */
    @Override
    public void syncReturnError(){
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.HOUR,-12);
        Date overTime = instance.getTime();
        QueryWrapper<BReturnMall> bReturnMallQueryWrapper = new QueryWrapper<>();
        bReturnMallQueryWrapper.eq("is_del",0);
        bReturnMallQueryWrapper.eq("status",0);
        bReturnMallQueryWrapper.lt("update_time",overTime);
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, 2000);
        List<BReturnMall> list = ibReturnMallService.list(bReturnMallQueryWrapper);
        for (BReturnMall bReturnMall : list){
            BReturnMall lBr = ibReturnMallService.getById(bReturnMall.getId());
            if(lBr.getStatus() == 1L){
                continue;
            }
            this.processingReturnBus(bReturnMall.getBoxReturnId(),true);
        }

    }


    @Override
    public String boxAllot2Store(List<AllotsBox2StoreReq.AllotProduct> allotSku, String sourceNo, long storeId){
        if(allotSku.size() == 0){
            return null;
        }
        AllotStorageEntity allotStorageEntity = new AllotStorageEntity();
        allotStorageEntity.setOrigStoreId(boxStoreId);
        allotStorageEntity.setDestStoreId(storeId);
        allotStorageEntity.setDescription("BOX后台调货");
        allotStorageEntity.setSource(sourceNo+"-trans");
        List<AllotStorageEntity.AllotProduct> productList = allotSku.stream().map(e -> {
            AllotStorageEntity.AllotProduct allotProduct = new AllotStorageEntity.AllotProduct();
            allotProduct.setSku(e.getSku());
            allotProduct.setQty((int) e.getQty());
            return allotProduct;
        }).collect(Collectors.toList());
        allotStorageEntity.setProduct(productList);
        return this.allAllotsStorage(allotStorageEntity);
    }

    /**
     * 统计调拨数据
     */
    @Override
    public void countTransStock(CountTransEntity countTransEntity){
        Date date = new Date();
        List<BBoxEbTransFlow> bBoxEbTransFlowList = new ArrayList<>();
        List<BBoxEbTransTot> bBoxEbTransTotList = new ArrayList<>();
        List<BBoxTransPool> bBoxTransPoolList = new ArrayList<>();
        countTransEntity.getProductList().forEach(e -> {
            BBoxEbTransFlow bBoxEbTransFlow = new BBoxEbTransFlow();
            bBoxEbTransFlow.setIsDel(0);
            bBoxEbTransFlow.setSendStoreId(boxStoreId);
            bBoxEbTransFlow.setReceiptStoreId(e.getStoreId());
            bBoxEbTransFlow.setSkuId(e.getSkuId());
            bBoxEbTransFlow.setSkuNo(e.getSku());
            bBoxEbTransFlow.setQty(e.getQty());
            bBoxEbTransFlow.setCreateTime(date);
            bBoxEbTransFlow.setUpdateTime(date);
            bBoxEbTransFlow.setId(idLeaf.getId());
            bBoxEbTransFlowList.add(bBoxEbTransFlow);
            BBoxEbTransTot bBoxEbTransTot = new BBoxEbTransTot();
            bBoxEbTransTot.setSendStoreId(boxStoreId);
            bBoxEbTransTot.setReceiptStoreId(e.getStoreId());
            bBoxEbTransTot.setSendUnionStoreId(countTransEntity.getStoreId());
            bBoxEbTransTot.setSkuId(e.getSkuId());
            bBoxEbTransTot.setSkuNo(e.getSku());
            bBoxEbTransTot.setQty(e.getQty());
            bBoxEbTransTotList.add(bBoxEbTransTot);
            BBoxTransPool bBoxTransPool = new BBoxTransPool();
            bBoxTransPool.setId(e.getSkuId());
            bBoxTransPool.setSku(e.getSku());
            bBoxTransPool.setBoxEbQty(e.getQty());
            bBoxTransPool.setIsDel(0);
            bBoxTransPoolList.add(bBoxTransPool);
        });

        boxTemplate.execute(action -> {
            // 调出明细
            boxEbTransFlowService.saveBatch(bBoxEbTransFlowList);
            // 调出门店明细
            boxEbTransTotService.batchCreateOrUpdateTransTot(bBoxEbTransTotList);
            // 调出统计
            boxTransPoolService.batchCreateOrUpdateTransPool(bBoxTransPoolList);
            return action;
        });
    }


    @Override
    public void syncReturnGoods(String returnDetailId) {
        BoxReturnDetails returnDetails = boxReturnDetailsService.getById(returnDetailId);
        if(ObjectUtils.isEmpty(returnDetails)){
            throw new RuntimeException("未查询到还货商品");
        }
        if(ObjectUtils.isEmpty(returnDetails.getRetStatus() == 2)){
            // 已入库
            return;
        }
        QueryWrapper<BReturnMallret> bReturnMallretQueryWrapper = new QueryWrapper<>();
        bReturnMallretQueryWrapper.eq("is_del",0L);
        bReturnMallretQueryWrapper.in("status",0L,1L);
        bReturnMallretQueryWrapper.eq("box_return_detail_id",returnDetailId);
        List<BReturnMallret> list = ibReturnMallretService.list(bReturnMallretQueryWrapper);
        if(CollectionUtils.isEmpty(list)){
            throw new RuntimeException("还货同步商品表中未找到数据");
        }
        BReturnMallret bReturnMallret = list.get(0);
        checkStorageIn(bReturnMallret);
        // 修改还货商品监控表状态
        BReturnMallret upDateBReturnMallret = new BReturnMallret();
        upDateBReturnMallret.setId(bReturnMallret.getId());
        upDateBReturnMallret.setStatus(1L);
        upDateBReturnMallret.setUpdateTime(new Date());
        // 修改还货单商品
        BoxReturnDetails updateBoxReturnDetails = new BoxReturnDetails();
        updateBoxReturnDetails.setId(returnDetails.getId());
        updateBoxReturnDetails.setRetStatus(2L);
        // 修改服务单商品
        BoxDetailsWithBLOBs boxDetailsWithBLOBs = boxDetailsService.getById(returnDetails.getBoxDetailId());
        BoxWithBLOBs box = boxRepository.findById(boxDetailsWithBLOBs.getBoxId());
        BoxDetailsWithBLOBs updateBoxDetailsWithBLOBs = new BoxDetailsWithBLOBs();
        updateBoxDetailsWithBLOBs.setId(returnDetails.getBoxDetailId());
        updateBoxDetailsWithBLOBs.setStatus(UltimaBoxDetailsStatusEnum.RETURNED.getCode().longValue());

        // 判断商品商品是否已经全部完成

        QueryWrapper<BoxDetailsWithBLOBs> boxDetailsWithBLOBsQueryWrapper = new QueryWrapper<>();
        boxDetailsWithBLOBsQueryWrapper.eq("box_id",boxDetailsWithBLOBs.getBoxId());
        boxDetailsWithBLOBsQueryWrapper.ne("type",30L);
        boxDetailsWithBLOBsQueryWrapper.select("id,status,type");
        // 获取操作人
        LoginUser userInfo = userInfoService.getUserInfo();
        boxTemplate.execute(action -> {
            if(bReturnMallret.getStatus().equals(0L)) {
                ibReturnMallretService.updateById(upDateBReturnMallret);
            }
            boxReturnDetailsService.updateById(updateBoxReturnDetails);
            boxDetailsService.updateById(updateBoxDetailsWithBLOBs);
            List<BoxDetailsWithBLOBs> details = boxDetailsService.list(boxDetailsWithBLOBsQueryWrapper);
            List<Long> notFinStatus = new ArrayList<>();
            notFinStatus.add(UltimaBoxDetailsStatusEnum.UNSEND.getCode().longValue());
            notFinStatus.add(UltimaBoxDetailsStatusEnum.RETURNING.getCode().longValue());
            notFinStatus.add(UltimaBoxDetailsStatusEnum.SEND.getCode().longValue());
            notFinStatus.add(UltimaBoxDetailsStatusEnum.SIGNED.getCode().longValue());
            notFinStatus.add(UltimaBoxDetailsStatusEnum.UNRETURN.getCode().longValue());
            notFinStatus.add(UltimaBoxDetailsStatusEnum.UNPUTSTORAGE.getCode().longValue());
            boolean b = details.stream().anyMatch(e -> notFinStatus.contains(e.getStatus()));
            if(!b){
                // 所有商品完成，更新服务单
                BoxWithBLOBs updateBox = new BoxWithBLOBs();
                updateBox.setId(boxDetailsWithBLOBs.getBoxId());
                updateBox.setIsWarn(0L);
                updateBox.setStatus(UltimaBoxStatusEnum.FISHED.getCode().longValue());
                updateBox.setFinishTime(new Date());
                boxRepository.updateByPrimaryKeySelective(updateBox);

                if(!BoxSourceTypeEnum.FREE.getCode().equals(box.getSourceType())) {
                    // 修改订阅计划状态
                    CustomerAskBox customerAskBox = customerAskBoxService.findById(box.getSourceId());
                    BoxWithBLOBs theBox = boxService.findById(box.getId());
                    boxService.changeSubPlan(theBox,customerAskBox);
                }
                // 结束信用订单
                if(org.apache.commons.lang3.StringUtils.isNotBlank(box.getCreditId())) {
                    OverCreditOrderEntity overCreditOrderEntity = new OverCreditOrderEntity();
                    overCreditOrderEntity.setId(box.getCreditId());
                    ibCustomerZhimaCreditOrderService.createOverOrderEvent(overCreditOrderEntity);
                }
            }
            boxActionService.createBoxAction(box.getId(),"手动同步："+returnDetails.getSku()+"完成入库",box.getStatus(),Optional.ofNullable(userInfo).map(LoginUser::getRealname).orElse(""));
            return action;
        });
    }

    private void checkStorageIn(BReturnMallret bReturnMallret) {
        boolean flag = false;
        if(ObjectUtils.isNotEmpty(bReturnMallret.getEbMallretId())){
            // 查询微商城退货单是否已入库
            QueryWrapper<EbMallret> ebMallretQueryWrapper = new QueryWrapper<>();
            ebMallretQueryWrapper.eq("id", bReturnMallret.getEbMallretId());
            ebMallretQueryWrapper.eq("isactive","Y");
            ebMallretQueryWrapper.eq("status",2);
            List<EbMallret> mallRetList= ebMallretService.list(ebMallretQueryWrapper);
            if(CollectionUtils.isNotEmpty(mallRetList)){
                flag = true;
            }
        }
        if(!flag){
            // 查询聚水潭单据是否已入库
            Query2JSTDTO query2JSTDTO = new Query2JSTDTO();
            query2JSTDTO.setShopId(shopId.toString());
            query2JSTDTO.setOuterAsIds(Collections.singletonList(bReturnMallret.getBoxReturnDetailId()));

            ResponseResult<List<Query2JSTRespDTO>> result = retailApi.jstQuery(query2JSTDTO);
            if(result.getCode() != 0){
                throw new RuntimeException("查询聚水潭接口失败,"+result.getMsg());
            }
            if(CollectionUtils.isEmpty(result.getData())){
                throw new RuntimeException("聚水潭中未查询到对应数据,请核实");
            }
            Query2JSTRespDTO query2JSTRespDTO = result.getData().get(0);
            if(!"Confirmed".equals(query2JSTRespDTO.getStatus())){
                throw new RuntimeException("单据未入库,请前往聚水潭核实");
            }
        }
    }


    @Override
    public void cancelEbMallByReturnDetailId(String returnDetailId,String boxSn) {
        QueryWrapper<BReturnMallret> bReturnMallretQueryWrapper = new QueryWrapper<>();
        bReturnMallretQueryWrapper.eq("is_del",0L);
        bReturnMallretQueryWrapper.eq("status",0L);
        bReturnMallretQueryWrapper.eq("box_return_detail_id",returnDetailId);
        List<BReturnMallret> list = ibReturnMallretService.list(bReturnMallretQueryWrapper);
        if(CollectionUtils.isEmpty(list)){
            throw new RuntimeException("还货同步商品表中未找到数据");
        }
        BReturnMallret bReturnMallret = list.get(0);
        // 查询微商城退货单
       /* QueryWrapper<EbMallret> ebMallretQueryWrapper = new QueryWrapper<>();
        ebMallretQueryWrapper.eq("id",bReturnMallret.getEbMallretId());
        ebMallretQueryWrapper.eq("isactive","Y");
        ebMallretQueryWrapper.eq("status",1);
        EbMallret mallRet= ebMallretService.getOne(ebMallretQueryWrapper);
        if(ObjectUtils.isEmpty(mallRet)){
            throw new RuntimeException("未找到对应的微商城退货单，请核实");
        }*/
        // 删除同步中间表
        BReturnMallret updateBReturnMallret = new BReturnMallret();
        updateBReturnMallret.setId(bReturnMallret.getId());
        updateBReturnMallret.setIsDel(1L);
        ibReturnMallretService.updateById(updateBReturnMallret);
        // 取消微商城退货单
        if(ObjectUtils.isNotEmpty(bReturnMallret.getEbMallretId())){
            EbMallret updateEbMallret = new EbMallret();
            updateEbMallret.setId(bReturnMallret.getEbMallretId());
            updateEbMallret.setIsactive("N");
            ebMallretService.updateById(updateEbMallret);
        }
        // 作废聚水潭单据
        if(bReturnMallret.getChannel() == 2) {
            Update2JSTDTO update2JSTDTO = new Update2JSTDTO();
            update2JSTDTO.setType("BOX_BACK");
            update2JSTDTO.setOuterAsId(bReturnMallret.getBoxReturnDetailId());
            update2JSTDTO.setShopId(shopId);
            update2JSTDTO.setSoId(boxSn);
            retailApi.jstClose(Collections.singletonList(update2JSTDTO));
        }
    }

    private void syncReturnMall(List<String> boxReturnIds){
        QueryWrapper<BReturnMall> bReturnMallQueryWrapper = new QueryWrapper<>();
        bReturnMallQueryWrapper.in("box_return_id",boxReturnIds);
        // 无则插入，有则更新
        List<BReturnMall> list = ibReturnMallService.list(bReturnMallQueryWrapper);
        Date date = new Date();
        List<BReturnMall> updateList = boxReturnIds.stream().map(e -> {
            BReturnMall bReturnMall = new BReturnMall();
            BReturnMall bReturnMall2 = list.stream().filter(a -> a.getBoxReturnId().equals(e)).findFirst().orElse(null);
            if (bReturnMall2 != null) {
                bReturnMall.setId(bReturnMall2.getId());
                bReturnMall.setUpdateTime(date);
            } else {
                bReturnMall.setId(idLeaf.getId());
                bReturnMall.setStatus(0L);
                bReturnMall.setIsDel(0L);
                bReturnMall.setBoxReturnId(e);
                bReturnMall.setCreateTime(date);
                bReturnMall.setUpdateTime(date);
            }
            return bReturnMall;
        }).collect(Collectors.toList());
        ibReturnMallService.saveOrUpdateBatch(updateList);
    }

    private List<BReturnMallret> createMallRet(CreateEbMallRetContext context){
        // 判读是否已经创建过
        QueryWrapper<EbMallret> ebMallretQueryWrapper = new QueryWrapper<>();
        ebMallretQueryWrapper.eq("OMS_EBONUM",context.getBoxSn());
        ebMallretQueryWrapper.eq("OMS_EBONUM_RET",context.getSourceCode());
        ebMallretQueryWrapper.eq("isactive","Y");
        List<EbMallret> list = ebMallretService.list(ebMallretQueryWrapper);
        if(list.size() > 0){
            return new ArrayList<>();
        }
        Date createDate = new Date();
        Integer dateStr = Integer.valueOf(DateUtil.formatToStr(createDate, DateUtil.DATEFORMATE_YYYYMMDD));
        // 退货拆单，因为伯俊不支持一单多商品的逻辑，所以需要拆成每件商品一个退货单
        List<BReturnMallret> bReturnMallretList = new ArrayList<>();
        List<EbMallret> mallList = new ArrayList<>();
        List<EbMallretitem> mallItemList = new ArrayList<>();
        List<EbMallretPayitem> mallPayItemList = new ArrayList<>();
        for(CreateEbMallRetContext.MallRetItem mallRetItem : context.getMallRetItems()){
            EbMallret ebMallret = new EbMallret();
            ebMallret.setAdClientId(37L);
            ebMallret.setAdOrgId(27L);
            ebMallret.setBilldate(dateStr);
            ebMallret.setStatus(1);
            ebMallret.setOwnerid(893L);
            ebMallret.setModifierid(893L);
            ebMallret.setCreationdate(createDate);
            ebMallret.setModifieddate(createDate);
            ebMallret.setIsactive("Y");
            ebMallret.setcStoreId(boxInTransitStoreId);
            ebMallret.setIsTowmsin("N");
            ebMallret.setOmsEbonum(context.getBoxSn());
            ebMallret.setOmsEbonumRet(context.getSourceCode());
            ebMallret.setFastno(context.getExpressNo());
            ebMallret.setMallretType(context.getType());
            ebMallret.setTotAmtActual(mallRetItem.getPrice());
            ebMallret.setId(ebMallretService.getMaxId());
            ebMallret.setDocno(ebMallretService.getNextNo());
            ebMallret.setTotQty(mallRetItem.getQty());
            ebMallret.setTotQtyout(mallRetItem.getQty());
            ebMallret.setDescription(mallRetItem.getSkuId().toString());
            mallList.add(ebMallret);
            EbMallretitem ebMallretitem = new EbMallretitem();
            ebMallretitem.setId(ebMallretitemService.getMaxId());
            ebMallretitem.setEbMallretId(ebMallret.getId());
            ebMallretitem.setmProductaliasId(mallRetItem.getSkuId());
            ebMallretitem.setmProductId(mallRetItem.getSpuId());
            ebMallretitem.setmAttributesetinstanceId(mallRetItem.getAttrId());
            ebMallretitem.setQty(mallRetItem.getQty());
            ebMallretitem.setQtyout(mallRetItem.getQty());// 已发数量，暂时是售后一致，都是1
            ebMallretitem.setPriceactual(mallRetItem.getPrice());
            ebMallretitem.setTotAmtActual(mallRetItem.getPrice());
            ebMallretitem.setWebposRetreasonId(12L);
            ebMallretitem.setCreationdate(createDate);
            ebMallretitem.setModifieddate(createDate);
            ebMallretitem.setAdClientId(37L);
            ebMallretitem.setAdOrgId(27L);
            ebMallretitem.setOwnerid(893L);
            ebMallretitem.setModifierid(893L);
            ebMallretitem.setIsactive("Y");
            mallItemList.add(ebMallretitem);
            if(MallRetTypeEnum.BOX_BACK.getKey().equals(context.getType()) || MallRetTypeEnum.BOX_XNBACK.getKey().equals(context.getType())) {
                // 还货-加入监控表
                BReturnMallret bReturnMallret = new BReturnMallret();
                bReturnMallret.setEbMallretId(ebMallret.getId());
                bReturnMallret.setBoxReturnDetailId(mallRetItem.getReturnDetailId());
                bReturnMallretList.add(bReturnMallret);
            }
            List<EbMallretPayitem> payItemList = mallRetItem.getPayWayList().stream().map(e -> {
                EbMallretPayitem ebMallretPayitem = new EbMallretPayitem();
                ebMallretPayitem.setId(ebMallretPayitemService.getMaxId());
                ebMallretPayitem.setAdClientId(37L);
                ebMallretPayitem.setAdOrgId(27L);
                ebMallretPayitem.setEbMallretId(ebMallret.getId());
                ebMallretPayitem.setcPaywayId(e.getPayWayId());
                ebMallretPayitem.setPayamount(e.getTotPrice());
                ebMallretPayitem.setOwnerid(893L);
                ebMallretPayitem.setModifierid(893L);
                ebMallretPayitem.setIsactive("Y");
                ebMallretPayitem.setCreationdate(createDate);
                ebMallretPayitem.setModifieddate(createDate);
                return ebMallretPayitem;
            }).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(payItemList)) {
                mallPayItemList.addAll(payItemList);
            }
        }

        bojunTemplate.execute(action -> {
            ebMallretService.saveBatch(mallList);
            ebMallretitemService.saveBatch(mallItemList);
            if(CollectionUtils.isNotEmpty(mallPayItemList)){
                ebMallretPayitemService.saveBatch(mallPayItemList);
            }
            if(MallRetTypeEnum.BOX_XNBACK.getKey().equals(context.getType())){
                // box虚拟还货，修改数量，直接提交
                mallList.forEach(e -> {
                    ebMallretService.submitEbMallRet(e.getId().intValue());
                });
            }
            return action;
        });
        return bReturnMallretList;
    }

    @Override
    public SplitMRetailContext initOrderInfo(Order order){
        BoxWithBLOBs box = boxRepository.selectByBoxSn(order.getBoxSn());
        List<String> orderIds = new ArrayList<>();
        orderIds.add(order.getId());
        List<OrderDetail> orderDetailList = orderRepository.selectListByOrderIds(orderIds);
        SplitMRetailContext splitMRetailContext = new SplitMRetailContext();
        // 筛选出非换码的商品
        orderDetailList = orderDetailList.stream().filter(e -> ObjectUtils.isEmpty(e.getEbNum()) || e.getEbNum() == 0L).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orderDetailList)){
            throw new BoxException("未查询到订单商品信息");
        }
        List<OrderPayment> orderPaymentList = paymentRepository.selectOrderPaymentByOrderId(order.getId(), new ArrayList<Long>() {
            {
                add(OrderPaymentPayWayEnum.MONEY.getCode());
                add(OrderPaymentPayWayEnum.VOU.getCode());
                add(OrderPaymentPayWayEnum.INTEGRAL2VOU.getCode());
                add(4L);// 礼品卡
                add(OrderPaymentPayWayEnum.BALANCE.getCode());
                add(OrderPaymentPayWayEnum.SHOP_VOU.getCode());
            }
        }, false);
        // 获取用户会员数据
        CustomerDetails customerDetails = customerDetailsService.selectByPrimaryId(order.getCustomerId());
        List<MallVouInfoEntity> mallVouInfoList = new ArrayList<>();
        // 收银台支付订单信息补充
        if(StringUtils.isNotBlank(order.getOutPayNo())) {
            setCashierOrderData(orderDetailList,order,orderPaymentList,splitMRetailContext,mallVouInfoList);
            if(CollectionUtils.isNotEmpty(mallVouInfoList)){
                splitMRetailContext.setMallVouInfoList(mallVouInfoList);
            }
        }
        splitMRetailContext.setBox(box);
        splitMRetailContext.setOrderDetailList(orderDetailList);
        splitMRetailContext.setOrderPaymentList(orderPaymentList);
        splitMRetailContext.setCustomerDetails(customerDetails);
        splitMRetailContext.setOrder(order);
        return splitMRetailContext;
    }

    private void setCashierOrderData(List<OrderDetail> orderDetailList,Order order,List<OrderPayment> orderPaymentList, SplitMRetailContext splitMRetailContext,List<MallVouInfoEntity> mallVouInfoList){
        GetPayOrderResp payOrderInfo = iHandlerPayService.getPayOrderInfo(order.getOutPayNo());
        OrderEntity cashierOrder = payOrderInfo.getOrder();
        if (2 == cashierOrder.getPayChannel()) {
            splitMRetailContext.setDdLy("BOX-SQB");
        }
        if(StringUtils.isNotBlank(order.getOpenTotalDiscountAmount())) {
            // 修改支付金额
            List<OrderDetailEntity> cashierOrderDetailList = payOrderInfo.getDetailList();
            orderDetailList.forEach(e -> {
                List<OrderDetailEntity> theDetailList = cashierOrderDetailList.stream().filter(a -> a.getOutItemId().equals(e.getId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(theDetailList)){
                    BigDecimal vouDisAmount = theDetailList.get(0).getVouDisAmount();
                    BigDecimal integralDisAmount = theDetailList.get(0).getIntegralDisAmount();
                    BigDecimal outDisAmount = BigDecimal.ZERO;
                    if(ObjectUtils.isNotEmpty(vouDisAmount)){
                        outDisAmount = outDisAmount.add(vouDisAmount);
                    }
                    if(ObjectUtils.isNotEmpty(integralDisAmount)){
                        outDisAmount = outDisAmount.add(integralDisAmount);
                    }
                    if(BigDecimal.ZERO.compareTo(outDisAmount) < 0) {
                        BigDecimal productPaidPrice = new BigDecimal(e.getPriceactual());
                        BigDecimal priceActual = productPaidPrice.subtract(outDisAmount);
                        e.setShopVouAmt(outDisAmount);
                        e.setPriceactual(priceActual.toString());
                    }


                }
            });
            BigDecimal cOrderPriceActual = cashierOrder.getPriceActual();
            // 修改微支付金额
            orderPaymentList.forEach(e ->{
                if(e.getPayway().equals(OrderPaymentPayWayEnum.MONEY.getCode())){
                    // 微支付
                    e.setPaidAmount(cOrderPriceActual.doubleValue());
                }
            });
            // 增加商场资产类型支付
            List<OrderPaymentEntity> paymentList = payOrderInfo.getPaymentList();
            List<OrderPaymentEntity> mallPayment = paymentList.stream().filter(e -> 1L != e.getPaymentType()).collect(Collectors.toList());
            List<OrderPayment> shopVouPaymentList = mallPayment.stream().map(e -> {
                OrderPayment orderPayment = new OrderPayment();
                orderPayment.setOrderId(order.getId());
                orderPayment.setPayway(OrderPaymentPayWayEnum.SHOP_VOU.getCode());
                orderPayment.setPaidAmount(e.getAmount().doubleValue());
                orderPayment.setVouNo(e.getVoucherNo());
                return orderPayment;
            }).collect(Collectors.toList());
            orderPaymentList.addAll(shopVouPaymentList);
            List<MallVouInfoEntity> mallVouList = mallPayment.stream().map(e -> {
                MallVouInfoEntity mallVouInfo = new MallVouInfoEntity();
                mallVouInfo.setMallVouNo(e.getVoucherNo());
                if(StringUtils.isNotBlank(e.getVouShardingNum())) {
                    mallVouInfo.setMallCostRatio(new BigDecimal(e.getVouShardingNum()));
                }
                if(StringUtils.isNotBlank(e.getVouTmlCode())) {
                    mallVouInfo.setThirdVouNo(e.getVouTmlCode());
                }
                return mallVouInfo;
            }).collect(Collectors.toList());
            mallVouInfoList.addAll(mallVouList);
        }
    }


    /**
     * 未指定品牌拆分
     * @return
     */
    private List<CreateMRetailEntity> splitUnspecifiedGoods(SplitMRetailContext splitMRetailContext,List<SplitRetailVipEntity> splitVipList,Order order){
        List<CreateMRetailEntity> unspecifiedGoodsRetailList = new ArrayList<>();
        String vouNos = splitMRetailContext.getOrderPaymentList().stream().filter(e -> {
            return (e.getPayway() == 2L || e.getPayway() == 3L) && StringUtil.isNotEmpty(e.getVouNo());
        }).map(OrderPayment::getVouNo).collect(Collectors.joining(","));
        List<Long> spuIds = splitMRetailContext.getOrderDetailList().stream().map(e -> Long.valueOf(e.getOutId())).collect(Collectors.toList());
        List<ProductBrandEntity> productBrandEntityList = imProductService.selectBrandByProductIds(spuIds);

        // 优先拆分给提交要盒导购和邀请订阅导购
        List<CreateMRetailEntity> split2AppointSalesList = this.split2AppointSales(splitMRetailContext,splitVipList,order,productBrandEntityList,vouNos);
        if(CollectionUtils.isNotEmpty(split2AppointSalesList)){
            unspecifiedGoodsRetailList.addAll(split2AppointSalesList);
            if(CollectionUtils.isEmpty(splitMRetailContext.getOrderDetailList())){
                return unspecifiedGoodsRetailList;
            }
        }
        // 内淘商品业绩拆分到发货门店
        if(splitMRetailContext.getBox().getIsEb() == 1){
            List<String> boxDetailIds = splitMRetailContext.getOrderDetailList().stream().map(OrderDetail::getBoxDetailId).collect(Collectors.toList());
            List<BoxDetailsWithBLOBs> boxDetailList = boxRepository.findDetailByIds(boxDetailIds);
            List<BoxDetailsWithBLOBs> ebBoxDetailList = boxDetailList.stream().filter(e -> ObjectUtils.isNotEmpty(e.getSendUnionstoreId())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(ebBoxDetailList)){
                SplitRetailVipEntity jnbyVip = splitVipList.stream().filter(e -> e.getVipBrand().equals("江南布衣+")).findFirst().orElse(null);
                if(ObjectUtils.isEmpty(jnbyVip)){
                    throw new BoxException("未查询到江南布衣+卡");
                }
                Map<Long, List<BoxDetailsWithBLOBs>> sendStoreMap = ebBoxDetailList.stream().collect(Collectors.groupingBy(BoxDetailsWithBLOBs::getSendUnionstoreId));
                for(Long sendStoreId : sendStoreMap.keySet()){
                    List<String> sendStoreDetailIds = sendStoreMap.get(sendStoreId).stream().map(BoxDetailsWithBLOBs::getId).collect(Collectors.toList());
                    List<OrderDetail> ebOrderDetails = splitMRetailContext.getOrderDetailList().stream().filter(e -> sendStoreDetailIds.contains(e.getBoxDetailId())).collect(Collectors.toList());
                    SplitRetailVipEntity theVip = new SplitRetailVipEntity();
                    BeanUtils.copyProperties(jnbyVip,theVip);
                    if(!sendStoreId.equals(theVip.getStoreId())){
                        // 会员绑定不是发货门店，业绩不给清除导购
                        theVip.setStoreId(sendStoreId);
                        theVip.setSaleId(null);
                    }
                    unspecifiedGoodsRetailList.add(cCreateMRetailEntity(ebOrderDetails,order,vouNos,theVip));
                    // 清除已拆分的商品
                    splitMRetailContext.getOrderDetailList().removeAll(ebOrderDetails);
                }
            }
        }
        if(CollectionUtils.isEmpty(splitMRetailContext.getOrderDetailList())){
            return unspecifiedGoodsRetailList;
        }
        List<Long> theSpuIds = splitMRetailContext.getOrderDetailList().stream().map(e -> Long.valueOf(e.getOutId())).collect(Collectors.toList());
        productBrandEntityList = productBrandEntityList.stream().filter(e -> theSpuIds.contains(e.getId())).collect(Collectors.toList());
        Map<String, List<ProductBrandEntity>> map = productBrandEntityList.stream().collect(Collectors.groupingBy(ProductBrandEntity::getIfCurrent));
        // 奥莱商品
        List<ProductBrandEntity> outLetsGoods = map.get("N");
        if(CollectionUtils.isNotEmpty(outLetsGoods)){
            SplitRetailVipEntity outletsVip = splitVipList.stream().filter(e -> e.getVipBrand().equals("OUTLETS")).findFirst().orElse(null);
            if(ObjectUtils.isEmpty(outletsVip)){
                throw new BoxException("未查询到奥莱卡");
            }
            CreateMRetailEntity outLetsRetailEntity = splitOutLetsGoods(splitMRetailContext.getOrderDetailList(),order,vouNos,outletsVip,outLetsGoods);
            unspecifiedGoodsRetailList.add(outLetsRetailEntity);
        }
        // 正价商品
        List<ProductBrandEntity> currentGoods = map.get("Y");
        if(CollectionUtils.isNotEmpty(currentGoods)){
            List<Long> currentIds = currentGoods.stream().map(ProductBrandEntity::getId).collect(Collectors.toList());
            // 补充商品信息
            List<OrderDetail> currentOrderDetailList = splitMRetailContext.getOrderDetailList().stream().filter(e -> currentIds.contains(Long.valueOf(e.getOutId()))).collect(Collectors.toList());
            unspecifiedGoodsRetailList.addAll(splitCurrentGoods(currentOrderDetailList,order,vouNos,splitVipList,currentGoods));
        }
        return unspecifiedGoodsRetailList;
    }


    /**
     * 优先拆分给指定规则的导购
     */
    private List<CreateMRetailEntity> split2AppointSales(SplitMRetailContext splitMRetailContext,List<SplitRetailVipEntity> splitVipList,Order order,List<ProductBrandEntity> productBrandEntityList,String vouNos){
        List<CreateMRetailEntity> list = new ArrayList<>();
        BoxWithBLOBs box = boxRepository.selectByBoxSn(order.getBoxSn());
        if(StringUtils.isNotBlank(box.getSourceId())){
            CustomerAskBox askBox = customerAskBoxService.findById(box.getSourceId());
            if(StringUtils.isNotBlank(askBox.getSubmitSalesFashionerId())){
                // 拆分给提交要盒导购
                CreateMRetailEntity createMRetailEntity = this.split2Sales(askBox.getSubmitSalesFashionerId(), splitMRetailContext, splitVipList, order, productBrandEntityList, vouNos);
                if(ObjectUtils.isNotEmpty(createMRetailEntity)){
                    list.add(createMRetailEntity);
                }
            }
            if(StringUtils.isNotBlank(askBox.getSubId())){
                BSubscribeInfo subInfo = subscribeInfoService.getById(askBox.getSubId());
                if(StringUtils.isNotBlank(subInfo.getRecommender())){
                    // 拆分给邀请订阅导购
                    CreateMRetailEntity createMRetailEntity = this.split2Sales(subInfo.getRecommender(), splitMRetailContext, splitVipList, order, productBrandEntityList, vouNos);
                    if(ObjectUtils.isNotEmpty(createMRetailEntity)){
                        list.add(createMRetailEntity);
                    }
                }
            }
        }
        return list;
    }


    private CreateMRetailEntity split2Sales(String fashionerId,SplitMRetailContext splitMRetailContext,List<SplitRetailVipEntity> splitVipList,Order order,List<ProductBrandEntity> productBrandEntityList,String vouNos){
        if(CollectionUtils.isEmpty(splitMRetailContext.getOrderDetailList())) {
            return null;
        }
        Fashioner subAskFashioner = fashionerService.findById(fashionerId);
        if(subAskFashioner.getIsSales() != 1) {
            return null;
        }
        if(StringUtils.isEmpty(subAskFashioner.getcStoreId()) || StringUtils.isEmpty(subAskFashioner.getHrEmpId())) {
            return null;
        }
        // 拆分给指定导购
        // 判断绑定关系
        List<SplitRetailVipEntity> vipList = splitVipList.stream().filter(e ->subAskFashioner.getHrEmpId().equals(e.getSaleId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(vipList)){
           return null;
        }
        // 商品分组
        Map<String, List<ProductBrandEntity>> map = productBrandEntityList.stream().collect(Collectors.groupingBy(ProductBrandEntity::getIfCurrent));
        // 按品牌分组
        List<SplitProductEntity> productList = splitMRetailContext.getOrderDetailList().stream().map(e -> {
            SplitProductEntity splitProductEntity = new SplitProductEntity();
            splitProductEntity.setSkuId(e.getProductId());
            splitProductEntity.setSpuId(e.getOutId());
            ProductBrandEntity productBrandEntity = productBrandEntityList.stream().filter(a -> a.getId().equals(Long.valueOf(e.getOutId()))).findFirst().orElse(null);
            if (ObjectUtils.isEmpty(productBrandEntity)) {
                throw new BoxException("未查询到商品品牌");
            }
            splitProductEntity.setBrand(ParseBrandUtil.parseProductBrand2VipBrand(productBrandEntity.getBrand()));
            return splitProductEntity;
        }).collect(Collectors.toList());
        SplitRetailVipEntity splitRetailVipEntity = vipList.get(0);
        List<SplitProductEntity> splitProductEntityList;
        if("OUTLETS".equals(splitRetailVipEntity.getVipBrand())){
            // 所有奥莱商品
            List<ProductBrandEntity> outLetsGoods = map.get("N");
            if(CollectionUtils.isEmpty(outLetsGoods)){
                return null;
            }
            splitProductEntityList = productList.stream().filter(e -> outLetsGoods.stream().anyMatch(a -> a.getId().equals(Long.valueOf(e.getSpuId())))).collect(Collectors.toList());
        }else if("江南布衣+".equals(splitRetailVipEntity.getVipBrand())){
            // 所有正价商品
            List<ProductBrandEntity> currentGoods = map.get("Y");
            if(CollectionUtils.isEmpty(currentGoods)){
                return null;
            }
            splitProductEntityList = productList.stream().filter(e -> currentGoods.stream().anyMatch(a -> a.getId().equals(Long.valueOf(e.getSpuId())))).collect(Collectors.toList());
        }else{
            // 正价商品按品牌拆分
            List<ProductBrandEntity> currentGoods = map.get("Y");
            if(CollectionUtils.isEmpty(currentGoods)){
                return null;
            }
            List<SplitProductEntity> currentList = productList.stream().filter(e -> currentGoods.stream().anyMatch(a -> a.getId().equals(Long.valueOf(e.getSpuId())))).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(currentList)){
                return null;
            }
            // 判断有没有对应的品牌商品
            splitProductEntityList = currentList.stream().filter(e -> splitRetailVipEntity.getVipBrand().equals(e.getBrand())).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(splitProductEntityList)){
            return null;
        }
        CreateMRetailEntity createMRetailEntity = this.cCreateMRetailEntity(splitMRetailContext.getOrderDetailList(), order, vouNos, splitRetailVipEntity, splitProductEntityList);
        // 从列表里清除
        List<OrderDetail> splitOrderDetailList =  splitMRetailContext.getOrderDetailList().stream().filter(e -> {
            return splitProductEntityList.stream().anyMatch(a -> a.getSkuId().equals(e.getProductId()));
        }).collect(Collectors.toList());
        splitMRetailContext.getOrderDetailList().removeAll(splitOrderDetailList);
        return createMRetailEntity;

    }

    /**
     * 拆分奥莱商品
     */
    private CreateMRetailEntity splitOutLetsGoods(List<OrderDetail> orderDetailList,Order order,String vouNos, SplitRetailVipEntity outLetsVipEntity,List<ProductBrandEntity> outLetsGoods){
        List<Long> outLetsIds = outLetsGoods.stream().map(ProductBrandEntity::getId).collect(Collectors.toList());
        List<OrderDetail> outLetsDetailList = orderDetailList.stream().filter(e -> outLetsIds.contains(Long.valueOf(e.getOutId()))).collect(Collectors.toList());
        return cCreateMRetailEntity(outLetsDetailList,order,vouNos,outLetsVipEntity);
    }


    /**
     * 拆分正价商品
     */
    private List<CreateMRetailEntity> splitCurrentGoods(List<OrderDetail> orderDetailList,Order order,String vouNos,List<SplitRetailVipEntity> splitVipList,List<ProductBrandEntity> productBrandEntityList){
        // 按品牌分组
        List<SplitProductEntity> productList = orderDetailList.stream().map(e -> {
            SplitProductEntity splitProductEntity = new SplitProductEntity();
            splitProductEntity.setSkuId(e.getProductId());
            splitProductEntity.setSpuId(e.getOutId());
            ProductBrandEntity productBrandEntity = productBrandEntityList.stream().filter(a -> a.getId().equals(Long.valueOf(e.getOutId()))).findFirst().orElse(null);
            if (ObjectUtils.isEmpty(productBrandEntity)) {
                throw new BoxException("未查询到商品品牌");
            }
            splitProductEntity.setBrand(ParseBrandUtil.parseProductBrand2VipBrand(productBrandEntity.getBrand()));
            return splitProductEntity;
        }).collect(Collectors.toList());

        Map<String, List<SplitProductEntity>> productMap = productList.stream().collect(Collectors.groupingBy(SplitProductEntity::getBrand));
        Set<String> productBrands = productMap.keySet();
        List<CreateMRetailEntity> list = new ArrayList<>();
        // 经销
        List<SplitRetailVipEntity> dealerVipList = splitVipList.stream().filter(e -> {
            return productBrands.contains(e.getVipBrand()) && e.isIfDealer();
        }).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dealerVipList)) {
            list.addAll(cCreateMRetailEntity(productMap, orderDetailList, order, vouNos, dealerVipList));
        }
        // 商品处理完，直接返回
        if(productMap.isEmpty()){
            return list;
        }
        // 江南布衣+绑定门店
        SplitRetailVipEntity jnbyVip = splitVipList.stream().filter(e -> {
            return e.getVipBrand().equals("江南布衣+");
        }).findFirst().orElse(null);
        if(ObjectUtils.isNotEmpty(jnbyVip) && !Long.valueOf(25277L).equals(jnbyVip.getStoreId())){
            List<SplitProductEntity> jnbyProductList = productMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            list.add(cCreateMRetailEntity(orderDetailList,order,vouNos,jnbyVip,jnbyProductList));
            return list;
        }
        // 直营绑定门店
        List<SplitRetailVipEntity> directVipList = splitVipList.stream().filter(e -> {
            return productBrands.contains(e.getVipBrand()) && !e.isIfDealer() && !Long.valueOf(25277L).equals(e.getStoreId());
        }).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(directVipList)) {
            list.addAll(cCreateMRetailEntity(productMap, orderDetailList, order, vouNos, directVipList));
        }
        // 商品处理完，直接返回
        if(productMap.isEmpty()){
            return list;
        }
        // 江南布衣+未绑定门店
        if(ObjectUtils.isEmpty(jnbyVip)){
            throw new BoxException("未查询到江南布衣+卡");
        }
        List<SplitProductEntity> jnbyProductList = productMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        list.add(cCreateMRetailEntity(orderDetailList,order,vouNos,jnbyVip,jnbyProductList));
        return list;
    }

    /**
     * 生成创建零售单实体，并清除productMap中指定会员的品牌
     * @param productMap
     * @param orderDetailList
     * @param order
     * @param vouNos
     * @param vipList
     * @return
     */
    private List<CreateMRetailEntity> cCreateMRetailEntity(Map<String, List<SplitProductEntity>> productMap,List<OrderDetail> orderDetailList,Order order,String vouNos,List<SplitRetailVipEntity> vipList){
        return vipList.stream().map(e -> {
            List<SplitProductEntity> splitProductEntityList = productMap.get(e.getVipBrand());
            // 清除该品牌
            productMap.remove(e.getVipBrand());
            return cCreateMRetailEntity(orderDetailList, order, vouNos,e,splitProductEntityList);
        }).collect(Collectors.toList());
    }

    /**
     * 根据传入的productList筛选出orderDetailList，并生成创建零售单实体
     * @param orderDetailList
     * @param order
     * @param vouNos
     * @param vip
     * @param productList
     * @return
     */
    private CreateMRetailEntity cCreateMRetailEntity(List<OrderDetail> orderDetailList,Order order,String vouNos,SplitRetailVipEntity vip,List<SplitProductEntity> productList){
        List<OrderDetail> splitOrderDetailList = orderDetailList.stream().filter(e -> productList.stream().anyMatch(a -> a.getSkuId().equals(e.getProductId()))).collect(Collectors.toList());
        return cCreateMRetailEntity(splitOrderDetailList, order, vouNos, vip);

    }

    /**
     * 根据传入的商品数据生成零售单实体
     * @param orderDetailList
     * @param order
     * @param vouNos
     * @param vip
     * @return
     */
    private CreateMRetailEntity cCreateMRetailEntity(List<OrderDetail> orderDetailList,Order order,String vouNos,SplitRetailVipEntity vip){
        // 拆分付款明细
        double totAmt = orderDetailList.stream().mapToDouble(e -> new BigDecimal(e.getPriceactual()).multiply(new BigDecimal(e.getProductQuantity())).doubleValue()).sum();
        double vouAmt = orderDetailList.stream().mapToDouble(e -> Optional.ofNullable(e.getVoucherAmount()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(e.getProductQuantity())).doubleValue()).sum();
        double balanceAmt = orderDetailList.stream().mapToDouble(e -> Optional.ofNullable(e.getBalanceAmt()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(e.getProductQuantity())).doubleValue()).sum();
        double shopVouAmt = orderDetailList.stream().mapToDouble(e -> Optional.ofNullable(e.getShopVouAmt()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(e.getProductQuantity())).doubleValue()).sum();

        List<OrderPayment> thePaymentList = new ArrayList<>();
        OrderPayment totAmtPayment = new OrderPayment();
        totAmtPayment.setPayway(OrderPaymentPayWayEnum.MONEY.getCode());
        totAmtPayment.setPaidAmount(totAmt);
        thePaymentList.add(totAmtPayment);
        if(vouAmt > 0D){
            OrderPayment vouAmtPayment = new OrderPayment();
            vouAmtPayment.setPayway(OrderPaymentPayWayEnum.VOU.getCode());
            vouAmtPayment.setPaidAmount(vouAmt);
            vouAmtPayment.setVouNo(vouNos);
            thePaymentList.add(vouAmtPayment);
        }
        if(balanceAmt > 0D){
            OrderPayment balanceAmtPayment = new OrderPayment();
            balanceAmtPayment.setPayway(OrderPaymentPayWayEnum.BALANCE.getCode());
            balanceAmtPayment.setPaidAmount(balanceAmt);
            thePaymentList.add(balanceAmtPayment);
        }
        if(shopVouAmt > 0D){
            OrderPayment shopVouPayment = new OrderPayment();
            shopVouPayment.setPayway(OrderPaymentPayWayEnum.SHOP_VOU.getCode());
            shopVouPayment.setPaidAmount(shopVouAmt);
            thePaymentList.add(shopVouPayment);
        }
        return cCreateMRetailEntity(orderDetailList,order,thePaymentList,vip);
    }


    /**
     * 生成创建零售单实体
     * @param orderDetailList
     * @param order
     * @param orderPayments
     * @param vip
     * @return
     */
    @Override
    public CreateMRetailEntity cCreateMRetailEntity(List<OrderDetail> orderDetailList,Order order,List<OrderPayment> orderPayments,SplitRetailVipEntity vip){
        List<String> skuIds = orderDetailList.stream().map(OrderDetail::getProductId).collect(Collectors.toList());
        List<MProductAlias> mProductAliases = imProductAliasService.listByIds(skuIds);
        List<CreateMRetailEntity.Product> productList = orderDetailList.stream().map(e -> {
            CreateMRetailEntity.Product product = new CreateMRetailEntity.Product();
            product.setId(Long.valueOf(e.getOutId()));
            product.setCodeId(Long.valueOf(e.getProductId()));
            MProductAlias mProductAlias = mProductAliases.stream().filter(a -> a.getId().equals(Long.valueOf(e.getProductId()))).findFirst().orElse(null);
            if(ObjectUtils.isEmpty(mProductAlias)){
                throw new BoxException("未查询到商品");
            }
            product.setAttrId(mProductAlias.getmAttributesetinstanceId());
            product.setPriceActual(e.getPaidAmount().toString());
            product.setQty(Integer.parseInt(e.getProductQuantity()));
            product.setSaleId(vip.getSaleId());
            product.setVouDisAmt(e.getVoucherAmount());
            product.setShopVouAmt(e.getShopVouAmt());
            return product;
        }).collect(Collectors.toList());
        CreateMRetailEntity createMRetailEntity = new CreateMRetailEntity();
        createMRetailEntity.setDescription("由BOX订单"+order.getOrderSn()+"生成");
        createMRetailEntity.setStoreId(vip.getStoreId());
        createMRetailEntity.setVipId(vip.getVipId());
        createMRetailEntity.setChannel(vip.getChannel());
        createMRetailEntity.setUploadType(vip.getUploadType());
        createMRetailEntity.setProductList(productList);
        if("Y".equals(order.getIsEb())){
            // 根据商品拆分付款明细
            double totAmt = orderDetailList.stream().mapToDouble(e -> new BigDecimal(e.getPriceactual()).multiply(new BigDecimal(e.getProductQuantity())).doubleValue()).sum();
            double vouAmt = orderDetailList.stream().mapToDouble(e -> Optional.ofNullable(e.getVoucherAmount()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(e.getProductQuantity())).doubleValue()).sum();
            double balanceAmt = orderDetailList.stream().mapToDouble(e -> Optional.ofNullable(e.getBalanceAmt()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(e.getProductQuantity())).doubleValue()).sum();
            double shopVouAmt = orderDetailList.stream().mapToDouble(e -> Optional.ofNullable(e.getShopVouAmt()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(e.getProductQuantity())).doubleValue()).sum();
            List<CreateMRetailEntity.PayWay> payWayList = new ArrayList<>();
            if(totAmt > 0D) {
                payWayList.add(getPayWay(OrderPaymentPayWayEnum.MONEY,totAmt,null));
            }
            if(vouAmt > 0D){
                payWayList.add(getPayWay(OrderPaymentPayWayEnum.VOU,vouAmt,order.getVouchersNo()));
            }
            if(balanceAmt > 0D){
                payWayList.add(getPayWay(OrderPaymentPayWayEnum.BALANCE,balanceAmt,null));
            }
            if(shopVouAmt > 0D){
                payWayList.add(getPayWay(OrderPaymentPayWayEnum.SHOP_VOU,shopVouAmt,null));
            }
            createMRetailEntity.setPayList(payWayList);
        }else{
            List<CreateMRetailEntity.PayWay> payWayList = orderPayments.stream().filter( a -> a.getPaidAmount() > 0D).map(e -> {
                CreateMRetailEntity.PayWay payWay = new CreateMRetailEntity.PayWay();
                // 根据box支付类型id获取伯俊支付类型id
                OrderPaymentPayWayEnum paymentPayWayEnum = OrderPaymentPayWayEnum.getByCode(e.getPayway());
                payWay.setPayWayId(paymentPayWayEnum.getBjCode());
                payWay.setPayAmount(e.getPaidAmount().toString());
                payWay.setVouNo(e.getVouNo());
                return payWay;
            }).collect(Collectors.toList());
            createMRetailEntity.setPayList(payWayList);
        }
        return createMRetailEntity;
    }

    private CreateMRetailEntity.PayWay getPayWay(OrderPaymentPayWayEnum paymentPayWayEnum,double amt,String vouNo){
        CreateMRetailEntity.PayWay payment = new CreateMRetailEntity.PayWay();
        payment.setPayWayId(paymentPayWayEnum.getBjCode());
        payment.setPayAmount(String.valueOf(amt));
        payment.setVouNo(vouNo);
        return payment;
    }


    private List<SplitRetailVipEntity> getCustomerVip(String unionId){
        List<CclientVip> vips = customerVipService.findVips(unionId, null);
        List<SplitRetailVipEntity> splitVipList = vips.stream().map(e -> {
            SplitRetailVipEntity splitRetailVipEntity = new SplitRetailVipEntity();
            CVipType vipType = customerVipService.getVipTypeById(e.getcViptypeId());
            splitRetailVipEntity.setVipBrand(vipType.getDescription());
            splitRetailVipEntity.setVipId(e.getId());
            splitRetailVipEntity.setStoreId(e.getcStoreId() == null ? 25277L:e.getcStoreId());
            splitRetailVipEntity.setSaleId(e.getSalesrepId()== null ? null:e.getSalesrepId().toString());
            splitRetailVipEntity.setIfDealer(e.getcCustomerId() != null && !Long.valueOf(176L).equals(e.getcCustomerId()));
            splitRetailVipEntity.setCardNo(e.getCardno());
            return splitRetailVipEntity;
        }).collect(Collectors.toList());
        // 绑定集合店下品牌店的改为集合店/是否经销赋值
        List<Long> storeIds = splitVipList.stream().map(SplitRetailVipEntity::getStoreId).collect(Collectors.toList());
        List<CStore> cStores = icStoreRepository.selectCStoreByIds(storeIds);
        splitVipList.forEach(e -> {
            CStore cStore = cStores.stream().filter(a -> a.getId().equals(e.getStoreId())).findFirst().orElse(null);
            if(ObjectUtils.isEmpty(cStore)){
                throw new BoxException(e.getVipBrand()+"卡绑定门店未查询到，请核实");
            }
            if(ObjectUtils.isNotEmpty(cStore.getcUnionstoreId())){
                e.setStoreId(cStore.getcUnionstoreId());
            }
        });
        return splitVipList;
    }

    private void musterAllot2Box(List<String> allotSku,String sourceNo,Long origId){
        if(allotSku.size() == 0){
            return;
        }
        AllotStorageEntity allotStorageEntity = new AllotStorageEntity();
        allotStorageEntity.setOrigStoreId(origId);
        allotStorageEntity.setDestStoreId(boxStoreId);
        allotStorageEntity.setDescription("BOX调货");
        allotStorageEntity.setSource(sourceNo+"-trans");
        List<AllotStorageEntity.AllotProduct> productList = allotSku.stream().map(e -> {
            AllotStorageEntity.AllotProduct allotProduct = new AllotStorageEntity.AllotProduct();
            allotProduct.setSku(e);
            allotProduct.setQty(1);
            return allotProduct;
        }).collect(Collectors.toList());
        allotStorageEntity.setProduct(productList);
        this.allAllotsStorage(allotStorageEntity);
    }

    /**
     * 先试后买 调拨
     * @param allotSku
     * @param sourceNo
     */
    private void tryAfterAllotToBox(List<String> allotSku,String sourceNo){
        if(allotSku.size() == 0){
            return;
        }
        AllotStorageEntity allotStorageEntity = new AllotStorageEntity();
        allotStorageEntity.setOrigStoreId(musterStoreId);
        allotStorageEntity.setDestStoreId(tryAfterStoreId);
        allotStorageEntity.setDescription("BOX调货");
        allotStorageEntity.setSource(sourceNo+"-trans");
        List<AllotStorageEntity.AllotProduct> productList = allotSku.stream().map(e -> {
            AllotStorageEntity.AllotProduct allotProduct = new AllotStorageEntity.AllotProduct();
            allotProduct.setSku(e);
            allotProduct.setQty(1);
            return allotProduct;
        }).collect(Collectors.toList());
        allotStorageEntity.setProduct(productList);
        this.allAllotsStorage(allotStorageEntity);
    }


    private DeliverEntity createDeliverEntity(String sourceNo,List<BoxDetailsWithBLOBs> details,Logistics logistics,Integer ifVirDelivery){
        List<BoxDetailsWithBLOBs> boxShop = details.stream().filter(e -> {
            return ObjectUtils.isEmpty(e.getEb()) || e.getEb().intValue() == 0;
        }).collect(Collectors.toList());
        if(boxShop.size() == 0){
            throw new BoxException("没有总部发货商品，无需发货");
        }
        List<AllotStorageEntity.AllotProduct> productList = boxShop.stream().map(e -> {
            AllotStorageEntity.AllotProduct allotProduct = new AllotStorageEntity.AllotProduct();
            allotProduct.setSku(e.getSku());
            allotProduct.setQty(1);
            return allotProduct;
        }).collect(Collectors.toList());
        AllotStorageEntity.CusInfo cusInfo = new AllotStorageEntity.CusInfo();
        cusInfo.setContacts(logistics.getName());
        cusInfo.setPhone(logistics.getPhone());
        //设置省市区
        CRegions province = regionsService.getProvince(logistics.getProvince());
        cusInfo.setProvinceId(province.getId().toString());
        CRegions city = regionsService.getCity(logistics.getCity(), province.getId());
        cusInfo.setCityId(city.getId().toString());
        CRegions district = regionsService.getDistrict(logistics.getDistrict(), province.getId(), city.getId());
        cusInfo.setDistrictId(district.getId().toString());
        cusInfo.setAddress(logistics.getAddress());
        DeliverEntity deliverEntity = new DeliverEntity();
        deliverEntity.setSource(sourceNo);
        deliverEntity.setProduct(productList);
        deliverEntity.setCusInfo(cusInfo);
        deliverEntity.setIfVirDelivery(ifVirDelivery);
        return deliverEntity;
    }



    private void endBoxReturn(String boxReturnId,List<String> returnDetailIds,boolean ifWarn){
        BoxReturn boxReturn = boxReturnRepository.selectById(boxReturnId);
        List<BoxReturnDetails> boxReturnDetails = boxReturnDetailsService.listByIds(returnDetailIds);

        // 筛选出需要更新的商品
        List<BoxReturnDetails> updateDetails = boxReturnDetails.stream().filter(e -> {
            return e.getRetStatus().equals(1L) && returnDetailIds.contains(e.getId());
        }).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(updateDetails)){
            return;
        }
        // 监控表更新
        UpdateWrapper<BReturnMall> bReturnMallUpdateWrapper = new UpdateWrapper<>();
        bReturnMallUpdateWrapper.set("status",1);
        bReturnMallUpdateWrapper.eq("box_return_id",boxReturnId);
        ibReturnMallService.update(bReturnMallUpdateWrapper);
        if(!boxReturn.getStatus().equals(BoxReturnStatusEnum.OVER.getCode().longValue())) {
            // 还货单更新
           boxReturnRepository.updateStatus(boxReturn.getId(), BoxReturnStatusEnum.OVER.getCode().longValue(), boxReturn.getStatus());
        }
        // 还货详情更新
        List<BoxReturnDetails> updateReturnDetailList = updateDetails.stream().map(e -> {
            BoxReturnDetails upBoxReturnDetails = new BoxReturnDetails();
            upBoxReturnDetails.setId(e.getId());
            upBoxReturnDetails.setRetStatus(2L);
            return upBoxReturnDetails;
        }).collect(Collectors.toList());
        boxReturnDetailsService.updateBatchById(updateReturnDetailList);
        // 服务单详情更新
        List<BoxDetailsWithBLOBs> updateBoxDetailList = updateDetails.stream().map(e -> {
            BoxDetailsWithBLOBs boxDetailsWithBLOBs = new BoxDetailsWithBLOBs();
            boxDetailsWithBLOBs.setId(e.getBoxDetailId());
            boxDetailsWithBLOBs.setStatus(UltimaBoxDetailsStatusEnum.RETURNED.getCode().longValue());
            return boxDetailsWithBLOBs;
        }).collect(Collectors.toList());
        List<String> boxDetailIds = updateBoxDetailList.stream().map(BoxDetailsWithBLOBs::getId).collect(Collectors.toList());
        // 获取原服务单详情
        List<BoxDetailsWithBLOBs> details = boxDetailsService.listByIds(boxDetailIds);
        // 大内淘商品入库时更新调拨数据
        List<BoxDetailsWithBLOBs> ebDetails =  details.stream().filter(e -> ObjectUtils.isNotEmpty(e.getEb()) && e.getEb().intValue() == 1).collect(Collectors.toList());;
        // 汇总商品调入数据，用于调出的仓位线展示
        if(CollectionUtils.isNotEmpty(ebDetails)){
            countTransTot(ebDetails);
        }
        boxDetailsService.updateBatchById(updateBoxDetailList);
        BoxWithBLOBs boxWithBLOBs = boxRepository.findById(boxReturn.getBoxId());
        if(boxWithBLOBs.getStatus().equals(UltimaBoxStatusEnum.FISHED.getCode().longValue())){
            return;
        }
        BoxReturn updateBoxReturn = new BoxReturn();
        updateBoxReturn.setId(boxReturn.getId());
        Long realNum = ObjectUtils.isEmpty(boxReturn.getRealReturn()) ? 0 : boxReturn.getRealReturn();
        updateBoxReturn.setRealReturn(realNum + updateDetails.size());
        if(!ifWarn){
            // 服务单完成
            // 判断原单商品是否全部进入最终状态
            QueryWrapper<BoxDetailsWithBLOBs> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("box_id",boxWithBLOBs.getId());
            List<BoxDetailsWithBLOBs> list = boxDetailsService.list(queryWrapper);
            // 已完成状态包含：已完成/取消/删款
            // 是否包含未完成状态的商品(排除换码商品)
            long count = list.stream().filter(e -> {
                return  e.getType() != 30L &&
                        !UltimaBoxDetailsStatusEnum.CANCEL.getCode().equals(e.getStatus().intValue()) &&
                        !UltimaBoxDetailsStatusEnum.DELETE.getCode().equals(e.getStatus().intValue()) &&
                        !UltimaBoxDetailsStatusEnum.PAY.getCode().equals(e.getStatus().intValue()) &&
                        !UltimaBoxDetailsStatusEnum.RETURNED.getCode().equals(e.getStatus().intValue()) &&
                        !BoxDetailsStatusEnum.waitRefund.getCode().equals(e.getStatus().intValue()) &&
                        !BoxDetailsStatusEnum.overRefund.getCode().equals(e.getStatus().intValue());
            }).count();
            if(count > 0){
                boxActionService.createBoxAction(boxWithBLOBs.getId(),"商品未全部进入最终状态，主单状态不更新",boxWithBLOBs.getStatus(),"-1");
            }else{
                iSyncBoxReturnStatusJobService.dealAfterFinish(boxWithBLOBs,"还货商品校验完毕，服务单完成");
            }
        }else{
            updateBoxReturn.setIsWarn(1L);
            // 预警
            iSyncBoxReturnStatusJobService.boxReturnError(boxWithBLOBs,"还货异常，部分商品未还");

        }
        boxReturnRepository.updateById(updateBoxReturn);
    }

    private void countTransTot(List<BoxDetailsWithBLOBs> ebDetails){
        long count = ebDetails.stream().filter(e -> {
            return ObjectUtils.isNotEmpty(e.getSendStoreId()) && ObjectUtils.isNotEmpty(e.getSendUnionstoreId());
        }).count();
        if(count == 0){
            return;
        }
        List<BBoxEbTransTot> bBoxEbTransTotList = ebDetails.stream().map(e -> {
            BBoxEbTransTot bBoxEbTransTot = new BBoxEbTransTot();
            bBoxEbTransTot.setSkuId(Long.valueOf(e.getProductId()));
            bBoxEbTransTot.setSkuNo(e.getSku());
            bBoxEbTransTot.setSendStoreId(e.getSendStoreId());
            bBoxEbTransTot.setSendUnionStoreId(e.getSendUnionstoreId());
            bBoxEbTransTot.setReceiptStoreId(boxInTransitStoreId);
            bBoxEbTransTot.setQty(Long.valueOf(e.getProductQuantity()));
            return bBoxEbTransTot;
        }).collect(Collectors.toList());
        boxEbTransTotService.batchCreateOrUpdateTransTot(bBoxEbTransTotList);
        List<BBoxTransPool> boxTransPoolList = ebDetails.stream().map(e -> {
            BBoxTransPool bBoxTransPool = new BBoxTransPool();
            bBoxTransPool.setId(Long.valueOf(e.getProductId()));
            bBoxTransPool.setSku(e.getSku());
            bBoxTransPool.setEbBoxQty(Long.valueOf(e.getProductQuantity()));
            return bBoxTransPool;
        }).collect(Collectors.toList());
        boxTransPoolService.batchCreateOrUpdateTransPool(boxTransPoolList);
    }


    // 查询聚水潭是否推送成功
    private boolean checkIfPush(CreateEbMallRetContext createEbMallRetContext){
        Query2JSTDTO query2JSTDTO = getQuery2JSTDTO(createEbMallRetContext);
        log.info("查询聚水潭推送记录,param={}",JSON.toJSONString(query2JSTDTO));
        ResponseResult<List<Query2JSTRespDTO>> result = retailApi.jstQuery(query2JSTDTO);
        log.info("查询聚水潭推送记录,result={}",JSON.toJSONString(result));
        if(result.getCode() != 0){
            log.info("查询聚水潭失败,boxSn={},error={}",createEbMallRetContext.getBoxSn(),result.getMsg());
            throw new BoxException(result.getMsg());
        }
        return CollectionUtils.isNotEmpty(result.getData());
    }

    private Query2JSTDTO getQuery2JSTDTO(CreateEbMallRetContext createEbMallRetContext){
        List<String> outAsIds = createEbMallRetContext.getMallRetItems().stream().map(CreateEbMallRetContext.MallRetItem::getReturnDetailId).collect(Collectors.toList());
        Query2JSTDTO query2JSTDTO = new Query2JSTDTO();
        query2JSTDTO.setShopId(shopId.toString());
        query2JSTDTO.setOuterAsIds(outAsIds);
        return query2JSTDTO;
    }

    // 推送数据到聚水潭
    private void push2JST(CreateEbMallRetContext createEbMallRetContext){
        List<Push2JSTDTO> push2JSTDTO = getPush2JSTDTO(createEbMallRetContext);
        log.info("推送数据到聚水潭,param={}",JSON.toJSONString(push2JSTDTO));
        ResponseResult<?> result = retailApi.jstPush(push2JSTDTO);
        log.info("推送数据到聚水潭,result={}", JSON.toJSONString(result));
        if(result.getCode() != 0){
            log.info("推送数据到聚水潭失败,boxSn={},error={}",createEbMallRetContext.getBoxSn(),result.getMsg());
            throw new BoxException(result.getMsg());
        }
    }

    private List<Push2JSTDTO> getPush2JSTDTO(CreateEbMallRetContext createEbMallRetContext) {
        List<Push2JSTDTO> list = new ArrayList<>();
        for(CreateEbMallRetContext.MallRetItem mallRetItem : createEbMallRetContext.getMallRetItems()) {
            Push2JSTDTO push2JSTDTO = new Push2JSTDTO();
            push2JSTDTO.setShopId(shopId);
            push2JSTDTO.setOuterAsId(mallRetItem.getReturnDetailId());
            push2JSTDTO.setSoId(createEbMallRetContext.getBoxSn());
            push2JSTDTO.setType(createEbMallRetContext.getType());
            push2JSTDTO.setLogisticsCompany(createEbMallRetContext.getExpressCompany());
            push2JSTDTO.setLId(createEbMallRetContext.getExpressNo());
            push2JSTDTO.setTotalAmount(mallRetItem.getPrice());
            push2JSTDTO.setRefund(mallRetItem.getPrice());
            push2JSTDTO.setReceiverState(parseSFXmlUtil.getD_PROVINCE());
            push2JSTDTO.setReceiverCity(parseSFXmlUtil.getD_CITY());
            push2JSTDTO.setReceiverDistrict(parseSFXmlUtil.getD_COUNTY());
            push2JSTDTO.setReceiverAddress(parseSFXmlUtil.getD_ADDRESS());
            Push2JSTDTO.Push2JSTDTOItem push2JSTDTOItem = new Push2JSTDTO.Push2JSTDTOItem();
            push2JSTDTOItem.setSkuId(mallRetItem.getSku());
            push2JSTDTOItem.setQty(mallRetItem.getQty().intValue());
            push2JSTDTOItem.setAmount(mallRetItem.getPrice());
            push2JSTDTOItem.setOuterAiId(createEbMallRetContext.getSourceCode());
            push2JSTDTO.setItems(Collections.singletonList(push2JSTDTOItem));
            list.add(push2JSTDTO);
        }
        return list;
    }

}
