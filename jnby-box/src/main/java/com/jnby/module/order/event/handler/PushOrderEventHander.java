package com.jnby.module.order.event.handler;

import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.jnby.common.ProducerUtil;
import com.jnby.common.listener.router.ActivitySencCouponRouter;
import com.jnby.common.listener.router.msg.util.MsgTagUtil;
import com.jnby.module.marketing.coupon.service.ICouponActivityService;
import com.jnby.module.order.context.PushOrderContext;
import com.jnby.module.order.event.PushOrderEvent;
import com.jnby.module.order.service.IOmsService;
import com.jnby.module.order.service.box.IBoxService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022/1/21 11:27 上午
 * @Version 1.0
 */
@Component
@Slf4j
@RefreshScope
public class PushOrderEventHander{
    private final int nbThrowExceptions = 1;

    @Autowired
    private IOmsService iOmsService;

    @Autowired
    private IBoxService boxService;

    @Autowired
    private ICouponActivityService iCouponActivityService;

    @Autowired
    private ProducerUtil producerUtil;

    @Value("${coupon.activityId}")
    private String activityId;

    private volatile int gotExceptions;


    @AllowConcurrentEvents
    @Subscribe
    public void processMyEvent(final PushOrderEvent event) {
        log.info("push order event {}", event.getBox().getBoxSn());
        PushOrderContext context = PushOrderContext.builder()
                .box(event.getBox())
                .boxDetails(event.getBoxDetails())
                .logistics(event.getLogistics())
                .expressId(event.getExpressId())
                .build();
        try{
            iOmsService.pushOrder(context);
            //推单成功发送订阅消息
            producerUtil.send(JSON.toJSONBytes(event.getBox()), ActivitySencCouponRouter.tags);
        }catch (Exception e){
            log.error("push order error,boxSn={},user_token={}, message = {}",event.getBox().getBoxSn(),event.getUserToken(),e.getMessage(),e);
            if(gotExceptions < nbThrowExceptions){
                gotExceptions++;
                throw new RuntimeException("FAIL");
            }
            // 超出允许异常次数
            gotExceptions = 0;

            String message = "";
            if(StringUtils.isNotBlank(e.getMessage()) ){
                if(e.getMessage().length() > 150){
                    // 截取150位
                    message = "取消原因："+e.getMessage().substring(0,150);
                }else{
                    message = "取消原因："+e.getMessage();
                }
            }
            boxService.cancelBox(context.getBox().getId(),"推送服务单到第三方系统（OMS/伯俊寄售单）异常，系统自动取消。"+message);
            // 发送缺货、取消订单消息
            producerUtil.send(JSON.toJSONBytes(context.getBox().getId()), MsgTagUtil.DELIVER_CANCEL_OR_NO_STOCK_MSG_TAG);
        }
    }
}
