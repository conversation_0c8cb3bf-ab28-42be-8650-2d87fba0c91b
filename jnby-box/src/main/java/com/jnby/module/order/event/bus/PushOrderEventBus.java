package com.jnby.module.order.event.bus;

import com.jnby.base.service.IBusEventsService;
import com.jnby.module.order.event.BaseEventBus;
import com.jnby.module.order.event.PushOrderEvent;
import com.jnby.module.order.event.handler.PushOrderEventHander;
import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * 搭配师搭盒推单异步事件
 * <AUTHOR>
 * @Date 2022/1/21 11:24 上午
 * @Version 1.0
 */
@Service
@Slf4j
public class PushOrderEventBus implements InitializingBean, BaseEventBus<PushOrderEvent> {

    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private IBusEventsService iBusEventsService;

    @Autowired
    private PushOrderEventHander pushOrderEventHander;

    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(pushOrderEventHander);
    }

    @Override
    public void post(PushOrderEvent pushOrderEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(pushOrderEvent);
    }
}
