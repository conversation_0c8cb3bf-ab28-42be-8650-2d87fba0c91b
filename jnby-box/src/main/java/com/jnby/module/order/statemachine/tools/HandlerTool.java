package com.jnby.module.order.statemachine.tools;

import com.jnby.infrastructure.box.mapper.DefinitionEventMapper;
import com.jnby.infrastructure.box.mapper.DefinitionStateMapper;
import com.jnby.infrastructure.box.mapper.DefinitionStateWithEventMapper;
import com.jnby.module.order.statemachine.action.base.BaseAction;
import com.jnby.module.order.statemachine.action.base.BaseErrorAction;
import com.jnby.module.order.statemachine.action.base.DefaultAction;
import com.jnby.module.order.statemachine.action.base.DefaultErrorAction;
import com.jnby.module.order.statemachine.constant.StateMachineConstant;
import com.jnby.module.order.statemachine.entity.DefinitionStateWithEvent;
import com.jnby.module.order.statemachine.entity.DefinitionEvent;
import com.jnby.module.order.statemachine.entity.DefinitionState;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:处理状态机工具类
 */
@Component
public class HandlerTool {
    @Resource
    private List<BaseErrorAction> errorActions;

    @Resource
    private List<BaseAction> baseOrderActions;

    @Resource
    private DefinitionEventMapper definitionEventMapper;

    @Resource
    private DefinitionStateWithEventMapper definitionStateWithEventMapper;

    @Resource
    private DefinitionStateMapper definitionStateMapper;

    @Resource
    private DefaultAction defaultAction;

    @Resource
    private DefaultErrorAction defaultErrorAction;

    //  服务单
    private Set<DefinitionEvent> definitionEventSets = new HashSet<>();

    private List<DefinitionEvent> definitionEvents = new ArrayList<>();

    private Set<DefinitionState> definitionStateSets = new HashSet<>();

    private List<DefinitionState> definitionStates = new ArrayList<>();

    private List<DefinitionStateWithEvent> definitionStateWithEvents = new ArrayList<>();


    //服务子单

    private Set<DefinitionEvent> definitionSonEventSets = new HashSet<>();

    private List<DefinitionEvent> definitionSonEvents = new ArrayList<>();

    private Set<DefinitionState> definitionSonStateSets = new HashSet<>();

    private List<DefinitionState> definitionSonStates = new ArrayList<>();

    private List<DefinitionStateWithEvent> definitionSonStateWithEvents = new ArrayList<>();


    @PostConstruct
    public void init() {
        // 服务单
        definitionEvents = definitionEventMapper.selectAllByMachineId(StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID);
        if (!definitionEvents.isEmpty()) {
            definitionEventSets = definitionEvents.stream().collect(Collectors.toSet());
        }
        definitionStates = definitionStateMapper.selectAllByMachineId(StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID);
        if (!definitionStates.isEmpty()) {
            definitionStateSets = definitionStates.stream().collect(Collectors.toSet());
        }
        definitionStateWithEvents = definitionStateWithEventMapper.selectAllByMachineId(StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID);


        // 服务子单
        definitionSonEvents = definitionEventMapper.selectAllByMachineId(StateMachineConstant.SERVICE_ORDER_DETAIL_STATE_MACHINE_ID);
        if (!definitionSonEvents.isEmpty()) {
            definitionSonEventSets = definitionEvents.stream().collect(Collectors.toSet());
        }
        definitionSonStates = definitionStateMapper.selectAllByMachineId(StateMachineConstant.SERVICE_ORDER_DETAIL_STATE_MACHINE_ID);
        if (!definitionSonStates.isEmpty()) {
            definitionSonStateSets = definitionStates.stream().collect(Collectors.toSet());
        }
        definitionSonStateWithEvents = definitionStateWithEventMapper.selectAllByMachineId(StateMachineConstant.SERVICE_ORDER_DETAIL_STATE_MACHINE_ID);

    }


    public BaseAction getActionByName(String eventValue) {
        if (StringUtils.isEmpty(eventValue)) {
            throw new RuntimeException("事件名称不能为空 检查状态机数据配置");
        }
        return baseOrderActions.stream().filter(item -> item.getType().equals(eventValue)).collect(Collectors.toList()).stream().findFirst().orElse(defaultAction);
    }


    public BaseErrorAction getErrorActionByName(String errorValue) {
        if (StringUtils.isEmpty(errorValue)) {
            throw new RuntimeException("错误事件名称不能为空 检查状态机数据配置");
        }
        return errorActions.stream().filter(item -> item.getType().equals(errorValue)).collect(Collectors.toList()).stream().findFirst().orElse(defaultErrorAction);
    }

    /**
     * 获取全部事件 默认主服务单
     *
     * @return
     */
    public Set getEvents() {
       return this.getEvents(StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID);
    }

    /**
     * 根据 machineId 获取全部事件
     * @param machineId
     * @return
     */
    public Set getEvents(String machineId){
        if(StringUtils.equals(machineId,StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID)){
            return definitionEventSets;
        }
        return definitionSonEventSets;
    }


    /**
     * 根据value 获取DefinitionEvent 默认是主服务单
     * @param value
     * @return
     */
    public DefinitionEvent getEventByValue(String value) {
        return this.getEventByValue(value,StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID);
    }

    /**
     * 根据value和machineId 查询DefinitionEvent
     * @param value
     * @param machineId
     * @return
     */
    public DefinitionEvent getEventByValue(String value, String machineId) {
        if(StringUtils.equals(machineId,StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID)){

            return definitionEvents.stream().filter(definitionEvent -> value.equals(definitionEvent.getEventValue()))
                    .findFirst().orElse(new DefinitionEvent());
        }
        return definitionSonEvents.stream().filter(definitionEvent -> value.equals(definitionEvent.getEventValue()))
                .findFirst().orElse(new DefinitionEvent());
    }
    /**
     * 获取全部状态 默认主服务单
     * @return
     */
    public Set<DefinitionState> getDefinitionStates() {
        return this.getDefinitionStates(StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID);
    }

    /**
     * 根据服务单值 获取全部状态
     * @param machineId
     * @return
     */
    public Set<DefinitionState> getDefinitionStates(String machineId) {
        if(StringUtils.equals(machineId,StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID)){
            return definitionStateSets;
        }
        return definitionSonStateSets;
    }

    /**
     * 获取全部的DefinitionStateList 默认的主服务单
     * @return
     */
    public List<DefinitionState> getDefinitionStateList() {
        return this.getDefinitionStateList(StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID);
    }

    /**
     * 根据machineId 获取全部的DefinitionStateList
     * @param machineId
     * @return
     */
    public List<DefinitionState> getDefinitionStateList(String machineId) {
        if(StringUtils.equals(machineId,StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID)){
            return definitionStates;
        }
        return definitionSonStates;
    }


    /**
     * @description: 根据status 获取DefinitionState 默认主服务单
     * @param: [status]
     * @return: com.jnby.statemachine.entity.machine.DefinitionState
     * @date: 2021/10/11
     */
    public DefinitionState getDefinitionStateByStatus(String status) {
       return this.getDefinitionStateByStatus(status,StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID);
    }

    /**
     * @description: 获取全部事件与状态关系 默认主服务单
     * @param: []
     * @return: java.util.List<com.jnby.statemachine.entity.DefinitionStateWithEvent>
     * @date: 2021/10/18
     */
    public List<DefinitionStateWithEvent> getDefinitionStateWithEvents() {
        return this.getDefinitionStateWithEvents(StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID);
    }

    /**
     * 根据machineId 获取全部事件与状态关系
     * @param machineId
     * @return
     */
    public List<DefinitionStateWithEvent> getDefinitionStateWithEvents(String machineId) {
        if(StringUtils.equals(machineId,StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID)){
            return definitionStateWithEvents;
        }
        return definitionSonStateWithEvents;
    }



    /**
     * 根据状态值和machineId 获取DefinitionState
     * @param status
     * @param machineId
     * @return
     */
    public DefinitionState getDefinitionStateByStatus(String status, String machineId) {
        if(StringUtils.equals(machineId,StateMachineConstant.SERVICE_ORDER_STATE_MACHINE_ID)){
            return definitionStates.stream().filter(definitionState -> status.equals(definitionState.getStateValue()))
                    .findFirst().orElse(new DefinitionState());
        }
        return definitionSonStates.stream().filter(definitionState -> status.equals(definitionState.getStateValue()))
                .findFirst().orElse(new DefinitionState());
    }


}