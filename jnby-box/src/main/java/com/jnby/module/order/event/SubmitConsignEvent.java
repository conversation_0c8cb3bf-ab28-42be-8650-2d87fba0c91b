package com.jnby.module.order.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jnby.module.order.context.SubmitConsignContext;
import org.killbill.bus.api.BusEvent;

import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2022/1/21 2:01 下午
 * @Version 1.0
 */
public class SubmitConsignEvent implements BusEvent {
    private final SubmitConsignContext context;
    private final Long searchKey1;
    private final Long searchKey2;
    private final UUID userToken;
    private final String type;

    @JsonCreator
    public SubmitConsignEvent(@JsonProperty("context") final SubmitConsignContext context,
                              @JsonProperty("type") final String type,
                              @JsonProperty("searchKey1") final Long searchKey1,
                              @JsonProperty("searchKey2") final Long searchKey2,
                              @JsonProperty("userToken") final UUID userToken){
        this.searchKey1 = searchKey1;
        this.searchKey2 = searchKey2;
        this.userToken = userToken;
        this.context = context;
        this.type = type;
    }

    @Override
    public Long getSearchKey1() {
        return this.searchKey1;
    }

    @Override
    public Long getSearchKey2() {
        return searchKey2;
    }

    @Override
    public UUID getUserToken() {
        return this.userToken;
    }

    public SubmitConsignContext getContext() {
        return context;
    }

    public String getType() {
        return type;
    }
}
