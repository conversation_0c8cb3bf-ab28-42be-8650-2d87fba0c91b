package com.jnby.module.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.infrastructure.box.mapper.*;
import com.jnby.infrastructure.box.model.*;
import com.jnby.infrastructure.wx.mapper.CalcPriceMapper;
import com.jnby.module.marketing.pocket.service.IPocketTemplateService;
import com.jnby.module.order.context.CalcPriceContext;
import com.jnby.module.order.entity.BoxEntity;
import com.jnby.module.order.entity.CalcPriceResp;
import com.jnby.module.order.enums.BoxTypeEnum;
import com.jnby.module.order.enums.CalcPriceChannelEnum;
import com.jnby.module.order.enums.CalcPriceIfUseEnum;
import com.jnby.module.order.enums.CalcPriceTypeEnum;
import com.jnby.module.order.service.ICalcPriceService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/9
 */
@Service
public class CalcPriceServiceImpl implements ICalcPriceService {
    private static final Logger logger = LoggerFactory.getLogger(CalcPriceServiceImpl.class);

    @Autowired
    CalcPriceMapper calcPriceMapper;

    @Autowired
    BoxMapper boxMapper;

    @Autowired
    SysUserMapper userMapper;

    @Autowired
    FashionerMapper fashionerMapper;

    @Autowired
    SysStoreMapper sysStoreMapper;

    @Autowired
    IPocketTemplateService pocketTemplateService;


    @Override
    public void calcPromotionPrice(CalcPriceContext context) {
        Map<String, Object> map = new HashMap<>();
        map.put("unionid",context.getUnionid());
        map.put("params",JSON.toJSONString(context.getParams()));
        map.put("billno",context.getBillno());
        map.put("vouchers",JSON.toJSONString(context.getVouchers()));
        map.put("goods",JSON.toJSONString(context.getGoods()));
        logger.info("价格计算入参 unionId = {}, params = {}", context.getUnionid(), JSON.toJSONString(map));
        try{
            calcPriceMapper.calc(map);
        }catch (Exception e){
            logger.error("价格计算calc异常,入参params = {}, error = {}",context.toJson(),e.getMessage());
        }
        /**
         * 组建返回对象
         */
        CalcPriceResp resp = new CalcPriceResp();
        resp.setBillno((String)map.get("billno"));
        resp.setRet((Integer) map.get("ret"));
        resp.setMsg((String)map.get("msg"));
        resp.setOutGoodsJson((String)map.get("outgoodsjson"));
        resp.setOutDiscountJson((String)map.get("outdiscountjson"));
        resp.setOutVoucherList((String)map.get("outvoucherlist"));
        resp.setIntegral((String)map.get("integral"));

        context.setBillno(resp.getBillno());
        context.setRet(resp.getRet());
        context.setMsg(resp.getMsg());
        CalcPriceContext.OutGoods outGoods = JSON.toJavaObject(JSONObject.parseObject(resp.getOutGoodsJson()),CalcPriceContext.OutGoods.class);
        CalcPriceContext.OutDiscount outDiscount = JSON.toJavaObject(JSONObject.parseObject(resp.getOutDiscountJson()),CalcPriceContext.OutDiscount.class);
        CalcPriceContext.OutVoucher outVoucher = JSON.toJavaObject(JSONObject.parseObject(resp.getOutVoucherList()),CalcPriceContext.OutVoucher.class);
        CalcPriceContext.Integral integral = JSON.toJavaObject(JSONObject.parseObject(resp.getIntegral()),CalcPriceContext.Integral.class);
        context.setOutGoods(outGoods);
        context.setOutDiscount(outDiscount);
        context.setOutVoucher(outVoucher);
        context.setIntegral(integral);
    }


    @Override
    public CalcPriceContext calcPrice(String unionId,List<CalcPriceContext.GoodsItem> goodsItems,
                                      List<CalcPriceContext.VoucherItem> voucherItems,
                                      CalcPriceContext.Params params,
                                      String billNo){
        CalcPriceContext context = new CalcPriceContext();
        context.setUnionid(unionId);
        CalcPriceContext.Goods goods = new CalcPriceContext.Goods();
        goods.setROWS(goodsItems.size());
        goods.setGoodsItem(goodsItems);
        context.setGoods(goods);
        CalcPriceContext.Vouchers vouchers = new CalcPriceContext.Vouchers();
        vouchers.setRows(voucherItems.size());
        vouchers.setVoucheritem(voucherItems);
        context.setVouchers(vouchers);
        context.setParams(params);
        context.setBillno(billNo);
        calcPromotionPrice(context);
        context.setUseBalanceAmt(context.getOutGoods().getTotpriceactual());
        return context;
    }


}
