package com.jnby.module.order.context;


import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SubscribeContext {

    private String unionId;
    /**
     * 订阅方式 0 支付  1积分兑换  3邀请码 4心意盒子
     */
    private Long subType;
    /**
     *  状态(0:未支付;1:已支付;2:已退款)
     */
    private Long status;

    /**
     * 订阅渠道 0普通订阅  1心意盒子 2预售盒子 3导购邀请
     */
    private Long channel;

    /**
     * 邀请码
     */
    private String invitationCode;


    /**
     * 订阅天数
     */
    private int subscribeDays;


    private String channelId;


}
