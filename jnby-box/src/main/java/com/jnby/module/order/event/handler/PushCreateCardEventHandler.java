package com.jnby.module.order.event.handler;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.jnby.application.corp.dto.request.VerifyWhiteUserReq;
import com.jnby.base.context.CreateVipByDemogicContext;
import com.jnby.base.repository.ICStoreRepository;
import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.base.service.IBWhiteOpenFlagService;
import com.jnby.base.service.IMProductService;
import com.jnby.infrastructure.bojun.mapper.CVipTypeMapper;
import com.jnby.infrastructure.bojun.mapper.CclientVipMapper;
import com.jnby.infrastructure.bojun.model.*;
import com.jnby.infrastructure.box.model.CustomerDetails;
import com.jnby.module.customer.vip.service.IExternalDemoVipService;
import com.jnby.module.facade.context.AddDamoMemberContext;
import com.jnby.module.facade.service.IDamoMemberService;
import com.jnby.module.oauth.service.IBWhiteUserInfoService;
import com.jnby.module.order.enums.UseWhiteTypeEnum;
import com.jnby.module.order.event.PushCreateCardEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;



/**
 * <AUTHOR>
 * @Date 2022/1/21 11:27 上午
 * @Version 1.0
 */
@Component
@Slf4j
@RefreshScope
public class PushCreateCardEventHandler {
    private final int nbThrowExceptions = 1;

    private volatile int gotExceptions;

    @Resource
    private ICustomerDetailsRepository iCustomerDetailsRepository;

    @Resource
    private CVipTypeMapper cVipTypeMapper;

    @Resource
    private CclientVipMapper cclientVipMapper;

    @Resource
    private IExternalDemoVipService iExternalDemoVipService;


    @Resource
    private IMProductService imProductService;

    @Resource
    private IBWhiteOpenFlagService ibWhiteOpenFlagService;

    @Resource
    private IBWhiteUserInfoService ibWhiteUserInfoService;

    @Resource
    private ICStoreRepository cStoreRepository;


    @Resource
    private IDamoMemberService iDamoMemberService;


    /**
     * 伯俊卡 与 达摩 卡名称对应
     *
     * @return
     */
    Map<String, String> getBrandWithDe() {
        // LESS:2822095692,蓬马:6924108367,速写:2504948039,JNBY:2738574294,jnbybyJNBY:4,APN73:15,江南布衣+:5,Outlet:11,JNBYHOME:8348044436
        Map<String, String> map = new HashMap<>();
        // 左边 vipType卡名字   右边 达摩卡名字
        map.put("江南布衣+", "江南布衣+");
        map.put("OUTLETS", "Outlet");
        return map;
    }


    @AllowConcurrentEvents
    @Subscribe
    public void processMyEvent(final PushCreateCardEvent event) {
        log.info("push create card event event:{} ", JSONObject.toJSONString(event));
        log.info("开卡unionId:{} userToken:{}",event.getUnionId(),event.getUserToken());
        if(CollectionUtils.isEmpty(event.getSpuIds())){
            return;
        }
        log.info("搭盒补货结算前开卡unionId:{}  详细参数:{}",event.getUnionId(),JSONObject.toJSONString(event));
        List<String> brands = getBrands(event.getSpuIds());
        String unionId = event.getUnionId();
        if (CollectionUtils.isEmpty(brands)) {
            log.info("spu不符合开卡条件");
            return;
        }

        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(event.getUnionId());
        VerifyWhiteUserReq verifyWhiteUserReq = new VerifyWhiteUserReq();
        verifyWhiteUserReq.setWhiteType(UseWhiteTypeEnum.DEMOGIC_CREATE_CARD.getCode().longValue());
        verifyWhiteUserReq.setUserId(customerDetails.getUnionid());

        // 白名单开启  不是白名单不开卡
        if(ibWhiteOpenFlagService.verifyWhiteTypeOpen(verifyWhiteUserReq) && !ibWhiteUserInfoService.verifyWhiteUser(verifyWhiteUserReq)){
            return;
        }

        CclientVip cclientVip = new CclientVip();
        cclientVip.setUnionid(unionId);
        List<CclientVip> cclientVips = cclientVipMapper.selectListBySelective(cclientVip);

        List<Long> vipIds = cclientVips.stream().map(e -> e.getcViptypeId()).collect(Collectors.toList());
        List<CVipType> cVipTypes = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(vipIds)){
            cVipTypes = cVipTypeMapper.selectByPrimaryIds(vipIds);
        }

        // 处理开卡品牌
        for (CVipType cVipType : cVipTypes) {
            if(StringUtils.isEmpty(cVipType.getDescription())){
                continue;
            }
            if (cVipType.getDescription().contains("江南布衣+")) {
                brands.remove("5");
            }
            if (cVipType.getDescription().contains("OUTLETS")) {
                brands.remove("11");
            }
        }
        if(CollectionUtils.isEmpty(brands)){
            return;
        }

        String storeId = "25277";
        CStore cStore = cStoreRepository.findCstoreById(Long.valueOf(storeId));

        try {
            int createCardCount = handleCreateCard(customerDetails, brands, cStore);
            if (brands.size() != createCardCount) {
                throw new RuntimeException("搭盒补货结算前开卡开卡失败");
            }
        } catch (Exception e) {
            log.error("搭盒补货结算前开卡开卡失败 push create card error,unionId={} , spus:{} ,user_token={}, e = {}, message = {}", event.getUnionId(), event.getSpuIds(), event.getUserToken(), e, e.getMessage());
            if (gotExceptions < nbThrowExceptions) {
                gotExceptions++;
                throw new RuntimeException("FAIL");
            }
            // 超出允许异常次数
            gotExceptions = 0;
        }
    }

    /**
     * 开卡
     *
     * @param customerDetails
     * @param brands
     * @return
     */
//    int handleCreateCard(CustomerDetails customerDetails, List<String> brands, CStore cStore) {
//        Map<String, String> BrandWithDeMap = getBrandWithDe();
//        int createCardCount = 0;
//        for (String brand : brands) {
//            // 对应关系不存在 不开卡
//            if (!BrandWithDeMap.containsKey(brand)) {
//                continue;
//            }
//            try {
//                CreateVipByDemogicContext createVipByDemogicContext = new CreateVipByDemogicContext();
//                createVipByDemogicContext.setMobile(customerDetails.getPhone());
//                if (StringUtils.isNotEmpty(customerDetails.getNickName())) {
//                    createVipByDemogicContext.setName(customerDetails.getNickName());
//                } else {
//                    createVipByDemogicContext.setName(customerDetails.getPhone());
//                }
//                createVipByDemogicContext.setUnionid(customerDetails.getUnionid());
//                // 客户信息 性别 0女 1男
//
//                int sex = 0;
//                if (customerDetails.getGender() == null) {
//                    sex = 0;
//                }
//                // 女
//                if (customerDetails.getGender() != null && customerDetails.getGender().equals(0)) {
//                    sex = 2;
//                }
//                // 男
//                if (customerDetails.getGender() != null && customerDetails.getGender().equals(1)) {
//                    sex = 1;
//                }
//                createVipByDemogicContext.setSex(sex);
//                createVipByDemogicContext.setBrandName(BrandWithDeMap.get(brand));
//                // 门店
//                if (cStore != null) {
//                    createVipByDemogicContext.setStoreCode(cStore.getCode());
//                    createVipByDemogicContext.setStoreName(cStore.getName());
//                }
//                iExternalDemoVipService.createVipByDemo(createVipByDemogicContext);
//                createCardCount += 1;
//            } catch (Exception e) {
//                log.error("异步开卡失败 e:{}", e.getMessage());
//            }
//        }
//        return createCardCount;
//    }


    /**
     * 处理spu 到品牌
     * @param spuIds
     * @return
     */
    List<String> getBrands(List<String> spuIds){
        QueryWrapper<MProduct> mProductQueryWrapper = new QueryWrapper<>();
        mProductQueryWrapper.in("id",spuIds);
        mProductQueryWrapper.select("PRODUCT_LIFE_CYCLE as productLifeCycle");
        List<MProduct> list = imProductService.list(mProductQueryWrapper);
        List<String> productLifeCycles = list.stream().map(e->e.getProductLifeCycle()).collect(Collectors.toList());

        List<String> brands = new ArrayList<>();

        // 当季，开江南布衣+卡，
        if(productLifeCycles.contains("Y")){
            brands.add("5");
        }
        //往季，开奥莱卡
        if(productLifeCycles.contains("N")){
            brands.add("11");
        }
        return brands;
    }


    int handleCreateCard(CustomerDetails customerDetails, List<String> brands, CStore cStore) {
        int createCardCount = 0;
        for (String brand : brands) {
            try {
                // 客户信息 性别 0女 1男
                int sex = 0;
                // 女
                if (customerDetails.getGender() != null && customerDetails.getGender().equals(0)) {
                    sex = 2;
                }
                // 男
                if (customerDetails.getGender() != null && customerDetails.getGender().equals(1)) {
                    sex = 1;
                }

                AddDamoMemberContext addDamoMemberContext = new AddDamoMemberContext();
                addDamoMemberContext.setUnionId(customerDetails.getUnionid());
                addDamoMemberContext.setSex(String.valueOf(sex));
                if (StringUtils.isNotEmpty(customerDetails.getNickName())) {
                    addDamoMemberContext.setName(customerDetails.getNickName());
                } else {
                    addDamoMemberContext.setName(customerDetails.getPhone());
                }
                addDamoMemberContext.setName(customerDetails.getNickName());
                addDamoMemberContext.setBrandId(brand);
                addDamoMemberContext.setOpenCardDate(DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").print(DateTime.now()));
                // 门店
                if (cStore != null) {
                    addDamoMemberContext.setOpenStoreCode(cStore.getCode());
                    addDamoMemberContext.setMainStoreCode(cStore.getCode());
                }
                iDamoMemberService.addPosMemberWithCreateCard(addDamoMemberContext);

                createCardCount += 1;
            } catch (Exception e) {
                log.error("异步开卡失败 e:{}", e.getMessage());
            }
        }
        return createCardCount;
    }
}
