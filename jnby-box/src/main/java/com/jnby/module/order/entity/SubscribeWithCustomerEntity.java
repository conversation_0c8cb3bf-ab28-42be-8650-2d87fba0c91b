package com.jnby.module.order.entity;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SubscribeWithCustomerEntity {
    private String id;

    @ApiModelProperty(value = "订阅编号")
    private String subSn;

    private String serialNumber;

    private String daystr;

    @ApiModelProperty(value = "状态(0:未支付;1:已支付;2:已退款)")
    private Long status;

    @ApiModelProperty(value = "订阅配置id")
    private String sysSubId;

    @ApiModelProperty(value = "unionid")
    private String unionid;

    @ApiModelProperty(value = "搭配师id")
    private String fashionerId;


    @ApiModelProperty(value = "搭配记录id")
    private String configureId;


    @ApiModelProperty(value = "boxId")
    private String boxId;


    @ApiModelProperty(value = "创建时间")
    private Date createTime;


    @ApiModelProperty(value = "订阅开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;


    @ApiModelProperty(value = "订阅结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;


    @ApiModelProperty(value = "退订时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date unsubscribeTime;


    @ApiModelProperty(value = "退订金额")
    private Double price;


    @ApiModelProperty(value = "退订备注")
    private String memo;


    @ApiModelProperty(value = "订阅方式 0 支付  1积分兑换  2权益兑换  3邀请码 4心意盒子 5导购邀请 6金币兑换 7满送续订")
    private Long subType;


    private String formId;


    @ApiModelProperty(value = "邀请码")
    private String inviteCode;


    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    @ApiModelProperty(value = "心意盒子ID")
    private String mindId;


    @ApiModelProperty(value = "订阅渠道 0普通订阅  1心意盒子 2预售盒子 3导购邀请")
    private Long channel;


    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "搭配师名字")
    private String fashionerName;

    @ApiModelProperty(value = "搭配师类型")
    private String fashionerType;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "用户分类id")
    private String categoryId;
}
