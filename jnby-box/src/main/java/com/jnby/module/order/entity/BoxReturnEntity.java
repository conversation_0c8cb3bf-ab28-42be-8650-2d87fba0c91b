package com.jnby.module.order.entity;

import com.jnby.infrastructure.box.model.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: lwz
 * @Date: 2021/11/8 15:30
 * @Description: BoxReturnEntity
 * @Version 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BoxReturnEntity {
    private BoxReturn boxReturn;
    private CustomerDetails customerDetails;
    private BoxWithBLOBs box;
    private List<BoxDetailsWithBLOBs> boxDetails;
    private List<BoxReturnDetails> boxReturnDetails;
    private Logistics logistics;
    private BigDecimal amount;
    private String userId;
    private SysUser sysUser;
    /**
     * 是否有物流
     */
    private Boolean expressFlag;

    /**
     * 寄回物流信息
     */
    private Express express;
    /**
     * box的上个状态
     */
    private Long boxLastStatus;


    /**
     * 经销单数据
     */
    private LogisticsReconciliation logisticsReconciliation;
}
