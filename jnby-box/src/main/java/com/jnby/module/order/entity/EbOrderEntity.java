package com.jnby.module.order.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/3/2 8:57
 */
@Data
public class EbOrderEntity {

    /**
     * 是否结案，结案的没有物流信息
     */
    private boolean isClose;

    /**
     * 物流公司
     */
    private String expressName;

    /**
     * 物流单号
     */
    private String expressNo;

    /**
     * 内淘单id
     */
    private Long ebSoId;

    /**
     * 内淘单好
     */
    private String ebSoNo;

    /**
     * 包含的商品skuId
     */
    private List<Long> skuIdList;


}
