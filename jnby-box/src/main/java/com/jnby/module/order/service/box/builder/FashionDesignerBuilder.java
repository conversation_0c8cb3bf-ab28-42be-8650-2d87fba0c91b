package com.jnby.module.order.service.box.builder;
import com.jnby.infrastructure.box.model.*;
import com.jnby.module.order.repository.IBoxRepository;
import com.jnby.base.repository.ICustomerAskBoxRepository;
import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.base.repository.ILogisticsRepository;
import com.jnby.module.order.service.box.DefaultBox;

import java.util.List;

/**
 * 搭配师builder
 * <AUTHOR>
 * @version 1.0
 * @date 3/9/21 4:02 PM
 */
public class FashionDesignerBuilder extends BoxBuilder {

    private DefaultBox defaultBox = new FashionDesignerBox();

    @Override
    public void init(IBoxRepository iBoxRepository, ICustomerAskBoxRepository iCustomerAskBoxRepository, ILogisticsRepository iLogisticsRepository, ICustomerDetailsRepository iCustomerDetailsRepository) {
        this.isInit = true;
        defaultBox.setiBoxRepository(iBoxRepository);
        defaultBox.setiCustomerAskBoxRepository(iCustomerAskBoxRepository);
        defaultBox.setiLogisticsRepository(iLogisticsRepository);
        defaultBox.setiCustomerDetailsRepository(iCustomerDetailsRepository);
    }

    @Override
    public void buildBox(BoxWithBLOBs box, String customerAskId, String userRightsId) {
        defaultBox.setBoxBody(box, customerAskId, userRightsId);
    }

    @Override
    public void buildBoxDetails(List<BoxDetailsWithBLOBs> boxDetails) {
        defaultBox.setBoxDetails(boxDetails);
    }

    @Override
    public void buildLogistics(Logistics logistics) {
        defaultBox.setLogistics(logistics);
    }

    @Override
    public DefaultBox build() {
        return defaultBox;
    }
}
