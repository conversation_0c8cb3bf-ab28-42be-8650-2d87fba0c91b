package com.jnby.module.order.statemachine.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName StateMachineConstant.java
 * @Description 状态机常量
 */
public class StateMachineConstant {

    /**
     * 区分状态的 machineId  服务单状态机
     */
    public final static String SERVICE_ORDER_STATE_MACHINE_ID = "serviceOrderMachine";

    public final static String SERVICE_ORDER_DETAIL_STATE_MACHINE_ID="serviceOrderDetailMachine";

    public final static String ORDER_STATE_MACHINE_ID = "orderMachine";

    /**
     * 服务单初始化 初始状态名字
     */
    public final static String SERVICE_ORDER_SOURCE_STATE_NAME="待提交";

    /**
     * 服务单初始化 初始状态值
     */
    public final static String SERVICE_ORDER_SOURCE_STATE_VALUE="-1";

    /**
     * 默认错误的action
     */
    public final static String SERVICE_ORDER_STATE_DEFAULT_ERROR_ACTION="error_action";

}
