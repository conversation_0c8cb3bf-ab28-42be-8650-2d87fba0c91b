package com.jnby.module.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.base.service.ICustomerAskBoxService;
import com.jnby.base.service.IWorkBenchService;
import com.jnby.base.service.enums.BoxSourceTypeEnum;
import com.jnby.common.ProducerUtil;
import com.jnby.common.enums.BoxReturnStatusEnum;
import com.jnby.common.listener.router.msg.entity.ReserveWeimobMsgEntity;
import com.jnby.common.listener.router.msg.util.MsgTagUtil;
import com.jnby.config.MsgTemplateConfigProperties;
import com.jnby.infrastructure.bojun.mapper.TOmsRefundOrderMapper;
import com.jnby.infrastructure.bojun.mapper.TOmsrefundorderproductMapper;
import com.jnby.infrastructure.bojun.model.TOmsRefundOrder;
import com.jnby.infrastructure.bojun.model.TOmsrefundorderproduct;
import com.jnby.infrastructure.box.mapper.*;
import com.jnby.infrastructure.box.model.*;
import com.jnby.module.credit.entity.OverCreditOrderEntity;
import com.jnby.module.credit.service.IBCustomerZhimaCreditOrderService;
import com.jnby.module.order.enums.BoxStatusEnum;
import com.jnby.module.order.enums.BoxTypeEnum;
import com.jnby.module.order.enums.ExpressStatusEnum;
import com.jnby.module.order.enums.ExpressTypeEnum;
import com.jnby.module.order.service.ISyncBoxReturnStatusJobService;
import com.jnby.module.order.service.box.IBoxActionService;
import com.jnby.module.order.service.box.IBoxService;
import com.jnby.module.order.service.box.IExpressService;
import com.jnby.module.workbench.enums.WorkBenchTaskTriggerEnum;
import com.jnby.module.workbench.enums.WorkBenchTaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Auther: lwz
 * @Date: 2022/2/24 13:23
 * @Description: SyncBoxReturnStatusJobServiceImpl
 * @Version 1.0.0
 */
@Service
@Slf4j
public class SyncBoxReturnStatusJobServiceImpl implements ISyncBoxReturnStatusJobService {
    @Resource
    private TOmsRefundOrderMapper tOmsRefundOrderMapper;

    @Resource
    private BoxReturnMapper boxReturnMapper;

    @Resource
    private TOmsrefundorderproductMapper tOmsrefundorderproductMapper;

    @Resource
    private IExpressService expressService;

    @Resource
    private BoxReturnDetailsMapper boxReturnDetailsMapper;

    @Resource
    private BoxDetailsMapper boxDetailsMapper;

    @Resource
    private ExpressMapper expressMapper;

    @Autowired
    private IBoxService boxService;

    @Resource
    private BoxMapper boxMapper;

    @Resource
    private IWorkBenchService workBenchService;

    @Resource
    private IBoxActionService iBoxActionService;

    @Resource
    private ICustomerDetailsRepository customerDetailsRepository;


    @Resource
    private OrderMapper orderMapper;


    @Autowired
    @Qualifier("boxTransactionTemplate")
    private TransactionTemplate template;


    private MsgTemplateConfigProperties msgTemplateConfigProperties;

    @Autowired
    private ProducerUtil producerUtil;

    @Resource
    private ICustomerAskBoxService customerAskBoxService;

    @Resource
    private IBCustomerZhimaCreditOrderService ibCustomerZhimaCreditOrderService;


    /**
     * 更新还货单状态
     */
    @Override
    public void syncBoxReturnStatus() {
        log.info("更新还货单状态 job 开始>>>>>>>>>>>>>>>>>>>");
        // 0申请中 1已提交 2退货中 3已完成 4已揽收 5已签收 (非等于3)
        List<BoxReturn> list = boxReturnMapper.getUnFinishedList();
        for (BoxReturn boxReturn : list) {
            try {
                TOmsRefundOrder tOmsRefundOrder = getFirstTOmsRefund(boxReturn.getOrderSn());
                if (tOmsRefundOrder == null) {
                    continue;
                }
                handBoxReturn(boxReturn, tOmsRefundOrder);
            } catch (Exception e) {
                log.error("更新还货单状态 异常 boxReturnId:{} e:{} message:{}", boxReturn.getId(), e, e.getMessage());
            }
        }
        log.info("更新还货单状态 job 结束>>>>>>>>>>>>>>>>>>>");
    }

    @Override
    public void syncBoxReturnStatusGtThirtyDaysAndLtHundredAndTwenty() {
        log.info("syncBoxReturnStatusGtThirtyDaysAndLtHundredAndTwenty定时任务开始处理 ----------------");
        // 0申请中 1已提交 2退货中 3已完成 4已揽收 5已签收 (非等于3)
        List<BoxReturn> list = boxReturnMapper.getUnFinishedListGtThirtyDaysAndLtHundredAndTwenty();
        for (BoxReturn boxReturn : list) {
            try {
                TOmsRefundOrder tOmsRefundOrder = getFirstTOmsRefund(boxReturn.getOrderSn());
                if (tOmsRefundOrder == null) {
                    continue;
                }
                handBoxReturn(boxReturn, tOmsRefundOrder);
            } catch (Exception e) {
                log.error("syncBoxReturnStatusGtThirtyDaysAndLtHundredAndTwenty  更新还货单状态 异常 boxReturnId:{} e:{} message:{}", boxReturn.getId(), e, e.getMessage());
            }
        }
        log.info("syncBoxReturnStatusGtThirtyDaysAndLtHundredAndTwenty定时任务结束");

    }

    @Override
    public void syncBoxReturnStatusByBoxReturnId(String boxReturnId) {
        log.info("syncBoxReturnStatusByBoxReturnId  同步开始    boxReturnId = {}",boxReturnId);
        BoxReturn boxReturn = boxReturnMapper.getUnFinishedListByBoxReturnId(boxReturnId);
        if(boxReturn == null){
            throw  new RuntimeException("box_return 未查询到正确可用的数据");
        }
        TOmsRefundOrder tOmsRefundOrder = getFirstTOmsRefund(boxReturn.getOrderSn());
        if (tOmsRefundOrder == null) {
            throw  new RuntimeException("根据 orderSn =  "+boxReturn.getOrderSn()+"在OMS未查询到数据");
        }
        handBoxReturn(boxReturn, tOmsRefundOrder);
        log.info("syncBoxReturnStatusByBoxReturnId  同步结束    boxReturnId = {}",boxReturnId);
    }

    /**
     * 处理还货单
     *
     * @param boxReturn
     * @param tOmsRefundOrder
     */
    public void handBoxReturn(BoxReturn boxReturn, TOmsRefundOrder tOmsRefundOrder) {
        log.info("处理还货单 boxReturn:{} tOmsRefundOrder:{}", boxReturn, tOmsRefundOrder);
        // 查询oms 详细单列表
        List<TOmsrefundorderproduct> tOmsRefundOrderProductList = tOmsrefundorderproductMapper.getTOmsrefundorderproductByOmsRefundorderId(tOmsRefundOrder.getId());


        BoxReturn updateBoxReturn = new BoxReturn();
        updateBoxReturn.setId(boxReturn.getId());

        Integer status = tOmsRefundOrder.getStatus().intValue();
        log.info("oms 状态 status: {}", status);
        template.execute(action -> {
            if (status.equals(Integer.valueOf(10))) {
                //已创建
                updateBoxReturn.setStatus(Long.valueOf(BoxReturnStatusEnum.COMMITTED.getCode()));
                updateBoxReturnMethod(boxReturn,updateBoxReturn);
            } else if (status.equals(Integer.valueOf(20))) {
                //退货中
                updateBoxReturn.setStatus(Long.valueOf(BoxReturnStatusEnum.INRETURN.getCode()));
                updateBoxReturnMethod(boxReturn,updateBoxReturn);
                // 更新物流
                updateExpress(expressService.getExpressIdByBoxIdAndOrgIdAndType(boxReturn.getBoxId(), boxReturn.getId(), ExpressTypeEnum.boxReturn), Long.valueOf(ExpressStatusEnum.shiped.getCode()));
                updateExpress(expressService.getExpressIdByBoxIdAndOrgIdAndType(boxReturn.getBoxId(), boxReturn.getId(), ExpressTypeEnum.supplyGuideEb), Long.valueOf(ExpressStatusEnum.shiped.getCode()));

            } else if (status.equals(Integer.valueOf(50))) {
                //退货完成
                updateBoxReturn.setStatus(Long.valueOf(BoxReturnStatusEnum.OVER.getCode()));
                updateBoxReturnMethod(boxReturn,updateBoxReturn);
                // 更新物流
                updateExpress(expressService.getExpressIdByBoxIdAndOrgIdAndType(boxReturn.getBoxId(), boxReturn.getId(), ExpressTypeEnum.boxReturn), Long.valueOf(ExpressStatusEnum.received.getCode()));
                updateExpress(expressService.getExpressIdByBoxIdAndOrgIdAndType(boxReturn.getBoxId(), boxReturn.getId(), ExpressTypeEnum.supplyGuideEb), Long.valueOf(ExpressStatusEnum.received.getCode()));
                checkReturnGoodsTrue(boxReturn, tOmsRefundOrderProductList);
            }
            return true;
        });
    }

    private void updateBoxReturnMethod(BoxReturn boxReturn, BoxReturn updateBoxReturn) {

        int i = boxReturnMapper.updateStatus(boxReturn.getId(), updateBoxReturn.getStatus(), boxReturn.getStatus());
        if( i == 0){
            log.error("updateBoxReturnMethod    boxReturnId = {},  原状态  = {} , 应更新状态 = {}",boxReturn.getId(),boxReturn.getStatus(),updateBoxReturn.getStatus());
            throw  new RuntimeException("更新状态失败 !");
        }
    }


    /**
     * 验证还货商品
     *
     * @param boxReturn
     * @param returnOrderProducts
     */
    private void checkReturnGoods(BoxReturn boxReturn, List<TOmsrefundorderproduct> returnOrderProducts) {
        // 还货单明细
        List<BoxReturnDetails> returnDetails = boxReturnDetailsMapper.selectByReturnId(boxReturn.getId());
        // 服务单明细
        List<BoxDetails> boxDetails = boxDetailsMapper.getProductsByBoxId(boxReturn.getBoxId());
        // 已还货数量
        Integer realNum = 0;

        // 已还货商品
        List<String> hasReturn = new ArrayList<>();
        Set<String> hasReturnSet = new HashSet<>();
        // 应还商品
        List<String> applyReturn = new ArrayList<>();
        // 应还商品
        for (BoxReturnDetails returnDetail : returnDetails) {
            applyReturn.add(returnDetail.getSku());
        }

        // 处理已还货商品集合 和 已还货数量
        for (TOmsrefundorderproduct retProduct : returnOrderProducts) {
            if (hasReturnSet.add(StringUtils.removeEnd(retProduct.getSku(), "\n"))) {
                hasReturn.add(retProduct.getSku());
                realNum++;
            }
        }


        // noReturnList为非本次退货单中的商品
        List<String> noReturnList = new ArrayList(Arrays.asList(new Object[hasReturn.size()]));
        Collections.copy(noReturnList, hasReturn);
        noReturnList.removeAll(applyReturn);

        // returnList为本次退货单中的商品
        List<String> returnList = new ArrayList(Arrays.asList(new Object[hasReturn.size()]));
        Collections.copy(returnList, hasReturn);
        returnList.retainAll(applyReturn);

        // 更新box商品退货状态
        for (BoxDetails boxDetail : boxDetails) {
            BoxDetailsWithBLOBs updateBoxDetails = new BoxDetailsWithBLOBs();
            updateBoxDetails.setId(boxDetail.getId());

            // 更新已退货的商品
            for (String s : returnList) {
                if (boxDetail.getSku().equals(s)) {
                    updateBoxDetails.setStatus(Long.valueOf(2));
                    boxDetailsMapper.updateByPrimaryKeySelective(updateBoxDetails);
                }
            }
            // 更新非本次退货单中的商品
            for (String s : noReturnList) {
                if (boxDetail.getSku().equals(s)) {
                    if (boxDetail.getStatus() == 2) {
                        continue;
                    }
                    // 异常状态 0-正常 1-异常退货
                    updateBoxDetails.setExStatus(Long.valueOf(1));
                    boxDetailsMapper.updateByPrimaryKeySelective(updateBoxDetails);
                }
            }
        }
        // 更新还货单商品状态
        for (BoxReturnDetails returnDetail : returnDetails) {
            for (String s : returnList) {
                if (returnDetail.getSku().equals(s)) {
                    BoxReturnDetails updateBoxReturnDetails = new BoxReturnDetails();
                    updateBoxReturnDetails.setId(returnDetail.getId());
                    // 商品状态(1:申请退货;2:已退货;)
                    updateBoxReturnDetails.setRetStatus(Long.valueOf(2));
                    boxReturnDetailsMapper.updateByPrimaryKeySelective(updateBoxReturnDetails);
                }
            }
        }


        BoxReturn updateBoxReturn = new BoxReturn();
        updateBoxReturn.setId(boxReturn.getId());
        // 更新还货数量
        updateBoxReturn.setRealReturn(Long.valueOf(realNum));
        boxReturnMapper.updateByPrimaryKeySelective(updateBoxReturn);

        boolean isWarn = false;
        //退货商品中有非本次退货单中的商品则发出预警
        if (noReturnList.size() > 0) {
            isWarn = true;
        }
        //已还货商品数量不等于本次退货单中的商品数量，则发出预警
        if (hasReturn.size() != applyReturn.size()) {
            isWarn = true;
        }
        Box box = boxMapper.selectByPrimaryKey(boxReturn.getBoxId());
        if (isWarn) {
            updateBoxReturn.setIsWarn(Long.valueOf(1));
            boxReturnMapper.updateByPrimaryKeySelective(updateBoxReturn);
            //还货预警
            if (box != null) {
                BoxWithBLOBs updateBox = new BoxWithBLOBs();
                updateBox.setId(box.getId());
                updateBox.setIsWarn(Long.valueOf(1));
                updateBox.setWarnTime(new Date());
                boxMapper.updateByPrimaryKeySelective(updateBox);
            }
        } else {
            //如果退货正常则更新box为完成状态，并取消box预警状态
            updateBoxReturn.setIsWarn(Long.valueOf(0));
            boxReturnMapper.updateByPrimaryKeySelective(updateBoxReturn);
            if (box != null) {
                boolean boxWarn = false;
                //box详情中仍有应退货商品，则发出预警
                for (BoxDetails boxDetail : boxDetails) {
                    if (boxDetail.getStatus() == 1) {
                        boxWarn = true;
                        break;
                    }
                }
                if (!boxWarn) {
                    dealAfterFinish(box,"OMS同步未购买商品已还，box完成");
                }
            }
        }
    }


    /**
     * 1. 根据还货详情单子 查询本次还货
     * 2. 查询服务单明细
     * 3. 集合归纳
     */
    private void checkReturnGoodsTrue(BoxReturn boxReturn, List<TOmsrefundorderproduct> returnOrderProducts) {
        if (CollectionUtils.isEmpty(returnOrderProducts)) {
            log.info("还货单boxReturn ：{}  没有归还商品", boxReturn);
            return;
        }
        // oms 还货单map
        Map<String, Collection<TOmsrefundorderproduct>> omsReturnOrderMap = getTomsReturnOrderMap(returnOrderProducts);
        // oms 还货单sku 频次匹配
        Map<String,Integer> omsReturnOrderCountMap =  getTomsReturnOrderCountMap(omsReturnOrderMap);


        // 还货单明细
        List<BoxReturnDetails> shouldReturnDetails = boxReturnDetailsMapper.selectByReturnId(boxReturn.getId());

        // 服务单明细
        List<BoxDetailsWithBLOBs> shouldBoxDetails = boxDetailsMapper.selectListByBoxId(boxReturn.getBoxId());

        // 服务单详细 应该还货数量
        int totalShouldCount = 0;

        for (BoxDetailsWithBLOBs temp : shouldBoxDetails) {
            if (Long.valueOf(30).equals(temp.getType())) {
                continue;
            }
            // 在途
            if (
                    temp.getType().longValue() < Long.valueOf(30).longValue() && (
                            Long.valueOf(0).equals(temp.getStatus())
                                    || Long.valueOf(8).equals(temp.getStatus())
                                    || Long.valueOf(9).equals(temp.getStatus())
                                    || Long.valueOf(10).equals(temp.getStatus())
                                    || Long.valueOf(1).equals(temp.getStatus())
                                    || Long.valueOf(11).equals(temp.getStatus())
                    )
            ) {
                totalShouldCount += 1;
            }
        }


        // 退货商品中有非本次退货单中的商品则发出预警 和 已还货商品数量不等于本次退货单中的商品数量，则发出预警
        boolean isWarn = false;

        //已还货的商品
        Map<String, BoxReturnDetails> alreadyBoxReturnMap = new HashMap<>();


        for (BoxReturnDetails boxReturnDetails : shouldReturnDetails) {
            if (!omsReturnOrderCountMap.containsKey(boxReturnDetails.getSku())) {
                //退货商品中有非本次退货单中的商品则发出预警
                isWarn = true;
                continue;
            }
            // 获取同sku的
            Integer count = omsReturnOrderCountMap.get(boxReturnDetails.getSku());

            if (count == 0) {
                omsReturnOrderCountMap.remove(boxReturnDetails.getSku());
                continue;
            }
            if (count == 1) {
                omsReturnOrderCountMap.remove(boxReturnDetails.getSku());
                alreadyBoxReturnMap.put(boxReturnDetails.getId(), boxReturnDetails);
            }
            if (count > 1) {
                omsReturnOrderCountMap.put(boxReturnDetails.getSku(), count - 1);
                alreadyBoxReturnMap.put(boxReturnDetails.getId(), boxReturnDetails);
            }
        }
        // 真实还货数量
        int realReturnNum = 0;
        for (BoxReturnDetails boxReturnDetails : shouldReturnDetails) {
            BoxDetailsWithBLOBs updateBoxDetails = new BoxDetailsWithBLOBs();
            updateBoxDetails.setId(boxReturnDetails.getBoxDetailId());
            if (alreadyBoxReturnMap.containsKey(boxReturnDetails.getId())) {
                // 服务子单
                updateBoxDetails.setStatus(Long.valueOf(2));
                boxDetailsMapper.updateByPrimaryKeySelective(updateBoxDetails);

                // 还货子单
                BoxReturnDetails updateBoxReturnDetails = new BoxReturnDetails();
                updateBoxReturnDetails.setId(boxReturnDetails.getId());
                // 商品状态(1:申请退货;2:已退货;)
                updateBoxReturnDetails.setRetStatus(Long.valueOf(2));
                boxReturnDetailsMapper.updateByPrimaryKeySelective(updateBoxReturnDetails);
                realReturnNum += 1;
            } else {
                // 异常状态 0-正常 1-异常退货
                updateBoxDetails.setExStatus(Long.valueOf(1));
                boxDetailsMapper.updateByPrimaryKeySelective(updateBoxDetails);
            }
        }


        // 还货单主单
        BoxReturn updateBoxReturn = new BoxReturn();
        updateBoxReturn.setId(boxReturn.getId());
        // 更新还货数量
        updateBoxReturn.setRealReturn(Long.valueOf(realReturnNum));
        // 还货数量和应该还货数量不一致 发出预警
        if (realReturnNum != shouldReturnDetails.size()) {
            isWarn = true;
        }

        Box box = boxMapper.selectByPrimaryKey(boxReturn.getBoxId());
        if (isWarn) {
            updateBoxReturn.setIsWarn(Long.valueOf(1));
            boxReturnMapper.updateByPrimaryKeySelective(updateBoxReturn);
            //还货预警
            if (box != null) {
                BoxWithBLOBs updateBox = new BoxWithBLOBs();
                updateBox.setId(box.getId());
                updateBox.setIsWarn(Long.valueOf(1));
                updateBox.setWarnTime(new Date());
                boxMapper.updateByPrimaryKeySelective(updateBox);
                iBoxActionService.createBoxAction(box.getId(), "OMS还货预警，没有还货完成", box.getStatus(), "-1");
            }
        }else{
            //如果退货正常则更新box为完成状态，并取消box预警状态
            updateBoxReturn.setIsWarn(Long.valueOf(0));
            boxReturnMapper.updateByPrimaryKeySelective(updateBoxReturn);
            // 还货数量和还货单还货数量一致 修改主单
            if (box != null && totalShouldCount == realReturnNum) {
                dealAfterFinish(box,"OMS同步未购买商品已还，box完成");
            }
        }
    }


    /**
     * 去除同个sku的
     *
     * @param returnOrderProducts
     * @return
     */
    public Map<String, Integer> getTomsReturnOrderCountMap(List<TOmsrefundorderproduct> returnOrderProducts) {
        Map<String, Integer> resultMap = new HashMap<>();
        for (TOmsrefundorderproduct tOmsrefundorderproduct : returnOrderProducts) {
            resultMap.put(tOmsrefundorderproduct.getSku(), 1);
        }
        return resultMap;
    }

    /**
     * 匹配sku 频次
     *
     * @param originalMap
     * @return
     */
    public Map<String, Integer> getTomsReturnOrderCountMap(Map<String, Collection<TOmsrefundorderproduct>> originalMap) {
        Map<String, Integer> resultMap = new HashMap<>();
        for (String id : originalMap.keySet()) {
            resultMap.put(id, originalMap.get(id).size());
        }
        return resultMap;
    }


    /**
     * 获取oms 还货单
     *
     * @param returnOrderProducts
     * @return
     */
    public Map<String, Collection<TOmsrefundorderproduct>> getTomsReturnOrderMap(List<TOmsrefundorderproduct> returnOrderProducts) {
        Multimap<String, TOmsrefundorderproduct> omsReturnOrderMultiMap = ArrayListMultimap.create();
        for (TOmsrefundorderproduct tOmsrefundorderproduct : returnOrderProducts) {
            omsReturnOrderMultiMap.put(StringUtils.removeEnd(tOmsrefundorderproduct.getSku(), "\n"), tOmsrefundorderproduct);
        }

        Map<String, Collection<TOmsrefundorderproduct>> omsReturnOrderMap = omsReturnOrderMultiMap.asMap();
        return omsReturnOrderMap;
    }

    /**
     * 完成后处理
     *
     * @param box
     */
    @Override
    public void dealAfterFinish(Box box,String remark) {
        log.info("完成后处理 box: {}",box);
        CustomerDetails customerDetails = customerDetailsRepository.findByUnionId(box.getUnionid());

        BoxWithBLOBs updateBox = new BoxWithBLOBs();
        updateBox.setId(box.getId());
        Date now = new Date();
        updateBox.setFinishTime(now);
        updateBox.setIsWarn(Long.valueOf(0));
        updateBox.setStatus(BoxStatusEnum.finishall.getCode().longValue());


        handWorkBench(box, customerDetails);
        iBoxActionService.createBoxAction(box.getId(), remark, box.getStatus(), "-1");
        boxMapper.updateByPrimaryKeySelective(updateBox);

        BoxWithBLOBs box1 = boxMapper.selectByBoxSn(box.getBoxSn());
        // 完成后处理 先试后买单子
        boxService.dealTryAfterBuyIsHaveUnFinshBox(box1);

        if(box.getType().equals(BoxTypeEnum.TRY_AFTER_BUY.getCode())){
            ReserveWeimobMsgEntity reserveWeimobMsg = new ReserveWeimobMsgEntity();
            reserveWeimobMsg.setBoxId(box.getId());
            producerUtil.send(JSON.toJSONBytes(reserveWeimobMsg), MsgTagUtil.RECOVER_RIGHTS_MSG_TAG);
        }

        // 修改订阅计划
        if(!BoxSourceTypeEnum.FREE.getCode().equals(box.getSourceType())){
            CustomerAskBox customerAskBox = customerAskBoxService.findById(box.getSourceId());
            boxService.changeSubPlan(box1,customerAskBox);
        }
        // 结束信用订单
        if(StringUtils.isNotBlank(box.getCreditId())) {
            OverCreditOrderEntity overCreditOrderEntity = new OverCreditOrderEntity();
            overCreditOrderEntity.setId(box.getCreditId());
            ibCustomerZhimaCreditOrderService.createOverOrderEvent(overCreditOrderEntity);
        }
    }

    @Override
    public void boxReturnError(Box box, String remark) {
        //还货预警
        BoxWithBLOBs updateBox = new BoxWithBLOBs();
        updateBox.setId(box.getId());
        updateBox.setIsWarn(Long.valueOf(1));
        updateBox.setWarnTime(new Date());
        boxMapper.updateByPrimaryKeySelective(updateBox);
        iBoxActionService.createBoxAction(box.getId(), remark, box.getStatus(), "-1");

    }


    /**
     * 任务制处理
     *
     * @param box
     * @param customerDetails
     */
    public void handWorkBench(Box box, CustomerDetails customerDetails) {
        if(box.getType().equals(BoxTypeEnum.TRY_AFTER_BUY.getCode())){
            return;
        }
        // work-引导会员购买任务制-完成
        workBenchService.executorWorkBench(null, box.getCreateFasId(), box.getId(),
                box.getUnionid(), customerDetails.getNickName(), customerDetails.getPhone(),
                WorkBenchTaskTriggerEnum.TASK_TRIGGER_ENUM_PRODUCT_PUT_STORAGE.getValue(),
                WorkBenchTaskTypeEnum.TASK_TYPE_ENUM_GUIDE_MEMBER_BUY.getValue());
        // work-引导会员寄回任务制-完成
        workBenchService.executorWorkBench(null, box.getCreateFasId(), box.getId(),
                box.getUnionid(), customerDetails.getNickName(), customerDetails.getPhone(),
                WorkBenchTaskTriggerEnum.TASK_TRIGGER_ENUM_PRODUCT_PUT_STORAGE.getValue(),
                WorkBenchTaskTypeEnum.TASK_TYPE_ENUM_GUIDE_MEMBER_SEND.getValue());
        // work-引导会员入库任务制-完成
        workBenchService.executorWorkBench(null, box.getCreateFasId(), box.getId(),
                box.getUnionid(), customerDetails.getNickName(), customerDetails.getPhone(),
                WorkBenchTaskTriggerEnum.TASK_TRIGGER_ENUM_PRODUCT_PUT_STORAGE.getValue(),
                WorkBenchTaskTypeEnum.TASK_TYPE_ENUM_OPERATION_PRODUCT_PUT_IN_STORAGE.getValue());
        if (box.getIfFeedback() == 0) {
            // work-引导会员评价任务制-待处理
            workBenchService.executorWorkBench(null, box.getCreateFasId(), box.getId(),
                    box.getUnionid(), customerDetails.getNickName(), customerDetails.getPhone(),
                    WorkBenchTaskTriggerEnum.TASK_TRIGGER_ENUM_PRODUCT_PUT_STORAGE.getValue(),
                    WorkBenchTaskTypeEnum.TASK_TYPE_ENUM_MEMBER_COMMENTS.getValue());
        }


    }


    public void updateExpress(String expressId, Long targetStatus) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(expressId)) {
            Express updateExpress = new Express();
            updateExpress.setId(expressId);
            updateExpress.setStatus(targetStatus);
            expressMapper.updateByPrimaryKeySelective(updateExpress);
        }
    }


    public TOmsRefundOrder getFirstTOmsRefund(String refundId) {
        List<TOmsRefundOrder> tOmsRefundOrderList = tOmsRefundOrderMapper.selectByRefundId(refundId);
        return CollectionUtils.isEmpty(tOmsRefundOrderList) ? null : tOmsRefundOrderList.get(0);
    }


    /**
     * 走内淘更换尺码完成
     */
    public void finishOrderIsEb() {
        try {
            List<String> boxSnList = orderMapper.getChangeCodeBoxSnList();
            for (String boxSn : boxSnList) {
                Box box = boxMapper.selectByBoxSn(boxSn);
                BoxWithBLOBs updateBox = new BoxWithBLOBs();
                updateBox.setId(box.getId());
                updateBox.setStatus(BoxStatusEnum.finishall.getCode().longValue());
                iBoxActionService.createBoxAction(box.getId(), "更换尺码完成，box完成", box.getStatus(), "-1");
            }
        } catch (Exception e) {
            log.error("走内淘更换尺码完成 e:{}  message:{}", e, e.getMessage());
        }

    }
}
