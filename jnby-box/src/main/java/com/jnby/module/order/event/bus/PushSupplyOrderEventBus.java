package com.jnby.module.order.event.bus;


import com.jnby.base.service.IBusEventsService;
import com.jnby.module.order.event.BaseEventBus;
import com.jnby.module.order.event.PushSupplyOrderEvent;
import com.jnby.module.order.event.handler.PushSupplyOrderEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * @Auther: lwz
 * @Date: 2021/11/30 17:27
 * @Description: PushSupplyOrderEventBus  补货异步
 * @Version 1.0.0
 */
@Service
@Slf4j
public class PushSupplyOrderEventBus implements InitializingBean, BaseEventBus<PushSupplyOrderEvent> {

    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private IBusEventsService iBusEventsService;

    @Autowired
    private PushSupplyOrderEventHandler pushSupplyOrderEventHandler;

    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(pushSupplyOrderEventHandler);
    }

    @Override
    public void post(PushSupplyOrderEvent pushSupplyOrderEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(pushSupplyOrderEvent);
    }
}
