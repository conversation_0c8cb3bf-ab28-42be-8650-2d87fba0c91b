package com.jnby.module.order.event.bus;


import com.jnby.base.service.IBusEventsService;
import com.jnby.module.order.event.BaseEventBus;
import com.jnby.module.order.event.SendRefundOrderEvent;
import com.jnby.module.order.event.handler.SendRefundOrderEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * @auther: lwz
 * @Date: 2021/11/19 17:34
 * @Description: SendRefundOrderEventBus
 * @Version 1.0.0
 */
@Slf4j
@Service
public class SendRefundOrderEventBus implements InitializingBean, BaseEventBus<SendRefundOrderEvent> {

    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private IBusEventsService iBusEventsService;

    @Resource
    private SendRefundOrderEventHandler sendRefundOrderEventHandler;


    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(sendRefundOrderEventHandler);
    }

    @Override
    public void post(SendRefundOrderEvent sendRefundOrderEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(sendRefundOrderEvent);
    }

}
