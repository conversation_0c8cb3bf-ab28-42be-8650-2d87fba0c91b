package com.jnby.module.order.repository.impl;
import com.jnby.infrastructure.box.mapper.ExpressMapper;
import com.jnby.infrastructure.box.model.Express;
import com.jnby.module.order.repository.IExpressRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/22/21 11:31 AM
 */
@Repository
public class ExpressRepositoryImpl implements IExpressRepository {

    @Autowired
    private ExpressMapper expressMapper;

    @Override
    public void batchDelete(List<String> ids) {
        if (ids != null && ids.size() > 0){
            expressMapper.batchDeleteByPrimayKeys(ids);
        }
    }

    @Override
    public void insertSelective(Express express) {
        expressMapper.insertSelective(express);
    }

    @Override
    public List<Express> selectListBySelective(Express express) {
        return expressMapper.selectListBySelective(express);
    }

    @Override
    public void updateSelective(Express express) {
        expressMapper.updateByPrimaryKeySelective(express);
    }
}
