package com.jnby.module.order.context;

import lombok.Data;

@Data
public class SubscribeOrderWithCustomerContext {

    /**
     * 搜索条件
     */
    private String search;

    /**
     * 支付方式(0:微信支付;1:积分支付;2:权益兑换;3:邀请码;4:心意盒子  7满送续订)
     */
    private String way;


    /**
     * 支付类型(0:付款;1:退款)
     */
    private String type;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;


}
