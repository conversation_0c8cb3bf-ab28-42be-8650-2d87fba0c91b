package com.jnby.module.order.event.bus;

import com.jnby.base.service.IBusEventsService;
import com.jnby.module.order.event.AliCreditOrderEvent;
import com.jnby.module.order.event.BaseEventBus;
import com.jnby.module.order.event.handler.AliCreditOrderEventHander;
import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AliCreditOrderEventBus implements InitializingBean, BaseEventBus<AliCreditOrderEvent> {

    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private AliCreditOrderEventHander aliCreditOrderEventHander;

    @Autowired
    private IBusEventsService iBusEventsService;


    @Override
    public void post(AliCreditOrderEvent aliCreditOrderEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(aliCreditOrderEvent);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(aliCreditOrderEventHander);
    }
}
