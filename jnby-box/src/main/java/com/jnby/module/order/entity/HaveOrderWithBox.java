package com.jnby.module.order.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class HaveOrderWithBox {
    @ApiModelProperty("是否有单据")
    private Boolean flag;


    private String boxSn;


    private String orderId;


    private String orderSn;


    private String unionId;


    private String othersPayShareId;


    private String boxId;

    /**
     * 0正常单子  1代付单
     */
    @ApiModelProperty("0正常单子  1代付单")
    private Long type = 0L;
}
