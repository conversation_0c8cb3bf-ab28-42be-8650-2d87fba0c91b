package com.jnby.module.order.event.bus;

import com.jnby.base.service.IBusEventsService;
import com.jnby.module.order.event.BaseEventBus;
import com.jnby.module.order.event.PushCreateCardEvent;
import com.jnby.module.order.event.PushOrderEvent;
import com.jnby.module.order.event.handler.PushCreateCardEventHandler;
import com.jnby.module.order.event.handler.PushOrderEventHander;
import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 搭盒进行开卡 异步事件
 * <AUTHOR>
 * @Version 1.0
 */
@Service
@Slf4j
public class PushCreateCardEventBus implements InitializingBean, BaseEventBus<PushCreateCardEvent> {

    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private IBusEventsService iBusEventsService;

    @Autowired
    private PushCreateCardEventHandler pushCreateCardEventHandler;

    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(pushCreateCardEventHandler);
    }

    @Override
    public void post(PushCreateCardEvent pushCreateCardEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(pushCreateCardEvent);
    }

}
