package com.jnby.module.order.service.impl;

import com.jnby.infrastructure.box.model.BoxReturnTag;
import com.jnby.infrastructure.box.mapper.BoxReturnTagMapper;
import com.jnby.module.order.service.IBoxReturnTagService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @auther yuanxiaozhong
 * @create 2022-11-07 18:06:25
 * @describe 服务实现类
 */
@Service
public class BoxReturnTagServiceImpl extends ServiceImpl<BoxReturnTagMapper, BoxReturnTag> implements IBoxReturnTagService {

    @Override
    public List<BoxReturnTag> getGroupByCate(Integer groupId, String big_class, String small_class) {
        return baseMapper.getGroupByCate(groupId, big_class, small_class);
    }
}
