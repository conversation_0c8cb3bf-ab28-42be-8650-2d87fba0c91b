package com.jnby.module.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.infrastructure.bojun.model.BStoreStorageOccupy;
import com.jnby.infrastructure.bojun.model.BStoreStorageOccupyError;
import com.jnby.infrastructure.bojun.mapper.BStoreStorageOccupyErrorMapper;
import com.jnby.module.order.service.IBStoreStorageOccupyErrorService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jnby.module.order.service.ISalesCoreService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @auther wangchun
 * @create 2023-03-15 15:24:09
 * @describe 库存占用异常日志服务实现类
 */
@Service
public class BStoreStorageOccupyErrorServiceImpl extends ServiceImpl<BStoreStorageOccupyErrorMapper, BStoreStorageOccupyError> implements IBStoreStorageOccupyErrorService {

     @Autowired
     private IdLeafService idLeafService;

     @Autowired
     private ISalesCoreService salesCoreService;

     @Resource(name = "bojunTransactionTemplate")
     private TransactionTemplate bojunTemplate;

     @Override
     public void insertError(List<BStoreStorageOccupy> releaseList,String errMsg) {
          if(CollectionUtils.isEmpty(releaseList)){
               return;
          }
          BStoreStorageOccupyError bStoreStorageOccupyError = new BStoreStorageOccupyError();
          bStoreStorageOccupyError.setId(idLeafService.getId());
          bStoreStorageOccupyError.setStatus(0);
          bStoreStorageOccupyError.setIsDel(0);
          bStoreStorageOccupyError.setOccupyType(releaseList.get(0).getOccupyType());
          bStoreStorageOccupyError.setSourceNo(releaseList.get(0).getSourceNo());
          bStoreStorageOccupyError.setDescription(releaseList.get(0).getDescription());
          bStoreStorageOccupyError.setParamJson(JSON.toJSONString(releaseList));
          bStoreStorageOccupyError.setCreateTime(new Date());
          bStoreStorageOccupyError.setUpdateTime(new Date());
          bStoreStorageOccupyError.setErrMsg(errMsg);
          this.save(bStoreStorageOccupyError);
     }

     @Override
     public void handleOccupyStorageError(String id) {
          BStoreStorageOccupyError bStoreStorageOccupyError = this.getById(id);
          if(ObjectUtils.isEmpty(bStoreStorageOccupyError)){
               throw new RuntimeException("未查询到异常日志，请核实");
          }
          if(StringUtils.isBlank(bStoreStorageOccupyError.getParamJson())){
               throw new RuntimeException("未查询到异常日志，请核实");
          }
          List<BStoreStorageOccupy> list = JSONObject.parseArray(bStoreStorageOccupyError.getParamJson(), BStoreStorageOccupy.class);
          if(CollectionUtils.isEmpty(list)){
               throw new RuntimeException("异常日志为空，请核实");
          }
          // 判断当前商品是否已经释放过库存
          if(salesCoreService.verificationRelease(list)){
               return;
          }
          bojunTemplate.execute(boJunAction -> {
               salesCoreService.parseBoJunWork(list);
               // 修改状态
               BStoreStorageOccupyError updateEntity = new BStoreStorageOccupyError();
               updateEntity.setId(id);
               updateEntity.setStatus(1);
               updateEntity.setUpdateTime(new Date());
               this.updateById(updateEntity);
               return boJunAction;
          });
     }
}
