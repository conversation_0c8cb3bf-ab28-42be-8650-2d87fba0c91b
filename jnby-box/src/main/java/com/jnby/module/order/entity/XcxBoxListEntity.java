package com.jnby.module.order.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/11/1 10:22
 */
@Data
public class XcxBoxListEntity implements Serializable {

    private String id;

    @ApiModelProperty("服务单号")
    private String boxSn;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("类型：40为预售")
    private String type;

    @ApiModelProperty("搭配师名称")
    private String fashionerName;

    @ApiModelProperty("搭配师头像")
    private String fashionerPhoto;

    @ApiModelProperty("异常提醒")
    private Integer errorDays;

    @ApiModelProperty("签收时间")
    private Date signInTime;

    @ApiModelProperty("退回时间")
    private Date tryOutTime;

    @ApiModelProperty(value = "outNo")
    private String outNo;

    private String extend;

    @ApiModelProperty(" 0免费盒子  1订阅 2单次")
    private Long sourceType;

    @ApiModelProperty("信用单id")
    private String creditId;

}
