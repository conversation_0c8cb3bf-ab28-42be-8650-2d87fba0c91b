package com.jnby.module.order.repository;
import com.jnby.infrastructure.box.model.Express;

import java.util.List;

/**
 * 物流资源层
 * <AUTHOR>
 * @version 1.0
 * @date 3/22/21 11:27 AM
 */
public interface IExpressRepository {

    void batchDelete(List<String> ids);

    void insertSelective(Express express);

    List<Express> selectListBySelective(Express express);

    void updateSelective(Express express);
}
