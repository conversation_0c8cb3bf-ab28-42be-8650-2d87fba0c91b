package com.jnby.module.order.service;

import com.jnby.infrastructure.box.model.*;
import com.jnby.module.order.entity.SevenYearActivityEntity;

import java.util.List;
import java.util.Set;

public interface ISevenYearService {

    Boolean isActivity();


    /**
     * 指定活动是否有效
     *
     * @param activityId
     * @return
     */
    Boolean isActivity(String activityId);

    /**
     * 获取排除商品列表
     *
     * @return
     */
    Set<String> getBlackSkuList();


    /**
     * 处理 七周年活动
     *
     * @param recordSendBox
     * @param box
     */
    void handle(RecordSendBox recordSendBox, Box box);



    /**
     * 获取过滤之后详情 排除自提和已经取消的
     *
     * @param boxId
     * @return
     */
    List<Express> filterExpress(String boxId);


    /**
     * 获取符合条件的详情数量
     *
     * @param recordSendBox
     * @param boxDetailsWithBLOBsList
     * @param expressList
     * @return
     */
    int getBoxDetailsCount(RecordSendBox recordSendBox, List<BoxDetailsWithBLOBs> boxDetailsWithBLOBsList, List<Express> expressList);


    /**
     * 过滤黑名单sku和未发货的和已删款和已取消
     *
     * @param boxId
     * @return
     */
    List<BoxDetailsWithBLOBs> filterBlackSkuAndNoSend(String boxId);


    /**
     * 适配符合条件的活动
     *
     * @param recordSendBox
     * @param activity
     * @param expressList
     * @param boxDetailsWithBLOBsLis
     * @return
     */
    SevenYearActivityEntity adaptSearch(RecordSendBox recordSendBox, Activity activity, List<Express> expressList, List<BoxDetailsWithBLOBs> boxDetailsWithBLOBsLis);


    List<Integer> targetStoreIds(String storePackageId, List<Integer> storeIds);
}