package com.jnby.module.order.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/3/14
 */
public enum ExpressTypeEnum {
    //
    box(10, "服务单","发货物流"),
    boxReturn(20, "还货单","退回物流"),
    order(30, "订单","发货物流"),
    refund(40, "售后单","退货物流"),
    supply(50, "补货单","发货物流"),
    change(60, "换码订单","发货物流"),
    guideEb(70, "导购内淘单","发货物流"),
    marketing(80, "营销活动实物订单","营销活动实物订单"),
    supplyGuideEb(90, "导购内淘补货单","发货物流"),
    ;


    private static final Map<Integer, ExpressTypeEnum> LOOKUP = new HashMap<>();

    static {
        for (ExpressTypeEnum s : EnumSet.allOf(ExpressTypeEnum.class)) {
            LOOKUP.put(s.getCode(), s);
        }
    }

    private Integer code;
    private String name;

    private String minAppMemo;

    ExpressTypeEnum(Integer code, String name,String minAppMemo) {
        this.name = name;
        this.code = code;
        this.minAppMemo = minAppMemo;
    }

    public String getName() {
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMinAppMemo() {
        return this.minAppMemo;
    }


    public static String get(Integer code) {
        ExpressTypeEnum expressTypeEnum = LOOKUP.get(code);
        return expressTypeEnum.getMinAppMemo();
    }

    public static boolean isEb(Integer code) {
        if (code == null) {
            return false;
        }
        return Objects.equals(code, ExpressTypeEnum.guideEb.getCode()) || Objects.equals(code, ExpressTypeEnum.supplyGuideEb.getCode());
    }

    public static boolean isSupply(Integer code) {
        if (code == null) {
            return false;
        }
        return Objects.equals(code, ExpressTypeEnum.supply.getCode()) || Objects.equals(code, ExpressTypeEnum.supplyGuideEb.getCode());
    }
}
