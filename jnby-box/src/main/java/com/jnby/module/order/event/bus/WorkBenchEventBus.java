package com.jnby.module.order.event.bus;

import com.jnby.base.service.IBusEventsService;
import com.jnby.module.order.event.BaseEventBus;
import com.jnby.module.order.event.PushOrderEvent;
import com.jnby.module.order.event.WorkBenchEvent;
import com.jnby.module.order.event.handler.SubmitConsignEventHandler;
import com.jnby.module.order.event.handler.WorkBenchEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * @<PERSON> <PERSON><PERSON><PERSON>
 * @Date 2022/1/21 3:05 下午
 * @Version 1.0
 */
@Service
@Slf4j
public class WorkBenchEventBus  implements InitializingBean, BaseEventBus<WorkBenchEvent> {
    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private IBusEventsService iBusEventsService;

    @Autowired
    private WorkBenchEventHandler workBenchEventHandler;

    @Override
    public void post(WorkBenchEvent workBenchEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(workBenchEvent);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(workBenchEventHandler);
    }
}
