package com.jnby.module.order.service.box.impl;

import com.alibaba.fastjson.JSONObject;
import com.jnby.base.context.UseRightsContext;
import com.jnby.base.repository.ISubscribeAskRepository;
import com.jnby.base.service.ICustomerDetailsService;
import com.jnby.base.service.IFashionerService;
import com.jnby.common.enums.OperationTypeEnum;
import com.jnby.common.enums.UltimaBoxDetailsStatusEnum;
import com.jnby.common.enums.UltimaBoxStatusEnum;
import com.jnby.common.enums.UseRightsTypeEnum;
import com.jnby.infrastructure.box.mapper.BoxReturnMapper;
import com.jnby.infrastructure.box.mapper.BoxSupplyMapper;
import com.jnby.infrastructure.box.mapper.ExpressMapper;
import com.jnby.infrastructure.box.mapper.SubscribeAskMapper;
import com.jnby.infrastructure.box.model.*;
import com.jnby.module.message.entity.SysTemplateMsgEntity;
import com.jnby.module.message.enums.SysMsgTemplateCodeEnum;
import com.jnby.module.message.service.ISysMessageService;
import com.jnby.module.order.entity.BoxReturnEntity;
import com.jnby.module.order.entity.BoxSupplyEntity;
import com.jnby.module.order.enums.BoxSupplyStatusEnum;
import com.jnby.module.order.enums.BoxTypeEnum;
import com.jnby.module.order.enums.ExpressStatusEnum;
import com.jnby.module.order.repository.IBoxRepository;
import com.jnby.module.order.service.box.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: lwz
 * @Date: 2021/11/29 14:50
 * @Description: RestoreSceneServiceImpl
 * @Version 1.0.0
 */
@Service
@Slf4j
@RefreshScope
public class RestoreSceneServiceImpl implements IRestoreSceneService {

    @Resource
    private IExpressService iExpressService;


    @Resource
    private IBoxDetailsService iBoxDetailsService;

    @Autowired
    @Qualifier("boxTransactionTemplate")
    private TransactionTemplate template;

    @Resource
    private IBoxActionService iBoxActionService;

    @Resource
    private BoxReturnMapper boxReturnMapper;

    @Resource
    private IBoxReturnDetailsService iBoxReturnDetailsService;

    @Resource
    private ExpressMapper expressMapper;

    @Resource
    private IBGuideService ibGuideService;

    @Resource
    private BoxSupplyMapper boxSupplyMapper;

    @Resource
    private IFashionerService fashionerService;

    @Value("${role.kefu.phones}")
    private String kefuPhones;

    @Autowired
    private ISysMessageService sysMessageService;
    /**
     * 创建还货单进行现场回滚
     *
     * @param boxReturnEntity
     */
    @Override
    public void restoreCreateReturnOrder(BoxReturnEntity boxReturnEntity, String errorMsg) {
        log.info("创建还货单恢复 BoxReturnEntity:{}  errorMsg: {}", JSONObject.toJSON(boxReturnEntity),errorMsg);
        /**
         * 1.检查并取消快递
         * 2.恢复服务单为待还货 服务子单为待还货  任务制恢复
         */
        template.execute(action -> {
            BoxWithBLOBs box = boxReturnEntity.getBox();
            List<BoxDetailsWithBLOBs> boxWithBLOBsList = boxReturnEntity.getBoxDetails();
            if (boxReturnEntity.getExpressFlag()) {
                Boolean flag = iExpressService.checkAndCancelExpress(boxReturnEntity.getLogistics().getRefundId());
                if(!flag){
                    log.info("创建还货单恢复 BoxReturnEntity: {}   物流单号: {} 取消物流失败",boxReturnEntity,boxReturnEntity.getLogistics().getRefundId());
                }
                if(flag){
                    Express updateExpress = new Express();
                    updateExpress.setId(boxReturnEntity.getExpress().getId());
                    // 物流状态(10:待取件;20:待签收;30:已签收;40:取消)
                    updateExpress.setStatus(Long.valueOf(40));
                    expressMapper.updateByPrimaryKeySelective(updateExpress);
                }
            }
            // 服务主单回退 到待还货 （特殊条件： 服务单 已签收2 待还货9 更新主单为待还货）
            if (Long.valueOf(UltimaBoxStatusEnum.SIGNED.getCode()).equals(boxReturnEntity.getBoxLastStatus())
                    || (Long.valueOf(UltimaBoxStatusEnum.WAIT_RETURN.getCode()).equals(boxReturnEntity.getBoxLastStatus()))) {
                ibGuideService.handlerOnlyBoxStatus(box.getId(), Long.valueOf(UltimaBoxStatusEnum.WAIT_RETURN.getCode()), box.getStatus());
            }
            // 服务子单回退到待还货
            ibGuideService.handlerBoxDetailsStatus(boxReturnEntity.getBoxDetails(),Long.valueOf(UltimaBoxDetailsStatusEnum.UNRETURN.getCode()));

            CustomerDetails customerDetails = boxReturnEntity.getCustomerDetails();
            // 恢复权益
            if (box.getType().equals(BoxTypeEnum.FASHIONERBOX.getCode())) {
            }

            // 清除 boxReturn
            boxReturnMapper.deleteByPrimaryKey(boxReturnEntity.getBoxReturn().getId());
            List<String> boxReturnDetailsIds = boxReturnEntity.getBoxReturnDetails().stream().map(item -> item.getId()).collect(Collectors.toList());
            // 清除 boxReturnDetails
            iBoxReturnDetailsService.removeByIds(boxReturnDetailsIds);
            // box行为日志
            iBoxActionService.createBoxAction(boxReturnEntity.getBox().getId(), "申请还货失败 回滚数据"+errorMsg, boxReturnEntity.getBoxLastStatus(), boxReturnEntity.getUserId());
            return action;
        });


    }

    @Override
    public void restoreBoxSupply(BoxSupplyEntity boxSupplyEntity, String errorMsg) {
        log.info("补货恢复 boxSupplyEntity：{}   errorMsg: {}",JSONObject.toJSON(boxSupplyEntity),errorMsg);
        template.execute(action -> {
            List<BoxDetailsWithBLOBs> boxDetailsListTemp = boxSupplyEntity.getBoxDetailsWithBLOBsList();
            List<BoxDetailsWithBLOBs> updateDetailsListTemp = new ArrayList<>();
            for (BoxDetailsWithBLOBs boxDetailsWithBLOBs : boxDetailsListTemp) {
                BoxDetailsWithBLOBs temp = new BoxDetailsWithBLOBs();
                temp.setId(boxDetailsWithBLOBs.getId());
                temp.setStatus(Long.valueOf(UltimaBoxDetailsStatusEnum.CANCEL.getCode()));
                updateDetailsListTemp.add(temp);
            }
            // 补货失败改成已取消
            if (CollectionUtils.isNotEmpty(updateDetailsListTemp)) {
                iBoxDetailsService.updateBatchById(updateDetailsListTemp);
            }
            // 取消 express
            Express updateExpress = new Express();
            updateExpress.setId(boxSupplyEntity.getExpress().getId());
            updateExpress.setStatus(ExpressStatusEnum.cancel.getCode().longValue());
            expressMapper.updateByPrimaryKeySelective(updateExpress);
            // 取消boxSupply
            String boxSupplyId = boxSupplyEntity.getBoxSupply().getId();
            BoxSupply updateBoxSupply = new BoxSupply();
            updateBoxSupply.setId(boxSupplyId);
            updateBoxSupply.setStatus(BoxSupplyStatusEnum.cancel.getCode().longValue());
            boxSupplyMapper.updateByPrimaryKeySelective(updateBoxSupply);
            // box行为日志
            iBoxActionService.createBoxAction(boxSupplyEntity.getBox().getId(), "补货失败 回滚数据"+errorMsg, boxSupplyEntity.getBox().getStatus(), boxSupplyEntity.getUserId());

            // 发送消息给搭配师和客服
            Fashioner fashioner = fashionerService.findById(boxSupplyEntity.getBox().getCreateFasId());
            SysTemplateMsgEntity sysTemplateMsgEntity = new SysTemplateMsgEntity();
            sysTemplateMsgEntity.setPhone(fashioner.getPhone() + "," + kefuPhones);
            sysTemplateMsgEntity.setTemplateCode(SysMsgTemplateCodeEnum.BOX_SUPPLY_SEND_ERROR.getCode());
            HashMap<String, String> params = new HashMap<>();
            params.put("boxSn",boxSupplyEntity.getBoxSupply().getSupplySn());
            params.put("id",boxSupplyEntity.getBox().getId());
            sysTemplateMsgEntity.setParams(params);
            sysMessageService.sendTemplateSysMsg2User(sysTemplateMsgEntity);
            return action;
        });


    }
}
