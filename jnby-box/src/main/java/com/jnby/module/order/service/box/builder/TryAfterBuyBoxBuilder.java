package com.jnby.module.order.service.box.builder;

import com.jnby.base.repository.ICustomerAskBoxRepository;
import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.base.repository.ILogisticsRepository;
import com.jnby.infrastructure.box.model.BoxDetailsWithBLOBs;
import com.jnby.infrastructure.box.model.BoxWithBLOBs;
import com.jnby.infrastructure.box.model.Logistics;
import com.jnby.module.order.repository.IBoxRepository;
import com.jnby.module.order.service.box.DefaultBox;

import java.util.List;

/**
 * <AUTHOR>
 * 先试后买服务单
 */
public class TryAfterBuyBoxBuilder extends BoxBuilder {
    protected boolean isInit = false;

    private DefaultBox defaultBox = new TryAfterBuyBox();


    @Override
    public void init(IBoxRepository iBoxRepository, ICustomerAskBoxRepository iCustomerAskBoxRepository, ILogisticsRepository iLogisticsRepository, ICustomerDetailsRepository iCustomerDetailsRepository) {
        this.isInit = true;
        defaultBox.setiBoxRepository(iBoxRepository);
        defaultBox.setiCustomerAskBoxRepository(iCustomerAskBoxRepository);
        defaultBox.setiLogisticsRepository(iLogisticsRepository);
        defaultBox.setiCustomerDetailsRepository(iCustomerDetailsRepository);
    }

    @Override
    public void buildBox(BoxWithBLOBs box, String customerAskId, String userRightsId) {
        defaultBox.setBoxBody(box, null, null);
    }

    @Override
    public void buildBoxDetails(List<BoxDetailsWithBLOBs> boxDetails) {
        defaultBox.setBoxDetails(boxDetails);
    }

    @Override
    public void buildLogistics(Logistics logistics) {
        defaultBox.setLogistics(logistics);
    }

    @Override
    public DefaultBox build() {
        return defaultBox;
    }
}
