package com.jnby.module.order.service;

import com.jnby.infrastructure.bojun.model.BStoreStorageOccupy;
import com.jnby.infrastructure.box.model.BoxDetailsWithBLOBs;
import com.jnby.infrastructure.box.model.Order;
import com.jnby.module.order.context.SalesSendBoxContext;
import com.jnby.module.order.context.SplitMRetailContext;
import com.jnby.module.order.entity.BatchCreateMRetailEntity;
import com.jnby.module.order.entity.OccupyStorageEntity;
import com.jnby.module.order.entity.SalesSendBoxUpdateEntity;

import java.util.List;

public interface ISalesCoreService {


     void salesSendBox(SalesSendBoxContext context);

     BatchCreateMRetailEntity createMRetail(Order order);

     void imitateCreateRetail(SplitMRetailContext context);

     List<BStoreStorageOccupy> getOccupyStorage(SalesSendBoxContext context);

     void parseBoJunWork(List<BStoreStorageOccupy> list);

     void parseBoJunWork2Eb(List<BStoreStorageOccupy> list);

     List<BStoreStorageOccupy> getOccupyStorageList(List<OccupyStorageEntity.OccupyItem> itemList, String boxSn, String description);

     boolean verificationRelease(List<BStoreStorageOccupy> releaseList);

     void releaseStorage(List<BStoreStorageOccupy> releaseList);

     void placeOrderExpress(SalesSendBoxContext context);

     List<OccupyStorageEntity.OccupyItem> getOccupyItems(List<BoxDetailsWithBLOBs> updateBoxDetailsList,String boxSn);

     /**
      * 发货参数组装
      * @param context
      * @return
      */
     SalesSendBoxUpdateEntity packageBoxData(SalesSendBoxContext context);
     /**
      * 发货参数落库
      * @return
      */
     void parseBoxWork(SalesSendBoxUpdateEntity salesSendBoxUpdateEntity);
}


