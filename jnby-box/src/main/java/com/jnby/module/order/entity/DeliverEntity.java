package com.jnby.module.order.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 搭配师发货实体参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeliverEntity implements Serializable {

    /**
     * 描述，默认为：由BOX仓发起的平衡原库存数量，产生调拨
     */
    private String description ="BOX服务端发货";

    // 是否虚拟发货
    private Integer ifVirDelivery;

    /**
     * 来源号 唯一标识
     */
    private String source;

    /**
     * 用户地址信息
     */
    private AllotStorageEntity.CusInfo cusInfo;

    /**
     * 发货商品
     */
    private List<AllotStorageEntity.AllotProduct> product;

}
