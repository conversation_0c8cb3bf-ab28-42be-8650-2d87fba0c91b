package com.jnby.module.marketing.coupon.service.impl;

import com.jnby.application.minapp.dto.request.ExchangeCoinReq;
import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.common.BoxException;
import com.jnby.common.enums.CouponRecordEnum;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.common.leaf.support.IdWorkLeaf;
import com.jnby.infrastructure.box.mapper.CoinRecordMapper;
import com.jnby.infrastructure.box.model.Box;
import com.jnby.infrastructure.box.model.CoinExchange;
import com.jnby.infrastructure.box.mapper.CoinExchangeMapper;
import com.jnby.infrastructure.box.model.CoinRecord;
import com.jnby.infrastructure.box.model.CustomerDetails;
import com.jnby.module.jic.entity.RulesList;
import com.jnby.module.marketing.coupon.service.ICoinExchangeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jnby.module.marketing.coupon.service.IShareVoucherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * @auther yuanxiaozhong
 * @create 2022-10-28 09:46:56
 * @describe 服务实现类
 */
@Service
public class CoinExchangeServiceImpl extends ServiceImpl<CoinExchangeMapper, CoinExchange> implements ICoinExchangeService {

    @Autowired
    private ICustomerDetailsRepository iCustomerDetailsRepository;

    @Resource
    private CoinRecordMapper coinRecordMapper;

    @Autowired
    private IShareVoucherService iShareVoucherService;

    /**
     * 需要指定使用哪一个事务管理器
     */
    @Autowired
    @Qualifier("boxTransactionTemplate")
    private TransactionTemplate template;


    @Override
    public List<CoinExchange> selectCouponIds() {
        return baseMapper.selectCouponIds();
    }

    @Override
    public void exchangeCoinCoupon(ExchangeCoinReq exchangeCoinReq) {
        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(exchangeCoinReq.getUnionId());
        String coinStr = customerDetails.getCoin() == null ? "0" : customerDetails.getCoin();
        long customerCoin = Long.parseLong(coinStr);

        CoinExchange coinExchange = baseMapper.selectBySourceId(exchangeCoinReq.getCouponId());
        if (Objects.isNull(coinExchange)) throw new BoxException("无可用金币");

        if (customerCoin < Long.parseLong(coinExchange.getCoin())*exchangeCoinReq.getCouponNum()) {
            throw new BoxException("金币不足！！！");
        }

        //创建兑换记录
        template.execute(action -> {
            //扣减积分
            CustomerDetails details = new CustomerDetails();
            details.setId(customerDetails.getId());
            details.setCoin(String.valueOf(customerCoin - (Long.parseLong(coinExchange.getCoin())*exchangeCoinReq.getCouponNum())));
            iCustomerDetailsRepository.updateByPrimaryKeySelective(details);

            LinkedList<RulesList> rulesLists = new LinkedList<>();
            rulesLists.add(new RulesList(Long.valueOf(exchangeCoinReq.getCouponId()), exchangeCoinReq.getCouponNum()));
            iShareVoucherService.sendCouponByType(rulesLists, CouponRecordEnum.Type.coin, exchangeCoinReq.getCouponId(), customerDetails.getId(), customerDetails.getUnionid(), true);

            create(customerDetails.getId(), Double.parseDouble(coinExchange.getCoin())*exchangeCoinReq.getCouponNum(), 0l, "兑换券", 5l, exchangeCoinReq.getCouponId());
            return action;
        });
    }

    /**
     * 新增
     *
     * @param fromType 0:订单 1-邀请人奖励 2-被邀请人奖励 3-心意盒子赠送者奖励 4-心意盒子领取者奖励
     */
    private void create(String customerId, Double coin, Long type, String memo, Long fromType, String fromId) {
        CoinRecord record = new CoinRecord();
        record.setId(IdWorkLeaf.getInstance().getId());
        record.setCustomerId(customerId);
        record.setCoinSn(IdWorkLeaf.getInstance().getId());
        record.setCoin(coin);
        record.setType(type);
        record.setStatus(1l);
        record.setMemo(memo);
        record.setFromType(fromType);
        record.setFromId(fromId);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        coinRecordMapper.insertSelective(record);
    }
}
