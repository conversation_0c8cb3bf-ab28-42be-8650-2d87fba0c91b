package com.jnby.module.marketing.theme.activity.service;

import com.google.common.base.Preconditions;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.jnby.application.admin.dto.request.AddThemeActivityReq;
import com.jnby.application.admin.dto.request.EditThemeActivityByIdReq;
import com.jnby.application.admin.dto.request.GetThemeActivityByIdReq;
import com.jnby.application.admin.dto.request.GetThemeActivityListReq;
import com.jnby.application.admin.dto.response.StylingBaseDetailResp;
import com.jnby.application.admin.dto.response.ThemeActivityWithMaterial;
import com.jnby.application.minapp.dto.response.GetFashionersResp;
import com.jnby.base.entity.FashionerInfoEntity;
import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.base.repository.ICustomerVipRepository;
import com.jnby.base.repository.IFashionerRepository;
import com.jnby.common.enums.ThemeMatchStatusEnum;
import com.jnby.common.util.ParseBrandUtil;
import com.jnby.infrastructure.bojun.mapper.CCustomerMapper;
import com.jnby.infrastructure.bojun.mapper.CStoreMapper;
import com.jnby.infrastructure.bojun.mapper.CVipTypeMapper;
import com.jnby.infrastructure.bojun.mapper.CclientVipMapper;
import com.jnby.infrastructure.bojun.model.CCustomer;
import com.jnby.infrastructure.bojun.model.CVipType;
import com.jnby.infrastructure.bojun.model.CclientVip;
import com.jnby.infrastructure.box.mapper.BThemeMatchMapper;
import com.jnby.infrastructure.box.mapper.SceneMapper;
import com.jnby.infrastructure.box.model.*;
import com.jnby.module.marketing.theme.activity.context.ThemeActivityListContext;
import com.jnby.base.entity.CreateSceneMaterial;
import com.jnby.base.service.*;
import com.jnby.common.enums.SceneTypeEnum;
import com.jnby.common.enums.ThemeActivityStatusEnum;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.infrastructure.box.mapper.BThemeActivityMapper;
import com.jnby.module.marketing.theme.activity.IThemeActivityService;
import com.jnby.module.marketing.theme.activity.entity.ActivityWithMatchs;
import com.jnby.module.marketing.theme.activity.entity.BrandsWithStoreId;
import com.jnby.module.marketing.theme.activity.repository.IBThemeActivityService;
import com.jnby.module.themeshopcart.entity.ThemeBrandWithVipEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: lwz
 * @Date: 2022/4/19 14:59
 * @Description: ThemeActivityServiceImpl
 * @Version 1.0.0
 */
@Service
@Slf4j
public class ThemeActivityServiceImpl implements IThemeActivityService {
    @Resource
    private IBThemeActivityService ibThemeActivityService;

    @Resource
    private BThemeActivityMapper bThemeActivityMapper;

    @Resource
    private BThemeMatchMapper bThemeMatchMapper;

    @Resource
    private IdLeafService idLeafService;

    @Autowired
    @Qualifier("boxTransactionTemplate")
    private TransactionTemplate template;

    @Resource
    private ISceneQrcodeService iSceneQrcodeService;

    @Resource
    private IBWxMaterialService ibWxMaterialService;

    @Resource
    private SceneMapper sceneMapper;

    @Resource
    private CVipTypeMapper cVipTypeMapper;

    @Resource
    private ICustomerDetailsRepository iCustomerDetailsRepository;


    @Resource
    private IAskBoxService iAskBoxService;

    @Resource
    private CclientVipMapper cclientVipMapper;

    @Resource
    private CCustomerMapper cCustomerMapper;

    @Resource
    private IFashionerRepository fashionerRepository;


    //江南布衣+和奥莱
    @Value("${jnby.outlets}")
    private String jnbyAndOlai;


    private static final String[] womenBrand = {"JNBY", "less"};
    private static final String[] manBrand = {"CROQUIS", "APN73"};

    private String olaiBrand = "OUTLETS";


    // 经销限制品牌
    @Value("${dealer.brand}")
    private String dealerBrand;


    //开放区域
    @Value("${dealer.open.zone}")
    private String dealerOpenZone;


    @Override
    public List<BThemeActivity> getThemeActivityList(GetThemeActivityListReq getThemeActivityListReq) {
        ThemeActivityListContext themeActivityListContext = new ThemeActivityListContext();
        BeanUtils.copyProperties(getThemeActivityListReq, themeActivityListContext);
        themeActivityListContext.setNowDate(new Date());
        return bThemeActivityMapper.getThemeActivityList(themeActivityListContext);
    }

    @Override
    public ThemeActivityWithMaterial getThemeActivityById(GetThemeActivityByIdReq getThemeActivityByIdReq) {
        BThemeActivity bThemeActivity = Preconditions.checkNotNull(ibThemeActivityService.getById(getThemeActivityByIdReq.getId()), "主题活动不存在 ");
        BWxMaterial bWxMaterial = ibWxMaterialService.getById(bThemeActivity.getMaterialId());

        ThemeActivityWithMaterial themeActivityWithMaterial = new ThemeActivityWithMaterial();
        themeActivityWithMaterial.setBThemeActivity(bThemeActivity);
        themeActivityWithMaterial.setBWxMaterial(bWxMaterial);

        return themeActivityWithMaterial;
    }

    @Override
    public BThemeActivity addThemeActivity(AddThemeActivityReq addThemeActivityReq, String userId) {
        BThemeActivity bThemeActivity = new BThemeActivity();
        BeanUtils.copyProperties(addThemeActivityReq, bThemeActivity);
        bThemeActivity.setId(idLeafService.getId());
        bThemeActivity.setDelFlag(Long.valueOf(0));
        bThemeActivity.setCreateBy(userId);
        bThemeActivity.setCreateTime(new Date());
        bThemeActivity.setStatus(ThemeActivityStatusEnum.ON.getCode().longValue());
        template.execute(action -> {
            ibThemeActivityService.save(bThemeActivity);
            return true;
        });
        return bThemeActivity;
    }

    @Override
    public void editThemeActivityById(EditThemeActivityByIdReq editThemeActivityByIdReq, String userId) {
        BThemeActivity bThemeActivity = new BThemeActivity();
        BeanUtils.copyProperties(editThemeActivityByIdReq, bThemeActivity);
        bThemeActivity.setUpdateBy(userId);
        bThemeActivity.setUpdateTime(new Date());
        template.execute(action -> {
            ibThemeActivityService.updateById(bThemeActivity);
            return true;
        });
    }


    @Override
    public void handleThemeActivityStatusJob() {
        /**
         * 1. 当前在进行中 已不在主题活动中 改为已结束
         */
        List<BThemeActivity> willClose = bThemeActivityMapper.getWillCloseThemeActivity();
        for (BThemeActivity bThemeActivity : willClose) {
            try {
                handleThemeActivityStatus(bThemeActivity);
            } catch (Exception e) {
                log.error("主题活动状态改为已结束 异常 bThemeActivityId:{}  e:{}  message:{}", bThemeActivity.getId(), e, e.getMessage());
            }
        }

    }

    /**
     * 处理status
     *
     * @param bThemeActivity
     */
    public void handleThemeActivityStatus(BThemeActivity bThemeActivity) {
        template.execute(action -> {
            int result = bThemeActivityMapper.updateOnlyStatus(bThemeActivity.getId(), bThemeActivity.getStatus(), ThemeActivityStatusEnum.DOWN.getCode().longValue());
            if (result == 1) {
                bThemeMatchMapper.updateOnlyStatus(bThemeActivity.getId(), ThemeMatchStatusEnum.DOWN.getCode().longValue());
            }
            return true;
        });
    }

    @Override
    public Scene createQrCode(String materialId, String themeActivityId) {
        BThemeActivity bThemeActivity = Preconditions.checkNotNull(bThemeActivityMapper.selectById(themeActivityId), " 主题活动不存在");
        BWxMaterial bWxMaterial = Preconditions.checkNotNull(ibWxMaterialService.getById(materialId), "素材不存在");
        Preconditions.checkArgument(bWxMaterial.getIsDel().intValue() == 0, "素材不存在");


        CreateSceneMaterial createSceneMaterial = new CreateSceneMaterial();
        createSceneMaterial.setMaterialId(materialId);
        createSceneMaterial.setIfCreateQrCode(true);
        createSceneMaterial.setImgUrl(bWxMaterial.getImgUrl());
        createSceneMaterial.setTitle(bWxMaterial.getTitle());
        if (StringUtils.isNotEmpty(bThemeActivity.getScenesId())) {
            Scene sceneTemp = sceneMapper.selectByPrimaryKey(bThemeActivity.getScenesId());
            // 失效时间
            Date expireTime = DateUtils.addDays(new Date(), 30);
            // 大于30天重新生成码
            if (sceneTemp != null && DateUtils.truncatedCompareTo(sceneTemp.getCreateTime(), expireTime, Calendar.DATE) < 0) {
                createSceneMaterial.setSceneId(bThemeActivity.getScenesId());
            }
        }
        Map<String, Object> params = new HashMap<>();
        params.put("theme_activity_id", themeActivityId);
        createSceneMaterial.setParams(params);
        createSceneMaterial.setSceceTypeEnum(SceneTypeEnum.THEME_ACTIVTIY);
        Scene scene = iSceneQrcodeService.queryOrCreateSceneMaterial(createSceneMaterial);

        if (StringUtils.isEmpty(bThemeActivity.getScenesId()) || !StringUtils.equals(bThemeActivity.getScenesId(), scene.getId())) {
            BThemeActivity updateThActivity = new BThemeActivity();
            updateThActivity.setId(bThemeActivity.getId());
            updateThActivity.setScenesId(scene.getId());
            template.execute(action -> {
                ibThemeActivityService.updateById(updateThActivity);
                return true;
            });
        }
        return scene;
    }

    @Override
    public void upOrDownThemeActivityById(String themeActivityId, Long status, String userId) {
        Preconditions.checkNotNull(bThemeActivityMapper.selectByPrimaryKey(themeActivityId), "主题活动不存在");
        BThemeActivity updateBth = new BThemeActivity();
        updateBth.setId(themeActivityId);
        updateBth.setUpdateBy(userId);
        updateBth.setStatus(status);
        if (Long.valueOf(0).equals(status)) {
            updateBth.setStartTime(new Date());
        }
        template.execute(action -> {
            ibThemeActivityService.updateById(updateBth);
            // 标识(0上架，1下架)
            if (Long.valueOf(1).equals(status)) {
                bThemeMatchMapper.updateOnlyStatus(themeActivityId, ThemeMatchStatusEnum.DOWN.getCode().longValue());
            }
            return true;
        });
    }

    @Override
    public Map<String, Integer> getCount() {
        Map<String, Integer> map = new HashMap<>(6);
        map.put("wait", bThemeActivityMapper.findListCountByStatus(Long.valueOf(0)));
        map.put("running", bThemeActivityMapper.findListCountByStatus(Long.valueOf(1)));
        map.put("over", bThemeActivityMapper.findListCountByStatus(Long.valueOf(2)));
        return map;
    }

    @Override
    public BThemeActivity miniGetThemeActivity(String activityId) {
        BThemeActivity bThemeActivity = Preconditions.checkNotNull(bThemeActivityMapper.selectById(activityId), "主题活动不存在");
        Preconditions.checkArgument(bThemeActivity.getDelFlag().intValue() == 0, "主题活动不存在");
        return bThemeActivity;
    }

    @Override
    public GetFashionersResp getFashioners(String unionId, String brandId, String brandName) {
        CustomerDetails customerDetails = Preconditions.checkNotNull(iCustomerDetailsRepository.findByUnionId(unionId), "客户信息不存在");
        // 指定性别品牌    10 男装  20 女装
        int expert = Arrays.asList(womenBrand).contains(brandName) ? 20 : 10;

        // 获取符合的搭配师(根据男女品牌)
        List<Fashioner> fashionerList = fashionerRepository.findByExpert(expert)
                .stream().filter(r -> r.getIfUseMiniapp() == 1)
                .sorted(Comparator.comparing(e -> e.getPriority() == null ? 0 : e.getPriority())).collect(Collectors.toList());

        List<FashionerInfoEntity> fashioners = new ArrayList<>();
        // 获取通用搭配师信息
        List<FashionerInfoEntity> universalFashioners = iAskBoxService.createSalesListInfo(fashionerList);

        if (StringUtils.isNotEmpty(customerDetails.getFashionerId())) {
            Fashioner fashioner = fashionerRepository.findById(customerDetails.getFashionerId());
            if (fashioner!=null && fashioner.getIsSales() == 0 && fashioner.getStatus().intValue() == 1) {
                fashioners.add(iAskBoxService.setFashionerInfo(fashioner));
            } else {
                fashioners.addAll(universalFashioners);
            }
        } else {
            fashioners.addAll(universalFashioners);
        }
        GetFashionersResp getFashionersResp = new GetFashionersResp();
        getFashionersResp.setCode(1);
        getFashionersResp.setFashionerInfoEntities(fashioners);
        return getFashionersResp;
    }


    /**
     * 处理卡信息获取品牌和门店
     *
     * @param brand    品牌
     * @param userVips 卡信息
     * @return
     */
    BrandsWithStoreId getBrandsAndCStoreIdHandleVips(String brand, List<CclientVip> userVips) {
        BrandsWithStoreId brandsWithStoreId = new BrandsWithStoreId();
        List<String> brandList = new ArrayList<>();

        brand = ParseBrandUtil.parseBrandToVip(brand);
        for (CclientVip cclientVip : userVips) {
            //获取卡的信息
            CVipType cVipType = cVipTypeMapper.selectByPrimaryKey(cclientVip.getcViptypeId());
            if (cVipType == null) {
                continue;
            }

            // 非指定品牌
            if (!StringUtils.equals(brand, cVipType.getDescription())) {
                continue;
            }

            if (cclientVip.getcCustomerId() != null && !cclientVip.getcCustomerId().equals(176L)) {
                // 经销用户再校验是否在限制品牌内
                if (Arrays.asList(dealerBrand.split(",")).contains(cVipType.getDescription())) {
                    CCustomer cCustomer = cCustomerMapper.selectByPrimaryKey(cclientVip.getcCustomerId());
                    // 判断是否在经销开放区域内
                    if (!Arrays.asList(dealerOpenZone.split(",")).contains(cCustomer.getCode())) {
                        continue;
                    }
                }
            }
            brandList.add(cVipType.getDescription());
        }
        brandsWithStoreId.setBrandList(brandList);
        return brandsWithStoreId;
    }

    /**
     * 获取用户会员卡信息
     *
     * @param customerDetails
     * @return
     */
    public List<CclientVip> getUserVips(CustomerDetails customerDetails) {
        // 查询用户所有拥有的卡
        CclientVip clientVipSearch = new CclientVip();
        clientVipSearch.setUnionid(customerDetails.getUnionid());
        if (StringUtils.isNotBlank(customerDetails.getPhone())) {
            clientVipSearch.setMobil(customerDetails.getPhone());
        }
        clientVipSearch.setIsactive("Y");
        return cclientVipMapper.selectListBySelective(clientVipSearch);
    }

    /**
     * 用户无卡
     *
     * @param customerDetails
     * @param fashionerList
     * @return
     */
    public GetFashionersResp userNoCard(CustomerDetails customerDetails, List<Fashioner> fashionerList) {
        GetFashionersResp getFashionersResp = new GetFashionersResp();
        // 是否绑定搭配师
        getFashionersResp.setCode(0);

        Integer sales = fashionerRepository.fashionerOrSales(customerDetails.getFashionerId());
        if (sales == 0) {
            //绑定的搭配师
            Fashioner fashionerById = fashionerRepository.findById(customerDetails.getFashionerId());
            List<Fashioner> resultList = new ArrayList<>();
            resultList.add(fashionerById);
            getFashionersResp.setFashioners(resultList);
            return getFashionersResp;
        }
        getFashionersResp.setFashioners(fashionerList);

        return getFashionersResp;
    }

    @Override
    public ThemeBrandWithVipEntity getThemeBrandWithVip(String unionId, String brandId, String brandName) {
        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(unionId);

        List<CclientVip> userVips = getUserVips(customerDetails);
        brandName = ParseBrandUtil.parseBrandToVip(brandName);

        ThemeBrandWithVipEntity themeBrandWithVipEntity = new ThemeBrandWithVipEntity();
        themeBrandWithVipEntity.setType(0);
        themeBrandWithVipEntity.setFlag(true);
        for (CclientVip cclientVip : userVips) {
            //获取卡的信息
            CVipType cVipType = cVipTypeMapper.selectByPrimaryKey(cclientVip.getcViptypeId());
            if (cVipType == null) {
                continue;
            }

            // 非指定品牌
            if (!StringUtils.equals(brandName, cVipType.getDescription())) {
                continue;
            }

            if (cclientVip.getcCustomerId() == null) {
                continue;
            }

            if (cclientVip.getcCustomerId() != null && !cclientVip.getcCustomerId().equals(176L)) {
                // 经销用户再校验是否在限制品牌内
                if (Arrays.asList(dealerBrand.split(",")).contains(cVipType.getDescription())) {
                    CCustomer cCustomer = cCustomerMapper.selectByPrimaryKey(cclientVip.getcCustomerId());
                    // 判断是否在经销开放区域内
                    if (!Arrays.asList(dealerOpenZone.split(",")).contains(cCustomer.getCode())) {
                        themeBrandWithVipEntity.setFlag(false);
                        continue;
                    }
                }
            }
            themeBrandWithVipEntity.setCclientVip(cclientVip);
            themeBrandWithVipEntity.setType(1);
        }

        return themeBrandWithVipEntity;
    }


    @Override
    public ActivityWithMatchs getActivityWithMatchs(String activityId) {
        BThemeActivity bThemeActivity = bThemeActivityMapper.selectByPrimaryKey(activityId);


        BThemeMatch search = new BThemeMatch();
        search.setActivityId(activityId);
        search.setStatus(ThemeMatchStatusEnum.ON.getCode().longValue());
        List<BThemeMatch> bThemeMatches = bThemeMatchMapper.getThemeMatchList(search);

        ActivityWithMatchs activityWithMatchs = new ActivityWithMatchs();
        activityWithMatchs.setBThemeActivity(bThemeActivity);
        activityWithMatchs.setMatchCount(CollectionUtils.size(bThemeMatches));
        return activityWithMatchs;
    }

    @Override
    public List<ActivityWithMatchs> getAllActivityWithMatchs() {
        ThemeActivityListContext themeActivityListContext = new ThemeActivityListContext();
        themeActivityListContext.setStatus("1");
        themeActivityListContext.setMiniAppShow(Long.valueOf(1));
        themeActivityListContext.setNowDate(new Date());
        List<BThemeActivity> list = bThemeActivityMapper.getThemeActivityList(themeActivityListContext).stream()
                .sorted(
                        Comparator.comparing(BThemeActivity::getSortNo).reversed().thenComparing(BThemeActivity::getCreateTime).reversed()
                ).collect(Collectors.toList());
        List<ActivityWithMatchs> activityWithMatchs = new ArrayList<>();

        if (CollectionUtils.isEmpty(list)) {
            return activityWithMatchs;
        }

        List<String> activityIds = list.stream().map(e -> e.getId()).collect(Collectors.toList());


        List<BThemeMatch> bThemeMatches = bThemeMatchMapper.selectByActivityIds(activityIds);
        Multimap<String, BThemeMatch> bThemeMatchMap = ArrayListMultimap.create();
        for (BThemeMatch bThemeMatch : bThemeMatches) {
            if (bThemeMatch.getStatus().intValue() == 1 || bThemeMatch.getDelFlag().intValue() == 1 || bThemeMatch.getMarketingStatus().intValue() != 1) {
                continue;
            }
            bThemeMatchMap.put(bThemeMatch.getActivityId(), bThemeMatch);
        }

        for (BThemeActivity bThemeActivity : list) {
            ActivityWithMatchs temp = new ActivityWithMatchs();
            temp.setBThemeActivity(bThemeActivity);
            if (bThemeMatchMap.containsKey(bThemeActivity.getId())) {
                temp.setMatchCount(bThemeMatchMap.get(bThemeActivity.getId()).size());
            } else {
                temp.setMatchCount(0);
            }
            activityWithMatchs.add(temp);
        }

        return activityWithMatchs;
    }
}
