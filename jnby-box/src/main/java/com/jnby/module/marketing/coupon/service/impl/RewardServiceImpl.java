package com.jnby.module.marketing.coupon.service.impl;

import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.common.leaf.support.IdWorkLeaf;
import com.jnby.common.util.DateUtil;
import com.jnby.infrastructure.box.mapper.CoinRecordMapper;
import com.jnby.infrastructure.box.mapper.InvitationDetailsMapper;
import com.jnby.infrastructure.box.mapper.InvitationMapper;
import com.jnby.infrastructure.box.mapper.InvitationOrderDetailsMapper;
import com.jnby.infrastructure.box.model.*;
import com.jnby.module.marketing.coupon.service.IRewardService;
import com.jnby.module.order.repository.IOrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class RewardServiceImpl implements IRewardService {

    @Autowired
    private ICustomerDetailsRepository iCustomerDetailsRepository;

    @Autowired
    private IOrderRepository iOrderRepository;

    @Autowired
    private InvitationMapper invitationMapper;

    @Autowired
    private InvitationDetailsMapper invitationDetailsMapper;

    @Autowired
    private InvitationOrderDetailsMapper invitationOrderDetailsMapper;

    @Autowired
    private CoinRecordMapper coinRecordMapper;

    @Override
    public void nvitationBox(String unionId, String boxSn) {
        List<Invitation> invitations = invitationMapper.findByToUnionIdActive(unionId);
        if (invitations.isEmpty()) return;

        //判断是否为内部邀请
        InvitationDetails details = new InvitationDetails();
        details.setBoxSn(boxSn);
        List<InvitationDetails> list = invitationDetailsMapper.selectListBySelective(details);
        if (!list.isEmpty()) return;
        details.setId(IdWorkLeaf.getInstance().getId());
        details.setUnionid(unionId);
        details.setInvitationId(invitations.get(0).getId());
        details.setIscashout((short) 2);
        details.setIsRefund((short) 3);
        details.setCreateTime(new Date());
        details.setUpdateTime(new Date());
        invitationDetailsMapper.insertSelective(details);
    }

    @Override
    public void invitationAward(String orderId) {
        Order order = iOrderRepository.findById(orderId);
        CustomerDetails customerDetails = iCustomerDetailsRepository.findById(order.getCustomerId());
        List<Invitation> invitations = invitationMapper.findByToUnionIdActive(customerDetails.getUnionid());
        if (invitations.isEmpty()) return;

        Invitation invitation = invitations.get(0);
        InvitationDetails invitationDetails = new InvitationDetails();
        invitationDetails.setBoxSn(order.getBoxSn());
        List<InvitationDetails> invitationDetailsList = invitationDetailsMapper.selectListBySelective(invitationDetails);
        if (invitationDetailsList.isEmpty()){
            return;
        }

        invitationDetails.setId(IdWorkLeaf.getInstance().getId());
        invitationDetails.setUnionid(customerDetails.getUnionid());
        invitationDetails.setInvitationId(invitation.getId());
        invitationDetails.setIscashout((short) 0);
        invitationDetails.setIsRefund((short) 2);
        invitationDetails.setCreateTime(new Date());
        invitationDetails.setUpdateTime(new Date());
        invitationDetailsMapper.insertSelective(invitationDetails);

        InvitationOrderDetails invitationOrderDetail = new InvitationOrderDetails();
        invitationOrderDetail.setId(IdWorkLeaf.getInstance().getId());
        invitationOrderDetail.setInvitationDetailsId(invitationDetails.getId());
        invitationOrderDetail.setOrderId(orderId);
        invitationOrderDetail.setRecurrence(0.0);
        invitationOrderDetail.setType((short) 0);
        invitationOrderDetail.setIsRefund((short) 2);
        invitationOrderDetailsMapper.insertSelective(invitationOrderDetail);
    }

    @Override
    public void coinAward(String orderId) {
        Order order = iOrderRepository.findById(orderId);
        CustomerDetails customerDetails = iCustomerDetailsRepository.findById(order.getCustomerId());
        Double coin = order.getCoin();
        if (coin != null && coin > 0) {
            //查找订单收入金币记录
            CoinRecord coinRecord = new CoinRecord();
            coinRecord.setType(1L);
            coinRecord.setFromType(0l);
            coinRecord.setFromId(orderId);
            List<CoinRecord> coinRecords = coinRecordMapper.selectListBySelective(coinRecord);
            if (coinRecords.isEmpty()){
                String coinStr = customerDetails.getCoin() == null ? "0" : customerDetails.getCoin();
                CustomerDetails details = new CustomerDetails();
                details.setId(customerDetails.getId());
                details.setCoin(String.valueOf(Double.valueOf(coinStr) + coin));
                iCustomerDetailsRepository.updateByPrimaryKeySelective(details);
                createOrderCoinRecord(customerDetails.getId(), coin, 1, "订单购买", 0, orderId);
            }
        }
    }

    private void createOrderCoinRecord(String customerId, double coin, int type, String memo, int fromType, String fromId){
        CoinRecord record = new CoinRecord();
        record.setId(IdWorkLeaf.getInstance().getId());
        record.setCoin(coin);
        record.setType((long) type);
        record.setFromId(fromId);
        record.setFromType((long) fromType);
        record.setCustomerId(customerId);
        record.setMemo(memo);
        record.setStatus(1L);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());

        Date now = new Date();
        String dayStr = DateUtil.formatToStr(now, DateUtil.DATE_FORMAT_YYMMDD);
        String number = coinRecordMapper.selectMaxSerialNum(dayStr);
        Integer sn = number == null ? 0 : Integer.valueOf(number);
        AtomicInteger atomicNum = new AtomicInteger();
        atomicNum.set(sn);
        String serialNumber = String.format("%08d", atomicNum.incrementAndGet());
        record.setCoinSn("COIN" + dayStr + serialNumber);
        record.setDaystr(dayStr);
        record.setSerialNumber(serialNumber);
        coinRecordMapper.insertSelective(record);
    }

    @Override
    public void equityAward(String orderId) {

    }

    @Override
    public void cashBackAward(String orderId) {
        Order order = iOrderRepository.findById(orderId);
        CustomerDetails customerDetails = iCustomerDetailsRepository.findById(order.getCustomerId());
        List<Invitation> invitation = invitationMapper.findByToUnionIdActive(customerDetails.getUnionid());
        if (invitation.isEmpty()) return;

        //判断是否在返现邀请人员中
        //在判断返现订单主表是否有数据（服务单生成的时候生成）
        //在订单生成的时候新增返现订单明细表
        InvitationDetails invitationDetails = new InvitationDetails();
        invitationDetails.setBoxSn(order.getBoxSn());
        List<InvitationDetails> invitationDetailsList = invitationDetailsMapper.selectListBySelective(invitationDetails);
        if (invitationDetailsList.isEmpty()) return;

        InvitationOrderDetails orderDetails = new InvitationOrderDetails();
        orderDetails.setOrderId(orderId);
        List<InvitationOrderDetails> list = invitationOrderDetailsMapper.selectListBySelective(orderDetails);
        if (list.isEmpty()){
            orderDetails.setId(IdWorkLeaf.getInstance().getId());
            orderDetails.setType((short) 0);
            orderDetails.setInvitationDetailsId(invitationDetailsList.get(0).getId());
            orderDetails.setRecurrence((double) 0);
            orderDetails.setType((short) 0); //默认未提现
            orderDetails.setIsRefund((short) 2);
            invitationOrderDetailsMapper.insertSelective(orderDetails);
        }

    }
}
