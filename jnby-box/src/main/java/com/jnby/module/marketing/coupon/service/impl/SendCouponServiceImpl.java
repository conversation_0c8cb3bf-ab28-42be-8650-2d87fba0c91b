package com.jnby.module.marketing.coupon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jnby.infrastructure.box.model.SendCoupon;
import com.jnby.infrastructure.box.mapper.SendCouponMapper;
import com.jnby.module.marketing.coupon.service.ISendCouponService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @auther yuanxiaozhong
 * @create 2022-07-19 16:42:11
 * @describe 发券表服务实现类
 */
@Service
public class SendCouponServiceImpl extends ServiceImpl<SendCouponMapper, SendCoupon> implements ISendCouponService {

    @Override
    public List<SendCoupon> queryWaitSendCouponList() {
        return this.baseMapper.queryWaitSendCouponList();
    }

    @Override
    public SendCoupon getWaitSendCoupon() {
        return this.baseMapper.getWaitSendCoupon();
    }

    /**
     * 乐观锁更新状态
     * @param id
     * @param fromStatus
     * @param toStatus
     * @return
     */
    @Override
    public Boolean updateByIdOptimistic(String id, Integer fromStatus, Integer toStatus) {
        Assert.notNull(id, "id can not null");
        Assert.notNull(fromStatus, "fromStatus can not null");
        Assert.notNull(toStatus, "toStatus can not null");
        SendCoupon updateSend = new SendCoupon();
        updateSend.setStatus(toStatus);
        LambdaQueryWrapper<SendCoupon> whereCondition = new LambdaQueryWrapper<SendCoupon>().eq(SendCoupon::getId, id).eq(SendCoupon::getStatus, fromStatus);
        return this.baseMapper.update(updateSend, whereCondition)>0;
    }
}
