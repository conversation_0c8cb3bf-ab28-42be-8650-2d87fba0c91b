package com.jnby.module.marketing.theme.match.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Preconditions;
import com.jnby.application.admin.dto.request.AddThemeMatchReq;
import com.jnby.application.admin.dto.request.EditThemeMatchReq;
import com.jnby.application.admin.dto.request.OnOrDownThemeMatchReq;
import com.jnby.base.entity.CreateSceneMaterial;
import com.jnby.base.repository.ICustomerVipRepository;
import com.jnby.base.service.IBWxMaterialService;
import com.jnby.base.service.ISceneQrcodeService;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.common.enums.SceneTypeEnum;
import com.jnby.common.enums.ThemeActivityStatusEnum;
import com.jnby.infrastructure.bojun.mapper.CVipTypeMapper;
import com.jnby.infrastructure.bojun.model.CVipType;
import com.jnby.infrastructure.bojun.model.CclientVip;
import com.jnby.infrastructure.box.mapper.BThemeActivityMapper;
import com.jnby.infrastructure.box.mapper.SceneMapper;
import com.jnby.infrastructure.box.model.*;
//import com.jnby.material.api.IMaterialApi;
//import com.jnby.material.api.dto.GetLookReq;
//import com.jnby.material.api.dto.ReloadLookResp;
//import com.jnby.material.api.dto.SysLookProductResp;
import com.jnby.module.marketing.match.fashionerMatch.service.IBFashionerMatchService;
import com.jnby.module.marketing.theme.activity.repository.IBThemeActivityService;
import com.jnby.module.marketing.theme.match.entity.ThemeMatchWithLookEntity;
import com.jnby.module.marketing.theme.match.repository.IBThemeMatchService;
import com.jnby.base.service.IFashionerService;
import com.jnby.common.enums.ThemeMatchStatusEnum;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.infrastructure.box.mapper.BThemeMatchMapper;
import com.jnby.module.marketing.theme.match.service.IThemeMatchService;
import com.jnby.module.oauth.oss.service.IUserInfoService;
import com.jnby.module.themeshopcart.entity.ThemeBrandWithVipEntity;
import com.jnbyframework.boot.common.system.vo.LoginUser;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springcenter.material.api.IMaterialApi;
import org.springcenter.material.api.dto.GetLookReq;
import org.springcenter.material.api.dto.ReloadLookResp;
import org.springcenter.material.api.dto.SysLookProductResp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: lwz
 * @Date: 2022/4/20 15:22
 * @Description: ThemeMatchService
 * @Version 1.0.0
 */
@Service
public class ThemeMatchServiceImpl implements IThemeMatchService {

    @Resource
    private BThemeMatchMapper bThemeMatchMapper;

    @Resource
    private IFashionerService iFashionerService;

    @Resource
    private IdLeafService idLeafService;

    @Resource
    private IBThemeMatchService ibThemeMatchService;

    @Autowired
    @Qualifier("boxTransactionTemplate")
    private TransactionTemplate template;

    @Resource
    private IBWxMaterialService ibWxMaterialService;

    @Resource
    private ISceneQrcodeService iSceneQrcodeService;

    @Resource
    private IBThemeActivityService ibThemeActivityService;

    @Resource
    private IUserInfoService userInfoService;

    @Resource
    private BThemeMatchMapper ibThemeMatchMapper;

    @Resource
    private SceneMapper sceneMapper;

    @Resource
    private BThemeActivityMapper bThemeActivityMapper;

    @Resource
    private IBFashionerMatchService iBFashionerMatchService;

    @Resource
    private ICustomerVipRepository iCustomerVipRepository;


    @Resource
    private CVipTypeMapper cVipTypeMapper;

    @Autowired
    private IMaterialApi iMaterialApi;

    //江南布衣+和奥莱
    @Value("${jnby.outlets}")
    private String jnbyAndOlai;


    @Override
    public List<BThemeMatch> getThemeMatchList(BThemeMatch bThemeMatch) {
        return bThemeMatchMapper.getThemeMatchListOrderByCreate(bThemeMatch);
    }


    @Override
    public void addThemeMatch(AddThemeMatchReq addThemeMatchReq, String userId) {
        BThemeMatch bThemeMatch = new BThemeMatch();
        BeanUtils.copyProperties(addThemeMatchReq, bThemeMatch);
        bThemeMatch.setId(idLeafService.getId());
        bThemeMatch.setDelFlag(Long.valueOf(0));
        bThemeMatch.setCreateTime(new Date());
        if (StringUtils.isNotEmpty(userId)) {
            bThemeMatch.setCreateBy(userId);
        }

        template.execute(action -> {
            ibThemeMatchService.save(bThemeMatch);
            return true;
        });
    }


    @Override
    public void editThemeMatchById(EditThemeMatchReq editThemeMatchByIdReq, String userId) {
        Preconditions.checkNotNull(ibThemeMatchService.getById(editThemeMatchByIdReq.getId()), "主题Id:{} 搭配不存在", editThemeMatchByIdReq.getId());

        BThemeMatch updateBThemeMatch = new BThemeMatch();
        BeanUtils.copyProperties(editThemeMatchByIdReq, updateBThemeMatch);
        updateBThemeMatch.setUpdateTime(new Date());
        if (StringUtils.isNotEmpty(userId)) {
            updateBThemeMatch.setUpdateBy(userId);
        }

        template.execute(action -> {
            ibThemeMatchService.updateById(updateBThemeMatch);
            return true;
        });

    }

    @Override
    public void onOrDownThemeMatchById(OnOrDownThemeMatchReq onOrDownThemeMatchReq, String userId) {
        Preconditions.checkNotNull(ibThemeMatchService.getById(onOrDownThemeMatchReq.getId()), "主题搭配不存在");

        BThemeMatch updateBThemeMatch = new BThemeMatch();
        BeanUtils.copyProperties(onOrDownThemeMatchReq, updateBThemeMatch);
        updateBThemeMatch.setUpdateTime(new Date());
        if (StringUtils.isNotEmpty(userId)) {
            updateBThemeMatch.setUpdateBy(userId);
        }

        template.execute(action -> {
            ibThemeMatchService.updateById(updateBThemeMatch);
            return true;
        });
    }

    @Override
    public Scene createQrCode(String themeMatchId) {
        BThemeMatch bThemeMatch = Preconditions.checkNotNull(ibThemeMatchMapper.selectByPrimaryKey(themeMatchId), "主题搭配不存在");
        BThemeActivity bThemeActivity = Preconditions.checkNotNull(bThemeActivityMapper.selectByPrimaryKey(bThemeMatch.getActivityId()), "主题活动不存在");
        BWxMaterial bWxMaterial = Preconditions.checkNotNull(ibWxMaterialService.getById(bThemeActivity.getMaterialId()), "素材不存在");
        Preconditions.checkArgument(bWxMaterial.getIsDel().intValue() == 0, "素材不存在");

        CreateSceneMaterial createSceneMaterial = new CreateSceneMaterial();
        createSceneMaterial.setMaterialId(bThemeActivity.getMaterialId());
        createSceneMaterial.setIfCreateQrCode(true);
        createSceneMaterial.setImgUrl(bWxMaterial.getImgUrl());
        createSceneMaterial.setTitle(bWxMaterial.getTitle());
        if (StringUtils.isNotEmpty(bThemeMatch.getScenesId())) {
            Scene sceneTemp = sceneMapper.selectByPrimaryKey(bThemeMatch.getScenesId());
            // 失效时间
            Date expireTime = DateUtils.addDays(new Date(), 30);
            // 大于30天重新生成码
            if (sceneTemp != null && DateUtils.truncatedCompareTo(sceneTemp.getCreateTime(), expireTime, Calendar.DATE) < 0) {
                createSceneMaterial.setSceneId(bThemeActivity.getScenesId());
            }
        }


        Map<String, Object> params = new HashMap<>();
        params.put("theme_activity_id", bThemeActivity.getId());
        params.put("theme_match_id", bThemeMatch.getId());
        params.put("brand_id",bThemeMatch.getJoinBrandId());

        createSceneMaterial.setParams(params);
        createSceneMaterial.setSceceTypeEnum(SceneTypeEnum.THEME_MATCH);
        Scene scene = Preconditions.checkNotNull(iSceneQrcodeService.queryOrCreateSceneMaterial(createSceneMaterial), "场景码不存在");

        if (StringUtils.isEmpty(bThemeMatch.getScenesId()) || !StringUtils.equals(bThemeMatch.getScenesId(), scene.getId())) {
            LoginUser loginUser = userInfoService.getUserInfo();
            String userName = loginUser == null ? "" : loginUser.getRealname();
            BThemeMatch updateBthemeMatch = new BThemeMatch();
            updateBthemeMatch.setId(bThemeMatch.getId());
            updateBthemeMatch.setScenesId(scene.getId());
            updateBthemeMatch.setUpdateBy(userName);

            template.execute(action -> {
                ibThemeMatchService.updateById(updateBthemeMatch);
                return true;
            });
        }
        return scene;
    }

    @Override
    public Boolean validCanDelBrand(String themeActivityId, String joinBrandId) {
        BThemeMatch bThemeMatch = new BThemeMatch();
        bThemeMatch.setActivityId(themeActivityId);
        bThemeMatch.setJoinBrandId(joinBrandId);
        bThemeMatch.setStatus(ThemeMatchStatusEnum.ON.getCode().longValue());

        Preconditions.checkNotNull(ibThemeActivityService.getById(themeActivityId), "主题活动不存在");
        return CollectionUtils.isEmpty(ibThemeMatchMapper.getThemeMatchList(bThemeMatch));
    }

    @Override
    public List<BThemeMatch> getThemeMatchWithLookList(String themeActivityId, String joinBrandId) {
        BThemeMatch bThemeMatchSearch = new BThemeMatch();
        bThemeMatchSearch.setActivityId(themeActivityId);
        bThemeMatchSearch.setJoinBrandId(joinBrandId);
        bThemeMatchSearch.setStatus(ThemeMatchStatusEnum.ON.getCode().longValue());
        return ibThemeMatchMapper.getThemeMatchList(bThemeMatchSearch).stream()
                .sorted(
                        Comparator.comparing(BThemeMatch::getSortNo).reversed().thenComparing(BThemeMatch::getCreateTime).reversed()
                ).collect(Collectors.toList());
    }


    @Override
    public ThemeMatchWithLookEntity getLookProduct(String lookId) {
        BFashionerMatch match = Preconditions.checkNotNull(iBFashionerMatchService.getOne(new QueryWrapper<>(new BFashionerMatch().setId(lookId))), "有搭数据不存在");
        // look数据回显

        CommonRequest<GetLookReq> commonRequest = new CommonRequest();
        GetLookReq getLookReq = new GetLookReq();
        List<Integer> lookIds = new ArrayList<>();
        lookIds.add(match.getLookId().intValue());
        getLookReq.setLookIds(lookIds);
        commonRequest.setRequestData(getLookReq);
        ResponseResult<List<ReloadLookResp>> looks = iMaterialApi.getLooks(commonRequest);
        List<ReloadLookResp> data = looks.getData();
        ReloadLookResp lookRespEntity =data.get(0);
        // 商品数据
        List<SysLookProductResp> productList = lookRespEntity.getProductList();
        List<ThemeMatchWithLookEntity.LookProduct> tempList = productList.stream().map(e -> {
            ThemeMatchWithLookEntity.LookProduct lookProduct = new ThemeMatchWithLookEntity.LookProduct();
            BeanUtils.copyProperties(e, lookProduct);
            if (StringUtils.isNotBlank(e.getExtendJson())) {
                JSONObject productExtend = JSON.parseObject(e.getExtendJson());
                lookProduct.setProductBrand(productExtend.getString("productBrand"));
                lookProduct.setProductName(productExtend.getString("productName"));
                lookProduct.setProductColorName(productExtend.getString("productColorName"));
                lookProduct.setProductImage(productExtend.getString("productImage"));
            }
            return lookProduct;
        }).collect(Collectors.toList());
        ThemeMatchWithLookEntity themeMatchWithLookEntity = new ThemeMatchWithLookEntity();
        themeMatchWithLookEntity.setProductList(tempList);
        themeMatchWithLookEntity.setCount(CollectionUtils.isEmpty(tempList) ? 0 : tempList.size());

        return themeMatchWithLookEntity;

    }


    @Override
    public void delThemeMatchById(String id, String userId) {
        Preconditions.checkNotNull(ibThemeMatchMapper.selectByPrimaryKey(id), "主题搭配不存在");

        BThemeMatch updateBthemeMatch = new BThemeMatch();
        updateBthemeMatch.setId(id);
        updateBthemeMatch.setUpdateBy(userId);
        updateBthemeMatch.setDelFlag(Long.valueOf(1));

        template.execute(action -> {
            ibThemeMatchService.updateById(updateBthemeMatch);
            return true;
        });

    }

    @Override
    public List<BThemeMatch> getThemeMatchList(List<String> ids) {
        return bThemeMatchMapper.selectBatchIds(ids);
    }

    private String olaiBrand = "OUTLETS";

    @Override
    public ThemeBrandWithVipEntity getThemeBrandWithInStockEntity() {
        String unionId = "";
        String brandId = "";
        List<CclientVip> cclientVips = iCustomerVipRepository.findCustomerVipByUnionId(unionId);
        List<String> jnByAndOrli = new ArrayList<>();


        // 可用的品牌
        List<String> brandList = new ArrayList<>();

        // cStoreId
        Long cStoreId = null;

        //是否有江南布衣卡 或者 奥莱卡
        boolean haveJNByOrOali = false;

        for (CclientVip cclientVip : cclientVips) {
            //获取卡的信息
            CVipType cVipType = cVipTypeMapper.selectByPrimaryKey(cclientVip.getcViptypeId());

            //江南布衣+ ka  或者 奥莱   后面那部在判断是否可以用奥莱的导购
            if (Arrays.asList(jnbyAndOlai.split(",")).contains(cVipType.getDescription())) {
                if (cclientVip.getSalesrepId() != null) {

                    // 是否有下盒子权限 没有的话 查询单品sku

                    // 无库存被抢光了


                    // 有 关联导购
                    // 无库存被抢光了
                }
            }


            //江南布衣+ ka  或者 奥莱   后面那部在判断是否可以用奥莱的导购
            if (Arrays.asList(jnbyAndOlai.split(",")).contains(cVipType.getDescription())) {
                brandList.add(cVipType.getDescription());
                if (olaiBrand.equals(cVipType.getDescription())) {
                    cStoreId = cclientVip.getcStoreId();
                }
                haveJNByOrOali = true;
            }

            // 有无品牌卡
            /**
             * 1.
             * 2. 有卡
             *      1. 直营卡
             *
             *      2. 经销卡
             *
             *
             *
             *
             */
        }
        return null;
    }


    @Override
    public Boolean validThemeMatch(String unionId, String themeMatchId) {
        BThemeMatch bThemeMatch = Preconditions.checkNotNull(bThemeMatchMapper.selectByPrimaryKey(themeMatchId), "搭配已失效");
        Preconditions.checkArgument(bThemeMatch.getStatus().equals(ThemeMatchStatusEnum.ON.getCode()),"搭配已失效");
        BThemeActivity bThemeActivity = Preconditions.checkNotNull(bThemeActivityMapper.selectByPrimaryKey(bThemeMatch.getActivityId()), "活动已结束");
        Preconditions.checkArgument(bThemeActivity.getStatus().equals(ThemeActivityStatusEnum.ON.getCode()),"活动已结束");

        return true;
    }

    /**
     * 根据活动id获取搭配数量
     * @param activityId
     * @return
     */
    @Override
    public Map<String,Long> getCountByActivityId(String activityId){
        BThemeMatch search = new BThemeMatch();
        search.setActivityId(activityId);
        List<BThemeMatch> bThemeMatches = getThemeMatchList(search);
        Map<String,Long> map = new HashMap<>();
        map.put("up",bThemeMatches.stream().filter(e -> e.getStatus().intValue() == 0).count());
        map.put("down",bThemeMatches.stream().filter(e -> e.getStatus().intValue() == 1).count());

        return map;
    }
}
