package com.jnby.module.marketing.match.fashionerMatch.service;

import com.jnby.application.admin.dto.request.MatchUpOrDownReq;
import com.jnby.application.admin.dto.response.AddMatchConditionResp;
import com.jnby.application.admin.dto.response.FashionerMatchListResp;
import com.jnby.application.minapp.dto.request.*;
import com.jnby.application.minapp.dto.response.CalcPriceByProductCodeResp;
import com.jnby.application.minapp.dto.response.GetYdFashionerInfoAndThemeResp;
import com.jnby.application.openapi.request.CreateFashionerMatchReq;
import com.jnby.common.Page;
import com.jnby.infrastructure.box.model.BFashionerMatch;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jnby.infrastructure.box.model.BFashionerMatchLabel;
import com.jnby.module.marketing.match.fashionerMatch.entity.NoYearMatchEntity;
import com.jnby.module.marketing.styling.entity.StylingEntity;
import com.jnbyframework.boot.common.system.vo.LoginUser;
import org.springcenter.material.api.dto.CreateLookReq;
import org.springcenter.material.api.dto.StylingListContext;
import org.springcenter.material.api.dto.StylingReq;
import org.springcenter.material.api.dto.SysLookTypesResp;

import java.util.Date;
import java.util.List;

/**
 * @auther wangchun
 * @create 2021-10-14 09:17:44
 * @describe 服务类
 */
public interface IBFashionerMatchService extends IService<BFashionerMatch> {

    /**
     * @Description: 保存有搭
     * @Author: brian
     * @Date: 2021/10/18 17:38
     * @params: [req, userId]
     * @return: BFashionerMatch
     */
    BFashionerMatch saveFashionerMatch(CreateFashionerMatchReq req,String userId);

    public  void auditBox(Integer lookId, LoginUser loginUser, List<SysLookTypesResp> lookTypesResps, String bizId, String updateBy);


//    /**
//     * 插入es
//     * @param ydEntity
//     * @param esIndexLook
//     */
//    public void insertLookToEs(LookEntity ydEntity, String esIndexLook,Integer dumpId);
//
//
//    public YdEntity packetYdLook(BFashionerMatch fashionerMatch, List<BFashionerMatchLabel> matchLabels, Integer lookId);
//
//    public List<SysLookLabel> transferName(List<SysLookLabel> galleryList);


    /**
     * @Description: 组装列表返回数据
     * @Author: brian
     * @Date: 2021/10/18 17:38
     * @params: [list]
     * @return: java.util.List<com.jnby.application.admin.dto.response.FashionerMatchListResp>
     */
    List<FashionerMatchListResp> setMatchList(List<BFashionerMatch> list);


    /**
     * @Description: 根据id获取回显数据
     * @Author: brian
     * @Date: 2021/10/25 16:37
     * @params: [resp, id]
     * @return: void
     */
    void reloadPageParam(AddMatchConditionResp resp, String id);

    /**
     * @Description: 查询缺少年份数据的搭配
     * @Author: brian
     * @Date: 2021/11/8 17:13
     * @params: []
     * @return: com.jnby.module.marketing.match.fashionerMatch.entity.NoYearMatchEntity
     */
    List<NoYearMatchEntity> selectNoYearMatch();


    /**
     * 获取数据
     * @param look
     * @return
     */
    public CreateLookReq getFashionerData(CreateLookReq look);

    /**
     *获取搭配详情 搭配师信息
     * @param requestData
     * @return
     */
    GetYdFashionerInfoAndThemeResp getYdFashionerInfoAndTheme(GetYdFashionerInfoAndThemeReq requestData);

    /**
     * 搭配详情的商品信息
     * @param requestData
     * @return
     */
    GetYdDetailsResp getYdDetails(GetYdDetailsReq requestData);

    /**
     * 计算价格
     * @param requestData
     * @return
     */
    CalcPriceByProductCodeResp calcPriceByProductCode(CalcPriceByProductCodeReq requestData);

    /**
     * 收藏或者取消收藏
     * @param requestData
     * @return
     */
    Long editFavor(EditFavorReq requestData);

    /**
     * 根据ids查询
     * @param ids
     * @return
     */
    List<StylingEntity> getFashionerMatchByIds(List<String> ids,Integer sortType);

    /**
     * 查询有搭数据
     * @param context
     * @return
     */
    List<StylingEntity> getBfashionerMatchList(StylingListContext context);

    /**
     * 导购端查询有搭
     * @param stylingReq
     * @return
     */
    List<StylingEntity> getBfashionerMatchListForSales(StylingReq stylingReq);

    /**
     * 根据ids查询
     * @param bashionerMatchIds
     * @return
     */
    List<BFashionerMatchLabel> getFashionerMatchLabelByMatchIds(List<String> bashionerMatchIds);

    /**
     * 收藏列表
     * @param requestData
     * @param page
     * @return
     */
    List<BFashionerMatch> favorList(FavorListReq requestData, Page page);

//    /**
//     * 批量更新es上下架状态
//     * @param ids
//     * @param type
//     */
//    void updateEsByBFashionerMatchId(List<String> ids, Integer type);

//    /**
//     * 修复导入错误数据
//     * @param e
//     * @param userId
//     */
//    void fixImportErrorData(CreateFashionerMatchReq e, String userId);

    List<BFashionerMatch> selectThemeIdIsNotNullByEndDate(Date endDate);


    List<BFashionerMatch> getFashionerMatchByListIds(List<String> ids);

    /**
     * 每十分钟
     */
    void updateFashionerMatchStatusJob();

    void upOrDown(MatchUpOrDownReq requestData);
}
