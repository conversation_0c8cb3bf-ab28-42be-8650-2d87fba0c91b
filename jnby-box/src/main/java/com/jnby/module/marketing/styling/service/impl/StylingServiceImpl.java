package com.jnby.module.marketing.styling.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.application.admin.dto.request.*;
import com.jnby.application.admin.dto.request.CreateLookReq;
import com.jnby.application.admin.dto.response.BrandConfig;
import com.jnby.application.admin.dto.response.WaitPerfcetListDetailResp;
import com.jnby.application.admin.dto.response.WaitPerfectListResp;
import com.jnby.application.corp.dto.request.CreateSalesStylingReq;
import com.jnby.application.corp.dto.response.BatchGetIsCreateOrUpdateResp;
import com.jnby.application.openapi.request.CreateFashionerMatchReq;
import com.jnby.application.openapi.request.CreateMatchReq;
import com.jnby.application.openapi.request.SavePosExcelReq;
import com.jnby.common.*;
import com.jnby.common.enums.DelEnum;
import com.jnby.common.leaf.baidu.fsg.uid.utils.DateUtils;
import com.jnby.common.util.FileParseUtil;
import com.jnby.common.util.HttpUtil;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.base.entity.ProductAttrEntity;
import com.jnby.base.entity.ReturnProductEntity;
import com.jnby.base.service.IAttrService;
import com.jnby.base.service.IFileParseService;
import com.jnby.common.aspect.annotation.JRepeat;
import com.jnby.common.cache.RedisKeysEnum;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.common.enums.*;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.common.leaf.support.IdWorkLeaf;
import com.jnby.common.util.EsUtil;
import com.jnby.common.util.QiniuUtil;
import com.jnby.infrastructure.bojun.model.BoxMProduct;
import com.jnby.infrastructure.box.mapper.*;
import com.jnby.infrastructure.box.model.*;
import com.jnby.module.marketing.match.fashionerMatch.entity.NoYearMatchEntity;
import com.jnby.module.marketing.match.fashionerMatch.service.IBFashionerMatchService;
import com.jnby.module.marketing.styling.entity.StylingEntity;
import com.jnby.module.marketing.styling.entity.StylingProductEntity;
import com.jnby.module.marketing.styling.repository.IStylingRepository;
import com.jnby.module.marketing.styling.service.IStylingService;
import com.jnby.module.order.context.CalcPriceContext;
import com.jnby.module.order.entity.UserBoxSkc;
import com.jnby.module.order.enums.CalcPriceChannelEnum;
import com.jnby.module.order.enums.CalcPriceIfUseEnum;
import com.jnby.module.order.enums.CalcPriceTypeEnum;
import com.jnby.module.order.repository.IBoxRepository;
import com.jnby.module.order.service.ICalcPriceService;
import com.jnby.module.product.entity.GoodSpuEntity;
import com.jnby.module.product.entity.SpuStockEntity;
import com.jnby.module.product.service.IProductService;
import com.jnby.module.product.service.IStockService;
import org.springcenter.product.api.IProductApi;
import org.springcenter.product.api.dto.ProductSkcResp;
import org.springcenter.product.api.dto.QueryGoodsReq;
import com.jnbyframework.boot.api.ISysBaseAPI;
import com.jnbyframework.boot.common.system.vo.SysCategoryModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springcenter.material.api.IMaterialApi;
import org.springcenter.material.api.dto.*;
import org.springcenter.material.api.entity.CreateLookContext;
import org.springcenter.material.api.enums.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/20
 */
@Service
@Slf4j
@RefreshScope
public class StylingServiceImpl implements IStylingService{

    @Autowired
    IStylingRepository iStylingRepository;

    @Autowired
    IAttrService attrService;

    @Autowired
    IProductService productService;

    @Autowired
    BoxReturnDetailsMapper boxReturnDetailsMapper;

    @Autowired
    BStoreStylingRelMapper bStoreStylingRelMapper;

    @Autowired
    private ICalcPriceService calcPriceService;

    @Autowired
    private IStockService stockService;

    @Autowired
    private IBoxRepository iBoxRepository;

    @Autowired
    private RedisPoolUtil redisPoolUtil;

    /**
     * 需要指定使用哪一个事务管理器
     */
    @Autowired
    @Qualifier("boxTransactionTemplate")
    private TransactionTemplate template;

    @Autowired
    private IdLeafService idLeaf;

    @Autowired
    private IBFashionerMatchService ibFashionerMatchService;


    @Autowired
    private FashionerMapper fashionerMapper;

    @Value("${wx.brand.commonConfigs}")
    private String brandWeIdConfig;

    @Value("${import.excel.perfix.url}")
    private String perfixUploadImgPath;


    @Value("${import.pos.url}")
    private String importPosUrl;


    @Resource
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    private IFileParseService fileParseService;

    @Autowired
    private TagMapper tagMapper;


    @Autowired
    private BFashionerMatchMapper bFashionerMatchMapper;

    @Autowired
    private BFashionerMatchLabelMapper bFashionerMatchLabelMapper;


    @Autowired
    private IMaterialApi iMaterialApi;

    @Autowired
    private IProductApi iProductApi;

    @Autowired
    private StylingBaseMapper stylingBaseMapper;

    @Autowired
    private StylingDetailMapper stylingDetailMapper;


    @Override
    public List<StylingEntity> stylingList(Page page, StylingListContext context){
        List<String> tagIdsArray = new ArrayList<>();
        if(StringUtils.isNotBlank(context.getSearch())){
            //模糊查询标签
            List<Tag> list = tagMapper.selectLikeName(context.getSearch());
            if(CollectionUtils.isNotEmpty(list)){
                List<String> tagIds = list.stream().map(r -> r.getId().toString()).collect(Collectors.toList());
                // 再bfashionermatchlabel中查询
                tagIdsArray.addAll(tagIds);
            }
        }
        List<String> labelIds = context.getLabelIds();
        if(CollectionUtils.isNotEmpty(labelIds)){
            labelIds.addAll(tagIdsArray);
        }else{
            labelIds = new ArrayList<>();
            labelIds.addAll(tagIdsArray);
        }
        context.setLabelIds(labelIds);
        // 2022-06-16  确定了 先有搭去查询  通过es进行查询
        List<StylingEntityDto> stylingEntities = getBfashionerMatchListByEs(context,page);
        List<StylingEntity> stylingEntitiesResult = new ArrayList<>();
        for (StylingEntityDto stylingEntity : stylingEntities) {
            StylingEntity stylingEntity1 = new StylingEntity();
            BeanUtils.copyProperties(stylingEntity,stylingEntity1);
            stylingEntitiesResult.add(stylingEntity1);
        }

        if(CollectionUtils.isNotEmpty(stylingEntities)){
            List<String> bashionerMatchIds = stylingEntities.stream().map(r -> r.getFashionerMatchId()).collect(Collectors.toList());
            if(context.getScreen() == 0){
                stylingEntitiesResult = ibFashionerMatchService.getFashionerMatchByIds(bashionerMatchIds,1);
            }else{
                stylingEntitiesResult = ibFashionerMatchService.getFashionerMatchByIds(bashionerMatchIds,2);
            }
        }

//        List<StylingEntity> stylingEntities = iStylingRepository.stylingList(context);
        // 查询look数
        buildProductImage(stylingEntitiesResult);
        return stylingEntitiesResult;
    }

    private List<StylingEntityDto> getBfashionerMatchListByEs(StylingListContext context, Page page) {
        CommonRequest<StylingListContext> commonRequest = new CommonRequest<>();
        context.setIsFilterOlai(1);
        commonRequest.setRequestData(context);
        commonRequest.setPage(page);
        ResponseResult<List<StylingEntityDto>> listResponseResult = iMaterialApi.miniAppStylingList(commonRequest);
        page.setCount(listResponseResult.getPage().getCount());
        return listResponseResult.getData();
    }

    private void buildProductImage(List<StylingEntity> stylingEntities){
        stylingEntities.forEach(e->{
            String sourceProductImagePath = Optional.ofNullable(e.getBackgroundImg())
                    .map(backImg -> JSONArray.parseArray(JSONObject.parseObject(backImg).get("imgList").toString()))
                    .map(imgList -> CollectionUtils.isEmpty(imgList) ? null : imgList.get(0).toString())
                    .orElse(null);
            e.setImgUrl(sourceProductImagePath);
        });
    }

    @Override
    public List<StylingProductEntity> stylingDetails(String stylingId) {
        return iStylingRepository.stylingDetail(stylingId);
    }

    @Override
    public List<StylingEntity> findThemeListByIds(List<String> ids) {
//        List<StylingEntity> stylingEntities = iStylingRepository.findThemeListByIds(ids);
        List<StylingEntity> stylingEntities = ibFashionerMatchService.getFashionerMatchByIds(ids,1);

        buildProductImage(stylingEntities);
//        stylingEntities.forEach(e->{
//            String sourceProductImagePath = Optional.ofNullable(e.getBackgroundImg())
//                    .map(backImg -> JSONArray.parseArray(JSONObject.parseObject(backImg).get("productList").toString()))
//                    .map(imgList -> imgList.getJSONObject(0).getString("productImage"))
//                    .orElse(null);
//            e.setImgUrl(sourceProductImagePath);
//        });
        return stylingEntities;
    }

    @Override
    @JRepeat(lockKey = "styling:add:cart", lockTime = 1000)
    public StylingBoxCart addStylingCart(StylingProductReq req) {
        // 将商品id改成对应sku的商品id home商品的不同sku的商品id都不一样
        List<BoxMProduct> skuList = productService.selectGoodsListBySkuIds(Lists.newArrayList(req.getSkuId()));
        if (CollectionUtils.isEmpty(skuList)) {
            throw new BoxException(ErrorConstants.PARAMS_ERROR.getCode(), "未找到当前sku的信息" + req.getSkuId());
        }
        req.setProductId(skuList.get(0).getId());

        //查询是否添加过
        StylingBoxCart cart = new StylingBoxCart();
        cart.setUnionId(req.getUnionId());
        cart.setSysUserId(req.getSysUserId());
        cart.setProductId(req.getProductId());
        cart.setSkuId(req.getSkuId());
        cart.setStatus(StylingBoxCartStatus.NORMAL.getCode());
        List<StylingBoxCart> stylingBoxCarts = iStylingRepository.selectStylingCartListBySelective(cart);
        if (!stylingBoxCarts.isEmpty()){
            throw new BoxException(ErrorConstants.PARAMS_ERROR.getCode(), "你已经添加款号为" + req.getProductCode() + "的商品,无需重复添加");
        }
        BeanUtils.copyProperties(req, cart);
        //搭配购物车打标签
        if (cart.getProductId() != null && cart.getColorNo() != null){
            UserBoxSkc userBoxSkc = new UserBoxSkc();
            userBoxSkc.setUnionId(req.getUnionId());
            userBoxSkc.setOutId(req.getProductId().toString());
            userBoxSkc.setProductColorNo(req.getColorNo());
            StylingBoxCart.InnerRemark innerRemark = matchUserBoxSkc(userBoxSkc);
            cart.setRemark(innerRemark.toJson());
        }
        iStylingRepository.addStyleIngCart(cart);

        return cart;
    }

    @Override
    public void addStylingCartForBoxReset(StylingProductReq req) {
        //查询是否添加过
        StylingBoxCart cart = new StylingBoxCart();
        cart.setUnionId(req.getUnionId());
        cart.setSysUserId(req.getSysUserId());
        cart.setProductId(req.getProductId());
        cart.setSkuId(req.getSkuId());
        cart.setStatus(StylingBoxCartStatus.NORMAL.getCode());
        cart.setIsEb(req.getIsEb());
        List<StylingBoxCart> stylingBoxCarts = iStylingRepository.selectStylingCartListBySelective(cart);
        if (!stylingBoxCarts.isEmpty()){
            throw new BoxException(ErrorConstants.PARAMS_ERROR.getCode(), "你已经添加款号为" + req.getProductCode() + "的商品,无需重复添加");
        }
        BeanUtils.copyProperties(req, cart);
        //搭配购物车打标签
        if (cart.getProductId() != null && cart.getColorNo() != null){
            UserBoxSkc userBoxSkc = new UserBoxSkc();
            userBoxSkc.setUnionId(req.getUnionId());
            userBoxSkc.setOutId(req.getProductId().toString());
            userBoxSkc.setProductColorNo(req.getColorNo());
            StylingBoxCart.InnerRemark innerRemark = matchUserBoxSkc(userBoxSkc);
            cart.setRemark(innerRemark.toJson());
        }
        iStylingRepository.addStyleIngCart(cart);
    }

    @Override
    public List<StylingDetail> updateStylingCart(StylingProductReq req) {
        // 将商品id改成对应sku的商品id home商品的不同sku的商品id都不一样
        List<BoxMProduct> skuList = productService.selectGoodsListBySkuIds(Lists.newArrayList(req.getSkuId()));
        if (CollectionUtils.isEmpty(skuList)) {
            throw new BoxException(ErrorConstants.PARAMS_ERROR.getCode(), "未找到当前sku的信息" + req.getSkuId());
        }
        req.setProductId(skuList.get(0).getId());

        StylingBoxCart cart = new StylingBoxCart();
        cart.setUnionId(req.getUnionId());
        cart.setSysUserId(req.getSysUserId());
        cart.setProductId(req.getProductId());
        cart.setSkuId(req.getSkuId());
        cart.setStatus(StylingBoxCartStatus.NORMAL.getCode());
        List<StylingBoxCart> stylingBoxCarts = iStylingRepository.selectStylingCartListBySelective(cart);
        if (!stylingBoxCarts.isEmpty()){
            throw new BoxException(ErrorConstants.PARAMS_ERROR.getCode(), "你已经添加款号为" + req.getProductCode() + "的商品,无需重复添加");
        }
        BeanUtils.copyProperties(req, cart);
        //搭配购物车打标签
        if (cart.getProductId() != null && cart.getColorNo() != null){
            UserBoxSkc userBoxSkc = new UserBoxSkc();
            userBoxSkc.setUnionId(req.getUnionId());
            userBoxSkc.setOutId(req.getProductId().toString());
            userBoxSkc.setProductColorNo(req.getColorNo());
            StylingBoxCart.InnerRemark innerRemark = matchUserBoxSkc(userBoxSkc);
            cart.setRemark(innerRemark.toJson());
        }
        AtomicReference<List<StylingDetail>> result = new AtomicReference<List<StylingDetail>>();
        //updateLook
        template.execute(action->{
             result.set(updateLookDetail(req.getList()));
            iStylingRepository.updateStylingCart(cart);
            return action;
        });
        return result.get();
    }

    private List<StylingDetail> updateLookDetail(List<CreateStylingDetailReq> list) {
        List<StylingDetail> insertList  = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)){
            return insertList;
        }


        for (CreateStylingDetailReq createStylingDetailReq : list) {
            String id = createStylingDetailReq.getStylingBaseId();
            IdWorkLeaf leaf = IdWorkLeaf.getInstance();
            AtomicInteger sorted = new AtomicInteger(1);
            StylingDetail detail = new StylingDetail();
            detail.setCreateDate(new Date());
            detail.setUpdateDate(new Date());
            BeanUtils.copyProperties(createStylingDetailReq, detail);
            detail.setId(leaf.getId());
            if (id != null){
                detail.setStylingBaseId(id);
            }
//            detail.setSorted(sorted.getAndIncrement());
            insertList.add(detail);

            StylingDetail param = new StylingDetail();
            param.setId(createStylingDetailReq.getId());
            List<StylingDetail> stylingDetails = iStylingRepository.selectStylingDetailBySelective(param);
            if(CollectionUtils.isNotEmpty(stylingDetails)){
                List<String> detailIds = stylingDetails.stream().map(item -> item.getId()).collect(Collectors.toList());
                iStylingRepository.deleteStylingDetail(detailIds);
            }

        }
        //封装获取   来源    品牌    年份   季节     波段    商品大类   商品小类
        batchGetProductInfo(insertList);

        if(CollectionUtils.isNotEmpty(insertList)){
            iStylingRepository.batchAddStylingDetail(insertList);
        }
        return insertList;
    }

    @Override
    public void batchAddStylingCart(List<StylingProductReq> reqs, String sysUserId) {
        Set<Long> productIds = new HashSet<>();
        for (StylingProductReq req : reqs) {
            Long skuId = req.getSkuId();
            productIds.add(skuId);
        }
        if(productIds.size() != reqs.size()){
            throw new BoxException(ErrorConstants.PARAMS_ERROR.getCode(), "本次添加的商品有相同款号的商品，请重新进行选择!");
        }

        StylingBoxCart cart = new StylingBoxCart();
        cart.setUnionId(reqs.get(0).getUnionId());
        cart.setSysUserId(sysUserId);
        cart.setStatus(StylingBoxCartStatus.NORMAL.getCode());
        //首先从购物车表中查询出这个用户在这个搭配师下添加到购物车里处于搭配的正常状态下的所有商品
        // 新建逻辑排除购物车skuId为空的商品 防止校验出问题
        List<StylingBoxCart> stylingBoxCarts = iStylingRepository.selectStylingCartListBySelective(cart).stream().filter(e -> ObjectUtils.isNotEmpty(e.getSkuId())).collect(Collectors.toList());
        //然后在这里面去查询是否有添加过的商品
        //有则已添加，不予添加，无则添加到购物车表里去
        if (!stylingBoxCarts.isEmpty()){
            //求差集
            //根据商品唯一的productId或者skuId来判断是否添加过购物车
            reqs = reqs.stream().filter(item -> {
                if (item.getSkuId() == null){
                    //相同的productId下购物车里的数据源和请求的商品列表里只能有一个相等匹配上说明添加过，找到第一个findFirst()进行判断即可
                    Long productId = stylingBoxCarts.stream().filter(sb -> sb.getProductId().equals(item.getProductId()))
                            .map(sbo -> sbo.getProductId()).findFirst().orElse(null);
                    if (productId == null){
                        return true;
                    }
                    return false;
                }
                Long skuId = stylingBoxCarts.stream().filter(sb -> sb.getSkuId().equals(item.getSkuId())).map(sbo -> sbo.getSkuId()).findFirst().orElse(null);
                if (skuId == null){
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
        }

        //返回异常信息，应为请选中商品后再进行添加或你已添加过相同款式的商品，无需重复添加

        if (reqs.size() == 0){
            throw new BoxException(ErrorConstants.PARAMS_ERROR.getCode(), "你已经添加相同款号的商品,无需重复添加");
        }

        //批量插入
        IdWorkLeaf leaf = IdWorkLeaf.getInstance();
        List<StylingBoxCart> list = reqs.stream().map(item -> {
            StylingBoxCart stylingBoxCart = new StylingBoxCart();
            stylingBoxCart.setCreateTime(new Date());
            stylingBoxCart.setUpdateTime(new Date());
            BeanUtils.copyProperties(item, stylingBoxCart);
            stylingBoxCart.setId(leaf.getId());
            stylingBoxCart.setSysUserId(sysUserId);
            stylingBoxCart.setStatus(StylingBoxCartStatus.NORMAL.getCode());
            stylingBoxCart.setIsEb(item.getIsEb());
            //搭配购物车打标签
            if (stylingBoxCart.getProductId() != null && stylingBoxCart.getColorNo() != null){
                UserBoxSkc userBoxSkc = new UserBoxSkc();
                userBoxSkc.setUnionId(stylingBoxCart.getUnionId());
                userBoxSkc.setOutId(stylingBoxCart.getProductId().toString());
                userBoxSkc.setProductColorNo(stylingBoxCart.getColorNo());
                StylingBoxCart.InnerRemark innerRemark = matchUserBoxSkc(userBoxSkc);
                stylingBoxCart.setRemark(innerRemark.toJson());
            }
            return stylingBoxCart;
        }).collect(Collectors.toList());
        iStylingRepository.batchAddStylingCart(list);
    }

    @Override
    public void batchAddStylingCartNoCheck(List<StylingProductReq> reqs, String sysUserId) {
        //批量插入
        IdWorkLeaf leaf = IdWorkLeaf.getInstance();
        List<StylingBoxCart> list = reqs.stream().map(item -> {
            StylingBoxCart stylingBoxCart = new StylingBoxCart();
            stylingBoxCart.setCreateTime(new Date());
            stylingBoxCart.setUpdateTime(new Date());
            BeanUtils.copyProperties(item, stylingBoxCart);
            stylingBoxCart.setId(leaf.getId());
            stylingBoxCart.setSysUserId(sysUserId);
            stylingBoxCart.setStatus(StylingBoxCartStatus.NORMAL.getCode());
            stylingBoxCart.setIsEb(item.getIsEb());
            //搭配购物车打标签
            if (stylingBoxCart.getProductId() != null && stylingBoxCart.getColorNo() != null){
                UserBoxSkc userBoxSkc = new UserBoxSkc();
                userBoxSkc.setUnionId(stylingBoxCart.getUnionId());
                userBoxSkc.setOutId(stylingBoxCart.getProductId().toString());
                userBoxSkc.setProductColorNo(stylingBoxCart.getColorNo());
                StylingBoxCart.InnerRemark innerRemark = matchUserBoxSkc(userBoxSkc);
                stylingBoxCart.setRemark(innerRemark.toJson());
            }
            return stylingBoxCart;
        }).collect(Collectors.toList());
        iStylingRepository.batchAddStylingCart(list);
    }

    @Override
    public void deleteStylingCartById(String id) {
        iStylingRepository.updateStylingCartById(id, StylingBoxCartStatus.FORBID.getCode());
    }

    @Override
    public List<StylingBoxCart> stylingBoxCartList(String sysUserId, String unionId) {
        StylingBoxCart cart = new StylingBoxCart();
        cart.setUnionId(unionId);
        cart.setSysUserId(sysUserId);
        cart.setStatus(StylingBoxCartStatus.NORMAL.getCode());
        return iStylingRepository.selectStylingCartListBySelective(cart);
    }

    @Override
    public void createLook(CreateLookReq createLookReq) {
        template.execute(action -> {
            String id = createStylingBase(createLookReq.getStylingBase());
            batchCreateStylingDetail(createLookReq.getStylingDetails(), id);
            return action;
        });
    }

    @Override
    public void createSalesStyling(CreateSalesStylingReq createSalesStylingReq) {

        List<StylingBoxCart> list = iStylingRepository.findByStylingBoxCartIds(createSalesStylingReq.getStylingBoxCartIds());
        //base create
        CreateStylingBaseReq baseReq = new CreateStylingBaseReq();
        baseReq.setCreateBy(createSalesStylingReq.getStylingBase().getCreateBy());
        baseReq.setUnionId(createSalesStylingReq.getStylingBase().getUnionId());
        // 导购默认创建非正常
//        baseReq.setStatus(3L);

        List<String> stylingBoxCartIds = new ArrayList<>();
        List<CreateStylingDetailReq> stylingDetailReqs = list.stream().map(item -> {
            CreateStylingDetailReq detailReq = new CreateStylingDetailReq();
            detailReq.setColorName(item.getColorName());
            detailReq.setColorNo(item.getColorNo());
            detailReq.setImgUrl(item.getImgUrl());
            detailReq.setProductId(item.getProductId());
            detailReq.setProductCode(item.getProductCode());
            detailReq.setSkuId(item.getSkuId().toString());
            detailReq.setSizeName(item.getSizeName());
            detailReq.setSizeNo(item.getSizeNo());
            stylingBoxCartIds.add(item.getId());
            return detailReq;
        }).collect(Collectors.toList());
        template.execute(action -> {
//            baseReq.setSource(1L);
            String id = createStylingBase(baseReq);
            batchCreateStylingDetail(stylingDetailReqs, id);
            return action;
        });

    }

    public static void main(String[] args) {
//        StylingBoxCart.InnerRemark remark = new StylingBoxCart().new InnerRemark();
//        remark.setHasCreateStyling(2);
//        System.out.println(remark.toJson());
        //{"hasCreateStyling":2}
        AtomicInteger at = new AtomicInteger(1);
        for (int i = 0; i<10; i++){
            System.out.println(at.getAndIncrement());
        }
    }
    private StylingBoxCart.InnerRemark matchUserBoxSkc(UserBoxSkc userBoxSkc){
        List<BoxDetails> list = iBoxRepository.selectListBoxDetailBySkc(userBoxSkc);
        StylingBoxCart.InnerRemark remark = new StylingBoxCart().new InnerRemark();
        if (list.isEmpty()) {
            //暂无搭配
            remark.setHasCreateStyling(0);
            return remark;
        }

        //获取已经支付的记录
        boolean isPay = list.stream().filter(item -> item.getStatus() >= 3L).collect(Collectors.toList()).isEmpty();
        if (!isPay) {//已支付
            remark.setHasCreateStyling(1);
            return remark;
        }
        //已搭配，未支付
        remark.setHasCreateStyling(2);
        return remark;
    }

    @Override
    public String createStylingBase(CreateStylingBaseReq req) {
        StylingBaseWithBLOBs base = new StylingBaseWithBLOBs();
        BeanUtils.copyProperties(req, base);
        //设置信息
        String createBy = req.getCreateBy();
        if(StringUtils.isNotBlank(createBy)){
            Fashioner byUserId = fashionerMapper.findByUserId(createBy);
            if(byUserId != null){
                if(byUserId.getIsSales() == 0L){
                    base.setSource(2L);
                }else if(byUserId.getIsSales() == 1L){
                    base.setSource(1L);
                }else{
                    base.setSource(3L);
                }
            }else{
                base.setSource(3L);
            }
        }else{
            base.setSource(3L);
        }
        iStylingRepository.addStylingBase(base);
        return base.getId();
    }

    @Override
    public void deleteStylingBase(String id) {
        iStylingRepository.deleteStylingBase(id);
    }

    @Override
//    @JRepeat(lockKey = "styling:create:detail", lockTime = 1000)
    public String createStylingDetail(CreateStylingDetailReq stylingDetailReq) {
        StylingDetail detail = new StylingDetail();
        //查询是否存在相同的Look
        detail.setStylingBaseId(stylingDetailReq.getStylingBaseId());
        detail.setProductId(stylingDetailReq.getProductId());
        detail.setSkuId(stylingDetailReq.getSkuId());
        List<StylingDetail> details = iStylingRepository.selectStylingDetailBySelective(detail);
        if (!details.isEmpty()){
            throw new BoxException(ErrorConstants.PARAMS_ERROR.getCode(), "你已经添加相同SKU的商品,无需重复添加");
        }

        BeanUtils.copyProperties(stylingDetailReq, detail);
        iStylingRepository.addStylingDetail(detail);
        return detail.getId();
    }

    @Override
    public void batchCreateStylingDetail(List<CreateStylingDetailReq> list, String id) {
        IdWorkLeaf leaf = IdWorkLeaf.getInstance();
        AtomicInteger sorted = new AtomicInteger(1);
        List<StylingDetail> details = list.stream().map(item -> {
            StylingDetail detail = new StylingDetail();
            detail.setCreateDate(new Date());
            detail.setUpdateDate(new Date());
            BeanUtils.copyProperties(item, detail);
            detail.setId(leaf.getId());
            if (id != null){
                detail.setStylingBaseId(id);
            }
            detail.setSorted(sorted.getAndIncrement());
            return detail;
        }).collect(Collectors.toList());
        //封装获取   来源    品牌    年份   季节     波段    商品大类   商品小类
        batchGetProductInfo(details);

        template.execute(action -> {
            if (id != null){
                List<StylingDetail> haveDetails = getHaveStylingDetail(id);
                if (!haveDetails.isEmpty()){
                    List<String> detailIds = haveDetails.stream().map(item -> item.getId()).collect(Collectors.toList());
                    iStylingRepository.deleteStylingDetail(detailIds);
                }
            }
            iStylingRepository.batchAddStylingDetail(details);
            return action;
        });
    }

    // 根据产品款号和颜色获取 产品信息
    private void batchGetProductInfo(List<StylingDetail> details) {
        List<String> resultArray = new ArrayList<>();
        for (StylingDetail detail : details) {
            String productCode = detail.getProductCode();
            String colorNo = detail.getColorNo();
            resultArray.add(productCode+colorNo);
        }
        CommonRequest<QueryGoodsReq> request = new CommonRequest<>();
        QueryGoodsReq queryGoodsReq = new QueryGoodsReq();
        queryGoodsReq.setSkcIds(resultArray);
        queryGoodsReq.setSorted(2);
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(1000);
        request.setRequestData(queryGoodsReq);
        request.setPage(page);
        ResponseResult<List<ProductSkcResp>> listResponseResult = iProductApi.queryGoodSkc(request);
        List<ProductSkcResp> list = listResponseResult.getData();
        if(CollectionUtils.isNotEmpty(list)){
            //根据sku分组
            Map<String, List<ProductSkcResp>> collect = list.stream().collect(Collectors.groupingBy(ProductSkcResp::getId));
            for (String spuId : collect.keySet()) {
                // 填充数据
                List<ProductSkcResp> goodsSkcResps = collect.get(spuId);
                if(CollectionUtils.isNotEmpty(goodsSkcResps)){
                    inside: for (ProductSkcResp goodsSkcResp : goodsSkcResps) {
                        int index = resultArray.indexOf(goodsSkcResp.getName() + goodsSkcResp.getColorno());
                        if(index != -1){
                            StylingDetail stylingDetail = details.get(index);
                            //封装获取  品牌    年份   季节     波段    商品大类   商品小类

                            stylingDetail.setArcBrandId(goodsSkcResp.getC_arcbrand_id());
                            stylingDetail.setArcBrandName(goodsSkcResp.getBrand());
                            stylingDetail.setYears(goodsSkcResp.getYear());
                            stylingDetail.setSeasonId(goodsSkcResp.getSmall_season_id());
                            stylingDetail.setSeasonName(goodsSkcResp.getSmall_season());
                            stylingDetail.setBand(goodsSkcResp.getM_band());
                            stylingDetail.setTempBand(goodsSkcResp.getM_band());
                            stylingDetail.setBigCateId(goodsSkcResp.getM_big_category_id());
                            stylingDetail.setBigCateName(goodsSkcResp.getM_big_category());
                            stylingDetail.setSmallCateId(goodsSkcResp.getM_small_category_id());
                            stylingDetail.setSmallCateName(goodsSkcResp.getM_small_category());
                        }
                    }
                }
            }
        }
    }

    private List<StylingDetail> getHaveStylingDetail(String baseId){
        StylingDetail detail = new StylingDetail();
        //查询是否存在相同的Look
        detail.setStylingBaseId(baseId);
        List<StylingDetail> details = iStylingRepository.selectStylingDetailBySelective(detail);
        return details;
    }

    @Override
    public void deleteStylingDetailById(String id) {
        iStylingRepository.deleteStylingDetail(id);
    }

    @Override
    public List<StylingBaseDetail> queryStylingBaseDetailList(String outNo, Page page) {
        com.github.pagehelper.Page<StylingBaseDetail> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        iStylingRepository.selectStylingBaseDetailList(outNo);
        PageInfo<StylingBaseDetail> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        page.setPageNo(page.getPageNo());
        return pageInfo.getList();
    }

    @Override
    public void invalidBoxCart(String userId, String unionId,Long status) {
        iStylingRepository.invalidBoxCart(userId,unionId,status);
    }

    @Override
    public void invalidBoxCart(String unionId,Long status) {
        iStylingRepository.invalidBoxCart(null,unionId,status);
    }

    @Override
    public void initStylingCart(CustomerAskBox askBox, String userId) {
        // 查询购物车是否有商品
        List<StylingBoxCart> stylingBoxCarts = stylingBoxCartList(userId, askBox.getUnionid());
        if(stylingBoxCarts.size() > 0){
            return;
        }
        AttrValueExtends extend = attrService.findExtendsById(askBox.getThemeAttrId());
        Long themeId = extend.getThemeId();
        List<ThemeDetails> themeDetails = findThemeDetailByThemeId(new BigDecimal(themeId));
        List<StylingProductReq> stylingProductReqs = new ArrayList<>();
        // 插入数据
        for(ThemeDetails details : themeDetails){
            StylingProductReq stylingProductReq = new StylingProductReq();
            stylingProductReq.setUnionId(askBox.getUnionid());
            stylingProductReq.setPrice(details.getProductPrice());
            stylingProductReq.setProductId(new Long(details.getOutId()));
            stylingProductReq.setProductCode(details.getProductCode());
            stylingProductReq.setProductName(details.getProductName());
            stylingProductReq.setColorName(details.getProductColor());
            stylingProductReq.setColorNo(details.getProductColorNo());
            stylingProductReq.setImgUrl(details.getImgUrl());
            // 根据用户选择适配尺码
            JSONObject obj = Optional.ofNullable(askBox.getProductSize())
                    .map(jsonStr -> JSONObject.parseObject(jsonStr))
                    .orElse(null);
            String sizeName = details.getProductSize();
            stylingProductReq.setSkuId(new Long(details.getProductId()));
            stylingProductReq.setSkuCode(details.getSku());
            stylingProductReq.setSizeName(details.getProductSize());
            stylingProductReq.setChannel(StylingBoxCartChannelEnum.THEME.getCode());
            stylingProductReq.setOutId(themeId.toString());
            if (obj != null&& StringUtils.isNotBlank(details.getBigclass())) {
                String bigClass = details.getBigclass();
                if(("上装").equals(bigClass) || ("下装").equals(bigClass)){
                    if (("上装").equals(bigClass)) {
                        sizeName =  obj.getString("upper");
                    } else if (("下装").equals(bigClass)) {
                        sizeName =  obj.getString("lower");
                    }
                    List<GoodSpuEntity.Sku> skuList = productService.findSkuListByProductId(stylingProductReq.getProductId().toString());
                    for(GoodSpuEntity.Sku sku : skuList){
                        if(sku.getSize_name().equals(sizeName)){
                            stylingProductReq.setSkuId(new Long(sku.getId()));
                            stylingProductReq.setSkuCode(sku.getNo());
                            stylingProductReq.setSizeName(sku.getSize_name());
                            break;
                        }
                    }
                }
            }

            stylingProductReqs.add(stylingProductReq);
        }
        batchAddStylingCart(stylingProductReqs,userId);
    }


    @Override
    public CalcPriceContext calcUnitPrice(String stylingCartId) {
        StylingBoxCart stylingBoxCart = iStylingRepository.findCartById(stylingCartId);
        if(stylingBoxCart != null){
            // 组装参数
            CalcPriceContext.Params params = new CalcPriceContext.Params();
            params.setChannel(CalcPriceChannelEnum.BOX.getKeyWord());
            params.setType(CalcPriceTypeEnum.unitCalc.getKeyWord());
            params.setShopvip(CalcPriceIfUseEnum.NON.getKeyWord());
            params.setUseintegral(CalcPriceIfUseEnum.NON.getKeyWord());
            List<CalcPriceContext.VoucherItem> voucherItems = new ArrayList<>();

            List<CalcPriceContext.GoodsItem> goodsItems = new ArrayList<>();
            CalcPriceContext.GoodsItem goodsItem = new CalcPriceContext.GoodsItem();
            goodsItem.setRowno(1);
            goodsItem.setBarcode(stylingBoxCart.getSkuCode());
            goodsItem.setChange(CalcPriceIfUseEnum.NON.getKeyWord());
            goodsItem.setQty("1");
            goodsItem.setSkuid(stylingBoxCart.getSkuId().toString());
            goodsItems.add(goodsItem);
            // 调用价格计算
            return calcPriceService.calcPrice(stylingBoxCart.getUnionId(),goodsItems,voucherItems,params,null);
        }
        return null;
    }

    public List<ThemeDetails> findThemeDetailByThemeId(BigDecimal themeId){
        ThemeDetails themeDetails = new ThemeDetails();
        themeDetails.setThemeId(themeId);
        return iStylingRepository.findThemeDetailByPara(themeDetails);
    }

    @Override
    public List<ProductAttrEntity> getReturnTags(String unionId,String productId){
        // 获取当前商品小类
        GoodSpuEntity good = productService.findGoodByProductId(productId);
        // 获取同小类反馈标签
        List<String> tagJson = boxReturnDetailsMapper.selectReturnTags(unionId, good.getM_small_category());
        JSONArray tags = tagsSum(tagJson);
        List<ProductAttrEntity> tagList = findTagList(tags.toArray(new String[0]));
        return tagList;

    }

    @Override
    public List<ReturnProductEntity> selectProductByTag(String unionId,String tag, String productId) {
        GoodSpuEntity good = productService.findGoodByProductId(productId);
        try {
            List<ReturnProductEntity> returnProductEntities = boxReturnDetailsMapper.selectProductByTag(unionId, tag , good.getM_small_category());
            for (ReturnProductEntity re : returnProductEntities) {
                List<String> tagList = new ArrayList<>();
                tagList.add(re.getTagJson());
                String[] tags = tagsSum(tagList).toArray(new String[0]);
                re.setTags(tags);
                re.setPrice(new BigDecimal(re.getPrice()).setScale(2, RoundingMode.HALF_UP).toString());
            }
            return returnProductEntities;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public void updateStylingBaseContent(StylingBaseWithBLOBs stylingBase) {
        iStylingRepository.updateStylingBaseContent(stylingBase);
    }

    /***
     * @description : 标签解析汇总
     */
    private JSONArray tagsSum(List<String> list) {
        JSONArray tags = new JSONArray();
        // 将所有标签汇总
        for(String tagStr : list){
            JSONArray jsonArray = JSONArray.parseArray(tagStr);
            for(int i = 0;i<jsonArray.size();i++){
                JSONObject jo = JSONObject.parseObject(jsonArray.get(i).toString());
                JSONArray jts = JSONArray.parseArray(jo.getString("tags"));
                tags.addAll(jts);
            }
        }
        return tags;
    }

    /***
     * @description : 标签去重，记录个数，并返回数据
     * @param data
     */
    private List<ProductAttrEntity> findTagList(String [] data) {
        //用来记录运算后的数据
        Map<String, ProductAttrEntity> resultMap = new LinkedHashMap<>();
        int countIndex = 0;
        while (countIndex < data.length) {
            String s = data[countIndex];
            //如果这个值运算过 不再运算
            if (resultMap.get(s) != null) {
                countIndex++;
                continue;
            }
            ProductAttrEntity group = new ProductAttrEntity();
            group.setName(s);
            int count = 0;
            for (int i = countIndex; i < data.length; i++) {
                if (data[i].equals(s)) {
                    count++;
                }
            }
            group.setCount(count);
            resultMap.put(s, group);
            countIndex++;
        }
        // 排序
        ArrayList<ProductAttrEntity> records = new ArrayList<>(resultMap.values());
        List<ProductAttrEntity> collect = records.stream().sorted(Comparator.comparing(ProductAttrEntity::getCount).reversed()).collect(Collectors.toList());
        if(collect.size() <=6){
            return collect;
        }
        return collect.subList(0, 6);
    }


    @Override
    public void invalidStylingBase(String outNo) {
        iStylingRepository.invalidStylingBase(outNo);
    }

    @Override
    public void changeStoreStylingByListener(Long storeId, String productId) {
        BStoreStylingRel record= new BStoreStylingRel();
        record.setcStoreId(storeId);
        record.setStylingSpuList(productId);
        // 获取指定门店包含该商品的搭配
        List<BStoreStylingRel> bStoreStylingRelList = bStoreStylingRelMapper.selectByRecord(record);
        for(BStoreStylingRel styling : bStoreStylingRelList){
            List<Long> spuList = Optional.ofNullable(styling.getStylingSpuList())
                    .map(spuStr -> (Long[]) ConvertUtils.convert(spuStr.split(","), Long.class))
                    .map(longSpuArr -> Arrays.asList(longSpuArr))
                    .orElse(null);
            List<Long> storeList = Arrays.asList(storeId);
            // 获取当前搭配是否有库存
            List<SpuStockEntity> spuStorageList = stockService.getSpuStorages(spuList, storeList);
            double sum = spuStorageList.stream().mapToDouble(SpuStockEntity::getQty).sum();
            Long stylingQty = sum < 1? 0L:1L;
            if(!styling.getQty().equals(stylingQty)){
                BStoreStylingRel updateRecord = new BStoreStylingRel();
                updateRecord.setcStoreId(storeId);
                updateRecord.setStylingId(styling.getStylingId());
                updateRecord.setQty(stylingQty);
                bStoreStylingRelMapper.updateQty(updateRecord);
            }
        }
    }

    @Override
    public String initStyleOutNo(String userId, String unionId) {
        String outNo = RedisTemplateUtil.get(redisPoolUtil, RedisKeysEnum.STYLE.join(userId,unionId));
        if(StringUtils.isNotBlank(outNo)){
            return outNo;
        }
        // 生成id，缓存7天
        outNo = idLeaf.getId();
        RedisTemplateUtil.setex(redisPoolUtil, RedisKeysEnum.STYLE.join(userId,unionId),outNo,60*60*24*7);
        return outNo;
    }

    @Override
    public void syncStylingLabel2Look() {
        // 获取所有缺少年份季节的数据，刷新
        List<NoYearMatchEntity> noYearMatchEntityList = ibFashionerMatchService.selectNoYearMatch();
        List<BFashionerMatch> updateMatchList = noYearMatchEntityList.stream().map(e -> {
            BFashionerMatch bFashionerMatch = new BFashionerMatch();
            bFashionerMatch.setId(e.getMatchId());
            bFashionerMatch.setYear(Long.valueOf("20"+e.getYear()));
            bFashionerMatch.setSeason(e.getSeason().substring(0,1)+","+e.getSeason().substring(1,2));
            return bFashionerMatch;
        }).collect(Collectors.toList());
        ibFashionerMatchService.updateBatchById(updateMatchList);

    }

    @Override
    public void batchGuideAddStylingCart(List<StylingProductReq> reqs, String sysUserId) {
        StylingBoxCart cart = new StylingBoxCart();
        cart.setUnionId(reqs.get(0).getUnionId());
        cart.setSysUserId(sysUserId);
        cart.setStatus(StylingBoxCartStatus.NORMAL.getCode());
        //首先从购物车表中查询出这个用户在这个搭配师下添加到购物车里处于搭配的正常状态下的所有商品
        List<StylingBoxCart> stylingBoxCarts = iStylingRepository.selectStylingCartListBySelective(cart);
        //然后在这里面去查询是否有添加过的商品
        //有则已添加，不予添加，无则添加到购物车表里去
        if (!stylingBoxCarts.isEmpty()){
            //求差集
            //根据商品唯一的productId或者skuId来判断是否添加过购物车
            reqs = reqs.stream().filter(item -> {
                if (item.getSkuId() == null){
                    //相同的productId下购物车里的数据源和请求的商品列表里只能有一个相等匹配上说明添加过，找到第一个findFirst()进行判断即可
                    Long productId = stylingBoxCarts.stream().filter(sb -> sb.getProductId().equals(item.getProductId()))
                            .map(sbo -> sbo.getProductId()).findFirst().orElse(null);
                    if (productId == null){
                        return true;
                    }
                    return false;
                }
                Long skuId = stylingBoxCarts.stream().filter(sb -> sb.getSkuId().equals(item.getSkuId())).map(sbo -> sbo.getSkuId()).findFirst().orElse(null);
                if (skuId == null){
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
        }

        //返回异常信息，应为请选中商品后再进行添加或你已添加过相同款式的商品，无需重复添加
        if (reqs.size() == 0){
            return;
        }

        //批量插入
        IdWorkLeaf leaf = IdWorkLeaf.getInstance();
        List<StylingBoxCart> list = reqs.stream().map(item -> {
            StylingBoxCart stylingBoxCart = new StylingBoxCart();
            stylingBoxCart.setCreateTime(new Date());
            stylingBoxCart.setUpdateTime(new Date());
            BeanUtils.copyProperties(item, stylingBoxCart);
            stylingBoxCart.setId(leaf.getId());
            stylingBoxCart.setSysUserId(sysUserId);
            stylingBoxCart.setStatus(StylingBoxCartStatus.NORMAL.getCode());
            //搭配购物车打标签
            if (stylingBoxCart.getProductId() != null && stylingBoxCart.getColorNo() != null){
                UserBoxSkc userBoxSkc = new UserBoxSkc();
                userBoxSkc.setUnionId(stylingBoxCart.getUnionId());
                userBoxSkc.setOutId(stylingBoxCart.getProductId().toString());
                userBoxSkc.setProductColorNo(stylingBoxCart.getColorNo());
                StylingBoxCart.InnerRemark innerRemark = matchUserBoxSkc(userBoxSkc);
                stylingBoxCart.setRemark(innerRemark.toJson());
            }
            stylingBoxCart.setIsEb(item.getIsEb());
            return stylingBoxCart;
        }).collect(Collectors.toList());
        iStylingRepository.batchAddStylingCart(list);
    }

    @Override
    public void batchUpdateCartFPrice(List<StylingBoxCart> list) {
        template.execute(action -> {
            list.stream().forEach(e -> {
                iStylingRepository.updateStylingCart(e);
            });
            return action;
        });
    }

    @Override
    public void changeStylingCartSort(List<String> ids) {
        List<Integer> orders = new ArrayList<>();
        for (int i = 0 ; i < ids.size() ;  i ++) {
            orders.add(i);
        }
        iStylingRepository.updateBatchSort(orders,ids);
    }

    @Override
    public void updateStatusByOutNo(String outNo) {
        iStylingRepository.updateStylingBaseStatus(outNo,StylingBoxCartStatus.NORMAL.getCode());
    }

    @Override
    public void deleteStylingCartByIds(List<String> ids) {
        for (String id : ids) {
            deleteStylingCartById(id);
        }
    }

    @Override
    public void importExcel(String filePath,String keys,String importUser) {
        //
        List<String> titleList = new ArrayList<>();
        titleList.add("name");  // 名称
        titleList.add("introduction");  //简介
        titleList.add("source"); //来源
        titleList.add("sonType"); // 真正来源
        titleList.add("status"); // 上下架  上架 or 下架
        titleList.add("putInStartTime"); // 投放开始时间
        titleList.add("putInEndTime"); // 投放结束时间
        titleList.add("isDel");  //是否删除  默认否   是 or  否
        titleList.add("level");  //评级    需要新增
        titleList.add("isStreetSnap");  //是否街拍  是 or 否
        titleList.add("gender");   //性别  男女通用
        titleList.add("isCreateBill");   //是否生成海报    默认  是   是or否
        titleList.add("isOlai");   // 是否奥莱  是or否
        titleList.add("isPublic");    //是否公开   默认 否  是or否
        titleList.add("createBy");     // 创建人   默认  系统导入
        titleList.add("fashionerPhone");   //搭配师手机号   和 发布人名称  发布人 id  搭配师
        titleList.add("styleTag");         //风格标签  多字段 逗号分割 ， sys_look_label
        titleList.add("sceneTag");         //场景标签
        titleList.add("imgPath");          //相对路径 imgPath
        titleList.add("imgNames");         // 图片名称
        titleList.add("videos");           // 视频url
        titleList.add("skcs");           // skcs
        titleList.add("auditingStatus");           //  审批状态  4  待提审  3 审批中  如果是待提审 不审核  3 去审核
        RedisTemplateUtil.setex(redisPoolUtil, keys, "", 60);

        //返回数据
        List<Map<String,Object>> importData = fileParseService.readExcelByUrl(titleList,filePath);
        List<CreateFashionerMatchReq> createLookContexts = buildData(importData,keys,importUser);
        //整理数据
        createLookContexts.forEach(e->ibFashionerMatchService.saveFashionerMatch(e,"系统"));
    }

    @Override
    public List<CreateFashionerMatchReq> buildData(List<Map<String, Object>> importData,String keys,String importUser) {
        //封装数据  暂时只更改sysLookProduct
        List<CreateFashionerMatchReq> createLookContexts = new ArrayList<>();
//        if(importData.size() != 19){
//            RedisTemplateUtil.setex(redisPoolUtil, keys, "导入失败，模板错误！", 60);
//        }

        int successNum = 0;
        int failNum = 0;
        int i = 1;
        List<Integer> integers = new ArrayList<>();
        for (int y = 0 ; y < importData.size(); y++ ) {
            ++i;
            try {
                CreateFashionerMatchReq createFashionerMatchReq =  buildCreateLookContext(importData.get(y),keys,importUser);
                createLookContexts.add(createFashionerMatchReq);
                successNum++;
            }catch (Exception e){
                log.error(" 导入excel  第{}行封装数据错误 e = {} ", i,e);
                Integer failNumLine = new Integer(i);
                integers.add(failNumLine);
                failNum ++;
                continue;
            }
        }
        String s = "共导入数据" + importData.size() + "行，成功数据" + successNum + "行,失败数据" + failNum + "行，失败数据行数" + JSONObject.toJSONString(integers);
        String message = "";
        RedisTemplateUtil.setex(redisPoolUtil, keys, message, 600);
        return createLookContexts;
    }

//    @Override
//    public void fixImportErrorData(String filePath) {
//
//        List<String> titleList = new ArrayList<>();
//        titleList.add("name");  // 名称
//        titleList.add("introduction");  //简介
//        titleList.add("source"); //来源
//        titleList.add("status"); // 上下架  上架 or 下架
//        titleList.add("isDel");  //是否删除  默认否   是 or  否
//        titleList.add("level");  //评级    需要新增
//        titleList.add("isStreetSnap");  //是否街拍  是 or 否
//        titleList.add("gender");   //性别  男女通用
//        titleList.add("isCreateBill");   //是否生成海报    默认  是   是or否
//        titleList.add("isOlai");   // 是否奥莱  是or否
//        titleList.add("isPublic");    //是否公开   默认 否  是or否
//        titleList.add("createBy");     // 创建人   默认  系统导入
//        titleList.add("fashionerPhone");   //搭配师手机号   和 发布人名称  发布人 id  搭配师
//        titleList.add("styleTag");         //风格标签  多字段 逗号分割 ， sys_look_label
//        titleList.add("sceneTag");         //场景标签
//        titleList.add("imgPath");          //相对路径 imgPath
//        titleList.add("imgNames");         // 图片名称
//        titleList.add("videos");           // 视频url
//        titleList.add("skcs");           // skcs
////        titleList.add("brandIds");           // 品牌名称
////        titleList.add("years");           // 年份
////        titleList.add("season");           // 季节
////        titleList.add("month");           // 月份
////        titleList.add("band");           // 波段
////        titleList.add("bigType");           // 大类
////        titleList.add("smallType");           // 小类
//
//        //返回数据
//        List<Map<String,Object>> importData = fileParseService.readExcelByUrl(titleList,filePath);
//        List<CreateFashionerMatchReq> createLookContexts = buildData(importData);
//        //整理数据
//
//
//        for (CreateFashionerMatchReq createLookContext : createLookContexts) {
//            try{
//                ibFashionerMatchService.fixImportErrorData(createLookContext,"系统");
//            }catch (Exception e){
//                log.error("fixImportErrorData  createLookContext ={}",JSONObject.toJSON(createLookContext), e);
//                continue;
//            }
//        }
//
//    }

    @Override
    public void checkErrorDataFromThemeAndLook(String date) {
//        Date endDate = DateUtils.parseDate(date, "yyyy-MM-dd hh:mm:ss");
//        // 查询 bfashionermatch  的 thmemid不为空的, 然后进行 lookproduct和themeDetail进行对比   lookpcroduct 错误的进行修
//        List<BFashionerMatch> bFashionerMatches = ibFashionerMatchService.selectThemeIdIsNotNullByEndDate(endDate);
//        //进行处理
//        for (BFashionerMatch bFashionerMatch : bFashionerMatches) {
//            try{
//                //查询themeDetail
//                ThemeDetails params  = new ThemeDetails();
//                params.setThemeId(new BigDecimal(bFashionerMatch.getThemeId()));
//                List<ThemeDetails> themeDetails = themeDetailsMapper.selectBySelective(params);
//                //根据id查询
//                List<com.jnby.material.api.dto.CreateLookReq.LookProduct> lookProductList = themeDetails.stream().map(td -> {
//                    com.jnby.material.api.dto.CreateLookReq.LookProduct lookProduct = new com.jnby.material.api.dto.CreateLookReq.LookProduct();
//                    lookProduct.setProductId(Integer.valueOf(td.getOutId()));
//                    lookProduct.setProductCode(td.getProductCode());
//                    lookProduct.setProductName(td.getProductName());
//                    lookProduct.setProductColor(td.getProductColorNo());
//                    lookProduct.setProductColorName(td.getProductColor());
//                    lookProduct.setProductImage(td.getImgUrl());
//                    lookProduct.setProductBrand(td.getProductBrand());
//
//                    //增加额外数据
//                    List<String> skccodeIds = new ArrayList<>();
//                    skccodeIds.add(td.getProductCode());
//                    List<BoxMProduct> list = productService.batchGetProductBySkcCodes(skccodeIds);
//
//                    if(CollectionUtils.isNotEmpty(list)){
//                        inside : for (BoxMProduct boxMProduct : list) {
//                            if(boxMProduct.getColorno().equals(td.getProductColorNo()) && boxMProduct.getName().equals(td.getProductCode())){
//                                lookProduct.setProductCode(boxMProduct.getName());
//                                lookProduct.setProductId(Integer.parseInt(boxMProduct.getId()+""));
//                                lookProduct.setProductColor(boxMProduct.getColorno());
//
//                                lookProduct.setArcBrandId(boxMProduct.getcArcbrandId()+"");
//                                lookProduct.setBand(boxMProduct.getBand());
//                                lookProduct.setSkc(boxMProduct.getName()+boxMProduct.getColorno());
//                                lookProduct.setProductBrandId(boxMProduct.getBandid()+"");
//                                lookProduct.setYears(boxMProduct.getYear());
//                                lookProduct.setSeason(boxMProduct.getSmallseasonid()+"");
//
//                                lookProduct.setBigCategory(boxMProduct.getBigclassid()+"");
//                                lookProduct.setSmallCategory(boxMProduct.getSmallclassid()+"");
//                                lookProduct.setBrandId(transferBoxBrandIdToBrandWeId(boxMProduct.getcArcbrandId()+""));
//                                break  inside;
//                            }
//                        }
//                    }
//                    return lookProduct;
//                }).collect(Collectors.toList());
//
//                List<SysLookProduct> collect = lookProductList.stream().map(e -> {
//                    SysLookProduct product = new SysLookProduct();
//                    BeanUtils.copyProperties(e, product);
//                    JSONObject productExtend = new JSONObject();
//                    productExtend.put("productBrand", e.getProductBrand());
//                    productExtend.put("productColorName", e.getProductColorName());
//                    productExtend.put("productName", e.getProductName());
//                    productExtend.put("productImage", e.getProductImage());
//                    product.setExtendJson(JSON.toJSONString(productExtend))
//                            .setIsDel(DelEnum.NORMAL.getCode()).setCreateTime(new Date()).setUpdateTime(new Date());
//                    product.setProductBand(e.getBand());
//                    return product;
//                }).collect(Collectors.toList());
//
//
//                // 查询lookproduct
//                List<String> productCodes = lookProductList.stream().map(e -> e.getProductCode() + e.getProductColor()).sorted()
//                        .collect(Collectors.toList());
//                String productCodeStr = String.join(",", productCodes);
//
//                //对比数据
//                for (SysLookProduct lookProduct : collect) {
//
//                    List<SysLookProduct> productList = sysLookProductService.list(new QueryWrapper<>(new SysLookProduct()
//                            .setLookId(bFashionerMatch.getLookId().intValue()).setProductCode(lookProduct.getProductCode())));
//                    //修复 colorNo he    colorName 和 skc
//                    for (SysLookProduct sysLookProduct : productList) {
//                        SysLookProduct  update = new SysLookProduct();
//                        update.setId(sysLookProduct.getId());
//
//                        update.setProductColor(lookProduct.getProductColor());
//                        update.setSkc(lookProduct.getSkc());
//                        update.setExtendJson(lookProduct.getExtendJson());
//
//                        log.info("错误原数据 [ colorNo : {} , skc : {} , extendJson :{}]",sysLookProduct.getProductColor(),
//                                sysLookProduct.getSkc(),
//                                sysLookProduct.getExtendJson());
//
//                        log.info("更改新数据 [ colorNo : {} , skc : {} , extendJson :{}]",lookProduct.getProductColor(),
//                                lookProduct.getSkc(),
//                                lookProduct.getExtendJson());
//
//                        sysLookProductService.updateById(update);
//                    }
//                }
//                //更新 sysLOok
//                SysLook update  = new SysLook();
//                update.setId(bFashionerMatch.getLookId().intValue());
//                update.setLookProduct(productCodeStr);
//                sysLookMapper.updateById(update);
//            }catch (Exception e){
//                log.error("checkErrorDataFromThemeAndLook data = {}",JSONObject.toJSONString(bFashionerMatch),e);
//            }
//        }
    }

    @Override
    public void fixUploadImgs(Integer number) {
//        //查询到 图片 里面包含 jnby_box_look_pfop  这个的  找到原图 重新压缩
//        List<SysGallery>  list = sysGalleryMapper.selectUrlLikeJnbyBoxLookPfop((number - 1) * 1000 ,1000);
//        if(CollectionUtils.isEmpty(list)){
//            log.info("已全部修复完成  没有发现新数据需要修复");
//            return ;
//        }
//        for (SysGallery sysGallery : list) {
//            //
//            String url = sysGallery.getUrl();
//            //原图
//            String originUrl = "";
//            if(url.contains("%3F")){
//                String[] split = url.split("%3F");
//                originUrl = split[0];
//            }else if(url.contains("?")){
//                String[] split = url.split("\\?");
//                originUrl = split[0];
//            }
//
//            //无前缀路径url
//            String substring = originUrl.split(perfixUploadImgPath)[1].substring(1);
//
//            //逗分割
//            String[] split = substring.split("\\.");
//            String perfix = split[0];
//            String ext = "";
//            if(split.length > 1){
//                ext = "@compress_large."+split[1];
//            }
//
//            String ops = "imageMogr2/auto-orient/thumbnail/2400x2400>/blur/1x0/quality/75";
//            qiniuUtil.transCoding(substring,perfix + ext ,ops);
//
//            SysGallery update = new SysGallery();
//            update.setUrl(perfixUploadImgPath +"/" + perfix + ext);
//            update.setId(sysGallery.getId());
//            sysGalleryService.updateById(update);
//        }
//        log.info("本次修复  {} 条",list.size());
    }

    @Override
    public void checkBafshionerMatchAndLook(Integer number) {
//        com.github.pagehelper.Page<StylingEntity> hPage = PageHelper.startPage( number , 1000);
//        List<BFashionerMatch> list = bFashionerMatchMapper.selectListOrderByCreateTime();
//        if(CollectionUtils.isEmpty(list)){
//            log.info("checkBafshionerMatchAndLook  无数据需要进行修复");
//            return ;
//        }
//        for (BFashionerMatch bFashionerMatch : list) {
//            SysLook sysLook = sysLookMapper.selectById(bFashionerMatch.getLookId());
//            if(sysLook == null){
//                continue;
//            }
//            SysLook update = new SysLook();
//            update.setId(sysLook.getId());
//            if(!bFashionerMatch.getStatus().equals(sysLook.getStatus())){
//                update.setStatus(bFashionerMatch.getStatus());
//            }
//            if(!bFashionerMatch.getIsDel().equals(sysLook.getIsDel())){
//                update.setIsDel(bFashionerMatch.getIsDel());
//            }
//            if(update.getStatus() == null  && update.getIsDel() == null){
//                continue;
//            }
//            sysLookMapper.updateById(update);
//            log.info("修复statue  和  isDel  bafshionerMatch  id = {} , status = {} ,isDel = {}   , sysLook id = {}, status = {} , isDel = {}",
//                    bFashionerMatch.getId(), bFashionerMatch.getStatus(),bFashionerMatch.getIsDel(),sysLook.getId(),sysLook.getStatus(),sysLook.getIsDel());
//        }
    }

    @Override
    public List<WaitPerfectListResp> waitPerfectList(WaitPerfectListReq requestData, Page page) {
        List<WaitPerfectListResp> response  = new ArrayList<>();
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());

        if(requestData.getStartTime() != null && requestData.getEndTime() != null){
            Date startDate = DateUtils.parseDate(DateUtils.formatDate(requestData.getStartTime(), "yyyy-MM-dd") + " 00:00:00", DateUtils.DATETIME_PATTERN);
            Date endDate = DateUtils.parseDate(DateUtils.formatDate(requestData.getEndTime(), "yyyy-MM-dd") + " 23:59:59", DateUtils.DATETIME_PATTERN);
            requestData.setStartTime(startDate);
            requestData.setEndTime(endDate);
        }


        List<StylingBase> list = stylingDetailMapper.selectStylingBaseByParams(requestData);
        if(list.size() == 0){
            return response;
        }
        //获取ids
        List<String> stylingBaseIds = list.stream().map(r -> r.getId()).collect(Collectors.toList());
        List<StylingDetail> stylingDetails = stylingDetailMapper.selectStylingDetailByStylingBaseIds(stylingBaseIds);
        Map<String, List<StylingDetail>> groupByStylingBaseId = stylingDetails.stream().collect(Collectors.groupingBy(r -> r.getStylingBaseId()));
        //便利
        for (StylingBase stylingBase : list) {
            WaitPerfectListResp waitPerfectListResp = new WaitPerfectListResp();
            BeanUtils.copyProperties(stylingBase,waitPerfectListResp);
            List<StylingDetail> stylingDetails1 = groupByStylingBaseId.get(stylingBase.getId());
            List<WaitPerfcetListDetailResp> waitPerfcetListDetailResps1 = JSONObject.parseArray(JSONObject.toJSONString(stylingDetails1), WaitPerfcetListDetailResp.class);
            waitPerfectListResp.setDetailRespList(waitPerfcetListDetailResps1);
            if(StringUtils.isNotBlank(stylingBase.getCreateBy())){
                Fashioner byUserId = fashionerMapper.findByUserId(stylingBase.getCreateBy());
                if(byUserId != null){
                    waitPerfectListResp.setCreateByName(byUserId.getName());
                }
            }
            response.add(waitPerfectListResp);
        }
        PageInfo<Object> pageInfo = new PageInfo<Object>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return response;
    }

    @Override
    public void flushStylingDetail(CommonRequest commonRequest) {
        //查询数据
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(commonRequest.getPage().getPageNo(), commonRequest.getPage().getPageSize());
        // 查询没有 品牌的数据
        List<StylingDetail> stylingDetails = stylingDetailMapper.selectListByNoBrandIdAndHaveColorNo();
        log.info("stylingDetails = {}",JSONObject.toJSONString(stylingDetails));
        batchGetProductInfo(stylingDetails);
        for (StylingDetail stylingDetail : stylingDetails) {
            stylingDetailMapper.updateByPrimaryKeySelective(stylingDetail);
            log.info("updateByPrimaryKeySelective = {}",JSONObject.toJSONString(stylingDetail));
        }
    }

    @Override
    public void flushStylingBase(CommonRequest commonRequest) {
        //查询数据
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(commonRequest.getPage().getPageNo(), commonRequest.getPage().getPageSize());
        List<StylingBaseWithBLOBs> stylingBases = stylingBaseMapper.selectByCreateByAndSource();
        for (StylingBaseWithBLOBs stylingBase : stylingBases) {
            Fashioner byUserId = fashionerMapper.findByUserId(stylingBase.getCreateBy());
            if(byUserId != null){
                if(byUserId.getIsSales() == 0L){
                    stylingBase.setSource(2L);
                }else{
                    stylingBase.setSource(1L);
                }
            }
            stylingBaseMapper.updateByPrimaryKeySelective(stylingBase);
            log.info("flushStylingBase = {}",JSONObject.toJSONString(stylingBase));
        }



    }

    @Override
    public void finishById(String id) {
        StylingBaseWithBLOBs stylingBase = new StylingBaseWithBLOBs();
        stylingBase.setId(id);
        stylingBase.setIsFinish(1L);
        stylingBaseMapper.updateByPrimaryKeySelective(stylingBase);
    }

    @Override
    public void importExcelPos(String url, String keys,String realName,String workNo,String copyrights,String seasons) {
        // 0  非版权素材-不可生成二维码海报     1   非版权素材—可生成二维码海报       3    使用版权    copyrightId  字段 版权字段
//        List<String> titleList = new ArrayList<>();
//        titleList.add("name");                 // 名称
//        titleList.add("content");              //简介
//        titleList.add("publisherName");        //发布人名称  需要转为id  B11-A01-A02  B11  起头的  转换
//        titleList.add("isShelf");              //是否上架(0:下架;1上架) 默认上架   转换
//        titleList.add("channelTypeName");      //渠道名称    中台 B10 起头  转换
//        titleList.add("classifyName");         //分类名称    中台 B10 起头  转换
//        titleList.add("brandName");            //品牌名称， 逗号分隔，       转换 为id
//        titleList.add("putChannelName");       //可见渠道   经销  1  直营  2    直营,经销   0  转换
//        titleList.add("putStoreTypeName");     //不可见门店    1. 单品牌/集合店    2.  江南布衣+    3.    奥莱   可以传递多个  逗号分隔  转换
//        titleList.add("imgName");              //图片名称   英文逗号分隔
//        titleList.add("imgDir");               //图片存储地址文件夹   /22AW/01/  这种模式
//        titleList.add("videoName");            //视频地址信息  限制一个
//        titleList.add("shareStateName");       // 3  使用版权    1  非版权素材-可生成二维码海报     0 非版权素材-不可生成二维码海报
//        titleList.add("copyrightName");        // 版权名称  转换成id
//        titleList.add("createProductPoster");  //  是否生成单品海报   是   1  否   0
//        titleList.add("skcs");                 // skc 以逗号分隔
//        titleList.add("themeYear");            // 年份 直接用
//        titleList.add("themeSeasons");         // 季节多个  逗号分隔
//        RedisTemplateUtil.setex(redisPoolUtil, keys, "true", 60);

        List<Map<String,Object>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {

            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,Object> result = new HashMap<>();
                    result.put("name",integerStringMap.get(0));
                    result.put("content",integerStringMap.get(1));
                    result.put("publisherName",integerStringMap.get(2));
                    result.put("isShelf",integerStringMap.get(3));
                    result.put("channelTypeName",integerStringMap.get(4));
                    result.put("classifyName",integerStringMap.get(5));
                    result.put("brandName",integerStringMap.get(6));
                    result.put("putChannelName",integerStringMap.get(7));
                    result.put("putStoreTypeName",integerStringMap.get(8));
                    result.put("imgName",integerStringMap.get(9));
                    result.put("imgDir",integerStringMap.get(10));
                    result.put("videoName",integerStringMap.get(11));
                    result.put("shareStateName",integerStringMap.get(12));
                    result.put("copyrightName",integerStringMap.get(13));
                    result.put("createProductPoster",integerStringMap.get(14));
                    result.put("skcs",integerStringMap.get(15));
                    result.put("themeYear",integerStringMap.get(16));
                    result.put("themeSeasons",integerStringMap.get(17));

                    result.put("storeType",integerStringMap.get(19));
                    result.put("storePkgId",integerStringMap.get(20));
                    importData.add(result);
                }
            }
        });

        List<SavePosExcelReq> list = buildPosData(importData,keys,copyrights,seasons,realName,workNo);
        for (SavePosExcelReq savePosExcelReq : list) {
            log.info("savePosExcelReq = {}",JSONObject.toJSONString(savePosExcelReq));
            String post = HttpUtil.post(importPosUrl, JSONObject.parseObject(JSONObject.toJSONString(savePosExcelReq), Map.class));
            log.info("importExcelPos = {}",post);
        }
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
    }

    @Override
    public void changeStylingBaseStatus(String outNo, Long status) {
        stylingBaseMapper.updateByOutNoAndStatus(outNo,status);
    }

    @Override
    public Integer getIsCreateOrUpdate(String sysUserId, String unionId, String outNo) {

        StylingBase stylingBase = new StylingBase();
        stylingBase.setOutNo(outNo);
        stylingBase.setStatus(1L);
        List<StylingBaseWithBLOBs> stylingBaseWithBLOBs = stylingBaseMapper.selectListBySelective(stylingBase);


        stylingBase = new StylingBase();
        stylingBase.setOutNo(outNo);
        stylingBase.setStatus(3L);
        List<StylingBaseWithBLOBs> stylingBaseWithBLOBsThree = stylingBaseMapper.selectListBySelective(stylingBase);
        stylingBaseWithBLOBs.addAll(stylingBaseWithBLOBsThree);

        if(CollectionUtils.isNotEmpty(stylingBaseWithBLOBs)){
            // 1 是编辑  3 是创建
            return stylingBaseWithBLOBs.get(0).getStatus().intValue();
        }else{
            // 默认创建
            return 3;
        }
    }

    @Override
    public List<StylingBaseDetail> queryStylingBaseDetailListV2(String outNo, Page page) {
        com.github.pagehelper.Page<StylingBaseDetail> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        iStylingRepository.selectStylingBaseDetailListV2(outNo);
        PageInfo<StylingBaseDetail> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        page.setPageNo(page.getPageNo());
        return pageInfo.getList();
    }

    @Override
    public List<BatchGetIsCreateOrUpdateResp> batchGetIsCreateOrUpdate(List<String> outNos) {
        List<BatchGetIsCreateOrUpdateResp> resps = new ArrayList<>();

        List<StylingBaseWithBLOBs> list =  stylingBaseMapper.selectByOutNosAndStatus(outNos);
        if(CollectionUtils.isNotEmpty(list)){
            Map<String, List<StylingBaseWithBLOBs>> collect = list.stream().collect(Collectors.groupingBy(r -> r.getOutNo()));

            Set<String> strings = collect.keySet();
            for (String outNo : strings) {
                BatchGetIsCreateOrUpdateResp batchGetIsCreateOrUpdateResp = new BatchGetIsCreateOrUpdateResp();
                batchGetIsCreateOrUpdateResp.setOutNo(outNo);
                List<StylingBaseWithBLOBs> stylingBaseWithBLOBs = collect.get(outNo);
                stylingBaseWithBLOBs.sort(new Comparator<StylingBaseWithBLOBs>() {
                    @Override
                    public int compare(StylingBaseWithBLOBs o1, StylingBaseWithBLOBs o2) {
                        return o1.getStatus().intValue() - o2.getStatus().intValue();
                    }
                });
                if(CollectionUtils.isNotEmpty(stylingBaseWithBLOBs)){
                    batchGetIsCreateOrUpdateResp.setStatus(stylingBaseWithBLOBs.get(0).getStatus().intValue());
                }else{
                    batchGetIsCreateOrUpdateResp.setStatus(3);
                }
                resps.add(batchGetIsCreateOrUpdateResp);
            }
        }
        return resps;
    }

    @Override
    public void batchUpdateStylingCart(StylingProductUpdateReq req) {
        StylingBoxCart cart = new StylingBoxCart();
        cart.setIsEb(req.getIsEb());
        cart.setUpdateTime(new Date());
        List<String> ids = req.getIds();
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(ids), "id不能为空");
        log.info("批量更新购物车 更新内容={}， 条件={}", JSONObject.toJSONString(req), JSONObject.toJSONString(ids));
        iStylingRepository.batchUpdateStylingCartByIds(cart, ids);
    }

    private List<SavePosExcelReq>  buildPosData(List<Map<String, Object>> importData, String keys,String copyrights,String seasons,String realName,String workNo) {
        List<SavePosExcelReq> result  = new ArrayList<>();
        //发布人信息
        List<SysCategoryModel> publisherCate  = new ArrayList<>();
        getSysCategory("B11-A01",publisherCate);

        List<SysCategoryModel> categoryModels  = new ArrayList<>();
        getSysCategory("B10-A02",categoryModels);
        // 映射关系
        List<BrandConfig> brandConfigs = JSONObject.parseArray(brandWeIdConfig, BrandConfig.class);

        List<Map> seasonMap = JSONObject.parseArray(seasons, Map.class);


        for (Map<String, Object> importDatum : importData) {
            try {
                SavePosExcelReq savePosExcelReq = new SavePosExcelReq();
                //保存数据
                savePosExcelReq.setName(importDatum.get("name") == null ? null : importDatum.get("name").toString());
                savePosExcelReq.setContent(importDatum.get("content") == null ? null : importDatum.get("content").toString());
                // 转换发布人id 根据发布人名称
                SysCategoryModel publisherName = tansferPublisherId(importDatum.get("publisherName"), publisherCate);
                savePosExcelReq.setPublisherId(publisherName == null ?null:publisherName.getCode());
                savePosExcelReq.setPublisherName(publisherName == null ? null : publisherName.getName());
                savePosExcelReq.setIsShelf(importDatum.get("isShelf") == null ? null : importDatum.get("isShelf").toString().equals("上架") ? "1":"0");
                SysCategoryModel channelType = tansferChannelType(importDatum.get("channelTypeName"), categoryModels,"B10-A02");
                savePosExcelReq.setChannelType(channelType == null ? null : channelType.getPidCode());
                SysCategoryModel classify = tansferPublisherId(importDatum.get("classifyName"), categoryModels);
                savePosExcelReq.setClassifyName(classify == null ? null : classify.getName());
                savePosExcelReq.setClassifyId(classify == null ? null : classify.getCode());
                String brandName = importDatum.get("brandName").toString();
                String brandId = reflectBrandNameToWeId(brandName,brandConfigs);
                savePosExcelReq.setBrandId(brandId);
                savePosExcelReq.setPutAwayTime(DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss"));

                String putChannelName = importDatum.get("putChannelName").toString();
                String putStoreTypeName = importDatum.get("putStoreTypeName") == null ? null : importDatum.get("putStoreTypeName").toString();
                SavePosExcelReq.PosThemeExt posThemeExt = new SavePosExcelReq.PosThemeExt();
                posThemeExt.setPutChannel(reflectPutChannel(putChannelName));
                posThemeExt.setPutStoreType(reflectPutStoreType(putStoreTypeName));

                posThemeExt.setStoreType(transferStoreType(importDatum.get("storeType")));
                posThemeExt.setStorePkgId(importDatum.get("storePkgId") == null ? null : importDatum.get("storePkgId").toString());

                Object imgNames = importDatum.get("imgName");
                Object imgDir = importDatum.get("imgDir");
                if(imgNames != null && StringUtils.isNotBlank(imgNames.toString())){
                    List<String> imgs = new ArrayList<>();
                    String[] split = imgNames.toString().split(",");
                    for (String image : split) {
                        StringBuffer stringBuffer = new StringBuffer();
                        stringBuffer.append(perfixUploadImgPath);
                        stringBuffer.append(imgDir.toString());
                        stringBuffer.append(image);
                        stringBuffer.append(".jpg");
                        imgs.add(stringBuffer.toString());
                    }
                    savePosExcelReq.setBackgroundImage(imgs);
                }
                Object videoName = importDatum.get("videoName");
                if(videoName != null && StringUtils.isNotBlank(videoName.toString())){
                    StringBuffer stringBuffer = new StringBuffer();
                    stringBuffer.append(perfixUploadImgPath);
                    stringBuffer.append(imgDir.toString());
                    stringBuffer.append(videoName);
                    savePosExcelReq.setVideoDetail(stringBuffer.toString());
                }
                // 映射 shareStateName
                   // 3  使用版权    1  非版权素材—可生成二维码海报     0 非版权素材-不可生成二维码海报
                Object shareStateName = importDatum.get("shareStateName");
                reflectShareState(shareStateName,posThemeExt,importDatum,copyrights);
                Object skcs = importDatum.get("skcs");
                if(skcs != null && StringUtils.isNotBlank(skcs.toString())){
                    List<SavePosExcelReq.ThemeDetailsProducts> themeDetailsProducts = new ArrayList<>();
                    List<org.springcenter.material.api.dto.CreateLookReq.LookProduct> products = new ArrayList<>();
                    Map<String, Object> data  = new HashMap<>();
                    data.put("isDel","否");

                    // 处理product
                    CommonRequest<CreateSysLookProductReq> commonRequest = new CommonRequest<>();
                    CreateSysLookProductReq createSysLookProductReq = new CreateSysLookProductReq();
                    createSysLookProductReq.setProducts(products);
                    createSysLookProductReq.setSkcs(skcs.toString());
                    createSysLookProductReq.setData(data);
                    commonRequest.setRequestData(createSysLookProductReq);
                    products = iMaterialApi.createSysLookProduct(commonRequest);

                    //处理数据
                    for (org.springcenter.material.api.dto.CreateLookReq.LookProduct product : products) {
                        SavePosExcelReq.ThemeDetailsProducts themeDetailsProducts1 = new SavePosExcelReq.ThemeDetailsProducts();
                        themeDetailsProducts1.setImgUrl(product.getProductImage());
                        themeDetailsProducts1.setProductNo(product.getProductCode());
                        themeDetailsProducts1.setProductSeason(product.getSeason());
                        themeDetailsProducts1.setProductName(product.getProductName());
                        themeDetailsProducts1.setProductColor(product.getProductColor());
                        themeDetailsProducts1.setProductColorName(product.getProductColorName());
                        themeDetailsProducts1.setYear(product.getYears());
                        themeDetailsProducts1.setProductId(product.getProductId().toString());
                        themeDetailsProducts1.setProductPrice(product.getPrice()+"");
                        themeDetailsProducts.add(themeDetailsProducts1);
                    }
                    savePosExcelReq.setThemeDetails(themeDetailsProducts);
                }

                posThemeExt.setThemeYear(Integer.parseInt(importDatum.get("themeYear") == null ? "0" :importDatum.get("themeYear").toString()));

                String themeSeasonIds  = new String();
                Object themeSeasons = importDatum.get("themeSeasons");
                if(themeSeasons != null && StringUtils.isNotBlank(themeSeasons.toString())){
                    String[] split = themeSeasons.toString().split(",");
                    for (int i = 0 ; i < split.length ; i++){
                        for (Map map : seasonMap) {
                            if(map.get("value").toString().equals(split[i])){
                                themeSeasonIds += map.get("key").toString();
                                if(i != split.length -1){
                                    themeSeasonIds+=",";
                                }
                            }
                        }
                    }
                }
                savePosExcelReq.setRealName(realName);
                savePosExcelReq.setWorkNo(workNo);
                posThemeExt.setThemeSeasons(themeSeasonIds);
                savePosExcelReq.setPosThemeExt(posThemeExt);

                result.add(savePosExcelReq);
            }catch (Exception e){
                log.error("buildPosData = {}",JSONObject.toJSON(importData),e);
            }
        }
        return result;
    }

    private Integer transferStoreType(Object storeType) {
        String str = storeType.toString();
        if(str.equals("条件筛选")){
            return 1;
        }else if(str.equals("选择门店包")){
            return 2;
        }
        return null;
    }

    private SysCategoryModel tansferChannelType(Object channelTypeName, List<SysCategoryModel> categoryModels, String pidCode) {

        if( null == channelTypeName){
            return null;
        }
        for (SysCategoryModel sysCategoryModel : categoryModels) {
            if(channelTypeName.toString().equals(sysCategoryModel.getName()) && pidCode.equals(sysCategoryModel.getPidCode())){
                return sysCategoryModel;
            }
        }
        return null;
    }

    private void reflectShareState(Object shareStateName, SavePosExcelReq.PosThemeExt posThemeExt,
                                   Map<String, Object> importDatum, String copyrights) {
        // 3  使用版权    1  非版权素材—可生成二维码海报     0 非版权素材-不可生成二维码海报
        if(null == shareStateName){
            return;
        }
        List<Map> maps = JSONObject.parseArray(copyrights, Map.class);
        if("使用版权".equals(shareStateName.toString())){
            posThemeExt.setShareState("3");
            Object copyrightName = importDatum.get("copyrightName");
            if(null != copyrightName &&StringUtils.isNotBlank(copyrightName.toString())){
                for (Map map : maps) {
                    if(copyrightName.toString().equals(map.get("name").toString())){
                        posThemeExt.setCopyrightId(map.get("id").toString());
                    }
                }
            }
        }else if("非版权素材-可生成二维码海报".equals(shareStateName.toString())){
            posThemeExt.setShareState("1");
            posThemeExt.setCreateProductPoster("1");
        }else if("非版权素材-不可生成二维码海报".equals(shareStateName.toString())){
            posThemeExt.setShareState("0");
            Object createProductPoster = importDatum.get("createProductPoster");
            if(createProductPoster != null && StringUtils.isNotBlank(createProductPoster.toString())){
                posThemeExt.setCreateProductPoster(createProductPoster.toString().equals("是")?"1":"0");
            }
        }
    }



    private String reflectPutStoreType(String putStoreTypeName) {
        if(StringUtils.isBlank(putStoreTypeName)){
            return "";
        }
        StringBuffer putStoreTypeBuffer = new StringBuffer();
        String[] split = putStoreTypeName.split(",");
        for (int i = 0 ; i <  split.length; i++) {
            if(i != split.length - 1){
                if("单品牌/集合店".equals(split[i])){
                    putStoreTypeBuffer.append("1,");
                }else if("江南布衣+".equals(split[i])){
                    putStoreTypeBuffer.append("2,");
                }else{
                    putStoreTypeBuffer.append("3,");
                }
            }else {
                if("单品牌/集合店".equals(split[i])){
                    putStoreTypeBuffer.append("1");
                }else if("江南布衣+".equals(split[i])){
                    putStoreTypeBuffer.append("2");
                }else{
                    putStoreTypeBuffer.append("3");
                }
            }
        }
        return putStoreTypeBuffer.toString();
    }

    private Integer reflectPutChannel(String putChannelName) {
        if("经销".equals(putChannelName)){
            return 1;
        }else if("直营".equals(putChannelName)){
            return 2;
        }else{
            return 0;
        }
    }

    private String reflectBrandNameToWeId(String brandNameStr, List<BrandConfig> brandConfigs) {
        String[] split = brandNameStr.split(",");
        StringBuffer brandIdBuffer = new StringBuffer();
        for (int i = 0 ; i <  split.length; i++) {
            for (BrandConfig brandConfig : brandConfigs) {
                if(split[i].equals(brandConfig.getBrandName())){
                    brandIdBuffer.append(brandConfig.getWeid());
                    if(i != split.length - 1){
                        brandIdBuffer.append(",");
                    }
                }
            }
        }
        return brandIdBuffer.toString();
    }

    private SysCategoryModel tansferPublisherId(Object publisherName,List<SysCategoryModel> publisherCate) {
        if( null == publisherName){
            return null;
        }
        for (SysCategoryModel sysCategoryModel : publisherCate) {
            if(publisherName.toString().equals(sysCategoryModel.getName())){
                return sysCategoryModel;
            }
        }
        return null;
    }

    @Override
    public void syncNormalLookToEs(Integer number, String esIndexLook) {
        List<SysCategoryModel> scene = new ArrayList<>();

        Page page = new Page();
        page.setPageNo(number);
        page.setPageSize(500);
        //查询正常的look
        List<CreateLookContext> list = new ArrayList<>();
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<BFashionerMatch> bFashionerMatches = bFashionerMatchMapper.selectListOrderByCreateTime();
        if(CollectionUtils.isEmpty(bFashionerMatches)){
            return ;
        }
        List<BFashionerMatchLabel> labels = bFashionerMatchLabelMapper.selectListByMatchIds(bFashionerMatches.stream().map(r -> r.getId()).collect(Collectors.toList()));
        Map<String, List<BFashionerMatchLabel>> collect = labels.stream().collect(Collectors.groupingBy(r -> r.getMatchId()));
        for (BFashionerMatch bFashionerMatch : bFashionerMatches) {
            CreateLookContext createLookContext = new CreateLookContext();
            SysLookResp sysLookResp = new SysLookResp();
            sysLookResp.setId(bFashionerMatch.getLookId().intValue());
            createLookContext.setLook(sysLookResp);
            BFashionerMatchResp bFashionerMatchResp = new BFashionerMatchResp();
            BeanUtils.copyProperties(bFashionerMatch,bFashionerMatchResp);
            // 查询当前look是有搭还是企划
            BigDecimal lookId = bFashionerMatch.getLookId();

            CommonRequest<GetLookReq> commonRequest  = new CommonRequest<>();
            GetLookReq getLookReq = new GetLookReq();
            List<Integer> lookIds = new ArrayList<>();
            lookIds.add(lookId.intValue());
            getLookReq.setLookIds(lookIds);
            commonRequest.setRequestData(getLookReq);
            ResponseResult<List<ReloadLookResp>> looks = iMaterialApi.getLooks(commonRequest);
            if(CollectionUtils.isEmpty(looks.getData())){
                continue;
            }
            ReloadLookResp reloadLookResp = looks.getData().get(0);
            List<SysLookTypesResp> sysLookTypes = reloadLookResp.getSysLookTypes();
            for (SysLookTypesResp sysLookType : sysLookTypes) {
                if(sysLookType.getSonType().equals(SysLookSonTypeEnum.CUSTOMER_LOOK.getNewCode())){
                    //企划
                    createLookContext.setQihuabFashionerMatch(bFashionerMatchResp);
                    if(CollectionUtils.isNotEmpty(collect.get(bFashionerMatch.getId()))){
                        createLookContext.setQihuabFashionerMatchLabels(collect.get(bFashionerMatch.getId()).stream().map(e->{
                            BFashionerMatchLabelResp bFashionerMatchLabelResp = new BFashionerMatchLabelResp();
                            BeanUtils.copyProperties(e,bFashionerMatchLabelResp);
                            return bFashionerMatchLabelResp;
                        }).collect(Collectors.toList()));
                    }else{
                        createLookContext.setQihuabFashionerMatchLabels(new ArrayList<>());
                    }
                }else if(sysLookType.getSonType().equals(SysLookSonTypeEnum.YOUDA.getNewCode())){
                    createLookContext.setBFashionerMatch(bFashionerMatchResp);
                    if(CollectionUtils.isNotEmpty(collect.get(bFashionerMatch.getId()))){
                        createLookContext.setBFashionerMatchLabels(collect.get(bFashionerMatch.getId()).stream().map(e->{
                            BFashionerMatchLabelResp bFashionerMatchLabelResp = new BFashionerMatchLabelResp();
                            BeanUtils.copyProperties(e,bFashionerMatchLabelResp);
                            return bFashionerMatchLabelResp;
                        }).collect(Collectors.toList()));
                    }else{
                        createLookContext.setBFashionerMatchLabels(new ArrayList<>());
                    }
                }
            }
            list.add(createLookContext);
        }

        CommonRequest<List<CreateLookContext>> commonRequest = new CommonRequest<>();
        commonRequest.setRequestData(list);
        commonRequest.setMerchantId(esIndexLook);
        iMaterialApi.syncNormalLookToEs(commonRequest);
    }

//    private List<SysLookLabel> transferName(List<SysLookLabel> galleryList,List<SysCategoryModel> scene) {
//        //转换
//        if(CollectionUtils.isEmpty(galleryList)){
//            return new ArrayList<>();
//        }
//        for (SysLookLabel sysLookLabel : galleryList) {
//            for (SysCategoryModel sysCategoryModel : scene) {
//                if(StringUtils.isNotBlank(sysLookLabel.getLabelId()) && sysLookLabel.getLabelId().equals(sysCategoryModel.getCode())){
//                    sysLookLabel.setLabelName(sysCategoryModel.getName());
//                }
//            }
//        }
//        return galleryList;
//    }

    private CreateFashionerMatchReq buildCreateLookContext(Map<String, Object> data,String keys,String importUser) {

        CreateFashionerMatchReq  createLookContext = new CreateFashionerMatchReq();
        org.springcenter.material.api.dto.CreateLookReq look = createLookContext.getLook();
        CreateMatchReq match = createLookContext.getMatch();

        if(ObjectUtils.isEmpty(look) && ObjectUtils.isEmpty(match)){
            look = new org.springcenter.material.api.dto.CreateLookReq();
            match = new CreateMatchReq();
            match.setLastUpdateUserBy(importUser);
            look.setName(data.get("name").toString());
            look.setContent(data.get("introduction").toString());
            LookSourceEnum source = LookSourceEnum.getByDesc(data.get("source").toString());
            String sonType = data.get("sonType").toString();
            Object status = data.get("status");
            String auditingStatus = data.get("auditingStatus").toString();
            look.setSource( source == null ? null:source.getCode().toString());
//            look.setStatus("上架".equals(data.get("status").toString()) ? LookStatusEnum.UP.getCode():LookStatusEnum.DOWN.getCode());
            look.setIsDel("否".equals(data.get("isDel").toString())? DelEnum.NORMAL.getCode(): DelEnum.DELETE.getCode());
            String level = "30";
            if("S".equals(data.get("level").toString())){
                level = "10";
            }else if("A".equals(data.get("level").toString())){
                level = "20";
            }
            look.setLevel(level);
            look.setIsStreetSnap(data.get("isStreetSnap").equals("是")?1:0);
            look.setIsCreateBill(data.get("isCreateBill").equals("是")?1:0);
            look.setIsOlai(data.get("isOlai").equals("是")?1:0);
            look.setIsPublish(data.get("isPublic").toString().equals("是")?1:2);
            look.setCreateBy(data.get("createBy").toString());
            FashionerWithBLOBs params  =new FashionerWithBLOBs();
            params.setPhone(data.get("fashionerPhone").toString());
            List<FashionerWithBLOBs> fashionerWithBLOBs = fashionerMapper.selectFashionerBySelective(params);
            if(CollectionUtils.isNotEmpty(fashionerWithBLOBs)){
                look.setPublisherId(fashionerWithBLOBs.get(0).getId());
                look.setPublisherName(fashionerWithBLOBs.get(0).getName());
                look.setPublisherImg(fashionerWithBLOBs.get(0).getPhoto());
                match.setFashionerId(fashionerWithBLOBs.get(0).getId());
            }else{
                //搭配师为空 则直接跳过本条数据
                throw new RuntimeException("根据手机号未查询到搭配师 搭配师手机号： "+data.get("fashionerPhone").toString());
            }
            String gender = data.get("gender").toString();
            if("男".equals(gender)){
                gender = "1";
            }else if("女".equals(gender)){
                gender = "0";
            }else{
                gender = "2";
            }

            look.setGender(Integer.parseInt(gender));
            look.setType(source == null ?
                    SysLookTypeEnum.YOUDA.getCode():LookSourceEnum.YD.getCode().equals(source.getCode())? SysLookTypeEnum.YOUDA.getCode() : SysLookTypeEnum.QIHUA.getCode());
            look.setSonType(sonType);
            List<SysLookTypesResp> ara = new ArrayList<>();
            SysLookTypesResp sysLookTypes = new SysLookTypesResp();
            sysLookTypes.setType(source == null ?
                    SysLookTypeEnum.YOUDA.getCode():LookSourceEnum.YD.getCode().equals(source.getCode())? SysLookTypeEnum.YOUDA.getCode() : SysLookTypeEnum.QIHUA.getCode());
            sysLookTypes.setSonType(sonType);
            ara.add(sysLookTypes);
            look.setSysLookTypesList(ara);
            look.setAuditingStatus(auditingStatus.equals("否") ? 4:3);

            //设置是否有搭
            match.setIsYd(source == null ?
                    1:LookSourceEnum.YD.getCode().equals(source.getCode())? 1:0);

            //封装lookProduct数据
            // 封装match
            match.setName(data.get("name").toString());
            String lookGender = "73";
            if("1".equals(gender)){
                lookGender = "71";
            }else if("0".equals(gender)){
                lookGender = "72";
            }
//            match.setStatus("上架".equals(data.get("status").toString()) ? LookStatusEnum.UP.getCode():LookStatusEnum.DOWN.getCode());
            match.setIsDel("否".equals(data.get("isDel").toString())? DelEnum.NORMAL.getCode(): DelEnum.DELETE.getCode());
            match.setLookGender(lookGender);
            match.setMatchAbout(data.get("introduction").toString());
            if(sysLookTypes.getType().equals(SysLookTypeEnum.QIHUA.getCode())){
                // 企划 使用status
                match.setStatus(Integer.parseInt(status.toString().equals("上架")?"1":"0"));
            }else{
                if(data.get("putInStartTime") == null && data.get("putInEndTime") == null){
                    throw  new RuntimeException("投放时间为空！");
                }
                if(data.get("putInStartTime").toString().equals("-1")){
                    data.put("putInStartTime","2000-01-01 00:00");
                }

                if(data.get("putInEndTime").toString().equals("-1")){
                    data.put("putInEndTime","2100-01-01 00:00");
                }

                Date putInStartTime = DateUtils.parseDate(data.get("putInStartTime").toString(), "yyyy-MM-dd HH:mm");
                Date putInEndTime = DateUtils.parseDate(data.get("putInEndTime").toString(), "yyyy-MM-dd HH:mm");

                if(putInStartTime.after(putInEndTime)){
                    throw  new RuntimeException("投放结束时间必须大于投放开始时间！");
                }

                match.setPutInStartTime(putInStartTime);
                match.setPutInEndTime(putInEndTime);
            }

        }

        //处理图片视频地址
        List<org.springcenter.material.api.dto.CreateLookReq.LookImg> galleryList = look.getImgList();
        if(CollectionUtils.isEmpty(galleryList)){
            galleryList = new ArrayList<>();
            //图片地址
            String imgPath = data.get("imgPath") == null? "":data.get("imgPath").toString();
            String imgNames = data.get("imgNames") == null? "":data.get("imgNames").toString();
            if(StringUtils.isNotBlank(imgNames)){
                String[] images = imgNames.split(",");
                for (String image : images) {
                    org.springcenter.material.api.dto.CreateLookReq.LookImg sysGallery = new org.springcenter.material.api.dto.CreateLookReq.LookImg();
                    sysGallery.setIsDel("否".equals(data.get("isDel").toString())? DelEnum.NORMAL.getCode(): DelEnum.DELETE.getCode());
                    LookSourceEnum source = LookSourceEnum.getByDesc(data.get("source").toString());
                    sysGallery.setSource( source == null ? null:source.getCode().toString());
                    sysGallery.setType(0);
                    StringBuffer stringBuffer = new StringBuffer();
                    stringBuffer.append(perfixUploadImgPath);
                    stringBuffer.append(imgPath);

                    //处理压缩图片
                    stringBuffer.append(image);
                    stringBuffer.append(".jpg");
                    sysGallery.setUrl(stringBuffer.toString());
                    galleryList.add(sysGallery);
                }
            }

            String videos = data.get("videos") == null? "":data.get("videos").toString();
            if(StringUtils.isNotBlank(videos)){
                String[] split = videos.split(",");
                for (String videoUrl : split) {
                    org.springcenter.material.api.dto.CreateLookReq.LookImg sysGallery = new org.springcenter.material.api.dto.CreateLookReq.LookImg();
                    sysGallery.setIsDel("否".equals(data.get("isDel").toString())? DelEnum.NORMAL.getCode(): DelEnum.DELETE.getCode());
                    LookSourceEnum source = LookSourceEnum.getByDesc(data.get("source").toString());
                    sysGallery.setSource( source == null ? null:source.getCode().toString());
                    sysGallery.setType(1);
                    sysGallery.setUrl(videoUrl);
                    galleryList.add(sysGallery);
                }
            }
            look.setImgList(galleryList);
        }

        // 处理标签
        List<String> labels = look.getLabelList();
        if(CollectionUtils.isEmpty(labels)){
            labels = new ArrayList<>();
            // 获取look的场景标签  以及 风格标签
            String styleTag = data.get("styleTag") == null? "":data.get("styleTag").toString();
            String sceneTag = data.get("sceneTag") == null? "":data.get("sceneTag").toString();
            packetDataToLabels(styleTag,data,labels,0);  //type 0 为风格标签
            packetDataToLabels(sceneTag,data,labels,1);  // 1 为场景标签
            look.setLabelList(labels);
            match.setLabelList(new ArrayList<>());
        }

        //处理product
        List<org.springcenter.material.api.dto.CreateLookReq.LookProduct> products = look.getProductList();
        if(CollectionUtils.isEmpty(products)){
            products = new ArrayList<>();
            // 处理product
            CommonRequest<CreateSysLookProductReq> commonRequest = new CommonRequest<>();
            CreateSysLookProductReq createSysLookProductReq = new CreateSysLookProductReq();
            createSysLookProductReq.setProducts(products);
            createSysLookProductReq.setSkcs(data.get("skcs") == null? "":data.get("skcs").toString());
            createSysLookProductReq.setData(data);
            commonRequest.setRequestData(createSysLookProductReq);

            products = iMaterialApi.createSysLookProduct(commonRequest);
//            createSysLookProduct(products,data.get("skcs") == null? "":data.get("skcs").toString(),data);
            look.setProductList(products);
            String arcBrandId = "";
            String brandWeId = "";
            for (org.springcenter.material.api.dto.CreateLookReq.LookProduct product : products) {
                 arcBrandId = product.getArcBrandId();
                brandWeId = product.getBrandId();
            }
            match.setBrandId(StringUtils.isBlank(arcBrandId)?null:Long.parseLong(arcBrandId));
            look.setBrandId(StringUtils.isBlank(brandWeId)?null:Long.parseLong(brandWeId));
        }

        createLookContext.setLook(look);
        createLookContext.setMatch(match);

        return createLookContext;
    }

    private List<String> getYdLabelList(String styleTag, int type) {
        List<String> result = new ArrayList<>();
        List<Tag> list =  new ArrayList<>();
        if(type ==0){
            list  = tagMapper.selectByGroupId(4);
        }else{
            list = tagMapper.selectByGroupId(20);
        }
        if(StringUtils.isNotBlank(styleTag)){
            String[] sceneTagsName = styleTag.split(",");
            for (String tagName : sceneTagsName) {
                result.add(getYdLabelListId(tagName,type,list));
            }
        }
        return result;
    }

    private String getYdLabelListId(String tagName, int type,List<Tag> list) {
        for (Tag tag : list) {
            if(tagName.equals(tag.getName())){
                return tag.getId().toString();
            }
        }
        return null;
    }

//    /**
//     * 在es中查询获取数据
//     * @param products
//     * @param skcs
//     */
//    @Override
//    public void createSysLookProduct(List<com.jnby.material.api.dto.CreateLookReq.LookProduct> products, String skcs, Map<String,Object> data) {
//        if(StringUtils.isNotBlank(skcs)){
//            String[] skcCodes = skcs.split(",");
//            List<String> resultArray = new ArrayList<>();
//            List<String> skcCodeArray = Arrays.asList(skcCodes);
//            for (String skc : skcCodeArray) {
//                resultArray.add(skc.substring(0,skc.length()-3));
//            }
//
//            // 通过es in查询
//            List<BoxMProduct> list = productService.batchGetProductBySkcCodes(resultArray);
//            // 根据skccode分组
//            if(CollectionUtils.isNotEmpty(list)){
//                //根据sku分组
//                Map<Long, List<BoxMProduct>> collect = list.stream().collect(Collectors.groupingBy(BoxMProduct::getId));
//                for (Long spuId : collect.keySet()) {
//                    // 填充数据
//                    List<BoxMProduct> goodsSkcResps = collect.get(spuId);
//                    if(CollectionUtils.isNotEmpty(goodsSkcResps)){
//                        inside: for (BoxMProduct goodsSkcResp : goodsSkcResps) {
//                            if(skcCodeArray.contains(goodsSkcResp.getName() + goodsSkcResp.getColorno())){
//
//                                com.jnby.material.api.dto.CreateLookReq.LookProduct insertData = new com.jnby.material.api.dto.CreateLookReq.LookProduct();
//                                insertData.setProductCode(goodsSkcResp.getName());
//                                insertData.setProductId(Integer.parseInt(goodsSkcResp.getId()+""));
//                                insertData.setProductColor(goodsSkcResp.getColorno());
//                                insertData.setIsDel("否".equals(data.get("isDel").toString())?DelEnum.NORMAL.getCode():DelEnum.DELETE.getCode());
//                                insertData.setArcBrandId(goodsSkcResp.getcArcbrandId()+"");
//                                insertData.setBand(goodsSkcResp.getBand());
//                                insertData.setSkc(goodsSkcResp.getName()+goodsSkcResp.getColorno());
//                                insertData.setProductBrandId(goodsSkcResp.getBandid()+"");
//                                insertData.setYears(goodsSkcResp.getYear());
//                                insertData.setSeason(goodsSkcResp.getSmallseasonid()+"");
////            insertData.setMonth(goodsSkcResp.getmon);   es 中没有月份
//                                insertData.setBigCategory(goodsSkcResp.getBigclassid()+"");
//                                insertData.setSmallCategory(goodsSkcResp.getSmallclassid()+"");
//                                insertData.setBrandId(transferBoxBrandIdToBrandWeId(goodsSkcResp.getcArcbrandId()+""));
//
//                                insertData.setProductColorName(goodsSkcResp.getColorName());
//                                insertData.setProductImage(goodsSkcResp.getImgurl());
//                                insertData.setProductBrand(goodsSkcResp.getBrand());
//                                insertData.setProductName(goodsSkcResp.getValue());
//
//                                products.add(insertData);
//                                break inside;
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }

    //转换
    private String transferBoxBrandIdToBrandWeId(String arcBrandIds) {
        List<Map> maps = JSONObject.parseArray(brandWeIdConfig, Map.class);
        for (Map map : maps) {
            String arcBrandId = map.get("arcBrandId").toString();
            if(arcBrandIds.equalsIgnoreCase(arcBrandId)){
                return map.get("weid").toString();
            }
        }
        return null;
    }

    private void packetDataToLabels(String styleTag, Map<String, Object> data, List<String> labels,Integer type) {
        //type type 0 风格标签  1  场景标签
        //风格标签编码 	B08-A01     场景标签编码 B08-A04
        List<SysCategoryModel> result = new ArrayList<>();
        if(0 == type){
            getSysCategory("B08-A01",result);
        }else{
            //未定义 暂时使用这个
            getSysCategory("B08-A04",result);
        }

        if(StringUtils.isNotBlank(styleTag)){
            String[] sceneTagsName = styleTag.split(",");
            for (String tagName : sceneTagsName) {
//                SysLookLabel sysLookLabel = new SysLookLabel();
//                sysLookLabel.setIsDel("否".equals(data.get("isDel").toString())?DelEnum.NORMAL.getCode():DelEnum.DELETE.getCode());
//                sysLookLabel.setCreateTime(new Date());
//                sysLookLabel.setUpdateTime(new Date());
//                sysLookLabel.setLabelId(getlabelIdByName(tagName,type));
//                sysLookLabel.setType(type);
                labels.add(getlabelIdByName(tagName,type,result));
            }
        }
    }

    private String dealLookProduct(String skcs) {
        if(StringUtils.isNotBlank(skcs)){
            StringBuffer str = new StringBuffer();
            String[] split = skcs.split(",");
            for(int i = 0 ; i < split.length ; i++){
                if(i != split.length - 1){
                    str.append(split[i]);
                    str.append(",");
                }else{
                    str.append(split[i]);
                }
            }
            return str.toString();
        }
        return null;
    }


    private String getlabelIdByName(String tagName, Integer type, List<SysCategoryModel> result){
        for (SysCategoryModel sysCategoryModel : result) {
            if(sysCategoryModel.getName().equalsIgnoreCase(tagName)){
                return sysCategoryModel.getCode();
            }
        }
        return null;
    }

    @Override
    public List<SysCategoryModel> getSysCategory(String code, List<SysCategoryModel> result) {
        String key  = "sysCategory:" + code;
        String codeJson = RedisTemplateUtil.get(redisPoolUtil,key );
        if(StringUtils.isBlank(codeJson)){
            List<SysCategoryModel> sysCategoryModels = sysBaseAPI.queryAllDSysCategoryByCode(code);
            result.addAll(sysCategoryModels);
            RedisTemplateUtil.setex(redisPoolUtil,key,JSONObject.toJSONString(result),60*60);
        }else{
            result.addAll(JSONObject.parseArray(codeJson,SysCategoryModel.class));
        }
        return result;
    }

    @Override
    public void fixImportErrorData(String filePath) {

    }


    @Override
    @JRepeat(lockKey = "styling:add:cart", lockTime = 1000)
    public StylingBoxCart addStylingCartPreCheck(StylingProductReq req) {
        // 将商品id改成对应sku的商品id home商品的不同sku的商品id都不一样
        List<BoxMProduct> skuList = productService.selectGoodsListBySkuIds(Lists.newArrayList(req.getSkuId()));
        if (CollectionUtils.isEmpty(skuList)) {
            throw new BoxException(ErrorConstants.PARAMS_ERROR.getCode(), "未找到当前sku的信息" + req.getSkuId());
        }
        req.setProductId(skuList.get(0).getId());

        //查询是否添加过
        StylingBoxCart cart = new StylingBoxCart();
        cart.setUnionId(req.getUnionId());
        cart.setSysUserId(req.getSysUserId());
        cart.setProductId(req.getProductId());
        cart.setSkuId(req.getSkuId());
        cart.setStatus(StylingBoxCartStatus.NORMAL.getCode());
        List<StylingBoxCart> stylingBoxCarts = iStylingRepository.selectStylingCartListBySelective(cart);
        if (CollectionUtils.isNotEmpty(stylingBoxCarts)){
           return stylingBoxCarts.stream().findFirst().orElse(null);
        }
        BeanUtils.copyProperties(req, cart);
        //搭配购物车打标签
        if (cart.getProductId() != null && cart.getColorNo() != null){
            UserBoxSkc userBoxSkc = new UserBoxSkc();
            userBoxSkc.setUnionId(req.getUnionId());
            userBoxSkc.setOutId(req.getProductId().toString());
            userBoxSkc.setProductColorNo(req.getColorNo());
            StylingBoxCart.InnerRemark innerRemark = matchUserBoxSkc(userBoxSkc);
            cart.setRemark(innerRemark.toJson());
        }
        iStylingRepository.addStyleIngCart(cart);

        return cart;
    }

}
