package com.jnby.module.webapi;

import com.jnby.module.zto.entity.*;
import org.springframework.beans.factory.annotation.Value;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;

/**
 * <AUTHOR>
 * @Date:2022/9/16 13:49
 */
public interface IZtoHttpApi {

    @POST("/zto.open.createOrder")
    Call<ZtoCommonResponse<ZtoOrderRespDto>> createOrder(@Header("x-appKey") String appKey,
                                                         @Header("x-dataDigest") String dataDigest,
                                                         @Body ZtoOrderReqEntity ztoOrderReqEntity);

    @POST("/zto.open.cancelPreOrder")
    Call<ZtoCommonResponse<Object>> cancelPreOrder(@Header("x-appKey") String appKey,
                                                   @Header("x-dataDigest") String dataDigest,
                                                   @Body ZtoCancelReqEntity ztoCancelReqEntity);

    @POST("/zto.open.identifygw")
    Call<ZtoCommonResponse<ZtoIdentityRespDto>> identityExpress(@Header("x-appKey") String appKey,
                                                                @Header("x-dataDigest") String dataDigest,
                                                                @Body ZtoIdentityResEntity ztoIdentityResEntity);

}
