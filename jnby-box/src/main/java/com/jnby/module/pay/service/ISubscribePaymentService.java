package com.jnby.module.pay.service;


import com.github.binarywang.wxpay.bean.payscore.WxPayScoreResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.jnby.infrastructure.box.model.Payment;
import com.jnby.infrastructure.box.model.SubscribeOrder;
import com.jnby.infrastructure.box.model.WxvRentbill;
import com.jnby.module.pay.context.PayScorePaymentContext;
import com.jnby.module.pay.context.PaymentContext;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 订阅支付服务
 * <AUTHOR>
 */
public interface ISubscribePaymentService {

    /**
     * 通过订单ID获取支付渠道参数
     * @param context 上下文
     * @return
     */
    Map<String, String> getCharge(PaymentContext context);


    /**
     * 通过订单ID获取支付渠道参数
     * @param context 上下文
     * @return
     */
    Map<String, String> getChargeBySqb(PaymentContext context);


    /**
     * 微信支付分获取参数
     * @param payScorePaymentContext
     * @return
     */
    Map<String,String> payScoreGetCharge(PayScorePaymentContext payScorePaymentContext);


    /**
     *
     * @param orderno
     * @param queryId
     * @return
     * @throws WxPayException
     */
    WxPayScoreResult queryServiceOrder(String orderno,String  queryId) throws WxPayException;


    /**
     * 支付分订阅
     * @param data
     * @param request
     * @return
     */
    String wxvConfirmNotify(String data, HttpServletRequest request);


    /**
     * 处理微信订单
     * @param wxvRentbill
     */
    void dealWxvOrder(WxvRentbill wxvRentbill);


    /**
     * 完结租借账单
     * @param orderNo
     * @param totalAmount
     * @param rentFee
     * @return
     */
    WxvRentbill finishRentBill(String orderNo, int totalAmount, int rentFee);


    /**
     * 撤销支付分订单
     * @param orderId 订阅订单的id
     * @param totalAmount
     * @param rentFee
     */
    void cancelBill(String orderId, int totalAmount, int rentFee);



    /**
     * 完结支付分订单
     * @param orderId
     * @param returned
     */
    void finishBill(String orderId,Boolean returned);
}
