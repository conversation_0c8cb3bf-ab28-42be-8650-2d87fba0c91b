package com.jnby.module.pay.context;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 支付上下文
 * <AUTHOR>
 * @version 1.0
 * @date 4/25/21 2:29 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentContext {

    @NotNull(message = "支付订单ID不能为空")
    private String orderId;

    //支付总金额
    private BigDecimal totalAmount;

    /**
     * 支付类型，0订阅，1订单
     */
    private long payFor = 1;

    @NotNull(message = "用户ID不能为空")
    private String customerId;

    @NotNull(message = "微信OPENID不能为空")
    private String openId;
    /**
     * 0-在线支付,1-预存款支付,2-在线支付,3-线下支付,4-积分支付,5-微信支付,6-支付宝支付,7-微信退款
     */
    private long paymentType;
    /**
     * 类型(10:支付;20:退款) 退款不应该出现在这里，有个退款流水表
     */
    private long type = 10;

    /**
     * 通知URL
     */
    @NotNull(message = "支付回调不能为空")
    private String notifyUrl;


    private List<String> orderDetailsIds;

    /**
     * 支付渠道
     *  WEI_XIN_PAY(3, "微信"),
     *  ALIPAY(2, "支付宝"),
     */
    private String payChannel;


    /**
     * 是否使用收钱吧
     */
    private Boolean useShouQianBa;


    /**
     * 小程序id
     */
    private String appId;


    /**
     * 商户id
     */
    private String mchId;


    /**
     * 商品集合（微信代金券）
     */
    private List<OrderGoodsContext> orderGoodsContexts;

    // 花呗分期数
    private String hbFqNum;

    // 手续费 100表示商家付 0表示用户付
    private String hbFqSellerPercent = "100";

}
