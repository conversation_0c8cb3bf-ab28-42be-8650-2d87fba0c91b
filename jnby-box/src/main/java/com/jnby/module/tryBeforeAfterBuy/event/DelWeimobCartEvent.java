package com.jnby.module.tryBeforeAfterBuy.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jnby.base.context.CartDeleteCartGoodsContext;
import org.killbill.bus.api.BusEvent;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class DelWeimobCartEvent implements BusEvent {
    private final Long searchKey1;
    private final Long searchKey2;
    private final UUID userToken;
    private final CartDeleteCartGoodsContext cartDeleteCartGoodsContext ;


    @JsonCreator
    public DelWeimobCartEvent(@JsonProperty("cartDeleteCartGoodsContext") final CartDeleteCartGoodsContext cartDeleteCartGoodsContext,
                                   @JsonProperty("searchKey1") final Long searchKey1,
                                   @JsonProperty("searchKey2") final Long searchKey2,
                                   @JsonProperty("userToken") final UUID userToken){
        this.cartDeleteCartGoodsContext=cartDeleteCartGoodsContext;
        this.searchKey1 = searchKey1;
        this.searchKey2 = searchKey2;
        this.userToken = userToken;
    }

    @Override
    public Long getSearchKey1() {
        return this.searchKey1;
    }

    @Override
    public Long getSearchKey2() {
        return this.searchKey2;
    }

    @Override
    public UUID getUserToken() {
        return this.userToken;
    }



    public CartDeleteCartGoodsContext getCartDeleteCartGoodsContext(){
        return this.cartDeleteCartGoodsContext;
    }


}
