package com.jnby.module.customer.vip.service.impl;


import com.jnby.base.context.CreateVipByDemogicContext;
import com.jnby.base.context.PosMemberWithCreateCardContext;
import com.jnby.base.entity.AddPosMemberWithCreateCardResponse;
import com.jnby.module.customer.vip.service.IExternalDemoVipService;
import com.jnby.module.jic.service.IDemogicService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/***
 * 外部系统调用中间层
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExternalDemoVipServiceImpl implements IExternalDemoVipService {

    @Resource
    private IDemogicService iDemogicService;


    @Override
    public void createVipByDemo(CreateVipByDemogicContext createVipByDemogicContext) {
        log.info("中间层进行达摩开卡入参为： createVipByDemogicContext:{}",createVipByDemogicContext);

        PosMemberWithCreateCardContext posMemberWithCreateCardContext = new PosMemberWithCreateCardContext();
        posMemberWithCreateCardContext.setName(createVipByDemogicContext.getName());
        posMemberWithCreateCardContext.setMobile(createVipByDemogicContext.getMobile());
        String memberDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        posMemberWithCreateCardContext.setMemberDate(memberDate);
        posMemberWithCreateCardContext.setStoredCardStatus(1);
        posMemberWithCreateCardContext.setUnionid(createVipByDemogicContext.getUnionid());
        posMemberWithCreateCardContext.setSex(createVipByDemogicContext.getSex());
        posMemberWithCreateCardContext.setCompleteFlag(1);
        posMemberWithCreateCardContext.setWeid(iDemogicService.getWeidByBrandName(createVipByDemogicContext.getBrandName()));
        posMemberWithCreateCardContext.setStoreCode(createVipByDemogicContext.getStoreCode());
        posMemberWithCreateCardContext.setStoreName(createVipByDemogicContext.getStoreName());

        AddPosMemberWithCreateCardResponse addPosMemberWithCreateCardResponse = iDemogicService.addPosMemberWithCreateCard(posMemberWithCreateCardContext);

        if (addPosMemberWithCreateCardResponse == null) {
            throw new RuntimeException("达摩开卡异常");
        }
        if (!addPosMemberWithCreateCardResponse.getResult().equals("1")) {
            throw new RuntimeException("达摩开卡异常:" + addPosMemberWithCreateCardResponse.getCause());
        }
    }


}
