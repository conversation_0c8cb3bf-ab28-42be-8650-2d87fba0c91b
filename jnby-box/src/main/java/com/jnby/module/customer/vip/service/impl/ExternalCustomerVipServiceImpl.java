package com.jnby.module.customer.vip.service.impl;


import com.google.common.base.Preconditions;
import com.jnby.base.context.CreateVipByDemogicContext;
import com.jnby.base.context.MemberCardQueryContext;
import com.jnby.base.context.MemberQueryContext;
import com.jnby.base.context.PosMemberWithCreateCardContext;
import com.jnby.base.entity.AddPosMemberWithCreateCardResponse;
import com.jnby.base.entity.MemberCardEntity;
import com.jnby.base.repository.ICStoreRepository;
import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.base.service.ICustomerVipService;
import com.jnby.common.BoxException;
import com.jnby.infrastructure.bojun.mapper.HrEmployeeMapper;
import com.jnby.infrastructure.bojun.model.CStore;
import com.jnby.infrastructure.bojun.model.CVipType;
import com.jnby.infrastructure.bojun.model.CclientVip;
import com.jnby.infrastructure.bojun.model.HrEmployeeWithStoreEntity;
import com.jnby.module.customer.vip.service.IExternalCustomerVipService;
import com.jnby.module.jic.service.IDemogicService;
import com.jnby.module.jic.service.IUserVipService;
import com.jnby.module.shoppingGuide.account.service.IStoreTypeService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springcenter.core.tool.api.CustomerBaseResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/***
 * 外部系统调用中间层
 */
@Service
public class ExternalCustomerVipServiceImpl implements IExternalCustomerVipService {

    @Resource
    private IDemogicService iDemogicService;


    @Resource
    private IUserVipService iUserVipService;

    @Resource
    private ICStoreRepository icStoreRepository;

    @Resource
    private HrEmployeeMapper hrEmployeeMapper;

    @Resource
    private IStoreTypeService storeTypeService;

    @Resource
    private ICustomerVipService customerVipService;


    @Override
    public List<MemberCardEntity> clientVipVoList(String unionId) {
        MemberQueryContext memberQueryContext = new MemberQueryContext();
        memberQueryContext.setUnionId(unionId);
        memberQueryContext.setStatus("Y");
        CustomerBaseResponse<List<MemberCardEntity>> result = iUserVipService.getMemberCardList(memberQueryContext);

        if(result.isSuccess()){
            return result.getData();
        }
        throw new BoxException("获取外部服务用户卡列表信息异常" + result.getMsg());
    }

    @Override
    public List<MemberCardEntity> clientVipVoList(String unionId, String brandId) {
        MemberQueryContext memberQueryContext = new MemberQueryContext();
        memberQueryContext.setUnionId(unionId);
        memberQueryContext.setStatus("Y");
        memberQueryContext.setBrandId(brandId);
        CustomerBaseResponse<List<MemberCardEntity>> result = iUserVipService.getMemberCardList(memberQueryContext);

        if(result.isSuccess()){
            return result.getData();
        }
        throw new BoxException("获取外部服务用户卡列表信息异常" + result.getMsg());
    }

    @Override
    public List<CclientVip> convertClientVipList(String unionId) {
        List<MemberCardEntity> list = clientVipVoList(unionId);
        List<CclientVip> result = new ArrayList<>();

        for (MemberCardEntity clientVipVo : list) {
            CclientVip cclientVip = new CclientVip();
            cclientVip.setId(clientVipVo.getId());
            cclientVip.setVipname(clientVipVo.getNickName());
            cclientVip.setMobil(clientVipVo.getTel());
            cclientVip.setCardno(clientVipVo.getCardNo());
            cclientVip.setcViptypeId(clientVipVo.getVipTypeId());
            cclientVip.setSex(clientVipVo.getSex());
            if (StringUtils.isNotEmpty(clientVipVo.getBirthday())) {
                cclientVip.setBirthday(Integer.valueOf(clientVipVo.getBirthday()));
            }
            if(clientVipVo.getStoreId() !=null){
                cclientVip.setcStoreId(clientVipVo.getStoreId().longValue());
            }
            if(clientVipVo.getCustomerId() !=null){
                cclientVip.setcCustomerId(clientVipVo.getCustomerId().longValue());
            }
            if(clientVipVo.getCardIntegral() !=null){
                cclientVip.setIntegral(clientVipVo.getCardIntegral().longValue());
            }

            cclientVip.setWxopenid(clientVipVo.getOpenId());
            if(clientVipVo.getGroupId() !=null){
                cclientVip.setMemberno(clientVipVo.getGroupId().longValue());
            }
            if(clientVipVo.getSalesRepId()!=null){
                cclientVip.setSalesrepId(clientVipVo.getSalesRepId().longValue());
            }

            result.add(cclientVip);
        }

        return result;
    }


    @Override
    public void createVipByDemogic(CreateVipByDemogicContext createVipByDemogicContext) {
//        PosMemberWithCreateCardContext posMemberWithCreateCardContext = new PosMemberWithCreateCardContext();
//        posMemberWithCreateCardContext.setName(createVipByDemogicContext.getName());
//        posMemberWithCreateCardContext.setMobile(createVipByDemogicContext.getMobile());
//        String memberDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
//        posMemberWithCreateCardContext.setMemberDate(memberDate);
//        posMemberWithCreateCardContext.setStoredCardStatus(1);
//        posMemberWithCreateCardContext.setUnionid(createVipByDemogicContext.getUnionid());
//        posMemberWithCreateCardContext.setSex(createVipByDemogicContext.getSex());
//        posMemberWithCreateCardContext.setCompleteFlag(1);
//        posMemberWithCreateCardContext.setWeid(iDemogicService.getWeidByBrandName(createVipByDemogicContext.getBrandName()));
//
//        AddPosMemberWithCreateCardResponse addPosMemberWithCreateCardResponse = iDemogicService.addPosMemberWithCreateCard(posMemberWithCreateCardContext);
//
//        if (addPosMemberWithCreateCardResponse == null) {
//            throw new RuntimeException("达摩开卡异常");
//        }
//        if (!addPosMemberWithCreateCardResponse.getResult().equals("1")) {
//            throw new RuntimeException("达摩开卡异常:" + addPosMemberWithCreateCardResponse.getCause());
//        }
    }

    @Override
    public MemberCardEntity getClientVip(String unionId) {
        MemberCardQueryContext memberCardQueryContext = new MemberCardQueryContext();
        memberCardQueryContext.setUnionId(unionId);
        memberCardQueryContext.setStatus("Y");
        CustomerBaseResponse<MemberCardEntity> result = iUserVipService.getMemberCard(memberCardQueryContext);

        if(result.isSuccess()){
            return result.getData();
        }
        throw new BoxException("获取外部服务用户卡信息异常" + result.getMsg());
    }

    @Override
    public CclientVip getClientVipByCStoreIdAndFashionerPhone(String unionId, Long cStoreId, String fashionerPhone) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(unionId) && StringUtils.isNotEmpty(fashionerPhone) && cStoreId != null, "根据门店集合和导购手机号获取信息入参不存在");
        CStore cStore = Preconditions.checkNotNull(icStoreRepository.findCstoreById(cStoreId), "CStore门店信息不能为空");
        List<Long> storeIds = new ArrayList<>();
        // 品牌店
        if (cStore.getcUnionstoreId() == null) {
            storeIds.add(cStore.getId());
        } else {
            // 集合店
            CStore para2 = new CStore();
            para2.setcUnionstoreId(cStore.getcUnionstoreId());
            List<CStore> cStoreList = icStoreRepository.findCstoreListBySelective(para2);
            cStoreList.forEach(e -> {
                storeIds.add(e.getId());
            });
        }
        List<HrEmployeeWithStoreEntity> hrEmployeeWithStoreEntities = hrEmployeeMapper.selectListByPhoneAndStoreIds(fashionerPhone, storeIds);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(hrEmployeeWithStoreEntities), "根据门店集合和导购手机号获取信息不存在");
        List<Long> hrIds = hrEmployeeWithStoreEntities.stream().map(e -> e.getId()).collect(Collectors.toList());

        List<CclientVip> clientVips = convertClientVipList(unionId);
        if (CollectionUtils.isEmpty(clientVips)) {
            return null;
        }

        // 如果是非集合店且与用户关联的卡大于1张，根据门店属性取卡，否则走原逻辑
        CclientVip outLesCard = getOutLesCard(clientVips, cStore);
        if(outLesCard != null){
            return outLesCard;
        }

        Set<Long> hrIdSets = new HashSet<>();
        hrIdSets.addAll(hrIds);
        Set<Long> storeSets = new HashSet<>();
        storeSets.addAll(storeIds);

        CclientVip resultClientVip = null;
        for (CclientVip cclientVip : clientVips) {
            if (hrIdSets.contains(cclientVip.getSalesrepId())) {
                return cclientVip;
            }
            if (storeSets.contains(cclientVip.getcStoreId())) {
                resultClientVip = cclientVip;
            }
        }
        return resultClientVip;
    }

    private CclientVip getOutLesCard(List<CclientVip> clientVips,CStore cStore){
        if (cStore.getcUnionstoreId() == null) {
            List<CclientVip> vipList = clientVips.stream().filter(e -> cStore.getId().equals(e.getcStoreId())).collect(Collectors.toList());
            if(vipList.size() >1){
                // 奥莱门店判断，如果当前门店是奥莱门店，取奥莱卡，否则取另外的卡
                Map<Long, Boolean> map = storeTypeService.storeOutletTypeMap(Collections.singletonList(cStore.getId()));
                if(map.get(cStore.getId())){
                    List<CVipType> outlets = customerVipService.getVipTypeByDescription("OUTLETS");
                    if(CollectionUtils.isNotEmpty(outlets)){
                        List<Long> outletsIds = outlets.stream().map(CVipType::getId).collect(Collectors.toList());
                        return vipList.stream().filter(a -> outletsIds.contains(a.getcViptypeId())).findFirst().orElse(null);
                    }
                }
            }
        }
        return null;
    }
}
