package com.jnby.module.customer.joincategorylog.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: wangchun
 * @Date: 2021-09-14 16:37:57
 * @Description: 
 */
@TableName("B_JOIN_CATEGORY_LOG")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="BJoinCategoryLog对象", description="")
public class BJoinCategoryLog implements Serializable {

private static final long serialVersionUID = 1L;

        @TableId(value = "ID", type = IdType.UUID)
        private String id;

        @ApiModelProperty(value = "用户id")
        @TableField("USER_ID")
        private String userId;

        @ApiModelProperty(value = "源分类id")
        @TableField("B_ORI_CATEGORY_ID")
        private Long bOriCategoryId;

        @ApiModelProperty(value = "目标分类id")
        @TableField("B_DEST_CATEGORY_ID")
        private Long bDestCategoryId;

        @ApiModelProperty(value = "加入分类原因ID")
        @TableField("JOIN_CATEGORY_REASON_ID")
        private Long joinCategoryReasonId;

        @ApiModelProperty(value = "加入分类原因")
        @TableField("JOIN_CATEGORY_REASON")
        private String joinCategoryReason;

        @ApiModelProperty(value = "操作人")
        @TableField("OPERATOR_ID")
        private String operatorId;

        @TableField("CREATE_TIME")
        private Date createTime;

        @TableField("UPDATE_TIME")
        private Date updateTime;

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getId() {
            return id;
            }

        public BJoinCategoryLog
            setId(String id) {
            this.id = id;
                return this;
            }

    public String getUserId() {
            return userId;
            }

        public BJoinCategoryLog
            setUserId(String userId) {
            this.userId = userId;
                return this;
            }

    public Long getbOriCategoryId() {
            return bOriCategoryId;
            }

        public BJoinCategoryLog
            setbOriCategoryId(Long bOriCategoryId) {
            this.bOriCategoryId = bOriCategoryId;
                return this;
            }

    public Long getbDestCategoryId() {
            return bDestCategoryId;
            }

        public BJoinCategoryLog
            setbDestCategoryId(Long bDestCategoryId) {
            this.bDestCategoryId = bDestCategoryId;
                return this;
            }

    public Long getJoinCategoryReasonId() {
            return joinCategoryReasonId;
            }

        public BJoinCategoryLog
            setJoinCategoryReasonId(Long joinCategoryReasonId) {
            this.joinCategoryReasonId = joinCategoryReasonId;
                return this;
            }

    public String getJoinCategoryReason() {
            return joinCategoryReason;
            }

        public BJoinCategoryLog
            setJoinCategoryReason(String joinCategoryReason) {
            this.joinCategoryReason = joinCategoryReason;
                return this;
            }

    public Date getCreateTime() {
            return createTime;
            }

        public BJoinCategoryLog
            setCreateTime(Date createTime) {
            this.createTime = createTime;
                return this;
            }

    public Date getUpdateTime() {
            return updateTime;
            }

        public BJoinCategoryLog
            setUpdateTime(Date updateTime) {
            this.updateTime = updateTime;
                return this;
            }

@Override
public String toString() {
        return "BJoinCategoryLogModel{" +
                "id=" + id +
                ", userId=" + userId +
                ", bOriCategoryId=" + bOriCategoryId +
                ", bDestCategoryId=" + bDestCategoryId +
                ", joinCategoryReasonId=" + joinCategoryReasonId +
                ", joinCategoryReason=" + joinCategoryReason +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
        "}";
}
}
