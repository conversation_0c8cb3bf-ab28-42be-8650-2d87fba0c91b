package com.jnby.module.message.handle.impl;

import com.jnby.base.repository.IWeiXinFansRepository;
import com.jnby.base.service.ICustomerDetailsService;
import com.jnby.config.WxMpConfig;
import com.jnby.infrastructure.box.model.TemplateDataForWeiXin;
import com.jnby.infrastructure.box.model.WeiXinFans;
import com.jnby.module.message.entity.MsgKv;
import com.jnby.module.message.handle.ISendMsgHandle;
import com.jnby.module.message.handle.enums.SendMsgTypeEnum;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.enums.WxMpApiUrl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 公众号模版推送
 * first
 * keyword1
 * keyword2
 * ....
 * remark
 * appId;
 * path
 * <AUTHOR>
 * @Date 2021/10/14 4:19 下午
 * @Version 1.0
 */
@Service
@Slf4j
public class MpSendMsgHandle implements ISendMsgHandle {

    @Autowired
    private IWeiXinFansRepository iWeiXinFansRepository;

    @Autowired
    private ICustomerDetailsService customerDetailsService;

    @Override
    public String getType() {
        return SendMsgTypeEnum.WX.getType();
    }

    @Override
    public void SendMsg(String es_receiver, String templateCode, String es_content, String esparam) throws Exception {
        WeiXinFans weiXinFans = iWeiXinFansRepository.findWeiXinFansByOpenId(es_receiver);
        if(weiXinFans != null && StringUtils.isNotBlank(weiXinFans.getUnionid())){
            Boolean isBlack = customerDetailsService.checkUserIsBlack(null, weiXinFans.getUnionid());
            if(isBlack){
                return ;
            }
        }

        //解析数组对象
        List<MsgKv> msgKvs = MsgKv.decodeArr(es_content, MsgKv.class);
        TemplateDataForWeiXin templateDataForWeiXin = TemplateDataForWeiXin.New();

        String path = null;
        String appId = null;
        for (MsgKv item : msgKvs) {
            if ("appId".equals(item.getKey())){
                appId = item.getValue();
            }
            if ("path".equals(item.getKey())){
                path = item.getValue();
            }
            if (!"appId".equals(item.getKey()) || !"path".equals(item.getKey())){
                templateDataForWeiXin.add(item.getKey(),item.getValue());
            }
        }

        templateDataForWeiXin.setTemplate_id(templateCode);
        templateDataForWeiXin.setTouser(es_receiver);
        if (Objects.nonNull(appId) && Objects.nonNull(path)){
            templateDataForWeiXin.setMiniprogram(appId, path);
        }

        try {
            log.info("发送公众号消息模版 es_receiver = {}, content = {}", es_receiver, templateDataForWeiXin.toJson());
            WxMpConfig.getWxMpService().post(WxMpApiUrl.TemplateMsg.MESSAGE_TEMPLATE_SEND, templateDataForWeiXin.toJson());
        } catch (WxErrorException e) {
            log.error("发送公众号模版消息异常 templateCode = {}, e = {}", templateCode, e);
            //WxMpConfig.getWxMpService().getAccessToken(true);
            throw new Exception(e.getMessage());
        }
    }

    public static void main(String[] args) {
        List<MsgKv> msgKvs = MsgKv.decodeArr("[{\"key\":\"first\",\"value\":\"亲爱的顾客，您的订单状态已更改\"},{\"key\":\"keyword1\",\"value\":\"43535643656456456\"},{\"key\":\"keyword2\",\"value\":\"处理中\"},{\"key\":\"keyword3\",\"value\":\"蛋糕\"},{\"key\":\"remark\",\"value\":\"感谢您的使用！客服电话：12345\"},{\"key\":\"appId\",\"value\":\"wx2b785e2a311f5e84\"},{\"key\":\"path\",\"value\":\"pages/index_/index\"}]", MsgKv.class);
        TemplateDataForWeiXin templateDataForWeiXin = TemplateDataForWeiXin.New();

        String path = null;
        String appId = null;
        for (MsgKv item : msgKvs) {
            if ("appId".equals(item.getKey())){
                appId = item.getValue();
            }
            if ("path".equals(item.getKey())){
                path = item.getValue();
            }
            if (!"appId".equals(item.getKey()) || !"path".equals(item.getKey())){
                templateDataForWeiXin.add(item.getKey(),item.getValue());
            }
        }

        templateDataForWeiXin.setTemplate_id("97OBZO9j31IkEAnoSq7S0uHFGZ4-y2D6F-3b2NzTDaY");
        templateDataForWeiXin.setTouser("es_receiver");
        if (Objects.nonNull(appId) && Objects.nonNull(path)){
            templateDataForWeiXin.setMiniprogram(appId, path);
        }
        System.out.println(templateDataForWeiXin.toJson());
    }
}
