package com.jnby.module.process.context;

import com.jnby.infrastructure.bojun.model.CStore;
import com.jnby.infrastructure.box.model.BoxWithBLOBs;
import com.jnby.infrastructure.box.model.Fashioner;
import com.jnby.module.process.entity.BizNoInfoEntity;
import com.jnby.module.process.entity.ProcessProductEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/25 13:26
 * @description
 */
@Data
public class CreateProcessContext {
     @NotNull(message = "审批类型不能为空")
     private Long type;

     // 服务单号
     @NotNull(message = "服务单号不能为空")
     private String boxSn;

     // 服务单id
     @NotNull(message = "服务单id不能为空")
     private String boxId;

     // 门店id
     @NotNull(message = "门店不能为空")
     private Long storeId;

     // 商品列表
     @NotEmpty(message = "商品不能为空")
     private List<ProcessProductEntity> productList;

     // 结算金额
     @ApiModelProperty("结算金额,type=2时传入")
     private String totPrice;

     // 券面额
     @ApiModelProperty("券面额,type=2时传入")
     private String vouAmount;

     // 备注
     @ApiModelProperty("券面额,type=2时传入")
     private String memo;

     // 申请原因
     @ApiModelProperty("申请原因")
     private String reason;

     // 审批凭证
     @ApiModelProperty("审批凭证")
     private List<String> applyEvidence;

     private CStore cStore;

     private String linkId;

     private BoxWithBLOBs box;

     private Fashioner fashioner;

     private BizNoInfoEntity noInfo;
}
