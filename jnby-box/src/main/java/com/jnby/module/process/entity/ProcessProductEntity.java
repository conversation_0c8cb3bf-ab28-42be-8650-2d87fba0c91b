package com.jnby.module.process.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/19 13:58
 * @description
 */
@Data
public class ProcessProductEntity {

     @ApiModelProperty("服务单详情id")
     private String detailId;

     private String skuId;

     @ApiModelProperty("商品名称")
     private String name;

     @ApiModelProperty("sku")
     private String no;

     @ApiModelProperty("色号")
     private String colorNo;

     @ApiModelProperty("颜色")
     private String color;

     @ApiModelProperty("尺码")
     private String size;

     @ApiModelProperty("标准价")
     private String price;

     @ApiModelProperty("修改价")
     private String changePrice;

     @ApiModelProperty("商品图")
     private String img;

     @ApiModelProperty("折扣")
     private String discount;

     @ApiModelProperty("1改金额 2改折扣")
     private String changeType;

}
