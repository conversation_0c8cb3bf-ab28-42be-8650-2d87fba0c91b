package com.jnby.module.process.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/25 10:39
 * @description
 */
@Data
public class ProcessBusinessInfoEntity {

     private String url;

     private Head head;

     private Body body;

     private ProcessExtendInfoEntity extendInfoEntity;


     @Data
     public static class Body{
          @ApiModelProperty("正文标题")
          private String title;
          @ApiModelProperty("正文内容")
          private String content;
          @ApiModelProperty("表单内容")
          private List<Form> forms;
          @ApiModelProperty("内容链接图片")
          private String image;
          @ApiModelProperty("创建者")
          private String author;
          @ApiModelProperty("扩展信息")
          private String extInfo;
     }

     @Data
     public static class Form{
          @ApiModelProperty("key")
          private String key;
          @ApiModelProperty("value")
          private String value;
     }


     @Data
     public static class Head {
          @ApiModelProperty("背景颜色")
          private String bgColor;
          @ApiModelProperty("头部标题")
          private String text;
     }
}
