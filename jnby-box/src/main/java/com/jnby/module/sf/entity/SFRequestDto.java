package com.jnby.module.sf.entity;

import com.jnby.common.util.StrUtil;
import com.sf.csim.express.service.CallExpressServiceTools;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/10/22 10:05
 */
@Data
@Slf4j
@Configuration
@RefreshScope
public class SFRequestDto implements Serializable {

    private String partnerID;

    // 请求id
    private String requestID;

    // 接口服务码
    private String serviceCode;

    // 时间戳
    private String timestamp;

    // 请求JSON体
    private String msgData;

    // 验证码
    private String msgDigest;

    public SFRequestDto(){};


    public SFRequestDto(String serviceCode, String msgData){
        this.serviceCode = serviceCode;
        this.msgData = msgData;;
    };
    /**
     * @Description: 赋值顺丰账号信息
     * @Author: brian
     * @Date: 2021/10/22 14:38
     * @params: [clientCode, checkWord]
     * @return: com.jnby.module.sf.entity.SFRequestDto
     */
    public SFRequestDto build(String clientCode, String checkWord){
        this.partnerID = clientCode;
        this.requestID = StrUtil.getUUID();
        this.timestamp = String.valueOf(System.currentTimeMillis());
        CallExpressServiceTools client = CallExpressServiceTools.getInstance();
        try {
            this.msgDigest = client.getMsgDigest(msgData, timestamp, checkWord);
        }catch (UnsupportedEncodingException e){
            log.error("顺丰获取验证码异常,param = {}",msgData,e);
        }
        return this;
    }

    public Map<String,String> toMap(){
        Map<String,String> map = new HashMap<>();
        map.put("partnerID",partnerID);
        map.put("requestID",requestID);
        map.put("serviceCode",serviceCode);
        map.put("timestamp",timestamp);
        map.put("msgData",msgData);
        map.put("msgDigest",msgDigest);
        return map;
    }

}
