package com.jnby.module.sf.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/10/22 15:04
 */
@Data
public class SFQueryRouteRespDto implements Serializable {

    private List<RouteResp> routeResps;
    @Data
    public static class RouteResp implements Serializable {
        // 单号
        private String mailNo;
        // 路由列表
        private List<Route> routes;

        @Data
        public static class Route implements Serializable {
            // 地址
            private String acceptAddress;
            // 时间
            private String acceptTime;
            // 备注
            private String remark;
            // 返回码
            private String opCode;
        }
    }



}
