package com.jnby.module.calc.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 价格计算券数据
 */
@Data
public class CalcOrderVoucherEntity implements Serializable {

    private String billNo;

    private Integer rowNo;

    private String voucherNo;

    private String voucherType;

    private String voucherName;

    private Integer scRuleId;

    private String flag;

    private BigDecimal jeMk;

    private BigDecimal zkMk;

    private Integer qtyMax;

    private BigDecimal zk;

    private BigDecimal mz;

    private String remark;

    private String type;

    private String pp;

    private String isStack;

    private String isListLimit;
}
