package com.jnby.module.calc.entity;

import com.jnby.infrastructure.wx.model.*;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/4/13 15:24
 */
@Data
public class PromotionItemEntity {
    List<PromoStoreItem> promoStoreList;
    List<PromoDpitem> promoProductList;
    List<PromoVipItem> promoVipItemList;
    List<PromoVipSql> promoVipSqlList;
    List<PromoZditem> promoZdItemList;
    List<PromoGiftitem> promoGiftItemList;
}
