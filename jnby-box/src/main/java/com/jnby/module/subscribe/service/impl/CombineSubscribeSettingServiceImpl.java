package com.jnby.module.subscribe.service.impl;

import com.google.common.base.Preconditions;
import com.jnby.infrastructure.box.model.BSubscribeSetting;
import com.jnby.module.facade.context.StoreListContext;
import com.jnby.module.facade.service.IPackageStoresService;
import com.jnby.module.subscribe.service.IBSubscribeSettingService;
import com.jnby.module.subscribe.service.ICombineSubscribeSettingService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class CombineSubscribeSettingServiceImpl implements ICombineSubscribeSettingService {

    @Resource
    private IBSubscribeSettingService ibSubscribeSettingService;


    @Value("${subscribe.appid}")
    private String subscribeAppId;

    @Resource
    private IPackageStoresService iPackageStoresService;


    @Override
    public List<Integer> getAdaptStore(List<Integer> storeIds) {
        BSubscribeSetting bSubscribeSetting = Preconditions.checkNotNull(ibSubscribeSettingService.getSettingByAppId(subscribeAppId), "订阅3.0配置不存在");
        return this.getAdaptStore(bSubscribeSetting, storeIds);
    }



    @Override
    public List<Integer> getAdaptStore(BSubscribeSetting bSubscribeSetting, List<Integer> storeIds) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(bSubscribeSetting.getStoreIds()), "门店包不存在");

        /**
         * 1. 查询门店 根据门店包id 查询
         * 2. 处理逻辑
         */
        List<Integer> result = targetStoreIds(bSubscribeSetting.getStoreIds(), storeIds);

        // 指定门店类型
        if (Long.valueOf(0).equals(bSubscribeSetting.getStoreType())) {
            return result;
        }

        // 1排除门店
        if(Long.valueOf(1).equals(bSubscribeSetting.getStoreType())){
            Set<Integer> set = new HashSet<>();
            if(CollectionUtils.isNotEmpty(result)){
                set.addAll(result);
            }
            return storeIds.stream().filter(e->!set.contains(e)).collect(Collectors.toList());

        }
        throw new RuntimeException("订阅3.0配置有误");
    }


    /**
     * 获取外部符合的
     * @param storeIds
     * @return
     */
    @Override
   public List<Integer> targetStoreIds(String storePackageId, List<Integer> storeIds){
        StoreListContext storeListContext = new StoreListContext();
        storeListContext.setStorePackageId(Integer.valueOf(storePackageId));
        storeListContext.setStoreIdList(storeIds);
        List<Integer> result = iPackageStoresService.storeListById(storeListContext);
        return result;
    }
}
