package com.jnby.module.subscribe.service;

import com.jnby.application.minapp.dto.request.SiteSubPlanReqDto;
import com.jnby.infrastructure.box.model.BSubscribePlan;

import java.util.List;

public interface ICombineSubLogService {


    /**
     * 创建 log
     * @param createSubLogContext
     */
    void createSubLogCxt(CreateSubLogContext createSubLogContext);


    /**
     * 批量创建log
     * @param createSubLogContextList
     */
    void batchCreateSubLogCxt(List<CreateSubLogContext> createSubLogContextList);


    /**
     * 封装失效的订阅计划
     * @param bSubscribePlans
     * @return
     */
    List<CreateSubLogContext> buildExpireBySubscribePlan(List<BSubscribePlan> bSubscribePlans);


    /**
     * 封装更改节点
     * @param siteSubPlanReqDto
     * @return
     */
    CreateSubLogContext buildChangeSubPlan(SiteSubPlanReqDto siteSubPlanReqDto);
}
