package com.jnby.module.subscribe.event.handler;

import com.alibaba.fastjson.JSONObject;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.jnby.common.util.RedissonUtil;
import com.jnby.infrastructure.box.model.BSubscribePlan;
import com.jnby.module.subscribe.event.DefaultSiteSubscribePlanEvent;
import com.jnby.module.subscribe.service.IBSubscribePlanService;
import com.jnby.module.subscribe.service.ICombineSubscribePlanService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class IDefaultSiteSubscribePlanHandler {

    @Autowired
    private ICombineSubscribePlanService iCombineSubscribePlanService;

    @Autowired
    private RedissonUtil redissonUtil;

    @Resource
    private IBSubscribePlanService ibSubscribePlanService;

    @AllowConcurrentEvents
    @Subscribe
    @NewSpan
    public void processMyEvent(final DefaultSiteSubscribePlanEvent event) {
        String key ="DefaultSiteSubscribePlan:" + event.getDefaultSiteSubscribePlanContext().getSubId();
        if(!redissonUtil.tryLock(key)){
            throw new RuntimeException("异步处理正式卡订阅计划 subId:"+event.getDefaultSiteSubscribePlanContext().getSubId()+"正在处理中");
        }
        try {
            log.info("订阅3.0 正式卡设置订阅计划subId:{} event:{}", event.getDefaultSiteSubscribePlanContext().getSubId(), JSONObject.toJSONString(event));
            List<BSubscribePlan> plans = ibSubscribePlanService.getListBySubId(event.getDefaultSiteSubscribePlanContext().getSubId());
            if (CollectionUtils.isNotEmpty(plans)) {
                log.info("订阅计划subId:{} 已经存在不进行处理", event.getDefaultSiteSubscribePlanContext().getSubId());
                return;
            }
            iCombineSubscribePlanService.defaultSiteSubscribePlan(event.getDefaultSiteSubscribePlanContext());
        } finally {
            redissonUtil.unlock(key);
        }
    }
}
