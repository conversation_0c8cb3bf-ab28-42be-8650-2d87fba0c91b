package com.jnby.module.subscribe.event.bus;

import com.jnby.base.service.IBusEventsService;
import com.jnby.module.order.event.BaseEventBus;
import com.jnby.module.subscribe.event.DefaultSiteSingleSubscribePlanEvent;
import com.jnby.module.subscribe.event.UnSubscribeEvent;
import com.jnby.module.subscribe.event.handler.IDefaultSiteSingleSubscribePlanHandler;
import com.jnby.module.subscribe.event.handler.IUnSubscribeHandler;
import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date:2023/9/15 13:55
 */
@Service
@Slf4j
public class IUnSubscribeEventBus implements InitializingBean, BaseEventBus<UnSubscribeEvent> {

    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private IUnSubscribeHandler iUnSubscribeHandler;

    @Autowired
    private IBusEventsService iBusEventsService;


    @Override
    public void post(UnSubscribeEvent unSubscribeEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(unSubscribeEvent);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(iUnSubscribeHandler);
    }
}