package com.jnby.module.subscribe.event.bus;

import com.jnby.base.service.IBusEventsService;
import com.jnby.module.order.event.BaseEventBus;
import com.jnby.module.subscribe.event.DefaultSiteSingleSubscribePlanEvent;

import com.jnby.module.subscribe.event.handler.IDefaultSiteSingleSubscribePlanHandler;
import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date:2023/9/15 13:55
 */
@Service
@Slf4j
public class IDefaultSiteSingleSubscribePlanEventBus implements InitializingBean, BaseEventBus<DefaultSiteSingleSubscribePlanEvent> {

    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private IDefaultSiteSingleSubscribePlanHandler iDefaultSiteSubscribePlanHandler;

    @Autowired
    private IBusEventsService iBusEventsService;


    @Override
    public void post(DefaultSiteSingleSubscribePlanEvent defaultSiteSingleSubscribePlanEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(defaultSiteSingleSubscribePlanEvent);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(iDefaultSiteSubscribePlanHandler);
    }
}