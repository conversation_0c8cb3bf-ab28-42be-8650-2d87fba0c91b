package com.jnby.module.subscribe.context;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * 消耗订阅计划 上下文
 */
@Data
public class ConsumeSubscribePlanContext {
    @ApiModelProperty(value = "订阅id")
    @NotEmpty(message = "订阅id不能为空")
    private String subId;

    @ApiModelProperty(value = "订阅计划id")
    @NotEmpty(message = "订阅计划id不能为空")
    private String subscribePlanId;


    @ApiModelProperty(value = "askBoxId")
    @NotEmpty(message = "askBoxId不能为空")
    private String askBoxId;




    /**
     * 操作类型（0系统 1后台 2导购 3用户）默认0
     */
    @ApiModelProperty(value = "操作类型（0系统 1后台 2导购 3用户）默认0")
    private Long operateType;


    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operatePeople;


}
