package com.jnby.module.subscribe.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.application.admin.dto.request.UnSubscribeReq;
import com.jnby.application.minapp.dto.request.CalSubMoneyReqDto;
import com.jnby.application.minapp.dto.request.GetCardPriceWithOthersReq;
import com.jnby.application.minapp.dto.request.RenewConditionReqDto;
import com.jnby.application.minapp.dto.response.BoxPlusCenterHeadResp;
import com.jnby.application.minapp.dto.response.HasVoucherSubResp;
import com.jnby.application.openapi.request.PosSubPlanReq;
import com.jnby.application.openapi.response.PosSubscribePlanResp;
import com.jnby.base.context.QueryContext;
import com.jnby.base.context.RefundContext;
import com.jnby.base.context.SubtractUserIntegralContext;
import com.jnby.base.entity.*;
import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.base.service.*;
import com.jnby.common.enums.*;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.common.leaf.support.IdWorkLeaf;
import com.jnby.common.shouqianba.BizCommonResponse;
import com.jnby.common.shouqianba.SqbWithBizCommonResponse;
import com.jnby.common.util.DateUtil;
import com.jnby.common.util.IdLeaf;
import com.jnby.infrastructure.bojun.mapper.CStoreMapper;
import com.jnby.infrastructure.bojun.mapper.EmployeeBaseMapper;
import com.jnby.infrastructure.bojun.mapper.HrEmployeeMapper;
import com.jnby.infrastructure.bojun.model.CStore;
import com.jnby.infrastructure.bojun.model.EmployeeBase;
import com.jnby.infrastructure.bojun.model.HrEmployee;
import com.jnby.infrastructure.box.mapper.*;
import com.jnby.infrastructure.box.model.*;
import com.jnby.infrastructure.wx.mapper.BrandMappingMapper;
import com.jnby.infrastructure.wx.mapper.VoucherBaseMapper;
import com.jnby.infrastructure.wx.model.BrandMapping;
import com.jnby.infrastructure.wx.model.VoucherBase;
import com.jnby.module.alipay.service.IBOauthInfoService;
import com.jnby.module.credit.enums.AgreementStatusEnum;
import com.jnby.module.credit.service.IBCustomerZhimaCreditService;
import com.jnby.module.commonPay.service.IHandlePayService;
import com.jnby.module.cusRights.entity.NextNodeGiftEntity;
import com.jnby.module.cusRights.service.ICusRightsService;
import com.jnby.module.customer.expand.entity.BCustomerExpand;
import com.jnby.module.customer.expand.service.IBCustomerExpandService;
import com.jnby.module.customer.vip.service.IExternalCustomerIntegralService;
import com.jnby.module.customer.vip.service.IExternalCustomerVipService;
import com.jnby.module.facade.context.UnSingleSubscribeContext;
import com.jnby.module.facade.context.UnSubscribeContext;
import com.jnby.module.facade.service.INewRightsV3Service;
import com.jnby.module.jic.service.ISqbProxyService;
import com.jnby.module.order.enums.SubscribeOrderStatusEnum;
import com.jnby.module.order.enums.SubscribeOrderTypeEnum;
import com.jnby.module.order.repository.IBoxRepository;
import com.jnby.module.order.repository.IOrderRepository;
import com.jnby.module.order.service.IRefundService;
import com.jnby.module.order.service.box.ISubscribeService;
import com.jnby.module.pay.context.PaymentContext;
import com.jnby.module.pay.enums.PaymentStatusEnum;
import com.jnby.module.pay.service.IThreeSubscribePaymentService;
import com.jnby.module.subscribe.context.InvalidSiteSubscribePlanContext;
import com.jnby.module.subscribe.context.SubPayNotifyContext;
import com.jnby.module.subscribe.context.SubscribeContext;
import com.jnby.module.subscribe.entity.CardPriceWithOthersEntity;
import com.jnby.module.subscribe.entity.RenewConditionEntity;
import com.jnby.module.subscribe.entity.SubWithSubOrderInfoEntity;
import com.jnby.module.subscribe.entity.SubscribeEntranceEntity;
import com.jnby.module.subscribe.event.SingleUnSubRefundEvent;
import com.jnby.module.subscribe.event.UnSubscribeEvent;
import com.jnby.module.subscribe.event.bus.ISingleUnSubRefundBus;
import com.jnby.module.subscribe.event.bus.IUnSubscribeEventBus;
import com.jnby.module.subscribe.service.*;
import com.jnby.module.wx.service.IBrandMappingService;
import lombok.extern.slf4j.Slf4j;
import oracle.jdbc.proxy.annotation.Pre;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.marketing.api.dto.SubMemberCardRightsResp;
import org.springcenter.marketing.api.enums.RightsTypeEnum;
import org.springcenter.marketing.api.userule.ApplyForBoxRights;
import org.springcenter.paycenter.api.req.RefundReq;
import org.springcenter.paycenter.api.resp.RefundResp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CombineSubscribeServiceImpl implements ICombineSubscribeService {

    /**
     * 订阅计划海报跳转链接
      */
    private static final String SUBSCRIBE_PLAN_POSTER_URL = "%s/supershopper/home?userid=%s@jnby&service=/member-list";

    /**
     * 订阅计划重新开通链接
     */
    private static final String SUBSCRIBE_PLAN_REOPEN_URL = "%s/supershopper/home?userid=%s@jnby&service=/member-list";

    /**
     * 服务单详情链接
     */
    private static final String SERVICE_ORDER_DETAIL_URL = "%s/supershopper/home?userid=%s@jnby&service=";


    /**
     * 订阅回调
     */
    @Value("${three.subscribe.sqb.notify.url}")
    private String subscribeSqbNotifyUrl;



    @Value("${three.subscribe.common.pay.notify.url}")
    private String subscribeCommonPayNotifyUrl;

    /**
     *  jnby》less〉速写》jnbybyJNBY〉蓬马〉Apn73》 Jnbyhome
     */
    String[] brandIdStrArr = {
            "2738574294","2822095692","2504948039","4","6924108367","15","8348044436"
    };

    /**
     * arcId排序
     */
    String[] arcIdStrArr = {
            "2","5","3","4","12","57","17"
    };



    @Resource
    private SubscribeOrderMapper subscribeOrderMapper;

    @Resource
    private IExternalCustomerVipService iExternalCustomerVipService;

    @Resource
    private IBSubscribeSettingService ibSubscribeSettingService;

    @Resource
    private INewRightsV3Service iNewRightsV3Service;

    @Resource
    private IBSubscribeInfoService ibSubscribeInfoService;

    @Resource
    private IExternalCustomerIntegralService iExternalCustomerIntegralService;

    @Resource
    private IdLeafService idLeafService;

    @Resource
    private ICombineSubscribePlanService iCombineSubscribePlanService;

    @Resource
    private ICustomerDetailsService iCustomerDetailsService;

    @Autowired
    @Qualifier("boxTransactionTemplate")
    private TransactionTemplate template;

    @Resource
    private IBSubscribePlanService ibSubscribePlanService;

    @Resource
    private CustomerDetailsMapper customerDetailsMapper;


    @Resource
    private IFashionerService iFashionerService;

    @Resource
    private IBoxRepository iBoxRepository;

    @Resource
    private IBSubscribePlanNodeService ibSubscribePlanNodeService;

    @Resource
    private ICusRightsService iCusRightsService;

    @Resource
    private ICustomerDetailsRepository iCustomerDetailsRepository;

    @Resource
    private ICombineSubscribeSettingService iCombineSubscribeSettingService;

    @Resource
    private IBCustomerExpandService ibCustomerExpandService;


    @Resource
    private IThreeSubscribePaymentService iThreeSubscribePaymentService;

    @Resource
    private PaymentMapper paymentMapper;


    @Resource
    private CStoreMapper cStoreMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private EmployeeBaseMapper employeeBaseMapper;

    @Resource
    private HrEmployeeMapper hrEmployeeMapper;


    @Resource
    private BrandMappingMapper brandMappingMapper;

    @Resource
    private IBrandMappingService iBrandMappingService;

    @Resource
    private IOrderRepository iOrderRepository;

    @Resource
    private BoxDetailsMapper boxDetailsMapper;

    @Resource
    private IBSubscribeTaskService ibSubscribeTaskService;

    @Resource
    private ISingleUnSubRefundBus iSingleUnSubRefundBus;

    /**
     * 单位 元
     */
    private BigDecimal integralValueToMoney = BigDecimal.valueOf(0.05);

    @Resource
    private ISqbProxyService iSqbProxyService;

    @Resource
    private IntegralRecordMapper integralRecordMapper;

    @Resource
    private IUnSubscribeEventBus iUnSubscribeEventBus;

    @Resource
    private ICustomerAskBoxService iCustomerAskBoxService;

    @Resource
    private BoxMapper boxMapper;


    @Resource
    private IBOauthInfoService ibOauthInfoService;

    @Resource
    private IBCustomerZhimaCreditService ibCustomerZhimaCreditService;

    @Autowired
    private IRefundService refundService;

    @Autowired
    private IHandlePayService handlePayService;


    @Resource
    private VoucherBaseMapper voucherBaseMapper;



    @Value("${subAwardId}")
    private String subAwardId;

    @Resource
    private ICombineSubLogService iCombineSubLogService;


    /**
     * 1. 订阅3.0 是否有权限
     */
    @Override
    public Boolean validateSubscribe(String unionId, String appId) {
        BSubscribeSetting bSubscribeSetting = Preconditions.checkNotNull(ibSubscribeSettingService.getSettingByAppId(appId), "配置不存在");
        // 查询是否是导购用户  导购用户后面就不用查询了 直接不能订阅
        Boolean flag = checkUserIsSales(unionId);
        if(flag){
            return !flag;
        }

        List<MemberCardEntity> memberCardEntities = iExternalCustomerVipService.clientVipVoList(unionId);

        if (CollectionUtils.isEmpty(memberCardEntities)) {
            return false;
        }
        return validateNewSubscribe(bSubscribeSetting, memberCardEntities);
    }

    // 校验当前用户是否是导购账号  是 true  否  false
    public Boolean checkUserIsSales(String unionId) {
        CustomerDetails paramsCustomer = new CustomerDetails();
        paramsCustomer.setUnionid(unionId);
        List<CustomerDetails> customerDetails = customerDetailsMapper.selectListBySelective(paramsCustomer);
        if(CollectionUtils.isNotEmpty(customerDetails)){
            // 根据手机号查询employee_base  linkSource 为  OA  并且  职位 为   店员或者店长
            String phone = customerDetails.get(0).getPhone();
            if(StringUtils.isNotBlank(phone)){
                // employee_base进行查询
                List<EmployeeBase> list = employeeBaseMapper.selectByPhoneAndPositionAndLinkSource(phone);
                if(CollectionUtils.isNotEmpty(list)){
                    log.info("当前用户为导购小号  不允许开通订阅3.0 unionid= "+unionId);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 订阅3.0 是否有权限
     *
     * @param bSubscribeSetting  配置
     * @param memberCardEntities 用户卡
     * @return
     */
    Boolean validateNewSubscribe(BSubscribeSetting bSubscribeSetting, List<MemberCardEntity> memberCardEntities) {
        if (CollectionUtils.isEmpty(memberCardEntities)) {
            return false;
        }
        List<Integer> userStoreIds = memberCardEntities.stream().filter(e -> !Objects.isNull(e.getStoreId())).map(MemberCardEntity::getStoreId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(userStoreIds)){
            return false;
        }

        MemberCardEntity groupVipTypeNameEntity = memberCardEntities.stream().filter(e -> {
            return StringUtils.isNotEmpty(e.getGroupVipTypeName());
        }).findFirst().orElse(null);

        if (groupVipTypeNameEntity == null || StringUtils.isEmpty(groupVipTypeNameEntity.getGroupVipTypeName())) {
            return false;
        }
        String groupVipTypeName = groupVipTypeNameEntity.getGroupVipTypeName();

        List<Integer> targetStoreIds = iCombineSubscribeSettingService.getAdaptStore(bSubscribeSetting, userStoreIds);
        Boolean flag = memberCardEntities.stream().anyMatch(e -> {
            return targetStoreIds.contains(e.getStoreId());
        });
        if (flag) {
            List<SubMemberCardRightsResp> subMemberCardRightsResps = iNewRightsV3Service.getCardConfigByCardTypeAndSuitCardLevel(1, iNewRightsV3Service.convertSuitCardLevel(groupVipTypeName));
            return CollectionUtils.isNotEmpty(subMemberCardRightsResps);
        }
        return flag;
    }


    /**
     * 订阅入口
     */
    @Override
    public SubscribeEntranceEntity subscribeEntrance(String unionId, String appId) {
        SubscribeEntranceEntity subscribeEntranceEntity = new SubscribeEntranceEntity();
        subscribeEntranceEntity.setFlag(false);
        // 展示价格
        subscribeEntranceEntity.setShowType(0);
        List<BSubscribeInfo> bSubscribeInfos = ibSubscribeInfoService.getUserEffectSingleCardByUnionIds(Lists.newArrayList(unionId));
        if (bSubscribeInfos.stream().anyMatch(e->!Long.valueOf(4).equals(e.getPayWay()))) {
            subscribeEntranceEntity.setFlag(true);
            subscribeEntranceEntity.setShowType(1);
            return subscribeEntranceEntity;
        }

        List<MemberCardEntity> memberCardEntities = iExternalCustomerVipService.clientVipVoList(unionId);
        String groupVipName = "银卡";
        if (CollectionUtils.isNotEmpty(memberCardEntities) && memberCardEntities.stream().anyMatch(e -> StringUtils.isNotEmpty(e.getGroupVipTypeName()))) {
            groupVipName = memberCardEntities.stream().filter(e -> StringUtils.isNotEmpty(e.getGroupVipTypeName())).findFirst().get().getGroupVipTypeName();
        }
        List<SubMemberCardRightsResp> subMemberCardRightsResps = iNewRightsV3Service.getCardConfigByCardTypeAndSuitCardLevel(2, iNewRightsV3Service.convertSuitCardLevel(groupVipName));
        if (CollectionUtils.isEmpty(subMemberCardRightsResps)) {
            log.info("单次卡配置不存在");
            return subscribeEntranceEntity;
        }
        subscribeEntranceEntity.setFlag(true);
        subscribeEntranceEntity.setPrice(subMemberCardRightsResps.get(0).getPrice());
        subscribeEntranceEntity.setBMemberCardId(subMemberCardRightsResps.get(0).getBMemberCardId());
        return subscribeEntranceEntity;
    }

    @Override
    public  List<SubMemberCardRightsResp> unregisterGetCardInfo(String appId) {
        return  iNewRightsV3Service.getCardConfigByCardTypeAndSuitCardLevel(Lists.newArrayList(1, 2), 0);
    }

    /**
     * 获取用户积分
     *
     * @param unionId
     * @return
     */
    public Long getIntegral(String unionId) {
        GetUserIntegralEntity getUserIntegralEntity = Preconditions.checkNotNull(iExternalCustomerIntegralService.getUserIntegral(unionId), "积分信息不存在");
        return getUserIntegralEntity.getTotalIntegral();
    }


    @Override
    public void checkPreSub(SubscribeContext subscribeContext) {
        // 正式卡
        if (Integer.valueOf(1).equals(subscribeContext.getCardType())) {
            List<BSubscribeInfo> bSubscribeInfos = ibSubscribeInfoService.selectUserEffectSubInfoByUnionIds(Lists.newArrayList(subscribeContext.getUnionId()));
            Preconditions.checkArgument(CollectionUtils.isEmpty(bSubscribeInfos), "订阅有效信息存在，不能订阅");
            if(Long.valueOf(4).equals(subscribeContext.getPayWay())){
                throw new RuntimeException("正式卡支付方式非法");
            }
        }
        // 单次卡
        if (Integer.valueOf(2).equals(subscribeContext.getCardType())) {
            if (Long.valueOf(5).equals(subscribeContext.getPayWay())) {
                throw new RuntimeException("单次卡支付方式非法");
            }
            // 判断单次盒子是否组合支付 如果是组合支付
            if (!Long.valueOf(3).equals(subscribeContext.getPayWay())) {
                log.info("单次卡支付方式 不能组合支付");
                throw new RuntimeException("单次卡支付方式非法");
            }

            // 单次卡支付方式 只能微信支付
//            if (!Long.valueOf(1).equals(subscribeContext.getPayWay())) {
//                log.info("单次卡支付方式 只能微信支付");
//                throw new RuntimeException("单次卡支付方式非法");
//            }
        }

        if (StringUtils.isNotEmpty(subscribeContext.getBindFashionerId())) {
            Preconditions.checkNotNull(iFashionerService.findById(subscribeContext.getBindFashionerId()), "搭配师信息不存在");
        }
        if (Long.valueOf(2).equals(subscribeContext.getPayWay()) || Long.valueOf(3).equals(subscribeContext.getPayWay())) {
            if (subscribeContext.getPayIntegral() <= 0) {
                throw new RuntimeException("非法积分");
            }
            Long integral = getIntegral(subscribeContext.getUnionId());
            if (integral < subscribeContext.getPayIntegral()) {
                throw new RuntimeException("积分不足");
            }
        }
        List<BSubscribePlanNode> bSubscribePlanNodes = ibSubscribePlanNodeService.getListByAppId(subscribeContext.getAppId());
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(bSubscribePlanNodes) && CollectionUtils.size(bSubscribePlanNodes) >= 6, "订阅默认节点配置有误");
    }

    @Override
    public void packageSubscribeContext(SubscribeContext subscribeContext) {
        List<MemberCardEntity> memberCardEntities = iExternalCustomerVipService.clientVipVoList(subscribeContext.getUnionId());
        Integer userCardLevel = 0;
        if (CollectionUtils.isNotEmpty(memberCardEntities)){
            userCardLevel = iNewRightsV3Service.convertSuitCardLevel(memberCardEntities.get(0).getGroupVipTypeName());
        }
        subscribeContext.setUserCardLevel(userCardLevel);

        CustomerDetails customerDetails = Preconditions.checkNotNull(iCustomerDetailsService.selectCustomerIdByUnionId(subscribeContext.getUnionId()), "用户信息不存在");
        // 非免费订阅 校验openId
        if (!Long.valueOf(4).equals(subscribeContext.getPayWay())) {
            Preconditions.checkArgument(StringUtils.isNotEmpty(customerDetails.getOpenid()), "用户信息openid");
        }

        subscribeContext.setUnionId(customerDetails.getUnionid());
        subscribeContext.setCustId(customerDetails.getId());
        subscribeContext.setCustPhone(customerDetails.getPhone());
        subscribeContext.setCustomerName(customerDetails.getNickName());
        subscribeContext.setType(0L);

        // 邀请订阅（特殊处理）
        if (StringUtils.isNotEmpty(subscribeContext.getRecommender())) {
            subscribeContext.setType(1L);
            subscribeContext.setInvitePeople(subscribeContext.getRecommender());
        }
        if (StringUtils.isNotEmpty(subscribeContext.getBindFashionerId())) {
            handleBindFashioner(subscribeContext, customerDetails);
        }
        List<SubMemberCardRightsResp> subMemberCardRightsResps = iNewRightsV3Service.getCardConfigByCardTypeAndSuitCardLevel(subscribeContext.getCardType(),userCardLevel);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(subMemberCardRightsResps), "权益卡不存在");
        subscribeContext.setSubDays(subMemberCardRightsResps.get(0).getDays());
        subscribeContext.setGoodsId(subMemberCardRightsResps.get(0).getBMemberCardId());
        // 商品价格
        if (subMemberCardRightsResps.get(0).getMarkThePrice().compareTo(BigDecimal.ZERO) > 0) {
            // 转换元
            subscribeContext.setGoodsPrice(subMemberCardRightsResps.get(0).getMarkThePrice().divide(new BigDecimal(100)));
        }
        if (Long.valueOf(4).equals(subscribeContext.getPayWay())) {
            subscribeContext.setPayPrice(BigDecimal.ZERO);
        }
        subscribeContext.setTodayTime(new Date());
        // 是否续订
        Boolean hasRenew = subscribeContext.getHasRenew();
        if (hasRenew) {
            RenewConditionReqDto renewConditionReqDto = new RenewConditionReqDto();
            renewConditionReqDto.setCustId(subscribeContext.getCustId());
            renewConditionReqDto.setUserCardLevel(subscribeContext.getUserCardLevel());
            renewConditionReqDto.setCardType(subscribeContext.getCardType());
            // 续订校验
            RenewConditionEntity renewConditionEntity = renewCondition(renewConditionReqDto);
            Preconditions.checkArgument(renewConditionEntity.getFlag(), "续订条件不满足");
            // 微信支付 校验价格
            if (Long.valueOf(1).equals(subscribeContext.getPayWay())) {
                log.info("支付钱(分)：" + subscribeContext.getPayPrice().multiply(new BigDecimal(100)).toString() + " 远程钱（分）：" + subMemberCardRightsResps.get(0).getPrice() + " 远程折扣最终价格（分）:" + subMemberCardRightsResps.get(0).getDisPrice());
                Preconditions.checkArgument(subscribeContext.getPayPrice().multiply(new BigDecimal(100)).compareTo(subMemberCardRightsResps.get(0).getDisPrice()) == 0, "支付价格非法");
                return;
            }
            // 积分
            if(Long.valueOf(2).equals(subscribeContext.getPayWay())) {
                log.info("支付积分：" + subscribeContext.getPayIntegral().toString() + " 远程折扣积分：" + subMemberCardRightsResps.get(0).getDisCardIntegralExchange());
                Preconditions.checkArgument(subscribeContext.getPayIntegral().compareTo(Long.valueOf(subMemberCardRightsResps.get(0).getDisCardIntegralExchange().toString())) == 0, "支付价格非法");
                return;
            }
            // 组合
            if (Long.valueOf(3).equals(subscribeContext.getPayWay())) {
                log.info("支付钱(分)：" + subscribeContext.getPayPrice().multiply(new BigDecimal(100)).toString() + "  支付积分" + subscribeContext.getPayIntegral() + " 远程折扣钱（分）：" + subMemberCardRightsResps.get(0).getDisPrice());
                BigDecimal payPrice = subscribeContext.getPayPrice().multiply(new BigDecimal(100));
                BigDecimal payIntegralToMoney = BigDecimal.valueOf(subscribeContext.getPayIntegral()).multiply(integralValueToMoney).multiply(BigDecimal.valueOf(100));
                BigDecimal result = payPrice.add(payIntegralToMoney);
                log.info("支付钱(分)：" + payPrice.toString() + " 支付积分兑换钱(分):" + payIntegralToMoney.toString() + " 两个相加的结果(分):" + result.toString());
                Preconditions.checkArgument(result.compareTo(subMemberCardRightsResps.get(0).getDisPrice()) == 0, "组合方式计算金额错误");
                return;
            }
            // 免费订阅不校验
            if (Long.valueOf(4).equals(subscribeContext.getPayWay())) {
                return;
            }
            // 凭证券 免费订阅
            if (Long.valueOf(5).equals(subscribeContext.getPayWay())) {
                checkVoucherSub(subscribeContext.getUnionId(),subscribeContext.getVoucherId());
                return;
            }
            throw new RuntimeException("非法订阅支付方法");
        }
        // 微信支付 校验价格
        if (Long.valueOf(1).equals(subscribeContext.getPayWay())) {
            log.info("支付钱(分)：" + subscribeContext.getPayPrice().multiply(new BigDecimal(100)).toString() + " 远程钱（分）：" + subMemberCardRightsResps.get(0).getPrice());
            Preconditions.checkArgument(subscribeContext.getPayPrice().multiply(new BigDecimal(100)).compareTo(subMemberCardRightsResps.get(0).getPrice()) == 0, "支付价格非法");
            return;
        }
        // 单独积分
        if (Long.valueOf(2).equals(subscribeContext.getPayWay())) {
            log.info("支付积分：" + subscribeContext.getPayIntegral().toString() + " 远程积分：" + subMemberCardRightsResps.get(0).getCardIntegralExchange());
            Preconditions.checkArgument(subscribeContext.getPayIntegral().compareTo(subMemberCardRightsResps.get(0).getCardIntegralExchange().longValue()) == 0, "支付价格非法");
            return;
        }
        // 组合
        if (Long.valueOf(3).equals(subscribeContext.getPayWay())) {
            //log.info("支付钱(分)：" + subscribeContext.getPayPrice().multiply(new BigDecimal(100)).toString() + "  支付积分" + subscribeContext.getPayIntegral() + " 远程钱（分）：" + subMemberCardRightsResps.get(0).getPrice());
            BigDecimal payPrice = subscribeContext.getPayPrice().multiply(new BigDecimal(100));
            BigDecimal payIntegralToMoney = BigDecimal.valueOf(subscribeContext.getPayIntegral()).multiply(integralValueToMoney).multiply(BigDecimal.valueOf(100));
            BigDecimal result = payPrice.add(payIntegralToMoney);
            log.info("支付钱(分)：" + payPrice.toString() + " 支付积分兑换钱(分):" + payIntegralToMoney.toString() + " 两个相加的结果(分):" + result.toString());
            Preconditions.checkArgument(result.compareTo(subMemberCardRightsResps.get(0).getPrice()) == 0, "组合方式计算金额错误");
            return;
        }
        // 免费订阅不校验
        if (Long.valueOf(4).equals(subscribeContext.getPayWay())) {
            return;
        }

        // 凭证券 免费订阅
        if (Long.valueOf(5).equals(subscribeContext.getPayWay())) {
            checkVoucherSub(subscribeContext.getUnionId(),subscribeContext.getVoucherId());
            return;
        }
        throw new RuntimeException("非法订阅支付方法");
    }

    /**
     * 绑定搭配师逻辑
     * @param subscribeContext
     * @param customerDetails
     */
    void handleBindFashioner(SubscribeContext subscribeContext, CustomerDetails customerDetails) {
        log.info("进行绑定搭配师逻辑处理subscribeContext:{}  custId:{}",JSONObject.toJSONString(subscribeContext),customerDetails.getId());
        Fashioner willBindFashioner = Preconditions.checkNotNull(iFashionerService.findById(subscribeContext.getBindFashionerId()), "搭配师信息不存在");
        subscribeContext.setCreatCardFlag(false);
        subscribeContext.setBindFashionerIdRole(willBindFashioner.getIsSales());
        subscribeContext.setCreatCardType(0L);
        // 搭配师
        if (Long.valueOf(0).equals(willBindFashioner.getIsSales())) {
            subscribeContext.setRecommender("");
            if (StringUtils.isNotEmpty(customerDetails.getFashionerId()) && !StringUtils.equals(customerDetails.getFashionerId(),"0")) {
                subscribeContext.setBindFashionerId("");
                return;
            }
            subscribeContext.setBindFashionerId(willBindFashioner.getId());
            return;
        }
        // 导购
        if (Long.valueOf(1).equals(willBindFashioner.getIsSales())) {
            String cStoreId = willBindFashioner.getcStoreId();
            CStore cStore = cStoreMapper.selectByPrimaryKey(Long.valueOf(cStoreId));

            String targetHrId = "";
            String targetCStoreId = "";
            String targetBrandId = "";
            if (adaptUnionStoreOnlyBrand(cStore)) {
                HrEmployee hrEmployee = adaptOpenCardHrInfo(willBindFashioner);
                targetHrId = hrEmployee.getId().toString();
                targetCStoreId = hrEmployee.getcStoreId().toString();
            } else {
                targetHrId = willBindFashioner.getHrEmpId();
                targetCStoreId = willBindFashioner.getcStoreId();
            }
            if (StringUtils.isNotEmpty(targetCStoreId)) {
                cStore = cStoreMapper.selectByPrimaryKey(Long.valueOf(targetCStoreId));
            }
            // 是奥莱店 直接开奥莱品牌卡
            if (StringUtils.equals(cStore.getDefault05(), "是")) {
                targetBrandId = "11";
            } else {
                BrandMapping brandMapping = Preconditions.checkNotNull(brandMappingMapper.getByArcId(Long.valueOf(cStore.getcArcbrandId())), "品牌对应关系不存在");
                targetBrandId = brandMapping.getWeid().toString();
            }

            subscribeContext.setBindFashionerId(willBindFashioner.getId());
            subscribeContext.setCreatCardBrandId(targetBrandId);
            subscribeContext.setCreatCardCStoreId(targetCStoreId);
            subscribeContext.setCreatCardHrId(targetHrId);

            List<MemberCardEntity> memberCardEntities = iExternalCustomerVipService.clientVipVoList(customerDetails.getUnionid(), targetBrandId);
            if (CollectionUtils.isEmpty(memberCardEntities)) {
                log.info("会员custId:{} unionId:{} 品牌branId:{} 开新卡设置", customerDetails.getId(),customerDetails.getUnionid(), targetBrandId);
                subscribeContext.setCreatCardFlag(true);
                subscribeContext.setCreatCardType(1L);
                return;
            }
            MemberCardEntity memberCardEntity = memberCardEntities.get(0);
            log.info("卡信息unionId:{} cardNo:{} storeId:{} salesRepId:{}", memberCardEntity.getUnionId(), memberCardEntity.getCardNo(), memberCardEntity.getStoreId(), memberCardEntity.getSalesRepId());
            if (!Objects.isNull(memberCardEntity.getStoreId()) && StringUtils.equals(targetCStoreId, memberCardEntity.getStoreId().toString()) && Objects.isNull(memberCardEntity.getSalesRepId())
                    || Objects.isNull(memberCardEntity.getStoreId()) && Objects.isNull(memberCardEntity.getSalesRepId())
            ) {
                log.info("会员custId:{} unionId:{} 品牌branId:{} 补全信息设置", customerDetails.getId(), customerDetails.getUnionid(), targetBrandId);
                subscribeContext.setCreatCardFlag(true);
                subscribeContext.setCreatCardType(2L);
            }

            // 门店： 一个同一门店， 绑定导购信息为空或者-1， 补全卡信息
            if (
                    !Objects.isNull(memberCardEntity.getStoreId()) && StringUtils.equals(targetCStoreId, memberCardEntity.getStoreId().toString()) &&
                            (Objects.isNull(memberCardEntity.getSalesRepId()) || Objects.equals(Integer.valueOf(-1), memberCardEntity.getSalesRepId()))
            ) {
                subscribeContext.setCreatCardFlag(true);
                subscribeContext.setCreatCardType(2L);
            }

            // 门店为空
            if (Objects.isNull(memberCardEntity.getStoreId()) || Objects.equals(Integer.valueOf(-1), memberCardEntity.getStoreId())) {
                subscribeContext.setCreatCardFlag(true);
                subscribeContext.setCreatCardType(2L);
            }
        }
    }


    /**
     * 非奥莱、非江南布衣+， 是集合店
     *
     * @param cStore
     * @return
     */
    public Boolean adaptUnionStoreOnlyBrand(CStore cStore) {
        return !StringUtils.equals(cStore.getDefault05(), "是")
                && !Long.valueOf(67).equals(cStore.getcArcbrandId())
                && (StringUtils.equals(cStore.getIsUnionstore(), "Y") || cStore.getId().equals(cStore.getcUnionstoreId()));
    }


    /**
     * 适配开卡导购HrEmployee信息
     *
     * @param willBindFashioner
     * @return
     */
    @Override
    public HrEmployee adaptOpenCardHrInfo(Fashioner willBindFashioner) {
        List<HrEmployee> hrEmployees = hrEmployeeMapper.selectListByPhone(willBindFashioner.getPhone());
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(hrEmployees), "导购hrEmployee信息不存在");
        Map<Long, HrEmployee> hrEmployeeMap = new HashMap<>();

        hrEmployees.forEach(e -> {
            if (ObjectUtils.isNotEmpty(e.getcStoreId()) && !StringUtils.equals(e.getId().toString(), willBindFashioner.getcStoreId())) {
                hrEmployeeMap.put(e.getcStoreId(), e);
            }
        });
        Preconditions.checkArgument(MapUtils.isNotEmpty(hrEmployeeMap), "导购hrEmployee信息不存在");

        // 只取 符合当前集合店的门店
        List<CStore> cStores = cStoreMapper.selectCStoreByIds(Lists.newArrayList(hrEmployeeMap.keySet())).stream().filter(e ->
                (
                   StringUtils.equals(e.getIsactive(),"Y") &&
                   ObjectUtils.isNotEmpty(e.getcUnionstoreId()) &&
                           StringUtils.equals(willBindFashioner.getcStoreId(), e.getcUnionstoreId().toString())
                )
        ).collect(Collectors.toList());
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(cStores),"适配开卡导购CStore信息不存在");
        Long cStoreId = 0L;
        for (int i = 0; i < arcIdStrArr.length; i++) {
            String temp = arcIdStrArr[i];
            if (cStores.stream().anyMatch(e -> e.getcArcbrandId().toString().equals(temp))) {
                cStoreId = cStores.stream().filter(e -> e.getcArcbrandId().toString().equals(temp)).findFirst().get().getId();
                break;
            }
        }
        return Preconditions.checkNotNull(hrEmployeeMap.get(cStoreId), "适配开卡导购信息不存在");
    }

    @Override
    public void unSubscribe(UnSubscribeContext unSubscribeContext) {

    }


    @Override
    public Map<String, Object> subscribe(SubscribeContext subscribeContext) {
        log.info("订阅上下文subscribeContext:{}", JSONObject.toJSONString(subscribeContext));
        BSubscribeInfo bSubscribeInfo = packageSubscribeInfo(subscribeContext);
        SubscribeOrder subscribeOrder = packageSubscribeOrder(subscribeContext);
        CustomerDetails customerDetails = iCustomerDetailsService.getById(subscribeContext.getCustId());

        CreateSubLogContext createSubLogContext = CreateSubLogContext.builder()
                .detailType(DetailTypeEnum.SUB.getCode())
                .operateType(OperateTypeEnum.USER.getCode())
                .operatePeople(OperateTypeEnum.USER.getDesc())
                .subId(bSubscribeInfo.getId())
                .build();

        Map<String, Object> result = new HashMap<>();
        template.execute(action -> {
            // 正式卡订阅
            if (Integer.valueOf(1).equals(subscribeContext.getCardType())) {
                log.info("订阅计划订阅日志");
                iCombineSubLogService.createSubLogCxt(createSubLogContext);
            }

            ibSubscribeInfoService.save(bSubscribeInfo);
            subscribeOrderMapper.insert(subscribeOrder);
            // 付费 或者 组合
            if (Long.valueOf(1).equals(subscribeContext.getPayWay()) || Long.valueOf(3).equals(subscribeContext.getPayWay())) {
                result.putAll(subscribeByCommonPay(subscribeOrder, customerDetails));
            }
            return action;
        });
        // 单独积分
        if (Long.valueOf(2).equals(subscribeContext.getPayWay())) {
            iThreeSubscribePaymentService.handleSubNotify(SubPayNotifyContext.builder().customerDetails(customerDetails).bSubscribeInfo(bSubscribeInfo).subscribeOrder(subscribeOrder).build());
        }
        // 免费的 直接生成订阅计划
        if (Long.valueOf(4).equals(subscribeContext.getPayWay()) || Long.valueOf(5).equals(subscribeContext.getPayWay())) {
            iThreeSubscribePaymentService.handleSubNotify(SubPayNotifyContext.builder().customerDetails(customerDetails).bSubscribeInfo(bSubscribeInfo).subscribeOrder(subscribeOrder).build());
        }
        return result;
    }


//    @Override
//    public void unSubscribe(UnSubscribeContext unSubscribeContext) {
//        String subId = unSubscribeContext.getSubId();
//        String unionId = unSubscribeContext.getUnionId();
//        String custId = unSubscribeContext.getCustomerId();
//        String price = "0";
//
//        BSubscribeInfo nowSubInfo =  ibSubscribeInfoService.getById(subId);
//        if(!nowSubInfo.getStatus().equals(1L)){
//            throw new RuntimeException("订阅非有效");
//        }
//
//        // 修改订阅信息
//        BSubscribeInfo updateSubInfo = new BSubscribeInfo();
//        updateSubInfo.setUpdateTime(new Date());
//        updateSubInfo.setUnsubTime(new Date());
//        updateSubInfo.setEndTime(new Date());
//        updateSubInfo.setStatus(3L);
//        updateSubInfo.setId(subId);
//
//        // 修改订阅订单
//        List<SubscribeOrder> subscribeOrders = subscribeOrderMapper.getSubscribeOrderBySubId(subId);
//        Preconditions.checkArgument(CollectionUtils.isNotEmpty(subscribeOrders), "订阅订单不存在");
//        SubscribeOrder subscribeOrder = subscribeOrders.get(0);
//
//        //生成订阅订单
//        SubscribeOrder addSubScribeOrder = new SubscribeOrder();
//        BeanUtils.copyProperties(subscribeOrder, addSubScribeOrder);
//        Date now = new Date();
//        String dayStr = DateUtil.formatToStr(now, DateUtil.DATE_FORMAT_YYMMDD);
//        String number = subscribeOrderMapper.selectNumber(dayStr);
//        Integer sn = number == null ? 0 : Integer.valueOf(number);
//        sn = sn + new Random().nextInt(10) + 1;
//        AtomicInteger atomicNum = new AtomicInteger();
//        atomicNum.set(sn);
//        String serialNumber = String.format("%08d", atomicNum.incrementAndGet());
//        addSubScribeOrder.setId(idLeafService.getId());
//        addSubScribeOrder.setTradeNo("SYLS" + dayStr + serialNumber);
//        addSubScribeOrder.setType(SubscribeOrderTypeEnum.REFUND.getCode().longValue());
//        addSubScribeOrder.setStatus(SubscribeOrderStatusEnum.unpay.getCode().longValue());
//        if (StringUtils.isNotEmpty(price)) {
//            addSubScribeOrder.setRefundAmout(new Float(price));
//        }
//        addSubScribeOrder.setCreateTime(new Date());
//
//        //重置顾客订阅过期时间
//        CustomerDetails customerDetails = Preconditions.checkNotNull(iCustomerDetailsRepository.findByUnionId(unionId), "用户信息不存在");
//        InvalidSiteSubscribePlanContext invalidSiteSubscribePlanContext = InvalidSiteSubscribePlanContext.builder().subId(subId).cardType(nowSubInfo.getCardType()).custId(custId).build();
//        template.execute(action -> {
//            ibSubscribeInfoService.updateById(updateSubInfo);
//            subscribeOrderMapper.insert(addSubScribeOrder);
//            iCombineSubscribePlanService.invalidSiteSubscribePlan(invalidSiteSubscribePlanContext);
//            return action;
//        });
//
//    }

    @Override
    public BoxPlusCenterHeadResp getBoxPlusSubInfo(String subId) {
        BSubscribeInfo bSubscribeInfo = ibSubscribeInfoService.selectById(subId);
        BoxPlusCenterHeadResp resp = new BoxPlusCenterHeadResp();
        resp.setStartTime(bSubscribeInfo.getStartTime());
        resp.setEndTime(bSubscribeInfo.getEndTime());
        resp.setIsHaveRecovered(bSubscribeInfo.getFirstRecover() == 0 ? false : true);

        //查询绑定的搭配师信息
        CustomerDetails customerDetails = iCustomerDetailsService.findByUnionId(bSubscribeInfo.getUnionid());
        List<BSubscribePlan> subscribePlans = ibSubscribePlanService.getListBySubId(bSubscribeInfo.getId());
        String fashionerId = customerDetails.getFashionerId();
        int isFirstBox = subscribePlans.stream().filter(item -> !item.getStatus().equals(0l) && !item.getStatus().equals(4l)).collect(Collectors.toList()).size();
        resp.setStatus(isFirstBox == 1 ? 1 : 0);
        if (isFirstBox > 1){
            resp.setStatus(2);
        }
        if (fashionerId != null) {
            Fashioner fashioner = iFashionerService.getById(fashionerId);
            if (fashioner != null){
                BoxPlusCenterHeadResp.PlusFashionerResp plusFashionerResp = new BoxPlusCenterHeadResp.PlusFashionerResp();
                plusFashionerResp.setFashionerType(fashioner.getIsSales().equals(1l) ? 2 : 1);
                plusFashionerResp.setHeadimgurl(fashioner.getPhoto());
                plusFashionerResp.setNickname(fashioner.getName());
                plusFashionerResp.setFashionerId(fashioner.getId());
                plusFashionerResp.setFashionerWechatQrCode(fashioner.getWechatUrl());
                resp.setPlusFashionerResp(plusFashionerResp);
            }
        }

        //查询订阅周期内的消费信息
        if (CollectionUtils.isNotEmpty(subscribePlans)) {
            BSubscribePlan bSubscribePlan = subscribePlans.get(0);
            resp.setBoxConsumeInfo(new BoxPlusCenterHeadResp.BoxConsumeInfo());
            //订阅器内的积分
            int obatinIntegral = 0;
            for (BSubscribePlan validSubscribePlan : subscribePlans) {
                obatinIntegral = obatinIntegral + validSubscribePlan.getObtainIntegral();
            }
            resp.getBoxConsumeInfo().setObtainIntegral(obatinIntegral);
            //将validSubscribePlans中的payPrice相加  保存到payPrice中    savePrice不变
            BigDecimal payPrice = new BigDecimal(0);
            for (BSubscribePlan validSubscribePlan : subscribePlans) {
                payPrice = payPrice.add(validSubscribePlan.getPayPrice());
            }
            resp.getBoxConsumeInfo().setPayPrice(payPrice.floatValue());
            //将validSubscribePlans中的savePrice相加 保存到savePrice中    payPrice不变
            BigDecimal savePrice = new BigDecimal(0);
            for (BSubscribePlan validSubscribePlan : subscribePlans) {
                savePrice = savePrice.add(validSubscribePlan.getSavePrice());
            }
            resp.getBoxConsumeInfo().setSavePrice(savePrice.floatValue());
            resp.getBoxConsumeInfo().setTotalBoxCount(isFirstBox);
        }

        //订阅过期的场景
        if (bSubscribeInfo.getStatus().equals(2l)|| bSubscribeInfo.getStatus().equals(3l)){
            //查询所有计划中的BOX成交信息
            List<String> boxIds = subscribePlans.stream().filter(item -> item.getBoxId() != null).map(item -> item.getBoxId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(boxIds)) {
                BoxPlusCenterHeadResp.SubscribeExpireInfo subscribeExpireInfo = new BoxPlusCenterHeadResp.SubscribeExpireInfo();
                subscribeExpireInfo.setTotalBoxCount(boxIds.size());
                subscribeExpireInfo.setTotalBoxGiftCount(BigDecimal.valueOf(0));
                //节省金额
                subscribeExpireInfo.setTotalSavePrice(resp.getBoxConsumeInfo().getSavePrice());
                List<BoxDetailsWithBLOBs> boxEntities = boxDetailsMapper.selectByIds(boxIds);
                List<BoxWithBLOBs> boxWithBLOBs = iBoxRepository.selectByIds(boxIds);
                List<String> boxSns = boxWithBLOBs.stream().map(item -> item.getBoxSn()).collect(Collectors.toList());
                //试穿的件数就是BOX里面的记录数
                subscribeExpireInfo.setTotalTryCount(boxEntities.size());

                List<Order> orders = iOrderRepository.selectOrderByBoxSns(boxSns);
                List<Integer> totalQuantys = orders.stream().filter(item -> item.getOrderStatus().equals(1l) || item.getOrderStatus().equals(3l) || item.getOrderStatus().equals(5l)).map(item -> Integer.valueOf(item.getProductTotalQuantity())).collect(Collectors.toList());
                //将集合totalQuantys中的所有元素相加
                int buyCount = 0;
                for (Integer totalQuantity : totalQuantys) {
                    buyCount = buyCount + totalQuantity;
                }
                //购买的件数
                subscribeExpireInfo.setTotalBuyCount(buyCount);
                List<String> planIds = subscribePlans.stream().map(item -> item.getId()).collect(Collectors.toList());
                Map<String/** planId */, NextNodeGiftEntity.Gift> giftMaps = iCusRightsService.batchHaveBoxNodeGift(planIds);
                List<BigDecimal> gitfPrices = giftMaps.values().stream().map(item -> new BigDecimal(item.getPrice())).collect(Collectors.toList());
                //实物奖励
                for (BigDecimal gitfPrice : gitfPrices) {
                    if (gitfPrice != null){
                        subscribeExpireInfo.setTotalBoxGiftCount(subscribeExpireInfo.getTotalBoxGiftCount().add(gitfPrice));
                    }
                }
                resp.setSubscribeExpireInfo(subscribeExpireInfo);
            }
        }
        return resp;
    }

    @Override
    public PosSubscribePlanResp getPosSubscribePlanFromPos(PosSubPlanReq posSubPlanReq) {
        PosSubscribePlanResp resp = new PosSubscribePlanResp();
        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(posSubPlanReq.getUnionId());
        if (customerDetails != null){
            LambdaQueryWrapper<BCustomerZhimaCredit> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BCustomerZhimaCredit:: getUserId,customerDetails.getId());
            List<BCustomerZhimaCredit> list = ibCustomerZhimaCreditService.list(queryWrapper);
            if (list.isEmpty()){
                resp.setCreditStatus(AgreementStatusEnum.DEFAULT.getCode());
            }else {
                resp.setCreditStatus(list.get(0).getAgreementStatus());
            }
        }

        //查询用户是否有订阅记录
        List<BSubscribeInfo> recordSubscribes = ibSubscribeInfoService.selectAllSubInfoByUnionIds(Lists.newArrayList(posSubPlanReq.getUnionId()));
        if (CollectionUtils.isEmpty(recordSubscribes)) {
            resp.setStatus(3);
            resp.setJumpUrl(SUBSCRIBE_PLAN_REOPEN_URL);
            return resp;
        }

        //查询有效的记录
        List<BSubscribeInfo> subscribeInfos = ibSubscribeInfoService.selectUserEffectSubInfoByUnionIds(Lists.newArrayList(posSubPlanReq.getUnionId()));
        if (CollectionUtils.isEmpty(subscribeInfos)) {
            resp.setStatus(2);//已过期
            resp.setJumpUrl(SUBSCRIBE_PLAN_POSTER_URL);
        }
        //取出最近过期的订阅ID
        BCustomerExpand customerExpand = ibCustomerExpandService.getById(customerDetails.getId());
        if (customerExpand == null || Objects.isNull(customerExpand.getLateRightsId())) return resp;

        BSubscribeInfo subscribeInfo = ibSubscribeInfoService.selectById(customerExpand.getLateRightsId());
        resp.setStatus(subscribeInfo.getStatus().equals(1l) ? 1 : 2);
        resp.setJumpUrl(subscribeInfo.getStatus().equals(1l) ? SERVICE_ORDER_DETAIL_URL : SUBSCRIBE_PLAN_REOPEN_URL);
        resp.setSubId(subscribeInfo.getId());
        PosSubscribePlanResp.SubscribeInfo subscribeInfoResp = new PosSubscribePlanResp.SubscribeInfo();
        subscribeInfoResp.setExpireDate(subscribeInfo.getEndTime());
        BSubscribePlan currentPlan = ibSubscribePlanService.getCurrentSubscribePlan(subscribeInfo.getId());
        if (currentPlan != null){
            subscribeInfoResp.setCurrentPlan(currentPlan.getPlanMonth());
        }

        //获取所有的节点
        List<BSubscribePlan> allPlans = ibSubscribePlanService.getListBySubId(subscribeInfo.getId());
        List<BigDecimal> payPrices = allPlans.stream().map(item -> item.getPayPrice()).collect(Collectors.toList());
        //将所有的payPrice相加
        BigDecimal totalPayPrice = new BigDecimal(0);
        for (BigDecimal payPrice : payPrices) {
            totalPayPrice = totalPayPrice.add(payPrice);
        }
        subscribeInfoResp.setPayPrice(totalPayPrice.floatValue());
        resp.setSubscribeInfo(subscribeInfoResp);
        return resp;
    }

    @Override
    public void seasonableHandleSub(String subId) {
        BSubscribeInfo bSubscribeInfo = ibSubscribeInfoService.getById(subId);
        if (bSubscribeInfo == null || Long.valueOf(1).equals(bSubscribeInfo.getDelFlag()) || !Long.valueOf(1).equals(bSubscribeInfo.getStatus())) {
            return;
        }
        List<BSubscribePlan> plans = ibSubscribePlanService.getListBySubId(subId);
        if (CollectionUtils.isEmpty(plans)) {
            return;
        }
        // 订阅计划全部结束
        if (plans.stream().filter(e -> !Long.valueOf(3).equals(e.getStatus()) && !Long.valueOf(4).equals(e.getStatus())).count() == 0) {
            handleExpireSubInfo(bSubscribeInfo.getId(), bSubscribeInfo.getCardType(), bSubscribeInfo.getCustId());
            return;
        }
        List<BSubscribePlan> result = plans.stream().filter(e -> Long.valueOf(0).equals(e.getStatus()) && e.getPlanEndMonth().before(new Date())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(result)) {
            // 订阅信息过期的处理逻辑
            if (bSubscribeInfo.getEndTime().before(new Date())) {
                handleExpireSubInfo(bSubscribeInfo.getId(), bSubscribeInfo.getCardType(), bSubscribeInfo.getCustId());
            }
            return;
        }
        // 处理过期的订阅计划
        ibSubscribeTaskService.handleSubPlan(result);
        // 再次查询是否 全部结束订阅计划
        plans = ibSubscribePlanService.getListBySubId(subId);
        // 订阅计划全部结束
        if (plans.stream().filter(e -> !Long.valueOf(3).equals(e.getStatus()) && !Long.valueOf(4).equals(e.getStatus())).count() == 0) {
            handleExpireSubInfo(bSubscribeInfo.getId(), bSubscribeInfo.getCardType(), bSubscribeInfo.getCustId());
            return;
        }
        // 订阅信息过期的处理逻辑
        if (bSubscribeInfo.getEndTime().before(new Date())) {
            handleExpireSubInfo(bSubscribeInfo.getId(), bSubscribeInfo.getCardType(),bSubscribeInfo.getCustId());
            return;
        }
    }

    /**
     * 处理过期订阅信息
     * @param subId
     * @param cardType 卡类型（1正式卡 2单次卡 3免费卡）
     * @param custId 客户id
     */
    void handleExpireSubInfo(String subId, Long cardType, String custId) {
        // 修改订阅信息
        Date nowDate = DateTime.now().toDate();
        BSubscribeInfo updateSubInfo = new BSubscribeInfo();
        updateSubInfo.setUpdateTime(nowDate);
        updateSubInfo.setUnsubTime(nowDate);
        updateSubInfo.setStatus(2L);
        updateSubInfo.setId(subId);
        ibSubscribeInfoService.updateById(updateSubInfo);

        if (Long.valueOf(1).equals(cardType)) {
            // 修改用户统计数据--订阅状态和权益ID
            BCustomerExpand expand = new BCustomerExpand();
            expand.setUserId(custId);
            expand.setSubStatus(SubStatusEnum.OFFSUB.getCode());
            expand.setUpdateTime(new Date());
            ibCustomerExpandService.updateById(expand);
        }
    }

    /**
     * 封装订阅信息
     * @param subscribeContext
     * @return
     */
    BSubscribeInfo packageSubscribeInfo(SubscribeContext subscribeContext) {
        BSubscribeInfo bSubscribeInfo = new BSubscribeInfo();
        bSubscribeInfo.setId(idLeafService.getId());
        bSubscribeInfo.setCardId(subscribeContext.getGoodsId());
        bSubscribeInfo.setAppId(subscribeContext.getAppId());
        bSubscribeInfo.setCardType(Long.valueOf(subscribeContext.getCardType()));
        bSubscribeInfo.setCustId(subscribeContext.getCustId());
        bSubscribeInfo.setCreateTime(subscribeContext.getTodayTime());
        bSubscribeInfo.setStatus(1L);
        bSubscribeInfo.setStartTime(subscribeContext.getTodayTime());
        bSubscribeInfo.setEndTime(new DateTime(subscribeContext.getTodayTime()).plusDays(subscribeContext.getSubDays().intValue()).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).toDate());
        bSubscribeInfo.setDelFlag(1L);
        bSubscribeInfo.setUserCardLevel(subscribeContext.getUserCardLevel());
        bSubscribeInfo.setUnionid(subscribeContext.getUnionId());
        bSubscribeInfo.setPayWay(subscribeContext.getPayWay());
        bSubscribeInfo.setOutNo(subscribeContext.getOutNo());
        bSubscribeInfo.setRecommender(subscribeContext.getRecommender());
        bSubscribeInfo.setBindFashionerId(subscribeContext.getBindFashionerId());
        bSubscribeInfo.setRenew(0L);
        if(subscribeContext.getHasRenew()){
            bSubscribeInfo.setRenew(1L);
        }
        bSubscribeInfo.setType(subscribeContext.getType());
        bSubscribeInfo.setInvitePeople(subscribeContext.getInvitePeople());
        if (ObjectUtils.isNotEmpty(subscribeContext.getCreatCardType())) {
            bSubscribeInfo.setCreateCardHrId(subscribeContext.getCreatCardHrId());
            bSubscribeInfo.setCreateCardStoreId(subscribeContext.getCreatCardCStoreId());
            bSubscribeInfo.setCreateCardType(subscribeContext.getCreatCardType());
            bSubscribeInfo.setCreateCardBrandId(subscribeContext.getCreatCardBrandId());
        }
        // 如果正式卡 新的订阅就是3.5
        if(Integer.valueOf(1).equals(subscribeContext.getCardType())){
            bSubscribeInfo.setVersionNo("3.5");
        }
        subscribeContext.setSubId(bSubscribeInfo.getId());
        return bSubscribeInfo;
    }


    /**
     * 封装订阅订单
     * @param subscribeContext
     * @return
     */
    SubscribeOrder packageSubscribeOrder(SubscribeContext subscribeContext) {
        SubscribeOrder subscribeOrder = new SubscribeOrder();
        subscribeOrder.setId(idLeafService.getId());
        subscribeOrder.setSubId(subscribeContext.getSubId());
        subscribeOrder.setShopSn("DYDD" + IdLeaf.getDateId("SUBSCRIBE_ORDER"));
        subscribeOrder.setTradeNo("SYLS" +  IdLeaf.getDateId("SUBSCRIBE_ORDER"));
        subscribeOrder.setUnionid(subscribeContext.getUnionId());
        subscribeOrder.setSubNew(1L);
        subscribeOrder.setCreateTime(subscribeContext.getTodayTime());
        subscribeOrder.setDaystr(DateTimeFormat.forPattern("yyMMdd").print(new DateTime(subscribeContext.getTodayTime()).toLocalDate()));
        subscribeOrder.setPayWay(subscribeContext.getPayWay());
        subscribeOrder.setPayPrice(Float.valueOf(subscribeContext.getPayPrice().toString()));
        if (subscribeContext.getPayIntegral() != null) {
            subscribeOrder.setPayPoint(subscribeContext.getPayIntegral().toString());
        }
        subscribeOrder.setPayChannel(subscribeContext.getPayChannel());
        subscribeOrder.setProductPrice(Float.valueOf(subscribeContext.getGoodsPrice().toString()));
        subscribeOrder.setType(0L);
        subscribeOrder.setStatus(0L);
        if(StringUtils.isNotEmpty(subscribeContext.getVoucherId())){
            subscribeOrder.setVoucherId(subscribeContext.getVoucherId());
        }
        return subscribeOrder;
    }



    /**
     * 创建微信支付订阅订单
     * @param subscribeOrder
     * @param customerDetails
     * @return
     */
    public Map<String, String> subscribeByWeiXinPay(SubscribeOrder subscribeOrder,CustomerDetails customerDetails) {
        Map<String, String> chargeMap = new HashMap<>();
        //创建支付记录
        PaymentContext paymentContext = new PaymentContext();
        paymentContext.setOrderId(subscribeOrder.getId());
        paymentContext.setCustomerId(customerDetails.getId());
        paymentContext.setOpenId(customerDetails.getOpenid());
        paymentContext.setNotifyUrl(subscribeSqbNotifyUrl);
        paymentContext.setPayFor(Long.valueOf(0));
        paymentContext.setPaymentType(Long.valueOf(0));
        paymentContext.setType(Long.valueOf(10));
        paymentContext.setUseShouQianBa(true);
        paymentContext.setPayChannel(subscribeOrder.getPayChannel());

        if (paymentContext.getPayChannel().equals(PayChannelEnum.ALIPAY.getCode().toString())) {
            if (StringUtils.isEmpty(customerDetails.getPhone())) {
                throw new RuntimeException("用户手机号不能为空");
            }

            BOauthInfo oauthInfoSearch = new BOauthInfo();
            oauthInfoSearch.setPhone(customerDetails.getPhone());
            oauthInfoSearch.setType(StringUtils.equals(PayChannelEnum.ALIPAY.getCode().toString(), paymentContext.getPayChannel()) ? 1L : 0L);
            oauthInfoSearch.setSubType(0L);
            List<BOauthInfo> oauthInfos = ibOauthInfoService.getListBySearch(oauthInfoSearch);
            if (CollectionUtils.isEmpty(oauthInfos)) {
                throw new RuntimeException("授权信息不存在");
            }
            paymentContext.setOpenId(oauthInfos.get(0).getOpenid());
        }

        chargeMap.putAll(iThreeSubscribePaymentService.getChargeBySqb(paymentContext));
        return chargeMap;
    }


    /**
     * 订阅-通用支付
     * @param subscribeOrder
     * @param customerDetails
     * @return
     */
    public Map<String, Object> subscribeByCommonPay(SubscribeOrder subscribeOrder,CustomerDetails customerDetails) {
        Map<String, Object> chargeMap = new HashMap<>();
        //创建支付记录
        PaymentContext paymentContext = new PaymentContext();
        paymentContext.setOrderId(subscribeOrder.getId());
        paymentContext.setCustomerId(customerDetails.getId());
        paymentContext.setOpenId(customerDetails.getOpenid());
        paymentContext.setNotifyUrl(subscribeCommonPayNotifyUrl);
        paymentContext.setPayFor(Long.valueOf(0));
        paymentContext.setPaymentType(Long.valueOf(0));
        paymentContext.setType(Long.valueOf(10));
        paymentContext.setUseShouQianBa(true);
        paymentContext.setPayChannel(PayChannelEnum.WEI_XIN_PAY.getCode().toString());
        chargeMap.putAll(iThreeSubscribePaymentService.getChargeByCommonPay(paymentContext));
        return chargeMap;
    }


    /**
     * 计算-订阅价格
     * @param calSubMoneyReqDto
     * @return
     */
    @Override
    public BigDecimal calSubMoney(CalSubMoneyReqDto calSubMoneyReqDto) {
        // 会员优惠
        BigDecimal vipPrice = StringUtils.isNotEmpty(calSubMoneyReqDto.getVipPrice()) ? new BigDecimal(calSubMoneyReqDto.getVipPrice()) : BigDecimal.ZERO;
        // 续订优惠
        BigDecimal renewPrice = StringUtils.isNotEmpty(calSubMoneyReqDto.getRenewPrice()) ? new BigDecimal(calSubMoneyReqDto.getRenewPrice()) : BigDecimal.ZERO;
        // 积分价格
        BigDecimal integralValuePrice = StringUtils.isNotEmpty(calSubMoneyReqDto.getIntegralValuePrice()) ? new BigDecimal(calSubMoneyReqDto.getIntegralValuePrice()) : BigDecimal.ZERO;
        return new BigDecimal(calSubMoneyReqDto.getSubTotalPrice()).subtract(vipPrice).subtract(renewPrice).subtract(integralValuePrice);
    }

    @Override
    public BigDecimal calIntegralConvertMoney(Long integral) {
        return new BigDecimal(integral.toString()).multiply(integralValueToMoney);
    }

    @Override
    public BigDecimal calMoneyConvertIntegral(BigDecimal money) {
        return money.divide(integralValueToMoney).setScale(0, BigDecimal.ROUND_UP);
    }

    @Override
    public SubWithSubOrderInfoEntity getSubWithOrder(String subId) {
        SubWithSubOrderInfoEntity subWithSubOrderInfoEntity = new SubWithSubOrderInfoEntity();
        BSubscribeInfo bSubscribeInfo = ibSubscribeInfoService.getById(subId);
        SubscribeOrder subscribeOrder = subscribeOrderMapper.selectById(bSubscribeInfo.getSubOrderId());
        subWithSubOrderInfoEntity.setBSubscribeInfo(bSubscribeInfo);
        subWithSubOrderInfoEntity.setSubscribeOrder(subscribeOrder);
        return subWithSubOrderInfoEntity;
    }

    @Override
    public CardPriceWithOthersEntity getCardPriceWithOthers(GetCardPriceWithOthersReq getCardPriceWithOthersReq) {
        String unionId = getCardPriceWithOthersReq.getUnionId();
        Integer cardType = getCardPriceWithOthersReq.getCardType();
        String groupVipName = getCardPriceWithOthersReq.getGroupVipName();

        Integer cardLevel = iNewRightsV3Service.convertSuitCardLevel(groupVipName);
        GetUserIntegralEntity getUserIntegral = iExternalCustomerIntegralService.getUserIntegral(unionId);

        CardPriceWithOthersEntity cardPriceWithOthersEntity = new CardPriceWithOthersEntity();
        cardPriceWithOthersEntity.setCardLevel(cardLevel);
        cardPriceWithOthersEntity.setGroupVipName(groupVipName);

        List<SubMemberCardRightsResp> subMemberCardRightsResps = iNewRightsV3Service.getCardConfigByCardTypeAndSuitCardLevel(cardType, cardLevel);
        if (CollectionUtils.isEmpty(subMemberCardRightsResps)) {
            log.info("卡配置不存在");
            cardPriceWithOthersEntity.setSubMemberCardRightsResp(null);

        }
        SubMemberCardRightsResp subMemberCardRightsResp = subMemberCardRightsResps.get(0);
        cardPriceWithOthersEntity.setSubMemberCardRightsResp(subMemberCardRightsResp);
        cardPriceWithOthersEntity.setTotalIntegral(0L);
        if (getUserIntegral != null) {
            cardPriceWithOthersEntity.setTotalIntegral(getUserIntegral.getTotalIntegral());
        }
        return cardPriceWithOthersEntity;
    }


    /**
     * 单次盒子 退订
     *
     * @param unSubscribeContext
     */
    @Override
    public void unSingleSubscribe(UnSingleSubscribeContext unSubscribeContext) {
        log.info("单次盒子退订subId:{} unSubscribeContext:{}", unSubscribeContext.getSubId(), JSONObject.toJSONString(unSubscribeContext));
        String subId = unSubscribeContext.getSubId();
        String unionId = unSubscribeContext.getUnionId();
        String custId = unSubscribeContext.getCustomerId();

        BSubscribeInfo nowSubInfo = Preconditions.checkNotNull(ibSubscribeInfoService.getById(subId), "订阅信息不存在");
        Preconditions.checkArgument(nowSubInfo.getStatus().equals(1L), "订阅非有效");
        SubscribeOrder subscribeOrder = Preconditions.checkNotNull(subscribeOrderMapper.selectById(nowSubInfo.getSubOrderId()), "订阅订单不存在");
        SubscribeOrder addSubScribeOrder = packageUnSubscribeOrder(subscribeOrder);

        // 修改订阅信息
        BSubscribeInfo updateSubInfo = new BSubscribeInfo();
        updateSubInfo.setId(subId);
        updateSubInfo.setUpdateTime(new Date());
        updateSubInfo.setUnsubTime(new Date());
        updateSubInfo.setEndTime(new Date());
        updateSubInfo.setStatus(3L);
        ibSubscribeInfoService.updateById(updateSubInfo);
        subscribeOrderMapper.insert(addSubScribeOrder);

        InvalidSiteSubscribePlanContext invalidSiteSubscribePlanContext = InvalidSiteSubscribePlanContext.builder()
                .subId(subId).cardType(nowSubInfo.getCardType()).custId(custId).build();
        iCombineSubscribePlanService.invalidSiteSubscribePlan(invalidSiteSubscribePlanContext);
        // 付费的 调用收钱吧
        if (Long.valueOf(1).equals(nowSubInfo.getPayWay())) {
            handleRefundBySqb(addSubScribeOrder.getId());
        }
    }

    /**
     * 封装退款的订阅订单
     * @param subscribeOrder
     * @return
     */
    SubscribeOrder packageUnSubscribeOrder(SubscribeOrder subscribeOrder) {
        //生成订阅订单
        SubscribeOrder addSubScribeOrder = new SubscribeOrder();
        BeanUtils.copyProperties(subscribeOrder, addSubScribeOrder);
        addSubScribeOrder.setDaystr(DateTimeFormat.forPattern("yyMMdd").print(DateTime.now().toLocalDate()));
        addSubScribeOrder.setId(idLeafService.getId());
        addSubScribeOrder.setTradeNo("SYLS" + IdLeaf.getDateId("SUBSCRIBE_ORDER"));
        addSubScribeOrder.setType(SubscribeOrderTypeEnum.REFUND.getCode().longValue());
        addSubScribeOrder.setStatus(SubscribeOrderStatusEnum.unpay.getCode().longValue());
        if (!Objects.isNull(subscribeOrder.getPayPrice())) {
            addSubScribeOrder.setRefundAmout(subscribeOrder.getPayPrice());
        }
        // 支付方式(1微信支付 2积分支付 3组合支付 4免费)"
        if(Long.valueOf(4).equals(subscribeOrder.getPayWay())){
            addSubScribeOrder.setStatus(SubscribeOrderStatusEnum.paid.getCode().longValue());
        }
        addSubScribeOrder.setCreateTime(new Date());
        return addSubScribeOrder;
    }

    /**
     * 单次盒子退款异步
     * @param refundSubscribeOrderId
     */
     void handleRefundBySqb(String refundSubscribeOrderId){
         log.info("处理单次盒子退款异步subscribeOrderId:{}", refundSubscribeOrderId);
         SingleUnSubRefundEvent event = new SingleUnSubRefundEvent(refundSubscribeOrderId,
                 Long.valueOf(IdWorkLeaf.getInstance().getId()),
                 Long.valueOf(IdWorkLeaf.getInstance().getId()),
                 UUID.randomUUID());
         try {
             iSingleUnSubRefundBus.post(event);
         } catch (PersistentBus.EventBusException e) {
             throw new RuntimeException("处理单次盒子退款异步失败");
         }
     }



    /**
     *
     * @param customerId
     * @param payFor
     * @param totalAmount
     * @param paymentType
     * @param type
     * @param orderId
     * @return
     */
    public Payment createPayment(String customerId, String payFor, Double totalAmount, Long paymentType, Long type, String orderId) {
        Payment payment = new Payment();
        payment.setId(idLeafService.getId());
        payment.setCustomerId(customerId);
        payment.setPayer(customerId);
        payment.setPaymentSn(payFor);
        payment.setPaymentStatus(PaymentStatusEnum.ready.getCode().longValue());
        payment.setType(type);
        payment.setPaymentType(paymentType);
        payment.setTotalAmount(totalAmount);
        payment.setOrderId(orderId);

        payment.setPaymentStatus(PaymentStatusEnum.success.getCode().longValue());
        paymentMapper.insertSelective(payment);
        return payment;
    }


    /**
     * 续订条件
     */
    @Override
    public RenewConditionEntity renewCondition(RenewConditionReqDto renewConditionReqDto) {
        String custId = renewConditionReqDto.getCustId();
        Integer cardType = renewConditionReqDto.getCardType();
        Integer cardLevel  = renewConditionReqDto.getUserCardLevel();

        RenewConditionEntity renewConditionEntity = new RenewConditionEntity();
        renewConditionEntity.setFlag(false);
        List<SubMemberCardRightsResp> subMemberCardRightsResps = iNewRightsV3Service.getCardConfigByCardTypeAndSuitCardLevel(cardType, cardLevel);
        if (CollectionUtils.isEmpty(subMemberCardRightsResps) || Integer.valueOf(0).equals(subMemberCardRightsResps.get(0).getHaveRenewalOffer())) {
            log.info("卡配置不存在不满足条件");
            return renewConditionEntity;
        }
        // 天数
        Integer expireDays = subMemberCardRightsResps.get(0).getSubExpireDays();
        BCustomerExpand bCustomerExpand = ibCustomerExpandService.getById(custId);
        if (bCustomerExpand == null || StringUtils.isEmpty(bCustomerExpand.getLateRightsId())) {
            log.info("bCustomerExpand信息不存在不满足条件");
            return renewConditionEntity;
        }
        BSubscribeInfo bSubscribeInfo = ibSubscribeInfoService.getById(bCustomerExpand.getLateRightsId());
        if (bSubscribeInfo == null || Long.valueOf(3).equals(bSubscribeInfo.getStatus())) {
            log.info("订阅信息不存在不满足条件");
            return renewConditionEntity;
        }

        if (new DateTime(bSubscribeInfo.getEndTime()).plusDays(expireDays).isBefore(DateTime.now())) {
            log.info("续订天数不满足条件");
            return renewConditionEntity;
        }
        renewConditionEntity.setTimeDifference(new DateTime(bSubscribeInfo.getEndTime()).plusDays(expireDays).getMillis());
        List<BSubscribePlan> plans = ibSubscribePlanService.getListBySubId(bCustomerExpand.getLateRightsId());
        if (plans.stream().anyMatch(e -> !Long.valueOf(3).equals(e.getStatus()) && !Long.valueOf(4).equals(e.getStatus()))) {
            return renewConditionEntity;
        }

        long payCount = plans.stream().filter(e -> (e.getPayPrice().compareTo(BigDecimal.ZERO) > 0 || e.getSavePrice().compareTo(BigDecimal.ZERO) > 0)).count();
        renewConditionEntity.setPayCount(payCount);
        if (Long.valueOf(payCount).intValue() >= 2) {
            renewConditionEntity.setFlag(true);
        }else {
            log.info("支付盒子数量不满足条件");
        }
        return renewConditionEntity;
    }


    @Override
    public void unSubscribeByPayWay(UnSubscribeContext unSubscribeContext) {
        log.info("退订subId:{} 入参：{}", unSubscribeContext.getSubId(), JSONObject.toJSONString(unSubscribeContext));
        String subId = unSubscribeContext.getSubId();
        String refundAmount = unSubscribeContext.getRefundAmount();
        String refundPoint = unSubscribeContext.getRefundPoint();
        String unsubMemo = unSubscribeContext.getUnsubMemo();

        BSubscribeInfo nowSubInfo = Preconditions.checkNotNull(ibSubscribeInfoService.getById(subId));
        Preconditions.checkArgument(Long.valueOf(1).equals(nowSubInfo.getStatus()), "非订阅中状态不能退订");
        SubscribeOrder subscribeOrder = Preconditions.checkNotNull(subscribeOrderMapper.selectById(nowSubInfo.getSubOrderId()), "订阅订单不存在");

        CustomerAskBox customerAskBoxSearch = new CustomerAskBox();
        customerAskBoxSearch.setUnionid(nowSubInfo.getUnionid());
        customerAskBoxSearch.setStatus(Short.valueOf("0"));
        List<CustomerAskBox> customerAskBoxes = iCustomerAskBoxService.selectListBySelective(customerAskBoxSearch);
        if (CollectionUtils.isNotEmpty(customerAskBoxes)) {
            log.info("顾客有未完结的要盒或服务单，不允许退订 askBoxId:{}", customerAskBoxes.get(0).getId());
            throw new RuntimeException("顾客有未完结的要盒或服务单，不允许退订");
        }

        List<BoxWithBLOBs> boxLists =  boxMapper.selectOngoingsByUnionId(nowSubInfo.getUnionid());
        if (CollectionUtils.isNotEmpty(boxLists)) {
            log.info("顾客有未完结的要盒或服务单，不允许退订 boxId:{}", boxLists.get(0).getId());
            throw new RuntimeException("顾客有未完结的要盒或服务单，不允许退订");
        }

        BSubscribeInfo updateSubInfo = new BSubscribeInfo();
        updateSubInfo.setId(subId).setUpdateTime(new Date()).setEndTime(new Date()).setUnsubTime(new Date()).setStatus(3L).setUnsubMemo(unsubMemo).setUpdateBy(unSubscribeContext.getUpdateBy());

        //生成订阅订单
        SubscribeOrder addSubScribeOrder = new SubscribeOrder();
        BeanUtils.copyProperties(subscribeOrder, addSubScribeOrder);
        addSubScribeOrder.setId(idLeafService.getId());
        addSubScribeOrder.setShopSn("DYDD" + IdLeaf.getDateId("SUBSCRIBE_ORDER"));
        addSubScribeOrder.setTradeNo("SYLS" + IdLeaf.getDateId("SUBSCRIBE_ORDER"));
        addSubScribeOrder.setType(SubscribeOrderTypeEnum.REFUND.getCode().longValue());
        addSubScribeOrder.setStatus(SubscribeOrderStatusEnum.paid.getCode().longValue());
        addSubScribeOrder.setCreateTime(new Date());
        if (StringUtils.isNotEmpty(refundAmount)) {
            Preconditions.checkArgument(StringUtils.isNotEmpty(refundAmount) && Double.valueOf(refundAmount) > 0, "金额不能为空并且金额不能小于0");
            BigDecimal paidAmount = new BigDecimal(subscribeOrder.getPayPrice().toString());
            Preconditions.checkArgument(Double.valueOf(refundAmount) <= paidAmount.doubleValue(), "退款金额不能大于支付金额");
            addSubScribeOrder.setRefundAmout(new Float(refundAmount));
        }
        if (StringUtils.isNotEmpty(refundPoint)) {
            Preconditions.checkArgument(StringUtils.isNotEmpty(refundPoint) && Integer.valueOf(refundPoint) >= 0, "积分不能为空且不能小于0");
            String paidPoint = subscribeOrder.getPayPoint();
            if (Integer.valueOf(refundPoint) > 0) {
                Preconditions.checkArgument(Double.valueOf(refundPoint) <= Double.valueOf(paidPoint), "退还积分不能大于兑换积分");
            }
            addSubScribeOrder.setRefundPoint(refundPoint);
        }

        BCustomerExpand updateCustomerExpand = new BCustomerExpand();
        updateCustomerExpand.setUserId(nowSubInfo.getCustId());
        updateCustomerExpand.setSubStatus(SubStatusEnum.UNSUB.getCode());
        updateCustomerExpand.setUpdateTime(new Date());

        InvalidSiteSubscribePlanContext invalidSiteSubscribePlanContext = InvalidSiteSubscribePlanContext.builder()
                .subId(subId).cardType(nowSubInfo.getCardType()).custId(nowSubInfo.getCustId()).build();

        String trade_no = "";
        String paymentId = "";
        // 微信支付
        if (Long.valueOf(1).equals(nowSubInfo.getPayWay())) {
            paymentId = idLeafService.getId();
            trade_no = applyRefundBySqb(addSubScribeOrder.getSqbSn(), subscribeOrder.getShopSn(), addSubScribeOrder.getRefundAmout().toString());
        }
        // 积分
        if (Long.valueOf(2).equals(nowSubInfo.getPayWay())) {
            if (Integer.valueOf(refundPoint) > 0) {
                log.info("subId:{}退订积分",nowSubInfo.getId());
                changeIntegral(nowSubInfo.getUnionid(), Long.valueOf(refundPoint), Long.valueOf(1), "BOX用户退订，订阅编号" + subscribeOrder.getShopSn(),nowSubInfo.getId());
            }
        }
        // 组合支付
        if (Long.valueOf(3).equals(nowSubInfo.getPayWay())) {
            paymentId = idLeafService.getId();
            trade_no = applyRefundBySqb(addSubScribeOrder.getSqbSn(), subscribeOrder.getShopSn(), addSubScribeOrder.getRefundAmout().toString());
            if (Integer.valueOf(refundPoint) > 0) {
                log.info("subId:{}退订积分", nowSubInfo.getId());
                changeIntegral(nowSubInfo.getUnionid(), Long.valueOf(refundPoint), Long.valueOf(1), "BOX用户退订，订阅编号" + subscribeOrder.getShopSn(),nowSubInfo.getId());
            }
        }
        if (StringUtils.isNotEmpty(trade_no)) {
            addSubScribeOrder.setWechatRefundSn(trade_no);
        }
        String finalPaymentId = paymentId;

        CreateSubLogContext createSubLogContext = CreateSubLogContext.builder()
                .detailType(0L)
                .operateType(1L)
                .subId(subId)
                .operatePeople(unSubscribeContext.getUpdateBy()).build();
        template.execute(action -> {
            ibSubscribeInfoService.updateById(updateSubInfo);
            subscribeOrderMapper.insert(addSubScribeOrder);
            ibCustomerExpandService.updateById(updateCustomerExpand);
            // 考虑异步
            handleInvalidSiteSubscribePlan(invalidSiteSubscribePlanContext);
            // 微信和组合
            if (Long.valueOf(1).equals(nowSubInfo.getPayWay()) || Long.valueOf(3).equals(nowSubInfo.getPayWay())) {
                createPayment(finalPaymentId, nowSubInfo.getCustId(), "1", new Double(refundAmount), Long.valueOf(7), Long.valueOf(20), addSubScribeOrder.getId());
            }

            // 正式卡-退订
            if (Long.valueOf(1).equals(nowSubInfo.getCardType())) {
                log.info("订阅计划退订日志subId:{}",createSubLogContext.getSubId());
                iCombineSubLogService.createSubLogCxt(createSubLogContext);
            }
            return action;
        });
    }

    @Override
    public void batchUpdateBlackGudie(String phone) {
        // 查询所有 3000个     离职的为 N   在职的为 Y
        // 查询在职的
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, 50000);
        List<EmployeeBase> onLine = employeeBaseMapper.selectByPhoneAndPositionAndLinkSourceByIsActive(1,phone);

        //拿到在职的进行拉黑，  不是黑名单的用户才需要拉黑
        if(CollectionUtils.isNotEmpty(onLine)){
            List<List<EmployeeBase>> partition = Lists.partition(onLine, 500);
            // 查询需要拉黑的用户进行拉黑
            for (List<EmployeeBase> employeeBases : partition) {
                // 根据手机号查询用户不是黑名单中的用户  黑名单中的不管
                List<String> phones = employeeBases.stream().filter(r -> StringUtils.isNotBlank(r.getMobile())).map(r -> r.getMobile()).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(phones)){
                    List<CustomerDetails> list = customerDetailsMapper.selectByPhones(phones);
                    iCustomerDetailsService.blockOrUnBlockUsers(list,1);
                }
            }
        }

        // 查询离职的
        com.github.pagehelper.Page<Object> hPage2 = PageHelper.startPage(1, 50000);
        List<EmployeeBase> offLine = employeeBaseMapper.selectByPhoneAndPositionAndLinkSourceByIsActive(0,phone);

        //拿到离职的进行解除黑名单， 是黑名单 才需要解除
        if(CollectionUtils.isNotEmpty(offLine)){
            List<List<EmployeeBase>> partition = Lists.partition(offLine, 500);
            // 查询需要拉黑的用户进行拉黑
            for (List<EmployeeBase> employeeBases : partition) {
                // 根据手机号查询用户不是黑名单中的用户  黑名单中的不管
                List<String> phones = employeeBases.stream().filter(r -> StringUtils.isNotBlank(r.getMobile())).map(r -> r.getMobile()).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(phones)){
                    List<CustomerDetails> list = customerDetailsMapper.selectByPhones(phones);
                    iCustomerDetailsService.blockOrUnBlockUsers(list,0);
                }
            }
        }

    }

    /**
     * 异步处理失效 订阅计划
     * @param invalidSiteSubscribePlanContext
     */
    void handleInvalidSiteSubscribePlan(InvalidSiteSubscribePlanContext invalidSiteSubscribePlanContext) {
        UnSubscribeEvent event = new UnSubscribeEvent(invalidSiteSubscribePlanContext,
                Long.valueOf(IdWorkLeaf.getInstance().getId()),
                Long.valueOf(IdWorkLeaf.getInstance().getId()),
                UUID.randomUUID());
        try {
            iUnSubscribeEventBus.post(event);
        } catch (PersistentBus.EventBusException e) {
            throw new RuntimeException("处理退订失效订阅计划异步失败");
        }
    }


    /***
     * 微信申请退款
     * @param sqbSn 收钱吧sn
     * @param refund_request_no 退款单号
     * @param refundAmout 退款金额
     * @return
     */
    public String applyRefundBySqb(String sqbSn, String refund_request_no, String refundAmout) {
        log.info("sqb退款 sqbSn:{} refund_request_no:{} refundAmout:{}", sqbSn, refund_request_no, refundAmout);
        BigDecimal refundFee = new BigDecimal(refundAmout).multiply(new BigDecimal(100));
        RefundContext refundContext = new RefundContext();
        // 收钱吧id
        refundContext.setSn(sqbSn);
        // 退款序列号
        refundContext.setRefund_request_no(refund_request_no);
        // 退款金额
        refundContext.setRefund_amount(String.valueOf(refundFee.intValue()));
        // 支付平台的订单凭证号
        String trade_no;
        // 查询退款序列号是否退款成功
        RefundResp queryEntity = refundService.getRefundOrder(sqbSn, refund_request_no);
        if(queryEntity.getRefundStatus().equals("NOT_EXISTS")){
            // 可以退款
            RefundReq refundReq = new RefundReq();
            refundReq.setType(2);
            refundReq.setSqbSn(sqbSn);
            refundReq.setOutRefundNo(refund_request_no);
            refundReq.setRefundFee(refundFee.intValue());
            refundReq.setNotifyUrl("");
            RefundResp refund = handlePayService.refund(refundReq);
            trade_no = refund.getTransactionId();
        }else if(queryEntity.getRefundStatus().equals("SUCCESS")){
            trade_no = queryEntity.getTransactionId();
        }else{
            //退款失败
            log.info("退款失败 = {}",JSONObject.toJSONString(queryEntity));
            throw  new RuntimeException("退款失败！");
        }
        return trade_no;
    }


    /**
     * 获取退款信息
     *
     * @param sn                收钱吧单号
     * @param refund_request_no 退款单号
     * @return
     */
    public QueryEntity getRefundOrder(String sn, String refund_request_no) {
        QueryContext queryContext = new QueryContext();
        queryContext.setSn(sn);
        queryContext.setRefund_request_no(refund_request_no);
        SqbWithBizCommonResponse<QueryEntity> sqbWithBizCommonResponse = iSqbProxyService.query(queryContext);

        QueryEntity queryEntity = sqbWithBizCommonResponse.getBiz_response().getData();
        if (queryEntity == null || !StringUtils.equals(queryEntity.getStatus(), "SUCCESS")
                || (!StringUtils.equals(queryEntity.getOrder_status(), "PARTIAL_REFUNDED") && !StringUtils.equals(queryEntity.getOrder_status(), "REFUNDED"))
        ) {
            return null;
        }
        return queryEntity;
    }

    /**
     * 扣减积分
     *
     * @param unionId
     * @param point   积分
     * @param type    0支出 1 输入
     * @param memo  格式 BOX用户退订，订阅编号XXXXXX
     */
    public void changeIntegral(String unionId, Long point, Long type, String memo,String subId) {
        log.info("changeIntegral积分unionId:{} point:{} type:{} memo:{} subId:{}", unionId, point, type, memo,subId);
        /**
         * 1.积分表
         * 2.进行积分扣减
         * 3.进行修改积分记录
         */
        IntegralRecord integralRecord = new IntegralRecord();
        integralRecord.setId(idLeafService.getId());
        integralRecord.setUnionid(unionId);
        integralRecord.setPoint(point);
        // 0支出 1 输入
        integralRecord.setType(type);
        // 1处理成功 0处理中 2失败
        integralRecord.setStatus(Long.valueOf(0));
        integralRecord.setMemo(memo);
        integralRecordMapper.insertSelective(integralRecord);


        List<MemberCardEntity> list = iExternalCustomerVipService.clientVipVoList(unionId);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(list),"用户会员卡不存在");
        MemberCardEntity memberCardEntity = list.get(0);
        Long vipId = memberCardEntity.getId();


        SubtractUserIntegralContext subtractUserIntegralContext = new SubtractUserIntegralContext();
        subtractUserIntegralContext.setUnionId(unionId);
        subtractUserIntegralContext.setIntegral(point);
        //实时 1,非实时 2
        subtractUserIntegralContext.setSync(Long.valueOf(1));
        //渠道:微商城 1,线下 2
        subtractUserIntegralContext.setType(Long.valueOf(4));
        //会员id
        subtractUserIntegralContext.setVipId(vipId);
        subtractUserIntegralContext.setReason(memo);
        subtractUserIntegralContext.setStoreName(memberCardEntity.getStoreCode()+"-"+memberCardEntity.getStoreName());
        if(0 == type.longValue()){
            subtractUserIntegralContext.setSerialNo(subId);
        }else{
            subtractUserIntegralContext.setSerialNo(subId+"-1");
        }
        // 扣减积分
        iExternalCustomerIntegralService.SubtractUserIntegral(subtractUserIntegralContext);
    }



    public Payment createPayment(String paymentId, String customerId, String payFor, Double totalAmount, Long paymentType, Long type, String orderId) {
        Payment payment = new Payment();
        payment.setId(paymentId);
        payment.setCustomerId(customerId);
        payment.setPayer(customerId);
        payment.setPaymentSn(payFor);
        payment.setType(type);
        payment.setPaymentType(paymentType);
        payment.setTotalAmount(totalAmount);
        payment.setOrderId(orderId);

        payment.setPaymentStatus(PaymentStatusEnum.success.getCode().longValue());
        paymentMapper.insertSelective(payment);
        return payment;
    }


    @Override
    public HasVoucherSubResp hasVoucherSub(String unionId) {
        List<VoucherBase> voucherBases = voucherBaseMapper.findSubscribeVoucherByAwardId(subAwardId, unionId);
        HasVoucherSubResp hasVoucherSubResp = new HasVoucherSubResp();
        hasVoucherSubResp.setAwardId(subAwardId);
        hasVoucherSubResp.setFlag(false);
        if (CollectionUtils.isEmpty(voucherBases)) {
            return hasVoucherSubResp;
        }
        hasVoucherSubResp.setFlag(true);
        hasVoucherSubResp.setVoucherBase(voucherBases);
        return hasVoucherSubResp;
    }

    public void checkVoucherSub(String unionId, String voucherId) {
        HasVoucherSubResp hasVoucherSubResp = hasVoucherSub(unionId);
        if (!hasVoucherSubResp.getFlag()) {
            throw new RuntimeException("不存在订阅凭证");
        }
        Boolean flag = CollectionUtils.isNotEmpty(hasVoucherSubResp.getVoucherBase()) ? hasVoucherSubResp.getVoucherBase().stream().anyMatch(e -> ObjectUtils.equals(e.getId(), Long.valueOf(voucherId))) : false;
        if (!flag) {
            throw new RuntimeException("不存在订阅凭证");
        }
    }
}
