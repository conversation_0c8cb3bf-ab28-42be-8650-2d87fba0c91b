package com.jnby.module.mapper;

import com.jnby.module.eb.context.SolveBoxBusinessContext;
import com.jnby.module.eb.entity.DeliverOkMsgEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface BoxEbDeliverOkMapper {
     BoxEbDeliverOkMapper INSTANCE = Mappers.getMapper( BoxEbDeliverOkMapper.class );

     SolveBoxBusinessContext okMsg2Context(DeliverOkMsgEntity entity);
}
