package com.jnby.module.facade.context;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddDamoMemberContext {
    /**
     * 自动默认等级
     */
    private Integer autoGrade;
    /**
     * 微信开放平台唯一标识
     */
    private String brandId;
    /**
     * 是否gic开卡
     */
    private Boolean gicOpenCard;

    /**
     * 服务门店
     */
    private String mainStoreCode;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 姓名
     */
    private String name;
    /**
     * 开卡时间 格式:yyyy-mm-dd hh:mm:ss
     */
    private String openCardDate;
    /**
     * 开卡门店
     */
    private String openStoreCode;
    /**
     * 性别
     */
    private String sex;
    /**
     * 渠道
     */
    private String sourceCode;

    /**
     * 微信开放平台唯一标识
     */
    private String unionId;


    /**
     * 开卡导购
     */
    private String openClerkCode;

    /**
     * 服务导购
     */
    private String mainClerkCode;
}
