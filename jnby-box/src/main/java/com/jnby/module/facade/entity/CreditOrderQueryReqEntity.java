package com.jnby.module.facade.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/27 19:49
 * @description
 */
@Data
public class CreditOrderQueryReqEntity {

     // 信用服务订单号，out_order_no与credit_biz_order_id至少传一个
     @JSONField(name = "credit_biz_order_id")
     private String creditBizOrderId;

     // 商户外部单号，out_order_no与credit_biz_order_id至少传一个
     @JSONField(name = "out_order_no")
     private String outOrderNo;

}
