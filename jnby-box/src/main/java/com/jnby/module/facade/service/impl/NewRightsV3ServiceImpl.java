package com.jnby.module.facade.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseCodeEnum;
import com.jnby.common.ResponseResult;
import com.jnby.infrastructure.box.mapper.BSubscribeInfoMapper;
import com.jnby.infrastructure.box.model.BSubscribeInfo;
import com.jnby.module.facade.context.InvalidContext;
import com.jnby.module.facade.context.OpenCardContext;
import com.jnby.module.facade.context.RecoverContext;
import com.jnby.module.facade.service.INewRightsV3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springcenter.marketing.api.IRightsV3Api;
import org.springcenter.marketing.api.checkRightsDto.CheckRightCouponV3DtoForPre;
import org.springcenter.marketing.api.context.UseRightsContext;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.entity.CheckRightsEntity;
import org.springcenter.marketing.api.enums.ApplicablePartyEnum;
import org.springcenter.marketing.api.enums.CardTypeEnum;
import org.springcenter.marketing.api.enums.SuitCardLevelEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NewRightsV3ServiceImpl implements INewRightsV3Service {

    @Resource
    private IRightsV3Api iRightsV3Api;

    @Autowired
    private BSubscribeInfoMapper bSubscribeInfoMapper;

    /**
     * 获取卡配置
     * @param cardType 1 正式卡  2 单次卡  3 免费卡
     * @return
     */
    @Override
    public List<SubMemberCardRightsResp> getCardConfig(Integer cardType) {
        CommonRequest<SubMemberCardRightsReq> request = new CommonRequest<>();
        SubMemberCardRightsReq subMemberCardRightsReq = new SubMemberCardRightsReq();

        subMemberCardRightsReq.setCardType(cardType);
        request.setRequestData(subMemberCardRightsReq);
        ResponseResult<List<SubMemberCardRightsResp>> subMemberCardRights = iRightsV3Api.getSubMemberCardRights(request);

        return subMemberCardRights.getData();
    }

    /**
     * 获取卡配置
     * @param cardType cardType 1 正式卡  2 单次卡  3 免费卡
     * @return (适用卡等级  0 银卡  1 金卡  2 铂金卡  3 白金卡  4 黑卡)
     */
    @Override
    public Map<Integer, SubMemberCardRightsResp> getLevelWithCardConfigMap(Integer cardType) {
        Map<Integer, SubMemberCardRightsResp> map = new HashMap<>();
        getCardConfig(cardType).forEach(e -> {
            map.put(e.getSuitCardLevel(), e);
        });
        return map;
    }


    /**
     * 获取
     * @param cardType 1 正式卡  2 单次卡  3 免费卡
     * @param suitCardLevel 适用卡等级   0 银卡  1 金卡  2 铂金卡  3 白金卡  4 黑卡
     * @return
     */
    @Override
    public List<SubMemberCardRightsResp> getCardConfigByCardTypeAndSuitCardLevel(Integer cardType, Integer suitCardLevel) {

        CommonRequest<SubMemberCardRightsReq> request = new CommonRequest<>();
        SubMemberCardRightsReq subMemberCardRightsReq = new SubMemberCardRightsReq();
        subMemberCardRightsReq.setCardTypes(Lists.newArrayList(cardType));
        subMemberCardRightsReq.setSelectCardType(true);
        subMemberCardRightsReq.setSuitCardLevel(suitCardLevel);
        request.setRequestData(subMemberCardRightsReq);
        log.info("获取正式卡、免费卡、单次卡信息入参:request:{}", JSONObject.toJSONString(request));
        ResponseResult<List<SubMemberCardRightsResp>> subMemberCardRights = iRightsV3Api.getSubMemberCardRights(request);
        Preconditions.checkNotNull(subMemberCardRights, "获取正式卡、免费卡、单次卡信息异常");
        log.info("获取正式卡、免费卡、单次卡信息返回:subMemberCardRights:{}", JSONObject.toJSONString(subMemberCardRights));
        return subMemberCardRights.getData();
    }

    @Override
    public List<SubMemberCardRightsResp> getCardConfigByCardTypeAndSuitCardLevel(List<Integer> cardTypes, Integer suitCardLevel) {
        CommonRequest<SubMemberCardRightsReq> request = new CommonRequest<>();
        SubMemberCardRightsReq subMemberCardRightsReq = new SubMemberCardRightsReq();
        subMemberCardRightsReq.setCardTypes(cardTypes);
        subMemberCardRightsReq.setSelectCardType(false);
        subMemberCardRightsReq.setSuitCardLevel(suitCardLevel);
        request.setRequestData(subMemberCardRightsReq);

        log.info("获取正式卡、免费卡、单次卡信息入参:request:{}",JSONObject.toJSONString(request) );
        ResponseResult<List<SubMemberCardRightsResp>> subMemberCardRights = iRightsV3Api.getSubMemberCardRights(request);
        Preconditions.checkNotNull(subMemberCardRights, "获取正式卡、免费卡、单次卡信息异常");
        log.info("获取正式卡、免费卡、单次卡信息返回:subMemberCardRights:{}", JSONObject.toJSONString(subMemberCardRights));
        Preconditions.checkArgument(ResponseCodeEnum.SUCCESS.getCode() == subMemberCardRights.getCode(), "获取正式卡 免费卡 单次卡信息 异常");
        return subMemberCardRights.getData();
    }

    @Override
    public List<PreCalcResp> batchPreCalcExpansionCoupon(List<CheckRightCouponV3DtoForPre> list){
        CommonRequest<List<CheckRightCouponV3DtoForPre>> request = new CommonRequest<>();
        request.setRequestData(list);
        log.info("获取下个节点膨胀券,request={}", JSON.toJSONString(request));
        ResponseResult<List<PreCalcResp>> result = iRightsV3Api.preCalcExpansionCoupon(request);
        log.info("获取下个节点膨胀券,resp={}", JSON.toJSONString(result));
        if(0 != result.getCode()){
            throw new RuntimeException("批量调用预计算膨胀券异常"+result.getMsg());
        }
        return result.getData();
    }

    @Override
    public List<BNewUserBoxGiftResp> batchGetUserBoxGiftRights(List<UserBoxGiftRightsReq> list){
        CommonRequest<List<UserBoxGiftRightsReq>> request = new CommonRequest<>();
        request.setRequestData(list);
        log.info("获取下个节点随盒礼,request={}", JSON.toJSONString(request));
        ResponseResult<List<BNewUserBoxGiftResp>> result = iRightsV3Api.getUserBoxGiftRights(request);
        log.info("获取下个节点随盒礼,resp={}", JSON.toJSONString(result));
        if(0 != result.getCode()){
            throw new RuntimeException("批量调用预计算随盒礼异常"+result.getMsg());
        }
        return result.getData();
    }


    @Override
    public List<UserSubV3CouponBySubIdResp> batchGetUserRole(List<String> subIds){
        CommonRequest<GetUserCardBySubId> request = new CommonRequest<>();
        GetUserCardBySubId getUserCardBySubId = new GetUserCardBySubId();
        getUserCardBySubId.setSubIds(subIds);
        request.setRequestData(getUserCardBySubId);
        if(subIds.size() > 10){
            Page page = new Page();
            page.setPageNo(1);
            page.setPageSize(subIds.size());
            request.setPage(page);
        }
        log.info("根据subIds批量获取膨胀规则,request={}", JSON.toJSONString(request));
        ResponseResult<List<UserSubV3CouponBySubIdResp>> result = iRightsV3Api.getUserSubV3CouponBySubId(request);
        log.info("根据subIds批量获取膨胀规则,resp={}", JSON.toJSONString(result));
        if(0 != result.getCode()){
            throw new RuntimeException("根据subIds批量获取膨胀规则异常"+result.getMsg());
        }
        return result.getData();
    }


    @Override
    public Integer convertSuitCardLevel(String content) {
        if(content.contains("铂金卡")){
            return Integer.valueOf(2);
        }
        if(content.contains("白金卡")){
            return Integer.valueOf(3);
        }
        if(content.contains("银卡")){
            return Integer.valueOf(0);
        }
        if(content.contains("黑卡")){
            return Integer.valueOf(4);
        }
        if(content.contains("金卡")){
            return Integer.valueOf(1);
        }
        if(content.contains("贵宾卡")){
            return Integer.valueOf(5);
        }
        return Integer.valueOf(0); // 默认银卡 无集团卡的情况
//       throw new RuntimeException("非法卡等级");
    }

    @Override
    public boolean checkRights(CheckRightsEntity checkRightsEntity) {
        // 传递unionid
        CommonRequest<CheckRightsEntity> request = new CommonRequest<>();
        checkRightsEntity.setIsDeductionRights(false);
        checkRightsEntity.setCardTypeEnum(CardTypeEnum.SUB);
        checkRightsEntity.setApplicationParty(ApplicablePartyEnum.BOX.getCode());
        request.setRequestData(checkRightsEntity);
        log.info("校验权益,req={}",JSONObject.toJSONString(request));
        ResponseResult<Boolean> result = iRightsV3Api.check(request);
        log.info("校验权益,resp={}",JSONObject.toJSONString(result));
        if(0 != result.getCode()){
            throw new RuntimeException("校验用户权益异常"+result.getMsg());
        }
        return result.getData();
    }

    @Override
    public boolean useRights(CheckRightsEntity checkRightsEntity) {
        // 如果用户使用的是免费的权益,补全免费卡
//        this.openFreeCard(checkRightsEntity);
        CommonRequest<CheckRightsEntity> request = new CommonRequest<>();
        checkRightsEntity.setIsDeductionRights(true);
        checkRightsEntity.setApplicationParty(ApplicablePartyEnum.BOX.getCode());
        request.setRequestData(checkRightsEntity);
        log.info("使用权益,req={}",JSONObject.toJSONString(request));
        ResponseResult<Boolean> result = iRightsV3Api.check(request);
        log.info("使用权益,resp={}",JSONObject.toJSONString(result));
        if (0 != result.getCode()) {
            throw new RuntimeException("使用用户权益异常" + result.getMsg());
        }
        // 不抛异常  使用权益
//        if(!checkRightsEntity.getIsHold()  && checkRightsEntity.getIsDeductionRights()){
//            return true;
//        }
        if (!result.getData()) {
            throw new RuntimeException("使用用户权益异常" + result.getMsg());
        }
        return result.getData();
    }

    private void openFreeCard(CheckRightsEntity checkRightsEntity){
        // 获取用户所有可用卡
        CommonRequest<UserAvailableCardApplicationPartyReq> userRequest = new CommonRequest<>();
        UserAvailableCardApplicationPartyReq userAvailableCardApplicationPartyReq = new UserAvailableCardApplicationPartyReq();
        userAvailableCardApplicationPartyReq.setApplicationParty(ApplicablePartyEnum.BOX.getCode());
        userAvailableCardApplicationPartyReq.setUnionid(checkRightsEntity.getUnionid());
        userRequest.setRequestData(userAvailableCardApplicationPartyReq);
        ResponseResult<List<BNewUserMemberCardResp>> userAllCardApplicationParty = iRightsV3Api.getUserAvailableCardApplicationParty(userRequest);
        if(userAllCardApplicationParty.getCode() == 0){
            List<BNewUserMemberCardResp> data = userAllCardApplicationParty.getData();
            if(CollectionUtils.isEmpty(data)){
                // 开免费卡
                openFreeCard(checkRightsEntity.getUnionid());
            }
            // 筛选 订阅卡
            List<BNewUserMemberCardResp> collect = data.stream().filter(r -> r.getCardType().equals(CardTypeEnum.FREE.getCode())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(collect)){
                // 开免费卡
                openFreeCard(checkRightsEntity.getUnionid());
            }
        }
    }

    private void openFreeCard(String unionid) {
        // 开通免费卡  找到这张免费卡
        CommonRequest<SubMemberCardRightsReq> request = new CommonRequest<>();
        SubMemberCardRightsReq subMemberCardRightsReq = new SubMemberCardRightsReq();
        subMemberCardRightsReq.setCardType(CardTypeEnum.FREE.getCode());
        request.setRequestData(subMemberCardRightsReq);
        ResponseResult<List<SubMemberCardRightsResp>> subMemberCardRights = iRightsV3Api.getSubMemberCardRights(request);
        if(subMemberCardRights.getCode() == 0){
            List<SubMemberCardRightsResp> data = subMemberCardRights.getData();
            if(CollectionUtils.isEmpty(data)){
                log.info("开通免费卡失败 : 原因未配置免费卡");
                return ;
            }
            List<SubMemberCardRightsResp> collect = data.stream().filter(r -> r.getSuitCardLevel().equals(SuitCardLevelEnum.SILVER.getCode())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(data)){
                log.info("开通免费卡失败 : 原因未配置适用银卡的免费卡");
                return ;
            }

            SubMemberCardRightsResp subMemberCardRightsResp = collect.get(0);
            String bMemberCardId = subMemberCardRightsResp.getBMemberCardId();
            OpenCardContext openCardContext = new OpenCardContext();
            openCardContext.setMemberCardId(bMemberCardId);
            openCardContext.setUnionid(unionid);
            openCardContext.setSuitCardLevel(SuitCardLevelEnum.SILVER.getCode());
            openCardContext.setCustomerName("自动开通");
            openCard(openCardContext);
        }
    }

    @Override
    public void openCard(OpenCardContext openCardContext) {
        CommonRequest commonRequest = new CommonRequest();
        OpenCardAndRightsReq openCardAndRightsReq = new OpenCardAndRightsReq();
        BeanUtils.copyProperties(openCardContext, openCardAndRightsReq);
        openCardAndRightsReq.setIsBlack(false);
        openCardAndRightsReq.setSubcribeId(openCardContext.getSubscribeId());
        // 查询权益信息
        BSubscribeInfo bSubscribeInfo = bSubscribeInfoMapper.selectById(openCardContext.getSubscribeId());
        openCardAndRightsReq.setStoreId(bSubscribeInfo.getCreateCardStoreId());
        commonRequest.setRequestData(openCardAndRightsReq);
        log.info("开卡:request:{}", JSONObject.toJSONString(commonRequest));
        ResponseResult responseResult = iRightsV3Api.openCard(commonRequest);
        Preconditions.checkNotNull(responseResult,"开卡异常");
        log.info("开卡:返回:{}", JSONObject.toJSONString(responseResult));
        Preconditions.checkArgument( ResponseCodeEnum.SUCCESS.getCode() == responseResult.getCode(), "开卡失败");
    }


    @Override
    public void recover(RecoverContext recoverContext) {
        UseRightsContext userRightsContext = org.springcenter.marketing.api.context.UseRightsContext.builder()
                .customerId(recoverContext.getCustomerId())
                .outNo(recoverContext.getOutNo())
                .useRightsTypeEnum(org.springcenter.marketing.api.enums.UseRightsTypeEnum.RECOARY)
                .applicationParty(ApplicablePartyEnum.BOX.getCode())
                .operationName("")
                .unionid(recoverContext.getUnionId())
                .isHold(recoverContext.getIsHold())
                .operationTypeEnum(org.springcenter.marketing.api.enums.OperationTypeEnum.SYSTEM)
                .build();
        CommonRequest recoverReq = new CommonRequest();
        recoverReq.setRequestData(userRightsContext);
        log.info("恢复权益:request:{}", JSONObject.toJSONString(recoverReq));
        ResponseResult<Boolean> result =  iRightsV3Api.recover(recoverReq);
        Preconditions.checkNotNull(recoverReq,"恢复权益");
        log.info("恢复权益:response:{}", JSONObject.toJSONString(result));
        Preconditions.checkArgument( ResponseCodeEnum.SUCCESS.getCode() == result.getCode(), "恢复权益失败");
        Preconditions.checkArgument(result.getData(), "恢复权益失败");

    }

    @Override
    public void invalid(InvalidContext invalidContext) {
        CommonRequest<LoseCardByOutNoReq> request = new CommonRequest<>();
        LoseCardByOutNoReq loseCardByOutNoReq = new LoseCardByOutNoReq();
        loseCardByOutNoReq.setSubcribeId(invalidContext.getSubscribeId());
        request.setRequestData(loseCardByOutNoReq);
        log.info("失效卡:request:{}", JSONObject.toJSONString(request));
        ResponseResult responseResult = iRightsV3Api.loseCardByOutNo(request);
        log.info("失效卡:response:{}", JSONObject.toJSONString(responseResult));
        Preconditions.checkArgument(ResponseCodeEnum.SUCCESS.getCode() == responseResult.getCode(), "失效卡失败");
    }

    @Override
    public void invalidSubPlans(List<BatchUseUserRightsReq> lists) {
        CommonRequest<List<BatchUseUserRightsReq>> request = new CommonRequest<>();
        request.setRequestData(lists);
        log.info("失效指定订阅计划.request:{}", JSONObject.toJSONString(lists));
        ResponseResult<Boolean> responseResult = iRightsV3Api.batchUseUserRights(request);
        log.info("失效指定订阅计划.response:{}", JSONObject.toJSONString(lists));
        Preconditions.checkArgument(ResponseCodeEnum.SUCCESS.getCode() == responseResult.getCode() && responseResult.getData(), "失效指定订阅计划失败");
    }



    @Override
    public List<BNewUserRightsCouponResp> getUserSubCouponRightsByOutNo(List<String> outNos) {
        CommonRequest<UserRightsByConsumeOutNoReq> request = new CommonRequest<>();
        UserRightsByConsumeOutNoReq userRightsByConsumeOutNoReq = new UserRightsByConsumeOutNoReq();
        userRightsByConsumeOutNoReq.setConsumeOutNos(outNos);
        request.setRequestData(userRightsByConsumeOutNoReq);
        log.info("查询权益券发放.request:{}", JSONObject.toJSONString(outNos));
        ResponseResult<List<BNewUserRightsCouponResp>> result = iRightsV3Api.getUserSubCouponRightsByConsumeOutNo(request);
        log.info("查询权益券发放.response:{}", JSONObject.toJSONString(result));
        if (0 != result.getCode()) {
            throw new RuntimeException("查询权益券发放异常" + result.getMsg());
        }
        return result.getData();
    }
}
