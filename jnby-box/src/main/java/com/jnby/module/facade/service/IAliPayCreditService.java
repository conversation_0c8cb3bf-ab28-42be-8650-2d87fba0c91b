package com.jnby.module.facade.service;

import com.jnby.module.facade.entity.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/22 16:56
 * @description 支付宝信用服务
 */
public interface IAliPayCreditService {


     // 芝麻服务 开通/授权
     CreditSignRespEntity creditSign(CreditSignReqEntity req);

     // 芝麻服务 查询服务开通/授权信息
     CreditQueryRespEntity creditQuery(CreditQueryReqEntity req);

     // 芝麻服务-订单 下单（免用户确认场景）
     CreditOrderCreateRespEntity creditOrderCreate(CreditOrderCreateReqEntity req);

     // 芝麻服务-订单 信用服务订单查询
     CreditOrderQueryRespEntity creditOrderQuery(CreditOrderQueryReqEntity req);

     // 芝麻服务-订单 结束信用服务订单
     CreditOrderFinishRespEntity creditOrderFinish(CreditOrderFinishReqEntity req);

     // 芝麻服务-订单 统一收单交易订单支付接口
     CreditOrderPayRespEntity creditOrderPay(CreditOrderPayReqEntity req);

     // 芝麻服务-订单 统一收单交易查询
     CreditOrderPayQueryRespEntity creditOrderPayQuery(CreditOrderPayQueryReqEntity req);

     // 芝麻服务-订单 统一收单交易退款接口
     CreditOrderPayRefundRespEntity creditOrderPayRefund(CreditOrderPayRefundReqEntity req);

     // 支付宝验签
     boolean checkAlipaySign(Map<String, String> pushMap);



}
