package com.jnby.module.facade.entity;


import com.alipay.api.internal.mapping.ApiField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
public class GetAuthTokenEntity {
    private String accessToken;

    private String alipayUserId;

    private Date authStart;

    private String authTokenType;

    private String expiresIn;

    private String openId;

    private String reExpiresIn;

    private String refreshToken;

    private String unionId;

    private String userId;
}
