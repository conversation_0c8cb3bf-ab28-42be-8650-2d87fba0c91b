package com.jnby.module.oauth.oa.service;

import com.jnby.module.oauth.oa.entity.OaEntity;
import com.jnby.module.oauth.oa.entity.SaleEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/10
 */
public interface IOaService {


    /**
     * 获取所有OA数据
     *
     * @return
     */
    List<OaEntity> getOaEntityList();

    /**
     * 获取所有导购组织数据
     *
     * @return
     */
    List<SaleEntity> getSaleEntityList();

    /**
     * 根据ID获取OA数据
     * @param id
     * @return
     */
    OaEntity getOaDetailById(Integer id);


}