package com.jnby.module.alipay.service;

import com.jnby.infrastructure.box.model.BOauthInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @auther lwz
 * @create 2023-11-28 15:58:40
 * @describe 服务类
 */
public interface IBOauthInfoService extends IService<BOauthInfo> {

    /**
     *  获取box的支付包小程序 授权 信息
     * @param appId
     * @param unionId
     * @return
     */
    BOauthInfo getByUnionId(String unionId);


    /**
     * 根据custId查询用户
     * @param custId
     * @return
     */
    BOauthInfo getByCustId(String custId);
    /**
     * 根据cusId和类型获取授权信息
     * @param cusId
     * @param type
     * @return
     */
    BOauthInfo getInfoByCusIdAndType(String cusId,Long type);



    BOauthInfo getInfoByOpenIdAndType(String openId,Long type);
    /**
     * 通过条件查询
     * @param bOauthInfo
     * @return
     */
    List<BOauthInfo> getListBySearch(BOauthInfo bOauthInfo);



}
