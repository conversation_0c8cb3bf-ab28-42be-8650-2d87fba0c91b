package com.jnby.module.jic.service.impl;

import com.alibaba.fastjson.JSON;
import com.jnby.common.Jic.JicSdkCommonResponse;
import com.jnby.module.jic.entity.*;
import com.jnby.module.jic.service.IJICBalanceService;
import com.jnby.module.order.context.CalcPriceContext;
import com.jnby.module.order.enums.OrderPaymentPayWayEnum;
import com.jnby.module.webapi.IJICBalanceHttpApi;
import com.jnby.module.zto.util.SignatureUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springcenter.calc.api.dto.CalcResp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/1 13:23
 * @description
 */
@Service
@Slf4j
public class JICBalanceServiceImpl implements IJICBalanceService {


     @Value("${jic.balance.client.id}")
     private String clientId;
     @Value("${jic.balance.client.secret}")
     private String clientSecret;


     @Resource
     private IJICBalanceHttpApi ijicBalanceHttpApi;

     public void setJICBalanceSign(JICBalanceSignReq jicBalanceSignReq){
          long time = System.currentTimeMillis();
          jicBalanceSignReq.setClientId(clientId);
          jicBalanceSignReq.setTimestamp(time);
          jicBalanceSignReq.setSign(getSign(time,clientId,clientSecret));
     }

     // 签名生成规则：MD5(timestamp + MD5(clientId) + clientSecret)
     public String getSign(Long timestamp,String clientId,String clientSecret){
          return SignatureUtils.md5(timestamp + SignatureUtils.md5(clientId) + clientSecret);
     }


     @Override
     public JICBalanceRespEntity getBalance(String unionId){
          JICBalanceReqEntity jicBalanceReqEntity = new JICBalanceReqEntity();
          jicBalanceReqEntity.setUnionId(unionId);
          try {
               Response<JicSdkCommonResponse<JICBalanceRespEntity>> result = ijicBalanceHttpApi.getJICBalance(jicBalanceReqEntity).execute();
               if(!result.isSuccessful()) {
                    throw new RuntimeException("获取储值卡余额失败," + result.errorBody());
               }
               JicSdkCommonResponse<JICBalanceRespEntity> response = result.body();
               if(!response.getSuccess()){
                    throw new RuntimeException("获取储值卡余额失败," + response.getMsg());
               }
               return response.getData();
          } catch (Exception e) {
               log.error("获取储值卡余额异常,unionId={}", unionId, e);
               throw new RuntimeException("获取储值卡余额异常," + e.getMessage());
          }
     }


     @Override
     public FreezeJICBalanceRespEntity freezeBalance(FreeBalanceEntity freeBalance){
          FreezeJICBalanceReqEntity freezeJICBalanceReqEntity = new FreezeJICBalanceReqEntity();
          BeanUtils.copyProperties(freeBalance,freezeJICBalanceReqEntity);
          // 获取签名
          setJICBalanceSign(freezeJICBalanceReqEntity);
          String type = freeBalance.getFreeType() == 1 ? "冻结" : "解冻";
          try {
               Response<JicSdkCommonResponse<FreezeJICBalanceRespEntity>> result;
               if (freeBalance.getFreeType() == 1) {
                    result = ijicBalanceHttpApi.freezeJICBalance(freezeJICBalanceReqEntity).execute();
               }else{
                    freezeJICBalanceReqEntity.setTransId(freezeJICBalanceReqEntity.getRequestId());
                    result = ijicBalanceHttpApi.unfreezeJICBalance(freezeJICBalanceReqEntity).execute();
               }
               if(!result.isSuccessful()) {
                    throw new RuntimeException(type + "储值卡余额失败," + result.errorBody());
               }
               JicSdkCommonResponse<FreezeJICBalanceRespEntity> response = result.body();
               if(!response.getSuccess()){
                    throw new RuntimeException(type + "储值卡余额失败," + response.getMsg());
               }
               if(ObjectUtils.isEmpty(response.getData()) || ObjectUtils.isEmpty(response.getData().getCode())){
                    throw new RuntimeException(type + "储值卡余额失败,返回data为空");
               }
               if(!"0".equals(response.getData().getCode().getResult())){
                    throw new RuntimeException(type + "储值卡余额失败," + response.getData().getCode().getResultMsg());
               }
               return response.getData();
          } catch (Exception e) {
               log.error(type + "储值卡余额异常,param = {}", JSON.toJSONString(freezeJICBalanceReqEntity), e);
               throw new RuntimeException(type + "储值卡余额异常," + e.getMessage());
          }
     }

     @Override
     public ReduceJICBalanceRespEntity reduceBalance(ReduceJICBalanceReqEntity jicBalanceReqEntity) {
          // 获取签名
          setJICBalanceSign(jicBalanceReqEntity);
          try {
               Response<JicSdkCommonResponse<ReduceJICBalanceRespEntity>> result = ijicBalanceHttpApi.reduceJICBalance(jicBalanceReqEntity).execute();
               if(!result.isSuccessful()) {
                    throw new RuntimeException("扣减储值卡余额失败," + result.errorBody());
               }
               JicSdkCommonResponse<ReduceJICBalanceRespEntity> response = result.body();
               if(!response.getSuccess()){
                    throw new RuntimeException("扣减储值卡余额失败," + response.getMsg());
               }
               return response.getData();
          } catch (Exception e) {
               log.error("扣减储值卡余额异常,param = {}", JSON.toJSONString(jicBalanceReqEntity), e);
               throw new RuntimeException("扣减储值卡余额异常" + e.getMessage());
          }
     }

     @Override
     public RefundJICBalanceRespEntity refundBalance(RefundJICBalanceReqEntity refundJICBalanceReqEntity) {
          // 获取签名
          setJICBalanceSign(refundJICBalanceReqEntity);
          try {
               Response<JicSdkCommonResponse<RefundJICBalanceRespEntity>> result = ijicBalanceHttpApi.refundJICBalance(refundJICBalanceReqEntity).execute();
               if(!result.isSuccessful()) {
                    log.error("退回储值卡余额异常,param = {},resp={}",JSON.toJSONString(refundJICBalanceReqEntity), JSON.toJSONString(result));
                    throw new RuntimeException("退回储值卡余额失败," + result.errorBody());
               }
               JicSdkCommonResponse<RefundJICBalanceRespEntity> response = result.body();
               if(!response.getSuccess()){
                    log.error("退回储值卡余额异常,param = {},resp={}",JSON.toJSONString(refundJICBalanceReqEntity), JSON.toJSONString(response));
                    throw new RuntimeException("退回储值卡余额失败," + response.getMsg());
               }
               return response.getData();
          } catch (Exception e) {
               log.error("退回储值卡余额异常,param = {}",JSON.toJSONString(refundJICBalanceReqEntity), e);
               throw new RuntimeException("退回储值卡余额异常" + e.getMessage());
          }
     }

     @Override
     public void calcBalance(CalcPriceContext context, BigDecimal balanceAmt){
          if(ObjectUtils.isEmpty(balanceAmt)){
               return;
          }
          if(BigDecimal.ZERO.compareTo(balanceAmt) >= 0){
               return;
          }

          if(BigDecimal.ZERO.compareTo(context.getOutGoods().getTotpriceactual()) >= 0){
               return;
          }
          if(balanceAmt.compareTo(context.getOutGoods().getTotpriceactual()) > 0){
               // 值不会传递出去
               balanceAmt = context.getOutGoods().getTotpriceactual();
          }

          // 分摊计算
          BigDecimal finalBalanceAmt = balanceAmt;
          context.getOutGoods().getGoodsItem().stream().filter(e -> BigDecimal.ZERO.compareTo(e.getPriceactual()) < 0).forEach(a -> {
               // 比例
               BigDecimal disAmount = a.getPriceactual().divide(context.getOutGoods().getTotpriceactual(), 5, RoundingMode.HALF_UP).multiply(finalBalanceAmt).setScale(0,RoundingMode.DOWN);
               BigDecimal priceActual = a.getPriceactual().subtract(disAmount);
               a.setPriceactual(priceActual);
               a.setDisBalance(disAmount);
          });
          BigDecimal totShareAmount = context.getOutGoods().getGoodsItem().stream().map(CalcPriceContext.OutGoodsItem::getDisBalance)
                  .filter(ObjectUtils::isNotEmpty).reduce(BigDecimal.ZERO,BigDecimal::add);
          // 剩余金额再次分摊
          if(finalBalanceAmt.compareTo(totShareAmount) > 0){
               this.vouShareOver(context,finalBalanceAmt.subtract(totShareAmount));
          }
          int rowNo = context.getOutDiscount().getRows() + 1;
          context.getOutDiscount().setRows(rowNo);
          CalcPriceContext.Discount balanceDiscount = new CalcPriceContext.Discount();
          balanceDiscount.setRowno(rowNo);
          balanceDiscount.setZktype("99");
          balanceDiscount.setZkmoney(finalBalanceAmt);
          balanceDiscount.setZkmemo("储值卡");
          if(CollectionUtils.isEmpty(context.getOutDiscount().getDiscountitem())){
               context.getOutDiscount().setDiscountitem(new ArrayList<>());
          }
          context.getOutDiscount().getDiscountitem().add(balanceDiscount);
          context.getOutGoods().setTotpriceactual(context.getOutGoods().getTotpriceactual().subtract(balanceAmt));
     }



     @Override
     public void posBalanceSave(PosBalanceSaveReqEntity posBalanceSaveReqEntity) {
          try {
               log.info("储值卡使用明细保存到POS+,param={}",JSON.toJSONString(posBalanceSaveReqEntity));
               Response<JicSdkCommonResponse<List<Object>>> result = ijicBalanceHttpApi.posBalanceSave(posBalanceSaveReqEntity).execute();
               if(!result.isSuccessful()) {
                    log.error("储值卡使用明细保存到POS+,param = {},resp={}",JSON.toJSONString(posBalanceSaveReqEntity), JSON.toJSONString(result));
                    throw new RuntimeException("储值卡使用明细保存到POS+失败," + result.errorBody());
               }
          } catch (Exception e) {
               log.error("储值卡使用明细保存到POS+异常,param = {}",JSON.toJSONString(posBalanceSaveReqEntity), e);
               throw new RuntimeException("储值卡使用明细保存到POS+异常" + e.getMessage());
          }
     }


     @Override
     public QueryBalanceFlowRespEntity queryFlow(QueryBalanceFlowReqEntity queryBalanceFlowReqEntity) {
          // 获取签名
          setJICBalanceSign(queryBalanceFlowReqEntity);
          try {
               Response<JicSdkCommonResponse<QueryBalanceFlowRespEntity>> result = ijicBalanceHttpApi.queryFlow(queryBalanceFlowReqEntity).execute();
               if(!result.isSuccessful()) {
                    log.error("查询扣减储值卡流水失败,param={},resp={}",JSON.toJSONString(queryBalanceFlowReqEntity), JSON.toJSONString(result));
                    throw new RuntimeException("查询扣减储值卡流水失败," + result.errorBody());
               }
               JicSdkCommonResponse<QueryBalanceFlowRespEntity> response = result.body();
               if(!response.getSuccess()){
                    log.error("查询扣减储值卡流水失败,param={},resp={}",JSON.toJSONString(queryBalanceFlowReqEntity), JSON.toJSONString(response));
                    throw new RuntimeException("退回储值卡余额失败," + response.getMsg());
               }
               return response.getData();
          } catch (Exception e) {
               log.error("查询扣减储值卡流水失败,param = {}",JSON.toJSONString(queryBalanceFlowReqEntity), e);
               throw new RuntimeException("查询扣减储值卡流水失败" + e.getMessage());
          }
     }

     private void vouShareOver(CalcPriceContext context, BigDecimal remainingAmount){
          // 分摊不均，剩余金额放在金额最高那件商品上
          List<CalcPriceContext.OutGoodsItem> list = context.getOutGoods().getGoodsItem().stream().filter(e -> e.getPriceactual().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
          if(ObjectUtils.isNotEmpty(list)){
               list.sort(Comparator.comparing(CalcPriceContext.OutGoodsItem::getPriceactual).reversed());
               // 剩余金额
               final BigDecimal[] surAmount = {remainingAmount};
               context.getOutGoods().getGoodsItem().forEach(a -> {
                    if(BigDecimal.ZERO.compareTo(surAmount[0]) >= 0){
                         return;
                    }
                    boolean flag = list.stream().anyMatch(e -> e.getRowno().equals(a.getRowno()) && e.getSkuid().equals(a.getSkuid()));
                    if(flag){
                         BigDecimal disAmt = surAmount[0];
                         if(disAmt.compareTo(a.getPriceactual()) > 0){
                              disAmt = a.getPriceactual();
                         }
                         a.setPriceactual(a.getPriceactual().subtract(disAmt));
                         a.setDisBalance(a.getDisBalance().add(disAmt));
                         surAmount[0] = surAmount[0].subtract(disAmt);
                    }
               });
          }
     }
}
