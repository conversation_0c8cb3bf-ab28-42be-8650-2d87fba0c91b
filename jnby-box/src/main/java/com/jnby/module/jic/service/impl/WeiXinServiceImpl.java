package com.jnby.module.jic.service.impl;

import com.alibaba.fastjson.JSON;
import com.jnby.base.context.CardmainInfoQueryContext;
import com.jnby.base.context.weiXinHttpContext.MiniSubscribeContext;
import com.jnby.base.entity.CardmainInfoQueryEntity;
import com.jnby.common.Jic.JicSdkCommonResponse;
import com.jnby.module.jic.service.IWeiXinService;
import com.jnby.module.webapi.IWeiXinHttpApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.Resource;

@Service
@Slf4j
public class WeiXinServiceImpl implements IWeiXinService {

    @Resource
    private IWeiXinHttpApi iWeiXinHttpApi;

    @Override
    public CardmainInfoQueryEntity cardBaseInfo(CardmainInfoQueryContext cartDeleteCartGoodsContext) {

        log.info("微信会员基本信息:{}",cartDeleteCartGoodsContext);
        try {
            Response<JicSdkCommonResponse<CardmainInfoQueryEntity>> result = iWeiXinHttpApi.cardBaseInfo(cartDeleteCartGoodsContext).execute();
            if (!result.isSuccessful()) {
                throw new RuntimeException("微信会员基本信息异常" + result.errorBody());
            }
            return result.body().getData();
        } catch (Exception e) {
            log.error("微信会员基本信息失败接口异常，param={}", JSON.toJSONString(cartDeleteCartGoodsContext), e);
            throw new RuntimeException("微信会员基本信息失败接口异常失败 e:" + e);
        }
    }

    @Override
    public Boolean miniSubscribe(String appid, MiniSubscribeContext miniSubscribeContext) {
        log.info("订阅新增appId:{} miniSubscribeContext:{}", appid, miniSubscribeContext);
        try {
            Response<JicSdkCommonResponse<Boolean>> result = iWeiXinHttpApi.miniSubscribe(appid, miniSubscribeContext).execute();
            if (!result.isSuccessful()) {
                throw new RuntimeException("订阅新增appId异常" + result.errorBody());
            }
            return result.body().getData();
        } catch (Exception e) {
            log.error("订阅新增appId失败接口异常，param={}", JSON.toJSONString(miniSubscribeContext), e);
            throw new RuntimeException("订阅新增appId失败接口异常失败 e:" + e);
        }
    }
}
