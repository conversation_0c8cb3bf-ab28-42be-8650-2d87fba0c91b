package com.jnby.module.jic.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class JICVoucherSendReqEntity {
    @ApiModelProperty("用户唯一标识,unionId")
    @NotBlank(message = "openId 不能为空")
    private String openId;

    @NotBlank(message = "券模板Id 不能为空")
    @ApiModelProperty("券模板Id")
    private List<RulesList> rulesList;

    @ApiModelProperty("是否发送模板消息通知 默认false不发送")
    private Boolean sendMsg;

    @ApiModelProperty(value = "唯一信息 订单唯一信息")
    private String referOrder;

}
