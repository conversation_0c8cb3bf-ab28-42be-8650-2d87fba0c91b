package com.jnby.module.jic.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/9/8 13:42
 */
@Data
public class JICStoreRespEntity {

    @ApiModelProperty("状态码")
    private Integer code;

    @ApiModelProperty("返回消息")
    private String msg;

    @ApiModelProperty("是否成功")
    private boolean success;

    @ApiModelProperty("返回数据")
    private JICStoreData data;

    @Data
    public static class JICStoreData implements Serializable {
        @ApiModelProperty(value = "标识")
        private String globalTicket;

        @ApiModelProperty(value = "库存信息")
        private List<SkuSpuStock> mallStockList;
    }

    @Data
    public static class SkuSpuStock implements Serializable {

        @ApiModelProperty(value = "库存数")
        private Integer qty;

        @ApiModelProperty(value = "skuId")
        private Integer id;
    }

}
