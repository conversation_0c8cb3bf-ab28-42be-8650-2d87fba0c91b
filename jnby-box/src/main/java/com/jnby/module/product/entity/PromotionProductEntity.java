package com.jnby.module.product.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/7/215:59
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PromotionProductEntity implements Serializable {


    private BigDecimal totPrice;

    private BigDecimal totPromotionPrice;

    private List<Product> ProductList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Product implements Serializable{
        // skuId
        private String skuId;

        // 款
        private String spuId;

        // sku
        private String skuCode;

        // 吊牌价
        private BigDecimal price;

        // 促销价
        private BigDecimal promotionPrice;

        // 单品策略id
        private String promoId;

        private String qty = "1";
    }


}
