package com.jnby.module.credit.entity;

import com.jnby.infrastructure.box.model.Box;
import com.jnby.infrastructure.box.model.CustomerDetails;
import com.jnby.infrastructure.box.model.Fashioner;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/4 17:51
 * @description
 */
@Data
public class CreateCreditOrderEntity {

     // 来源类型
     private String sourceType = "BOX";

     // 来源单号
     private String sourceNo;

     // 用户id
     private String userId;

     // 代扣最大金额
     private String orderAmount;

     // 协议号
     private String creditAgreementId;


     private CustomerDetails customerDetails;

     private Fashioner fashioner;

     private Box box;

     private String aliOpenId;

}
