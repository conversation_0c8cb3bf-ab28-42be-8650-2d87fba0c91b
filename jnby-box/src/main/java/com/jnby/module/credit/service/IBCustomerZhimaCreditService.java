package com.jnby.module.credit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jnby.application.minapp.dto.request.CreditSignReq;
import com.jnby.infrastructure.box.model.BCustomerZhimaCredit;
import com.jnby.module.credit.entity.DealCreditEntity;
import com.jnby.module.facade.entity.CreditSignRespEntity;

/**
 * @auther wangchun
 * @create 2024-01-02 18:05:26
 * @describe 服务类
 */
public interface IBCustomerZhimaCreditService extends IService<BCustomerZhimaCredit> {


     // 创建芝麻先享订单
     CreditSignRespEntity createCredit(CreditSignReq creditSignReq);


     // 根据阿里芝麻先享状态变化调整用户协议状态
     void dealCredit(DealCreditEntity dealCredit);

     // 查询阿里芝麻先享状态变化调整用户协议状态
     boolean queryAndDealCredit(String outAgreementNo);

     BCustomerZhimaCredit getByUserId(String userId);


}
