package com.jnby.infrastructure.oa.mapper;

import com.jnby.application.openapi.response.OaUserResp;
import com.jnby.infrastructure.oa.model.HrmResource;

public interface HrmResourceMapper {
    int deleteByPrimaryKey(Long id);

    int insert(HrmResource record);

    int insertSelective(HrmResource record);

    HrmResource selectByPrimaryKey(Long id);

    HrmResource selectByWorkNo(Long workNo);

    OaUserResp selectJobTitleCate(Long id);

    int updateByPrimaryKeySelective(HrmResource record);

    int updateByPrimaryKey(HrmResource record);
}
