package com.jnby.infrastructure.wx.mapper;

import com.jnby.infrastructure.wx.model.JicWxCpEmp;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface JicWxCpEmpMapper {
    int deleteByPrimaryKey(BigDecimal id);

    int insert(JicWxCpEmp record);

    int insertSelective(JicWxCpEmp record);

    JicWxCpEmp selectByPrimaryKey(BigDecimal id);

    int updateByPrimaryKeySelective(JicWxCpEmp record);

    int updateByPrimaryKey(JicWxCpEmp record);

    List<JicWxCpEmp> selectByEmpId(@Param("empId") Integer empId);
}