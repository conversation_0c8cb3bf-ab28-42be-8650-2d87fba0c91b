package com.jnby.infrastructure.wx.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: wangchun
 * @Date: 2022-07-26 16:18:05
 * @Description: 
 */
@TableName("WX_STORE")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="WxStore对象", description="")
public class WxStore implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "租户ID")
    @TableId(value = "RENTID")
    private BigDecimal rentid;
    @ApiModelProperty(value = "WEID")
    @TableField("WEID")
    private BigDecimal weid;
    @ApiModelProperty(value = "品牌")
    @TableField("PP")
    private String pp;
    @ApiModelProperty(value = "店仓编号")
    @TableField("CODE")
    private String code;
    @ApiModelProperty(value = "经营方式")
    @TableField("YT")
    private String yt;
    @ApiModelProperty(value = "店仓名称")
    @TableField("NAME")
    private String name;
    @ApiModelProperty(value = "经纬度")
    @TableField("JWD")
    private String jwd;
    @ApiModelProperty(value = "省份")
    @TableField("PROVINCE")
    private String province;
    @ApiModelProperty(value = "城市")
    @TableField("CITY")
    private String city;
    @ApiModelProperty(value = "区域")
    @TableField("COUNTRY")
    private String country;
    @ApiModelProperty(value = "详细地址")
    @TableField("ADDRESS")
    private String address;
    @ApiModelProperty(value = "电话")
    @TableField("TEL")
    private String tel;
    @ApiModelProperty(value = "纬度")
    @TableField("JD")
    private BigDecimal jd;
    @ApiModelProperty(value = "经度")
    @TableField("WD")
    private BigDecimal wd;
    @ApiModelProperty(value = "城市编号")
    @TableField("CITY_PINYIN")
    private String cityPinyin;
    @ApiModelProperty(value = "店铺编号")
    @TableField("NAME_PINYIN")
    private String namePinyin;
    @ApiModelProperty(value = "ID")
    @TableField("ID")
    private BigDecimal id;
    @ApiModelProperty(value = "状态")
    @TableField("STATUS")
    private String status;
    @TableField("UPDATE_TIME")
    private Date updateTime;


    public BigDecimal getRentid() {
        return rentid;
    }

    public WxStore setRentid(BigDecimal rentid) {
        this.rentid = rentid;
        return this;
    }

    public BigDecimal getWeid() {
        return weid;
    }

    public WxStore setWeid(BigDecimal weid) {
        this.weid = weid;
        return this;
    }

    public String getPp() {
        return pp;
    }

    public WxStore setPp(String pp) {
        this.pp = pp;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WxStore setCode(String code) {
        this.code = code;
        return this;
    }

    public String getYt() {
        return yt;
    }

    public WxStore setYt(String yt) {
        this.yt = yt;
        return this;
    }

    public String getName() {
        return name;
    }

    public WxStore setName(String name) {
        this.name = name;
        return this;
    }

    public String getJwd() {
        return jwd;
    }

    public WxStore setJwd(String jwd) {
        this.jwd = jwd;
        return this;
    }

    public String getProvince() {
        return province;
    }

    public WxStore setProvince(String province) {
        this.province = province;
        return this;
    }

    public String getCity() {
        return city;
    }

    public WxStore setCity(String city) {
        this.city = city;
        return this;
    }

    public String getCountry() {
        return country;
    }

    public WxStore setCountry(String country) {
        this.country = country;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public WxStore setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getTel() {
        return tel;
    }

    public WxStore setTel(String tel) {
        this.tel = tel;
        return this;
    }

    public BigDecimal getJd() {
        return jd;
    }

    public WxStore setJd(BigDecimal jd) {
        this.jd = jd;
        return this;
    }

    public BigDecimal getWd() {
        return wd;
    }

    public WxStore setWd(BigDecimal wd) {
        this.wd = wd;
        return this;
    }

    public String getCityPinyin() {
        return cityPinyin;
    }

    public WxStore setCityPinyin(String cityPinyin) {
        this.cityPinyin = cityPinyin;
        return this;
    }

    public String getNamePinyin() {
        return namePinyin;
    }

    public WxStore setNamePinyin(String namePinyin) {
        this.namePinyin = namePinyin;
        return this;
    }

    public BigDecimal getId() {
        return id;
    }

    public WxStore setId(BigDecimal id) {
        this.id = id;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public WxStore setStatus(String status) {
        this.status = status;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public WxStore setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return "WxStoreModel{" +
            "rentid=" + rentid +
            ", weid=" + weid +
            ", pp=" + pp +
            ", code=" + code +
            ", yt=" + yt +
            ", name=" + name +
            ", jwd=" + jwd +
            ", province=" + province +
            ", city=" + city +
            ", country=" + country +
            ", address=" + address +
            ", tel=" + tel +
            ", jd=" + jd +
            ", wd=" + wd +
            ", cityPinyin=" + cityPinyin +
            ", namePinyin=" + namePinyin +
            ", id=" + id +
            ", status=" + status +
            ", updateTime=" + updateTime +
            "}";
    }
}
