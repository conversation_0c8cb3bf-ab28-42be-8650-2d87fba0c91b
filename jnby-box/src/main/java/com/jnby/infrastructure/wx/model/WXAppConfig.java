package com.jnby.infrastructure.wx.model;

import java.util.Date;

public class WXAppConfig {
    private Long id;

    private Long weid;

    private String hash;

    private String appid;

    private String secret;

    private String orgid;

    private Date inputdate;

    private String paysecret;

    private String apppath;

    private String enterpriseid;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWeid() {
        return weid;
    }

    public void setWeid(Long weid) {
        this.weid = weid;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash == null ? null : hash.trim();
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid == null ? null : appid.trim();
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret == null ? null : secret.trim();
    }

    public String getOrgid() {
        return orgid;
    }

    public void setOrgid(String orgid) {
        this.orgid = orgid == null ? null : orgid.trim();
    }

    public Date getInputdate() {
        return inputdate;
    }

    public void setInputdate(Date inputdate) {
        this.inputdate = inputdate;
    }

    public String getPaysecret() {
        return paysecret;
    }

    public void setPaysecret(String paysecret) {
        this.paysecret = paysecret == null ? null : paysecret.trim();
    }

    public String getApppath() {
        return apppath;
    }

    public void setApppath(String apppath) {
        this.apppath = apppath == null ? null : apppath.trim();
    }

    public String getEnterpriseid() {
        return enterpriseid;
    }

    public void setEnterpriseid(String enterpriseid) {
        this.enterpriseid = enterpriseid == null ? null : enterpriseid.trim();
    }
}