package com.jnby.infrastructure.wx.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * JIC_VOUCHER_SEND_RECORD
 * <AUTHOR>
@Data
public class JicVoucherSendRecord implements Serializable {
    private Long id;

    /**
     * awardid
     */
    private Long awardId;

    /**
     * 券名称
     */
    private String voucherName;

    /**
     * 券号
     */
    private String voucherNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 1-常规；2-营销活动
     */
    private String sourceId;

    /**
     * 由对应source_id取各自source内容
     */
    private String source;

    /**
     * 关联单号
     */
    private String referOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 会员openid
     */
    private String openId;

    /**
     * 券类型。4：外部洗护券
     */
    private String voucherType;

    private static final long serialVersionUID = 1L;
}