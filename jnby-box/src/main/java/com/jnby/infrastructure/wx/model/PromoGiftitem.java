package com.jnby.infrastructure.wx.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: wangchun
 * @Date: 2021-12-16 15:45:20
 * @Description: 
 */
@TableName("PROMO_GIFTITEM")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="PromoGiftitem对象", description="")
public class PromoGiftitem implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private Long id;
    @TableField("CHANNEL")
    private String channel;
    @TableField("M_PROMO_ID")
    private Long mPromoId;
    @TableField("GOODS_ID")
    private Long goodsId;
    @TableField("RELATIONTYPE")
    private String relationtype;
    @TableField("OWNERID")
    private Long ownerid;
    @TableField("MODIFIERID")
    private Long modifierid;
    @TableField("CREATIONDATE")
    private Date creationdate;
    @TableField("MODIFIEDDATE")
    private Date modifieddate;
    @TableField("ISACTIVE")
    private String isactive;
    @TableField("QTY")
    private Long qty;


    public Long getId() {
        return id;
    }

    public PromoGiftitem setId(Long id) {
        this.id = id;
        return this;
    }

    public String getChannel() {
        return channel;
    }

    public PromoGiftitem setChannel(String channel) {
        this.channel = channel;
        return this;
    }

    public Long getmPromoId() {
        return mPromoId;
    }

    public PromoGiftitem setmPromoId(Long mPromoId) {
        this.mPromoId = mPromoId;
        return this;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public PromoGiftitem setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
        return this;
    }

    public String getRelationtype() {
        return relationtype;
    }

    public PromoGiftitem setRelationtype(String relationtype) {
        this.relationtype = relationtype;
        return this;
    }

    public Long getOwnerid() {
        return ownerid;
    }

    public PromoGiftitem setOwnerid(Long ownerid) {
        this.ownerid = ownerid;
        return this;
    }

    public Long getModifierid() {
        return modifierid;
    }

    public PromoGiftitem setModifierid(Long modifierid) {
        this.modifierid = modifierid;
        return this;
    }

    public Date getCreationdate() {
        return creationdate;
    }

    public PromoGiftitem setCreationdate(Date creationdate) {
        this.creationdate = creationdate;
        return this;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public PromoGiftitem setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
        return this;
    }

    public String getIsactive() {
        return isactive;
    }

    public PromoGiftitem setIsactive(String isactive) {
        this.isactive = isactive;
        return this;
    }

    public Long getQty() {
        return qty;
    }

    public PromoGiftitem setQty(Long qty) {
        this.qty = qty;
        return this;
    }

    @Override
    public String toString() {
        return "PromoGiftitemModel{" +
            "id=" + id +
            ", channel=" + channel +
            ", mPromoId=" + mPromoId +
            ", goodsId=" + goodsId +
            ", relationtype=" + relationtype +
            ", ownerid=" + ownerid +
            ", modifierid=" + modifierid +
            ", creationdate=" + creationdate +
            ", modifieddate=" + modifieddate +
            ", isactive=" + isactive +
            ", qty=" + qty +
            "}";
    }
}
