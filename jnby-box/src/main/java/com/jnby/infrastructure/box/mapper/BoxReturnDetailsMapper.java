package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.application.minapp.dto.response.OrderReturnTagResp;
import com.jnby.base.entity.ReturnProductEntity;
import com.jnby.infrastructure.box.model.BoxReturnDetails;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface BoxReturnDetailsMapper extends BaseMapper<BoxReturnDetails> {
    int deleteByPrimaryKey(String id);

    int deleteByReturnId(String returnId);

    int insert(BoxReturnDetails record);

    int insertSelective(BoxReturnDetails record);

    BoxReturnDetails selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(BoxReturnDetails record);

    int updateByPrimaryKeyWithBLOBs(BoxReturnDetails record);

    int updateByPrimaryKey(BoxReturnDetails record);

    List<String> selectReturnTags(@Param("unionId") String unionId,@Param("smallClass") String smallClass);

    List<ReturnProductEntity> selectProductByTag(@Param("unionId") String unionId,@Param("tag") String tag,@Param("smallClass") String smallClass);

    /**
     * 查询评价的商品明细
     * @param boxId
     * @return
     */
    List<ReturnProductEntity> selectReturnProductEvals(@Param("boxId") String boxId);

    List<BoxReturnDetails> selectByReturnId(String returnId);


    int updateByBoxReturnId(BoxReturnDetails record);

    int batchInsertSelective(@Param("list")List<BoxReturnDetails> list);

    /**
     * 通过BOXID查询还货商品信息
     * @param boxId
     * @return
     */
    @Select("select a.id,b.product_name as productName,b.img_url as imgUrl,b.big_class as bigClass,b.small_class as smallClass,a.return_tags as returnTags from box_return_details a " +
            "inner join box_details b on a.box_detail_id = b.id where b.box_id = #{boxId} " +
            "and b.product_code not in ('1LA11911','7I9221692','7K92C0502','7I9221690')")
    List<OrderReturnTagResp.ReturnProduct> selectReturnProductsByBoxId(@Param("boxId") String boxId);
}

