package com.jnby.infrastructure.box.model;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> null
 */
@Data
public class BActivityParticipation implements Serializable {

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "活动id")
    private String activityId;

    @ApiModelProperty(value = "用户唯一标识unionid")
    private String unionid;

    @ApiModelProperty(value = "用户参与活动总次数")
    private Integer rewardNumber;

    @ApiModelProperty(value = "用户当天参与活动次数")
    private Integer everydayRewardNumber;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "状态：0待领取 1领取成功")
    private String status;

    private static final long serialVersionUID = 1L;

}