package com.jnby.infrastructure.box.model;

import java.util.Date;

public class SysConfig {
    private String id;

    private String sysName;

    private Long price;

    private Long coin;

    private String name;

    private String phone;

    private String code;

    private String province;

    private String city;

    private String district;

    private String address;

    private String agreementName;

    private String memberName;

    private String returnName;

    private Long dev;

    private String serviceProfileName;

    private Long openScope;

    private String servicePhone;

    private Date createTime;

    private Date updateTime;

    private Long rebate;

    private String couponId;

    private Long activityState;

    private String customerServiceTelephone;

    private String manBeginTime;

    private String manEndTime;

    private String robatBeginTime;

    private String robatEndTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getSysName() {
        return sysName;
    }

    public void setSysName(String sysName) {
        this.sysName = sysName == null ? null : sysName.trim();
    }

    public Long getPrice() {
        return price;
    }

    public void setPrice(Long price) {
        this.price = price;
    }

    public Long getCoin() {
        return coin;
    }

    public void setCoin(Long coin) {
        this.coin = coin;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getAgreementName() {
        return agreementName;
    }

    public void setAgreementName(String agreementName) {
        this.agreementName = agreementName == null ? null : agreementName.trim();
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName == null ? null : memberName.trim();
    }

    public String getReturnName() {
        return returnName;
    }

    public void setReturnName(String returnName) {
        this.returnName = returnName == null ? null : returnName.trim();
    }

    public Long getDev() {
        return dev;
    }

    public void setDev(Long dev) {
        this.dev = dev;
    }

    public String getServiceProfileName() {
        return serviceProfileName;
    }

    public void setServiceProfileName(String serviceProfileName) {
        this.serviceProfileName = serviceProfileName == null ? null : serviceProfileName.trim();
    }

    public Long getOpenScope() {
        return openScope;
    }

    public void setOpenScope(Long openScope) {
        this.openScope = openScope;
    }

    public String getServicePhone() {
        return servicePhone;
    }

    public void setServicePhone(String servicePhone) {
        this.servicePhone = servicePhone == null ? null : servicePhone.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getRebate() {
        return rebate;
    }

    public void setRebate(Long rebate) {
        this.rebate = rebate;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId == null ? null : couponId.trim();
    }

    public Long getActivityState() {
        return activityState;
    }

    public void setActivityState(Long activityState) {
        this.activityState = activityState;
    }

    public String getCustomerServiceTelephone() {
        return customerServiceTelephone;
    }

    public void setCustomerServiceTelephone(String customerServiceTelephone) {
        this.customerServiceTelephone = customerServiceTelephone == null ? null : customerServiceTelephone.trim();
    }

    public String getManBeginTime() {
        return manBeginTime;
    }

    public void setManBeginTime(String manBeginTime) {
        this.manBeginTime = manBeginTime == null ? null : manBeginTime.trim();
    }

    public String getManEndTime() {
        return manEndTime;
    }

    public void setManEndTime(String manEndTime) {
        this.manEndTime = manEndTime == null ? null : manEndTime.trim();
    }

    public String getRobatBeginTime() {
        return robatBeginTime;
    }

    public void setRobatBeginTime(String robatBeginTime) {
        this.robatBeginTime = robatBeginTime == null ? null : robatBeginTime.trim();
    }

    public String getRobatEndTime() {
        return robatEndTime;
    }

    public void setRobatEndTime(String robatEndTime) {
        this.robatEndTime = robatEndTime == null ? null : robatEndTime.trim();
    }
}