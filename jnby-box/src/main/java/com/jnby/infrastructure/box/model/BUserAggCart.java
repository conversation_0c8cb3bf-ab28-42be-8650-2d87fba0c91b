package com.jnby.infrastructure.box.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class BUserAggCart {

    @ApiModelProperty(value = "unionid")
    private String unionId;

    @ApiModelProperty(value = "产品id  SPUID")
    private String productId;

    @ApiModelProperty(value = "款号")
    private String productCode;

    @ApiModelProperty(value = "来源 两种  两个值BOX和MALL")
    private String source;

    @ApiModelProperty(value = "颜色")
    private String productColor;

    @ApiModelProperty(value = "颜色 no")
    private String colorNo;

    @ApiModelProperty(value = "名称")
    private String productName;

    @ApiModelProperty(value = "价格")
    private String productPrice;

    @ApiModelProperty(value = "尺码")
    private String productSize;

    @ApiModelProperty(value = "图片 地址")
    private String imgUrl;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "数量")
    private String productQuantity = "1";

    @ApiModelProperty(value = "品牌")
    private String productBrand;

    @ApiModelProperty(value = "产品id  SKUID")
    private String outId;

}
