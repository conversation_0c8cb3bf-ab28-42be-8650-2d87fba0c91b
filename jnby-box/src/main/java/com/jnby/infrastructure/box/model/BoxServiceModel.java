package com.jnby.infrastructure.box.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/11/17
 */
@Data
public class BoxServiceModel {
    private String id;

    @ApiModelProperty(value = " box服务单编号")
    private String boxSn;

    private String userId;

    @ApiModelProperty(value = "该用户的unionId")
    private String unionId;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "用户手机号")
    private String phone;

    @ApiModelProperty(value = "服务单的用户物流信息id")
    private String customerLogisticsId;

    private String logisticsId;

    private String askId;

    private Integer isEval;

    private Integer isWarn;

    private String extend;

    @ApiModelProperty(value = "状态(-1:待提交;0:待发货;1:发货中;2:已签收;3:待入库;4:已评价,5.已完成 6:已取消,7:系统作废,8:被合单后;9:待还货;10:还货中)")
    private Integer status;

    @ApiModelProperty(value = " 订单来源;1为搭配师搭盒;2为导购搭盒;3为主动要盒")
    private Integer placleOrder;

    private Integer stockType;

    private String sales;

    @ApiModelProperty(value = " 服务单类型 10:搭配师服务单 20:导购服务单 30:有搭服务单 40:主题盒子服务单")
    private Integer type;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "搭配师")
    private String fashionerName;

    @ApiModelProperty(value = "创建平台 1新平台 0老平台")
    private Integer platForm;
    @ApiModelProperty(value = "是否内淘")
    private Integer isEb;

    @ApiModelProperty(value = "结算业绩指定品牌")
    private String explicitBrand;

    @ApiModelProperty(value = "来源 1主题搭配")
    private String sourceCode;

    @ApiModelProperty(value = "appId")
    private String appId;

    @ApiModelProperty(value = "空无 0免费 1订阅 2单次 ")
    private Long sourceType;

    @ApiModelProperty(value = "信用单id")
    private String creditId;

    private String createFasId;

    @ApiModelProperty(value = "服务单标签")
    private List<String> tagIds;

    @ApiModelProperty(value = "千万仓标记")
    private Boolean isQwc = false;
    @ApiModelProperty(value = "城市仓标记")
    private Boolean isCsc = false;

    @ApiModelProperty(value = "-1 无类型 老数据 0 经销  1 直营")
    private Integer isDirect;
}
