package com.jnby.infrastructure.box.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * SALES_CREATE_BOX_REWARD
 * <AUTHOR>
@Data
public class SalesCreateBoxReward implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * BOXSN
     */
    private String boxSn;

    /**
     * unionid
     */
    private String unionid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 奖励时间
     */
    private Date rewardTime;

    /**
     * 是否删除  0 未删除  1 已删除
     */
    private Integer isDel;

    /**
     * 触发时机    0  盒子快递签收   1  盒子成交
     */
    private Integer sendOpportunity;

    /**
     * 0 默认  1 已触发 （已触发才算发了奖励）
     */
    private Integer status;

    /**
     * 发放金额
     */
    private BigDecimal rewardAmount;

     /**
     * 导购id fashioner表id
     */
    private String salesId;

    /**
     * 店员 or 店长 
     */
    private String identity;

    /**
     * 门店id
     */
    private String cStoreId;

    /**
     * 门店名称
     */
    private String cStoreName;

    /**
     * 导购名称  (店长给店员搭 需要把当前记录记录为店员吗，后面加店长帮搭)
     */
    private String salesName;

    /**
     * 用户手机号
     */
    private String customerPhone;

    @ApiModelProperty(value = "fashioner表中的hrempId")
    private String hrEmpId;

    @ApiModelProperty(value = "发放奖励的当时的盒子排序数 第x个")
    private Integer boxNum;

    @ApiModelProperty(value = "用户类型   0  中小客  1 非中小客")
    private Integer customerType;

    private static final long serialVersionUID = 1L;
}