package com.jnby.infrastructure.box.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName BOX_GOODSREMIND
 */
public class BoxGoodsremind implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 条码ID
     */
    private String codeid;

    /**
     * sku
     */
    private String sku;

    /**
     * 用户unionid 
     */
    private String unionid;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 0 未处理 1 已处理
     */
    private Integer status;

    /**
     * 提醒到货数量
     */
    private Integer qty;

    /**
     * 1 到货提醒
     */
    private Integer type;

    /**
     * 品牌
     */
    private String productBrand;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 颜色
     */
    private String colorName;

    /**
     * 尺码
     */
    private String sizeName;

    /**
     * 有搭明细ID 
     */
    private String themedetailsId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    public String getId() {
        return id;
    }

    /**
     * 主键ID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 条码ID
     */
    public String getCodeid() {
        return codeid;
    }

    /**
     * 条码ID
     */
    public void setCodeid(String codeid) {
        this.codeid = codeid;
    }

    /**
     * sku
     */
    public String getSku() {
        return sku;
    }

    /**
     * sku
     */
    public void setSku(String sku) {
        this.sku = sku;
    }

    /**
     * 用户unionid 
     */
    public String getUnionid() {
        return unionid;
    }

    /**
     * 用户unionid 
     */
    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    /**
     * 用户手机号
     */
    public String getPhone() {
        return phone;
    }

    /**
     * 用户手机号
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * 0 未处理 1 已处理
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 0 未处理 1 已处理
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 提醒到货数量
     */
    public Integer getQty() {
        return qty;
    }

    /**
     * 提醒到货数量
     */
    public void setQty(Integer qty) {
        this.qty = qty;
    }

    /**
     * 1 到货提醒
     */
    public Integer getType() {
        return type;
    }

    /**
     * 1 到货提醒
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 品牌
     */
    public String getProductBrand() {
        return productBrand;
    }

    /**
     * 品牌
     */
    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    /**
     * 商品名称
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 商品名称
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 颜色
     */
    public String getColorName() {
        return colorName;
    }

    /**
     * 颜色
     */
    public void setColorName(String colorName) {
        this.colorName = colorName;
    }

    /**
     * 尺码
     */
    public String getSizeName() {
        return sizeName;
    }

    /**
     * 尺码
     */
    public void setSizeName(String sizeName) {
        this.sizeName = sizeName;
    }

    /**
     * 有搭明细ID 
     */
    public String getThemedetailsId() {
        return themedetailsId;
    }

    /**
     * 有搭明细ID 
     */
    public void setThemedetailsId(String themedetailsId) {
        this.themedetailsId = themedetailsId;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BoxGoodsremind other = (BoxGoodsremind) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCodeid() == null ? other.getCodeid() == null : this.getCodeid().equals(other.getCodeid()))
            && (this.getSku() == null ? other.getSku() == null : this.getSku().equals(other.getSku()))
            && (this.getUnionid() == null ? other.getUnionid() == null : this.getUnionid().equals(other.getUnionid()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getQty() == null ? other.getQty() == null : this.getQty().equals(other.getQty()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getProductBrand() == null ? other.getProductBrand() == null : this.getProductBrand().equals(other.getProductBrand()))
            && (this.getProductName() == null ? other.getProductName() == null : this.getProductName().equals(other.getProductName()))
            && (this.getColorName() == null ? other.getColorName() == null : this.getColorName().equals(other.getColorName()))
            && (this.getSizeName() == null ? other.getSizeName() == null : this.getSizeName().equals(other.getSizeName()))
            && (this.getThemedetailsId() == null ? other.getThemedetailsId() == null : this.getThemedetailsId().equals(other.getThemedetailsId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCodeid() == null) ? 0 : getCodeid().hashCode());
        result = prime * result + ((getSku() == null) ? 0 : getSku().hashCode());
        result = prime * result + ((getUnionid() == null) ? 0 : getUnionid().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getQty() == null) ? 0 : getQty().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getProductBrand() == null) ? 0 : getProductBrand().hashCode());
        result = prime * result + ((getProductName() == null) ? 0 : getProductName().hashCode());
        result = prime * result + ((getColorName() == null) ? 0 : getColorName().hashCode());
        result = prime * result + ((getSizeName() == null) ? 0 : getSizeName().hashCode());
        result = prime * result + ((getThemedetailsId() == null) ? 0 : getThemedetailsId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", codeid=").append(codeid);
        sb.append(", sku=").append(sku);
        sb.append(", unionid=").append(unionid);
        sb.append(", phone=").append(phone);
        sb.append(", status=").append(status);
        sb.append(", qty=").append(qty);
        sb.append(", type=").append(type);
        sb.append(", productBrand=").append(productBrand);
        sb.append(", productName=").append(productName);
        sb.append(", colorName=").append(colorName);
        sb.append(", sizeName=").append(sizeName);
        sb.append(", themedetailsId=").append(themedetailsId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}