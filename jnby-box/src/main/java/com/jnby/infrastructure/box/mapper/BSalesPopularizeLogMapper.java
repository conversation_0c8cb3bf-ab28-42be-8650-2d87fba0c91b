package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.application.admin.dto.response.BSalesStoreUsedResp;
import com.jnby.application.admin.dto.response.GuidePromotionDetailResp;
import com.jnby.application.admin.dto.response.GuidePromotionResp;
import com.jnby.application.corp.dto.response.NewPopularizePlanSuccessCustomersResp;
import com.jnby.infrastructure.box.model.BSalesPopularizeLog;
import com.jnby.infrastructure.box.model.SalesTotalInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * @Author: xiaozhong yuan
 * @Date: 2022-01-11 11:05:11
 * @Description: Mapper
 */
public interface BSalesPopularizeLogMapper extends BaseMapper<BSalesPopularizeLog> {


    /**
     * 查询当前推广列表
     *
     * @param searchFashionerId 搭配师id
     * @param year              年
     * @param month             月
     * @param cStoreId          门店
     * @param isDividend
     * @return 返回
     */
    List<NewPopularizePlanSuccessCustomersResp> newPopularizePlanSuccessCustomers(@Param("fashionerId") String searchFashionerId,
                                                                                  @Param("year") String year,
                                                                                  @Param("month") String month,
                                                                                  @Param("cStoreId") String cStoreId, @Param("isDividend") Integer isDividend);

    /**
     * 统计用户个数和总金额
     *
     * @param searchFashionerId 导购
     * @param year              年
     * @param month             月
     * @param cStoreId          门店
     * @param personType
     * @param isDividend
     * @return 返回
     */
    List<SalesTotalInfo> selectTotalInfo(@Param("fashionerId") String searchFashionerId, @Param("year") String year,
                                         @Param("month") String month, @Param("cStoreId") String cStoreId, @Param("personType") Integer personType,
                                         @Param("isDividend") Integer isDividend);

    /**
     * 批量插入
     * @param saveList 插入数据
     */
    void batchInsert(@Param("list") List<BSalesPopularizeLog> saveList);

    /**
     * 查询当前导购的推广人数和信息
     * @param fashionerIds 搭配师信息
     * @param year 年
     * @param month 月
     * @return 返回
     */
    List<SalesTotalInfo> selectTotalPeople(@Param("list") List<String> fashionerIds, @Param("year") String year, @Param("month") String month);

    /**
     * 查询导购推广奖励列表
     *
     * @param yearMonth  年月
     * @param personType
     * @return 返回
     */
    List<GuidePromotionResp> guidePromotionRewardList(@Param("yearMonth") String yearMonth, @Param("personType") Integer personType);


    /**
     * 统计当前月的人数
     * @param yearMonth 统计当前月份推广人数
     * @return 返回
     */
    List<GuidePromotionResp> peopleCountsList(@Param("yearMonthLists") List<String> yearMonth);

    /**
     * 店长总分红人数
     * @param yearMonth
     * @return
     */
    List<GuidePromotionResp> managerCountsList(@Param("yearMonthLists") List<String> yearMonth);


    List<GuidePromotionDetailResp> guidePromotionRewardDetailList(@Param("saleName") String saleName,
                                                                  @Param("storeName") String storeName,
                                                                  @Param("phone") String phone,
                                                                  @Param("subscribeStartTime") Date subscribeStartTime,
                                                                  @Param("subscribeEndTime") Date subscribeEndTime,
                                                                  @Param("yearMonth") String yearMonth, @Param("personType") Integer personType);
    List<BSalesPopularizeLog> selectBySubInfoIds(@Param("subInfoIds") List<String> subInfoIds);


    Integer selectNumByStoreId(@Param("cStoreId") Long valueOf);

    List<BSalesStoreUsedResp> selectStoreNum();

    List<BSalesPopularizeLog> selectByCustomerIdSubInfoIdAndIsAward(@Param("customerId") String id,
                                                                    @Param("subId") String subId,
                                                                    @Param("isAward") Integer isAward);

    List<BSalesPopularizeLog> selectBySubIdAndReward(@Param("subId") String subId, @Param("rewardType") int isReward, @Param("isFirstSub") int isFirstSub);

    List<BSalesPopularizeLog> selectByCustomerIdAndFashionerId(@Param("customerId") String customerId,
                                                               @Param("fashionerId") String fashionerId,
                                                               @Param("createDate") Date createDate,
                                                                @Param("endDate") Date endDate);

    List<GuidePromotionDetailResp> managerPromotionRewardDetailList(@Param("saleName") String saleName,
                                                                    @Param("storeName") String storeName,
                                                                    @Param("phone") String phone,
                                                                    @Param("subscribeStartTime") Date subscribeStartTime,
                                                                    @Param("subscribeEndTime") Date subscribeEndTime,
                                                                    @Param("yearMonth") String yearMonth, @Param("personType") Integer personType);
}
