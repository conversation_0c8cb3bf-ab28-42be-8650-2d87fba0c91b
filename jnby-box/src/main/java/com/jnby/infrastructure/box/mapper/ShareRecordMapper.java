package com.jnby.infrastructure.box.mapper;

import com.jnby.infrastructure.box.model.ShareRecord;

import java.util.List;

public interface ShareRecordMapper {
    int deleteByPrimaryKey(String id);

    int insert(ShareRecord record);

    int insertSelective(ShareRecord record);

    ShareRecord selectByPrimaryKey(String id);

    List<ShareRecord> selectListBySelective(ShareRecord record);

    int updateByPrimaryKeySelective(ShareRecord record);

    int updateByPrimaryKey(ShareRecord record);
}