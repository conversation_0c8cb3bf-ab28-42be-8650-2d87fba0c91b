package com.jnby.infrastructure.box.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: wangchun
 * @Date: 2023-08-30 11:09:52
 * @Description: 服务单详情货损表
 */
@TableName("BOX_DETAIL_CARGO_DAMAGE")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="BoxDetailCargoDamage对象", description="服务单详情货损表")
public class BoxDetailCargoDamage implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableField("BOX_DETAIL_ID")
    private String boxDetailId;
    @TableField("BOX_ID")
    private String boxId;
    @TableField("BOX_SN")
    private String boxSn;
    @TableField("SKU")
    private String sku;
    @TableField("REMARK")
    private String remark;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @TableField("PRICE")
    private String price;
    @TableField("ORIG_STATUS")
    private BigDecimal origStatus;
    @TableField("DEST_STATUS")
    private BigDecimal destStatus;
    @TableId(value = "ID")
    private String id;


    public String getBoxDetailId() {
        return boxDetailId;
    }

    public BoxDetailCargoDamage setBoxDetailId(String boxDetailId) {
        this.boxDetailId = boxDetailId;
        return this;
    }

    public String getBoxId() {
        return boxId;
    }

    public BoxDetailCargoDamage setBoxId(String boxId) {
        this.boxId = boxId;
        return this;
    }

    public String getBoxSn() {
        return boxSn;
    }

    public BoxDetailCargoDamage setBoxSn(String boxSn) {
        this.boxSn = boxSn;
        return this;
    }

    public String getSku() {
        return sku;
    }

    public BoxDetailCargoDamage setSku(String sku) {
        this.sku = sku;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public BoxDetailCargoDamage setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public BoxDetailCargoDamage setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public BoxDetailCargoDamage setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getPrice() {
        return price;
    }

    public BoxDetailCargoDamage setPrice(String price) {
        this.price = price;
        return this;
    }

    public BigDecimal getOrigStatus() {
        return origStatus;
    }

    public BoxDetailCargoDamage setOrigStatus(BigDecimal origStatus) {
        this.origStatus = origStatus;
        return this;
    }

    public BigDecimal getDestStatus() {
        return destStatus;
    }

    public BoxDetailCargoDamage setDestStatus(BigDecimal destStatus) {
        this.destStatus = destStatus;
        return this;
    }

    public String getId() {
        return id;
    }

    public BoxDetailCargoDamage setId(String id) {
        this.id = id;
        return this;
    }

    @Override
    public String toString() {
        return "BoxDetailCargoDamageModel{" +
            "boxDetailId=" + boxDetailId +
            ", boxId=" + boxId +
            ", boxSn=" + boxSn +
            ", sku=" + sku +
            ", remark=" + remark +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", price=" + price +
            ", origStatus=" + origStatus +
            ", destStatus=" + destStatus +
            ", id=" + id +
            "}";
    }
}
