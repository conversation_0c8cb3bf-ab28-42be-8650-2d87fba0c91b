package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: wangchun
 * @Date: 2023-12-19 13:20:15
 * @Description: 审批中间表
 */
@TableName("B_PROCESS_FLOW")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="BProcessFlow对象", description="审批中间表")
public class BProcessFlow implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "流程编号")
    @TableField("BIZ_ID")
    private String bizId;
    @ApiModelProperty(value = "业务单号")
    @TableField("OUT_NO")
    private String outNo;
    @ApiModelProperty(value = "0已申请,1审批通过,2审批驳回,3审批撤销")
    @TableField("STATUS")
    private Long status;
    @ApiModelProperty(value = "审批数据")
    @TableField("CONTENT_INFO")
    private String contentInfo;
    @ApiModelProperty(value = "备注")
    @TableField("MEMO")
    private String memo;
    @ApiModelProperty(value = "驳回/撤销原因")
    @TableField("RJ_REASON")
    private String rjReason;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "是否删除 1删除 0正常")
    @TableField("IS_DEL")
    private Long isDel;
    @ApiModelProperty(value = "1改价 2大额券")
    @TableField("TYPE")
    private Long type;

    @ApiModelProperty(value = "申请原因")
    @TableField("REASON")
    private String reason;

    @ApiModelProperty(value = "服务单流程序号")
    @TableField("OUT_NO_NUM")
    private Long outNoNum;
    @ApiModelProperty(value = "渠道")
    @TableField("CHANNEL")
    private String channel;


    public Long getOutNoNum() {
        return outNoNum;
    }

    public void setOutNoNum(Long outNoNum) {
        this.outNoNum = outNoNum;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getId() {
        return id;
    }

    public BProcessFlow setId(String id) {
        this.id = id;
        return this;
    }

    public String getBizId() {
        return bizId;
    }

    public BProcessFlow setBizId(String bizId) {
        this.bizId = bizId;
        return this;
    }

    public String getOutNo() {
        return outNo;
    }

    public BProcessFlow setOutNo(String outNo) {
        this.outNo = outNo;
        return this;
    }

    public Long getStatus() {
        return status;
    }

    public BProcessFlow setStatus(Long status) {
        this.status = status;
        return this;
    }

    public String getContentInfo() {
        return contentInfo;
    }

    public BProcessFlow setContentInfo(String contentInfo) {
        this.contentInfo = contentInfo;
        return this;
    }

    public String getMemo() {
        return memo;
    }

    public BProcessFlow setMemo(String memo) {
        this.memo = memo;
        return this;
    }

    public String getRjReason() {
        return rjReason;
    }

    public BProcessFlow setRjReason(String rjReason) {
        this.rjReason = rjReason;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public BProcessFlow setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public BProcessFlow setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public Long getIsDel() {
        return isDel;
    }

    public BProcessFlow setIsDel(Long isDel) {
        this.isDel = isDel;
        return this;
    }

    public Long getType() {
        return type;
    }

    public BProcessFlow setType(Long type) {
        this.type = type;
        return this;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    @Override
    public String toString() {
        return "BProcessFlowModel{" +
            "id=" + id +
            ", bizId=" + bizId +
            ", outNo=" + outNo +
            ", status=" + status +
            ", contentInfo=" + contentInfo +
            ", memo=" + memo +
            ", rjReason=" + rjReason +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDel=" + isDel +
            ", type=" + type +
            "}";
    }
}
