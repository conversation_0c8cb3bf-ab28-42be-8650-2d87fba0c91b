package com.jnby.infrastructure.box.mapper;

import com.jnby.application.admin.dto.request.SalesOrderReq;
import com.jnby.application.admin.dto.response.BoxRefundAndOrderResp;
import com.jnby.infrastructure.box.model.Order;
import com.jnby.infrastructure.box.model.ZhlsOrderSum;
import com.jnby.module.order.context.OrderListContext;
import com.jnby.module.order.entity.OrderEntity;
import com.jnby.module.order.entity.SalesOrderEntity;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface OrderMapper {
    int deleteByPrimaryKey(String id);

    int insert(Order record);

    int insertSelective(Order record);

    Order selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(Order record);

    int updateByPrimaryKey(Order record);

    List<OrderEntity> selectOrderEntity(OrderListContext context);

    List<Order> selectListBySelective(Order order);

    List<Order> getCustomerBoxAcc(String customerId);

    List<String> selectIdListBySelective(Order order);

    List<Order> findByBoxSn(@Param("boxSn") String boxSn);

    /**
     * 获取未支付的订单
     * @param boxSn
     * @return
     */
    List<Order> findUnPayByBoxSn(@Param("boxSn") String boxSn);


    List<Order> findUnPayByUnionId(@Param("unionId") String unionId);


    int getBuyProductNumByBoxSn(String boxSn);

    /**
     * 根据box服务单号获取订单
     */
    List<Order> getOrdersByBoxSn(@Param("boxSn")String boxSn);

    String getEBSN(@Param("serviceSn")String serviceSn);

    List<SalesOrderEntity> selectSalesOrderResp(SalesOrderReq orderReq);


    /**
     * 根据boxSn查询order
     * @param boxSnList
     * @return
     */
    List<Order> getOrdersByBoxSnList(@Param("list")List<String> boxSnList);


    /**
     * 未发货有搭订单
     * @return
     */
    public List<Order> getUnShipList();


    /**
     * 获取换码boxSnList
     * @return
     */
    List<String> getChangeCodeBoxSnList();


    /**
     * 获取未同步完成内淘物流的换码订单
     * @return
     */
    List<Order> getUnSyncEbExpressOrder();

    BigDecimal sumOrderAmount(@Param("customerId") String customerId);

    Integer getBoxBuyCountByUnionid(@Param("unionId") String unionid);

    Long selectSuccessCountNumByFashionerIdAndDate(@org.apache.ibatis.annotations.Param("fashionerId") String fashionerId,
                                                   @org.apache.ibatis.annotations.Param("startDate")Date startDate,
                                                   @org.apache.ibatis.annotations.Param("endDate")Date endDate);

    BigDecimal selectSuccessPayAmountByFashionerIdAndDate(@org.apache.ibatis.annotations.Param("fashionerId") String fashionerId,
                                                          @org.apache.ibatis.annotations.Param("startDate")Date startDate,
                                                          @org.apache.ibatis.annotations.Param("endDate")Date endDate);

    BigDecimal selectSuccessPayAmountAndVouAmountByFashionerIdAndDate(@org.apache.ibatis.annotations.Param("fashionerId") String fashionerId,
                                                                      @org.apache.ibatis.annotations.Param("startDate")Date startDate,
                                                                      @org.apache.ibatis.annotations.Param("endDate")Date endDate);

    BoxRefundAndOrderResp selectOrderByOrderSn(@Param("orderSn") String orderSn);

    BoxRefundAndOrderResp selectOrderByRefundSn(@Param("refundSn")String refundSn);

    List<Order> selectOrderByOrderSns(@Param("list") List<String> list);

    List<Order> selectOrderByBoxSns(@Param("list") List<String> list);

    BigDecimal selectWebPosOrderByTradeNo(@Param("tradeNo") String tradeNo);

    BigDecimal selectSuccessRefundTotAmount(@org.apache.ibatis.annotations.Param("fashionerId") String fashionerId,
                                                                      @org.apache.ibatis.annotations.Param("startDate")Date startDate,
                                                                      @org.apache.ibatis.annotations.Param("endDate")Date endDate);

    List<Order> selectThirthMiniteUnPayOrderList();

    List<Order> selectThreeMiniteUnPayOrderList();

    List<Order> selectUnPayOrderListByBoxSn(@Param("boxSn") String boxSn);

    List<Order> selectUnPayOrderList(@Param("days") Long days);

    List<Order> selectOver15UnPayOrderList(@Param("days") Long days);

    Order selectThreeMiniteUnPayOrder(@Param("orderId") String orderId);

    List<String> getNoSyncOrder();

    List<String> getNoSyncGuideOrder();

    /**
     * 获取前一天订单支付总额
     * @return
     */
    ZhlsOrderSum selectCountOrderBeforeOrderSum();

    /**
     * 获取前一天下单金额之和，商品实际支付金额
     * @return
     */
    ZhlsOrderSum selectCountCreateOrderSum();

    /**
     * 根据类型获取未支付的订单
     * @param customerId
     * @param type
     * @param appId
     * @return
     */
    List<Order> getNoPayOrderByTypeAndCustId(@org.apache.ibatis.annotations.Param("customerId")String customerId, @org.apache.ibatis.annotations.Param("boxSn")String boxSn,@org.apache.ibatis.annotations.Param("type")String type,@org.apache.ibatis.annotations.Param("appId")String appId);

    List<Order> selectByWechatTranstionIds(@Param("list") List<String> tradeNos);
}
