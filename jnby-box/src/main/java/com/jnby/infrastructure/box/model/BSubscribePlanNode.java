package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Author: lwz
 * @Date: 2023-09-08 15:11:57
 * @Description: 
 */
@TableName("B_SUBSCRIBE_PLAN_NODE")
@Data
@Accessors(chain = true)
@ApiModel(value="BSubscribePlanNode对象", description="")
public class BSubscribePlanNode implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "应用id")
    @TableField("APP_ID")
    private String appId;

    @ApiModelProperty(value = "应用名字")
    @TableField("APP_NAME")
    private String appName;

    @ApiModelProperty(value = "月份")
    @TableField("MONTH_NODE")
    private Long monthNode;

    @ApiModelProperty(value = "营销词")
    @TableField("MARKETING_WORD")
    private String marketingWord;

    @TableField("CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("CREATE_BY")
    private String createBy;

    @TableField("UPDATE_BY")
    private String updateBy;

    @TableField("UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "删除（0否 1是）")
    @TableField("DEL_FLAG")
    private Long delFlag;

}
