package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: wangchun
 * @Date: 2024-04-23 10:24:26
 * @Description: 多选一券明细
 */
@TableName("B_CHOOSE_ONE_COUPON")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="BChooseOneCoupon对象", description="多选一券明细")
public class BChooseOneCoupon implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "券组id")
    @TableField("GROUP_ID")
    private String groupId;
    @ApiModelProperty(value = "券号")
    @TableField("VOUCHER_NO")
    private String voucherNo;
    @ApiModelProperty(value = "状态 0,未核销 1正常购买核销 2未购买核销")
    @TableField("STATUS")
    private Long status;
    @ApiModelProperty(value = "是否删除 1删除 0未删除")
    @TableField("IS_DEL")
    private Long isDel;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;


    public String getId() {
        return id;
    }

    public BChooseOneCoupon setId(String id) {
        this.id = id;
        return this;
    }

    public String getGroupId() {
        return groupId;
    }

    public BChooseOneCoupon setGroupId(String groupId) {
        this.groupId = groupId;
        return this;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public BChooseOneCoupon setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
        return this;
    }

    public Long getStatus() {
        return status;
    }

    public BChooseOneCoupon setStatus(Long status) {
        this.status = status;
        return this;
    }

    public Long getIsDel() {
        return isDel;
    }

    public BChooseOneCoupon setIsDel(Long isDel) {
        this.isDel = isDel;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public BChooseOneCoupon setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public BChooseOneCoupon setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return "BChooseOneCouponModel{" +
            "id=" + id +
            ", groupId=" + groupId +
            ", voucherNo=" + voucherNo +
            ", status=" + status +
            ", isDel=" + isDel +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            "}";
    }
}
