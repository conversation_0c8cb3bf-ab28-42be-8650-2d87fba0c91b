package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.box.model.BMemberRecentBrowse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @Author: yui
 * @Date: 2022-09-06 14:55:18
 * @Description: 会员最近浏览Mapper
 */
@Mapper
public interface BMemberRecentBrowseMapper extends BaseMapper<BMemberRecentBrowse> {

    List<BMemberRecentBrowse> selectListByUnionIdAndWeId(@Param("unionId") String unionId, @Param("weId") String weId);
}
