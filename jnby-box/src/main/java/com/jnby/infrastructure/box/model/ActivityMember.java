package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@TableName("B_ACTIVITY_MEMBER")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="ActivityMember对象", description="会员标签")
@Data
public class ActivityMember implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "ID")
    private String id;

    @ApiModelProperty(value = "会员名称")
    @TableId(value = "MEMBER_NAME")
    private String memberName;

    @ApiModelProperty(value = "文件导入成功，返回的key")
    private String key;

    @ApiModelProperty(value = "状态")
    @TableId(value = "STATUS")
    private Integer status;

    @ApiModelProperty(value = "逻辑删除：0未删除，1删除")
    @TableId(value = "IS_DEL")
    private Integer isDel;

    @ApiModelProperty(value = "创建时间")
    @TableId(value = "CREATE_TIME")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableId(value = "UPDATE_TIME")
    private Date updateTime;

    @ApiModelProperty(value = "顾客unionid")
    private String unionId;

}
