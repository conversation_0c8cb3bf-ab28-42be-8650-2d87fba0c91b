package com.jnby.infrastructure.box.model;

import java.util.Date;

public class ChooseActivityNewNotice {
    private String id;

    private String unionid;

    private Short status;

    private String chooseActivityId;

    private Date createTime;

    private Date updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid == null ? null : unionid.trim();
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }

    public String getChooseActivityId() {
        return chooseActivityId;
    }

    public void setChooseActivityId(String chooseActivityId) {
        this.chooseActivityId = chooseActivityId == null ? null : chooseActivityId.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}