package com.jnby.infrastructure.box.model;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> null
 */
@Data
public class BActivityReward implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "奖励类型 1:优惠券 2 实物")
    private Integer rewardType;

    @ApiModelProperty(value = "实物类型：1内部商品 2外部商品")
    private Integer entityType;

    @ApiModelProperty(value = "优惠券ID / 款号")
    private String voucherId;

    @ApiModelProperty(value = "优惠券金额")
    private String amount;

    @ApiModelProperty(value = "优惠券折扣")
    private String vouDis;

    @ApiModelProperty(value = "现金券VOU3 , 折扣券VOU4")
    private String couType;

    @ApiModelProperty(value = "优惠券奖励每份数量/实物奖励每份数量")
    private String voucherNumber;

    @ApiModelProperty(value = "实物名称")
    private String entityName;

    @ApiModelProperty(value = "金额限制（使用门槛）")
    private String useLimit;

    @ApiModelProperty(value = "图片")
    private String image;

    @ApiModelProperty(value = "排序")
    private String seq;

    @ApiModelProperty(value = "奖励总份数")
    private Integer totalNumber;

    @ApiModelProperty(value = "已领取份数")
    private Integer openNumber;

    @ApiModelProperty(value = "奖励方式：1 到账弹窗 2 红包领取 3直接到账")
    private Integer rewardMethod;

    @ApiModelProperty(value = "逻辑删除：0 未删除 1 删除")
    private Integer isDel;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    @ApiModelProperty(value = "失效类型，给的是字母ZD指定时间SY按领取时间顺延")
    private String dateType;

    @ApiModelProperty(value = "生效时间")
    private String startDate;

    @ApiModelProperty(value = "失效时间")
    private String endDate;

    @ApiModelProperty(value = "领取后几天生效")
    private Integer vouUseCycle;

    @ApiModelProperty(value = "礼券周期(天)")
    private Integer vouCycle;

    private static final long serialVersionUID = 1L;

}