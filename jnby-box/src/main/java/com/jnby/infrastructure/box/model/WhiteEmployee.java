package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: lwz
 * @Date: 2022-06-01 16:54:16
 * @Description: 
 */
@TableName("WHITE_EMPLOYEE")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="WhiteEmployee对象", description="")
public class WhiteEmployee implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableField("UNIONID")
    private String unionid;
    @ApiModelProperty(value = "白名单类型0返现1支付分")
    @TableField("TYPE")
    private Integer type;

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "WhiteEmployee{" +
                "unionid='" + unionid + '\'' +
                ", type=" + type +
                '}';
    }
}
