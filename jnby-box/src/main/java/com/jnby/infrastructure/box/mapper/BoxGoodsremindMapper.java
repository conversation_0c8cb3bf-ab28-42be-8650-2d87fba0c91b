package com.jnby.infrastructure.box.mapper;


import com.jnby.infrastructure.box.model.BoxGoodsremind;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @Entity com.jnby.infrastructure.box.model.BoxGoodsremind
*/
public interface BoxGoodsremindMapper {


    List<BoxGoodsremind> selectByUnionid(@Param("unionId") String unionId, @Param("skuId")String skuId);

    void batchInsert(@Param("list") List<BoxGoodsremind> insertDataList);
}
