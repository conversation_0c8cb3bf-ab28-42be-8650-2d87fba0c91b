package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@TableName("COUPON_SETTING")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="CouponSetting对象", description="券")
public class CouponSetting implements Serializable {
    @TableId(value = "ID")
    private String id;

    @ApiModelProperty(value = "券名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty(value = "全类型")
    @TableField("TYPE")
    private Long type;

    @TableField("AMOUNT")
    private Double amount;

    @TableField("DISCOUNT")
    private Double discount;

    @ApiModelProperty(value = "过期类型：指定时间||按照领取时间")
    @TableField("EXPIRE_TYPE")
    private Long expireType;

    @ApiModelProperty(value = "生效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+08")
    @TableField("START_DATE")
    private Date startDate;

    @ApiModelProperty(value = "生效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+08")
    @TableField("END_DATE")
    private Date endDate;

    @TableField("START_DAYS")
    private Long startDays;

    @TableField("END_DAYS")
    private Long endDays;

    @ApiModelProperty(value = "兑换所需金币")
    @TableField("COIN")
    private Long coin;

    @ApiModelProperty(value = "商品兑换数量")
    @TableField("EXCHANGE_QUANTITY")
    private Long exchangeQuantity;

    @ApiModelProperty(value = "券详情")
    @TableField("DETAILS")
    private String details;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+08")
    @TableField("CREATE_TIME")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;

    @ApiModelProperty(value = "内部名称")
    @TableField("INNER_NAME")
    private String innerName;

    @ApiModelProperty(value = "实付最低折扣")
    @TableField("PAY_DISCOUNT")
    private Double payDiscount;

    @ApiModelProperty(value = "使用门槛")
    @TableField("USE_LIMIT")
    private Double useLimit;

    @ApiModelProperty(value = "同类型叠加使用0否1是")
    @TableField("SAME_TYPE_ADD")
    private Long sameTypeAdd;

    @ApiModelProperty(value = "使用场景(多个逗号隔开)1BOX5微商城6线下")
    @TableField("USE_TYPE")
    private String useType;

    @ApiModelProperty(value = "商品过滤类型1可用2不可用")
    @TableField("GOOD_FTILER")
    private Long goodFtiler;

    @ApiModelProperty(value = "品牌")
    @TableField("BRAND")
    private String brand;

    @ApiModelProperty(value = "年份")
    @TableField("YEAR")
    private String year;

    @ApiModelProperty(value = "wx_scrule_id")
    @TableField("BIG_CLASS")
    private String bigClass;

    @ApiModelProperty(value = "小类")
    @TableField("SMALL_CLASS")
    private String smallClass;

    @ApiModelProperty(value = "波段")
    @TableField("BAND")
    private String band;

    @ApiModelProperty(value = "季节")
    @TableField("SEASON")
    private String season;

    @TableField("GOODS")
    private String goods;

    @ApiModelProperty(value = "是否限定商品0不限1限制")
    @TableField("GOODS_FILTER")
    private Long goodsFilter;

    @TableField("WX_SCRULE_ID")
    private Long wxScruleId;

    @TableField("BRAND_NAME")
    private String brandName;

    @TableField("BIGCLASS_NAME")
    private String bigclassName;

    @TableField("SMALLCLASS_NAME")
    private String smallclassName;

    @TableField("SEASON_NAME")
    private String seasonName;

    @TableField("BAND_NAME")
    private String bandName;

    @TableField("SETTING_TYPE")
    private Integer settingType;

    @TableField("OVERLY")
    private Long overly;


    @TableField(exist = false)
    @ApiModelProperty(value = "awardId")
    private String awardId;

    public String getAwardId() {
        return awardId;
    }

    public void setAwardId(String awardId) {
        this.awardId = awardId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    private String description;
    public Long getOverly() {
        return overly;
    }

    public void setOverly(Long overly) {
        this.overly = overly;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Long getExpireType() {
        return expireType;
    }

    public void setExpireType(Long expireType) {
        this.expireType = expireType;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getStartDays() {
        return startDays;
    }

    public void setStartDays(Long startDays) {
        this.startDays = startDays;
    }

    public Long getEndDays() {
        return endDays;
    }

    public void setEndDays(Long endDays) {
        this.endDays = endDays;
    }

    public Long getCoin() {
        return coin;
    }

    public void setCoin(Long coin) {
        this.coin = coin;
    }

    public Long getExchangeQuantity() {
        return exchangeQuantity;
    }

    public void setExchangeQuantity(Long exchangeQuantity) {
        this.exchangeQuantity = exchangeQuantity;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getInnerName() {
        return innerName;
    }

    public void setInnerName(String innerName) {
        this.innerName = innerName;
    }

    public Double getPayDiscount() {
        return payDiscount;
    }

    public void setPayDiscount(Double payDiscount) {
        this.payDiscount = payDiscount;
    }

    public Double getUseLimit() {
        return useLimit;
    }

    public void setUseLimit(Double useLimit) {
        this.useLimit = useLimit;
    }

    public Long getSameTypeAdd() {
        return sameTypeAdd;
    }

    public void setSameTypeAdd(Long sameTypeAdd) {
        this.sameTypeAdd = sameTypeAdd;
    }

    public String getUseType() {
        return useType;
    }

    public void setUseType(String useType) {
        this.useType = useType;
    }

    public Long getGoodFtiler() {
        return goodFtiler;
    }

    public void setGoodFtiler(Long goodFtiler) {
        this.goodFtiler = goodFtiler;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getBigClass() {
        return bigClass;
    }

    public void setBigClass(String bigClass) {
        this.bigClass = bigClass;
    }

    public String getSmallClass() {
        return smallClass;
    }

    public void setSmallClass(String smallClass) {
        this.smallClass = smallClass;
    }

    public String getBand() {
        return band;
    }

    public void setBand(String band) {
        this.band = band;
    }

    public String getSeason() {
        return season;
    }

    public void setSeason(String season) {
        this.season = season;
    }

    public String getGoods() {
        return goods;
    }

    public void setGoods(String goods) {
        this.goods = goods;
    }

    public Long getGoodsFilter() {
        return goodsFilter;
    }

    public void setGoodsFilter(Long goodsFilter) {
        this.goodsFilter = goodsFilter;
    }

    public Long getWxScruleId() {
        return wxScruleId;
    }

    public void setWxScruleId(Long wxScruleId) {
        this.wxScruleId = wxScruleId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getBigclassName() {
        return bigclassName;
    }

    public void setBigclassName(String bigclassName) {
        this.bigclassName = bigclassName;
    }

    public String getSmallclassName() {
        return smallclassName;
    }

    public void setSmallclassName(String smallclassName) {
        this.smallclassName = smallclassName;
    }

    public String getSeasonName() {
        return seasonName;
    }

    public void setSeasonName(String seasonName) {
        this.seasonName = seasonName;
    }

    public String getBandName() {
        return bandName;
    }

    public void setBandName(String bandName) {
        this.bandName = bandName;
    }

    public Integer getSettingType() {
        return settingType;
    }

    public void setSettingType(Integer settingType) {
        this.settingType = settingType;
    }
}
