package com.jnby.infrastructure.box.mapper;

import com.jnby.infrastructure.box.model.SysStore;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SysStoreMapper {
    int deleteByPrimaryKey(String id);

    int insert(SysStore record);

    int insertSelective(SysStore record);

    SysStore selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(SysStore record);

    int updateByPrimaryKey(SysStore record);

    List<SysStore> selectBySelective(SysStore record);

    List<SysStore> selectByCodes(@Param("codes") List<String> codes);

    List<SysStore> findSysStoreByCStoreId(@Param("storeIds") List<Long> cStoreIdList);

    Integer findStoreIdByBoxSn(@Param("boxSn") String boxSn);

    List<SysStore> selectByIds(@Param("ids") List<String> storeIds);

    SysStore selectDistributeInfo(@Param("boxId") String id);

    List<SysStore> selectAll();

    List<SysStore> selectByName(@Param("cStoreName") String cStoreName);

    /**
     * 查询所有门店 同步数据用
     * @return
     */
    List<SysStore> selectAllSysStore();

}
