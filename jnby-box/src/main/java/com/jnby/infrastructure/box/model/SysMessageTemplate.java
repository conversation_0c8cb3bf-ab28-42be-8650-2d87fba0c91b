package com.jnby.infrastructure.box.model;
import com.baomidou.mybatisplus.annotation.TableField;
import org.jeecgframework.poi.excel.annotation.Excel;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 消息模板
 * @Author: jeecg-boot
 * @Date:  2019-04-09
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_sms_template")
public class SysMessageTemplate implements Serializable {
	private String id;

	/**模板CODE*/
	@Excel(name = "模板CODE", width = 15)
	@TableField("template_code")
	private String templateCode;

	/**模板标题*/
	@Excel(name = "模板标题", width = 30)
	@TableField("template_name")
	private String templateName;

	/**模板内容*/
	@Excel(name = "模板内容", width = 50)
	@TableField("template_content")
	private String templateContent;

	/**模板测试json*/
	@Excel(name = "模板测试json", width = 15)
	@TableField("template_test_json")
	private String templateTestJson;

	/**模板类型*/
	@Excel(name = "模板类型", width = 15)
	@TableField("template_type")
	private String templateType;
}
