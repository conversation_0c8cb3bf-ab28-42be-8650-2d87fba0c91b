package com.jnby.infrastructure.box.model;

public class SwitchBrand {
    private String id;

    private String productBrand;

    private String brand;

    private String description;

    private String weid;

    private String shareBrand;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand == null ? null : productBrand.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getWeid() {
        return weid;
    }

    public void setWeid(String weid) {
        this.weid = weid == null ? null : weid.trim();
    }

    public String getShareBrand() {
        return shareBrand;
    }

    public void setShareBrand(String shareBrand) {
        this.shareBrand = shareBrand == null ? null : shareBrand.trim();
    }
}