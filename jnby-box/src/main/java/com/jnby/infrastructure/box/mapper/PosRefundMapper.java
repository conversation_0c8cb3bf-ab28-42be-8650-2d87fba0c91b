package com.jnby.infrastructure.box.mapper;

import com.jnby.infrastructure.box.model.BoxRefund;
import com.jnby.infrastructure.box.model.PosRefund;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
* @Entity com.jnby.infrastructure.box.model.PosRefund
*/
public interface PosRefundMapper {


    List<PosRefund> selectPosRefundByDocNos(@Param("list") List<String> docNos);

    void batchInsert(@Param("list") List<PosRefund> insertList);

    List<PosRefund> selectPosRefundByRefundNoIsNull();

    void batchUpdate(@Param("list") List<PosRefund> refundSuccess);

    List<PosRefund> selectByRefundSn(@Param("refundSn") String refundSn);

    void updateByDocno(PosRefund update);

    List<PosRefund> selectRefundingStatus();
}
