package com.jnby.infrastructure.box.model;

import java.io.Serializable;
import java.util.Date;

public class Logistics implements Serializable {
    private String id;

    private String boxId;

    private String boxReturnId;

    private String refundId;

    private String logSn;

    private String logName;

    private String name;

    private String phone;

    private String code;

    private String province;

    private String city;

    private String district;

    private String address;

    private Long status;

    private Date createTime;

    private Date updateTime;

    private String getDate;

    private String customerLogisticsId;

    private Long type;

    private String empCode;

    private String empPhone;

    private Integer sendType;


    public Integer getSendType() {
        return sendType;
    }

    public void setSendType(Integer sendType) {
        this.sendType = sendType;
    }


    public String getEmpCode() {
        return empCode;
    }

    public void setEmpCode(String empCode) {
        this.empCode = empCode;
    }

    public String getEmpPhone() {
        return empPhone;
    }

    public void setEmpPhone(String empPhone) {
        this.empPhone = empPhone;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getBoxId() {
        return boxId;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId == null ? null : boxId.trim();
    }

    public String getBoxReturnId() {
        return boxReturnId;
    }

    public void setBoxReturnId(String boxReturnId) {
        this.boxReturnId = boxReturnId == null ? null : boxReturnId.trim();
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId == null ? null : refundId.trim();
    }

    public String getLogSn() {
        return logSn;
    }

    public void setLogSn(String logSn) {
        this.logSn = logSn == null ? null : logSn.trim();
    }

    public String getLogName() {
        return logName;
    }

    public void setLogName(String logName) {
        this.logName = logName == null ? null : logName.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getGetDate() {
        return getDate;
    }

    public void setGetDate(String getDate) {
        this.getDate = getDate == null ? null : getDate.trim();
    }

    public String getCustomerLogisticsId() {
        return customerLogisticsId;
    }

    public void setCustomerLogisticsId(String customerLogisticsId) {
        this.customerLogisticsId = customerLogisticsId == null ? null : customerLogisticsId.trim();
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }
}
