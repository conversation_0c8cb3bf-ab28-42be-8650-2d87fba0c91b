package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: yuanxiaozhong
 * @Date: 2022-07-19 17:05:56
 * @Description: 
 */
@TableName("BOX_ORDER_STATE")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="BoxOrderState对象", description="")
public class BoxOrderState implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "订单号")
    @TableField("ORDER_SN")
    private String orderSn;
    @ApiModelProperty(value = "订单处理状态0支付中1已支付")
    @TableField("STATE")
    private Integer state;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UDPATE_TIME")
    private Date udpateTime;


    public String getId() {
        return id;
    }

    public BoxOrderState setId(String id) {
        this.id = id;
        return this;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public BoxOrderState setOrderSn(String orderSn) {
        this.orderSn = orderSn;
        return this;
    }

    public Integer getState() {
        return state;
    }

    public BoxOrderState setState(Integer state) {
        this.state = state;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public BoxOrderState setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUdpateTime() {
        return udpateTime;
    }

    public BoxOrderState setUdpateTime(Date udpateTime) {
        this.udpateTime = udpateTime;
        return this;
    }

    @Override
    public String toString() {
        return "BoxOrderStateModel{" +
            "id=" + id +
            ", orderSn=" + orderSn +
            ", state=" + state +
            ", createTime=" + createTime +
            ", udpateTime=" + udpateTime +
            "}";
    }
}
