package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: lwz
 * @Date: 2024-05-08 15:53:28
 * @Description: 
 */
@TableName("B_CUSTOMER_ZHIMA_CREDIT_LOG")
@Accessors(chain = true)
@ApiModel(value="BCustomerZhimaCreditLog对象", description="")
public class BCustomerZhimaCreditLog implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;

    @TableField("USER_ID")
    private String userId;

    @TableField("ALI_OPEN_ID")
    private String aliOpenId;

    @TableField("CREDIT_ID")
    private String creditId;

    @TableField("FASHIONER_ID")
    private String fashionerId;

    @ApiModelProperty(value = "是否有效（0初始 1授信）")
    @TableField("STATUS")
    private Long status;


    @ApiModelProperty(value = "是否邀请（0否 1是））")
    @TableField("TYPE")
    private Long type;

    @TableField("CREDIT_TIME")
    private Date creditTime;


    @TableField("CREATE_TIME")
    private Date createTime;

    @TableField("UPDATE_TIME")
    private Date updateTime;

    @ApiModelProperty(value = "0有效 1已删除")
    @TableField("IS_DEL")
    private Long isDel;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAliOpenId() {
        return aliOpenId;
    }

    public void setAliOpenId(String aliOpenId) {
        this.aliOpenId = aliOpenId;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getFashionerId() {
        return fashionerId;
    }

    public void setFashionerId(String fashionerId) {
        this.fashionerId = fashionerId;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Date getCreditTime() {
        return creditTime;
    }

    public void setCreditTime(Date creditTime) {
        this.creditTime = creditTime;
    }


    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getIsDel() {
        return isDel;
    }

    public void setIsDel(Long isDel) {
        this.isDel = isDel;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "BCustomerZhimaCreditLog{" +
                "id='" + id + '\'' +
                ", userId='" + userId + '\'' +
                ", aliOpenId='" + aliOpenId + '\'' +
                ", creditId='" + creditId + '\'' +
                ", fashionerId='" + fashionerId + '\'' +
                ", status=" + status +
                ", type=" + type +
                ", creditTime=" + creditTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", isDel=" + isDel +
                '}';
    }
}
