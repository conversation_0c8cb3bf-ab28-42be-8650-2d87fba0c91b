package com.jnby.infrastructure.box.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Auther: lwz
 * @Date: 2021/12/2 16:06
 * @Description: GuideBoxEntity
 * @Version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GuideBoxEntity{

    @ApiModelProperty(value = "boxId")
    private String id;

    @ApiModelProperty(value = "boxSn")
    private String boxSn;

    @ApiModelProperty(value = "导购id")
    private String sales;

    @ApiModelProperty(value = "导购名字")
    private String salesName;

    @ApiModelProperty(value = "搭配师名字  y由谁进行服务的")
    private String fashionerName;

    @ApiModelProperty(value = "搭配师名字  y由谁进行服务的")
    private String fashionerId;

    @ApiModelProperty(value = "用户id")
    private String customerId;

    @ApiModelProperty(value = "用户名字")
    private String customerName;

    @ApiModelProperty(value = "用户头像")
    private String customerHeadUrl;

    @ApiModelProperty(value = "用户手机")
    private String customerPhone;

    @ApiModelProperty(value = "用户unionId")
    private String unionId;

    @ApiModelProperty(value = "服务单状态")
    private String status;

    @ApiModelProperty(value = "是否评价 0-未评价 1-已评价")
    private String isEval;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "取消原因")
    private String cancelReason;

    @ApiModelProperty(value = "创建者id")
    private String createFasId;

    @ApiModelProperty(value = "来源 1主题单配")
    private String sourceCode;

    private String outNo;

    private  String extend;

    @ApiModelProperty(value = "来源类型 0免费盒子 1订阅主动要盒 2单次主动要盒")
    private Long sourceType;

    @ApiModelProperty(value = "使用城市仓的标记，true=使用，false=不使用")
    private Boolean useCsc = false;

    @ApiModelProperty(value = "是否已操作发货")
    private Boolean useSendBox = false;

    @ApiModelProperty(value = "是否开启内淘")
    private Boolean openEbSwitch = false;
}
