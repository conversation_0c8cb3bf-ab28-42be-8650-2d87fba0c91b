package com.jnby.infrastructure.box.mapper;

import com.jnby.application.admin.dto.response.BoxEvaluationResp;
import com.jnby.infrastructure.box.model.BoxEvaluation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @Author: xiaozhong
 * @Date: 2021-11-18 15:39:39
 * @Description: Mapper
 */
public interface BoxEvaluationMapper extends BaseMapper<BoxEvaluation> {

    List<BoxEvaluationResp> getEvaluateList(@Param("fashionerId") String fashionerId,@Param("isShow") Integer isShow);
}
