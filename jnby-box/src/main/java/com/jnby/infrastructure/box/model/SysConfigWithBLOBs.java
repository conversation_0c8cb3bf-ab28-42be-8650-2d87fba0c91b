package com.jnby.infrastructure.box.model;

public class SysConfigWithBLOBs extends SysConfig {
    private String agreementContent;

    private String memberContent;

    private String returnContent;

    private String serviceProfileContent;

    public String getAgreementContent() {
        return agreementContent;
    }

    public void setAgreementContent(String agreementContent) {
        this.agreementContent = agreementContent == null ? null : agreementContent.trim();
    }

    public String getMemberContent() {
        return memberContent;
    }

    public void setMemberContent(String memberContent) {
        this.memberContent = memberContent == null ? null : memberContent.trim();
    }

    public String getReturnContent() {
        return returnContent;
    }

    public void setReturnContent(String returnContent) {
        this.returnContent = returnContent == null ? null : returnContent.trim();
    }

    public String getServiceProfileContent() {
        return serviceProfileContent;
    }

    public void setServiceProfileContent(String serviceProfileContent) {
        this.serviceProfileContent = serviceProfileContent == null ? null : serviceProfileContent.trim();
    }
}