package com.jnby.infrastructure.box.mapper;

import com.jnby.infrastructure.box.model.OthersPayShare;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * @Author: lwz
 * @Date: 2023-03-22 17:46:16
 * @Description: Mapper
 */
public interface OthersPayShareMapper extends BaseMapper<OthersPayShare> {


    /**
     * 根据时间获取未失效的单子
     * @param nowTime
     * @return
     */
    List<OthersPayShare> getOthersPayList(@Param("nowTime")Date nowTime);


    /**
     * 根据失效时间获取代付分享单子
     * @return
     */
    List<OthersPayShare> getExpireTimeOthers();

    /**
     * 更新过期时间
     * @param ids
     * @return
     */
    int updateExpireTimeOthersPays(@Param("ids")List<String> ids);



    /**
     * 更新过期时间
     * @param id
     * @return
     */
    int updateExpireTimeOthersPaysById(@Param("id")String id);


    /**
     * 根据订单id 获取代付信息
     * @param orderId
     * @return
     */
    List<OthersPayShare> getListByOrderId(@Param("orderId")String orderId);

}
