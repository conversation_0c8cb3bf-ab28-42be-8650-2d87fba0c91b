package com.jnby.infrastructure.box.model;

import java.util.Date;

public class Mind {
    private String id;

    private Long num;

    private Long exType;

    private Long exTime;

    private Long inviteRewardType;

    private String inviteReward;

    private Long invitedRewardType;

    private String invitedReward;

    private Long recTime;

    private Long finishBoxNum;

    private Long askExTime;

    private String receivePageImg;

    private String shareImg;

    private Date createTime;

    private Date updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public Long getNum() {
        return num;
    }

    public void setNum(Long num) {
        this.num = num;
    }

    public Long getExType() {
        return exType;
    }

    public void setExType(Long exType) {
        this.exType = exType;
    }

    public Long getExTime() {
        return exTime;
    }

    public void setExTime(Long exTime) {
        this.exTime = exTime;
    }

    public Long getInviteRewardType() {
        return inviteRewardType;
    }

    public void setInviteRewardType(Long inviteRewardType) {
        this.inviteRewardType = inviteRewardType;
    }

    public String getInviteReward() {
        return inviteReward;
    }

    public void setInviteReward(String inviteReward) {
        this.inviteReward = inviteReward == null ? null : inviteReward.trim();
    }

    public Long getInvitedRewardType() {
        return invitedRewardType;
    }

    public void setInvitedRewardType(Long invitedRewardType) {
        this.invitedRewardType = invitedRewardType;
    }

    public String getInvitedReward() {
        return invitedReward;
    }

    public void setInvitedReward(String invitedReward) {
        this.invitedReward = invitedReward == null ? null : invitedReward.trim();
    }

    public Long getRecTime() {
        return recTime;
    }

    public void setRecTime(Long recTime) {
        this.recTime = recTime;
    }

    public Long getFinishBoxNum() {
        return finishBoxNum;
    }

    public void setFinishBoxNum(Long finishBoxNum) {
        this.finishBoxNum = finishBoxNum;
    }

    public Long getAskExTime() {
        return askExTime;
    }

    public void setAskExTime(Long askExTime) {
        this.askExTime = askExTime;
    }

    public String getReceivePageImg() {
        return receivePageImg;
    }

    public void setReceivePageImg(String receivePageImg) {
        this.receivePageImg = receivePageImg == null ? null : receivePageImg.trim();
    }

    public String getShareImg() {
        return shareImg;
    }

    public void setShareImg(String shareImg) {
        this.shareImg = shareImg == null ? null : shareImg.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}