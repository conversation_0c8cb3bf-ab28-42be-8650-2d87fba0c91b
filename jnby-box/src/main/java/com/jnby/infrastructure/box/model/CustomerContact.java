package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName CUSTOMER_CONTACT
 */

@TableName("CUSTOMER_CONTACT")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="CustomerContact对象", description="")
public class CustomerContact implements Serializable {
    /**
     * 
     */
    @TableId(value = "ID")
    private String id;

    /**
     * 顾客ID
     */
    @TableField(value = "CUSTOMER_ID")
    private String customerId;

    /**
     * 订阅ID
     */
    @TableField(value = "SUBSCRIBE_ID")
    private String subscribeId;

    /**
     * 手机号
     */
    @TableField(value = "PHONE")
    private String phone;

    /**
     * 微信号
     */
    @TableField(value = "WECHAT_NUMBER")
    private String wechatNumber;

    /**
     * 联系方式
     */
    @TableField(value = "CONTACT")
    private String contact;

    /**
     * 联系时间
     */
    @TableField(value = "CONTACTTIME")
    private String contacttime;

    /**
     * 已联系次数
     */
    @TableField(value = "CONTACTNUM")
    private Long contactnum;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 状态 0 :第一次订阅  1 续订
     */
    @TableField(value = "STATUS")
    private Long status;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 是否完成  0 未完成   1 已完成
     */
    @TableField(value = "IS_OK")
    private Long isOk;

    /**
     *   搭配师 0   客服 1 
     */
    @TableField(value = "ROLE")
    private Long role;

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public String getId() {
        return id;
    }

    /**
     * 
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 顾客ID
     */
    public String getCustomerId() {
        return customerId;
    }

    /**
     * 顾客ID
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * 订阅ID
     */
    public String getSubscribeId() {
        return subscribeId;
    }

    /**
     * 订阅ID
     */
    public void setSubscribeId(String subscribeId) {
        this.subscribeId = subscribeId;
    }

    /**
     * 手机号
     */
    public String getPhone() {
        return phone;
    }

    /**
     * 手机号
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * 微信号
     */
    public String getWechatNumber() {
        return wechatNumber;
    }

    /**
     * 微信号
     */
    public void setWechatNumber(String wechatNumber) {
        this.wechatNumber = wechatNumber;
    }

    /**
     * 联系方式
     */
    public String getContact() {
        return contact;
    }

    /**
     * 联系方式
     */
    public void setContact(String contact) {
        this.contact = contact;
    }

    /**
     * 联系时间
     */
    public String getContacttime() {
        return contacttime;
    }

    /**
     * 联系时间
     */
    public void setContacttime(String contacttime) {
        this.contacttime = contacttime;
    }

    /**
     * 已联系次数
     */
    public Long getContactnum() {
        return contactnum;
    }

    /**
     * 已联系次数
     */
    public void setContactnum(Long contactnum) {
        this.contactnum = contactnum;
    }

    /**
     * 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 状态 0 :第一次订阅  1 续订
     */
    public Long getStatus() {
        return status;
    }

    /**
     * 状态 0 :第一次订阅  1 续订
     */
    public void setStatus(Long status) {
        this.status = status;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 是否完成  0 未完成   1 已完成
     */
    public Long getIsOk() {
        return isOk;
    }

    /**
     * 是否完成  0 未完成   1 已完成
     */
    public void setIsOk(Long isOk) {
        this.isOk = isOk;
    }

    /**
     *   搭配师 0   客服 1 
     */
    public Long getRole() {
        return role;
    }

    /**
     *   搭配师 0   客服 1 
     */
    public void setRole(Long role) {
        this.role = role;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CustomerContact other = (CustomerContact) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCustomerId() == null ? other.getCustomerId() == null : this.getCustomerId().equals(other.getCustomerId()))
            && (this.getSubscribeId() == null ? other.getSubscribeId() == null : this.getSubscribeId().equals(other.getSubscribeId()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getWechatNumber() == null ? other.getWechatNumber() == null : this.getWechatNumber().equals(other.getWechatNumber()))
            && (this.getContact() == null ? other.getContact() == null : this.getContact().equals(other.getContact()))
            && (this.getContacttime() == null ? other.getContacttime() == null : this.getContacttime().equals(other.getContacttime()))
            && (this.getContactnum() == null ? other.getContactnum() == null : this.getContactnum().equals(other.getContactnum()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getIsOk() == null ? other.getIsOk() == null : this.getIsOk().equals(other.getIsOk()))
            && (this.getRole() == null ? other.getRole() == null : this.getRole().equals(other.getRole()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCustomerId() == null) ? 0 : getCustomerId().hashCode());
        result = prime * result + ((getSubscribeId() == null) ? 0 : getSubscribeId().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getWechatNumber() == null) ? 0 : getWechatNumber().hashCode());
        result = prime * result + ((getContact() == null) ? 0 : getContact().hashCode());
        result = prime * result + ((getContacttime() == null) ? 0 : getContacttime().hashCode());
        result = prime * result + ((getContactnum() == null) ? 0 : getContactnum().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsOk() == null) ? 0 : getIsOk().hashCode());
        result = prime * result + ((getRole() == null) ? 0 : getRole().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", customerId=").append(customerId);
        sb.append(", subscribeId=").append(subscribeId);
        sb.append(", phone=").append(phone);
        sb.append(", wechatNumber=").append(wechatNumber);
        sb.append(", contact=").append(contact);
        sb.append(", contacttime=").append(contacttime);
        sb.append(", contactnum=").append(contactnum);
        sb.append(", remark=").append(remark);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isOk=").append(isOk);
        sb.append(", role=").append(role);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}