package com.jnby.infrastructure.box.mapper;

import com.jnby.base.entity.CustomerAttrEntity;
import com.jnby.infrastructure.box.model.CustomerAnswer;
import com.jnby.infrastructure.box.model.StylingBoxCart;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerAnswerMapper {
    int deleteByPrimaryKey(String id);

    int insert(CustomerAnswer record);

    void batchInsert(@Param("list") List<CustomerAnswer> list);

    int insertSelective(CustomerAnswer record);

    CustomerAnswer selectByPrimaryKey(String id);

    List<CustomerAnswer> selectListBySelective(CustomerAnswer record);

    int updateByPrimaryKeySelective(CustomerAnswer record);


    int updateByPrimaryKeyWithBLOBs(CustomerAnswer record);

    int updateByPrimaryKey(CustomerAnswer record);

    List<CustomerAttrEntity> selectCustomerAttr(String unionId);

    //查询用户对于某个问卷下的某个问题的回答
    List<CustomerAnswer> getAnswer(@Param("unionid") String unionid, @Param("questionId") String questionId);

    List<CustomerAnswer> getAnswers(@Param("unionid") String unionid, @Param("ids") List<String> questionIds);

    List<CustomerAnswer> getAnswerForUpdate(@Param("unionid") String unionid, @Param("questionId") String questionId);

    //根据问卷问题id和用户unionId将新版填写标志改为已填写1
    int updateAnswerFlagForWrite(@Param("id") String id);

    int getAnswerForCount(@Param("unionid") String unionid, @Param("questionId") String questionId);

    List<CustomerAnswer> getAnswerForAward(@Param("unionid") String unionid, @Param("questionId") String questionId);

    List<CustomerAnswer> getAnswerListForAward(@Param("unionid") String unionid, @Param("list") List<String> scheduleIds);

    int getAnswerListForCount(@Param("unionid") String unionid, @Param("list") List<String> scheduleIds);


    List<CustomerAnswer> getAnswerForAwardByQuestionIds(@Param("unionid") String unionid, @Param("list") List<String> ids);

    //批量查询用户的非空值答案
    int getAnswerGoodCount(@Param("unionid") String unionid, @Param("list") List<String> ids);

    List<CustomerAnswer> getAwardGoodCount(@Param("surveyId") String surveyId,@Param("pageId") String pageId,@Param("unionid") String unionid);


}
