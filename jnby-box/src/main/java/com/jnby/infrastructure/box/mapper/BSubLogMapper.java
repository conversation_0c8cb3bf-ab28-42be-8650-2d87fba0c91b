package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.box.model.BSubLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @Author: lwz
 * @Date: 2024-07-25 15:20:15
 * @Description: Mapper
 */
public interface BSubLogMapper extends BaseMapper<BSubLog> {

    /**
     * 根据subId查询list
     * @param subId
     * @return
     */
    List<BSubLog> subLogListBySubId(@Param("subId") String subId);
}
