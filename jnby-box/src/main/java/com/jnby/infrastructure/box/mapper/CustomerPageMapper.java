package com.jnby.infrastructure.box.mapper;

import com.jnby.infrastructure.box.model.CustomerPage;
import com.jnby.infrastructure.box.model.CustomerQuestion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerPageMapper {
    int deleteByPrimaryKey(String id);

    int insert(CustomerPage record);

    int insertSelective(CustomerPage record);

    CustomerPage selectByPrimaryKey(String id);

    List<CustomerPage> selectListBySelective(CustomerPage record);

    int updateByPrimaryKeySelective(CustomerPage record);

    int updateByPrimaryKey(CustomerPage record);

    List<CustomerPage> selectListBySelectiveForSecond(CustomerPage page);

    List<CustomerPage> selectCountForQuestion(@Param("surveyId")String surveyId,@Param("list")List<Integer> styleFlags);

    //查询当前问卷id下的页面类型为我的偏好的包含有包含有喜爱的搭配风格有 (多选)或者若您不喜欢上述风格我的偏好的页面id
    List<String> queryMyLoveForMyDream(@Param("surveyId")String surveyId);


}
