package com.jnby.infrastructure.box.model;

import java.util.Date;

public class ChooseDetails {
    private String id;

    private String chooseThemeId;

    private Long productNo;

    private String productId;

    private String productName;

    private String productSize;

    private String productPrice;

    private String productFavorablePrice;

    private String vipPrice;

    private String productQuantity;

    private String productColorNo;

    private String productColor;

    private String sku;

    private String productCode;

    private String outId;

    private String productBrand;

    private String bigSeason;

    private String year;

    private String topic;

    private String gbcode;

    private String bigclass;

    private Date createTime;

    private Date updateTime;

    private String smallclass;

    private String imgUrl;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getChooseThemeId() {
        return chooseThemeId;
    }

    public void setChooseThemeId(String chooseThemeId) {
        this.chooseThemeId = chooseThemeId == null ? null : chooseThemeId.trim();
    }

    public Long getProductNo() {
        return productNo;
    }

    public void setProductNo(Long productNo) {
        this.productNo = productNo;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId == null ? null : productId.trim();
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getProductSize() {
        return productSize;
    }

    public void setProductSize(String productSize) {
        this.productSize = productSize == null ? null : productSize.trim();
    }

    public String getProductPrice() {
        return productPrice;
    }

    public void setProductPrice(String productPrice) {
        this.productPrice = productPrice == null ? null : productPrice.trim();
    }

    public String getProductFavorablePrice() {
        return productFavorablePrice;
    }

    public void setProductFavorablePrice(String productFavorablePrice) {
        this.productFavorablePrice = productFavorablePrice == null ? null : productFavorablePrice.trim();
    }

    public String getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(String vipPrice) {
        this.vipPrice = vipPrice == null ? null : vipPrice.trim();
    }

    public String getProductQuantity() {
        return productQuantity;
    }

    public void setProductQuantity(String productQuantity) {
        this.productQuantity = productQuantity == null ? null : productQuantity.trim();
    }

    public String getProductColorNo() {
        return productColorNo;
    }

    public void setProductColorNo(String productColorNo) {
        this.productColorNo = productColorNo == null ? null : productColorNo.trim();
    }

    public String getProductColor() {
        return productColor;
    }

    public void setProductColor(String productColor) {
        this.productColor = productColor == null ? null : productColor.trim();
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode == null ? null : productCode.trim();
    }

    public String getOutId() {
        return outId;
    }

    public void setOutId(String outId) {
        this.outId = outId == null ? null : outId.trim();
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand == null ? null : productBrand.trim();
    }

    public String getBigSeason() {
        return bigSeason;
    }

    public void setBigSeason(String bigSeason) {
        this.bigSeason = bigSeason == null ? null : bigSeason.trim();
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year == null ? null : year.trim();
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic == null ? null : topic.trim();
    }

    public String getGbcode() {
        return gbcode;
    }

    public void setGbcode(String gbcode) {
        this.gbcode = gbcode == null ? null : gbcode.trim();
    }

    public String getBigclass() {
        return bigclass;
    }

    public void setBigclass(String bigclass) {
        this.bigclass = bigclass == null ? null : bigclass.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getSmallclass() {
        return smallclass;
    }

    public void setSmallclass(String smallclass) {
        this.smallclass = smallclass == null ? null : smallclass.trim();
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl == null ? null : imgUrl.trim();
    }
}