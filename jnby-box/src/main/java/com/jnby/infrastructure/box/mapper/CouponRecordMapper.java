package com.jnby.infrastructure.box.mapper;

import com.jnby.infrastructure.box.model.CouponRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CouponRecordMapper {
    int deleteByPrimaryKey(String id);

    int insert(CouponRecord record);

    int insertSelective(CouponRecord record);

    CouponRecord selectByPrimaryKey(String id);

    List<CouponRecord> selectListBySelective(CouponRecord record);

    int updateByPrimaryKeySelective(CouponRecord record);

    int updateByPrimaryKey(CouponRecord record);


    /**
     * 根据红包活动ids-查询红包活动
     * @param sourceId
     * @param unionIds
     * @return
     */
    List<String> selectUnionIdsByActivityIdAndUnionIds(@Param("sourceid") String sourceid, @Param("unionIds") List<String> unionIds);



    List<String> selectUnionIdsByActivityIdAndCustIds(@Param("sourceid") String sourceid, @Param("custIds") List<String> custIds);
}