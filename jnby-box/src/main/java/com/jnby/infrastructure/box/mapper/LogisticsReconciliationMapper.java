package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.box.model.LogisticsReconciliation;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;


/**
 * @Author: yui
 * @Date: 2022-09-20 17:19:36
 * @Description: 经销物流对账Mapper
 */
@Repository
public interface LogisticsReconciliationMapper extends BaseMapper<LogisticsReconciliation>{

    //批量更新运费和状态
    void updateFreight(@Param("list") List<LogisticsReconciliation> rets);

    List<LogisticsReconciliation> selectByInSettled(@Param("submitPerson")String submitPerson);

    List<LogisticsReconciliation>  selectByParams(@Param("cStoreId") Long cStoreId,
                                                  @Param("start") Date startTime, @Param("end") Date endTime,
                                                  @Param("status") Integer status, @Param("submitStatus") Integer submitStatus,
                                                  @Param("wayBillNo") String wayBill, @Param("orderNo") String orderNo);


    void updateDistribute(@Param("list") List<LogisticsReconciliation> updateLists);

    LogisticsReconciliation selectByTraceNo(@Param("logSn") String logSn);

    //批量更新提交状态
    void updateSubmitInfo(@Param("list") List<LogisticsReconciliation> rets);

    void updateSubmitInfoByAll(@Param("userRealName") String userRealName, @Param("submitDate") Date submitDate);
}
