package com.jnby.infrastructure.bojun.mapper;

import com.jnby.infrastructure.bojun.model.FaVipintegralFtp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @Entity com.jnby.infrastructure.bojun.model.FaVipintegralFtp
*/
public interface FaVipintegralFtpMapper  {

    public List<FaVipintegralFtp> selectByDocNo(@Param("docNo") String docNo);

    long getTotalIntegralByDocNos(@Param("docNos") List<String> docNos);

    List<FaVipintegralFtp> selectByDocNoForDeduction(@Param("docNo") String docno);
}
