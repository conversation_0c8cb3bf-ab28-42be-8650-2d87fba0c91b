package com.jnby.infrastructure.bojun.mapper;

import com.jnby.infrastructure.bojun.model.TOmsOnlineOrder;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

public interface TOmsOnlineOrderMapper {
    int deleteByPrimaryKey(Short id);

    int insert(TOmsOnlineOrder record);

    int insertSelective(TOmsOnlineOrder record);

    TOmsOnlineOrder selectByPrimaryKey(Short id);

    List<TOmsOnlineOrder> selectListBySelective(TOmsOnlineOrder record);

    int updateByPrimaryKeySelective(TOmsOnlineOrder record);

    int updateByPrimaryKey(TOmsOnlineOrder record);

    int updateStatusBySourceCode(TOmsOnlineOrder record);


    List<TOmsOnlineOrder> getOrder(@Param("sourcecode")  String sourcecode);

    List<TOmsOnlineOrder> getOrderList(@Param("list")  List<String> list);


    TOmsOnlineOrder getOrderById(@Param("id")  Long id);
}
