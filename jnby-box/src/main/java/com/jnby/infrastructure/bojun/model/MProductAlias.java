package com.jnby.infrastructure.bojun.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: wangchun
 * @Date: 2022-04-19 15:38:22
 * @Description: 
 */
@TableName("M_PRODUCT_ALIAS")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="MProductAlias对象", description="")
public class MProductAlias implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private Long id;
    @TableField("AD_CLIENT_ID")
    private Long adClientId;
    @TableField("AD_ORG_ID")
    private Long adOrgId;
    @TableField("NO")
    private String no;
    @TableField("DESCRIPTION")
    private String description;
    @TableField("M_PRODUCT_ID")
    private Long mProductId;
    @TableField("M_ATTRIBUTESETINSTANCE_ID")
    private Long mAttributesetinstanceId;
    @TableField("INTSCODE")
    private String intscode;
    @TableField("FORCODE")
    private String forcode;
    @TableField("COMMENTS")
    private String comments;
    @TableField("OWNERID")
    private Long ownerid;
    @TableField("MODIFIERID")
    private Long modifierid;
    @TableField("CREATIONDATE")
    private Date creationdate;
    @TableField("MODIFIEDDATE")
    private Date modifieddate;
    @TableField("ISACTIVE")
    private String isactive;
    @TableField("LASTCODE")
    private Long lastcode;
    @TableField("BIND_PROALIAS01")
    private String bindProalias01;
    @TableField("BIND_PROALIAS02")
    private String bindProalias02;
    @TableField("M_RKSJ")
    private Integer mRksj;
    @TableField("IS_CANCEL")
    private String isCancel;
    @TableField("M_DIM20_ID")
    private Long mDim20Id;
    @TableField("FIRSTDATE")
    private Integer firstdate;
    @TableField("IS_WINDOWSTYLE")
    private String isWindowstyle;
    @TableField("IS_TOC17")
    private String isToc17;
    @TableField("IS_TOWMS")
    private String isTowms;
    @TableField("PREDISTIME")
    private Integer predistime;
    @TableField("IS_TORFID")
    private String isTorfid;
    @TableField("PRODUCT_NO")
    private String productNo;
    @TableField("FACTORYID")
    private Long factoryid;
    @TableField("GOODSNO")
    private String goodsno;
    @TableField("IS_TOJD")
    private String isTojd;
    @TableField("IS_JJ")
    private String isJj;


    public Long getId() {
        return id;
    }

    public MProductAlias setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getAdClientId() {
        return adClientId;
    }

    public MProductAlias setAdClientId(Long adClientId) {
        this.adClientId = adClientId;
        return this;
    }

    public Long getAdOrgId() {
        return adOrgId;
    }

    public MProductAlias setAdOrgId(Long adOrgId) {
        this.adOrgId = adOrgId;
        return this;
    }

    public String getNo() {
        return no;
    }

    public MProductAlias setNo(String no) {
        this.no = no;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public MProductAlias setDescription(String description) {
        this.description = description;
        return this;
    }

    public Long getmProductId() {
        return mProductId;
    }

    public MProductAlias setmProductId(Long mProductId) {
        this.mProductId = mProductId;
        return this;
    }

    public Long getmAttributesetinstanceId() {
        return mAttributesetinstanceId;
    }

    public MProductAlias setmAttributesetinstanceId(Long mAttributesetinstanceId) {
        this.mAttributesetinstanceId = mAttributesetinstanceId;
        return this;
    }

    public String getIntscode() {
        return intscode;
    }

    public MProductAlias setIntscode(String intscode) {
        this.intscode = intscode;
        return this;
    }

    public String getForcode() {
        return forcode;
    }

    public MProductAlias setForcode(String forcode) {
        this.forcode = forcode;
        return this;
    }

    public String getComments() {
        return comments;
    }

    public MProductAlias setComments(String comments) {
        this.comments = comments;
        return this;
    }

    public Long getOwnerid() {
        return ownerid;
    }

    public MProductAlias setOwnerid(Long ownerid) {
        this.ownerid = ownerid;
        return this;
    }

    public Long getModifierid() {
        return modifierid;
    }

    public MProductAlias setModifierid(Long modifierid) {
        this.modifierid = modifierid;
        return this;
    }

    public Date getCreationdate() {
        return creationdate;
    }

    public MProductAlias setCreationdate(Date creationdate) {
        this.creationdate = creationdate;
        return this;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public MProductAlias setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
        return this;
    }

    public String getIsactive() {
        return isactive;
    }

    public MProductAlias setIsactive(String isactive) {
        this.isactive = isactive;
        return this;
    }

    public Long getLastcode() {
        return lastcode;
    }

    public MProductAlias setLastcode(Long lastcode) {
        this.lastcode = lastcode;
        return this;
    }

    public String getBindProalias01() {
        return bindProalias01;
    }

    public MProductAlias setBindProalias01(String bindProalias01) {
        this.bindProalias01 = bindProalias01;
        return this;
    }

    public String getBindProalias02() {
        return bindProalias02;
    }

    public MProductAlias setBindProalias02(String bindProalias02) {
        this.bindProalias02 = bindProalias02;
        return this;
    }

    public Integer getmRksj() {
        return mRksj;
    }

    public MProductAlias setmRksj(Integer mRksj) {
        this.mRksj = mRksj;
        return this;
    }

    public String getIsCancel() {
        return isCancel;
    }

    public MProductAlias setIsCancel(String isCancel) {
        this.isCancel = isCancel;
        return this;
    }

    public Long getmDim20Id() {
        return mDim20Id;
    }

    public MProductAlias setmDim20Id(Long mDim20Id) {
        this.mDim20Id = mDim20Id;
        return this;
    }

    public Integer getFirstdate() {
        return firstdate;
    }

    public MProductAlias setFirstdate(Integer firstdate) {
        this.firstdate = firstdate;
        return this;
    }

    public String getIsWindowstyle() {
        return isWindowstyle;
    }

    public MProductAlias setIsWindowstyle(String isWindowstyle) {
        this.isWindowstyle = isWindowstyle;
        return this;
    }

    public String getIsToc17() {
        return isToc17;
    }

    public MProductAlias setIsToc17(String isToc17) {
        this.isToc17 = isToc17;
        return this;
    }

    public String getIsTowms() {
        return isTowms;
    }

    public MProductAlias setIsTowms(String isTowms) {
        this.isTowms = isTowms;
        return this;
    }

    public Integer getPredistime() {
        return predistime;
    }

    public MProductAlias setPredistime(Integer predistime) {
        this.predistime = predistime;
        return this;
    }

    public String getIsTorfid() {
        return isTorfid;
    }

    public MProductAlias setIsTorfid(String isTorfid) {
        this.isTorfid = isTorfid;
        return this;
    }

    public String getProductNo() {
        return productNo;
    }

    public MProductAlias setProductNo(String productNo) {
        this.productNo = productNo;
        return this;
    }

    public Long getFactoryid() {
        return factoryid;
    }

    public MProductAlias setFactoryid(Long factoryid) {
        this.factoryid = factoryid;
        return this;
    }

    public String getGoodsno() {
        return goodsno;
    }

    public MProductAlias setGoodsno(String goodsno) {
        this.goodsno = goodsno;
        return this;
    }

    public String getIsTojd() {
        return isTojd;
    }

    public MProductAlias setIsTojd(String isTojd) {
        this.isTojd = isTojd;
        return this;
    }

    public String getIsJj() {
        return isJj;
    }

    public MProductAlias setIsJj(String isJj) {
        this.isJj = isJj;
        return this;
    }

    @Override
    public String toString() {
        return "MProductAliasModel{" +
            "id=" + id +
            ", adClientId=" + adClientId +
            ", adOrgId=" + adOrgId +
            ", no=" + no +
            ", description=" + description +
            ", mProductId=" + mProductId +
            ", mAttributesetinstanceId=" + mAttributesetinstanceId +
            ", intscode=" + intscode +
            ", forcode=" + forcode +
            ", comments=" + comments +
            ", ownerid=" + ownerid +
            ", modifierid=" + modifierid +
            ", creationdate=" + creationdate +
            ", modifieddate=" + modifieddate +
            ", isactive=" + isactive +
            ", lastcode=" + lastcode +
            ", bindProalias01=" + bindProalias01 +
            ", bindProalias02=" + bindProalias02 +
            ", mRksj=" + mRksj +
            ", isCancel=" + isCancel +
            ", mDim20Id=" + mDim20Id +
            ", firstdate=" + firstdate +
            ", isWindowstyle=" + isWindowstyle +
            ", isToc17=" + isToc17 +
            ", isTowms=" + isTowms +
            ", predistime=" + predistime +
            ", isTorfid=" + isTorfid +
            ", productNo=" + productNo +
            ", factoryid=" + factoryid +
            ", goodsno=" + goodsno +
            ", isTojd=" + isTojd +
            ", isJj=" + isJj +
            "}";
    }
}
