package com.jnby.infrastructure.bojun.model;

import java.util.Date;

public class EmployeeBase {
    private Integer id;

    private String linkid;

    private String name;

    private String position;

    private String mobile;

    private String email;

    private String weixinid;

    private String ppid;

    private String roleid;

    private String wxopenid;

    private String wxunionid;

    private Short isactive;

    private String ownerid;

    private String modifierid;

    private Date creationdate;

    private Date modifieddate;

    private Short rentid;

    private Short isneedsync;

    private Date lastsyncdate;

    private String linkstore;

    private String certno;

    private String linkid2;

    private Short wxstatus;

    private String avatar;

    private Date wxupdatedate;

    private String wximage;

    private String linksource;

    private String syncerror;

    private String recmsg;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLinkid() {
        return linkid;
    }

    public void setLinkid(String linkid) {
        this.linkid = linkid == null ? null : linkid.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position == null ? null : position.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getWeixinid() {
        return weixinid;
    }

    public void setWeixinid(String weixinid) {
        this.weixinid = weixinid == null ? null : weixinid.trim();
    }

    public String getPpid() {
        return ppid;
    }

    public void setPpid(String ppid) {
        this.ppid = ppid == null ? null : ppid.trim();
    }

    public String getRoleid() {
        return roleid;
    }

    public void setRoleid(String roleid) {
        this.roleid = roleid == null ? null : roleid.trim();
    }

    public String getWxopenid() {
        return wxopenid;
    }

    public void setWxopenid(String wxopenid) {
        this.wxopenid = wxopenid == null ? null : wxopenid.trim();
    }

    public String getWxunionid() {
        return wxunionid;
    }

    public void setWxunionid(String wxunionid) {
        this.wxunionid = wxunionid == null ? null : wxunionid.trim();
    }

    public Short getIsactive() {
        return isactive;
    }

    public void setIsactive(Short isactive) {
        this.isactive = isactive;
    }

    public String getOwnerid() {
        return ownerid;
    }

    public void setOwnerid(String ownerid) {
        this.ownerid = ownerid == null ? null : ownerid.trim();
    }

    public String getModifierid() {
        return modifierid;
    }

    public void setModifierid(String modifierid) {
        this.modifierid = modifierid == null ? null : modifierid.trim();
    }

    public Date getCreationdate() {
        return creationdate;
    }

    public void setCreationdate(Date creationdate) {
        this.creationdate = creationdate;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }

    public Short getRentid() {
        return rentid;
    }

    public void setRentid(Short rentid) {
        this.rentid = rentid;
    }

    public Short getIsneedsync() {
        return isneedsync;
    }

    public void setIsneedsync(Short isneedsync) {
        this.isneedsync = isneedsync;
    }

    public Date getLastsyncdate() {
        return lastsyncdate;
    }

    public void setLastsyncdate(Date lastsyncdate) {
        this.lastsyncdate = lastsyncdate;
    }

    public String getLinkstore() {
        return linkstore;
    }

    public void setLinkstore(String linkstore) {
        this.linkstore = linkstore == null ? null : linkstore.trim();
    }

    public String getCertno() {
        return certno;
    }

    public void setCertno(String certno) {
        this.certno = certno == null ? null : certno.trim();
    }

    public String getLinkid2() {
        return linkid2;
    }

    public void setLinkid2(String linkid2) {
        this.linkid2 = linkid2 == null ? null : linkid2.trim();
    }

    public Short getWxstatus() {
        return wxstatus;
    }

    public void setWxstatus(Short wxstatus) {
        this.wxstatus = wxstatus;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar == null ? null : avatar.trim();
    }

    public Date getWxupdatedate() {
        return wxupdatedate;
    }

    public void setWxupdatedate(Date wxupdatedate) {
        this.wxupdatedate = wxupdatedate;
    }

    public String getWximage() {
        return wximage;
    }

    public void setWximage(String wximage) {
        this.wximage = wximage == null ? null : wximage.trim();
    }

    public String getLinksource() {
        return linksource;
    }

    public void setLinksource(String linksource) {
        this.linksource = linksource == null ? null : linksource.trim();
    }

    public String getSyncerror() {
        return syncerror;
    }

    public void setSyncerror(String syncerror) {
        this.syncerror = syncerror == null ? null : syncerror.trim();
    }

    public String getRecmsg() {
        return recmsg;
    }

    public void setRecmsg(String recmsg) {
        this.recmsg = recmsg == null ? null : recmsg.trim();
    }
}