package com.jnby.infrastructure.bojun.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: wangchun
 * @Date: 2022-04-20 10:29:55
 * @Description: 
 */
@TableName("EB_TRANSFERSUMITEM")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="EbTransfersumitem对象", description="")
public class EbTransfersumitem implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableField("ID")
    private Long id;
    @TableField("AD_CLIENT_ID")
    private Long adClientId;
    @TableField("AD_ORG_ID")
    private Long adOrgId;
    @TableField("OWNERID")
    private Long ownerid;
    @TableField("MODIFIERID")
    private Long modifierid;
    @TableField("CREATIONDATE")
    private Date creationdate;
    @TableField("MODIFIEDDATE")
    private Date modifieddate;
    @TableField("ISACTIVE")
    private String isactive;
    @TableField("STATUS")
    private Integer status;
    @TableField("EB_TRANSFERSUM_ID")
    private Long ebTransfersumId;
    @TableField("ORDERNO")
    private Long orderno;
    @TableField("M_PRODUCT_ID")
    private Long mProductId;
    @TableField("M_ATTRIBUTESETINSTANCE_ID")
    private Long mAttributesetinstanceId;
    @TableField("QTYOUT")
    private Long qtyout;
    @TableField("QTYIN")
    private Long qtyin;
    @TableField("QTYDIFF")
    private Long qtydiff;
    @TableField("PRICELIST")
    private BigDecimal pricelist;
    @TableField("TOT_AMTOUT_LIST")
    private BigDecimal totAmtoutList;
    @TableField("TOT_AMTIN_LIST")
    private BigDecimal totAmtinList;
    @TableField("DESCRIPTION")
    private String description;
    @TableField("IN_STATUS")
    private Integer inStatus;
    @TableField("OUT_STATUS")
    private Integer outStatus;
    @TableField("QTY")
    private Long qty;
    @TableField("TOT_AMTQTY_LIST")
    private BigDecimal totAmtqtyList;
    @TableField("M_PRODUCTALIAS_ID")
    private Long mProductaliasId;
    @TableField("BOXNO")
    private String boxno;
    @TableField("QTYFCAN")
    private Long qtyfcan;
    @TableField("PRECOST")
    private BigDecimal precost;
    @TableField("TOT_AMT_PRECOST")
    private BigDecimal totAmtPrecost;
    @TableField("TOT_AMTIN_PRECOST")
    private BigDecimal totAmtinPrecost;
    @TableField("QTYCAN")
    private Long qtycan;
    @TableField("NC_STATUS")
    private Integer ncStatus;
    @TableField("NC_STATUSERID")
    private Long ncStatuserid;
    @TableField("NC_STATUSTIME")
    private Date ncStatustime;
    @TableField("DISCOUNT")
    private BigDecimal discount;
    @TableField("Y_DISCOUNT")
    private BigDecimal yDiscount;


    public Long getId() {
        return id;
    }

    public EbTransfersumitem setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getAdClientId() {
        return adClientId;
    }

    public EbTransfersumitem setAdClientId(Long adClientId) {
        this.adClientId = adClientId;
        return this;
    }

    public Long getAdOrgId() {
        return adOrgId;
    }

    public EbTransfersumitem setAdOrgId(Long adOrgId) {
        this.adOrgId = adOrgId;
        return this;
    }

    public Long getOwnerid() {
        return ownerid;
    }

    public EbTransfersumitem setOwnerid(Long ownerid) {
        this.ownerid = ownerid;
        return this;
    }

    public Long getModifierid() {
        return modifierid;
    }

    public EbTransfersumitem setModifierid(Long modifierid) {
        this.modifierid = modifierid;
        return this;
    }

    public Date getCreationdate() {
        return creationdate;
    }

    public EbTransfersumitem setCreationdate(Date creationdate) {
        this.creationdate = creationdate;
        return this;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public EbTransfersumitem setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
        return this;
    }

    public String getIsactive() {
        return isactive;
    }

    public EbTransfersumitem setIsactive(String isactive) {
        this.isactive = isactive;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public EbTransfersumitem setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Long getEbTransfersumId() {
        return ebTransfersumId;
    }

    public EbTransfersumitem setEbTransfersumId(Long ebTransfersumId) {
        this.ebTransfersumId = ebTransfersumId;
        return this;
    }

    public Long getOrderno() {
        return orderno;
    }

    public EbTransfersumitem setOrderno(Long orderno) {
        this.orderno = orderno;
        return this;
    }

    public Long getmProductId() {
        return mProductId;
    }

    public EbTransfersumitem setmProductId(Long mProductId) {
        this.mProductId = mProductId;
        return this;
    }

    public Long getmAttributesetinstanceId() {
        return mAttributesetinstanceId;
    }

    public EbTransfersumitem setmAttributesetinstanceId(Long mAttributesetinstanceId) {
        this.mAttributesetinstanceId = mAttributesetinstanceId;
        return this;
    }

    public Long getQtyout() {
        return qtyout;
    }

    public EbTransfersumitem setQtyout(Long qtyout) {
        this.qtyout = qtyout;
        return this;
    }

    public Long getQtyin() {
        return qtyin;
    }

    public EbTransfersumitem setQtyin(Long qtyin) {
        this.qtyin = qtyin;
        return this;
    }

    public Long getQtydiff() {
        return qtydiff;
    }

    public EbTransfersumitem setQtydiff(Long qtydiff) {
        this.qtydiff = qtydiff;
        return this;
    }

    public BigDecimal getPricelist() {
        return pricelist;
    }

    public EbTransfersumitem setPricelist(BigDecimal pricelist) {
        this.pricelist = pricelist;
        return this;
    }

    public BigDecimal getTotAmtoutList() {
        return totAmtoutList;
    }

    public EbTransfersumitem setTotAmtoutList(BigDecimal totAmtoutList) {
        this.totAmtoutList = totAmtoutList;
        return this;
    }

    public BigDecimal getTotAmtinList() {
        return totAmtinList;
    }

    public EbTransfersumitem setTotAmtinList(BigDecimal totAmtinList) {
        this.totAmtinList = totAmtinList;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public EbTransfersumitem setDescription(String description) {
        this.description = description;
        return this;
    }

    public Integer getInStatus() {
        return inStatus;
    }

    public EbTransfersumitem setInStatus(Integer inStatus) {
        this.inStatus = inStatus;
        return this;
    }

    public Integer getOutStatus() {
        return outStatus;
    }

    public EbTransfersumitem setOutStatus(Integer outStatus) {
        this.outStatus = outStatus;
        return this;
    }

    public Long getQty() {
        return qty;
    }

    public EbTransfersumitem setQty(Long qty) {
        this.qty = qty;
        return this;
    }

    public BigDecimal getTotAmtqtyList() {
        return totAmtqtyList;
    }

    public EbTransfersumitem setTotAmtqtyList(BigDecimal totAmtqtyList) {
        this.totAmtqtyList = totAmtqtyList;
        return this;
    }

    public Long getmProductaliasId() {
        return mProductaliasId;
    }

    public EbTransfersumitem setmProductaliasId(Long mProductaliasId) {
        this.mProductaliasId = mProductaliasId;
        return this;
    }

    public String getBoxno() {
        return boxno;
    }

    public EbTransfersumitem setBoxno(String boxno) {
        this.boxno = boxno;
        return this;
    }

    public Long getQtyfcan() {
        return qtyfcan;
    }

    public EbTransfersumitem setQtyfcan(Long qtyfcan) {
        this.qtyfcan = qtyfcan;
        return this;
    }

    public BigDecimal getPrecost() {
        return precost;
    }

    public EbTransfersumitem setPrecost(BigDecimal precost) {
        this.precost = precost;
        return this;
    }

    public BigDecimal getTotAmtPrecost() {
        return totAmtPrecost;
    }

    public EbTransfersumitem setTotAmtPrecost(BigDecimal totAmtPrecost) {
        this.totAmtPrecost = totAmtPrecost;
        return this;
    }

    public BigDecimal getTotAmtinPrecost() {
        return totAmtinPrecost;
    }

    public EbTransfersumitem setTotAmtinPrecost(BigDecimal totAmtinPrecost) {
        this.totAmtinPrecost = totAmtinPrecost;
        return this;
    }

    public Long getQtycan() {
        return qtycan;
    }

    public EbTransfersumitem setQtycan(Long qtycan) {
        this.qtycan = qtycan;
        return this;
    }

    public Integer getNcStatus() {
        return ncStatus;
    }

    public EbTransfersumitem setNcStatus(Integer ncStatus) {
        this.ncStatus = ncStatus;
        return this;
    }

    public Long getNcStatuserid() {
        return ncStatuserid;
    }

    public EbTransfersumitem setNcStatuserid(Long ncStatuserid) {
        this.ncStatuserid = ncStatuserid;
        return this;
    }

    public Date getNcStatustime() {
        return ncStatustime;
    }

    public EbTransfersumitem setNcStatustime(Date ncStatustime) {
        this.ncStatustime = ncStatustime;
        return this;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public EbTransfersumitem setDiscount(BigDecimal discount) {
        this.discount = discount;
        return this;
    }

    public BigDecimal getyDiscount() {
        return yDiscount;
    }

    public EbTransfersumitem setyDiscount(BigDecimal yDiscount) {
        this.yDiscount = yDiscount;
        return this;
    }

    @Override
    public String toString() {
        return "EbTransfersumitemModel{" +
            "id=" + id +
            ", adClientId=" + adClientId +
            ", adOrgId=" + adOrgId +
            ", ownerid=" + ownerid +
            ", modifierid=" + modifierid +
            ", creationdate=" + creationdate +
            ", modifieddate=" + modifieddate +
            ", isactive=" + isactive +
            ", status=" + status +
            ", ebTransfersumId=" + ebTransfersumId +
            ", orderno=" + orderno +
            ", mProductId=" + mProductId +
            ", mAttributesetinstanceId=" + mAttributesetinstanceId +
            ", qtyout=" + qtyout +
            ", qtyin=" + qtyin +
            ", qtydiff=" + qtydiff +
            ", pricelist=" + pricelist +
            ", totAmtoutList=" + totAmtoutList +
            ", totAmtinList=" + totAmtinList +
            ", description=" + description +
            ", inStatus=" + inStatus +
            ", outStatus=" + outStatus +
            ", qty=" + qty +
            ", totAmtqtyList=" + totAmtqtyList +
            ", mProductaliasId=" + mProductaliasId +
            ", boxno=" + boxno +
            ", qtyfcan=" + qtyfcan +
            ", precost=" + precost +
            ", totAmtPrecost=" + totAmtPrecost +
            ", totAmtinPrecost=" + totAmtinPrecost +
            ", qtycan=" + qtycan +
            ", ncStatus=" + ncStatus +
            ", ncStatuserid=" + ncStatuserid +
            ", ncStatustime=" + ncStatustime +
            ", discount=" + discount +
            ", yDiscount=" + yDiscount +
            "}";
    }
}
