package com.jnby.infrastructure.bojun.model;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author: lwz
 * @Date: 2021-12-01 14:17:06
 * @Description:
 */
@TableName("T_OMSREFUNDORDER")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value = "TOmsRefundOrder对象", description = "")
public class TOmsRefundOrder implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private BigDecimal id;

    @TableField("REFUNDTYPE")
    private BigDecimal refundtype;

    @TableField("REFUND_ID")
    private String refundId;

    @TableField("ORIGINALORDERNO")
    private BigDecimal originalorderno;

    @TableField("BUYER_NICK")
    private String buyerNick;

    @TableField("CREATED")
    private Date created;

    @TableField("MODIFIED")
    private Date modified;

    @TableField("ORDER_STATUS")
    private BigDecimal orderStatus;

    @TableField("GOOD_RETURN_TIME")
    private Date goodReturnTime;

    @TableField("REFUND_FEE")
    private BigDecimal refundFee;

    @TableField("OMSREASONTYPEID")
    private String omsreasontypeid;

    @TableField("RETURNDESC")
    private String returndesc;

    @TableField("COMPANY_NAME")
    private String companyName;

    @TableField("SID")
    private String sid;

    @TableField("REMARK")
    private String remark;

    @TableField("CREATIONDATE")
    private Date creationdate;

    @TableField("OWNERID")
    private String ownerid;

    @TableField("GOODPRICE")
    private BigDecimal goodprice;

    @TableField("REFUNDSHIPAMOUNT")
    private BigDecimal refundshipamount;

    @TableField("REFUNDOTHERAMOUNT")
    private BigDecimal refundotheramount;

    @TableField("RECEIVPROVINCE")
    private BigDecimal receivprovince;

    @TableField("RECEIVCITY")
    private BigDecimal receivcity;

    @TableField("RECEIVAREA")
    private BigDecimal receivarea;

    @TableField("RECEIVADDRESS")
    private String receivaddress;

    @TableField("RECEIVZIP")
    private String receivzip;

    @TableField("RECEIVMOBILE")
    private String receivmobile;
    @TableField("RECEIVPHONE")
    private String receivphone;
    @TableField("RECEIVNAME")
    private String receivname;
    @TableField("MODIFIERID")
    private String modifierid;
    @TableField("MODIFIEDDATE")
    private Date modifieddate;
    @TableField("ISACTIVE")
    private Integer isactive;
    @TableField("NEWORDER_FEE")
    private BigDecimal neworderFee;
    @TableField("RECEIVPROVINCENAME")
    private String receivprovincename;
    @TableField("RECEIVCITYNAME")
    private String receivcityname;
    @TableField("RECEIVAREANAME")
    private String receivareaname;
    @TableField("SOURCECODE")
    private String sourcecode;
    @TableField("RECEIVSHIPAMOUNT")
    private BigDecimal receivshipamount;
    @TableField("INVENTEDSTATUS")
    private BigDecimal inventedstatus;
    @TableField("ISTODRP")
    private BigDecimal istodrp;
    @TableField("SHOPID")
    private BigDecimal shopid;
    @TableField("PLATFORM")
    private BigDecimal platform;
    @TableField("USERID")
    private BigDecimal userid;
    @TableField("COMPANYID")
    private BigDecimal companyid;
    @TableField("IESWAREHOUSEID")
    private BigDecimal ieswarehouseid;
    @TableField("OMS_DISTRIBUTORID")
    private BigDecimal omsDistributorid;
    @TableField("ISRESERVEDSTOCK")
    private Integer isreservedstock;
    @TableField("ORDERFLAG")
    private BigDecimal orderflag;
    @TableField("TMCURRENTPHASETIMEOUT")
    private Date tmcurrentphasetimeout;
    @TableField("ALLSKU")
    private String allsku;
    @TableField("REFUND_PHASE")
    private String refundPhase;
    @TableField("ALLINSTORENUM")
    private BigDecimal allinstorenum;
    @TableField("CHECKDATE")
    private Date checkdate;
    @TableField("INSTOREDATE")
    private Date instoredate;
    @TableField("INSPECTOR")
    private String inspector;
    @TableField("INSTOREWORKER")
    private String instoreworker;
    @TableField("REFUNDTOSTORE")
    private Integer refundtostore;
    @TableField("CHECKSTATUS")
    private BigDecimal checkstatus;
    @TableField("CHECKINFO")
    private String checkinfo;
    @TableField("STATUS")
    private Long status;
    @TableField("ISSYNCPLAT")
    private Integer issyncplat;
    @TableField("SYNCMESSAGE")
    private String syncmessage;
    @ApiModelProperty(value = "是否黑名单,否0，是1；默认0")
    @TableField("ISBLACKUSER")
    private Integer isblackuser;
    @TableField("ISSTORAGE")
    private Integer isstorage;
    @TableField("ISEXAMINE")
    private Integer isexamine;
    @TableField("OPERATOR")
    private String operator;
    @TableField("ISNOTLOGMBER")
    private Integer isnotlogmber;
    @TableField("DISPUTE_ID")
    private String disputeId;
    @TableField("RETURNGOODCONFIRM")
    private Integer returngoodconfirm;
    @TableField("WMSFAILREASON")
    private String wmsfailreason;
    @TableField("ISTOWMS")
    private Integer istowms;
    @ApiModelProperty(value = "0未丢件1丢件")
    @TableField("ISLOSE")
    private Long islose;
    @TableField("ISOPENAG")
    private Integer isopenag;
    @ApiModelProperty(value = "0未发监控1已发监控数据")
    @TableField("ISMONITOR")
    private Long ismonitor;
    @TableField("ORIGSOURCECODE")
    private String origsourcecode;
    @ApiModelProperty(value = "换货原单平台号")
    @TableField("ORIGINALTID")
    private String originaltid;
    @TableField("TC_ORDER_ID")
    private String tcOrderId;
    @TableField("ORIGINALRECEIVMOBILE")
    private String originalreceivmobile;

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getRefundtype() {
        return refundtype;
    }

    public void setRefundtype(BigDecimal refundtype) {
        this.refundtype = refundtype;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public BigDecimal getOriginalorderno() {
        return originalorderno;
    }

    public void setOriginalorderno(BigDecimal originalorderno) {
        this.originalorderno = originalorderno;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public BigDecimal getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(BigDecimal orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Date getGoodReturnTime() {
        return goodReturnTime;
    }

    public void setGoodReturnTime(Date goodReturnTime) {
        this.goodReturnTime = goodReturnTime;
    }

    public BigDecimal getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(BigDecimal refundFee) {
        this.refundFee = refundFee;
    }

    public String getOmsreasontypeid() {
        return omsreasontypeid;
    }

    public void setOmsreasontypeid(String omsreasontypeid) {
        this.omsreasontypeid = omsreasontypeid;
    }

    public String getReturndesc() {
        return returndesc;
    }

    public void setReturndesc(String returndesc) {
        this.returndesc = returndesc;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreationdate() {
        return creationdate;
    }

    public void setCreationdate(Date creationdate) {
        this.creationdate = creationdate;
    }

    public String getOwnerid() {
        return ownerid;
    }

    public void setOwnerid(String ownerid) {
        this.ownerid = ownerid;
    }

    public BigDecimal getGoodprice() {
        return goodprice;
    }

    public void setGoodprice(BigDecimal goodprice) {
        this.goodprice = goodprice;
    }

    public BigDecimal getRefundshipamount() {
        return refundshipamount;
    }

    public void setRefundshipamount(BigDecimal refundshipamount) {
        this.refundshipamount = refundshipamount;
    }

    public BigDecimal getRefundotheramount() {
        return refundotheramount;
    }

    public void setRefundotheramount(BigDecimal refundotheramount) {
        this.refundotheramount = refundotheramount;
    }

    public BigDecimal getReceivprovince() {
        return receivprovince;
    }

    public void setReceivprovince(BigDecimal receivprovince) {
        this.receivprovince = receivprovince;
    }

    public BigDecimal getReceivcity() {
        return receivcity;
    }

    public void setReceivcity(BigDecimal receivcity) {
        this.receivcity = receivcity;
    }

    public BigDecimal getReceivarea() {
        return receivarea;
    }

    public void setReceivarea(BigDecimal receivarea) {
        this.receivarea = receivarea;
    }

    public String getReceivaddress() {
        return receivaddress;
    }

    public void setReceivaddress(String receivaddress) {
        this.receivaddress = receivaddress;
    }

    public String getReceivzip() {
        return receivzip;
    }

    public void setReceivzip(String receivzip) {
        this.receivzip = receivzip;
    }

    public String getReceivmobile() {
        return receivmobile;
    }

    public void setReceivmobile(String receivmobile) {
        this.receivmobile = receivmobile;
    }

    public String getReceivphone() {
        return receivphone;
    }

    public void setReceivphone(String receivphone) {
        this.receivphone = receivphone;
    }

    public String getReceivname() {
        return receivname;
    }

    public void setReceivname(String receivname) {
        this.receivname = receivname;
    }

    public String getModifierid() {
        return modifierid;
    }

    public void setModifierid(String modifierid) {
        this.modifierid = modifierid;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }

    public Integer getIsactive() {
        return isactive;
    }

    public void setIsactive(Integer isactive) {
        this.isactive = isactive;
    }

    public BigDecimal getNeworderFee() {
        return neworderFee;
    }

    public void setNeworderFee(BigDecimal neworderFee) {
        this.neworderFee = neworderFee;
    }

    public String getReceivprovincename() {
        return receivprovincename;
    }

    public void setReceivprovincename(String receivprovincename) {
        this.receivprovincename = receivprovincename;
    }

    public String getReceivcityname() {
        return receivcityname;
    }

    public void setReceivcityname(String receivcityname) {
        this.receivcityname = receivcityname;
    }

    public String getReceivareaname() {
        return receivareaname;
    }

    public void setReceivareaname(String receivareaname) {
        this.receivareaname = receivareaname;
    }

    public String getSourcecode() {
        return sourcecode;
    }

    public void setSourcecode(String sourcecode) {
        this.sourcecode = sourcecode;
    }

    public BigDecimal getReceivshipamount() {
        return receivshipamount;
    }

    public void setReceivshipamount(BigDecimal receivshipamount) {
        this.receivshipamount = receivshipamount;
    }

    public BigDecimal getInventedstatus() {
        return inventedstatus;
    }

    public void setInventedstatus(BigDecimal inventedstatus) {
        this.inventedstatus = inventedstatus;
    }

    public BigDecimal getIstodrp() {
        return istodrp;
    }

    public void setIstodrp(BigDecimal istodrp) {
        this.istodrp = istodrp;
    }

    public BigDecimal getShopid() {
        return shopid;
    }

    public void setShopid(BigDecimal shopid) {
        this.shopid = shopid;
    }

    public BigDecimal getPlatform() {
        return platform;
    }

    public void setPlatform(BigDecimal platform) {
        this.platform = platform;
    }

    public BigDecimal getUserid() {
        return userid;
    }

    public void setUserid(BigDecimal userid) {
        this.userid = userid;
    }

    public BigDecimal getCompanyid() {
        return companyid;
    }

    public void setCompanyid(BigDecimal companyid) {
        this.companyid = companyid;
    }

    public BigDecimal getIeswarehouseid() {
        return ieswarehouseid;
    }

    public void setIeswarehouseid(BigDecimal ieswarehouseid) {
        this.ieswarehouseid = ieswarehouseid;
    }

    public BigDecimal getOmsDistributorid() {
        return omsDistributorid;
    }

    public void setOmsDistributorid(BigDecimal omsDistributorid) {
        this.omsDistributorid = omsDistributorid;
    }

    public Integer getIsreservedstock() {
        return isreservedstock;
    }

    public void setIsreservedstock(Integer isreservedstock) {
        this.isreservedstock = isreservedstock;
    }

    public BigDecimal getOrderflag() {
        return orderflag;
    }

    public void setOrderflag(BigDecimal orderflag) {
        this.orderflag = orderflag;
    }

    public Date getTmcurrentphasetimeout() {
        return tmcurrentphasetimeout;
    }

    public void setTmcurrentphasetimeout(Date tmcurrentphasetimeout) {
        this.tmcurrentphasetimeout = tmcurrentphasetimeout;
    }

    public String getAllsku() {
        return allsku;
    }

    public void setAllsku(String allsku) {
        this.allsku = allsku;
    }

    public String getRefundPhase() {
        return refundPhase;
    }

    public void setRefundPhase(String refundPhase) {
        this.refundPhase = refundPhase;
    }

    public BigDecimal getAllinstorenum() {
        return allinstorenum;
    }

    public void setAllinstorenum(BigDecimal allinstorenum) {
        this.allinstorenum = allinstorenum;
    }

    public Date getCheckdate() {
        return checkdate;
    }

    public void setCheckdate(Date checkdate) {
        this.checkdate = checkdate;
    }

    public Date getInstoredate() {
        return instoredate;
    }

    public void setInstoredate(Date instoredate) {
        this.instoredate = instoredate;
    }

    public String getInspector() {
        return inspector;
    }

    public void setInspector(String inspector) {
        this.inspector = inspector;
    }

    public String getInstoreworker() {
        return instoreworker;
    }

    public void setInstoreworker(String instoreworker) {
        this.instoreworker = instoreworker;
    }

    public Integer getRefundtostore() {
        return refundtostore;
    }

    public void setRefundtostore(Integer refundtostore) {
        this.refundtostore = refundtostore;
    }

    public BigDecimal getCheckstatus() {
        return checkstatus;
    }

    public void setCheckstatus(BigDecimal checkstatus) {
        this.checkstatus = checkstatus;
    }

    public String getCheckinfo() {
        return checkinfo;
    }

    public void setCheckinfo(String checkinfo) {
        this.checkinfo = checkinfo;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Integer getIssyncplat() {
        return issyncplat;
    }

    public void setIssyncplat(Integer issyncplat) {
        this.issyncplat = issyncplat;
    }

    public String getSyncmessage() {
        return syncmessage;
    }

    public void setSyncmessage(String syncmessage) {
        this.syncmessage = syncmessage;
    }

    public Integer getIsblackuser() {
        return isblackuser;
    }

    public void setIsblackuser(Integer isblackuser) {
        this.isblackuser = isblackuser;
    }

    public Integer getIsstorage() {
        return isstorage;
    }

    public void setIsstorage(Integer isstorage) {
        this.isstorage = isstorage;
    }

    public Integer getIsexamine() {
        return isexamine;
    }

    public void setIsexamine(Integer isexamine) {
        this.isexamine = isexamine;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Integer getIsnotlogmber() {
        return isnotlogmber;
    }

    public void setIsnotlogmber(Integer isnotlogmber) {
        this.isnotlogmber = isnotlogmber;
    }

    public String getDisputeId() {
        return disputeId;
    }

    public void setDisputeId(String disputeId) {
        this.disputeId = disputeId;
    }

    public Integer getReturngoodconfirm() {
        return returngoodconfirm;
    }

    public void setReturngoodconfirm(Integer returngoodconfirm) {
        this.returngoodconfirm = returngoodconfirm;
    }

    public String getWmsfailreason() {
        return wmsfailreason;
    }

    public void setWmsfailreason(String wmsfailreason) {
        this.wmsfailreason = wmsfailreason;
    }

    public Integer getIstowms() {
        return istowms;
    }

    public void setIstowms(Integer istowms) {
        this.istowms = istowms;
    }

    public Long getIslose() {
        return islose;
    }

    public void setIslose(Long islose) {
        this.islose = islose;
    }

    public Integer getIsopenag() {
        return isopenag;
    }

    public void setIsopenag(Integer isopenag) {
        this.isopenag = isopenag;
    }

    public Long getIsmonitor() {
        return ismonitor;
    }

    public void setIsmonitor(Long ismonitor) {
        this.ismonitor = ismonitor;
    }

    public String getOrigsourcecode() {
        return origsourcecode;
    }

    public void setOrigsourcecode(String origsourcecode) {
        this.origsourcecode = origsourcecode;
    }

    public String getOriginaltid() {
        return originaltid;
    }

    public void setOriginaltid(String originaltid) {
        this.originaltid = originaltid;
    }

    public String getTcOrderId() {
        return tcOrderId;
    }

    public void setTcOrderId(String tcOrderId) {
        this.tcOrderId = tcOrderId;
    }

    public String getOriginalreceivmobile() {
        return originalreceivmobile;
    }

    public void setOriginalreceivmobile(String originalreceivmobile) {
        this.originalreceivmobile = originalreceivmobile;
    }
}
