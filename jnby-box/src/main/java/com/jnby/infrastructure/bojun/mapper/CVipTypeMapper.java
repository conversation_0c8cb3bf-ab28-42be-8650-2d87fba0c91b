package com.jnby.infrastructure.bojun.mapper;

import com.jnby.infrastructure.bojun.model.CVipType;
import com.jnby.module.calc.entity.BrandDiscountEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CVipTypeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CVipType record);

    int insertSelective(CVipType record);

    CVipType selectByPrimaryKey(Long id);

    @Select("select id, name, description, discount from c_viptype where isactive = 'Y'")
    List<CVipType> selectAll();

    int updateByPrimaryKeySelective(CVipType record);

    int updateByPrimaryKey(CVipType record);

    List<String> selectUserLinealBrand(String unionId);

    List<String> selectUserDealerBrand(String unionId);

    List<CVipType> selectByIds(@Param("ids") List<Long> cVipTypes);


    List<CVipType> selectByPrimaryIds(@Param("ids") List<Long> cVipTypes);

    List<CVipType> selectAllData();

    List<BrandDiscountEntity> selectBrandMinDiscountByBrandIds(@Param("ids") List<Long> brandIds);

    List<CVipType> selectByCodes(@Param("codes") List<String> codes);
}