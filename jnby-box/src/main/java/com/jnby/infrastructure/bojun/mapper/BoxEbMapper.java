package com.jnby.infrastructure.bojun.mapper;


import java.util.HashMap;

public interface BoxEbMapper {

    /**
     * 生成要货单
     * @param map
     */
    void transferSumInsert(HashMap<String, Object> map);

    /**
     * 自动匹配数量
     * @param map
     */
    void autoMatchQty(HashMap<String, Object> map);

    /**
     * 提交入库单
     * @param map
     */
    void submitMIn(HashMap<String, Object> map);

    /**
     * 提交发货单
     * @param map
     */
    void submitEbTransferSum(HashMap<String, Object> map);

    /**
     * 内淘发货
     * @param map
     */
    void boxEbTransferSumExpress(HashMap<String, Object> map);
}
