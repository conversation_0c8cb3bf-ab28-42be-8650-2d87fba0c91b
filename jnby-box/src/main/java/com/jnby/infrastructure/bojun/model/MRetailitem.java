package com.jnby.infrastructure.bojun.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: wangchun
 * @Date: 2021-12-22 15:47:48
 * @Description: 
 */
@TableName("M_RETAILITEM")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="MRetailitem对象", description="")
public class MRetailitem implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private Long id;
    @TableField("AD_CLIENT_ID")
    private Long adClientId;
    @TableField("AD_ORG_ID")
    private Long adOrgId;
    @TableField("ISACTIVE")
    private String isactive;
    @TableField("CREATIONDATE")
    private Date creationdate;
    @TableField("OWNERID")
    private Long ownerid;
    @TableField("MODIFIEDDATE")
    private Date modifieddate;
    @TableField("MODIFIERID")
    private Long modifierid;
    @TableField("M_RETAIL_ID")
    private Long mRetailId;
    @TableField("ORDERNO")
    private Long orderno;
    @TableField("C_VIP_ID")
    private Long cVipId;
    @TableField("SALESREP_ID")
    private Long salesrepId;
    @TableField("M_PRODUCT_ID")
    private Long mProductId;
    @TableField("M_ATTRIBUTESETINSTANCE_ID")
    private Long mAttributesetinstanceId;
    @TableField("QTY")
    private Long qty;
    @TableField("PRICELIST")
    private BigDecimal pricelist;
    @TableField("PRICEACTUAL")
    private BigDecimal priceactual;
    @TableField("DISCOUNT")
    private BigDecimal discount;
    @TableField("DESCRIPTION")
    private String description;
    @TableField("TOT_AMT_LIST")
    private BigDecimal totAmtList;
    @TableField("TOT_AMT_ACTUAL")
    private BigDecimal totAmtActual;
    @TableField("STATUS")
    private Integer status;
    @TableField("TYPE")
    private Integer type;
    @TableField("ORGDOCNO")
    private String orgdocno;
    @TableField("MARKBALTYPE")
    private Long markbaltype;
    @TableField("M_PRODUCTALIAS_ID")
    private Long mProductaliasId;
    @TableField("C_MARKBALTYPE_ID")
    private Long cMarkbaltypeId;
    @TableField("OLD_TRACE")
    private String oldTrace;
    @TableField("OLD_AUTHCODE")
    private String oldAuthcode;
    @TableField("OLD_DATETIME")
    private Date oldDatetime;
    @TableField("OLD_FLAG")
    private Long oldFlag;
    @TableField("OLD_REMARK")
    private String oldRemark;
    @TableField("HANDDIS")
    private String handdis;
    @TableField("INTEGRAL")
    private Long integral;
    @TableField("SIG_TDEFPOSDIS_ID")
    private Long sigTdefposdisId;
    @TableField("COM_TDEFPOSDIS_ID")
    private Long comTdefposdisId;
    @TableField("C_INTEGRALMIN_ID")
    private Long cIntegralminId;
    @TableField("B_RETAILDISSKU_ID")
    private Long bRetaildisskuId;
    @TableField("ORG_M_RETAILITEM_ID")
    private Long orgMRetailitemId;
    @TableField("RETURNQTY")
    private Long returnqty;
    @TableField("RQTY")
    private Long rqty;
    @TableField("SALESREP_ID2")
    private Long salesrepId2;
    @TableField("SALESREP_ID3")
    private Long salesrepId3;
    @TableField("SALESREPS_NAME")
    private String salesrepsName;
    @TableField("SALESREPS_ID")
    private String salesrepsId;
    @TableField("C_VIPBIRDIS_ID")
    private Long cVipbirdisId;
    @TableField("IS_INTEGRAL")
    private String isIntegral;
    @TableField("NC_STATUS")
    private Integer ncStatus;
    @TableField("NC_STATUSTIME")
    private Date ncStatustime;
    @TableField("NC_STATUSERID")
    private Long ncStatuserid;
    @TableField("M_RETAILITEM_ID")
    private Long mRetailitemId;
    @TableField("RCANQTY")
    private Long rcanqty;
    @TableField("EB_SO_ID")
    private Long ebSoId;
    @TableField("WEBPOS_RETREASON_ID")
    private Long webposRetreasonId;
    @TableField("SALESREPS_RATE")
    private String salesrepsRate;
    @TableField("SET_STATUS")
    private Integer setStatus;
    @TableField("SETERID")
    private Long seterid;
    @TableField("SETTIME")
    private Date settime;
    @TableField("TBQTY")
    private Long tbqty;
    @TableField("RETTBQTY")
    private Long rettbqty;
    @TableField("RETQTY")
    private Long retqty;
    @TableField("NC_ZGSTATUS")
    private Integer ncZgstatus;
    @TableField("SIG_TDEFPOSDIS_MOREID")
    private String sigTdefposdisMoreid;
    @TableField("COM_TDEFPOSDIS_MOREID")
    private String comTdefposdisMoreid;
    @TableField("B_RETAILDISSKU_MOREID")
    private String bRetaildisskuMoreid;
    @TableField("IS_EXC")
    private String isExc;
    @TableField("M_DIM1_ID")
    private Long mDim1Id;
    @TableField("MASTERCODE_PDT")
    private String mastercodePdt;
    @TableField("ACTUALLY_PRICE")
    private Long actuallyPrice;
    @TableField("SALESREP_NAME")
    private String salesrepName;
    @TableField("C_DISPLAY_AREA_ID")
    private Long cDisplayAreaId;
    @TableField("C_DISPLAY_TYPE_NAME")
    private String cDisplayTypeName;
    @TableField("IS_CLOUD")
    private String isCloud;
    @TableField("USEINTEGRAL")
    private String useintegral;
    @TableField("IS_SWAP")
    private String isSwap;
    @TableField("C_VIPBIRDISPROITEM_ID")
    private Long cVipbirdisproitemId;
    @TableField("PROMOTION")
    private String promotion;
    @TableField("PRICEACTUAL_WITHOUTTAX")
    private BigDecimal priceactualWithouttax;
    @TableField("C_STORE_LOCATION_ID")
    private Long cStoreLocationId;
    @TableField("TAXRATE")
    private BigDecimal taxrate;
    @TableField("VOU_NO")
    private String vouNo;
    @TableField("IS_MART_READ")
    private Integer isMartRead;
    @TableField("INTEGRAL_NEW")
    private Long integralNew;
    @TableField("NUMEB")
    private Long numeb;
    @TableField("MODIFYAMT_REASON_ID")
    private Long modifyamtReasonId;
    @TableField("PICOUPONDIS")
    private BigDecimal picoupondis;
    @TableField("IS_CHECK")
    private String isCheck;
    @TableField("OFF_PERCENT")
    private String offPercent;
    @TableField("VIRTUALPRICE")
    private BigDecimal virtualprice;
    @TableField("ACTUALPRICE")
    private BigDecimal actualprice;
    @TableField("DELIVERYDATE")
    private String deliverydate;
    @TableField("CHANGEPRICE_USERID")
    private Long changepriceUserid;
    @TableField("CNY_TOT_AMT_ACTUAL")
    private BigDecimal cnyTotAmtActual;
    @TableField("ISEXCHANGE")
    private String isexchange;
    @TableField("FASTNO_PRICE")
    private BigDecimal fastnoPrice;
    @TableField("COMMON01")
    private BigDecimal common01;
    @TableField("C_REALNAME")
    private String cRealname;
    @TableField("C_REALSTORE")
    private BigDecimal cRealstore;
    @TableField("C_INVENTORY")
    private BigDecimal cInventory;
    @TableField("IS_EXPRESS")
    private String isExpress;
    @TableField("COMMON03")
    private BigDecimal common03;
    @TableField("COMMON02")
    private BigDecimal common02;
    @TableField("PRICESETTLE")
    private BigDecimal pricesettle;
    @TableField("TOT_PASSENGER_AMT")
    private BigDecimal totPassengerAmt;
    @TableField("CUSTOM_REASON_ID")
    private String customReasonId;
    @TableField("DELIVERYTYPE")
    private Integer deliverytype;
    @TableField("ISBLESSING")
    private String isblessing;
    @TableField("UNTAXEDPRICE")
    private BigDecimal untaxedprice;
    @TableField("TAX_AMOUNT1")
    private BigDecimal taxAmount1;
    @TableField("TAX_AMOUNT2")
    private BigDecimal taxAmount2;
    @TableField("TAXRATE1")
    private BigDecimal taxrate1;
    @TableField("TAXRATE2")
    private BigDecimal taxrate2;
    @TableField("DISCOUNTAMT")
    private BigDecimal discountamt;
    @TableField("WEIGHT")
    private BigDecimal weight;
    @TableField("UNIT")
    private String unit;
    @TableField("INTEGRAL_VALUE")
    private Long integralValue;
    @TableField("LINENO")
    private String lineno;
    @TableField("SUBACCOUNT_ID")
    private Long subaccountId;
    @TableField("ENTERING_REASON_ID")
    private String enteringReasonId;
    @TableField("AMOUNTAFTERDIS")
    private BigDecimal amountafterdis;
    @TableField("AMOUNTOFUSEDGOODS")
    private BigDecimal amountofusedgoods;
    @TableField("RESERVEPAYCODE")
    private String reservepaycode;
    @TableField("DELIVERYTIME")
    private String deliverytime;
    @TableField("INSTALLMAINTENANCE")
    private String installmaintenance;
    @TableField("DOORREMARKS")
    private String doorremarks;
    @TableField("SALECODE")
    private String salecode;
    @TableField("VOU_DISCOUNTAM")
    private BigDecimal vouDiscountam;
    @TableField("BATCH_NO")
    private String batchNo;
    @TableField("PRODUCTION_DATE")
    private String productionDate;
    @TableField("SHELFLIFE_NO")
    private String shelflifeNo;
    @TableField("IS_REVERSE_O2O")
    private String isReverseO2o;
    @TableField("AUTHORREALNAME")
    private String authorrealname;
    @TableField("AUTHOREMAIL")
    private String authoremail;
    @TableField("ISDROPPRICE")
    private String isdropprice;
    @TableField("EANCODE")
    private String eancode;
    @TableField("DM_PRO_ENAME")
    private String dmProEname;
    @TableField("MALLCARD")
    private String mallcard;
    @TableField("DISCOUNTAMTLIST")
    private String discountamtlist;
    @TableField("IS_DEL_SALE")
    private String isDelSale;
    @TableField("SKU_GROUP_NO")
    private String skuGroupNo;
    @TableField("MARKET_RATE")
    private BigDecimal marketRate;
    @TableField("INTSCODE")
    private String intscode;
    @TableField("M_RETAIL_DISCOUNTACTIVITY")
    private BigDecimal mRetailDiscountactivity;
    @TableField(exist = false)
    private String no;


    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public Long getId() {
        return id;
    }

    public MRetailitem setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getAdClientId() {
        return adClientId;
    }

    public MRetailitem setAdClientId(Long adClientId) {
        this.adClientId = adClientId;
        return this;
    }

    public Long getAdOrgId() {
        return adOrgId;
    }

    public MRetailitem setAdOrgId(Long adOrgId) {
        this.adOrgId = adOrgId;
        return this;
    }

    public String getIsactive() {
        return isactive;
    }

    public MRetailitem setIsactive(String isactive) {
        this.isactive = isactive;
        return this;
    }

    public Date getCreationdate() {
        return creationdate;
    }

    public MRetailitem setCreationdate(Date creationdate) {
        this.creationdate = creationdate;
        return this;
    }

    public Long getOwnerid() {
        return ownerid;
    }

    public MRetailitem setOwnerid(Long ownerid) {
        this.ownerid = ownerid;
        return this;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public MRetailitem setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
        return this;
    }

    public Long getModifierid() {
        return modifierid;
    }

    public MRetailitem setModifierid(Long modifierid) {
        this.modifierid = modifierid;
        return this;
    }

    public Long getmRetailId() {
        return mRetailId;
    }

    public MRetailitem setmRetailId(Long mRetailId) {
        this.mRetailId = mRetailId;
        return this;
    }

    public Long getOrderno() {
        return orderno;
    }

    public MRetailitem setOrderno(Long orderno) {
        this.orderno = orderno;
        return this;
    }

    public Long getcVipId() {
        return cVipId;
    }

    public MRetailitem setcVipId(Long cVipId) {
        this.cVipId = cVipId;
        return this;
    }

    public Long getSalesrepId() {
        return salesrepId;
    }

    public MRetailitem setSalesrepId(Long salesrepId) {
        this.salesrepId = salesrepId;
        return this;
    }

    public Long getmProductId() {
        return mProductId;
    }

    public MRetailitem setmProductId(Long mProductId) {
        this.mProductId = mProductId;
        return this;
    }

    public Long getmAttributesetinstanceId() {
        return mAttributesetinstanceId;
    }

    public MRetailitem setmAttributesetinstanceId(Long mAttributesetinstanceId) {
        this.mAttributesetinstanceId = mAttributesetinstanceId;
        return this;
    }

    public Long getQty() {
        return qty;
    }

    public MRetailitem setQty(Long qty) {
        this.qty = qty;
        return this;
    }

    public BigDecimal getPricelist() {
        return pricelist;
    }

    public MRetailitem setPricelist(BigDecimal pricelist) {
        this.pricelist = pricelist;
        return this;
    }

    public BigDecimal getPriceactual() {
        return priceactual;
    }

    public MRetailitem setPriceactual(BigDecimal priceactual) {
        this.priceactual = priceactual;
        return this;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public MRetailitem setDiscount(BigDecimal discount) {
        this.discount = discount;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public MRetailitem setDescription(String description) {
        this.description = description;
        return this;
    }

    public BigDecimal getTotAmtList() {
        return totAmtList;
    }

    public MRetailitem setTotAmtList(BigDecimal totAmtList) {
        this.totAmtList = totAmtList;
        return this;
    }

    public BigDecimal getTotAmtActual() {
        return totAmtActual;
    }

    public MRetailitem setTotAmtActual(BigDecimal totAmtActual) {
        this.totAmtActual = totAmtActual;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public MRetailitem setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public MRetailitem setType(Integer type) {
        this.type = type;
        return this;
    }

    public String getOrgdocno() {
        return orgdocno;
    }

    public MRetailitem setOrgdocno(String orgdocno) {
        this.orgdocno = orgdocno;
        return this;
    }

    public Long getMarkbaltype() {
        return markbaltype;
    }

    public MRetailitem setMarkbaltype(Long markbaltype) {
        this.markbaltype = markbaltype;
        return this;
    }

    public Long getmProductaliasId() {
        return mProductaliasId;
    }

    public MRetailitem setmProductaliasId(Long mProductaliasId) {
        this.mProductaliasId = mProductaliasId;
        return this;
    }

    public Long getcMarkbaltypeId() {
        return cMarkbaltypeId;
    }

    public MRetailitem setcMarkbaltypeId(Long cMarkbaltypeId) {
        this.cMarkbaltypeId = cMarkbaltypeId;
        return this;
    }

    public String getOldTrace() {
        return oldTrace;
    }

    public MRetailitem setOldTrace(String oldTrace) {
        this.oldTrace = oldTrace;
        return this;
    }

    public String getOldAuthcode() {
        return oldAuthcode;
    }

    public MRetailitem setOldAuthcode(String oldAuthcode) {
        this.oldAuthcode = oldAuthcode;
        return this;
    }

    public Date getOldDatetime() {
        return oldDatetime;
    }

    public MRetailitem setOldDatetime(Date oldDatetime) {
        this.oldDatetime = oldDatetime;
        return this;
    }

    public Long getOldFlag() {
        return oldFlag;
    }

    public MRetailitem setOldFlag(Long oldFlag) {
        this.oldFlag = oldFlag;
        return this;
    }

    public String getOldRemark() {
        return oldRemark;
    }

    public MRetailitem setOldRemark(String oldRemark) {
        this.oldRemark = oldRemark;
        return this;
    }

    public String getHanddis() {
        return handdis;
    }

    public MRetailitem setHanddis(String handdis) {
        this.handdis = handdis;
        return this;
    }

    public Long getIntegral() {
        return integral;
    }

    public MRetailitem setIntegral(Long integral) {
        this.integral = integral;
        return this;
    }

    public Long getSigTdefposdisId() {
        return sigTdefposdisId;
    }

    public MRetailitem setSigTdefposdisId(Long sigTdefposdisId) {
        this.sigTdefposdisId = sigTdefposdisId;
        return this;
    }

    public Long getComTdefposdisId() {
        return comTdefposdisId;
    }

    public MRetailitem setComTdefposdisId(Long comTdefposdisId) {
        this.comTdefposdisId = comTdefposdisId;
        return this;
    }

    public Long getcIntegralminId() {
        return cIntegralminId;
    }

    public MRetailitem setcIntegralminId(Long cIntegralminId) {
        this.cIntegralminId = cIntegralminId;
        return this;
    }

    public Long getbRetaildisskuId() {
        return bRetaildisskuId;
    }

    public MRetailitem setbRetaildisskuId(Long bRetaildisskuId) {
        this.bRetaildisskuId = bRetaildisskuId;
        return this;
    }

    public Long getOrgMRetailitemId() {
        return orgMRetailitemId;
    }

    public MRetailitem setOrgMRetailitemId(Long orgMRetailitemId) {
        this.orgMRetailitemId = orgMRetailitemId;
        return this;
    }

    public Long getReturnqty() {
        return returnqty;
    }

    public MRetailitem setReturnqty(Long returnqty) {
        this.returnqty = returnqty;
        return this;
    }

    public Long getRqty() {
        return rqty;
    }

    public MRetailitem setRqty(Long rqty) {
        this.rqty = rqty;
        return this;
    }

    public Long getSalesrepId2() {
        return salesrepId2;
    }

    public MRetailitem setSalesrepId2(Long salesrepId2) {
        this.salesrepId2 = salesrepId2;
        return this;
    }

    public Long getSalesrepId3() {
        return salesrepId3;
    }

    public MRetailitem setSalesrepId3(Long salesrepId3) {
        this.salesrepId3 = salesrepId3;
        return this;
    }

    public String getSalesrepsName() {
        return salesrepsName;
    }

    public MRetailitem setSalesrepsName(String salesrepsName) {
        this.salesrepsName = salesrepsName;
        return this;
    }

    public String getSalesrepsId() {
        return salesrepsId;
    }

    public MRetailitem setSalesrepsId(String salesrepsId) {
        this.salesrepsId = salesrepsId;
        return this;
    }

    public Long getcVipbirdisId() {
        return cVipbirdisId;
    }

    public MRetailitem setcVipbirdisId(Long cVipbirdisId) {
        this.cVipbirdisId = cVipbirdisId;
        return this;
    }

    public String getIsIntegral() {
        return isIntegral;
    }

    public MRetailitem setIsIntegral(String isIntegral) {
        this.isIntegral = isIntegral;
        return this;
    }

    public Integer getNcStatus() {
        return ncStatus;
    }

    public MRetailitem setNcStatus(Integer ncStatus) {
        this.ncStatus = ncStatus;
        return this;
    }

    public Date getNcStatustime() {
        return ncStatustime;
    }

    public MRetailitem setNcStatustime(Date ncStatustime) {
        this.ncStatustime = ncStatustime;
        return this;
    }

    public Long getNcStatuserid() {
        return ncStatuserid;
    }

    public MRetailitem setNcStatuserid(Long ncStatuserid) {
        this.ncStatuserid = ncStatuserid;
        return this;
    }

    public Long getmRetailitemId() {
        return mRetailitemId;
    }

    public MRetailitem setmRetailitemId(Long mRetailitemId) {
        this.mRetailitemId = mRetailitemId;
        return this;
    }

    public Long getRcanqty() {
        return rcanqty;
    }

    public MRetailitem setRcanqty(Long rcanqty) {
        this.rcanqty = rcanqty;
        return this;
    }

    public Long getEbSoId() {
        return ebSoId;
    }

    public MRetailitem setEbSoId(Long ebSoId) {
        this.ebSoId = ebSoId;
        return this;
    }

    public Long getWebposRetreasonId() {
        return webposRetreasonId;
    }

    public MRetailitem setWebposRetreasonId(Long webposRetreasonId) {
        this.webposRetreasonId = webposRetreasonId;
        return this;
    }

    public String getSalesrepsRate() {
        return salesrepsRate;
    }

    public MRetailitem setSalesrepsRate(String salesrepsRate) {
        this.salesrepsRate = salesrepsRate;
        return this;
    }

    public Integer getSetStatus() {
        return setStatus;
    }

    public MRetailitem setSetStatus(Integer setStatus) {
        this.setStatus = setStatus;
        return this;
    }

    public Long getSeterid() {
        return seterid;
    }

    public MRetailitem setSeterid(Long seterid) {
        this.seterid = seterid;
        return this;
    }

    public Date getSettime() {
        return settime;
    }

    public MRetailitem setSettime(Date settime) {
        this.settime = settime;
        return this;
    }

    public Long getTbqty() {
        return tbqty;
    }

    public MRetailitem setTbqty(Long tbqty) {
        this.tbqty = tbqty;
        return this;
    }

    public Long getRettbqty() {
        return rettbqty;
    }

    public MRetailitem setRettbqty(Long rettbqty) {
        this.rettbqty = rettbqty;
        return this;
    }

    public Long getRetqty() {
        return retqty;
    }

    public MRetailitem setRetqty(Long retqty) {
        this.retqty = retqty;
        return this;
    }

    public Integer getNcZgstatus() {
        return ncZgstatus;
    }

    public MRetailitem setNcZgstatus(Integer ncZgstatus) {
        this.ncZgstatus = ncZgstatus;
        return this;
    }

    public String getSigTdefposdisMoreid() {
        return sigTdefposdisMoreid;
    }

    public MRetailitem setSigTdefposdisMoreid(String sigTdefposdisMoreid) {
        this.sigTdefposdisMoreid = sigTdefposdisMoreid;
        return this;
    }

    public String getComTdefposdisMoreid() {
        return comTdefposdisMoreid;
    }

    public MRetailitem setComTdefposdisMoreid(String comTdefposdisMoreid) {
        this.comTdefposdisMoreid = comTdefposdisMoreid;
        return this;
    }

    public String getbRetaildisskuMoreid() {
        return bRetaildisskuMoreid;
    }

    public MRetailitem setbRetaildisskuMoreid(String bRetaildisskuMoreid) {
        this.bRetaildisskuMoreid = bRetaildisskuMoreid;
        return this;
    }

    public String getIsExc() {
        return isExc;
    }

    public MRetailitem setIsExc(String isExc) {
        this.isExc = isExc;
        return this;
    }

    public Long getmDim1Id() {
        return mDim1Id;
    }

    public MRetailitem setmDim1Id(Long mDim1Id) {
        this.mDim1Id = mDim1Id;
        return this;
    }

    public String getMastercodePdt() {
        return mastercodePdt;
    }

    public MRetailitem setMastercodePdt(String mastercodePdt) {
        this.mastercodePdt = mastercodePdt;
        return this;
    }

    public Long getActuallyPrice() {
        return actuallyPrice;
    }

    public MRetailitem setActuallyPrice(Long actuallyPrice) {
        this.actuallyPrice = actuallyPrice;
        return this;
    }

    public String getSalesrepName() {
        return salesrepName;
    }

    public MRetailitem setSalesrepName(String salesrepName) {
        this.salesrepName = salesrepName;
        return this;
    }

    public Long getcDisplayAreaId() {
        return cDisplayAreaId;
    }

    public MRetailitem setcDisplayAreaId(Long cDisplayAreaId) {
        this.cDisplayAreaId = cDisplayAreaId;
        return this;
    }

    public String getcDisplayTypeName() {
        return cDisplayTypeName;
    }

    public MRetailitem setcDisplayTypeName(String cDisplayTypeName) {
        this.cDisplayTypeName = cDisplayTypeName;
        return this;
    }

    public String getIsCloud() {
        return isCloud;
    }

    public MRetailitem setIsCloud(String isCloud) {
        this.isCloud = isCloud;
        return this;
    }

    public String getUseintegral() {
        return useintegral;
    }

    public MRetailitem setUseintegral(String useintegral) {
        this.useintegral = useintegral;
        return this;
    }

    public String getIsSwap() {
        return isSwap;
    }

    public MRetailitem setIsSwap(String isSwap) {
        this.isSwap = isSwap;
        return this;
    }

    public Long getcVipbirdisproitemId() {
        return cVipbirdisproitemId;
    }

    public MRetailitem setcVipbirdisproitemId(Long cVipbirdisproitemId) {
        this.cVipbirdisproitemId = cVipbirdisproitemId;
        return this;
    }

    public String getPromotion() {
        return promotion;
    }

    public MRetailitem setPromotion(String promotion) {
        this.promotion = promotion;
        return this;
    }

    public BigDecimal getPriceactualWithouttax() {
        return priceactualWithouttax;
    }

    public MRetailitem setPriceactualWithouttax(BigDecimal priceactualWithouttax) {
        this.priceactualWithouttax = priceactualWithouttax;
        return this;
    }

    public Long getcStoreLocationId() {
        return cStoreLocationId;
    }

    public MRetailitem setcStoreLocationId(Long cStoreLocationId) {
        this.cStoreLocationId = cStoreLocationId;
        return this;
    }

    public BigDecimal getTaxrate() {
        return taxrate;
    }

    public MRetailitem setTaxrate(BigDecimal taxrate) {
        this.taxrate = taxrate;
        return this;
    }

    public String getVouNo() {
        return vouNo;
    }

    public MRetailitem setVouNo(String vouNo) {
        this.vouNo = vouNo;
        return this;
    }

    public Integer getIsMartRead() {
        return isMartRead;
    }

    public MRetailitem setIsMartRead(Integer isMartRead) {
        this.isMartRead = isMartRead;
        return this;
    }

    public Long getIntegralNew() {
        return integralNew;
    }

    public MRetailitem setIntegralNew(Long integralNew) {
        this.integralNew = integralNew;
        return this;
    }

    public Long getNumeb() {
        return numeb;
    }

    public MRetailitem setNumeb(Long numeb) {
        this.numeb = numeb;
        return this;
    }

    public Long getModifyamtReasonId() {
        return modifyamtReasonId;
    }

    public MRetailitem setModifyamtReasonId(Long modifyamtReasonId) {
        this.modifyamtReasonId = modifyamtReasonId;
        return this;
    }

    public BigDecimal getPicoupondis() {
        return picoupondis;
    }

    public MRetailitem setPicoupondis(BigDecimal picoupondis) {
        this.picoupondis = picoupondis;
        return this;
    }

    public String getIsCheck() {
        return isCheck;
    }

    public MRetailitem setIsCheck(String isCheck) {
        this.isCheck = isCheck;
        return this;
    }

    public String getOffPercent() {
        return offPercent;
    }

    public MRetailitem setOffPercent(String offPercent) {
        this.offPercent = offPercent;
        return this;
    }

    public BigDecimal getVirtualprice() {
        return virtualprice;
    }

    public MRetailitem setVirtualprice(BigDecimal virtualprice) {
        this.virtualprice = virtualprice;
        return this;
    }

    public BigDecimal getActualprice() {
        return actualprice;
    }

    public MRetailitem setActualprice(BigDecimal actualprice) {
        this.actualprice = actualprice;
        return this;
    }

    public String getDeliverydate() {
        return deliverydate;
    }

    public MRetailitem setDeliverydate(String deliverydate) {
        this.deliverydate = deliverydate;
        return this;
    }

    public Long getChangepriceUserid() {
        return changepriceUserid;
    }

    public MRetailitem setChangepriceUserid(Long changepriceUserid) {
        this.changepriceUserid = changepriceUserid;
        return this;
    }

    public BigDecimal getCnyTotAmtActual() {
        return cnyTotAmtActual;
    }

    public MRetailitem setCnyTotAmtActual(BigDecimal cnyTotAmtActual) {
        this.cnyTotAmtActual = cnyTotAmtActual;
        return this;
    }

    public String getIsexchange() {
        return isexchange;
    }

    public MRetailitem setIsexchange(String isexchange) {
        this.isexchange = isexchange;
        return this;
    }

    public BigDecimal getFastnoPrice() {
        return fastnoPrice;
    }

    public MRetailitem setFastnoPrice(BigDecimal fastnoPrice) {
        this.fastnoPrice = fastnoPrice;
        return this;
    }

    public BigDecimal getCommon01() {
        return common01;
    }

    public MRetailitem setCommon01(BigDecimal common01) {
        this.common01 = common01;
        return this;
    }

    public String getcRealname() {
        return cRealname;
    }

    public MRetailitem setcRealname(String cRealname) {
        this.cRealname = cRealname;
        return this;
    }

    public BigDecimal getcRealstore() {
        return cRealstore;
    }

    public MRetailitem setcRealstore(BigDecimal cRealstore) {
        this.cRealstore = cRealstore;
        return this;
    }

    public BigDecimal getcInventory() {
        return cInventory;
    }

    public MRetailitem setcInventory(BigDecimal cInventory) {
        this.cInventory = cInventory;
        return this;
    }

    public String getIsExpress() {
        return isExpress;
    }

    public MRetailitem setIsExpress(String isExpress) {
        this.isExpress = isExpress;
        return this;
    }

    public BigDecimal getCommon03() {
        return common03;
    }

    public MRetailitem setCommon03(BigDecimal common03) {
        this.common03 = common03;
        return this;
    }

    public BigDecimal getCommon02() {
        return common02;
    }

    public MRetailitem setCommon02(BigDecimal common02) {
        this.common02 = common02;
        return this;
    }

    public BigDecimal getPricesettle() {
        return pricesettle;
    }

    public MRetailitem setPricesettle(BigDecimal pricesettle) {
        this.pricesettle = pricesettle;
        return this;
    }

    public BigDecimal getTotPassengerAmt() {
        return totPassengerAmt;
    }

    public MRetailitem setTotPassengerAmt(BigDecimal totPassengerAmt) {
        this.totPassengerAmt = totPassengerAmt;
        return this;
    }

    public String getCustomReasonId() {
        return customReasonId;
    }

    public MRetailitem setCustomReasonId(String customReasonId) {
        this.customReasonId = customReasonId;
        return this;
    }

    public Integer getDeliverytype() {
        return deliverytype;
    }

    public MRetailitem setDeliverytype(Integer deliverytype) {
        this.deliverytype = deliverytype;
        return this;
    }

    public String getIsblessing() {
        return isblessing;
    }

    public MRetailitem setIsblessing(String isblessing) {
        this.isblessing = isblessing;
        return this;
    }

    public BigDecimal getUntaxedprice() {
        return untaxedprice;
    }

    public MRetailitem setUntaxedprice(BigDecimal untaxedprice) {
        this.untaxedprice = untaxedprice;
        return this;
    }

    public BigDecimal getTaxAmount1() {
        return taxAmount1;
    }

    public MRetailitem setTaxAmount1(BigDecimal taxAmount1) {
        this.taxAmount1 = taxAmount1;
        return this;
    }

    public BigDecimal getTaxAmount2() {
        return taxAmount2;
    }

    public MRetailitem setTaxAmount2(BigDecimal taxAmount2) {
        this.taxAmount2 = taxAmount2;
        return this;
    }

    public BigDecimal getTaxrate1() {
        return taxrate1;
    }

    public MRetailitem setTaxrate1(BigDecimal taxrate1) {
        this.taxrate1 = taxrate1;
        return this;
    }

    public BigDecimal getTaxrate2() {
        return taxrate2;
    }

    public MRetailitem setTaxrate2(BigDecimal taxrate2) {
        this.taxrate2 = taxrate2;
        return this;
    }

    public BigDecimal getDiscountamt() {
        return discountamt;
    }

    public MRetailitem setDiscountamt(BigDecimal discountamt) {
        this.discountamt = discountamt;
        return this;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public MRetailitem setWeight(BigDecimal weight) {
        this.weight = weight;
        return this;
    }

    public String getUnit() {
        return unit;
    }

    public MRetailitem setUnit(String unit) {
        this.unit = unit;
        return this;
    }

    public Long getIntegralValue() {
        return integralValue;
    }

    public MRetailitem setIntegralValue(Long integralValue) {
        this.integralValue = integralValue;
        return this;
    }

    public String getLineno() {
        return lineno;
    }

    public MRetailitem setLineno(String lineno) {
        this.lineno = lineno;
        return this;
    }

    public Long getSubaccountId() {
        return subaccountId;
    }

    public MRetailitem setSubaccountId(Long subaccountId) {
        this.subaccountId = subaccountId;
        return this;
    }

    public String getEnteringReasonId() {
        return enteringReasonId;
    }

    public MRetailitem setEnteringReasonId(String enteringReasonId) {
        this.enteringReasonId = enteringReasonId;
        return this;
    }

    public BigDecimal getAmountafterdis() {
        return amountafterdis;
    }

    public MRetailitem setAmountafterdis(BigDecimal amountafterdis) {
        this.amountafterdis = amountafterdis;
        return this;
    }

    public BigDecimal getAmountofusedgoods() {
        return amountofusedgoods;
    }

    public MRetailitem setAmountofusedgoods(BigDecimal amountofusedgoods) {
        this.amountofusedgoods = amountofusedgoods;
        return this;
    }

    public String getReservepaycode() {
        return reservepaycode;
    }

    public MRetailitem setReservepaycode(String reservepaycode) {
        this.reservepaycode = reservepaycode;
        return this;
    }

    public String getDeliverytime() {
        return deliverytime;
    }

    public MRetailitem setDeliverytime(String deliverytime) {
        this.deliverytime = deliverytime;
        return this;
    }

    public String getInstallmaintenance() {
        return installmaintenance;
    }

    public MRetailitem setInstallmaintenance(String installmaintenance) {
        this.installmaintenance = installmaintenance;
        return this;
    }

    public String getDoorremarks() {
        return doorremarks;
    }

    public MRetailitem setDoorremarks(String doorremarks) {
        this.doorremarks = doorremarks;
        return this;
    }

    public String getSalecode() {
        return salecode;
    }

    public MRetailitem setSalecode(String salecode) {
        this.salecode = salecode;
        return this;
    }

    public BigDecimal getVouDiscountam() {
        return vouDiscountam;
    }

    public MRetailitem setVouDiscountam(BigDecimal vouDiscountam) {
        this.vouDiscountam = vouDiscountam;
        return this;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public MRetailitem setBatchNo(String batchNo) {
        this.batchNo = batchNo;
        return this;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public MRetailitem setProductionDate(String productionDate) {
        this.productionDate = productionDate;
        return this;
    }

    public String getShelflifeNo() {
        return shelflifeNo;
    }

    public MRetailitem setShelflifeNo(String shelflifeNo) {
        this.shelflifeNo = shelflifeNo;
        return this;
    }

    public String getIsReverseO2o() {
        return isReverseO2o;
    }

    public MRetailitem setIsReverseO2o(String isReverseO2o) {
        this.isReverseO2o = isReverseO2o;
        return this;
    }

    public String getAuthorrealname() {
        return authorrealname;
    }

    public MRetailitem setAuthorrealname(String authorrealname) {
        this.authorrealname = authorrealname;
        return this;
    }

    public String getAuthoremail() {
        return authoremail;
    }

    public MRetailitem setAuthoremail(String authoremail) {
        this.authoremail = authoremail;
        return this;
    }

    public String getIsdropprice() {
        return isdropprice;
    }

    public MRetailitem setIsdropprice(String isdropprice) {
        this.isdropprice = isdropprice;
        return this;
    }

    public String getEancode() {
        return eancode;
    }

    public MRetailitem setEancode(String eancode) {
        this.eancode = eancode;
        return this;
    }

    public String getDmProEname() {
        return dmProEname;
    }

    public MRetailitem setDmProEname(String dmProEname) {
        this.dmProEname = dmProEname;
        return this;
    }

    public String getMallcard() {
        return mallcard;
    }

    public MRetailitem setMallcard(String mallcard) {
        this.mallcard = mallcard;
        return this;
    }

    public String getDiscountamtlist() {
        return discountamtlist;
    }

    public MRetailitem setDiscountamtlist(String discountamtlist) {
        this.discountamtlist = discountamtlist;
        return this;
    }

    public String getIsDelSale() {
        return isDelSale;
    }

    public MRetailitem setIsDelSale(String isDelSale) {
        this.isDelSale = isDelSale;
        return this;
    }

    public String getSkuGroupNo() {
        return skuGroupNo;
    }

    public MRetailitem setSkuGroupNo(String skuGroupNo) {
        this.skuGroupNo = skuGroupNo;
        return this;
    }

    public BigDecimal getMarketRate() {
        return marketRate;
    }

    public MRetailitem setMarketRate(BigDecimal marketRate) {
        this.marketRate = marketRate;
        return this;
    }

    public String getIntscode() {
        return intscode;
    }

    public MRetailitem setIntscode(String intscode) {
        this.intscode = intscode;
        return this;
    }

    public BigDecimal getmRetailDiscountactivity() {
        return mRetailDiscountactivity;
    }

    public MRetailitem setmRetailDiscountactivity(BigDecimal mRetailDiscountactivity) {
        this.mRetailDiscountactivity = mRetailDiscountactivity;
        return this;
    }

    @Override
    public String toString() {
        return "MRetailitemModel{" +
            "id=" + id +
            ", adClientId=" + adClientId +
            ", adOrgId=" + adOrgId +
            ", isactive=" + isactive +
            ", creationdate=" + creationdate +
            ", ownerid=" + ownerid +
            ", modifieddate=" + modifieddate +
            ", modifierid=" + modifierid +
            ", mRetailId=" + mRetailId +
            ", orderno=" + orderno +
            ", cVipId=" + cVipId +
            ", salesrepId=" + salesrepId +
            ", mProductId=" + mProductId +
            ", mAttributesetinstanceId=" + mAttributesetinstanceId +
            ", qty=" + qty +
            ", pricelist=" + pricelist +
            ", priceactual=" + priceactual +
            ", discount=" + discount +
            ", description=" + description +
            ", totAmtList=" + totAmtList +
            ", totAmtActual=" + totAmtActual +
            ", status=" + status +
            ", type=" + type +
            ", orgdocno=" + orgdocno +
            ", markbaltype=" + markbaltype +
            ", mProductaliasId=" + mProductaliasId +
            ", cMarkbaltypeId=" + cMarkbaltypeId +
            ", oldTrace=" + oldTrace +
            ", oldAuthcode=" + oldAuthcode +
            ", oldDatetime=" + oldDatetime +
            ", oldFlag=" + oldFlag +
            ", oldRemark=" + oldRemark +
            ", handdis=" + handdis +
            ", integral=" + integral +
            ", sigTdefposdisId=" + sigTdefposdisId +
            ", comTdefposdisId=" + comTdefposdisId +
            ", cIntegralminId=" + cIntegralminId +
            ", bRetaildisskuId=" + bRetaildisskuId +
            ", orgMRetailitemId=" + orgMRetailitemId +
            ", returnqty=" + returnqty +
            ", rqty=" + rqty +
            ", salesrepId2=" + salesrepId2 +
            ", salesrepId3=" + salesrepId3 +
            ", salesrepsName=" + salesrepsName +
            ", salesrepsId=" + salesrepsId +
            ", cVipbirdisId=" + cVipbirdisId +
            ", isIntegral=" + isIntegral +
            ", ncStatus=" + ncStatus +
            ", ncStatustime=" + ncStatustime +
            ", ncStatuserid=" + ncStatuserid +
            ", mRetailitemId=" + mRetailitemId +
            ", rcanqty=" + rcanqty +
            ", ebSoId=" + ebSoId +
            ", webposRetreasonId=" + webposRetreasonId +
            ", salesrepsRate=" + salesrepsRate +
            ", setStatus=" + setStatus +
            ", seterid=" + seterid +
            ", settime=" + settime +
            ", tbqty=" + tbqty +
            ", rettbqty=" + rettbqty +
            ", retqty=" + retqty +
            ", ncZgstatus=" + ncZgstatus +
            ", sigTdefposdisMoreid=" + sigTdefposdisMoreid +
            ", comTdefposdisMoreid=" + comTdefposdisMoreid +
            ", bRetaildisskuMoreid=" + bRetaildisskuMoreid +
            ", isExc=" + isExc +
            ", mDim1Id=" + mDim1Id +
            ", mastercodePdt=" + mastercodePdt +
            ", actuallyPrice=" + actuallyPrice +
            ", salesrepName=" + salesrepName +
            ", cDisplayAreaId=" + cDisplayAreaId +
            ", cDisplayTypeName=" + cDisplayTypeName +
            ", isCloud=" + isCloud +
            ", useintegral=" + useintegral +
            ", isSwap=" + isSwap +
            ", cVipbirdisproitemId=" + cVipbirdisproitemId +
            ", promotion=" + promotion +
            ", priceactualWithouttax=" + priceactualWithouttax +
            ", cStoreLocationId=" + cStoreLocationId +
            ", taxrate=" + taxrate +
            ", vouNo=" + vouNo +
            ", isMartRead=" + isMartRead +
            ", integralNew=" + integralNew +
            ", numeb=" + numeb +
            ", modifyamtReasonId=" + modifyamtReasonId +
            ", picoupondis=" + picoupondis +
            ", isCheck=" + isCheck +
            ", offPercent=" + offPercent +
            ", virtualprice=" + virtualprice +
            ", actualprice=" + actualprice +
            ", deliverydate=" + deliverydate +
            ", changepriceUserid=" + changepriceUserid +
            ", cnyTotAmtActual=" + cnyTotAmtActual +
            ", isexchange=" + isexchange +
            ", fastnoPrice=" + fastnoPrice +
            ", common01=" + common01 +
            ", cRealname=" + cRealname +
            ", cRealstore=" + cRealstore +
            ", cInventory=" + cInventory +
            ", isExpress=" + isExpress +
            ", common03=" + common03 +
            ", common02=" + common02 +
            ", pricesettle=" + pricesettle +
            ", totPassengerAmt=" + totPassengerAmt +
            ", customReasonId=" + customReasonId +
            ", deliverytype=" + deliverytype +
            ", isblessing=" + isblessing +
            ", untaxedprice=" + untaxedprice +
            ", taxAmount1=" + taxAmount1 +
            ", taxAmount2=" + taxAmount2 +
            ", taxrate1=" + taxrate1 +
            ", taxrate2=" + taxrate2 +
            ", discountamt=" + discountamt +
            ", weight=" + weight +
            ", unit=" + unit +
            ", integralValue=" + integralValue +
            ", lineno=" + lineno +
            ", subaccountId=" + subaccountId +
            ", enteringReasonId=" + enteringReasonId +
            ", amountafterdis=" + amountafterdis +
            ", amountofusedgoods=" + amountofusedgoods +
            ", reservepaycode=" + reservepaycode +
            ", deliverytime=" + deliverytime +
            ", installmaintenance=" + installmaintenance +
            ", doorremarks=" + doorremarks +
            ", salecode=" + salecode +
            ", vouDiscountam=" + vouDiscountam +
            ", batchNo=" + batchNo +
            ", productionDate=" + productionDate +
            ", shelflifeNo=" + shelflifeNo +
            ", isReverseO2o=" + isReverseO2o +
            ", authorrealname=" + authorrealname +
            ", authoremail=" + authoremail +
            ", isdropprice=" + isdropprice +
            ", eancode=" + eancode +
            ", dmProEname=" + dmProEname +
            ", mallcard=" + mallcard +
            ", discountamtlist=" + discountamtlist +
            ", isDelSale=" + isDelSale +
            ", skuGroupNo=" + skuGroupNo +
            ", marketRate=" + marketRate +
            ", intscode=" + intscode +
            ", mRetailDiscountactivity=" + mRetailDiscountactivity +
            "}";
    }
}
