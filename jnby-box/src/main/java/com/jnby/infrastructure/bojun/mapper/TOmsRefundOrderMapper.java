package com.jnby.infrastructure.bojun.mapper;

import com.jnby.infrastructure.bojun.model.TOmsRefundOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @Author: lwz
 * @Date: 2021-12-01 14:17:06
 * @Description: Mapper
 */
public interface TOmsRefundOrderMapper extends BaseMapper<TOmsRefundOrder> {

    /**
     * 根据refundId获取信息
     * @param refundId
     * @return
     */
    List<TOmsRefundOrder> selectByRefundId(@Param("refundId")String refundId);

}
