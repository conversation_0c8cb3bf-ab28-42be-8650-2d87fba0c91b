package com.jnby.infrastructure.bojun.model;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

public class CVipType {

    @ApiModelProperty(value = "此ID为传递的品牌卡ID")
    private Long id;

    private Long adClientId;

    private Long adOrgId;

    private String isactive;

    private Long modifierid;

    private Date creationdate;

    private Date modifieddate;

    private Long ownerid;

    private String name;

    private String description;

    private BigDecimal discount;

    private BigDecimal integralrate;

    private BigDecimal rediscount;

    private String canupgrade;

    private Long cViptypeupId;

    private Long needintg;

    private Long checkoffintg;

    private Integer defaultvalid;

    private String dbintday;

    private BigDecimal dbintdnum;

    private String dbintmon;

    private BigDecimal dbintmnum;

    private String code;

    private Long intlOnceUp;

    private Long intlHalfyearUp;

    private Long intlYearUp;

    private Long intlOnceLst;

    private Long intlHalfyearLst;

    private Long intlYearLst;

    private Long intlGreaterOff;

    private Long intlSmallerOff;

    private Long intlTwoyearUp;

    private Long intlTwoyearLst;

    private Long intlTygreaterOff;

    private Long intlTysmallerOff;

    private Integer defaultvalidIntg;

    private Long intlOnceUp2;

    private Long intlYearUp2;

    private Long intlTwoyearUp2;

    private Long intlGreaterOff2;

    private Long intlSmallerOff2;

    private Long intlTygreaterOff2;

    private Long intlTysmallerOff2;

    private Integer defaultvalidUpintg;

    private BigDecimal brithdisatm;

    private BigDecimal brithdaydis;

    private Long isshareBirthday;

    private Long validdailyIntg;

    private Long validdailyUpintg;

    private Long validDaily;

    private Long xkvaliddailyUpintg;

    private Long xkvaliddailyIntg;

    private Long xkvalidDaily;

    private Long integralUpdeal;

    private String integralDeal;

    private String ifcharge;

    private String isLimitminintl;

    private Long deValiddailyOffintg;

    private Long deValiddailyIntg;

    private Long deValiddailyOff;

    private String isPasswordCheck;

    private Long brithdayqty;

    private Long mon1;

    private Long intlMon1Up;

    private Long mon2;

    private Long intlMon2Up;

    private String isLimitmin;

    private String wechatfans;

    private String isAutoactive;

    private String isSmscode;

    private String isscan;

    private BigDecimal discountLimit;

    private Long cVipDalei;

    private Long cConsumeareaId;

    private String subaccountDefaultType;

    private String isListLimit;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAdClientId() {
        return adClientId;
    }

    public void setAdClientId(Long adClientId) {
        this.adClientId = adClientId;
    }

    public Long getAdOrgId() {
        return adOrgId;
    }

    public void setAdOrgId(Long adOrgId) {
        this.adOrgId = adOrgId;
    }

    public String getIsactive() {
        return isactive;
    }

    public void setIsactive(String isactive) {
        this.isactive = isactive == null ? null : isactive.trim();
    }

    public Long getModifierid() {
        return modifierid;
    }

    public void setModifierid(Long modifierid) {
        this.modifierid = modifierid;
    }

    public Date getCreationdate() {
        return creationdate;
    }

    public void setCreationdate(Date creationdate) {
        this.creationdate = creationdate;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }

    public Long getOwnerid() {
        return ownerid;
    }

    public void setOwnerid(Long ownerid) {
        this.ownerid = ownerid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public BigDecimal getIntegralrate() {
        return integralrate;
    }

    public void setIntegralrate(BigDecimal integralrate) {
        this.integralrate = integralrate;
    }

    public BigDecimal getRediscount() {
        return rediscount;
    }

    public void setRediscount(BigDecimal rediscount) {
        this.rediscount = rediscount;
    }

    public String getCanupgrade() {
        return canupgrade;
    }

    public void setCanupgrade(String canupgrade) {
        this.canupgrade = canupgrade == null ? null : canupgrade.trim();
    }

    public Long getcViptypeupId() {
        return cViptypeupId;
    }

    public void setcViptypeupId(Long cViptypeupId) {
        this.cViptypeupId = cViptypeupId;
    }

    public Long getNeedintg() {
        return needintg;
    }

    public void setNeedintg(Long needintg) {
        this.needintg = needintg;
    }

    public Long getCheckoffintg() {
        return checkoffintg;
    }

    public void setCheckoffintg(Long checkoffintg) {
        this.checkoffintg = checkoffintg;
    }

    public Integer getDefaultvalid() {
        return defaultvalid;
    }

    public void setDefaultvalid(Integer defaultvalid) {
        this.defaultvalid = defaultvalid;
    }

    public String getDbintday() {
        return dbintday;
    }

    public void setDbintday(String dbintday) {
        this.dbintday = dbintday == null ? null : dbintday.trim();
    }

    public BigDecimal getDbintdnum() {
        return dbintdnum;
    }

    public void setDbintdnum(BigDecimal dbintdnum) {
        this.dbintdnum = dbintdnum;
    }

    public String getDbintmon() {
        return dbintmon;
    }

    public void setDbintmon(String dbintmon) {
        this.dbintmon = dbintmon == null ? null : dbintmon.trim();
    }

    public BigDecimal getDbintmnum() {
        return dbintmnum;
    }

    public void setDbintmnum(BigDecimal dbintmnum) {
        this.dbintmnum = dbintmnum;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public Long getIntlOnceUp() {
        return intlOnceUp;
    }

    public void setIntlOnceUp(Long intlOnceUp) {
        this.intlOnceUp = intlOnceUp;
    }

    public Long getIntlHalfyearUp() {
        return intlHalfyearUp;
    }

    public void setIntlHalfyearUp(Long intlHalfyearUp) {
        this.intlHalfyearUp = intlHalfyearUp;
    }

    public Long getIntlYearUp() {
        return intlYearUp;
    }

    public void setIntlYearUp(Long intlYearUp) {
        this.intlYearUp = intlYearUp;
    }

    public Long getIntlOnceLst() {
        return intlOnceLst;
    }

    public void setIntlOnceLst(Long intlOnceLst) {
        this.intlOnceLst = intlOnceLst;
    }

    public Long getIntlHalfyearLst() {
        return intlHalfyearLst;
    }

    public void setIntlHalfyearLst(Long intlHalfyearLst) {
        this.intlHalfyearLst = intlHalfyearLst;
    }

    public Long getIntlYearLst() {
        return intlYearLst;
    }

    public void setIntlYearLst(Long intlYearLst) {
        this.intlYearLst = intlYearLst;
    }

    public Long getIntlGreaterOff() {
        return intlGreaterOff;
    }

    public void setIntlGreaterOff(Long intlGreaterOff) {
        this.intlGreaterOff = intlGreaterOff;
    }

    public Long getIntlSmallerOff() {
        return intlSmallerOff;
    }

    public void setIntlSmallerOff(Long intlSmallerOff) {
        this.intlSmallerOff = intlSmallerOff;
    }

    public Long getIntlTwoyearUp() {
        return intlTwoyearUp;
    }

    public void setIntlTwoyearUp(Long intlTwoyearUp) {
        this.intlTwoyearUp = intlTwoyearUp;
    }

    public Long getIntlTwoyearLst() {
        return intlTwoyearLst;
    }

    public void setIntlTwoyearLst(Long intlTwoyearLst) {
        this.intlTwoyearLst = intlTwoyearLst;
    }

    public Long getIntlTygreaterOff() {
        return intlTygreaterOff;
    }

    public void setIntlTygreaterOff(Long intlTygreaterOff) {
        this.intlTygreaterOff = intlTygreaterOff;
    }

    public Long getIntlTysmallerOff() {
        return intlTysmallerOff;
    }

    public void setIntlTysmallerOff(Long intlTysmallerOff) {
        this.intlTysmallerOff = intlTysmallerOff;
    }

    public Integer getDefaultvalidIntg() {
        return defaultvalidIntg;
    }

    public void setDefaultvalidIntg(Integer defaultvalidIntg) {
        this.defaultvalidIntg = defaultvalidIntg;
    }

    public Long getIntlOnceUp2() {
        return intlOnceUp2;
    }

    public void setIntlOnceUp2(Long intlOnceUp2) {
        this.intlOnceUp2 = intlOnceUp2;
    }

    public Long getIntlYearUp2() {
        return intlYearUp2;
    }

    public void setIntlYearUp2(Long intlYearUp2) {
        this.intlYearUp2 = intlYearUp2;
    }

    public Long getIntlTwoyearUp2() {
        return intlTwoyearUp2;
    }

    public void setIntlTwoyearUp2(Long intlTwoyearUp2) {
        this.intlTwoyearUp2 = intlTwoyearUp2;
    }

    public Long getIntlGreaterOff2() {
        return intlGreaterOff2;
    }

    public void setIntlGreaterOff2(Long intlGreaterOff2) {
        this.intlGreaterOff2 = intlGreaterOff2;
    }

    public Long getIntlSmallerOff2() {
        return intlSmallerOff2;
    }

    public void setIntlSmallerOff2(Long intlSmallerOff2) {
        this.intlSmallerOff2 = intlSmallerOff2;
    }

    public Long getIntlTygreaterOff2() {
        return intlTygreaterOff2;
    }

    public void setIntlTygreaterOff2(Long intlTygreaterOff2) {
        this.intlTygreaterOff2 = intlTygreaterOff2;
    }

    public Long getIntlTysmallerOff2() {
        return intlTysmallerOff2;
    }

    public void setIntlTysmallerOff2(Long intlTysmallerOff2) {
        this.intlTysmallerOff2 = intlTysmallerOff2;
    }

    public Integer getDefaultvalidUpintg() {
        return defaultvalidUpintg;
    }

    public void setDefaultvalidUpintg(Integer defaultvalidUpintg) {
        this.defaultvalidUpintg = defaultvalidUpintg;
    }

    public BigDecimal getBrithdisatm() {
        return brithdisatm;
    }

    public void setBrithdisatm(BigDecimal brithdisatm) {
        this.brithdisatm = brithdisatm;
    }

    public BigDecimal getBrithdaydis() {
        return brithdaydis;
    }

    public void setBrithdaydis(BigDecimal brithdaydis) {
        this.brithdaydis = brithdaydis;
    }

    public Long getIsshareBirthday() {
        return isshareBirthday;
    }

    public void setIsshareBirthday(Long isshareBirthday) {
        this.isshareBirthday = isshareBirthday;
    }

    public Long getValiddailyIntg() {
        return validdailyIntg;
    }

    public void setValiddailyIntg(Long validdailyIntg) {
        this.validdailyIntg = validdailyIntg;
    }

    public Long getValiddailyUpintg() {
        return validdailyUpintg;
    }

    public void setValiddailyUpintg(Long validdailyUpintg) {
        this.validdailyUpintg = validdailyUpintg;
    }

    public Long getValidDaily() {
        return validDaily;
    }

    public void setValidDaily(Long validDaily) {
        this.validDaily = validDaily;
    }

    public Long getXkvaliddailyUpintg() {
        return xkvaliddailyUpintg;
    }

    public void setXkvaliddailyUpintg(Long xkvaliddailyUpintg) {
        this.xkvaliddailyUpintg = xkvaliddailyUpintg;
    }

    public Long getXkvaliddailyIntg() {
        return xkvaliddailyIntg;
    }

    public void setXkvaliddailyIntg(Long xkvaliddailyIntg) {
        this.xkvaliddailyIntg = xkvaliddailyIntg;
    }

    public Long getXkvalidDaily() {
        return xkvalidDaily;
    }

    public void setXkvalidDaily(Long xkvalidDaily) {
        this.xkvalidDaily = xkvalidDaily;
    }


    public String getIntegralDeal() {
        return integralDeal;
    }

    public void setIntegralDeal(String integralDeal) {
        this.integralDeal = integralDeal == null ? null : integralDeal.trim();
    }

    public String getIfcharge() {
        return ifcharge;
    }

    public void setIfcharge(String ifcharge) {
        this.ifcharge = ifcharge == null ? null : ifcharge.trim();
    }

    public String getIsLimitminintl() {
        return isLimitminintl;
    }

    public void setIsLimitminintl(String isLimitminintl) {
        this.isLimitminintl = isLimitminintl == null ? null : isLimitminintl.trim();
    }

    public Long getDeValiddailyOffintg() {
        return deValiddailyOffintg;
    }

    public void setDeValiddailyOffintg(Long deValiddailyOffintg) {
        this.deValiddailyOffintg = deValiddailyOffintg;
    }

    public Long getDeValiddailyIntg() {
        return deValiddailyIntg;
    }

    public void setDeValiddailyIntg(Long deValiddailyIntg) {
        this.deValiddailyIntg = deValiddailyIntg;
    }

    public Long getDeValiddailyOff() {
        return deValiddailyOff;
    }

    public void setDeValiddailyOff(Long deValiddailyOff) {
        this.deValiddailyOff = deValiddailyOff;
    }

    public String getIsPasswordCheck() {
        return isPasswordCheck;
    }

    public void setIsPasswordCheck(String isPasswordCheck) {
        this.isPasswordCheck = isPasswordCheck == null ? null : isPasswordCheck.trim();
    }

    public Long getBrithdayqty() {
        return brithdayqty;
    }

    public void setBrithdayqty(Long brithdayqty) {
        this.brithdayqty = brithdayqty;
    }

    public Long getMon1() {
        return mon1;
    }

    public void setMon1(Long mon1) {
        this.mon1 = mon1;
    }

    public Long getIntlMon1Up() {
        return intlMon1Up;
    }

    public void setIntlMon1Up(Long intlMon1Up) {
        this.intlMon1Up = intlMon1Up;
    }

    public Long getMon2() {
        return mon2;
    }

    public void setMon2(Long mon2) {
        this.mon2 = mon2;
    }

    public Long getIntlMon2Up() {
        return intlMon2Up;
    }

    public void setIntlMon2Up(Long intlMon2Up) {
        this.intlMon2Up = intlMon2Up;
    }

    public String getIsLimitmin() {
        return isLimitmin;
    }

    public void setIsLimitmin(String isLimitmin) {
        this.isLimitmin = isLimitmin == null ? null : isLimitmin.trim();
    }

    public String getWechatfans() {
        return wechatfans;
    }

    public void setWechatfans(String wechatfans) {
        this.wechatfans = wechatfans == null ? null : wechatfans.trim();
    }

    public String getIsAutoactive() {
        return isAutoactive;
    }

    public void setIsAutoactive(String isAutoactive) {
        this.isAutoactive = isAutoactive == null ? null : isAutoactive.trim();
    }

    public String getIsSmscode() {
        return isSmscode;
    }

    public void setIsSmscode(String isSmscode) {
        this.isSmscode = isSmscode == null ? null : isSmscode.trim();
    }

    public String getIsscan() {
        return isscan;
    }

    public void setIsscan(String isscan) {
        this.isscan = isscan == null ? null : isscan.trim();
    }

    public BigDecimal getDiscountLimit() {
        return discountLimit;
    }

    public void setDiscountLimit(BigDecimal discountLimit) {
        this.discountLimit = discountLimit;
    }

    public Long getIntegralUpdeal() {
        return integralUpdeal;
    }

    public void setIntegralUpdeal(Long integralUpdeal) {
        this.integralUpdeal = integralUpdeal;
    }

    public Long getcVipDalei() {
        return cVipDalei;
    }

    public void setcVipDalei(Long cVipDalei) {
        this.cVipDalei = cVipDalei;
    }

    public Long getcConsumeareaId() {
        return cConsumeareaId;
    }

    public void setcConsumeareaId(Long cConsumeareaId) {
        this.cConsumeareaId = cConsumeareaId;
    }

    public String getSubaccountDefaultType() {
        return subaccountDefaultType;
    }

    public void setSubaccountDefaultType(String subaccountDefaultType) {
        this.subaccountDefaultType = subaccountDefaultType == null ? null : subaccountDefaultType.trim();
    }

    public String getIsListLimit() {
        return isListLimit;
    }

    public void setIsListLimit(String isListLimit) {
        this.isListLimit = isListLimit == null ? null : isListLimit.trim();
    }
}
