package com.jnby.infrastructure.bojun.model;

import java.math.BigDecimal;
import java.util.Date;

public class Vouchers {
    private Long id;

    private Long adClientId;

    private Long adOrgId;

    private String vouchersNo;

    private BigDecimal accountLimit;

    private String isAllstore;

    private String isListLimit;

    private String isAccpayAfterdis;

    private String isDelaccount;

    private String isOnetime;

    private BigDecimal vouDis;

    private String isVerifyed;

    private BigDecimal amtAcount;

    private Long vouCycle;

    private Long validDate;

    private String vouType;

    private String isValid;

    private Long ownerid;

    private Long modifierid;

    private Date creationdate;

    private Date modifieddate = new Date();

    private String isactive;

    private BigDecimal accountLimitDue;

    private Long qtyLimit;

    private Long hrEmployeeId;

    private Long verifyedStoreId;

    private Long verifyedUserId;

    private Long cCustomerId;

    private Long cDepartmentId;

    private String department;

    private Date verifyedTime;

    private String isshareVouchers;

    private String issharePaytype;

    private String isshareDoutype;

    private String isshowDiscount;

    private String isshowFeelsale;

    private String storename;

    private String identifyCode;

    private String empNo;

    private String headvc1;

    private String headvc2;

    private String headvc3;

    private String isExceptdis;

    private Short headvc4;

    private String headvc5;

    private String headvc6;

    private String headvc7;

    private String headvc8;

    private Long cVipId;

    private BigDecimal amtDiscount;

    private String isNomoreqty;

    private Long mDim1Id;

    private String isEntire;

    private Long amtNobef;

    private Long amtNoles;

    private BigDecimal disNoles;

    private BigDecimal disNoless;

    private Long startdate;

    private Long ruleid;

    private String isMdamt;

    private BigDecimal amtLimit;

    private String isUseamt;

    private Short vouamt;

    private Short source;

    private String isWx;

    private String activityname;

    private String mRetailDocno;

    private String isshowVoudiscount;

    private String deltype;

    private Long startDate;

    private Long mDim7Id;

    private Long mDim17Id;

    private String vouName;

    private String platform;

    private String details;

    private Long cCVipId;

    private Long cPaywayId;

    private String accountLimitoverlay;

    private String isListVipdis;

    private String couponsWay;

    private String isVipdisbeforevou;

    private String batchno;

    private String voudiscount;

    private Long qtyLimitMin;

    private String whetherSingle;

    private String applicationStoreType;

    private String applicationProductType;

    private String vouIsclaim;

    private BigDecimal vouConsumeclaim;

    private Long vouDoublenum;

    private String thresholdIsactive;

    private String isReachall;

    private BigDecimal threshold;

    private String dollarbond;

    private String isNeedlock;

    private String lockStatus;

    private String isSharejf;

    private String isSuperposition;

    private Long proMaxQty;

    private String vouchersAndvipdis;

    private String islimitOlddiscount;

    private Long verifyProductQty;

    public Long getVerifyProductQty() {
        return verifyProductQty;
    }

    public void setVerifyProductQty(Long verifyProductQty) {
        this.verifyProductQty = verifyProductQty;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAdClientId() {
        return adClientId;
    }

    public void setAdClientId(Long adClientId) {
        this.adClientId = adClientId;
    }

    public Long getAdOrgId() {
        return adOrgId;
    }

    public void setAdOrgId(Long adOrgId) {
        this.adOrgId = adOrgId;
    }

    public String getVouchersNo() {
        return vouchersNo;
    }

    public void setVouchersNo(String vouchersNo) {
        this.vouchersNo = vouchersNo == null ? null : vouchersNo.trim();
    }

    public BigDecimal getAccountLimit() {
        return accountLimit;
    }

    public void setAccountLimit(BigDecimal accountLimit) {
        this.accountLimit = accountLimit;
    }

    public String getIsAllstore() {
        return isAllstore;
    }

    public void setIsAllstore(String isAllstore) {
        this.isAllstore = isAllstore == null ? null : isAllstore.trim();
    }

    public String getIsListLimit() {
        return isListLimit;
    }

    public void setIsListLimit(String isListLimit) {
        this.isListLimit = isListLimit == null ? null : isListLimit.trim();
    }

    public String getIsAccpayAfterdis() {
        return isAccpayAfterdis;
    }

    public void setIsAccpayAfterdis(String isAccpayAfterdis) {
        this.isAccpayAfterdis = isAccpayAfterdis == null ? null : isAccpayAfterdis.trim();
    }

    public String getIsDelaccount() {
        return isDelaccount;
    }

    public void setIsDelaccount(String isDelaccount) {
        this.isDelaccount = isDelaccount == null ? null : isDelaccount.trim();
    }

    public String getIsOnetime() {
        return isOnetime;
    }

    public void setIsOnetime(String isOnetime) {
        this.isOnetime = isOnetime == null ? null : isOnetime.trim();
    }

    public BigDecimal getVouDis() {
        return vouDis;
    }

    public void setVouDis(BigDecimal vouDis) {
        this.vouDis = vouDis;
    }

    public String getIsVerifyed() {
        return isVerifyed;
    }

    public void setIsVerifyed(String isVerifyed) {
        this.isVerifyed = isVerifyed == null ? null : isVerifyed.trim();
    }

    public BigDecimal getAmtAcount() {
        return amtAcount;
    }

    public void setAmtAcount(BigDecimal amtAcount) {
        this.amtAcount = amtAcount;
    }

    public Long getVouCycle() {
        return vouCycle;
    }

    public void setVouCycle(Long vouCycle) {
        this.vouCycle = vouCycle;
    }

    public Long getValidDate() {
        return validDate;
    }

    public void setValidDate(Long validDate) {
        this.validDate = validDate;
    }

    public String getVouType() {
        return vouType;
    }

    public void setVouType(String vouType) {
        this.vouType = vouType == null ? null : vouType.trim();
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid == null ? null : isValid.trim();
    }

    public Long getOwnerid() {
        return ownerid;
    }

    public void setOwnerid(Long ownerid) {
        this.ownerid = ownerid;
    }

    public Long getModifierid() {
        return modifierid;
    }

    public void setModifierid(Long modifierid) {
        this.modifierid = modifierid;
    }

    public Date getCreationdate() {
        return creationdate;
    }

    public void setCreationdate(Date creationdate) {
        this.creationdate = creationdate;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }

    public String getIsactive() {
        return isactive;
    }

    public void setIsactive(String isactive) {
        this.isactive = isactive == null ? null : isactive.trim();
    }

    public BigDecimal getAccountLimitDue() {
        return accountLimitDue;
    }

    public void setAccountLimitDue(BigDecimal accountLimitDue) {
        this.accountLimitDue = accountLimitDue;
    }

    public Long getQtyLimit() {
        return qtyLimit;
    }

    public void setQtyLimit(Long qtyLimit) {
        this.qtyLimit = qtyLimit;
    }

    public Long getHrEmployeeId() {
        return hrEmployeeId;
    }

    public void setHrEmployeeId(Long hrEmployeeId) {
        this.hrEmployeeId = hrEmployeeId;
    }

    public Long getVerifyedStoreId() {
        return verifyedStoreId;
    }

    public void setVerifyedStoreId(Long verifyedStoreId) {
        this.verifyedStoreId = verifyedStoreId;
    }

    public Long getVerifyedUserId() {
        return verifyedUserId;
    }

    public void setVerifyedUserId(Long verifyedUserId) {
        this.verifyedUserId = verifyedUserId;
    }

    public Long getcCustomerId() {
        return cCustomerId;
    }

    public void setcCustomerId(Long cCustomerId) {
        this.cCustomerId = cCustomerId;
    }

    public Long getcDepartmentId() {
        return cDepartmentId;
    }

    public void setcDepartmentId(Long cDepartmentId) {
        this.cDepartmentId = cDepartmentId;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department == null ? null : department.trim();
    }

    public Date getVerifyedTime() {
        return verifyedTime;
    }

    public void setVerifyedTime(Date verifyedTime) {
        this.verifyedTime = verifyedTime;
    }

    public String getIsshareVouchers() {
        return isshareVouchers;
    }

    public void setIsshareVouchers(String isshareVouchers) {
        this.isshareVouchers = isshareVouchers == null ? null : isshareVouchers.trim();
    }

    public String getIssharePaytype() {
        return issharePaytype;
    }

    public void setIssharePaytype(String issharePaytype) {
        this.issharePaytype = issharePaytype == null ? null : issharePaytype.trim();
    }

    public String getIsshareDoutype() {
        return isshareDoutype;
    }

    public void setIsshareDoutype(String isshareDoutype) {
        this.isshareDoutype = isshareDoutype == null ? null : isshareDoutype.trim();
    }

    public String getIsshowDiscount() {
        return isshowDiscount;
    }

    public void setIsshowDiscount(String isshowDiscount) {
        this.isshowDiscount = isshowDiscount == null ? null : isshowDiscount.trim();
    }

    public String getIsshowFeelsale() {
        return isshowFeelsale;
    }

    public void setIsshowFeelsale(String isshowFeelsale) {
        this.isshowFeelsale = isshowFeelsale == null ? null : isshowFeelsale.trim();
    }

    public String getStorename() {
        return storename;
    }

    public void setStorename(String storename) {
        this.storename = storename == null ? null : storename.trim();
    }

    public String getIdentifyCode() {
        return identifyCode;
    }

    public void setIdentifyCode(String identifyCode) {
        this.identifyCode = identifyCode == null ? null : identifyCode.trim();
    }

    public String getEmpNo() {
        return empNo;
    }

    public void setEmpNo(String empNo) {
        this.empNo = empNo == null ? null : empNo.trim();
    }

    public String getHeadvc1() {
        return headvc1;
    }

    public void setHeadvc1(String headvc1) {
        this.headvc1 = headvc1 == null ? null : headvc1.trim();
    }

    public String getHeadvc2() {
        return headvc2;
    }

    public void setHeadvc2(String headvc2) {
        this.headvc2 = headvc2 == null ? null : headvc2.trim();
    }

    public String getHeadvc3() {
        return headvc3;
    }

    public void setHeadvc3(String headvc3) {
        this.headvc3 = headvc3 == null ? null : headvc3.trim();
    }

    public String getIsExceptdis() {
        return isExceptdis;
    }

    public void setIsExceptdis(String isExceptdis) {
        this.isExceptdis = isExceptdis == null ? null : isExceptdis.trim();
    }

    public Short getHeadvc4() {
        return headvc4;
    }

    public void setHeadvc4(Short headvc4) {
        this.headvc4 = headvc4;
    }

    public String getHeadvc5() {
        return headvc5;
    }

    public void setHeadvc5(String headvc5) {
        this.headvc5 = headvc5 == null ? null : headvc5.trim();
    }

    public String getHeadvc6() {
        return headvc6;
    }

    public void setHeadvc6(String headvc6) {
        this.headvc6 = headvc6 == null ? null : headvc6.trim();
    }

    public String getHeadvc7() {
        return headvc7;
    }

    public void setHeadvc7(String headvc7) {
        this.headvc7 = headvc7 == null ? null : headvc7.trim();
    }

    public String getHeadvc8() {
        return headvc8;
    }

    public void setHeadvc8(String headvc8) {
        this.headvc8 = headvc8 == null ? null : headvc8.trim();
    }

    public Long getcVipId() {
        return cVipId;
    }

    public void setcVipId(Long cVipId) {
        this.cVipId = cVipId;
    }

    public BigDecimal getAmtDiscount() {
        return amtDiscount;
    }

    public void setAmtDiscount(BigDecimal amtDiscount) {
        this.amtDiscount = amtDiscount;
    }

    public String getIsNomoreqty() {
        return isNomoreqty;
    }

    public void setIsNomoreqty(String isNomoreqty) {
        this.isNomoreqty = isNomoreqty == null ? null : isNomoreqty.trim();
    }

    public Long getmDim1Id() {
        return mDim1Id;
    }

    public void setmDim1Id(Long mDim1Id) {
        this.mDim1Id = mDim1Id;
    }

    public String getIsEntire() {
        return isEntire;
    }

    public void setIsEntire(String isEntire) {
        this.isEntire = isEntire == null ? null : isEntire.trim();
    }

    public Long getAmtNobef() {
        return amtNobef;
    }

    public void setAmtNobef(Long amtNobef) {
        this.amtNobef = amtNobef;
    }

    public Long getAmtNoles() {
        return amtNoles;
    }

    public void setAmtNoles(Long amtNoles) {
        this.amtNoles = amtNoles;
    }

    public BigDecimal getDisNoles() {
        return disNoles;
    }

    public void setDisNoles(BigDecimal disNoles) {
        this.disNoles = disNoles;
    }

    public BigDecimal getDisNoless() {
        return disNoless;
    }

    public void setDisNoless(BigDecimal disNoless) {
        this.disNoless = disNoless;
    }

    public Long getStartdate() {
        return startdate;
    }

    public void setStartdate(Long startdate) {
        this.startdate = startdate;
    }

    public Long getRuleid() {
        return ruleid;
    }

    public void setRuleid(Long ruleid) {
        this.ruleid = ruleid;
    }

    public String getIsMdamt() {
        return isMdamt;
    }

    public void setIsMdamt(String isMdamt) {
        this.isMdamt = isMdamt == null ? null : isMdamt.trim();
    }

    public BigDecimal getAmtLimit() {
        return amtLimit;
    }

    public void setAmtLimit(BigDecimal amtLimit) {
        this.amtLimit = amtLimit;
    }

    public String getIsUseamt() {
        return isUseamt;
    }

    public void setIsUseamt(String isUseamt) {
        this.isUseamt = isUseamt == null ? null : isUseamt.trim();
    }

    public Short getVouamt() {
        return vouamt;
    }

    public void setVouamt(Short vouamt) {
        this.vouamt = vouamt;
    }

    public Short getSource() {
        return source;
    }

    public void setSource(Short source) {
        this.source = source;
    }

    public String getIsWx() {
        return isWx;
    }

    public void setIsWx(String isWx) {
        this.isWx = isWx == null ? null : isWx.trim();
    }

    public String getActivityname() {
        return activityname;
    }

    public void setActivityname(String activityname) {
        this.activityname = activityname == null ? null : activityname.trim();
    }

    public String getmRetailDocno() {
        return mRetailDocno;
    }

    public void setmRetailDocno(String mRetailDocno) {
        this.mRetailDocno = mRetailDocno == null ? null : mRetailDocno.trim();
    }

    public String getIsshowVoudiscount() {
        return isshowVoudiscount;
    }

    public void setIsshowVoudiscount(String isshowVoudiscount) {
        this.isshowVoudiscount = isshowVoudiscount == null ? null : isshowVoudiscount.trim();
    }

    public String getDeltype() {
        return deltype;
    }

    public void setDeltype(String deltype) {
        this.deltype = deltype == null ? null : deltype.trim();
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getmDim7Id() {
        return mDim7Id;
    }

    public void setmDim7Id(Long mDim7Id) {
        this.mDim7Id = mDim7Id;
    }

    public Long getmDim17Id() {
        return mDim17Id;
    }

    public void setmDim17Id(Long mDim17Id) {
        this.mDim17Id = mDim17Id;
    }

    public String getVouName() {
        return vouName;
    }

    public void setVouName(String vouName) {
        this.vouName = vouName == null ? null : vouName.trim();
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform == null ? null : platform.trim();
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details == null ? null : details.trim();
    }

    public Long getcCVipId() {
        return cCVipId;
    }

    public void setcCVipId(Long cCVipId) {
        this.cCVipId = cCVipId;
    }

    public Long getcPaywayId() {
        return cPaywayId;
    }

    public void setcPaywayId(Long cPaywayId) {
        this.cPaywayId = cPaywayId;
    }

    public String getAccountLimitoverlay() {
        return accountLimitoverlay;
    }

    public void setAccountLimitoverlay(String accountLimitoverlay) {
        this.accountLimitoverlay = accountLimitoverlay == null ? null : accountLimitoverlay.trim();
    }

    public String getIsListVipdis() {
        return isListVipdis;
    }

    public void setIsListVipdis(String isListVipdis) {
        this.isListVipdis = isListVipdis == null ? null : isListVipdis.trim();
    }

    public String getCouponsWay() {
        return couponsWay;
    }

    public void setCouponsWay(String couponsWay) {
        this.couponsWay = couponsWay == null ? null : couponsWay.trim();
    }

    public String getIsVipdisbeforevou() {
        return isVipdisbeforevou;
    }

    public void setIsVipdisbeforevou(String isVipdisbeforevou) {
        this.isVipdisbeforevou = isVipdisbeforevou == null ? null : isVipdisbeforevou.trim();
    }

    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno == null ? null : batchno.trim();
    }

    public String getVoudiscount() {
        return voudiscount;
    }

    public void setVoudiscount(String voudiscount) {
        this.voudiscount = voudiscount == null ? null : voudiscount.trim();
    }

    public Long getQtyLimitMin() {
        return qtyLimitMin;
    }

    public void setQtyLimitMin(Long qtyLimitMin) {
        this.qtyLimitMin = qtyLimitMin;
    }

    public String getWhetherSingle() {
        return whetherSingle;
    }

    public void setWhetherSingle(String whetherSingle) {
        this.whetherSingle = whetherSingle == null ? null : whetherSingle.trim();
    }

    public String getApplicationStoreType() {
        return applicationStoreType;
    }

    public void setApplicationStoreType(String applicationStoreType) {
        this.applicationStoreType = applicationStoreType == null ? null : applicationStoreType.trim();
    }

    public String getApplicationProductType() {
        return applicationProductType;
    }

    public void setApplicationProductType(String applicationProductType) {
        this.applicationProductType = applicationProductType == null ? null : applicationProductType.trim();
    }

    public String getVouIsclaim() {
        return vouIsclaim;
    }

    public void setVouIsclaim(String vouIsclaim) {
        this.vouIsclaim = vouIsclaim == null ? null : vouIsclaim.trim();
    }

    public BigDecimal getVouConsumeclaim() {
        return vouConsumeclaim;
    }

    public void setVouConsumeclaim(BigDecimal vouConsumeclaim) {
        this.vouConsumeclaim = vouConsumeclaim;
    }

    public Long getVouDoublenum() {
        return vouDoublenum;
    }

    public void setVouDoublenum(Long vouDoublenum) {
        this.vouDoublenum = vouDoublenum;
    }

    public String getThresholdIsactive() {
        return thresholdIsactive;
    }

    public void setThresholdIsactive(String thresholdIsactive) {
        this.thresholdIsactive = thresholdIsactive == null ? null : thresholdIsactive.trim();
    }

    public String getIsReachall() {
        return isReachall;
    }

    public void setIsReachall(String isReachall) {
        this.isReachall = isReachall == null ? null : isReachall.trim();
    }

    public BigDecimal getThreshold() {
        return threshold;
    }

    public void setThreshold(BigDecimal threshold) {
        this.threshold = threshold;
    }

    public String getDollarbond() {
        return dollarbond;
    }

    public void setDollarbond(String dollarbond) {
        this.dollarbond = dollarbond == null ? null : dollarbond.trim();
    }

    public String getIsNeedlock() {
        return isNeedlock;
    }

    public void setIsNeedlock(String isNeedlock) {
        this.isNeedlock = isNeedlock == null ? null : isNeedlock.trim();
    }

    public String getLockStatus() {
        return lockStatus;
    }

    public void setLockStatus(String lockStatus) {
        this.lockStatus = lockStatus == null ? null : lockStatus.trim();
    }

    public String getIsSharejf() {
        return isSharejf;
    }

    public void setIsSharejf(String isSharejf) {
        this.isSharejf = isSharejf == null ? null : isSharejf.trim();
    }

    public String getIsSuperposition() {
        return isSuperposition;
    }

    public void setIsSuperposition(String isSuperposition) {
        this.isSuperposition = isSuperposition == null ? null : isSuperposition.trim();
    }

    public Long getProMaxQty() {
        return proMaxQty;
    }

    public void setProMaxQty(Long proMaxQty) {
        this.proMaxQty = proMaxQty;
    }

    public String getVouchersAndvipdis() {
        return vouchersAndvipdis;
    }

    public void setVouchersAndvipdis(String vouchersAndvipdis) {
        this.vouchersAndvipdis = vouchersAndvipdis == null ? null : vouchersAndvipdis.trim();
    }

    public String getIslimitOlddiscount() {
        return islimitOlddiscount;
    }

    public void setIslimitOlddiscount(String islimitOlddiscount) {
        this.islimitOlddiscount = islimitOlddiscount == null ? null : islimitOlddiscount.trim();
    }
}