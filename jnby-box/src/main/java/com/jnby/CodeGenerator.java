package com.jnby;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
//import com.sun.javafx.PlatformUtil;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
/**
 * @Description: 代码生成器
 * @Author: brian
 * @Date: 2021/9/28 10:54
 * @params:
 * @return:
 */
public class CodeGenerator {

    /**
     *  box库
     */
/*    public static final String JDBC_BOX_URL = "**********************************";
    public static final String JDBC_DRIVER_NAME = "oracle.jdbc.OracleDriver";
    public static final String JDBC_USERNAME = "jnby";
    public static final String JDBC_PASSWORD = "TMl#sdhus72j2o24";*/

    /**
     *  伯俊库
     */
    public static final String JDBC_BOX_URL = "*********************************";
    public static final String JDBC_DRIVER_NAME = "oracle.jdbc.OracleDriver";
    public static final String JDBC_USERNAME = "neands3";
    public static final String JDBC_PASSWORD = "Tml#bh6g8k#2o23";


    /**
     *  商品库
     */

//    public static final String JDBC_BOX_URL = "*****************************************************************************************************";
//    public static final String JDBC_DRIVER_NAME = "com.mysql.cj.jdbc.Driver";
//    public static final String JDBC_USERNAME = "jnby_product";
//    public static final String JDBC_PASSWORD = "jnby";


    /**
     * 要生成的表名
     */

    public static final String[] tables= {"c_webposdis"};
    /**
     * 要生成的模块名（与表名位置对应）
     */
    public static final String[] modules={"cWebposdis",};


    /**
     * 指定文件生成位置
     */
    //controller接口生成位置
    public static final String CONTROLLER_PC = "com.jnby.application.openapi";
    //service接口生成位置
    public static final String SERVICE_PC = "com.jnby.base.service";
    //serviceImpl生成位置
    public static final String SERVICE_IMPL_PC = "com.jnby.base.service.impl";
    //mapper生成位置
    public static final String MAPPER_PC = "com.jnby.infrastructure.bojun.mapper";
    //mapper.xml生成位置
    public static final String MAPPER_XML_PC = "persistence/bojun/";
    //MODEL生成位置
    public static final String MODEL_PC = "com.jnby.infrastructure.bojun.model";



    /**
     * 作者
     */
    public static final String AUTHOR = "wangchun";

    /**
     * 代码生成位置 用于单体生成
     */
    //public static final String PARENT_NAME = "com.jnby.module.product";


    /**
     * SERVICE代码生成父模块
     */
    // public static final String SERVICE_PARENT_NAME = "customer";

    /**
     * modular 名字
     */
    public static final String MODULAR_NAME = "";

    /**
     * 基本路径
     */
    public static final String SRC_MAIN_JAVA = "/jnby-box/src/main/java/";

    /**
     * 是否是 rest 接口
     */
    private static final boolean REST_CONTROLLER_STYLE = true;
    public static void main(String[] args) {
        // 前缀（默认#）
        String tablePrefix = "";
        // 开始生成模板
        System.out.println("====>开始生成模板<====");
        for(int i = 0;i< tables.length;i++){
            System.out.println("====>开始生成表"+tables[i]+"的模板<====");
            autoGenerator(modules[i], tables[i].toUpperCase(), tablePrefix);
            System.out.println("====>表"+tables[i]+"的模板生成完毕<====");
        }
    }

    public static void autoGenerator(String moduleName, String tableName, String tablePrefix) {
        new AutoGenerator()
                .setGlobalConfig(getGlobalConfig())
                .setDataSource(getDataSourceConfig())
                .setPackageInfo(getPackageConfig(moduleName))
                .setStrategy(getStrategyConfig(tableName, tablePrefix))
                .setCfg(getInjectionConfig(moduleName))
                .setTemplate(getTemplateConfig())
                .execute();
    }

    private static String getDateTime() {
        LocalDateTime localDate = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return localDate.format(formatter);
    }

    private static InjectionConfig getInjectionConfig(final String moduleName) {
        return new InjectionConfig() {
            @Override
            public void initMap() {
                Map map = new HashMap();
                map.put("dateTime", getDateTime());
                setMap(map);
                final String projectPath = System.getProperty("user.dir");
                List<FileOutConfig> fileOutConfigList = new ArrayList<FileOutConfig>();
                // 自定义配置会被优先输出
                fileOutConfigList.add(new FileOutConfig("/templates-generator/mapper.xml.vm") {
                    @Override
                    public String outputFile(TableInfo tableInfo) {
                        // 自定义输出文件名，如果entity设置了前后缀，此次注意xml的名称也会跟着发生变化
                        return projectPath + "/jnby-box/src/main/resources/"+ MAPPER_XML_PC
                                + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
                    }
                });
                /*
                fileOutConfigList.add(new FileOutConfig("/templates-generator/entity.java.vm") {
                    @Override
                    public String outputFile(TableInfo tableInfo) {
                        // 自定义输出文件名，如果entity设置了前后缀，此次注意xml的名称也会跟着发生变化
                        return projectPath + "/jnby-box/src/main/java/com/jnby/infrastructure/box/model/"
                                +tableInfo.getEntityName() + StringPool.DOT_JAVA;
                    }
                });

                fileOutConfigList.add(new FileOutConfig("/templates-generator/mapper.java.vm") {
                    @Override
                    public String outputFile(TableInfo tableInfo) {
                        // 自定义输出文件名，如果entity设置了前后缀，此次注意xml的名称也会跟着发生变化
                        return projectPath + "/jnby-box/src/main/java/com/jnby/infrastructure/box/mapper/"
                                +tableInfo.getMapperName() + StringPool.DOT_JAVA;
                    }
                });

                fileOutConfigList.add(new FileOutConfig("/templates-generator/serviceImpl.java.vm") {
                    @Override
                    public String outputFile(TableInfo tableInfo) {
                        // 自定义输出文件名，如果entity设置了前后缀，此次注意xml的名称也会跟着发生变化
                        return projectPath + "/jnby-box/src/main/java/com/jnby/"+SERVICE_PARENT_NAME+"/"+moduleName+"/"
                                +tableInfo.getServiceName() + StringPool.DOT_JAVA;
                    }
                });

                fileOutConfigList.add(new FileOutConfig("/templates-generator/serviceImpl.java.vm") {
                    @Override
                    public String outputFile(TableInfo tableInfo) {
                        // 自定义输出文件名，如果entity设置了前后缀，此次注意xml的名称也会跟着发生变化
                        return projectPath + "/jnby-box/src/main/java/com/jnby/"+SERVICE_PARENT_NAME+"/"+moduleName+"/impl/"
                                +tableInfo.getServiceImplName() + StringPool.DOT_JAVA;
                    }
                });
                */
                setFileOutConfigList(fileOutConfigList);
            }
        };
    }


    private static StrategyConfig getStrategyConfig(String tableName, String tablePrefix) {
        return new StrategyConfig()
                .setNaming(NamingStrategy.underline_to_camel)
                .setColumnNaming(NamingStrategy.underline_to_camel)
                .setInclude(tableName)
                .setRestControllerStyle(REST_CONTROLLER_STYLE)
                .setEntityBuilderModel(true)
                .setControllerMappingHyphenStyle(true)
                .setEntityTableFieldAnnotationEnable(true)
                .setTablePrefix(tablePrefix + "_");
    }

    private static PackageConfig getPackageConfig(String moduleName) {
        // 指定不同文件生成位置
        return new PackageConfig()
                .setController(null)
                .setParent(null)
                .setService(SERVICE_PC)
                .setServiceImpl(SERVICE_IMPL_PC)
                .setEntity(MODEL_PC)
                .setMapper(MAPPER_PC)
                .setXml(null);
    }

    private static DataSourceConfig getDataSourceConfig() {
        return new DataSourceConfig()
                .setUrl(JDBC_BOX_URL)
                .setDriverName(JDBC_DRIVER_NAME)
                .setUsername(JDBC_USERNAME)
                .setPassword(JDBC_PASSWORD);
    }

    private static GlobalConfig getGlobalConfig() {
        String projectPath = System.getProperty("user.dir");
        String filePath = projectPath + "/" + MODULAR_NAME + SRC_MAIN_JAVA;

        //if (PlatformUtil.isWindows()) {
            //filePath = filePath.replaceAll("/+|\\\\+", "\\\\");
        //} else {
            filePath = filePath.replaceAll("/+|\\\\+", "/");
       // }
        return new GlobalConfig()
                .setOutputDir(filePath)
                .setDateType(DateType.ONLY_DATE)
                .setIdType(IdType.UUID)
                .setAuthor(AUTHOR)
                .setBaseColumnList(true)
                .setSwagger2(true)
                .setEnableCache(false)
                .setBaseResultMap(true)
                .setOpen(false);
    }

    private static TemplateConfig getTemplateConfig() {
        return new TemplateConfig()
                .setController(null)
                .setService("/templates-generator/service.java.vm")
                .setServiceImpl("/templates-generator/serviceImpl.java.vm")
                .setEntity("/templates-generator/entity.java.vm")
                .setMapper("/templates-generator/mapper.java.vm")
                .setXml(null);
    }

    private static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in);
        StringBuilder sb = new StringBuilder();
        sb.append("please input " + tip + " : ");
        System.out.println(sb.toString());
        if (scanner.hasNext()) {
            String ipt = scanner.next();
            if (StringUtils.isNoneEmpty(ipt)) {
                return ipt;
            }
        }
        throw new MybatisPlusException("please input the correct " + tip + ". ");
    }
}
