package com.jnby.config;
import com.jnby.base.repository.IBoxDictRepository;
import com.jnby.common.cache.DefaultJvmCacheFactory;
import com.jnby.common.cache.JvmCacheFactory;
import com.jnby.infrastructure.bojun.mapper.CProvinceMapper;
import com.jnby.infrastructure.bojun.mapper.CVipTypeMapper;
import com.jnby.infrastructure.bojun.mapper.CityMapper;
import com.jnby.infrastructure.bojun.mapper.DistrictMapper;
import com.jnby.infrastructure.bojun.model.CVipType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 4/7/21 10:45 AM
 */
@Configuration
public class CacheConfig {

    @Autowired
    private IBoxDictRepository iBoxDictRepository;

    @Autowired
    private CProvinceMapper provinceMapper;

    @Autowired
    private CityMapper cityMapper;

    @Autowired
    private DistrictMapper districtMapper;

    @Autowired
    private CVipTypeMapper vipTypeMapper;

    @Bean(value = "jvmCache")
    public JvmCacheFactory initJvmCache(){
        JvmCacheFactory jvmCacheFactory = new DefaultJvmCacheFactory();
        ((DefaultJvmCacheFactory) jvmCacheFactory).setiBoxDictRepository(iBoxDictRepository);
        ((DefaultJvmCacheFactory) jvmCacheFactory).setCityMapper(cityMapper);
        ((DefaultJvmCacheFactory) jvmCacheFactory).setDistrictMapper(districtMapper);
        ((DefaultJvmCacheFactory) jvmCacheFactory).setProvinceMapper(provinceMapper);
        ((DefaultJvmCacheFactory) jvmCacheFactory).setVipTypeMapper(vipTypeMapper);
        ((DefaultJvmCacheFactory) jvmCacheFactory).init();
        return jvmCacheFactory;
    }
}
