package com.jnby.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.redisson.config.Config;
import org.springframework.util.StringUtils;

/**
 * @Auther: lwz
 * @Date: 2021/11/8 18:20
 * @Description: RedissonConfig
 * @Version 1.0.0
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private String port;

    @Value("${redis.password:#{null}}")
    private String password;

    @Bean
    public RedissonClient getRedisson(){
        Config config = new Config();
        config.useSingleServer().setAddress("redis://" + host + ":" + port);
        if (!StringUtils.isEmpty(password)){
            config.useSingleServer().setPassword(password);
        }
        return Redisson.create(config);
    }
}
