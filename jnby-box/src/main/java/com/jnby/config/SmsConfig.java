package com.jnby.config;

import com.jnby.common.util.SmsUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/6/229:52
 */
@Configuration
public class SmsConfig {

    @Value("${sms.url}")
    private String url;

    @Value("${sms.account}")
    private String account;

    @Value("${sms.password}")
    private String password;


    @Bean
    public SmsUtil SmsUtil(){
        SmsUtil util = new SmsUtil();
        util.setUrl(url);
        util.setAccount(account);
        util.setPassword(password);
        return util;
    }
}
