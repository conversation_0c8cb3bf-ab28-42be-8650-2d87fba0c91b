package com.jnby.common.shouqianba;

import jodd.util.Base64;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.security.*;
import java.security.spec.X509EncodedKeySpec;

/**
 * 收钱吧 验签工具
 *
 * <AUTHOR>
 */
@Slf4j
public class ValidateSignUtil {
    /**
     * 公钥
     */
    private static String pubKey = SqbReadFileUtil.parseFileToStrByPath(ShouQianBaConstant.PUBLIC_KEY_PATH);

    /**
     * 验签
     *
     * @param data          签名原数据
     * @param request       请求
     * @return
     */
    public static boolean validateSign(String data, HttpServletRequest request) {
        return validateSign(data, request.getHeader(ShouQianBaConstant.HEADER_NAME));
    }

    /**
     * 验签
     *
     * @param data          签名原数据
     * @param request       请求
     * @param publicKeyPath 公钥秘钥
     * @return
     */
    public static boolean validateSign(String data, HttpServletRequest request, String publicKeyPath) {
        return validateSign(data, request.getHeader(ShouQianBaConstant.HEADER_NAME), publicKeyPath);
    }



    /**
     * 验签
     *
     * @param data          签名原数据
     * @param sign          签名
     * @param publicKeyPath 收钱吧公钥
     * @return
     */
    public static boolean validateSign(String data, String sign, String publicKeyPath) {
        log.info("sbq验签data:{} sign:{} ", data, sign);
        String publicKey = SqbReadFileUtil.parseFileToStrByPath(publicKeyPath);

        try {
            Signature signature = Signature.getInstance("SHA256WithRSA");
            PublicKey localPublicKey = getPublicKeyFromX509("RSA", publicKey);
            signature.initVerify(localPublicKey);
            signature.update(data.getBytes());
            byte[] bytesSign = Base64.decode(sign);
            return signature.verify(bytesSign);
        } catch (Exception e) {
            log.error("收钱吧验签工具异常 e:{} message:{}", e, e.getMessage());
            return false;
        }
    }


    /**
     * 收钱吧默认验签
     *
     * @param data          签名原数据
     * @param sign          签名
     * @return
     */
    public static boolean validateSign(String data, String sign) {
        log.info("sbq验签data:{} sign:{} ", data, sign);
        try {
            Signature signature = Signature.getInstance("SHA256WithRSA");
            PublicKey localPublicKey = getPublicKeyFromX509("RSA", pubKey);
            signature.initVerify(localPublicKey);
            signature.update(data.getBytes());
            byte[] bytesSign = Base64.decode(sign);
            return signature.verify(bytesSign);
        } catch (Exception e) {
            log.error("收钱吧验签工具异常 e:{} message:{}", e, e.getMessage());
            return false;
        }
    }

    public static PublicKey getPublicKeyFromX509(String algorithm, String publicKey) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        return keyFactory.generatePublic(new X509EncodedKeySpec(Base64.decode(publicKey)));
    }


}
