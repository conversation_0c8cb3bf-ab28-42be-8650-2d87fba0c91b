package com.jnby.common.job;

import com.jnby.module.order.service.IRefundService;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 零售单退货  job
 */
@Component
@Slf4j
public class SyncRefundOrderJob extends IJobHandler {

    @Autowired
    private IRefundService refundService;

    @Override
    @XxlJob("syncRefundOrderJob")
    public void execute() throws Exception {

        log.info("SyncRefundOrderJob   生成导购退款 box_refund和box_refund_detail         开始");
        try {
            refundService.syncGuideRefund();
        }catch (Exception e){
            log.error("SyncRefundOrderJob   e = {}",e);
        }
        log.info("SyncRefundOrderJob   生成导购退款 box_refund和box_refund_detail         结束");


        log.info("SyncRefundOrderJob   开始执行pos退单        开始");
        refundService.syncPosRefund();
        log.info("SyncRefundOrderJob   开始执行pos退单        结束");
    }
}
