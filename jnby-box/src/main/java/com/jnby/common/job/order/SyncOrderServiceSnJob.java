package com.jnby.common.job.order;

import com.jnby.infrastructure.box.mapper.OrderMapper;
import com.jnby.module.order.service.IRetailService;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 补偿一小时内未同步零售单号的搭配师和有搭订单
 */
@Component
@Slf4j
public class SyncOrderServiceSnJob  extends IJobHandler {
    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private IRetailService retailService;

    @Override
    @XxlJob("syncOrderServiceSnJob")
    public void execute() throws Exception {
        // 搭配师和有搭
        List<String> orderIdList = orderMapper.getNoSyncOrder();
        orderIdList.stream().forEach(e -> {
            try {
                retailService.syncServiceSn(e);
            }catch (Exception ex){
                log.error("同步订单serviceSn异常，订单id={},",e,ex);
            }
        });
    }
}
