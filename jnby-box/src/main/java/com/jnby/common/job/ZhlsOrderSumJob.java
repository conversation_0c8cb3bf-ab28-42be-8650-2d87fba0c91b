package com.jnby.common.job;

import com.google.common.collect.Lists;
import com.jnby.common.util.DateUtil;
import com.jnby.common.util.zhls.ZhlsApiTool;
import com.jnby.common.util.zhls.model.AddOrderSumReq;
import com.jnby.infrastructure.box.mapper.OrderMapper;
import com.jnby.infrastructure.box.model.ZhlsOrderSum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 每天 6:00 前完成前一天的数据上报
 */
@Component
public class ZhlsOrderSumJob  extends IJobHandler {

    @Resource
    private OrderMapper orderMapper;

    @Autowired
    private ZhlsApiTool zhlsApiTool;

    @Override
    @XxlJob("zhlsOrderSumJob")
    public void execute() throws Exception {
        ZhlsOrderSum zhlsOrderSum = orderMapper.selectCountOrderBeforeOrderSum();
        ZhlsOrderSum createOrderSum = orderMapper.selectCountCreateOrderSum();
        if (Objects.isNull(zhlsOrderSum)) return ;

        Date beforeTime = DateUtil.addDate(new Date(), -1);
        AddOrderSumReq sumReq = new AddOrderSumReq();
        AddOrderSumReq.Orders orders = new AddOrderSumReq.Orders();
        orders.setGive_order_amount_sum(createOrderSum.getPaidAmount());
        orders.setGive_order_num_sum(createOrderSum.getOrderSum());
        orders.setRef_date(DateUtil.getCurrentTime(beforeTime) + "000");

        orders.setPayed_num_sum(zhlsOrderSum.getOrderSum());
        orders.setPayment_amount_sum(zhlsOrderSum.getPaidAmount());
        sumReq.setOrders(Lists.newArrayList(orders));
        zhlsApiTool.addOrderSum(sumReq);
    }

    public static void main(String[] args) {
        Date beforeTime = DateUtil.addDate(new Date(), -1);
        System.out.println(DateUtil.getCurrentTime(beforeTime));
    }
}
