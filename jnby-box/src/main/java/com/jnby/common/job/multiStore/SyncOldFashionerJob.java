package com.jnby.common.job.multiStore;

import com.google.common.base.Preconditions;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.jnby.base.service.IFashionerUserStoreService;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.infrastructure.bojun.mapper.CStoreMapper;
import com.jnby.infrastructure.bojun.mapper.EmployeeBaseMapper;
import com.jnby.infrastructure.bojun.mapper.HrEmployeeMapper;
import com.jnby.infrastructure.bojun.model.CStore;
import com.jnby.infrastructure.bojun.model.EmployeeBase;
import com.jnby.infrastructure.bojun.model.HrEmployee;
import com.jnby.infrastructure.bojun.model.HrEmployeeWithStoreEntity;
import com.jnby.infrastructure.box.mapper.FashionerMapper;
import com.jnby.infrastructure.box.mapper.SysStoreMapper;
import com.jnby.infrastructure.box.mapper.SysUserMapper;
import com.jnby.infrastructure.box.model.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步历史导购信息
 */
@Component
@Slf4j
public class SyncOldFashionerJob {
    @Resource
    private FashionerMapper fashionerMapper;

    @Resource
    private EmployeeBaseMapper employeeBaseMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private HrEmployeeMapper hrEmployeeMapper;

    @Autowired
    @Qualifier("boxTransactionTemplate")
    private TransactionTemplate template;

    @Resource
    private SysStoreMapper sysStoreMapper;


    @Resource
    private IFashionerUserStoreService iFashionerUserStoreService;

    /**
     * 处理全部历史导购
     */
    public void handleHistory() {
        List<SysUser> sysUserList = sysUserMapper.getAllGuideSysUer().stream().filter(e -> {
            return (e.getIsNew() == 0);
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(sysUserList)) {
            return;
        }
        List<List<SysUser>> lists = Lists.partition(sysUserList, 900);
        lists.stream().forEach(list -> {
            handleHistory(list);
        });
    }

    /**
     * 处理指定历史导购
     * @param linkId
     */
    public void handleHistory(String linkId) {
        SysUser sysUserSearch = new SysUser();
        sysUserSearch.setcEmployeeLinkid(linkId);
        sysUserSearch.setIsSales(1L);
        sysUserSearch.setStatus(1L);
        sysUserSearch.setIsNew(0L);

        List<SysUser> sysUserList = sysUserMapper.selectListBySelective(sysUserSearch);
        if (CollectionUtils.isEmpty(sysUserList)) {
            return;
        }
        handleHistory(sysUserList);
    }

    public void handleHistory(List<SysUser> sysUserList) {
        List<String> sysUserIds = sysUserList.stream().map(SysUser::getId).collect(Collectors.toList());
        List<Fashioner> fashioners = fashionerMapper.findIdsWithUserIdsByUserIds(sysUserIds);
        if (CollectionUtils.isEmpty(fashioners)) {
            return;
        }

        Map<String, String> sysUserIdWithFashionerIdMap = new HashMap<>();
        fashioners.stream().forEach(e -> {
            sysUserIdWithFashionerIdMap.put(e.getUserId(), e.getId());
        });


        Multimap<String, SysUser> sysUserMultimap = ArrayListMultimap.create();
        sysUserList.stream().forEach(e -> {
            sysUserMultimap.put(e.getStoreId(), e);
        });

        List<String> sysStoreIds = new ArrayList<>();
        sysStoreIds.addAll(sysUserMultimap.keySet());
        if (CollectionUtils.isEmpty(sysStoreIds)) {
            return;
        }

        List<SysStore> sysStoreList = sysStoreMapper.selectAllSysStore();
        if (CollectionUtils.isEmpty(sysStoreList)) {
            return;
        }
        Map<String, String> sysStoreIdWithUnionStoreIdMap = new HashMap<>();
        Map<String, String> cStoreIdWithSysStoreIdMap = new HashMap<>();
        sysStoreList.stream().forEach(e -> {
            sysStoreIdWithUnionStoreIdMap.put(e.getId(), StringUtils.isEmpty(e.getcUnionstoreId()) ? String.valueOf(e.getcStoreId()) : e.getcUnionstoreId());
            cStoreIdWithSysStoreIdMap.put(String.valueOf(e.getcStoreId()), e.getId());
        });

        Date nowDate = new Date();

        for (SysUser sysUser : sysUserList) {
            try {
                handleData(nowDate, sysUser, sysStoreIdWithUnionStoreIdMap, cStoreIdWithSysStoreIdMap, sysUserIdWithFashionerIdMap);
            } catch (Exception e) {
                log.error("处理历史数据用户id:{} 工号:{} 手机号为:{} 错误:e:{} message:{}", sysUser.getId(), sysUser.getcEmployeeLinkid(), sysUser.getUsername(), e, e.getMessage());
            }
        }
    }


    /**
     * 处理数据
     *
     * @param nowDate
     * @param sysUser
     * @param sysStoreIdWithUnionStoreIdMap
     * @param cStoreIdWithSysStoreIdMap
     * @param sysUserIdWithFashionerIdMap
     */
    void handleData(Date nowDate, SysUser sysUser, Map<String, String> sysStoreIdWithUnionStoreIdMap, Map<String, String> cStoreIdWithSysStoreIdMap, Map<String, String> sysUserIdWithFashionerIdMap) {
        String cStoreId = sysStoreIdWithUnionStoreIdMap.get(sysUser.getStoreId());
        Preconditions.checkArgument(StringUtils.isNotEmpty(cStoreId), "sysUser信息用户为:" + sysUser.getUsername() + " 翻译cStoreId出错");
        String sysStoreId = cStoreIdWithSysStoreIdMap.get(cStoreId);
        Preconditions.checkArgument(StringUtils.isNotEmpty(sysStoreId), "sysUser信息用户为:" + sysUser.getUsername() + " 翻译sysStoreId出错");
        SysUser updateSysUser = new SysUser();
        updateSysUser.setId(sysUser.getId());
        updateSysUser.setcStoreId(cStoreId);
        updateSysUser.setStoreId(sysStoreId);
        if (StringUtils.isNotEmpty(sysUser.getUsername())) {
            HrEmployeeWithStoreEntity hrEmployeeWithStoreEntity = getHrHrEmployeeWithStoreEntity(sysUser.getUsername(), cStoreId);
            if (hrEmployeeWithStoreEntity != null) {
                updateSysUser.setcEmployeeId(hrEmployeeWithStoreEntity.getId());
                updateSysUser.setcEmployeeCode(hrEmployeeWithStoreEntity.getNo());
                updateSysUser.setcEmployeeName(hrEmployeeWithStoreEntity.getName());
            }
        }
        updateSysUser.setUpdateTime(nowDate);

        String fashionerId = sysUserIdWithFashionerIdMap.get(sysUser.getId());
        Preconditions.checkArgument(StringUtils.isNotEmpty(fashionerId), "sysUser信息用户为:" + sysUser.getUsername() + " 翻译fashionerId出错");
        FashionerWithBLOBs updateFashioner = new FashionerWithBLOBs();
        updateFashioner.setId(fashionerId);
        updateFashioner.setcStoreId(cStoreId);
        updateFashioner.setHrEmpId(updateSysUser.getcEmployeeId() != null ? String.valueOf(updateSysUser.getcEmployeeId()) : String.valueOf(sysUser.getcEmployeeId()));
        updateFashioner.setUpdateTime(nowDate);

        FashionerUserStore fashionerUserStore = new FashionerUserStore();
        fashionerUserStore.setId(fashionerId);
        fashionerUserStore.setSysUserId(sysUser.getId());
        fashionerUserStore.setSysStoreId(sysStoreId);
        fashionerUserStore.setOldCStoreId(sysUser.getcStoreId());
        fashionerUserStore.setNewCStoreId(cStoreId);
        fashionerUserStore.setOldHrEmId(String.valueOf(sysUser.getcEmployeeId()));
        fashionerUserStore.setNewHrEmId(String.valueOf(updateSysUser.getcEmployeeId()));
        fashionerUserStore.setLinkId(sysUser.getcEmployeeLinkid());
        fashionerUserStore.setSyncTime(nowDate);
        fashionerUserStore.setNewSysStoreId(sysStoreId);
        fashionerUserStore.setSyncStatus(0L);

        template.execute(action -> {
            fashionerMapper.updateByPrimaryKeySelective(updateFashioner);
            sysUserMapper.updateByPrimaryKeySelective(updateSysUser);
            iFashionerUserStoreService.saveOrUpdate(fashionerUserStore);
            return true;
        });
    }

    /**
     * 根据手机号获取 账户信息
     *
     * @param phone
     * @param cStoreId
     * @return
     */
    HrEmployeeWithStoreEntity getHrHrEmployeeWithStoreEntity(String phone, String cStoreId) {
        HrEmployeeWithStoreEntity hrEmployeeWithStoreEntity = new HrEmployeeWithStoreEntity();
        hrEmployeeWithStoreEntity.setStoreId(Long.valueOf(cStoreId));
        hrEmployeeWithStoreEntity.setPhone(phone);
        List<HrEmployeeWithStoreEntity> hrEmployeeWithStoreEntities = hrEmployeeMapper.selectHrEmployeeWithStoreBySearch(hrEmployeeWithStoreEntity);
        if (CollectionUtils.isEmpty(hrEmployeeWithStoreEntities)) {
            return null;
        }
        return hrEmployeeWithStoreEntities.get(0);
    }

}
