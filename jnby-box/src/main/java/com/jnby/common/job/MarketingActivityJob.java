package com.jnby.common.job;

import com.jnby.infrastructure.box.mapper.MarketingActivityMapper;
import com.jnby.infrastructure.box.model.BActivityBasis;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import com.xxl.job.core.biz.model.ReturnT;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 定时扫描活动任务状态
 * */

@Component
@Slf4j
public class MarketingActivityJob extends IJobHandler {

    @Resource
    private MarketingActivityMapper marketingActivityMapper;

    @Override
    @XxlJob("marketingActivityJob")
    public void execute() throws Exception {
        log.info("======>活动任务扫描进行中任务开始......");
        marketingActivityMapper.updateBatchActivityStateByTime(2);
        log.info("======>活动任务扫描进行中任务结束......");
        log.info("======>活动任务扫描结束任务开始......");
        marketingActivityMapper.updateBatchActivityStateByTime(3);
        log.info("======>活动任务扫描结束任务结束......");
    }
}
