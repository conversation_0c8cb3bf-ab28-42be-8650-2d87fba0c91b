package com.jnby.common.job;

import com.jnby.common.enums.OptionalChannelEnum;
import com.jnby.infrastructure.box.model.BOptionalChannel;
import com.jnby.module.marketing.styling.service.IBOptionalChannelService;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.xxl.job.core.biz.model.ReturnT;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;


/**
 * 自选频道状态刷新
 * <AUTHOR>
 * @date 2021/4/22
 */
@Component
public class OptionalChannelJob extends IJobHandler {
    private static Logger logger = LoggerFactory.getLogger(OptionalChannelJob.class);

    @Autowired
    IBOptionalChannelService ibOptionalChannelService;

    @Override
    @XxlJob("optionalChannelJob")
    public void execute(){
        /**
         * 同步进行中状态
         */
        syncRunningChannel();
        /**
         * 同步已结束状态
         */
        syncOverChannel();

    }


    private void syncRunningChannel(){
        logger.info("开始:自选频道进行中状态刷新");
        BOptionalChannel optionalChannel = new BOptionalChannel();
        optionalChannel.setStatus(OptionalChannelEnum.WAIT.getCode());
        optionalChannel.setBeginDate(new Date());
        List<BOptionalChannel> list = ibOptionalChannelService.findList(optionalChannel);
        list.forEach(e -> {
            e.setStatus(OptionalChannelEnum.RUNNING.getCode());
            try {
                ibOptionalChannelService.update(e);
            }catch (Exception ex){
                logger.error("自选频道进行中状态刷新异常,id={}",e.getId(),ex);
            }
        });
        logger.info("结束:自选频道进行中状态刷新");
    }

    private void syncOverChannel(){
        logger.info("开始:自选频道已结束状态刷新");
        BOptionalChannel optionalChannel = new BOptionalChannel();
        optionalChannel.setStatus(OptionalChannelEnum.RUNNING.getCode());
        optionalChannel.setEndDate(new Date());
        List<BOptionalChannel> list = ibOptionalChannelService.findList(optionalChannel);
        list.forEach(e -> {
            e.setStatus(OptionalChannelEnum.OVER.getCode());
            try {
                ibOptionalChannelService.update(e);
            }catch (Exception ex){
                logger.error("自选频道已结束状态刷新异常,id={}",e.getId(),ex);
            }
        });
        logger.info("结束:自选频道已结束状态刷新");
    }

 }
