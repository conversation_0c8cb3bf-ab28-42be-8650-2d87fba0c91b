package com.jnby.common.job.multiStore;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.application.openapi.request.LoseUserMemberCard;
import com.jnby.base.service.IFashionerUserStoreService;
import com.jnby.common.ResponseResult;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.common.util.RedissonUtil;
import com.jnby.infrastructure.bojun.mapper.CStoreMapper;
import com.jnby.infrastructure.bojun.mapper.EmployeeBaseMapper;
import com.jnby.infrastructure.bojun.mapper.HrEmployeeMapper;
import com.jnby.infrastructure.bojun.model.CStore;
import com.jnby.infrastructure.bojun.model.EmployeeBase;
import com.jnby.infrastructure.bojun.model.HrEmployee;
import com.jnby.infrastructure.bojun.model.HrEmployeeWithStoreEntity;
import com.jnby.infrastructure.box.mapper.FashionerMapper;
import com.jnby.infrastructure.box.mapper.SysStoreMapper;
import com.jnby.infrastructure.box.mapper.SysUserMapper;
import com.jnby.infrastructure.box.model.*;
import com.xxl.job.core.handler.IJobHandler;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.sf.saxon.trans.SymbolicName;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步导购信息
 */
@Component
@Slf4j
public class SyncFashionerJob {
    private static final List<String> positions = Arrays.asList("大店长","店长", "店经理", "店员",
            "店员-陈列小帮手", "店员-代班", "店员-导购", "店员-收银", "店员-账库", "营业员", "账库");
    @Resource
    private FashionerMapper fashionerMapper;

    @Resource
    private EmployeeBaseMapper employeeBaseMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private HrEmployeeMapper hrEmployeeMapper;

    @Autowired
    @Qualifier("boxTransactionTemplate")
    private TransactionTemplate template;

    @Resource
    private SysStoreMapper sysStoreMapper;

    @Resource
    private IFashionerUserStoreService iFashionerUserStoreService;

    @Resource
    private IdLeafService idLeafService;

    @Autowired
    private RedissonUtil redissonUtil;


    /**
     * 处理所有多门店,通过employBaseId查询
     *        LINKID2 is not null and ISACTIVE='1'
     *        and position
     *        in(
     *         '大店长','店长','店经理',
     *         '店员','店员-陈列小帮手','店员-代班','店员-导购','店员-收银', '店员-账库',
     *         '营业员','账库'
     *         )
     */
    public void handleMultiAccount(Long employBaseId) {
        // 处理新增账户
        String key = "boxService:handleMultiAccount:employBaseId:" + employBaseId;
        if(!redissonUtil.tryLock(key)){
            throw new RuntimeException("监听到并发消息employBaseId=" + employBaseId);
        }
        try{
            EmployeeBase employeeBase = employeeBaseMapper.selectByPrimaryKey(employBaseId);
            if (Objects.isNull(employeeBase)) {
                return;
            }
            if (!positions.contains(employeeBase.getPosition())
                    && Objects.isNull(employeeBase.getLinkid2()) && !employeeBase.getIsactive().equals(new Short("1"))){
                return;
            }
            handleMultiAccount(employeeBase);
        }finally {
            redissonUtil.unlock(key);
        }

    }

    /**
     * 处理指定多门店
     */
    public void handleMultiAccount(String linkId) {
        EmployeeBase employeeBase = Preconditions.checkNotNull(employeeBaseMapper.selectByLinkId(linkId), "employeeBase信息不存在");
        List<EmployeeBase> employeeBases = new ArrayList<>();
        employeeBases.add(employeeBase);
        if (CollectionUtils.isEmpty(employeeBases)) {
            return;
        }
        handleMultiAccount(employeeBase);
    }


    public void handleMultiAccount(EmployeeBase employeeBase) {
        try {
            HandleMultiAccountContext handleMultiAccountContext = new HandleMultiAccountContext();
            handleMultiAccountContext.setEmployeeBase(employeeBase);
            handleMultiAccount(handleMultiAccountContext);
        } catch (Exception e) {
            log.error("处理多门店employeeBaseId:{} 错误e:{} message:{}", employeeBase.getId(), e, e.getMessage());
        }
    }

    /**
     * 处理多账户
     */
    public void handleMultiAccount(HandleMultiAccountContext handleMultiAccountContext) {
        EmployeeBase employeeBase = handleMultiAccountContext.getEmployeeBase();
        HrEmployeeWithStoreEntity hrEmployeeWithStoreEntitySearch = new HrEmployeeWithStoreEntity();
        hrEmployeeWithStoreEntitySearch.setPhone(employeeBase.getMobile());
        List<HrEmployeeWithStoreEntity> hrEmployeeWithStoreEntities = hrEmployeeMapper.selectHrEmployeeWithStoreBySearch(hrEmployeeWithStoreEntitySearch);
        if (CollectionUtils.isEmpty(hrEmployeeWithStoreEntities)) {
            return;
        }

        for (HrEmployeeWithStoreEntity hrEmployeeWithStoreEntity : hrEmployeeWithStoreEntities) {
            try {
                //hrid和sysuser关系
                Map<String, SysUser> hrIdWithSysUserMap = getAllHrIdWithSysUser(hrEmployeeWithStoreEntity.getId());
                //当前hremploy中的伯俊门店与sys_store关系
                Map<String, String> cStoreIdWithStoreIdMap = getCStoreIdWithSysStoreId(hrEmployeeWithStoreEntity.getStoreId());
                AddOrUpdateSysUserWithOthersContext addOrUpdateSysUserWithOthersContext = new AddOrUpdateSysUserWithOthersContext();
                addOrUpdateSysUserWithOthersContext.setCStoreIdWithStoreIdMap(cStoreIdWithStoreIdMap);
                addOrUpdateSysUserWithOthersContext.setHrEmployeeWithStoreEntity(hrEmployeeWithStoreEntity);
                addOrUpdateSysUserWithOthersContext.setEmployeeBase(employeeBase);
                // 修改账户
                if (hrIdWithSysUserMap.containsKey(hrEmployeeWithStoreEntity.getId().toString())) {
                    addOrUpdateSysUserWithOthersContext.setSysUser(hrIdWithSysUserMap.get(hrEmployeeWithStoreEntity.getId().toString()));
                    updateSysUserWithOthers(addOrUpdateSysUserWithOthersContext);
                } else {
                    addSysUserWithOthers(addOrUpdateSysUserWithOthersContext);
                }
            } catch (Exception e) {
                log.error("处理多账户用户id:{} 用户名:{} 手机号为:{} 错误:e:{} message:{}", hrEmployeeWithStoreEntity.getId(), hrEmployeeWithStoreEntity.getName(), hrEmployeeWithStoreEntity.getPhone(), e, e.getMessage());
            }
        }
    }


    /**
     * 新增SysUser
     *
     * @param addOrUpdateSysUserWithOthersContext
     */
    void addSysUserWithOthers(AddOrUpdateSysUserWithOthersContext addOrUpdateSysUserWithOthersContext) {
        EmployeeBase employeeBase = addOrUpdateSysUserWithOthersContext.getEmployeeBase();
        HrEmployeeWithStoreEntity hrEmployeeWithStoreEntity = addOrUpdateSysUserWithOthersContext.getHrEmployeeWithStoreEntity();
        Map<String, String> cStoreIdWithStoreIdMap = addOrUpdateSysUserWithOthersContext.getCStoreIdWithStoreIdMap();

        HrEmployee hrEmployee = Preconditions.checkNotNull(hrEmployeeMapper.selectByPrimaryKey(Long.valueOf(hrEmployeeWithStoreEntity.getId())), "新增sysUser hrEmployee不存在");
        if (!cStoreIdWithStoreIdMap.containsKey(hrEmployee.getcStoreId().toString())) {
            return;
        }
        String sysStoreId = cStoreIdWithStoreIdMap.get(hrEmployee.getcStoreId().toString());

        Date nowDate = new Date();
        // sysUser 信息
        SysUser sysUser = new SysUser();
        sysUser.setId(idLeafService.getId());
        sysUser.setUsername(hrEmployee.getHandset());
        sysUser.setPassword("e10adc3949ba59abbe56e057f20f883e");
        sysUser.setRoleId("8bee5a88b4d34a05908af4abce9ef800");
        sysUser.setOrgId("1d6a2f432fd841fc89840314a868cb11");
        sysUser.setStatus(1L);
        sysUser.setcEmployeeId(hrEmployee.getId());
        sysUser.setcEmployeeCode(hrEmployee.getNo());
        sysUser.setcEmployeeName(hrEmployee.getName());
        sysUser.setIsSales(1L);
        sysUser.setRole(0L);
        sysUser.setHasRank(0L);
        sysUser.setCreateTime(nowDate);
        sysUser.setcEmployeeLinkid(employeeBase.getLinkid());
        sysUser.setSysempid(Long.valueOf(employeeBase.getLinkid()));
        sysUser.setcStoreId(String.valueOf(hrEmployee.getcStoreId()));
        sysUser.setStoreId(sysStoreId);
        sysUser.setIsNew(1L);

        // fashioner信息
        FashionerWithBLOBs fashionerWithBLOBs = new FashionerWithBLOBs();
        fashionerWithBLOBs.setId(idLeafService.getId());
        fashionerWithBLOBs.setUserId(sysUser.getId());
        fashionerWithBLOBs.setName(hrEmployee.getName());
        fashionerWithBLOBs.setPhone(hrEmployee.getHandset());
        fashionerWithBLOBs.setStatus(1L);
        fashionerWithBLOBs.setScore("10.0");
        fashionerWithBLOBs.setCreateTime(nowDate);
        fashionerWithBLOBs.setIsSales(1L);
        fashionerWithBLOBs.setHrEmpId(String.valueOf(hrEmployee.getId()));
        fashionerWithBLOBs.setcStoreId(String.valueOf(hrEmployee.getcStoreId()));
        fashionerWithBLOBs.setFashionerTypeId("3");

        // 同步日志
        FashionerUserStore fashionerUserStore = new FashionerUserStore();
        fashionerUserStore.setId(fashionerWithBLOBs.getId());
        fashionerUserStore.setSysUserId(sysUser.getId());
        fashionerUserStore.setNewCStoreId(String.valueOf(hrEmployee.getcStoreId()));
        fashionerUserStore.setNewHrEmId(String.valueOf(hrEmployee.getId()));
        fashionerUserStore.setLinkId(employeeBase.getLinkid());
        fashionerUserStore.setSyncTime(nowDate);
        // 1新 0旧
        fashionerUserStore.setSyncStatus(1L);
        fashionerUserStore.setNewSysStoreId(sysStoreId);

        template.execute(action -> {
            sysUserMapper.insertSelective(sysUser);
            fashionerMapper.insertSelective(fashionerWithBLOBs);
            iFashionerUserStoreService.save(fashionerUserStore);
            return true;
        });

    }


    /**
     * 修改SysUser
     *
     * @param addOrUpdateSysUserWithOthersContext
     */
    void updateSysUserWithOthers(AddOrUpdateSysUserWithOthersContext addOrUpdateSysUserWithOthersContext) {
        SysUser sysUser = addOrUpdateSysUserWithOthersContext.getSysUser();
        EmployeeBase employeeBase = addOrUpdateSysUserWithOthersContext.getEmployeeBase();
        HrEmployeeWithStoreEntity hrEmployeeWithStoreEntity = addOrUpdateSysUserWithOthersContext.getHrEmployeeWithStoreEntity();
        Map<String, String> cStoreIdWithStoreIdMap = addOrUpdateSysUserWithOthersContext.getCStoreIdWithStoreIdMap();

        String hrId = hrEmployeeWithStoreEntity.getId().toString();
        String linkId = employeeBase.getLinkid();
        String sysStoreId = cStoreIdWithStoreIdMap.get(hrEmployeeWithStoreEntity.getStoreId().toString());
        String cStoreId = hrEmployeeWithStoreEntity.getStoreId().toString();

        String sysUserId = sysUser.getId();
        HrEmployee hrEmployee = Preconditions.checkNotNull(hrEmployeeMapper.selectByPrimaryKey(Long.valueOf(hrId)), "修改SysUser hrEmployee信息不存在");

        if (Objects.equals(sysUser.getcEmployeeId(), hrEmployee.getId())
                && Objects.equals(sysUser.getcEmployeeCode(), hrEmployee.getNo())
                && Objects.equals(sysUser.getcEmployeeName(), hrEmployee.getName())
                && Objects.equals(sysUser.getcEmployeeLinkid(), employeeBase.getLinkid())
                && Objects.equals(sysUser.getUsername(), hrEmployee.getPhone())
                && Objects.equals(sysUser.getStoreId(), sysStoreId)
                && Objects.equals(sysUser.getcStoreId(), cStoreId)) {
            return;
        }
        Date nowDate = new Date();
        // sysUser 信息
        SysUser updateSysUser = new SysUser();
        updateSysUser.setId(sysUserId);
        updateSysUser.setUpdateTime(nowDate);
        updateSysUser.setStoreId(sysStoreId);
        updateSysUser.setcStoreId(cStoreId);
        updateSysUser.setUsername(employeeBase.getMobile());

        updateSysUser.setcEmployeeId(hrEmployee.getId());
        updateSysUser.setcEmployeeCode(hrEmployee.getNo());
        updateSysUser.setcEmployeeName(hrEmployee.getName());
        updateSysUser.setcEmployeeLinkid(employeeBase.getLinkid());
        updateSysUser.setUsername(hrEmployee.getHandset());
        updateSysUser.setSysempid(Long.valueOf(employeeBase.getLinkid()));
        updateSysUser.setUpdateTime(nowDate);

        FashionerWithBLOBs fashionerWithBLOBsSearch = new FashionerWithBLOBs();
        fashionerWithBLOBsSearch.setUserId(sysUserId);
        List<FashionerWithBLOBs> fashionerWithBLOBsList = fashionerMapper.selectFashionerBySelective(fashionerWithBLOBsSearch);
        if (CollectionUtils.isEmpty(fashionerWithBLOBsList)) {
            return;
        }
        String fashionerId = fashionerWithBLOBsList.get(0).getId();

        // fashioner信息
        FashionerWithBLOBs updateFashioner = new FashionerWithBLOBs();
        updateFashioner.setId(fashionerId);
        updateFashioner.setUserId(sysUserId);
        updateFashioner.setName(hrEmployee.getName());
        updateFashioner.setPhone(hrEmployee.getHandset());
        updateFashioner.setStatus(1L);
        updateFashioner.setIsSales(1L);
        updateFashioner.setHrEmpId(String.valueOf(hrEmployee.getId()));
        updateFashioner.setcStoreId(String.valueOf(hrEmployee.getcStoreId()));
        updateFashioner.setUpdateTime(nowDate);

        // 同步日志
        FashionerUserStore fashionerUserStore = new FashionerUserStore();
        fashionerUserStore.setId(fashionerId);
        fashionerUserStore.setSysUserId(sysUserId);
        fashionerUserStore.setSysStoreId(sysUser.getStoreId());
        fashionerUserStore.setNewSysStoreId(sysStoreId);
        fashionerUserStore.setNewCStoreId(String.valueOf(hrEmployee.getcStoreId()));
        fashionerUserStore.setNewHrEmId(String.valueOf(hrEmployee.getId()));
        fashionerUserStore.setLinkId(linkId);
        fashionerUserStore.setSyncTime(nowDate);

        template.execute(action -> {
            sysUserMapper.updateByPrimaryKeySelective(updateSysUser);
            fashionerMapper.updateByPrimaryKeySelective(updateFashioner);
            iFashionerUserStoreService.saveOrUpdate(fashionerUserStore);
            return true;
        });

    }


    /**
     * 新增或更新用户和其他信息上下文
     */
    @Data
    public static class AddOrUpdateSysUserWithOthersContext {
        private EmployeeBase employeeBase;
        private HrEmployeeWithStoreEntity hrEmployeeWithStoreEntity;

        private SysUser sysUser;
        /**
         * cStoreId 与 sysStoreId 关系
         */
        private Map<String, String> cStoreIdWithStoreIdMap;
    }


    /**
     * 处理多账户上下文
     */
    @Data
    public static class HandleMultiAccountContext {
        private EmployeeBase employeeBase;
    }

    /**
     * 获取cstoreId与storeId
     *
     * @param ctoreId
     * @return
     */
    public Map<String, String> getCStoreIdWithSysStoreId(Long ctoreId) {
        List<SysStore> sysStores = sysStoreMapper.findSysStoreByCStoreId(Lists.newArrayList(ctoreId));
        Map<String, String> sysStoreIdWithCStoreIdMap = new HashMap<>();
        for (SysStore sysStore : sysStores) {
            sysStoreIdWithCStoreIdMap.put(sysStore.getcStoreId().toString(), sysStore.getId());
        }
        return sysStoreIdWithCStoreIdMap;
    }

    /**
     * 获取hrId信息与sysUser信息
     *
     * @return
     */
    public Map<String, SysUser> getAllHrIdWithSysUser(Long hrEmployId) {
        List<SysUser> sysUsers = sysUserMapper.selectSysUserListByCemployeeId(Lists.newArrayList(hrEmployId));
        Map<String, SysUser> sysUserMap = new HashMap<>();
        for (SysUser sysUser : sysUsers) {
            sysUserMap.put(sysUser.getcEmployeeId().toString(), sysUser);
        }
        return sysUserMap;
    }
}
