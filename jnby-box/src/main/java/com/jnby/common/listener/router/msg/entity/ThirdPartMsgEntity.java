package com.jnby.common.listener.router.msg.entity;

import com.jnby.base.entity.CardmainInfoQueryEntity;
import com.jnby.infrastructure.box.model.JicTemplateConfig;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date:2023/3/28 19:03
 */
@Data
public class ThirdPartMsgEntity {
    private Map<String, String> map;
    
    private String unionId;
    
    private JicTemplateConfig brandInfo;
    
    private String templateId;
    
    private String path;
    
    private String openId;

    public static ThirdPartMsgEntity deliveryBuild(Map<String, String> map, String unionId, CardmainInfoQueryEntity fans,
                                                   JicTemplateConfig brandInfos) {
        ThirdPartMsgEntity entity = new ThirdPartMsgEntity();
        entity.setMap(map);
        entity.setUnionId(unionId);
        entity.setTemplateId(brandInfos.getOffTemplateCode());
        entity.setBrandInfo(brandInfos);
        entity.setOpenId(fans.getOpenId());
        return entity;
    }
}
