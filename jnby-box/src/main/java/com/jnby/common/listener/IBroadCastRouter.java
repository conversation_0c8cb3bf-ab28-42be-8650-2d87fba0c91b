package com.jnby.common.listener;

import com.jnby.module.order.event.ProducerMqEvent;
import org.apache.rocketmq.common.message.Message;
import org.springframework.cloud.sleuth.annotation.NewSpan;

/**
 * 广播路由接口
 * <AUTHOR>
 * @Date 2022/4/21 9:58 上午
 * @Version 1.0
 */
public interface IBroadCastRouter {
    //取消订单tag
    String CANCEL_ORDER_TAG = "orderCancelTag";
    String CREATE_ORDER_TAG = "createOrderTag";

    String CREATE_BOX_ORDER_TAG = "createBoxOrderTag";

    String getTags();

    void consume(Message message);

    /**
     * 路由 根据span
     * @param event
     */
    @NewSpan
    void consumeKillBillQueue(ProducerMqEvent event);
}
