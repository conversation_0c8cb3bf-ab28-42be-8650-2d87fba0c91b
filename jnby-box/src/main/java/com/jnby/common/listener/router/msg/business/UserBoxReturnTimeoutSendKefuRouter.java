package com.jnby.common.listener.router.msg.business;

import com.alibaba.fastjson.JSONObject;
import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.common.listener.IBroadCastRouter;
import com.jnby.common.listener.router.msg.util.MsgTagUtil;
import com.jnby.infrastructure.box.model.BoxDetailsWithBLOBs;
import com.jnby.infrastructure.box.model.BoxWithBLOBs;
import com.jnby.infrastructure.box.model.CustomerDetails;
import com.jnby.infrastructure.box.model.Fashioner;
import com.jnby.module.order.event.ProducerMqEvent;
import com.jnby.module.order.repository.IBoxRepository;
import com.jnbyframework.boot.api.ISysBaseAPI;
import com.jnbyframework.boot.common.api.dto.message.TemplateMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.openxml4j.opc.PackageRelationship;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RefreshScope
@Slf4j
public class UserBoxReturnTimeoutSendKefuRouter implements IBroadCastRouter {
    private static final String TEMPLATE_CODE = "BOX_RETURN_TIMEOUT";
    @Autowired
    private ICustomerDetailsRepository iCustomerDetailsRepository;

    @Autowired
    private IBoxRepository iBoxRepository;

    @Value("${role.kefu.phones}")
    private String kefuPhones;

    @Resource
    private ISysBaseAPI iSysBaseAPI;

    @Override
    public String getTags() {
        return MsgTagUtil.TURN_TO_BE_RETURN_MAG_TAG;
    }

    @Override
    public void consume(Message message) {

    }

    @Override
    public void consumeKillBillQueue(ProducerMqEvent event) {
        // 转换数据
        String para = null;
        String boxId = null;
        try {
            para = new String(event.getMessage(), "UTF-8");
            boxId = JSONObject.parseObject(para, String.class);
        } catch (UnsupportedEncodingException e) {
            log.error("TURN_TO_BELT_RETURN_MAG_TAG.Param转换报错");
        }

        // 查询box信息
        BoxWithBLOBs box = iBoxRepository.findById(boxId);
        if (box == null) {
            log.error("处理签收第二天发送信息有异常，未找到对应的服务单, id为{}", boxId);
            return;
        }

        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(box.getUnionid());
        if (customerDetails == null) return;

        //发送客服消息
        sendTemplateMsg(customerDetails, boxId);
    }

    private void sendTemplateMsg(CustomerDetails customerDetails, String boxId){
        TemplateMessageDTO messageDTO = new TemplateMessageDTO();
        messageDTO.setTemplateCode(TEMPLATE_CODE);
        Map<String, String> params = new HashMap<>();
        params.put("phone", customerDetails.getPhone());
        params.put("boxId", boxId);
        messageDTO.setTemplateParam(params);
        messageDTO.setFromUser("admin");
        messageDTO.setToUser(kefuPhones);
        try {
            iSysBaseAPI.sendTemplateAnnouncement(messageDTO);
        }catch (Exception e){

        }
    }
}
