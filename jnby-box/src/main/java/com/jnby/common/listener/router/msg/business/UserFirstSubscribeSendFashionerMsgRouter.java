package com.jnby.common.listener.router.msg.business;

import com.alibaba.fastjson.JSONObject;
import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.base.repository.IFashionerRepository;
import com.jnby.common.enums.CouponRecordEnum;
import com.jnby.common.listener.IBroadCastRouter;
import com.jnby.common.listener.router.msg.util.MsgTagUtil;
import com.jnby.common.util.DateUtil;
import com.jnby.infrastructure.box.mapper.CouponRecordMapper;
import com.jnby.infrastructure.box.model.CouponRecord;
import com.jnby.infrastructure.box.model.CustomerDetails;
import com.jnby.infrastructure.box.model.Fashioner;
import com.jnby.module.order.event.ProducerMqEvent;
import com.jnbyframework.boot.api.ISysBaseAPI;
import com.jnbyframework.boot.common.api.dto.message.TemplateMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首次订阅并且绑定搭配师，发送搭配师消息
 */
@Component
@Slf4j
public class UserFirstSubscribeSendFashionerMsgRouter implements IBroadCastRouter {
    private static final String TEMPLATE_CODE = "USER_SUBSCRIBE_SEND_FASHIONER";

    @Resource
    private CouponRecordMapper couponRecordMapper;

    @Resource
    private ICustomerDetailsRepository iCustomerDetailsRepository;

    @Autowired
    private IFashionerRepository iFashionerRepository;

    @Autowired
    private ISysBaseAPI iSysBaseAPI;

    @Override
    public String getTags() {
        return MsgTagUtil.FIRST_SUBSCRIBE;
    }

    @Override
    public void consume(Message message) {

    }

    @Override
    public void consumeKillBillQueue(ProducerMqEvent event) {
        String para = null;
        String customerId = null;
        try {
            para = new String(event.getMessage(), "UTF-8");
            customerId = JSONObject.parseObject(para, String.class);
        } catch (UnsupportedEncodingException e) {

        }

        CustomerDetails customerDetails = iCustomerDetailsRepository.findById(customerId);
        if (customerDetails == null) return;

        CouponRecord couponRecord = new CouponRecord();
        couponRecord.setCustomerId(customerId);
        couponRecord.setSourceid(customerId);
        couponRecord.setUnionid(customerDetails.getUnionid());
        couponRecord.setType(CouponRecordEnum.Type.ydboxfirst.getCode().longValue());
        List<CouponRecord> couponRecords = couponRecordMapper.selectListBySelective(couponRecord);
        if (couponRecords.size() > 1) {
            return;
        }

        //如果搭配师ID为空，直接return
        if (customerDetails.getFashionerId() == null){
            return;
        }

        Fashioner fashioner = iFashionerRepository.findById(customerDetails.getFashionerId());
        if (fashioner == null){
            return;
        }

        if (couponRecords.isEmpty()){
            sendTemplateMsg(customerDetails, fashioner);
        }

        if (couponRecords.size() == 1){
            CouponRecord record = couponRecords.get(0);
            if (record.getCreateTime() == null) return;

            String nowDate = DateUtil.formatToStr(new Date(), DateUtil.DATEFORMATE_YYYY_MM_DD);
            String covertCreateDate = DateUtil.formatToStr(record.getCreateTime(), DateUtil.DATEFORMATE_YYYY_MM_DD);
            if (!nowDate.equals(covertCreateDate)){
                return;
            }
            sendTemplateMsg(customerDetails, fashioner);
        }

    }

    private void sendTemplateMsg(CustomerDetails customerDetails, Fashioner fashioner){
        TemplateMessageDTO messageDTO = new TemplateMessageDTO();
        messageDTO.setTemplateCode(TEMPLATE_CODE);
        Map<String, String> params = new HashMap<>();
        params.put("phone", customerDetails.getPhone());
        params.put("unionId", customerDetails.getUnionid());
        messageDTO.setTemplateParam(params);
        messageDTO.setFromUser("admin");
        messageDTO.setToUser(fashioner.getPhone());
        try {
            iSysBaseAPI.sendTemplateAnnouncement(messageDTO);
        }catch (Exception e){

        }
    }
}
