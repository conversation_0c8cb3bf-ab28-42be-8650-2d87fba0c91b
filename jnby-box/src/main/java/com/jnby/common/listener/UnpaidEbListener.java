package com.jnby.common.listener;

import com.jnby.common.listener.api.IMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 大内淘处理完成消息通知
 * <AUTHOR>
 * @Date 2023/05/12 10:06
 */
@Component
@Slf4j
@RefreshScope
public class UnpaidEbListener implements IMessageListener {
    @Value("${mq.retail.unpaideb.producer.topic}")
    private String topic;

    @Autowired
    private List<IBroadCastRouter> broadCastRouters;


    @Override
    public String getTopic() {
        return topic;
    }

    @Override
    public String getTags() {
        return "";
    }

    @Override
    public ConsumeConcurrentlyStatus consume(Message msg) {
        log.info("监听到大内淘消息通知 topic = {}, tags = {}, keys = {}", topic, msg.getTags(),msg.getKeys());
        broadCastRouters.forEach(item -> {
            List<String> tags = Arrays.asList(msg.getTags().split(","));
            if (tags.contains("*") || tags.contains(item.getTags())){
                log.info("item tags = {}, msg tags = {}", item.getTags(), tags);
                item.consume(msg);
            }
        });
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
