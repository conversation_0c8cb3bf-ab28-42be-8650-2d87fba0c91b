package com.jnby.common.listener.router.msg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.base.repository.ISysConfigRepository;
import com.jnby.common.enums.UltimaBoxDetailsStatusEnum;
import com.jnby.common.listener.router.msg.entity.DeliverMsgEntity;
import com.jnby.common.listener.router.msg.entity.SupplyDeliverMsgEntity;
import com.jnby.common.listener.router.msg.entity.WxOffMapEntity;
import com.jnby.common.listener.router.msg.service.CommonInfoService;
import com.jnby.common.listener.router.msg.service.DealMsgService;
import com.jnby.common.util.DateUtil;
import com.jnby.common.util.MsgTemplateUtil;
import com.jnby.config.MsgTemplateConfigProperties;
import com.jnby.infrastructure.box.mapper.BoxSupplyMapper;
import com.jnby.infrastructure.box.mapper.ExpressMapper;
import com.jnby.infrastructure.box.model.*;
import com.jnby.module.jic.service.IVoucherMemberService;
import com.jnby.module.message.service.ISysMessageService;
import com.jnby.module.order.enums.BoxTypeEnum;
import com.jnby.module.order.enums.ExpressTypeEnum;
import com.jnby.module.order.service.box.IBoxService;
import com.jnby.module.order.service.box.IExpressService;
import com.jnbyframework.boot.common.api.dto.message.TemplateMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.SimpleFormatter;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/3/29 10:41
 */
@Service
@Slf4j
public class BoxDealMsgServiceImpl implements DealMsgService {

    @Autowired
    private CommonInfoService commonInfoService;

    @Autowired
    private MsgTemplateConfigProperties msgTemplateConfigProperties;

    @Autowired
    private ISysMessageService iSysMessageService;

    @Autowired
    private IExpressService expressService;

    @Value("${msg.sendMsg.templateId}")
    private String SEND_MSG;

    @Value("${msg.signMsg.templateId}")
    private String SIGN_MSG;

    private final static String WAY = "自提";

    @Autowired
    private ExpressMapper expressMapper;

    @Autowired
    private ICustomerDetailsRepository iCustomerDetailsRepository;

    @Autowired
    private BoxSupplyMapper boxSupplyMapper;

    @Autowired
    private IBoxService boxService;

    @Autowired
    private ISysConfigRepository iSysConfigRepository;

    @Autowired
    private IVoucherMemberService voucherMemberService;


    @Override
    public List<Long> getType() {
        return Lists.newArrayList(BoxTypeEnum.SALESBOX.getCode(),
                BoxTypeEnum.FASHIONERBOX.getCode(),
                BoxTypeEnum.YDBOX.getCode(),
                BoxTypeEnum.THEMEBOX.getCode());
    }

    @Override
    public void sendCancelOrNoStockMsg(BoxWithBLOBs box, List<BoxDetailsWithBLOBs> details) {
        // 获取取消的商品
        List<BoxDetailsWithBLOBs> cancelDetails = details.stream().filter(e -> {
            return e.getStatus().equals(UltimaBoxDetailsStatusEnum.CANCEL.getCode().longValue()) ||
                    e.getStatus().equals(UltimaBoxDetailsStatusEnum.DELETE.getCode().longValue());
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(cancelDetails)) {
            return;
        }

        // 获取公众号信息
        WeiXinFans weiXinFans = commonInfoService.getWeiXinFans(box.getUnionid());

        // 查询用户信息
        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(box.getUnionid());

        // 区分整单取消和缺货
        if (cancelDetails.size() == details.size()) {
            cancelNotice(weiXinFans, box, cancelDetails, customerDetails);
        } else {
            noStockNotice(weiXinFans, box, cancelDetails, customerDetails);
        }
    }

    private void cancelNotice(WeiXinFans weiXinFans, BoxWithBLOBs box, List<BoxDetailsWithBLOBs> cancelDetails, CustomerDetails customerDetails) {
        TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
        if (weiXinFans == null) {
            //发送短信
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getOrderCancelTemplateId());
            templateMessageDTO.setToUser(customerDetails.getPhone());
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        } else {
            // 发送模板消息
            Map<String, String> map = new HashMap<>();
            map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.SUPPLY_FIRST_ALL_NONE);
            map.put(MsgTemplateUtil.MSG_KEYWORD2, MsgTemplateUtil.CANCEL_ORDER);
            SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
            map.put(MsgTemplateUtil.ORDER_TIME, formatter.format(box.getCreateTime()));
            map.put(MsgTemplateUtil.BOX_SN, box.getBoxSn());
            map.put(MsgTemplateUtil.BOX_ID, box.getId());
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getCommonOffTemplateId());
            templateMessageDTO.setToUser(weiXinFans.getOpenid());
            templateMessageDTO.setTemplateParam(map);
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        }
    }

    private void noStockNotice(WeiXinFans weiXinFans, Box box, List<BoxDetailsWithBLOBs> cancelDetails, CustomerDetails customerDetails) {

        TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
        String productName = cancelDetails.stream().map(BoxDetails::getProductName).collect(Collectors.joining(","));
        if (weiXinFans != null) {
            // 发送模板消息
            Map<String, String> map = new HashMap<>();
            if (cancelDetails.size() == 1) {
                map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.SUPPLY_FIRST_BEFORE_APART +
                        cancelDetails.get(0).getProductName() + MsgTemplateUtil.SUPPLY_FIRST_AFTER_APART);
            } else {
                map.put(MsgTemplateUtil.MSG_KEYWORD1, cancelDetails.get(0).getProductName() + MsgTemplateUtil.DENG
                        + cancelDetails.size() + MsgTemplateUtil.JIAN + MsgTemplateUtil.SUPPLY_FIRST_AFTER_APART);
            }
            map.put(MsgTemplateUtil.MSG_KEYWORD2, MsgTemplateUtil.NO_STOCK);
            SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
            map.put(MsgTemplateUtil.ORDER_TIME, formatter.format(box.getCreateTime()));
            map.put(MsgTemplateUtil.BOX_SN, box.getBoxSn());
            map.put(MsgTemplateUtil.BOX_ID, box.getId());
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getCommonOffTemplateId());
            templateMessageDTO.setToUser(weiXinFans.getOpenid());
            templateMessageDTO.setTemplateParam(map);
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        }
        if (weiXinFans == null) {
            //发送短信
            Map<String, String> map = new HashMap<>();
            map.put(MsgTemplateUtil.PRODUCT_NAME, productName);
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getOrderNoStockTemplateId());
            templateMessageDTO.setToUser(customerDetails.getPhone());
            templateMessageDTO.setTemplateParam(map);
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        }
    }

    @Override
    public void sendEbMsg(DeliverMsgEntity event) {
        try {
            WeiXinFans weiXinFans = commonInfoService.getWeiXinFans(event.getCustomerDetails().getUnionid());
            TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();

            // 查询用户快递
            List<Express> expressList = searchExpress(event);
            if (CollectionUtils.isEmpty(expressList)) {
                log.error("未找到相关的快递信息，boxId:{}", event.getBox().getId());
                return;
            }
            // 需要判断是补货还是发货信息
            String supplyId = event.getBoxDetails().stream().map(BoxDetailsWithBLOBs::getSupplyId)
                    .filter(org.apache.commons.lang.StringUtils:: isNotBlank).distinct().findFirst().orElse(null);
            if (StringUtils.isNotBlank(supplyId)) {
                // 补货消息
                SupplyDeliverMsgEntity entity = new SupplyDeliverMsgEntity();
                entity.setBox(event.getBox());
                entity.setAddress(event.getAddress());
                entity.setTrackingNumber(event.getLogisticsNumber());
                entity.setCompany(event.getCompany());
                sendSupplyMsg(entity);
            } else {
                // 发货消息
                if (weiXinFans != null) {
                    // 公众号消息 推送
                    Map<String, String> map = new HashMap<>();
                    if (expressList.size() == 1) {
                        map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.EB_SEND_BOX_FIRST);
                    } else {
                        map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.EB_SEND_BOX_FIRST_3 +
                                expressList.size() + MsgTemplateUtil.EB_SEND_BOX_FIRST_2);
                    }
                    map.put(MsgTemplateUtil.MSG_KEYWORD2, MsgTemplateUtil.SEND_EXPRESS);
                    commonInfoService.buildMap(WxOffMapEntity.buildByDelivery(event), map);
                    templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getCommonOffTemplateId());
                    templateMessageDTO.setToUser(weiXinFans.getOpenid());
                    templateMessageDTO.setTemplateParam(map);
                    iSysMessageService.sendMessageByTemplate(templateMessageDTO);
                }

                if (weiXinFans == null) {
                    // 短信推送 自提提醒/发货提醒
                    Map<String, String> map = new HashMap<>();
                    map.put(MsgTemplateUtil.PARAM, event.getCompany() + MsgTemplateUtil.DELIVER);
                    templateMessageDTO.setTemplateParam(map);
                    if (expressList.size() > 1) {
                        map.put(MsgTemplateUtil.NUM, Objects.toString(expressList.size()));
                        templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getDeliverNumSmsTemplateId());
                    } else {
                        templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getDeliverSmsTemplateId());
                    }
                    templateMessageDTO.setToUser(event.getCustomerDetails().getPhone());
                    iSysMessageService.sendMessageByTemplate(templateMessageDTO);
                }
            }

        } catch (Exception e) {
            log.error("处理内淘BOX发送消息 异常 e: {}  message {}", e, e.getMessage());
        }
    }

    @Override
    public void sendDeliveryMsg(DeliverMsgEntity event) {
        // 发送box消息
        // 1、组装消息
        try {
            WeiXinFans weiXinFans = commonInfoService.getWeiXinFans(event.getCustomerDetails().getUnionid());
            TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();

            //根据快递单号和boxId查询快递信息
            List<Express> expressList = searchExpress(event);
            if (CollectionUtils.isEmpty(expressList)) {
                log.error("未找到相关的快递信息，boxId:{}", event.getBox().getId());
                return;
            }

            // 自提不发短信和公众号
            if (StringUtils.isNotEmpty(event.getLogisticsNumber()) && StringUtils.equals(event.getLogisticsNumber(), WAY)) {
                return;
            }

            if (weiXinFans != null) {
                // 公众号消息 推送
                Map<String, String> map = new HashMap<>();
                if (expressList.size() == 1) {
                    map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.EB_SEND_BOX_FIRST);
                } else {
                    map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.EB_SEND_BOX_FIRST_3 +
                            expressList.size() + MsgTemplateUtil.EB_SEND_BOX_FIRST_2);
                }
                map.put(MsgTemplateUtil.MSG_KEYWORD2, MsgTemplateUtil.SEND_EXPRESS);
                commonInfoService.buildMap(WxOffMapEntity.buildByDelivery(event), map);
                templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getCommonOffTemplateId());
                templateMessageDTO.setToUser(weiXinFans.getOpenid());
                templateMessageDTO.setTemplateParam(map);
                iSysMessageService.sendMessageByTemplate(templateMessageDTO);
            }
            if (weiXinFans == null) {
                // 短信推送 自提提醒/发货提醒
                Map<String, String> map = new HashMap<>();
                map.put(MsgTemplateUtil.PARAM, event.getCompany() + MsgTemplateUtil.DELIVER);
                templateMessageDTO.setTemplateParam(map);
                if (expressList.size() > 1) {
                    map.put(MsgTemplateUtil.NUM, Objects.toString(expressList.size()));
                    templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getDeliverNumSmsTemplateId());
                } else {
                    templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getDeliverSmsTemplateId());
                }
                templateMessageDTO.setToUser(event.getCustomerDetails().getPhone());
                iSysMessageService.sendMessageByTemplate(templateMessageDTO);
            }
        } catch (Exception e) {
            log.error("处理BOX发货后消息 异常 e: {}  message {}", e, e.getMessage());
        }
    }

    private List<Express> searchExpress(DeliverMsgEntity event) {
        List<ExpressTypeEnum> enums = new ArrayList<>();
        enums.add(ExpressTypeEnum.box);
        enums.add(ExpressTypeEnum.order);
        enums.add(ExpressTypeEnum.change);
        enums.add(ExpressTypeEnum.guideEb);
        enums.add(ExpressTypeEnum.supplyGuideEb);
        List<Express> list = expressService.getExpressIdByBoxIdAndTypeByList(event.getBox().getId(), enums);
        if (CollectionUtils.isNotEmpty(list)) {
            list = list.stream().filter(v -> StringUtils.isNotBlank(v.getExpressNo())).collect(Collectors.toList());
        }
        return  list;
    }

    @Override
    public void sendSignInOneDayMsg(BoxWithBLOBs box) {
        try {
            // 查询用户公众号信息
            WeiXinFans fans = commonInfoService.getWeiXinFans(box.getUnionid());

            // 查询用户信息
            CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(box.getUnionid());


            TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
            if (fans != null) {
                // 发送模板消息
                // 公众号消息 推送
                Map<String, String> map = new HashMap<>();

                map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.SIGN_IN_ONE_DAY_FIRST);
                map.put(MsgTemplateUtil.MSG_KEYWORD2, MsgTemplateUtil.SIGN_IN_ONE_K2);
                SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
                map.put(MsgTemplateUtil.ORDER_TIME, formatter.format(box.getCreateTime()));
                map.put(MsgTemplateUtil.BOX_SN, box.getBoxSn());
                map.put(MsgTemplateUtil.BOX_ID, box.getId());
                templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getCommonOffTemplateId());
                templateMessageDTO.setToUser(fans.getOpenid());
                templateMessageDTO.setTemplateParam(map);
                iSysMessageService.sendMessageByTemplate(templateMessageDTO);
            }

            if (fans == null && StringUtils.isNotBlank(customerDetails.getPhone())) {
                //发送短信
                templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getSignInOneTemplateId());
                templateMessageDTO.setToUser(customerDetails.getPhone());
                iSysMessageService.sendMessageByTemplate(templateMessageDTO);
            }
        } catch (Exception e) {
            log.error("BOX签收第2天报错, e = {}, boxSn = {}", e, box.getBoxSn());
        }
    }

    @Override
    public void sendOverdueNonReturnMsg(BoxWithBLOBs box, List<BoxDetailsWithBLOBs> details) {
        WeiXinFans fans = commonInfoService.getWeiXinFans(box.getUnionid());

        // 查询用户信息
        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(box.getUnionid());

        // 发送模板消息
        TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
        if (fans != null && details.size() > 0) {
            Map<String, String> map = new HashMap<>();
            map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.NO_RETURN_K1);
            map.put(MsgTemplateUtil.MSG_KEYWORD2, MsgTemplateUtil.NO_RETURN);
            SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
            map.put(MsgTemplateUtil.ORDER_TIME, formatter.format(box.getCreateTime()));
            map.put(MsgTemplateUtil.BOX_SN, box.getBoxSn());
            map.put(MsgTemplateUtil.BOX_ID, box.getId());
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getCommonOffTemplateId());
            templateMessageDTO.setToUser(fans.getOpenid());
            templateMessageDTO.setTemplateParam(map);
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        }

        if (fans == null && customerDetails != null) {
            //发送短信
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getOverdueNoReturnTemplateId());
            templateMessageDTO.setToUser(customerDetails.getPhone());
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        }
    }

    @Override
    public void sendSignInThreeDayMsg(BoxWithBLOBs box, List<BoxDetailsWithBLOBs> details) {
        WeiXinFans fans = commonInfoService.getWeiXinFans(box.getUnionid());
        // 查询用户信息
        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(box.getUnionid());
        // 发送模板消息
        TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
        if (fans != null) {
            // 公众号消息 推送
            Map<String, String> map = new HashMap<>();
            map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.SIGN_IN_THREE_DAY_MSG);
            map.put(MsgTemplateUtil.MSG_KEYWORD2, MsgTemplateUtil.SIGN_IN_THREE_K2);
            SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
            map.put(MsgTemplateUtil.ORDER_TIME, formatter.format(box.getCreateTime()));
            map.put(MsgTemplateUtil.BOX_SN, box.getBoxSn());
            map.put(MsgTemplateUtil.BOX_ID, box.getId());
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getCommonOffTemplateId());
            templateMessageDTO.setToUser(fans.getOpenid());
            templateMessageDTO.setTemplateParam(map);
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        } else if (fans == null && customerDetails != null) {
            //发送短信
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getUNRETURN_TO_CUSTOMER_SMS_MSG());
            templateMessageDTO.setToUser(customerDetails.getPhone());
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        }
    }

    @Override
    public void sendTurnToBeltReturnMsg(BoxWithBLOBs box, List<BoxDetailsWithBLOBs> details) {
        WeiXinFans fans = commonInfoService.getWeiXinFans(box.getUnionid());
        // 查询用户信息
        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(box.getUnionid());
        // 发送模板消息
        TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
        if (fans != null) {

            // 公众号消息 推送
            Map<String, String> map = new HashMap<>();

            map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.SIGN_IN_SEVEN_DAY_MSG);
            map.put(MsgTemplateUtil.MSG_KEYWORD2, MsgTemplateUtil.SEVEN_DAY_NO_RETURN);
            SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
            map.put(MsgTemplateUtil.ORDER_TIME, formatter.format(box.getCreateTime()));
            map.put(MsgTemplateUtil.BOX_SN, box.getBoxSn());
            map.put(MsgTemplateUtil.BOX_ID, box.getId());
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getCommonOffTemplateId());
            templateMessageDTO.setToUser(fans.getOpenid());
            templateMessageDTO.setTemplateParam(map);
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        } else if (fans == null && customerDetails != null) {
            //发送短信
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getUNRETURN_DEADLINE_TO_CUSTOMER_SMS_MSG());
            templateMessageDTO.setToUser(customerDetails.getPhone());
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        }
    }

    @Override
    public void sendBeReturnTwoDayMsg(BoxWithBLOBs box, List<BoxDetailsWithBLOBs> details) {
        WeiXinFans weiXinFans = commonInfoService.getWeiXinFans(box.getUnionid());
        // 查询用户信息
        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(box.getUnionid());
        // 发送模板消息
        TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
        if (weiXinFans != null) {
            // 公众号消息 推送
            Map<String, String> map = new HashMap<>();
            map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.BE_RETURN_K1);
            map.put(MsgTemplateUtil.MSG_KEYWORD2, MsgTemplateUtil.NO_RETURN);
            SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
            map.put(MsgTemplateUtil.ORDER_TIME, formatter.format(box.getCreateTime()));
            map.put(MsgTemplateUtil.BOX_SN, box.getBoxSn());
            map.put(MsgTemplateUtil.BOX_ID, box.getId());
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getCommonOffTemplateId());
            templateMessageDTO.setToUser(weiXinFans.getOpenid());
            templateMessageDTO.setTemplateParam(map);
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        } else if (weiXinFans == null && customerDetails != null) {
            //发送短信
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getSIGN_IN_TEN_DAY_SMS_MSG());
            templateMessageDTO.setToUser(customerDetails.getPhone());
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        }
    }

    @Override
    public void sendReturnOneDayMsg(BoxReturn boxReturn, BoxWithBLOBs box, List<BoxDetailsWithBLOBs> details) {
        WeiXinFans weiXinFans = commonInfoService.getWeiXinFans(box.getUnionid());
        // 查询用户信息
        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(box.getUnionid());

        // 判断是否填写了退回问卷
        if (box.getIfFeedback() == 1) {
            return;
        }
        // 获取是否有优惠券
        SysConfig sysConfig = iSysConfigRepository.selectForBoxService();
        Long rebate = sysConfig.getRebate();
        CouponSetting voucher = null;
        if (StringUtils.isNotBlank(sysConfig.getCouponId())) {
            voucher = voucherMemberService.getVoucherById(sysConfig.getCouponId());
        }

        // 发送模板消息
        TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
        String voucherStr = "";
        if (voucher != null && voucher.getType() == 1) {
            voucherStr = voucher.getDiscount() + MsgTemplateUtil.ZHE;
        } else if (voucher != null) {
            voucherStr = voucher.getAmount() + MsgTemplateUtil.YUAN;
        }

        if (weiXinFans != null) {
            HashMap<String, String> map = new HashMap<>();
            if (java.util.Objects.equals(rebate, 1L) && voucher != null) {

                map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.BOX_REMARK_FOR_QUESTIONNAIRE_COUPON
                        + voucherStr + MsgTemplateUtil.BOX_REMARK_FOR_QUESTIONNAIRE_COUPON_1);

            } else {
                map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.BOX_REMARK_FOR_QUESTIONNAIRE);
            }
            map.put(MsgTemplateUtil.MSG_KEYWORD2, MsgTemplateUtil.WAIT_REQUEST);
            SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
            map.put(MsgTemplateUtil.ORDER_TIME, formatter.format(box.getCreateTime()));
            map.put(MsgTemplateUtil.BOX_SN, box.getBoxSn());
            map.put(MsgTemplateUtil.BOX_ID, box.getId());
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getCommonOffTemplateId());
            templateMessageDTO.setToUser(weiXinFans.getOpenid());
            templateMessageDTO.setTemplateParam(map);
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        } else if (weiXinFans == null && customerDetails != null) {
            //发送短信
            if (java.util.Objects.equals(rebate, 1L)) {
                // 1L代表返券
                HashMap<String, String> map = new HashMap<>();
                map.put(MsgTemplateUtil.COUPON, voucherStr);
                templateMessageDTO.setTemplateParam(map);
                templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getQUESTIONNAIRE_COUPON_SMS_MSG());
            } else {
                templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getQUESTIONNAIRE_SMS_MSG());
            }
            templateMessageDTO.setToUser(customerDetails.getPhone());
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        }
    }


    // 处理补货消息
    public void sendSupplyMsg(SupplyDeliverMsgEntity event) {
        WeiXinFans fans = commonInfoService.getWeiXinFans(event.getBox().getUnionid());

        // 查询用户信息
        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(event.getBox().getUnionid());
        TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
        if (fans != null) {
            // 公众号消息 推送
            Map<String, String> map = new HashMap<>();
            map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.TRACK_SUPPLY_FIRST);
            map.put(MsgTemplateUtil.MSG_KEYWORD2, MsgTemplateUtil.SEND_EXPRESS);
            commonInfoService.buildMap(WxOffMapEntity.buildBySupplyDelivery(event), map);
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getCommonOffTemplateId());
            templateMessageDTO.setToUser(fans.getOpenid());
            templateMessageDTO.setTemplateParam(map);
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        }

        if (fans == null) {
            Map<String, String> map = new HashMap<>();
            map.put(MsgTemplateUtil.MSG_TRACE_COMPANY, event.getCompany());
            templateMessageDTO.setTemplateParam(map);
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getSupplySmsTemplateId());
            templateMessageDTO.setToUser(customerDetails.getPhone());
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        }
    }

    // 补货签收第二天发送信息
    public void sendSupplySignInOneDayMsg(String supplyBoxId) {
        log.info("sendSupplySignInOneDayMsg 入参：{}", supplyBoxId);
        // 查询boxSupply信息
        QueryWrapper<BoxSupply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ID", supplyBoxId);
        BoxSupply boxSupply = boxSupplyMapper.selectOne(queryWrapper);
        if (boxSupply == null) {
            log.error("sendSupplySignInOneDayMsg获取补货数据为空， supplyBoxId{}", supplyBoxId);
            return;
        }

        // 查询Box信息
        BoxWithBLOBs box = boxService.findById(boxSupply.getBoxId());
        if (box == null) {
            log.error("sendSupplySignInOneDayMsg获取服务单数据为空，BoxId{}", boxSupply.getBoxId());
            return;
        }

        WeiXinFans fans = commonInfoService.getWeiXinFans(box.getUnionid());


        // 查询用户信息
        CustomerDetails customerDetails = iCustomerDetailsRepository.findByUnionId(box.getUnionid());


        // 发送模板消息
        TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
        if (fans != null) {
            HashMap<String, String> map = new HashMap<>();
            map.put(MsgTemplateUtil.MSG_KEYWORD1, MsgTemplateUtil.SUPPLY_SIGN_IN_ONE_DAY_FIRST);
            map.put(MsgTemplateUtil.MSG_KEYWORD2, MsgTemplateUtil.SIGN_IN_ONE_K2);
            SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
            map.put(MsgTemplateUtil.ORDER_TIME, formatter.format(box.getCreateTime()));
            map.put(MsgTemplateUtil.BOX_SN, box.getBoxSn());
            map.put(MsgTemplateUtil.BOX_ID, box.getId());
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getCommonOffTemplateId());
            templateMessageDTO.setToUser(fans.getOpenid());
            templateMessageDTO.setTemplateParam(map);
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        } else if (fans == null && customerDetails != null) {
            //发送短信
            templateMessageDTO.setTemplateCode(msgTemplateConfigProperties.getSIGNIN_ONEDAY_TO_CUSTOMER_SMS_MSG());
            templateMessageDTO.setToUser(customerDetails.getPhone());
            iSysMessageService.sendMessageByTemplate(templateMessageDTO);
        }
    }
}
