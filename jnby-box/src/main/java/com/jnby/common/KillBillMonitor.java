package com.jnby.common;


import com.jnby.common.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.killbill.CreatorName;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.commons.concurrent.Executors;
import org.killbill.queue.api.PersistentQueueConfig;
import org.skife.jdbi.v2.exceptions.DBIException;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class KillBillMonitor {
    // Deferred in start sequence to allow for restart, which is not possible after the shutdown (mostly for test purpose)
    private ExecutorService executor;

    private static final long ONE_MILLION = 1000L * 1000L;

    /**
     * 最大休眠时间
     */
    private static final long MAX_SLEEP_TIME_MS = 100;

    /**
     * 配置
     */
    private PersistentQueueConfig config;

    /**
     * 是否启动监视器
     */
    private volatile boolean isProcessingMonitor;

    /**
     * 事件总线
     */
    private DefaultPersistentBus defaultPersistentBus;

    /**
     * 休眠时间 自定义配置还是取配置的轮询时间
     */
    private long sleepTimeMs = 0;

    /**
     * 阈值数量  可配置
     */
    private long limitCount = 1;

    /**
     * 创建者名字
     */
    private String creatorName;


    // TODO: 2024/2/18  按照应用业务 进行阈值报警（业务+ip+表名）

    public KillBillMonitor() {
    }


    public KillBillMonitor(DefaultPersistentBus defaultPersistentBus) {
        this.defaultPersistentBus = defaultPersistentBus;
        this.config = defaultPersistentBus.getConfig();
    }

    public void startMonitor() {
        this.creatorName = CreatorName.get();
        this.executor = Executors.newFixedThreadPool(1, config.getTableName() + "-monitor-lifecycle-th");
        log.info("kill-bill queue Monitor Starting...");
        isProcessingMonitor = true;
        // 初始化 一个守护线程
        executor.execute(new KillBillMonitor.DispatcherRunnable());
    }


    public void stopMonitor() {
        this.isProcessingMonitor = false;
        executor.shutdown();
        try {
            executor.awaitTermination(5, TimeUnit.SECONDS);
        } catch (final InterruptedException e) {
            log.info("kill-bill queue Monitor异常 Stop  has been interrupted");
        } finally {
            log.warn("kill-bill queue Monitor Stopped");
        }
    }

    /**
     * 守护线程
     */
    private final class DispatcherRunnable implements Runnable {
        @Override
        public void run() {

            try {
                log.info("kill-bill queue Monitor Thread {}-dispatcher [{}] starting ",
                        Thread.currentThread().getName(),
                        Thread.currentThread().getId());

                while (true) {

                    if (!isProcessingMonitor) {
                        break;
                    }
                    withHandlingRuntimeException(new RunnableRawCallback() {
                        @Override
                        public void callback() throws InterruptedException {
                            final long beforeLoop = System.nanoTime();
                            getNbCount();
                            final long afterLoop = System.nanoTime();

                            sleepSporadically((afterLoop - beforeLoop) / ONE_MILLION);
                        }
                    });
                }
            } catch (final InterruptedException e) {
                log.info("kill-bill queue Monitor异常 Thread {} got interrupted, exiting... ", Thread.currentThread().getName());
            } catch (final Error e) {
                log.error("kill-bill queue Monitor异常 Thread {} got an exception, exiting... ", Thread.currentThread().getName(), e);
            } finally {
                log.info("kill-bill queue Monitor异常 Thread {} has exited", Thread.currentThread().getName());
            }
        }

        private void sleepSporadically(final long loopTimeMsec) throws InterruptedException {
            long remainingSleepTime = config.getPollingSleepTimeMs() - loopTimeMsec;
            while (remainingSleepTime > 0) {
                final long curSleepTime = remainingSleepTime > MAX_SLEEP_TIME_MS ? MAX_SLEEP_TIME_MS : remainingSleepTime;
                Thread.sleep(curSleepTime);
                remainingSleepTime -= curSleepTime;
            }
        }
    }

    private void getNbCount() {
        long count = defaultPersistentBus.getNbReadyEntries(new DateTime());
        //  log.info("统计数量NbReadyEntriesCount:{} creatorName:{}", count, creatorName);
        if (count >= limitCount) {
            log.error("统计数量超过阈值NbReadyEntriesCount:{} limitCount:{} creatorName:{} realIp:{}", count, limitCount, creatorName, IpUtil.getRealIp());
        }
    }


    private interface RunnableRawCallback {
        void callback() throws InterruptedException;
    }


    private void withHandlingRuntimeException(final KillBillMonitor.RunnableRawCallback cb) throws InterruptedException {
        try {
            cb.callback();
        } catch (final DBIException e) {
            log.error("kill-bill queue Monitor异常 Thread {} got DBIException exception: ", Thread.currentThread().getName(), e);
        } catch (final RuntimeException e) {
            log.error("kill-bill queue Monitor异常 Thread {} got Runtime exception: ", Thread.currentThread().getName(), e);
        }
    }
}
