package com.jnby.common.util;

/**
 *  品牌格式化工具
 */
public class ParseBrandUtil {

    // 商品品牌属性转为会员品牌
    public static String parseBrandToVip(String brand) {
        switch (brand) {
            case "LESS":
                return "less";
            case "童装":
                return "jnby by JNBY";
            case "蓬马":
                return "Pomme de terre";
            case "Reverb":
                return "REVERB";
            case "LASU":
                return "LASU MIN SOLA";
            case "A PERSONAL NOTE 73":
                return "APN73";
            default:
                return brand;
        }
    }


    public static String parseProductBrand2VipBrand(String brand) {
        switch (brand) {
            case "LESS":
                return "less";
            case "LASU":
                return "江南布衣+";
            case "A PERSONAL NOTE 73":
                return "APN73";
            default:
                return brand;
        }
    }

}
