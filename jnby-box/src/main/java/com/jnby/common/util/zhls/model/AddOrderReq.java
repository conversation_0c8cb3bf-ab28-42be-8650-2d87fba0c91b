package com.jnby.common.util.zhls.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单添加/变更
 */
@Data
public class AddOrderReq implements Serializable {
    private String dataSourceId;
    private List<Orders>  orders;

    @Data
    public static class Orders{
        //商家订单号
        private String external_order_id;
        //订单创建时间，unix时间戳 字段长度为 13 字节
        private String create_time;

        private String order_source = "wxapp";
        private int order_type = 1;

        //商品总数量
        private int goods_num_total;

        //商品总金额，单位默认为元
        private float goods_amount_total;

        //订单运费，单位默认为元 注：运费为0时，传0.00
        private float freight_amount = 0;

        //订单金额，单位默认为元 注：商品总金额+运费金额=订单金额
        private float order_amount;

        //订单应付金额，单位默认为元 注：订单金额-订单级别的优惠金额（如：订单满减）=订单应付金额
        private float payable_amount;

        //实付金额，单位默认为元 注：订单应付金额-支付优惠金额（如：微信支付优惠、招商银行优惠等）=订单实付金额
        private float payment_amount;

        //主订单状态，1110待支付，1130未支付取消，1140已支付未发货取消，1150已支付待发货/已支付待核销，1160已发货，1180销售完成/已收货/已核销
        private String order_status;

        //状态变更时间，unix毫秒级时间，如 order_status状态为 1150 ，则传 1150（已支付待发货）状态变更的时间
        private String status_change_time;

        private UserInfo user_info;

        private List<GoodsInfo> goods_info;

        private List<PaymentInfo> payment_info;
        @Data
        public static class UserInfo{
            private String open_id;
        }
        @Data
        public static class GoodsInfo{
            //sku 编号
            private String external_sku_id;
            private String sku_name_chinese;

            //单件商品原价，单位默认为元
            private float goods_amount;

            //多件商品实付金额（分摊了优惠的金额）,单位默认为元，注：有数GMV计算使用该字段
            private float payment_amount;

            //sku 所属 spu 编号，若无 spu，传输内容请与 external_sku_id 保持一致
            private String external_spu_id;

            private String spu_name_chinese;

            private int goods_num;
        }
        @Data
        public static class PaymentInfo{
            private String combine_out_trade_no;
            private String out_trade_number;
            private String payment_type = "00009";
            private String trans_id;
            private float trans_amount;//元
        }
    }
}
