package com.jnby.common.enums;

public enum BCusServiceAutoAskBoxStatusEnum {

    CANCEL(0, "已取消"),
    UN_FINISH(1, "待完善"),
    CREATED(2, "已创建"),
    ON_LOADING(3, "待处理"),

    ;


    public static String getDesc(Integer code){
        BCusServiceAutoAskBoxStatusEnum[] values = BCusServiceAutoAskBoxStatusEnum.values();
        for (BCusServiceAutoAskBoxStatusEnum value : values) {
            boolean equals = value.getCode().equals(code);
            if(equals){
                return value.getDesc();
            }
        }
        return "";
    }

    private Integer code;
    private String desc;

    BCusServiceAutoAskBoxStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
