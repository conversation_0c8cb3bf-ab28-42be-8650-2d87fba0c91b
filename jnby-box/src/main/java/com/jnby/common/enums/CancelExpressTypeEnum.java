package com.jnby.common.enums;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/7/914:38
 */
public enum CancelExpressTypeEnum {

    RETURN_PRODUCT(4,"还货"),
    REFUND(5,"退款"),
    ;

    private Integer code;
    private String desc;

    CancelExpressTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CancelExpressTypeEnum find(Integer code){
        CancelExpressTypeEnum[] enums = CancelExpressTypeEnum.values();
        CancelExpressTypeEnum cancelExpressTypeEnum = null;

        for (int i = 0; i < enums.length; i++) {
            if (enums[i].getCode().equals(code)){
                cancelExpressTypeEnum = enums[i];
            }
        }
        return cancelExpressTypeEnum;
    }

    public static List<CancelExpressTypeEnum> findAll(){
        CancelExpressTypeEnum[] enums = CancelExpressTypeEnum.values();
        return Arrays.asList(enums);
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String toString() {
        return "CancelExpressTypeEnum{" +
                "code=" + code +
                ", desc='" + desc + '\'' +
                '}';
    }
}
