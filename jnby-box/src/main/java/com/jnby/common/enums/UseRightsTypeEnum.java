package com.jnby.common.enums;
/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/23/21 11:53 AM
 */
public enum UseRightsTypeEnum {
    HOLD(1, "占用"),
    USED(2, "使用"),
    RECOARY(3, "恢复"),
    ;

    private Integer code;
    private String desc;


    UseRightsTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UseRightsTypeEnum find(Integer code){
        UseRightsTypeEnum[] enums = UseRightsTypeEnum.values();
        UseRightsTypeEnum  typeEnum = null;

        for (int i = 0; i < enums.length; i++) {
            if (enums[i].getCode().equals(code)){
                typeEnum = enums[i];
            }
        }
        return typeEnum;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
