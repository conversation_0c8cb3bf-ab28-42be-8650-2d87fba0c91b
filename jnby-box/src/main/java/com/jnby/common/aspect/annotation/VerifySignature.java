package com.jnby.common.aspect.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 *
 * <AUTHOR>
 * @description: 外部叫快递验签注解
 * @date 2023/05/09
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface VerifySignature {
     String urlPath() default "";
     String method() default "";
}