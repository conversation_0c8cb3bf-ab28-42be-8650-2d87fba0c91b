package com.jnby.common.aspect;

import com.jnby.application.openapi.request.SignatureReq;
import com.jnby.common.CommonRequest;
import com.jnby.common.aspect.annotation.VerifySignature;
import com.jnby.module.zto.util.SignatureUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @description: 外部叫快递接口验签切面
 * @date 2023/05/09
 */
@Aspect
@Component
@RefreshScope
public class SignatureVerifyAspect {

     @Value("${eb.express.secret}")
     private String ebExpressSecret;

     @Pointcut("@annotation(com.jnby.common.aspect.annotation.VerifySignature)")
     public void verifySignatureAnnotation() {}
     @Before("verifySignatureAnnotation()")
     public void verifySignature(JoinPoint joinPoint) throws Throwable {
          Object[] args = joinPoint.getArgs();
          if (args == null || args.length == 0) {
               throw new IllegalArgumentException("Invalid request: missing signature parameter!");
          }
          CommonRequest<SignatureReq> request = (CommonRequest<SignatureReq>)args[0];
          SignatureReq requestData = request.getRequestData();
          // 秘钥校验
          String[] split = ebExpressSecret.split(",");
          List<String> secretList = Arrays.asList(split);
          if(!secretList.contains(requestData.getSecret())){
               throw new RuntimeException("unauthorized secret key!");
          }
          Method method = getMethod(joinPoint);
          VerifySignature verifySignatureAnnotation = method.getAnnotation(VerifySignature.class);
          String urlPath = verifySignatureAnnotation.urlPath();
          String methodName = verifySignatureAnnotation.method();
          if(!requestData.getSignature().equals(SignatureUtils.generateSignature(requestData.getCheckParams(),requestData.getSecret(),methodName,urlPath,requestData.getCurrentDate()))){
               throw new IllegalArgumentException("Invalid signature!");
          }
     }
     private Method getMethod(JoinPoint joinPoint) {
          Object target = joinPoint.getTarget();
          String methodName = joinPoint.getSignature().getName();
          Class<?>[] parameterTypes = Arrays.stream(joinPoint.getArgs()).map(Object::getClass).toArray(Class[]::new);
          try {
               return target.getClass().getMethod(methodName, parameterTypes);
          } catch (NoSuchMethodException e) {
               throw new IllegalArgumentException(e);
          }
     }
}
