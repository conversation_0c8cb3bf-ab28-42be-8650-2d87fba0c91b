/**
 *
 */
package com.jnby.common.leaf.support;

import com.jnby.common.leaf.IdLeafService;
import com.jnby.common.leaf.baidu.fsg.uid.impl.DefaultUidGenerator;
import org.apache.commons.lang3.time.FastDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowCallbackHandler;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.Date;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 *
 */
public class DbIdLeafServiceImpl implements IdLeafService {
	private DefaultUidGenerator defaultUidGenerator;

	public void setDefaultUidGenerator(DefaultUidGenerator defaultUidGenerator){
		this.defaultUidGenerator=defaultUidGenerator;
	}


	@Override
	public String getId() {
		return String.valueOf(defaultUidGenerator.getUID());
	}

}
