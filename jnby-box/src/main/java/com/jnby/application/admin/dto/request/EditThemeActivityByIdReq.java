package com.jnby.application.admin.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Auther: lwz
 * @Date: 2022/4/19 17:43
 * @Description: EditThemeActivityByIdReq
 * @Version 1.0.0
 */
@Data
@ApiModel(value = "修改主题活动 请求信息 入参", description = "修改主题活动 请求信息相关入参")
public class EditThemeActivityByIdReq {
    @ApiModelProperty(value = "活动id", required = true)
    @NotBlank(message = "活动id不能为空")
    private String id;

    @ApiModelProperty(value = "活动名字", required = true)
    @NotBlank(message = "活动名字不能为空")
    private String activityName;

    @ApiModelProperty(value = "参与品牌id", required = true)
    @NotBlank(message = "参与品牌id不能为空")
    private String joinBrandId;

    @ApiModelProperty(value = "参与品牌名字", required = true)
    @NotBlank(message = "参与品牌名字不能为空")
    private String joinBrandName;

    @ApiModelProperty(value = "封面图", required = true)
    @NotBlank(message = "封面图不能为空")
    private String coverPicture;


    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "场景码ID")
    private String scenesId;

    @ApiModelProperty(value = "素材id", required = true)
    @NotBlank(message = "素材id不能为空")
    private String materialId;


    @ApiModelProperty(value = "排序标识")
    private Long sortNo;

    @ApiModelProperty(value = "显示标识（0 不显示 1显示）")
    private Long miniAppShow;


}
