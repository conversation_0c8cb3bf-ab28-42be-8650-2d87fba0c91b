package com.jnby.application.admin.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR> null
 */
@ApiModel
@Data
public class BActivityDetailRecordReq implements Serializable {

    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动ID，不能为空")
    private String activityId;

    @ApiModelProperty(value = "BOX服务单号/有搭订单号根据活动ID判断，可以为空")
    private String orderNo;

    @ApiModelProperty(value = "物流单号")
    private String logisticsNo;

    @ApiModelProperty(value = "集团卡号")
    private String jnbyCardno;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "奖励类型 1:优惠券 2 实物")
    private Integer rewardType;

    private static final long serialVersionUID = 1L;


}