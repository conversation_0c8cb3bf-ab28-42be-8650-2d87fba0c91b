package com.jnby.application.admin.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class HandSignAndReceiveReq {


    @ApiModelProperty(value = "物流主键ID ")
    @NotBlank(message = "物流主键id必传 ")
    private String expressId;

    @ApiModelProperty(value = "BOXID")
    private String boxId;


    @ApiModelProperty(value = "补货单主键ID")
    private String supId;

}
