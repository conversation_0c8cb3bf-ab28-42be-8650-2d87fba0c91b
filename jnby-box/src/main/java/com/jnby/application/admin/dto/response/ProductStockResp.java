package com.jnby.application.admin.dto.response;
import com.jnby.common.BaseSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProductStockResp extends BaseSerializable {
    private Long skuId;
    @ApiModelProperty("总库存")
    private long stock;
    private boolean ifEb;
    @ApiModelProperty("千万仓库存")
    private Long qwcStock = 0L;
    @ApiModelProperty("城市仓库存")
    private Long cscStock = 0L;
    @ApiModelProperty("门店库存")
    private Long storeStock = 0L;
}
