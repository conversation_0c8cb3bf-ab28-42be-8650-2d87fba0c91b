package com.jnby.application.admin.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class WaitPerfectListReq {

    @ApiModelProperty(value = "最外层对象的主键id查询")
    private String id;

    @ApiModelProperty(value = "款号精确查询")
    private String productCode;

    @ApiModelProperty(value = "来源   默认全部不传参数   1  导购搭配   2 搭配师搭配 ")
    private Integer source;

    @ApiModelProperty(value = "品牌id查询")
    private Long arcBrandId;

    @ApiModelProperty(value = "年份列表筛选")
    private List<String> yearsList;

    @ApiModelProperty(value = "季节id列表筛选")
    private List<Long> seasonIdList;

    @ApiModelProperty(value = "波段列表筛选")
    private List<String> bandList;

    @ApiModelProperty(value = "大类列表筛选")
    private List<Long> bigCateIdList;

    @ApiModelProperty(value = "小类列表筛选")
    private List<Long> smallCateIdList;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endTime;
}
