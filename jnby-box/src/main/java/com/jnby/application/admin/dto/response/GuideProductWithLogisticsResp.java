package com.jnby.application.admin.dto.response;

import com.jnby.base.entity.SFLogisticsEntity;
import com.jnby.infrastructure.box.model.BoxDetailsWithBLOBs;
import com.jnby.infrastructure.box.model.Express;
import com.jnby.infrastructure.box.model.Logistics;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class GuideProductWithLogisticsResp implements Serializable {
    @ApiModelProperty(value = "商品信息")
    private List<BoxDetailsWithBLOBs> boxDetailsWithBLOBsList;

    @ApiModelProperty(value = "发货地址")
    private SFLogisticsEntity send;

    @ApiModelProperty(value = "收货地址")
    private SFLogisticsEntity receive;

    @ApiModelProperty(value = "express")
    private Express express;

    @ApiModelProperty(value = "客户地址Id")
    private String customerLogisticsId;

    @ApiModelProperty(value = "快递员工号")
    private String empCode;

    @ApiModelProperty(value = "快递员手机号")
    private String empPhone;

    @ApiModelProperty(value = "预约取件时间")
    private String getDate;

    @ApiModelProperty(value = "用户unionId")
    private String unionId;

}



