package com.jnby.application.admin.dto.response;

import com.jnby.infrastructure.box.model.BoxWithBLOBs;
import com.jnby.infrastructure.box.model.Logistics;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ResetAddCartByBoxIdResp {

    BoxWithBLOBs box;

    Logistics logistics;

    @ApiModelProperty(value = "使用单次盒子  1  使用订阅盒子  2")
    private Integer useOnce = 1;
}
