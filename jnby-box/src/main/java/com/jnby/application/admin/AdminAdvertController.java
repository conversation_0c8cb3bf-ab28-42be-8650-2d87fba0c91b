package com.jnby.application.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jnby.application.BaseController;
import com.jnby.base.service.IMainAdvertService;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.leaf.support.IdWorkLeaf;
import com.jnby.infrastructure.box.model.Attr;
import com.jnby.infrastructure.box.model.MainAdvert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

@Controller
@RequestMapping("/admin/advert")
@Api(value = "AdminAdvert",tags = "广告位管理接口")
public class AdminAdvertController extends BaseController {

    @Autowired
    private IMainAdvertService iMainAdvertService;

    /**
     *   #if(status == 0)
     *   and ma.status = 1 and ma.begin_time > sysdate
     *   #else if(status == 1)
     *   and ma.status = 1 and ma.begin_time < sysdate and ma.end_time > sysdate
     *   #else
     *   and ((ma.status = 1 and ma.end_time < sysdate)  or ( ma.status =0 ))
     *   #end
     * @param request
     * @return
     */
    @ApiOperation(value = "广告位列表")
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<MainAdvert>> list(@RequestBody CommonRequest<MainAdvert> request){
        Page page = request.getPage();
        MainAdvert advert = request.getRequestData();
        QueryWrapper<MainAdvert> wrapper = new QueryWrapper<>();
        if (Objects.nonNull(advert.getSkipType())){
            wrapper.eq("SKIP_TYPE", advert.getSkipType());
        }
        if (Objects.nonNull(advert.getPlace())){
            wrapper.eq("place", advert.getPlace());
        }
        if (Objects.nonNull(advert.getStatus())){
//            wrapper.eq("status", 1);
            if (advert.getStatus().equals(0l)){//未开始
                wrapper.gt("begin_time", new Date());
                wrapper.eq("status", 1);
            }else if (advert.getStatus().equals(1l)){//进行中
                wrapper.gt("end_time", new Date()).lt("begin_time", new Date());
                wrapper.eq("status", 1);
            }else if (advert.getStatus().equals(2l)){//已结束，包含下线或时间过了
                wrapper.eq("status", 0).or().lt("end_time", new Date());
            }
        }
        if (Objects.nonNull(advert.getPlatform())){
            wrapper.eq("PLATFORM", advert.getPlatform());
        }
        wrapper.orderByDesc("CREATE_TIME");
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<MainAdvert> pages = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<MainAdvert>(page.getPageNo(), page.getPageSize());
        IPage<MainAdvert> pageList = iMainAdvertService.page(pages, wrapper);
        page.setCount(pageList.getTotal());
        page.setPages((int) pageList.getPages());
        return ResponseResult.success(pageList.getRecords(), page);
    }

    @ApiOperation(value = "创建广告位")
    @RequestMapping(value = "/createOrUpdate",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult createOrUpdate(@RequestBody CommonRequest<MainAdvert> request){
        MainAdvert advert = request.getRequestData();
        if (Objects.isNull(advert.getId())){
            advert.setId(IdWorkLeaf.getInstance().getId());
            advert.setCreateTime(new Date());
        }
        advert.setUpdateTime(new Date());
        iMainAdvertService.saveOrUpdate(advert);
        return ResponseResult.success();
    }

    @ApiOperation(value = "查询广告位")
    @RequestMapping(value = "/detail",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult detail(@RequestBody CommonRequest<MainAdvert> request){
        return ResponseResult.success(iMainAdvertService.getById(request.getRequestData().getId()));
    }
}
