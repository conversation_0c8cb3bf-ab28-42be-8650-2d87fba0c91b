package com.jnby.application.admin.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class StylingBaseDetailResp implements Serializable {
    private String stylingBaseId;
    private String name;
    private String content;
    private String tagsId;
    private Long type;
    private List<Styling> stylings;
    private Date createTime;

    private String outNo;

    @Data
    public static class Styling{
        private String stylingBaseId;

        private String productName;

        private String productCode;

        private Long productId;

        private String colorNo;

        private String colorName;
        private String skuId;
        private String sizeNo;
        private String sizeName;
        private String imgUrl;
        private String id;
        private int sorted;
    }
}
