package com.jnby.application.admin.dto.request;
import com.jnby.common.BaseSerializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 卡商品创建入参
 * <AUTHOR>
 * @version 1.0
 * @date 3/5/21 10:32 AM
 */
public class GoodMemberCardCreateReq extends BaseSerializable {
    private static final long serialVersionUID = -2482216857543000669L;

    /**
     * 商品ID
     */
    private String id;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 卡信息ID
     */
    private String memberCardId;
    /**
     * 卡天数ID
     */
    private String memberCardTypeId;

    private String name;

    private String label;

    private String content;

    private String firstImg;

    private String posterImg;

    private String detailImgs;

    private Long price;

    /**
     * 商品类型 2000 会员卡
     */
    private Long goodsType;

    /**
     * 卡类型 1订阅卡，2体验卡
     */
    @ApiModelProperty(value = " 1 正式卡  2 单次卡  3 免费卡")
    private Long memberType;

    private Long days;

    private Long status = 1L;

    @ApiModelProperty(value = "品牌名称")
    private String brandId;

    @ApiModelProperty(value = "适用卡等级  attrValue 中的值")
    private Integer cardLevel;

    @ApiModelProperty(value = " 使用 cVipDetai 字段")
    private Integer levelId;

    public Integer getLevelId() {
        return levelId;
    }

    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public Integer getCardLevel() {
        return cardLevel;
    }

    public void setCardLevel(Integer cardLevel) {
        this.cardLevel = cardLevel;
    }

    public String getBrandIds() {
        return brandIds;
    }

    private List<Integer> brandIdsList;

    public List<Integer> getBrandIdsList() {
        return brandIdsList;
    }

    public void setBrandIdsList(List<Integer> brandIdsList) {
        this.brandIdsList = brandIdsList;
    }

    public void setBrandIds(String brandIds) {
        this.brandIds = brandIds;
    }
    @ApiModelProperty(value = "json格式  []  数组")
    private String brandIds;

    private Integer applicableParty;


    public Integer getApplicableParty() {
        return applicableParty;
    }

    public void setApplicableParty(Integer applicableParty) {
        this.applicableParty = applicableParty;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getMemberCardId() {
        return memberCardId;
    }

    public void setMemberCardId(String memberCardId) {
        this.memberCardId = memberCardId;
    }

    public String getMemberCardTypeId() {
        return memberCardTypeId;
    }

    public void setMemberCardTypeId(String memberCardTypeId) {
        this.memberCardTypeId = memberCardTypeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFirstImg() {
        return firstImg;
    }

    public void setFirstImg(String firstImg) {
        this.firstImg = firstImg;
    }

    public String getPosterImg() {
        return posterImg;
    }

    public void setPosterImg(String posterImg) {
        this.posterImg = posterImg;
    }

    public String getDetailImgs() {
        return detailImgs;
    }

    public void setDetailImgs(String detailImgs) {
        this.detailImgs = detailImgs;
    }

    public Long getPrice() {
        return price;
    }

    public void setPrice(Long price) {
        this.price = price;
    }

    public Long getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Long goodsType) {
        this.goodsType = goodsType;
    }

    public Long getMemberType() {
        return memberType;
    }

    public void setMemberType(Long memberType) {
        this.memberType = memberType;
    }

    public Long getDays() {
        return days;
    }

    public void setDays(Long days) {
        this.days = days;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }
}
