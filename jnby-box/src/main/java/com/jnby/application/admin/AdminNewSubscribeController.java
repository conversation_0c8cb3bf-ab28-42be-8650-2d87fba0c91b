package com.jnby.application.admin;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.jnby.application.admin.dto.request.*;
import com.jnby.application.admin.dto.response.*;
import com.jnby.application.minapp.dto.request.GetCurrentSubWithSubPlanReq;
import com.jnby.application.minapp.dto.request.NewSubscribeListReq;
import com.jnby.application.minapp.dto.request.SubscribeListReq;
import com.jnby.base.repository.ICustomerDetailsRepository;
import com.jnby.base.service.IBSalesPopularizeLogService;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseCodeEnum;
import com.jnby.common.ResponseResult;
import com.jnby.common.enums.SubPlanStatusEnum;
import com.jnby.common.enums.SubscribeStatusEnum;
import com.jnby.common.util.DateUtil;
import com.jnby.common.util.RedissonUtil;
import com.jnby.infrastructure.box.mapper.BSubLogMapper;
import com.jnby.infrastructure.box.mapper.BSubscribeInfoMapper;
import com.jnby.infrastructure.box.mapper.BSubscribePlanMapper;
import com.jnby.infrastructure.box.mapper.SubscribeOrderMapper;
import com.jnby.infrastructure.box.model.*;
import com.jnby.module.facade.context.UnSubscribeContext;
import com.jnby.module.order.context.SubscribeWithCustomerContext;
import com.jnby.module.order.entity.SubscribeWithCustomerEntity;
import com.jnby.module.subscribe.context.SubWithOrderContext;
import com.jnby.module.subscribe.entity.CurrentSubWithSubPlan;
import com.jnby.module.subscribe.entity.SubWithOrderEntity;
import com.jnby.module.subscribe.entity.SubscribeRenewDayEntity;
import com.jnby.module.subscribe.service.IBSubscribeInfoService;
import com.jnby.module.subscribe.service.IBSubscribePlanService;
import com.jnby.module.subscribe.service.ICombineSubscribeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/9/18 14:11
 */

@RestController
@RequestMapping("/admin/new/subscribe")
@Api(value = "adminNewSubscribe", tags = "后台 - 新订阅接口")
public class AdminNewSubscribeController {

    @Autowired
    private IBSalesPopularizeLogService bSalesPopularizeLogService;

    @Resource
    private ICombineSubscribeService iCombineSubscribeService;

    @Resource
    private BSubscribeInfoMapper bSubscribeInfoMapper;

    @Resource
    private IBSubscribeInfoService ibSubscribeInfoService;

    @Resource
    private ICustomerDetailsRepository iCustomerDetailsRepository;

    @Resource
    private SubscribeOrderMapper subscribeOrderMapper;

    @Resource
    private RedissonUtil redissonUtil;

    @Resource
    private BSubLogMapper bSubLogMapper;


    @Resource
    private BSubscribePlanMapper bSubscribePlanMapper;

    @Resource
    private  IBSubscribePlanService ibSubscribePlanService;






    @ApiOperation(value = "导入excel进行更新数据")
    @ResponseBody
    @RequestMapping(value = "/importExcelUpdateData", method = RequestMethod.POST)
    public ResponseResult importExcelUpdateData(@RequestBody CommonRequest<String> request){
        bSalesPopularizeLogService.importExcelUpdateData(request.getRequestData());
        return ResponseResult.success();
    }



    @ApiOperation(value = "导购推广奖励列表")
    @ResponseBody
    @RequestMapping(value = "/guidePromotionRewardList", method = RequestMethod.POST)
    public ResponseResult<List<GuidePromotionResp>> guidePromotionRewardList(@RequestBody CommonRequest<GuidePromotionReq> request){
        Page page = request.getPage();
        List<GuidePromotionResp> resp = bSalesPopularizeLogService.guidePromotionRewardList(request.getRequestData(), page);
        return ResponseResult.success(resp, page);
    }


    @ApiOperation(value = "店长店员   导购推广明细列表  (不是分红的)")
    @ResponseBody
    @RequestMapping(value = "/guidePromotionRewardDetailList", method = RequestMethod.POST)
    public ResponseResult<List<GuidePromotionDetailResp>> guidePromotionRewardDetailList(@Validated @RequestBody CommonRequest<GuidePromotionDetailReq> request){
        Page page = request.getPage();
        List<GuidePromotionDetailResp> resp = bSalesPopularizeLogService.guidePromotionRewardDetailList(request.getRequestData(), page);
        return ResponseResult.success(resp, page);
    }


    @ApiOperation(value = "店长  推广明细列表  (分红的)")
    @ResponseBody
    @RequestMapping(value = "/managerPromotionRewardDetailList", method = RequestMethod.POST)
    public ResponseResult<List<GuidePromotionDetailResp>> managerPromotionRewardDetailList(@Validated @RequestBody CommonRequest<GuidePromotionDetailReq> request){
        Page page = request.getPage();
        List<GuidePromotionDetailResp> resp = bSalesPopularizeLogService.managerPromotionRewardDetailList(request.getRequestData(), page);
        return ResponseResult.success(resp, page);
    }



    @ApiOperation(value = "订阅列表")
    @PostMapping(value = "/subscribeList")
    @ResponseBody
    public ResponseResult<List<SubWithOrderEntity>> subscribeList(@Validated @RequestBody CommonRequest<NewSubscribeListReq> request) {
        NewSubscribeListReq newSubscribeListReq = request.getRequestData();
        SubWithOrderContext subWithOrderContext = new SubWithOrderContext();
        BeanUtils.copyProperties(newSubscribeListReq, subWithOrderContext);

        if (StringUtils.isNotEmpty(subWithOrderContext.getStartTime())) {
            subWithOrderContext.setStartTime(subWithOrderContext.getStartTime() + " 00:00:00");
        }
        if (StringUtils.isNotEmpty(subWithOrderContext.getEndTime())) {
            subWithOrderContext.setEndTime(subWithOrderContext.getEndTime() + " 23:59:59");
        }

        Page page = request.getPage();
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        //分页查询
        bSubscribeInfoMapper.selectBySearches(subWithOrderContext);
        PageInfo<SubWithOrderEntity> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        // 替换订阅结束时间，因为订阅结束时间会随着6次要盒完成而被替换，但是后台也没又要求显示真实订阅结束时间
        List<SubWithOrderEntity> list = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> ids = list.stream().map(SubWithOrderEntity::getId).collect(Collectors.toList());
            Map<String, Integer> map = Optional.ofNullable(ibSubscribeInfoService.selectRenewDayByIds(ids))
                    .orElse(Lists.newArrayList()).stream()
                    .collect(Collectors.toMap(SubscribeRenewDayEntity::getId, SubscribeRenewDayEntity::getRenewDay));

            list.forEach(rsp -> {
                Integer renewDay = map.get(rsp.getId());
                if (Objects.nonNull(renewDay) && Objects.nonNull(rsp.getStartTime())) {
                    rsp.setEndTime(DateUtil.addDate(rsp.getStartTime(), renewDay));
                }
            });
        }
        return ResponseResult.success(list, page);
    }

    @ApiOperation(value = "订阅详情")
    @PostMapping(value = "/subscribeDetails")
    @ResponseBody
    public ResponseResult<NewSubscribeDetailsResp> subscribeDetails(@Validated @RequestBody CommonRequest<NewSubscribeDetailsReq> request) {
        NewSubscribeDetailsReq newSubscribeDetailsReq = request.getRequestData();
        BSubscribeInfo bSubscribeInfo = Preconditions.checkNotNull(ibSubscribeInfoService.getById(newSubscribeDetailsReq.getSubId()), "订阅信息不存在");
        CustomerDetails customerDetails = Preconditions.checkNotNull(iCustomerDetailsRepository.selectByPrimaryKey(bSubscribeInfo.getCustId()), "用户信息不存在");

        // 替换订阅结束时间，因为订阅结束时间会随着6次要盒完成而被替换，但是后台也没又要求显示真实订阅结束时间
        Map<String, Integer> map = Optional.ofNullable(ibSubscribeInfoService.selectRenewDayByIds(Lists.newArrayList(bSubscribeInfo.getId())))
                .orElse(Lists.newArrayList()).stream()
                .collect(Collectors.toMap(SubscribeRenewDayEntity::getId, SubscribeRenewDayEntity::getRenewDay));
        if (Objects.nonNull(map.get(bSubscribeInfo.getId())) && Objects.nonNull(bSubscribeInfo.getStartTime())) {
            bSubscribeInfo.setEndTime(DateUtil.addDate(bSubscribeInfo.getStartTime(), map.get(bSubscribeInfo.getId())));
        }

        NewSubscribeDetailsResp newSubscribeDetailsResp = new NewSubscribeDetailsResp();
        newSubscribeDetailsResp.setCustomerDetails(customerDetails);
        newSubscribeDetailsResp.setBSubscribeInfo(bSubscribeInfo);

        List<SubscribeOrder> subscribeOrders = subscribeOrderMapper.getSubscribeOrderBySubId(newSubscribeDetailsReq.getSubId())
                .stream().filter(e-> SubscribeStatusEnum.NO_PAY.getCode().intValue()!=e.getStatus().intValue())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(subscribeOrders)) {
            newSubscribeDetailsResp.setSubscribeOrder(subscribeOrders);
        }
        return ResponseResult.success(newSubscribeDetailsResp);
    }


    @ApiOperation(value = "退订")
    @ResponseBody
    @RequestMapping(value = "/unSubscribe", method = RequestMethod.POST)
    public ResponseResult unSubscribe(@Validated @RequestBody CommonRequest<UnSubscribeReqDto> request) {
        UnSubscribeReqDto unSubscribeReqDto = request.getRequestData();
        String key = "unSubscribe:" + request.getRequestData().getSubId();
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "正在操作，请勿重试");
        }
        try {
            UnSubscribeContext unSubscribeContext = UnSubscribeContext.builder()
                    .subId(unSubscribeReqDto.getSubId())
                    .unsubMemo(unSubscribeReqDto.getUnsubMemo())
                    .refundAmount(unSubscribeReqDto.getRefundAmount())
                    .refundPoint(unSubscribeReqDto.getRefundPoint())
                    .updateBy(unSubscribeReqDto.getUpdateBy())
                    .build();
            iCombineSubscribeService.unSubscribeByPayWay(unSubscribeContext);
            return ResponseResult.success();
        } finally {
            redissonUtil.unlock(key);
        }
    }


    @ApiOperation(value = "获取订阅订单根据subIds")
    @PostMapping(value = "/getSubOrdersBySubIds")
    @ResponseBody
    public ResponseResult<List<SubscribeOrder>>  getSubOrdersBySubIds(@Validated @RequestBody CommonRequest<GetSubOrdersBySubIdsReqDto> request) {
        GetSubOrdersBySubIdsReqDto getSubOrdersBySubIdsReqDto  =request.getRequestData();
        List<SubscribeOrder> subscribeOrders =subscribeOrderMapper.getPaySubscribeOrderBySubIds(getSubOrdersBySubIdsReqDto.getSubIds());
        return ResponseResult.success(subscribeOrders);
    }



    @ApiOperation(value = "获取订阅log")
    @PostMapping(value = "/getSubLogList")
    @ResponseBody
    public ResponseResult<List<BSubLog>>  getSubLogList(@Validated @RequestBody CommonRequest<GetSubLogListReqDto> request) {
        GetSubLogListReqDto getSubLogListReqDto =Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        return ResponseResult.success(bSubLogMapper.subLogListBySubId(getSubLogListReqDto.getSubId()));
    }




    @ApiOperation(value = "获取订阅信息与计划subIds")
    @PostMapping(value = "/getSubInfoWithPlanBySubIds")
    @ResponseBody
    public ResponseResult<List<GetSubInfoWithPlanBySubIdsResp>>  getSubInfoWithPlanBySubIds(@Validated @RequestBody CommonRequest<GetSubOrdersBySubIdsReqDto> request) {
        GetSubOrdersBySubIdsReqDto getSubOrdersBySubIdsReqDto  = Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        if(CollectionUtils.isEmpty(getSubOrdersBySubIdsReqDto.getSubIds())){
            throw new RuntimeException("入参不能为空");
        }

        List<BSubscribeInfo> bSubscribeInfos =  bSubscribeInfoMapper.selectBySubIds(getSubOrdersBySubIdsReqDto.getSubIds());
        List<BSubscribePlan> subscribePlans = bSubscribePlanMapper.getAllSubscribePlanListBySubIds(getSubOrdersBySubIdsReqDto.getSubIds());

        List<GetSubInfoWithPlanBySubIdsResp> result = new ArrayList<>();
        bSubscribeInfos.forEach(e->{
            GetSubInfoWithPlanBySubIdsResp getSubInfoWithPlanBySubIdsRespTemp = new GetSubInfoWithPlanBySubIdsResp();
            getSubInfoWithPlanBySubIdsRespTemp.setBSubscribeInfo(e);
            getSubInfoWithPlanBySubIdsRespTemp.setBSubscribePlanList(subscribePlans.stream().filter(x-> e.getId().equals(x.getSubId())).collect(Collectors.toList()));
            result.add(getSubInfoWithPlanBySubIdsRespTemp);
        });
        return ResponseResult.success(result);
    }



    @ApiOperation(value = "获取当前有效订阅信息")
    @GetMapping(value = "/getCurrentSubWithSubPlan/{unionId}")
    @ResponseBody
    public  ResponseResult<CurrentSubWithSubPlan> getCurrentSubWithSubPlan(@PathVariable String unionId){
        if(StringUtils.isEmpty(unionId)){
            throw new RuntimeException("请求入参为空");
        }
        List<BSubscribeInfo> bSubscribeInfos = ibSubscribeInfoService.selectUserEffectSubInfoByUnionIds(Lists.newArrayList(unionId));

        CurrentSubWithSubPlan currentSubWithSubPlan = new CurrentSubWithSubPlan();
        currentSubWithSubPlan.setSubFlag(false);
        if (CollectionUtils.isEmpty(bSubscribeInfos)) {
            return ResponseResult.success(currentSubWithSubPlan);
        }
        BSubscribeInfo bSubscribeInfo = bSubscribeInfos.stream().sorted(Comparator.comparing(BSubscribeInfo::getStartTime)).findFirst().orElse(null);
        if(Long.valueOf(1).equals(bSubscribeInfo.getStatus())){
            currentSubWithSubPlan.setSubFlag(true);
            currentSubWithSubPlan.setBSubscribeInfo(bSubscribeInfo);
        }else{
            return ResponseResult.success(currentSubWithSubPlan);
        }

        List<BSubscribePlan> bSubscribePlanList = ibSubscribePlanService.getListBySubId(bSubscribeInfo.getId()).stream().filter(e -> {
            return SubPlanStatusEnum.WAIT_WANT_ASK.getCode().equals(e.getStatus());
        }).sorted(Comparator.comparing(BSubscribePlan::getPlanMonth)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(bSubscribePlanList)){
            return ResponseResult.success(currentSubWithSubPlan);
        }
        currentSubWithSubPlan.setBSubscribePlan(bSubscribePlanList.get(0));
        return ResponseResult.success(currentSubWithSubPlan);
    }
}
