package com.jnby.application.admin.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class CreateOthersPaySceneReqDto {

    @ApiModelProperty(value = "代付单id")
    @NotBlank(message = "代付单id 不能为空")
    private String othersPayId;

    @ApiModelProperty(value = "unionId")
    @NotBlank(message = "unionId 不能为空")
    private String unionId;


    @ApiModelProperty(value = "orderId")
    @NotBlank(message = "orderId 不能为空")
    private String orderId;
}
