package com.jnby.application.admin.dto.response;

import com.jnby.common.BaseSerializable;
import com.jnby.infrastructure.box.model.EbOrderListForAdmin;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class OrderFaStorage extends BaseSerializable {

    @ApiModelProperty(value = "售后状态:'是否退款  1是已退款;")
    private Integer refundStatus;

    @ApiModelProperty(value = "销售状态:是否购买,1是已购买;")
    private Integer orderStatus;

    private Long boxStatus;

    private String id;

    private String codeId;

    @ApiModelProperty(value = "商品名称")
    private String value;

    @ApiModelProperty(value = "商品尺码")
    private String sizeName;

    @ApiModelProperty(value = "商品吊牌价")
    private String price;

    private String colorNo;

    @ApiModelProperty(value = "商品颜色")
    private String colorName;

    @ApiModelProperty(value = "商品缩略图")
    private String imgUrl;

    @ApiModelProperty(value = "商品品牌")
    private String brand;

    @ApiModelProperty(value = "商品vip价格")
    private String vipPrice;

    @ApiModelProperty(value = "商品优惠价格")
    private String favorablePrice;

    private Long productNo;

    @ApiModelProperty(value = "商品状态(0:待发货;1:还货中;2:已还货;3:已购买;4:已处理;5:待退款 ;6;已退款;7:已取消;8:已发货;9:已签收;10:待还货;11:待入库;12:已删款)")
    private Long status;

    @ApiModelProperty(value = "商品款号")
    private String name;

    private String bigSeason;

    private String year;

    private String promotionName;

    private BigDecimal stock;

    private Long type;

    private String goodsId;

    @ApiModelProperty(value = "内淘订单")
    private String ebsn;

    private List tags;

    @ApiModelProperty(value = "关联换码单")
    private  List<EbOrderListForAdmin> ebOrderList;

}
