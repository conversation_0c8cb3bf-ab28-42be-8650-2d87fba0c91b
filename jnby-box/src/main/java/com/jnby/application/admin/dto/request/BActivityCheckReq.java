package com.jnby.application.admin.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR> null
 */
@ApiModel
@Data
public class BActivityCheckReq implements Serializable {

    @ApiModelProperty(value = "活动id，不传默认匹配普通活动")
    private String id;

    @ApiModelProperty(value = "顾客unionid")
    @NotNull(message = "unionid不能为空")
    private String unionid;

    @ApiModelProperty(value = "活动类型：1 推广活动  2 常规活动")
    private Integer activityType;

    private static final long serialVersionUID = 1L;

}