package com.jnby.application.admin.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class EbOrderListReq {

     @ApiModelProperty("搜索内容")
     private String search;

     @ApiModelProperty("类型")
     private Long type;

     @ApiModelProperty("状态")
     private Long status;

     @ApiModelProperty(value = "时间开始")
     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
     private Date beginDate;

     @ApiModelProperty(value = "时间结束")
     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
     private Date endDate;

     @ApiModelProperty(value = "发货店仓名称")
     private String sendStoreName;
}
