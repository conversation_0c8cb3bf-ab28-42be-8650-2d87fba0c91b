package com.jnby.application.admin.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @Auther: lwz
 * @Date: 2022/4/27 10:10
 * @Description: DelThemeMatchReq
 * @Version 1.0.0
 */
@Data
@ApiModel(value = "主题搭配删除 请求信息 入参", description = "主题搭配删除 请求信息相关入参")
public class DelThemeMatchReq {
    @ApiModelProperty(value = "id",  required = true)
    @NotEmpty(message = "主题搭配Id 不能为空")
    private String id;
}

