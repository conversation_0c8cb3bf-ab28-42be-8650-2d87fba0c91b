package com.jnby.application.admin.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/9 9:44
 */
@Data
@ApiModel(value = "相似商品查询参数")
public class SimilarGoodsReq {

    @ApiModelProperty(value = "商品款号")
    @NotBlank(message = "款号不能为空")
    private String spu;

    @ApiModelProperty(value = "门店")
    @NotEmpty(message = "门店信息不能为空")
    private List<Long> storeIds;
}
