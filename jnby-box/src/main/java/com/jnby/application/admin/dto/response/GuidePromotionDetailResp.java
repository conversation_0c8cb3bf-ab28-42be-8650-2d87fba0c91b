package com.jnby.application.admin.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/9/18 17:36
 */
@Data
public class GuidePromotionDetailResp {

    @ApiModelProperty(value = "导购名称")
    private String saleName;

    @ApiModelProperty(value = "导购类型 0店长 1店员 2店经理")
    private Integer saleType;

    @ApiModelProperty(value = "导购id")
    private String hrId;

    @ApiModelProperty(value = "导购店铺名称")
    private String saleStoreName;

    @ApiModelProperty(value = "用户手机号")
    private String phone;

    @ApiModelProperty(value = "奖励金额")
    private BigDecimal awardMoney;

    @ApiModelProperty(value = "是否有奖励 0无 1有")
    private Integer isAward;

    @ApiModelProperty(value = "奖励类型 0邀请 1分红")
    private Integer awardType;

    @ApiModelProperty(value = "0代表单店长 1代表多店长 当有奖励且多店长则展示多店长的标")
    private Integer isMultipleStoreManager;

    @ApiModelProperty(value = "订阅时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "导购id")
    private String fashionerId;

    @ApiModelProperty(value = "id")
    private String id;


    @ApiModelProperty(value = "是否有奖励 0无 1有 isAward字段以后不再使用 ")
    private Integer rewardType;

    @ApiModelProperty(value = "是否首次订阅  0 非首次  1 首次")
    private Integer isFirstSub;

    @ApiModelProperty(value = "发放奖励时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendRewardTime;

    @ApiModelProperty(value = "分红人数")
    private Integer invitationNum;

    @ApiModelProperty(value = "邀请分红金额")
    private Long invitationAmount;
}
