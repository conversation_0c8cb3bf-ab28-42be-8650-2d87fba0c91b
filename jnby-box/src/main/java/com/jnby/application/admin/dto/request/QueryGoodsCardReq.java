package com.jnby.application.admin.dto.request;
import com.jnby.common.BaseSerializable;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/5/21 2:23 PM
 */
public class QueryGoodsCardReq extends BaseSerializable {
    private static final long serialVersionUID = 8539662741041148915L;

    private Long status;

    private Long type;

    private String name;

    @ApiModelProperty(value = "适用品牌")
    private String brandId;

    @ApiModelProperty(value = "适用卡等级")
    private Integer cardLevel;

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public Integer getCardLevel() {
        return cardLevel;
    }

    public void setCardLevel(Integer cardLevel) {
        this.cardLevel = cardLevel;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
