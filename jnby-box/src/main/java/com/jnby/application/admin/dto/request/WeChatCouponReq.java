package com.jnby.application.admin.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WeChatCouponReq {

    @ApiModelProperty(value = "unactivated：未激活\n" +
            "audit：审核中\n" +
            "running：运行中\n" +
            "stoped：已停止\n" +
            "paused：暂停发放")
    private String status;


    @ApiModelProperty(value = "微信商户号id")
    private String merchId;




}
