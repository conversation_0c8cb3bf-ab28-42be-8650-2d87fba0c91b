package com.jnby.application.admin.dto.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * @Auther: lwz
 * @Date: 2022/4/20 15:42
 * @Description: AddThemeMatchReq
 * @Version 1.0.0
 */
@Data
@ApiModel(value = "新增主题搭配列表 请求信息 入参", description = "新增主题搭配列表 请求信息相关入参")
public class AddThemeMatchReq {

    @ApiModelProperty(value = "活动ID", required = true)
    @NotBlank(message = "活动ID不能为空")
    private String activityId;

    @ApiModelProperty(value = "活动名字", required = true)
    @NotBlank(message = "活动名字不能为空")
    private String activityName;


    @ApiModelProperty(value = "搭配名字", required = true)
    @NotBlank(message = "搭配名字不能为空")
    private String matchName;

    @ApiModelProperty(value = "推荐理由", required = true)
    @NotBlank(message = "推荐理由不能为空")
    private String recommReason;

    @ApiModelProperty(value = "lookId", required = true)
    @NotBlank(message = "lookId不能为空")
    private String lookId;

    @ApiModelProperty(value = "营销标识(0无，1NEW，2HOT)", required = true)
    @NotNull(message = "营销标识不能为空")
    private Long marketingStatus;


    @ApiModelProperty(value = "参与品牌id", required = true)
    @NotBlank(message = "参与品牌id不能为空")
    private String joinBrandId;

    @ApiModelProperty(value = "参与品牌名字", required = true)
    @NotBlank(message = "参与品牌名字不能为空")
    private String joinBrandName;

    @ApiModelProperty(value = "搭配图片", required = true)
    @NotBlank(message = "搭配图片不能为空")
    private String matchingPicture;

    @ApiModelProperty(value = "标识0上架，1下架")
    @NotNull(message = "标识不能为空")
    private Long status;


    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    private Long sortNo;
}
