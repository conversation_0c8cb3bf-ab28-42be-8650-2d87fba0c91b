package com.jnby.application.admin;

import com.jnby.application.BaseController;
import com.jnby.application.admin.dto.request.OrderForSetReq;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2021/10/27
 */
@Slf4j
@RestController
@RequestMapping("/admin/find")
@Api(value = "findForClickOn", tags = "根据服务单的不同状态查询接口")
public class FindForClickOnController extends BaseController {

    @ApiOperation(value = "查询应该显示的服务单操作按钮")
    @RequestMapping(value = "/forClickOn", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<Integer[]> list(@RequestBody CommonRequest<OrderForSetReq> request) {
        OrderForSetReq requestData = request.getRequestData();
        Integer status = requestData.getStatus();
        Integer[] clickStatus = {};
        //补货、物流、测试签收操作；
        //0,详情;1,打印;2,取消;3,删货;4,测试发货;5,补货;6,物流;7,测试签收;8,还货;9,测试完成;10,异常处理;11,重制
        switch (status) {
            //0:待发货,待发货可进行取消、删货、测试发货操作
            case 0:
                clickStatus = new Integer[]{0, 1, 2, 3, 4};
                break;
            //1:发货中,发货中可进行补货、物流、测试签收操作；
            case 1:
                clickStatus = new Integer[]{0, 1, 5, 6, 7};
                break;
            //2:已签收,已签收可进行补货、还货、物流、测试完成操作；
            case 2:
                clickStatus = new Integer[]{0, 1, 5, 8, 6, 9};
                break;
            //9:待还货,待还货可进行还货、测试完成,物流操作；
            case 9:
                clickStatus = new Integer[]{0, 1, 8, 9, 6};
                break;
            //10:还货中,还货中可进行物流、测试完成操作；
            case 10:
                clickStatus = new Integer[]{0, 1, 6, 9};
                break;
            //3:待入库;待入库时，测试完成操作，以及有还货异常时，可进行还货异常处理操作
            case 3:
                //异常判断
                Integer isWarn = requestData.getIsWarn();
                if (isWarn != null && isWarn != 0) {
                    //还货异常
                    clickStatus = new Integer[]{0, 1, 9, 10};
                } else {
                    clickStatus = new Integer[]{0, 1, 9};
                }
                break;
            //6:已取消,已取消师，可进行重制操作
            case 6:
                clickStatus = new Integer[]{0, 1, 11};
                break;
        }
        return ResponseResult.success(clickStatus);
    }
}
