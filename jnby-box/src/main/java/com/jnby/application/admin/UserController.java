package com.jnby.application.admin;

import com.jnby.common.ResponseResult;
import com.jnby.config.WxCpConfig;
import com.jnby.module.message.service.ISysMessageService;
import com.jnbyframework.boot.common.api.dto.message.TemplateMessageDTO;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.message.WxCpMessageSendResult;
import org.killbill.bus.api.PersistentBus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/6/26
 * <p>
 */
@RequestMapping("/admin/user")
@RestController
public class UserController {

    @Autowired
    private PersistentBus persistentBus;

    @Autowired
    private TransactionTemplate template;

    @Autowired
    private ISysMessageService iSysMessageService;

    @Autowired
    @Qualifier("boxDataSourceTransactionManager")
    private DataSourceTransactionManager boxDataSourceTransactionManager;

    @ResponseBody
    @GetMapping("/test")
    public ResponseResult<List<students>> test() throws PersistentBus.EventBusException {
//        Connection connection = null;
//        try {
//            connection = boxDataSourceTransactionManager.getDataSource().getConnection();
//        } catch (SQLException e) {
//        }
//
//        WorkBenchEventBus benchEventBus = new WorkBenchEventBus(persistentBus, connection);
//        WorkBenchEventBus.WorkBenchEvent event = new WorkBenchEventBus.WorkBenchEvent(
//                "20210316",
//                "1025",
//                "DD2021",
//                "1025",
//                "yxz1025",
//                "18668166209",
//                "event_type",
//                "1",
//                20211L,
//                202122L,
//                UUID.randomUUID()
//        );
//        benchEventBus.setBenchEvent(event);
//        benchEventBus.post();
////        WorkBenchEventBus.MyEventHandler handler = new WorkBenchEventBus.MyEventHandler(0,12);
////        persistentBus.register(handler);
////
////        WorkBenchEventBus.MyEvent myEvent = new WorkBenchEventBus.MyEvent("box-name", 1001L, "1", 1001L, 1002L, UUID.randomUUID());
////        persistentBus.post(myEvent);
//        List<students> list = new ArrayList<>();
//        list.add(new students("yxz1", 1));
//        list.add(new students("yxz2", 2));
//
        return null;
//        return ResponseResult.success(list);
    }

    @ResponseBody
    @GetMapping("/cp/push/{id}")
    public ResponseResult cpPush(@PathVariable String id) throws WxErrorException {
        WxCpMessage message = new WxCpMessage();
        message.setAgentId(WxCpConfig.defaultAgentId);
        message.setMsgType("text");
        message.setToUser(id);
        message.setContent("这是一个企业微信测试推送");
        WxCpMessageSendResult result = WxCpConfig.getCpService().getMessageService().send(message);
        return ResponseResult.success(result);
    }


    @ResponseBody
    @PostMapping("/sendMsg")
    public ResponseResult sendMsg(@RequestBody TemplateMessageDTO dto){
        iSysMessageService.sendMessageByTemplate(dto);
        return ResponseResult.success();
    }

/*    @ResponseBody
    @PostMapping("/doTask")
    public ResponseResult doTask(){
        try {
            sendMsgJob.execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseResult.success();
    }*/



    class students{
        private String name;
        private int sex;

        public students(String name, int sex){
            this.name = name;
            this.sex = sex;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getSex() {
            return sex;
        }

        public void setSex(int sex) {
            this.sex = sex;
        }
    }
}
