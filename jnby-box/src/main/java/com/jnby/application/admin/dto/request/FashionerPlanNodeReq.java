package com.jnby.application.admin.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FashionerPlanNodeReq implements Serializable {
    @ApiModelProperty("待联系状态 0,待联系，1要盒未搭盒，2已搭盒，3已取消，4已完成,5待提交要盒")
    private Integer status;

    @ApiModelProperty("计划月份;格式为:2020-03")
    private String planMonth;

    @ApiModelProperty("关键词搜索")
    private String q;

    private String fashionerId;

    private int hasContract = -1;//未联系

    private List<Integer> statusArr;

    @ApiModelProperty(value = "支付宝授信状态 DEFAULT:未授信 VALID：授信中，INVALID：授信失效")
    private String agreementStatus;
}
