package com.jnby.application.admin.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class RefundListResp {

    @ApiModelProperty(value = "售后单号")
    private  String refundSn;

    private String refundPhotos;
    @ApiModelProperty(value = "物流单号")
    private String trackingNumber;

    @ApiModelProperty(value = "微盟售后单号 ")
    private String weimoRefundId;

    private String getDate;
    @ApiModelProperty(value = "售后状态")
    private Integer status;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    @ApiModelProperty(value = "订单编号")
    private String orderSn;
    @ApiModelProperty(value = "订单类型")
    private Integer type;
    @ApiModelProperty(value = "用户昵称")
    private String nickName;
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "搭配师名称")
    private String name;
    @ApiModelProperty(value = "售后原因")
    private String refundRemark;

    @ApiModelProperty(value = "boxSn")
    private String boxSn;

}
