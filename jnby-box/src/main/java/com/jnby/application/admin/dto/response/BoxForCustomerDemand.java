package com.jnby.application.admin.dto.response;


import com.jnby.infrastructure.box.model.CustomerAskBox;
import com.jnby.infrastructure.box.model.StylingBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/11/12
 */
@Data
@ApiModel
public class BoxForCustomerDemand {

    @ApiModelProperty(value = "会员需求")
    private BoxForCustomerAsk boxForCustomerAsk;

    @ApiModelProperty(value = "搭配方案")
    private List<StylingBaseForOrder> stylingBaseForOrder;

}
