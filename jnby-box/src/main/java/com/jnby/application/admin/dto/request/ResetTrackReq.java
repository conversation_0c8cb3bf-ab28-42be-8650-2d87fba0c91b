package com.jnby.application.admin.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ResetTrackReq {

    private String refundSn;

    @ApiModelProperty(value = "客户地址Id",required = true)
    @NotBlank(message = "客户地址 不能为空")
    private String customerLogisticsId;

    private String getDate;

}
