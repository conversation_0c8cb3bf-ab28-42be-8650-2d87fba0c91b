package com.jnby.application.admin;


import com.jnby.application.admin.dto.request.BatchChannelToParentReq;
import com.jnby.application.admin.dto.request.CreateChannelReq;
import com.jnby.application.admin.dto.request.UpdateChannelReq;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.infrastructure.box.model.BChannelInfo;
import com.jnby.module.order.service.IBChannelInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/channel")
@Api(value = "AdminChannel", tags = "渠道接口")
public class AdminChannelController {
    @Resource
    private IBChannelInfoService ibChannelInfoService;


    @ApiOperation(value = "渠道列表")
    @PostMapping(value = "/listChannel")
    public ResponseResult<List<BChannelInfo>> listChannel() {
        return ResponseResult.success(ibChannelInfoService.listChannel());
    }


    @ApiOperation(value = "根据type渠道列表")
    @GetMapping(value = "/getListByType")
    public ResponseResult<List<BChannelInfo>> getListByType(@RequestParam("type") Long type) {
        return ResponseResult.success(ibChannelInfoService.listChannelByType(type));
    }


    @ApiOperation(value = "创建渠道")
    @PostMapping(value = "/createChannel")
    public ResponseResult createChannel(@RequestBody CommonRequest<CreateChannelReq> request)  {
        ibChannelInfoService.createChannel(request.getRequestData());
        return ResponseResult.success();
    }

    @ApiOperation(value = "修改渠道")
    @PostMapping(value = "/updateChannel")
    public ResponseResult updateChannel(@Validated  @RequestBody CommonRequest<UpdateChannelReq> request){
        ibChannelInfoService.updateChannel(request.getRequestData());
        return ResponseResult.success();
    }


    @ApiOperation(value = "批量归属父节点")
    @PostMapping(value = "/batchChannelToParent")
    public ResponseResult batchChannelToParent(@Validated  @RequestBody CommonRequest<BatchChannelToParentReq> request){
        ibChannelInfoService.batchChannelToParent(request.getRequestData());
        return ResponseResult.success();
    }

}
