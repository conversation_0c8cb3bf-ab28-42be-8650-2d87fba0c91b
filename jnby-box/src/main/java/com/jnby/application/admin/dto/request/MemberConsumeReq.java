package com.jnby.application.admin.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date:2022/9/6 15:20
 */
@ApiModel
@Data
public class MemberConsumeReq implements Serializable {

    @ApiModelProperty(value = "品牌weId")
    @NotBlank(message = "品牌weId必传")
    private String weId;

    @ApiModelProperty(value = "用户id")
    @NotBlank(message = "unionId必传")
    private String unionId;
}
