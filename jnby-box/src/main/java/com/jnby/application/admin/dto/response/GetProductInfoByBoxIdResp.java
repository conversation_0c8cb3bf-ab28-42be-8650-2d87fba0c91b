package com.jnby.application.admin.dto.response;

import com.jnby.infrastructure.box.model.BoxDetailsWithBLOBs;
import com.jnby.module.order.entity.BoxDetailsWithChangeEntity;
import com.jnby.module.order.entity.BoxDetailsWithSupplyEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: lwz
 * @Date: 2022/1/18 17:11
 * @Description: GetProductInfoByBoxIdResp
 * @Version 1.0.0
 */
@Data
public class GetProductInfoByBoxIdResp {

    @ApiModelProperty(value = "商品总数")
    private int allCount;

    @ApiModelProperty(value = "发货总数")
    private int sendCount;

    @ApiModelProperty(value = "购买总数")
    private int buyCount;

    @ApiModelProperty(value = "退还总数")
    private int backCount;

    @ApiModelProperty(value = "成交金额")
    private BigDecimal dealPrice;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退款个数")
    private Integer refundCount;


    @ApiModelProperty(value = "搭盒")
    private List<BoxDetailsWithBLOBs> createBoxList;

    @ApiModelProperty(value = "补货")
    private List<BoxDetailsWithSupplyEntity> supplyRespList;

    @ApiModelProperty(value = "换货")
    private List<BoxDetailsWithChangeEntity> changeCodeList ;


}
