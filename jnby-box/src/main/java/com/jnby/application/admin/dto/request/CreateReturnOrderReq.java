package com.jnby.application.admin.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Auther: lwz
 * @Date: 2021/11/11 14:44
 * @Description: CreateNewReturnOrderReq
 * @Version 1.0.0
 */
@Data
@ApiModel(value = "新增还货单 请求信息 入参", description = "新增换货单 请求信息相关入参")
public class CreateReturnOrderReq {

    @ApiModelProperty(value = "物流单号",required = true)
//    @NotBlank(message = "物流单号 不能为空")
    private String logSn;

    @ApiModelProperty(value = "物流公司",required = true)
//    @NotBlank(message = "物流公司 不能为空")
    private String logName;

    @ApiModelProperty(value = "实际退货金额",required = true)
//    @NotBlank(message = "实际退货金额 不能为空")
    private String refundAmount = "0";

    @ApiModelProperty(value = "boxId", required = true)
    @NotBlank(message = "boxId 不能为空")
    private String boxId;

    @ApiModelProperty(value = "boxReturnId")
    private String boxReturnId;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否退款单 0 否 1是",required = true)
//    @NotNull(message = "是否退款单 不能为空")
    private Long isRefund = 0L ;

    @ApiModelProperty(value = "还货详情id列表 不能为空",required = true)
    private List<String> boxDetailsId;

    @ApiModelProperty(value = "是否虚拟还货：1是 0否")
    private Integer ifVirBack = 0;
}
