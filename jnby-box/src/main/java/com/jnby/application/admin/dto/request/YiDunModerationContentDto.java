package com.jnby.application.admin.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class YiDunModerationContentDto {

    @ApiModelProperty(value = "渠道(1:pos+;2:微商城;3:BOX)",required = true)
    private Integer channel;

    @ApiModelProperty("审核中心返回编号")
    private String moderationNo;

    @ApiModelProperty("外部审核id，方便外部查询结果")
    private String outerModerationId;

    @ApiModelProperty("回调地址(仅支持post请求)")
    private String callbackUrl;

    private List<ModerationData> moderationData;

    @Data
    public static class ModerationData {

        @ApiModelProperty("终端数据明细id，便于终端数据查询")
        private String moderationDataId;

        @ApiModelProperty(value = "文本或URL",required = true)
        private String data;

        @ApiModelProperty(value = "分别为1-文本，2-图片，3-点播语音，4-音视频，5-文档",required = true)
        private int type;

        @ApiModelProperty("易盾使用数据id")
        private String dataId;
    }


}
