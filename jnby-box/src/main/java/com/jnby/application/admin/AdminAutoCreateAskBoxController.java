package com.jnby.application.admin;

import com.jnby.application.admin.dto.request.BCusServiceAutoAskBoxListReq;
import com.jnby.application.admin.dto.request.BatchChangeFashionerReq;
import com.jnby.application.admin.dto.request.CancelBCusServiceAutoReq;
import com.jnby.application.admin.dto.request.FinishFashionerReq;
import com.jnby.base.repository.ICustomerAskBoxRepository;
import com.jnby.base.service.IAskBoxService;
import com.jnby.base.service.IBCusServiceAutoAskBoxService;
import com.jnby.base.service.ICustomerDetailsService;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.enums.BCusServiceAutoAskBoxStatusEnum;
import com.jnby.common.enums.CustomerAskBoxStatusEnum;
import com.jnby.infrastructure.box.model.BCusServiceAutoAskBox;
import com.jnby.infrastructure.box.model.CustomerAskBox;
import com.jnby.infrastructure.box.model.CustomerDetails;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Controller
@RequestMapping("/admin/autoCreateAskBox")
@Api(value = "adminAutoCreateAskBox",tags = "客服创建要盒")
public class AdminAutoCreateAskBoxController {

    @Autowired
    private IAskBoxService askBoxService;

    @Autowired
    private IBCusServiceAutoAskBoxService ibCusServiceAutoAskBoxService;

    @Autowired
    private ICustomerDetailsService customerDetailsService;

    @Autowired
    private ICustomerDetailsService iCustomerDetailsService;

    @Autowired
    private ICustomerAskBoxRepository customerAskBoxRepository;

    /**
     * 列表信息 ， 表信息 , 查询参数信息，  导入信息
     * 1. 不做自动注册
     * 2. 必须换绑成搭配师 才可以进行下一步
     */

    @ApiOperation(value = "创建盒子工具列表")
    @ResponseBody
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    public ResponseResult<List<BCusServiceAutoAskBox>> list(@RequestBody CommonRequest<BCusServiceAutoAskBoxListReq> request){
        Page page = request.getPage();
        List<BCusServiceAutoAskBox> response = ibCusServiceAutoAskBoxService.list(request.getRequestData(),page);
        return ResponseResult.success(response,page);
    }


    @ApiOperation(value = "导出excel")
    @ResponseBody
    @RequestMapping(value = "/exportExcel",method = RequestMethod.POST)
    public ResponseResult exportExcel(@RequestBody CommonRequest<BCusServiceAutoAskBoxListReq> request){
        Page page = request.getPage();
        String url = ibCusServiceAutoAskBoxService.exportExcel(request.getRequestData(),page);
        return ResponseResult.success(url);
    }

    @ApiOperation(value = "取消")
    @ResponseBody
    @RequestMapping(value = "/cancel",method = RequestMethod.POST)
    public ResponseResult cancel(@RequestBody CommonRequest<CancelBCusServiceAutoReq> request){
        Page page = request.getPage();
        ibCusServiceAutoAskBoxService.cancel(request.getRequestData());
        return ResponseResult.success();
    }



    @ApiOperation(value = "创盒工具 - 导入")
    @ResponseBody
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public ResponseResult importExcel(@RequestParam(value = "file",required = true) String filePath){
        askBoxService.importExcel(filePath,"true", "系统");
        return ResponseResult.success();
    }


    @ApiOperation(value = "更改搭配师调用批量更改搭配师接口 ， batchChangeFashioner , 完善搭配后，继续走客服创建要盒")
    @ResponseBody
    @RequestMapping(value = "/finishFashioner",method = RequestMethod.POST)
    public ResponseResult batchChangeFashioner(@RequestBody CommonRequest<FinishFashionerReq> request){
        FinishFashionerReq requestData = request.getRequestData();
        CustomerDetails byPhone = customerDetailsService.findByPhone(requestData.getPhone());
        if(byPhone == null){
            return ResponseResult.error(-1,"用户不存在！");
        }
        CustomerAskBox params = new CustomerAskBox();
        params.setUnionid(byPhone.getUnionid());
        params.setStatus(CustomerAskBoxStatusEnum.READY.getCode().shortValue());
        List<CustomerAskBox> customerAskBoxes = customerAskBoxRepository.selectListBySelective(params);
        if(CollectionUtils.isNotEmpty(customerAskBoxes)){
            return ResponseResult.error(-1,"当前用户有进行中的盒子，不允许更改搭配师，不允许完善，请处理完要盒后再来操作！");
        }

        BatchChangeFashionerReq reqData = new BatchChangeFashionerReq();
        List<String> customerIds  =new ArrayList<>();
        customerIds.add(byPhone.getId());
        reqData.setCustomerIds(customerIds);
        reqData.setFashionerId(requestData.getFashionerId());
        reqData.setRemark("客服创建要盒完善搭配师");
        customerDetailsService.batchChangeFashioner(reqData,"");
        askBoxService.checkAndCreateAskBox(requestData.getPhone(),requestData.getId(),null);
        return ResponseResult.success();
    }

}
