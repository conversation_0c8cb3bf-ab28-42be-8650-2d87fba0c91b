package com.jnby.application.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.application.BaseController;
import com.jnby.application.admin.dto.request.*;
import com.jnby.application.admin.dto.response.GetTransGoodsInfoResp;
import com.jnby.base.service.IBEbStoreService;
import com.jnby.common.BoxException;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.cache.RedisKeysEnum;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.common.util.RedissonUtil;
import com.jnby.infrastructure.bojun.model.BoxMProduct;
import com.jnby.infrastructure.box.model.BBoxEbTransTot;
import com.jnby.infrastructure.box.model.BBoxEbTransfer;
import com.jnby.infrastructure.box.model.BEbStore;
import com.jnby.module.eb.service.*;
import com.jnby.module.facade.entity.AddLogEntity;
import com.jnby.module.facade.service.ISysLogService;
import com.jnby.module.oauth.oss.service.IUserInfoService;
import com.jnby.module.order.entity.CountTransEntity;
import com.jnby.module.order.service.IFasCoreService;
import com.jnby.module.order.service.box.IBoxService;
import com.jnby.module.product.entity.ProductStockEntity;
import com.jnby.module.product.repository.IBoxMProductRepository;
import com.jnby.module.product.service.IStockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/admin/boxEb")
@Api(value = "boxEb",tags = "大内淘后台接口")
@Slf4j
public class BoxEbController extends BaseController {

     @Autowired
     private IBEbStoreService ibEbStoreService;

     @Autowired
     private RedisPoolUtil redisPoolUtil;

     @Autowired
     private IBBoxEbTransferService boxEbTransferService;

     @Autowired
     private IBoxEbService boxEbService;

     @Autowired
     private IBoxMProductRepository boxMProductRepository;

     @Autowired
     private IBBoxEbTransTotService boxEbTransTotService;

     @Autowired
     private IStockService stockService;

     @Autowired
     @Qualifier("bojunTransactionTemplate")
     private TransactionTemplate template;

     @Autowired
     private IFasCoreService fasCoreService;

     @Autowired
     private IdLeafService idLeafService;

     @Autowired
     private RedissonUtil redissonUtil;

     @Autowired
     private IEbTransfersumitemService ebTransfersumitemService;

     @Autowired
     private IBoxService boxService;

     @Resource
     private IUserInfoService userInfoService;

     @Resource
     private ISysLogService sysLogService;

     @ApiOperation(value = "BOX内淘门店列表")
     @ResponseBody
     @RequestMapping(value = "/storeList",method = RequestMethod.POST)
     public ResponseResult<List<BEbStore>> storeList(@RequestBody CommonRequest request) {
          Page page = request.getPage();
          com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
          List<BEbStore> list = ibEbStoreService.list();
          PageInfo<BEbStore> pageInfo = new PageInfo(hPage);
          page.setCount(hPage.getTotal());
          page.setPages(pageInfo.getPages());
          return ResponseResult.success(pageInfo.getList(),page);
     }

     @ApiOperation(value = "BOX内淘门店开关")
     @ResponseBody
     @RequestMapping(value = "/openOrClose/{id}",method = RequestMethod.GET)
     public ResponseResult<?> openOrClose(@PathVariable String id) {
          BEbStore bEbStore = ibEbStoreService.getById(id);
          BEbStore updateEbStore = new BEbStore();
          updateEbStore.setId(bEbStore.getId());
          updateEbStore.setEbStatus(bEbStore.getEbStatus() == 1 ? 0:1);
          ibEbStoreService.updateById(updateEbStore);
          return ResponseResult.success();
     }

     @ApiOperation(value = "获取大内淘安全库存数")
     @ResponseBody
     @RequestMapping(value = "/getEbSafetyStock",method = RequestMethod.GET)
     public ResponseResult<?> getEbSafetyStock() {
          String safetyStock = RedisTemplateUtil.get(redisPoolUtil, RedisKeysEnum.EB_SAFETY_STOCK.getValue());
          return ResponseResult.success(safetyStock);
     }

     @ApiOperation(value = "设置大内淘安全库存数")
     @ResponseBody
     @RequestMapping(value = "/setEbSafetyStock/{safetyStock}",method = RequestMethod.GET)
     public ResponseResult<?> setEbSafetyStock(@PathVariable String safetyStock) {
          RedisTemplateUtil.set(redisPoolUtil, RedisKeysEnum.EB_SAFETY_STOCK.getValue(),safetyStock);
          return ResponseResult.success();
     }

     @ApiOperation(value = "BOX内淘预警列表")
     @ResponseBody
     @RequestMapping(value = "/ebOrderList",method = RequestMethod.POST)
     public ResponseResult<List<BBoxEbTransfer>> ebOrderList(@RequestBody CommonRequest<EbOrderListReq> request) {
          EbOrderListReq requestData = request.getRequestData();
          Page page = request.getPage();
          com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());

          QueryWrapper<BBoxEbTransfer> queryWrapper = new QueryWrapper<>();
          if(ObjectUtils.isNotEmpty(requestData.getStatus())){
               queryWrapper.eq("status",requestData.getStatus());
          }
          if(ObjectUtils.isNotEmpty(requestData.getType())){
               queryWrapper.eq("type",requestData.getType());
          }
          if(ObjectUtils.isNotEmpty(requestData.getSearch())){
               if(requestData.getSearch().startsWith("BX")){
                    queryWrapper.eq("box_sn",requestData.getSearch());
               }else if(requestData.getSearch().startsWith("TF")){
                    queryWrapper.eq("EB_TRANSFERSUM_DOCNO",requestData.getSearch());
               }else{
                    queryWrapper.eq("cus_phone",requestData.getSearch());
               }
          }
          if(ObjectUtils.isNotEmpty(requestData.getBeginDate())){
               queryWrapper.ge("create_time",requestData.getBeginDate());
          }
          if(ObjectUtils.isNotEmpty(requestData.getEndDate())){
               queryWrapper.le("create_time",requestData.getEndDate());
          }
          if(ObjectUtils.isNotEmpty(requestData.getSendStoreName())){
               queryWrapper.like("send_store_name",requestData.getSendStoreName());
          }
          queryWrapper.eq("is_del",0);
          queryWrapper.orderByDesc("create_time");
          List<BBoxEbTransfer> list = boxEbTransferService.list(queryWrapper);
          PageInfo<BBoxEbTransfer> pageInfo = new PageInfo(hPage);
          page.setCount(hPage.getTotal());
          page.setPages(pageInfo.getPages());
          return ResponseResult.success(pageInfo.getList(),page);
     }

     @ApiOperation(value = "BOX内淘预警列表操作---结案")
     @ResponseBody
     @RequestMapping(value = "/closeEbOrder",method = RequestMethod.POST)
     public ResponseResult<?> closeEbOrder(@Validated @RequestBody CommonRequest<CloseEbOrderReq> request) {
          BBoxEbTransfer boxEbTransfer = boxEbTransferService.getById(request.getRequestData().getId());
          boxEbService.closeEb(boxEbTransfer,userInfoService.getUserRealName());
          return ResponseResult.success();
     }


     @ApiOperation(value = "获取调拨商品信息")
     @ResponseBody
     @RequestMapping(value = "/getTransGoodsInfo",method = RequestMethod.POST)
     public ResponseResult<GetTransGoodsInfoResp> getTransGoodsInfo(@Validated @RequestBody CommonRequest<GetTransGoodsInfoReq> request) {
          GetTransGoodsInfoReq requestData = request.getRequestData();
          List<String> skuList = new ArrayList<>();
          skuList.add(requestData.getSku());
          List<BoxMProduct> product = boxMProductRepository.findBySkuList(skuList);
          if(CollectionUtils.isEmpty(product)){
               throw new BoxException("未查询到商品信息");
          }
          QueryWrapper<BBoxEbTransTot> bBoxEbTransTotQueryWrapper = new QueryWrapper<>();
          bBoxEbTransTotQueryWrapper.eq("send_union_store_id",requestData.getStoreId());
          bBoxEbTransTotQueryWrapper.eq("sku_no",requestData.getSku());
          bBoxEbTransTotQueryWrapper.eq("is_del",0);
          List<BBoxEbTransTot> list = boxEbTransTotService.list(bBoxEbTransTotQueryWrapper);
          if(CollectionUtils.isEmpty(list)) {
               throw new BoxException("不存在调入记录,请核实");
          }
          GetTransGoodsInfoResp transGoodsInfoResp = new GetTransGoodsInfoResp();
          transGoodsInfoResp.setSkuId(product.get(0).getCodeid());
          transGoodsInfoResp.setSku(product.get(0).getNo());
          transGoodsInfoResp.setName(product.get(0).getValue());
          transGoodsInfoResp.setColorNo(product.get(0).getColorno());
          transGoodsInfoResp.setColorName(product.get(0).getColorName());
          transGoodsInfoResp.setSize(product.get(0).getSizeName());
          transGoodsInfoResp.setSkuId(product.get(0).getCodeid());
          long sum = list.stream().mapToLong(e -> {
               return e.getSendStoreId().equals(417608L)? -e.getQty() : e.getQty();
          }).sum();
          transGoodsInfoResp.setStoreId(list.get(0).getSendStoreId());
          transGoodsInfoResp.setTransInNum(sum);
          return ResponseResult.success(transGoodsInfoResp);
     }


     @ApiOperation(value = "获取box仓商品库存")
     @ResponseBody
     @RequestMapping(value = "/getBoxShopStock",method = RequestMethod.POST)
     public ResponseResult<List<ProductStockEntity>> getBoxShopStock(@RequestBody CommonRequest<List<String>> request) {
          List<String> requestData = request.getRequestData();
          if(CollectionUtils.isEmpty(requestData)){
               throw new BoxException("传入商品为空");
          }
          List<ProductStockEntity> boxStockList = stockService.getStoreStorageBySku(417608L, requestData);
          List<ProductStockEntity> resp = requestData.stream().map(e -> {
               ProductStockEntity productStockEntity = new ProductStockEntity();
               productStockEntity.setSkuNo(e);
               ProductStockEntity productStock = boxStockList.stream().filter(a -> a.getSkuNo().equals(e)).findFirst().orElse(null);
               if (productStock != null) {
                    productStockEntity.setQty(productStock.getQty());
               } else {
                    productStockEntity.setQty(0);
               }
               return productStockEntity;
          }).collect(Collectors.toList());
          return ResponseResult.success(resp);
     }


     @ApiOperation(value = "提交调拨")
     @ResponseBody
     @RequestMapping(value = "/allotsBox2Store",method = RequestMethod.POST)
     public ResponseResult<?> allotsBox2Store(@Validated @RequestBody CommonRequest<AllotsBox2StoreReq> request) {
          AllotsBox2StoreReq requestData = Optional.ofNullable(request.getRequestData()).orElseThrow(() -> new IllegalArgumentException("请求参数不可为空"));
          List<AllotsBox2StoreReq.AllotProduct> transList = requestData.getProductList().stream().filter(e -> e.getQty() > 0L).collect(Collectors.toList());
          if(transList.size() == 0){
               throw new BoxException("调出数量均为0,请核实");
          }
          // 库存校验
          List<String> skuList = transList.stream().map(AllotsBox2StoreReq.AllotProduct::getSku).collect(Collectors.toList());
          List<ProductStockEntity> boxStockList = stockService.getStoreStorageBySku(417608L, skuList);
          transList.forEach(e -> {
               ProductStockEntity productStockEntity = boxStockList.stream().filter(a -> a.getSkuNo().equals(e.getSku())).findFirst().orElse(null);
               if(ObjectUtils.isEmpty(productStockEntity)){
                    throw new BoxException(e.getSku() + "库存不足");
               }
               if(e.getQty() > productStockEntity.getQty()){
                    throw new BoxException(e.getSku() + "库存不足");
               }
          });
          // 按照门店分别调拨
          Map<Long, List<AllotsBox2StoreReq.AllotProduct>> transMap = transList.stream().collect(Collectors.groupingBy(AllotsBox2StoreReq.AllotProduct::getStoreId));
          // 统计调拨数据
          template.execute(action -> {
                 for(long key : transMap.keySet()){
                      List<AllotsBox2StoreReq.AllotProduct> allotProducts = transMap.get(key);
                      // 调拨
                      fasCoreService.boxAllot2Store(allotProducts,idLeafService.getId(),key);
                 }
               return action;
          });
          CountTransEntity countTransEntity = new CountTransEntity();
          countTransEntity.setStoreId(requestData.getStoreId());
          List<CountTransEntity.CountTransProduct> countTransProductList = transList.stream().map(e -> {
               CountTransEntity.CountTransProduct countTransProduct = new CountTransEntity.CountTransProduct();
               countTransProduct.setStoreId(e.getStoreId());
               countTransProduct.setSku(e.getSku());
               countTransProduct.setQty(e.getQty());
               ProductStockEntity productStockEntity = boxStockList.stream().filter(a -> a.getSkuNo().equals(e.getSku())).findFirst().orElse(null);
               if (ObjectUtils.isNotEmpty(productStockEntity)) {
                    countTransProduct.setSkuId(productStockEntity.getSkuId());
               }
               return countTransProduct;
          }).collect(Collectors.toList());
          countTransEntity.setProductList(countTransProductList);
          // 更新调拨数量
          fasCoreService.countTransStock(countTransEntity);
          return ResponseResult.success();
     }

     @ApiOperation(value = "BOX内淘预警列表操作---缺货仓,派单")
     @ResponseBody
     @RequestMapping(value = "/dispatch",method = RequestMethod.POST)
     public ResponseResult<?> dispatch(@Validated @RequestBody CommonRequest<CloseEbOrderReq> request) {
          BBoxEbTransfer boxEbTransfer = boxEbTransferService.getById(request.getRequestData().getId());
          if(boxEbTransfer.getStatus() != 0){
               throw new RuntimeException("非待发货状态,无法派单");
          }
          // 库存校验
          boxEbService.checkEbStock(boxEbTransfer.getEbTransfersumId(),boxEbTransfer.getType());
          String lockKey = "vacancy-"+boxEbTransfer.getEbTransfersumId();
          if(!redissonUtil.tryLock(lockKey)){
               throw new RuntimeException("派单中,请勿重复操作!");
          }
          try {
               boxEbService.occupyVacancy(boxEbTransfer, 1);

               // 记录日志
               try {
                    AddLogEntity log = new AddLogEntity();
                    log.setOutNo("EB-"+boxEbTransfer.getBoxSn());
                    log.setUserName(userInfoService.getUserRealName());
                    log.setContent("派单|原发货单号："+boxEbTransfer.getEbTransfersumDocno());
                    sysLogService.addLog(log);
               } catch (Exception e) {
                    log.error("记录日志失败 派单",e);
               }
          }catch (Exception e){
               log.error("BOX内淘派单异常，ebSumId={},error={}",boxEbTransfer.getEbTransfersumId(),e.getMessage(),e);
               throw new RuntimeException(e.getMessage());
          }finally {
               redissonUtil.unlock(lockKey);
          }
          return ResponseResult.success();
     }



     @ApiOperation(value = "BOX内淘预警列表操作---增加备注")
     @ResponseBody
     @RequestMapping(value = "/addRemark",method = RequestMethod.POST)
     public ResponseResult<?> addRemark(@Validated @RequestBody CommonRequest<BoxEbAddRemarkReq> request) {
          BoxEbAddRemarkReq requestData = request.getRequestData();
          BBoxEbTransfer boxEbTransfer = boxEbTransferService.getById(requestData.getId());
          if(ObjectUtils.isEmpty(boxEbTransfer)){
               throw new RuntimeException("未查询到单据");
          }
          BBoxEbTransfer updateEntity = new BBoxEbTransfer();
          updateEntity.setId(boxEbTransfer.getId());
          updateEntity.setRemark(requestData.getRemark());
          updateEntity.setUpdateTime(new Date());
          boxEbTransferService.updateById(updateEntity);

          // 记录日志
          try {
               AddLogEntity log = new AddLogEntity();
               log.setOutNo("EB-"+boxEbTransfer.getBoxSn());
               log.setUserName(userInfoService.getUserRealName());
               log.setContent("备注|"+requestData.getRemark());
               sysLogService.addLog(log);
          } catch (Exception e) {
               log.error("记录日志失败 备注",e);
          }
          return ResponseResult.success();
     }


     @ApiOperation(value = "BOX内淘预警列表操作---非缺货仓重新指派")
     @ResponseBody
     @RequestMapping(value = "/reassign",method = RequestMethod.POST)
     public ResponseResult<?> reassign(@Validated @RequestBody CommonRequest<CloseEbOrderReq> request) {
          BBoxEbTransfer boxEbTransfer = boxEbTransferService.getById(request.getRequestData().getId());
          if(boxEbTransfer.getStatus() != 0){
               throw new RuntimeException("非待发货状态,无法派单");
          }
          // 库存校验
          boxEbService.checkEbStock(boxEbTransfer.getEbTransfersumId(),boxEbTransfer.getType());
          String lockKey = "reassign-"+boxEbTransfer.getEbTransfersumId();
          if(!redissonUtil.tryLock(lockKey)){
               throw new RuntimeException("派单中,请勿重复操作!");
          }
          try {
               boxEbService.occupyVacancy(boxEbTransfer,2);

               // 记录日志
               try {
                    AddLogEntity log = new AddLogEntity();
                    log.setOutNo("EB-"+boxEbTransfer.getBoxSn());
                    log.setUserName(userInfoService.getUserRealName());
                    log.setContent("重新指派|原发货单号："+boxEbTransfer.getEbTransfersumDocno());
                    sysLogService.addLog(log);
               } catch (Exception e) {
                    log.error("记录日志失败 重新指派",e);
               }
          }catch (Exception e){
               log.error("BOX内淘派单异常，ebSumId={},error={}",boxEbTransfer.getEbTransfersumId(),e.getMessage(),e);
               throw new RuntimeException(e.getMessage());
          }finally {
               redissonUtil.unlock(lockKey);
          }
          return ResponseResult.success();
     }

}
