package com.jnby.application.admin.dto.request;

import com.jnby.infrastructure.box.model.BActivityPage;
import com.jnby.infrastructure.box.model.BActivityReward;
import com.jnby.infrastructure.box.model.BWxMaterial;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> null
 */

@ApiModel(value="BActivityBasisResp对象", description="活动记录")
@Data
public class BActivityBasisAddReq implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "活动类型：1 推广活动  2 常规活动")
    private Integer activityType;

    @ApiModelProperty(value = "活动场景,即触发的条件：1 主动要盒 2 浏览首页 3 公众号送券 4 红包活动")
    private Integer triggerCondition;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "有效期类型： 1 固定时间，0 长期有效")
    private Integer validityType;

    @ApiModelProperty(value = "活动开始时间")
    private String startTime;

    @ApiModelProperty(value = "活动结束时间")
    private String endTime;

    @ApiModelProperty(value = "参与活动人群：0 不限， 其他 指定人群，关联B_ACTIVITY_MEMBER表id")
    private String userType;

    @ApiModelProperty(value = "是否对参与活动人群标记:0 不标记  1 标记")
    private Integer isFlag;

    @ApiModelProperty(value = "标记名称")
    private String flagName;

    @ApiModelProperty(value = "标记颜色")
    private String flagColour;

    @ApiModelProperty(value = "活动状态：1 未开始 2 进行中 3 已结束")
    private Integer status;

    @ApiModelProperty(value = "同个用户可以重复获得奖励的总次数")
    private Integer rewardNumber;

    @ApiModelProperty(value = "同个用户每天可以重复获得奖励的次数")
    private Integer everydayRewardNumber;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    @ApiModelProperty(value = "关键字")
    private String keyWord;

    @ApiModelProperty(value = "搭盒人")
    private String matchPerson;

    @ApiModelProperty(value = "materialId")
    private String materialId;

    @ApiModelProperty(value = "转发好友：0 未开启  1开启")
    private Integer transmitFriend;

    @ApiModelProperty(value = "分享朋友圈：0 未开启  1开启")
    private Integer shareFriend;

    @ApiModelProperty(value = "奖励类型 1:优惠券 2:实物")
    private List<Integer> rewardTypes;

    @ApiModelProperty(value = "活动奖励列表")
    List<BActivityReward> bActivityRewardList;

    @ApiModelProperty(value = "活动页面")
    BActivityPage bActivityPage;

    @ApiModelProperty(value = "微信素材卡片信息")
    BWxMaterial bWxMaterial;

    @ApiModelProperty(value = "materialId")
    private String scenesId;

    private static final long serialVersionUID = 1L;

}