package com.jnby.application.corp;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.jnby.application.BaseController;
import com.jnby.application.admin.dto.request.*;
import com.jnby.application.admin.dto.response.SimilarGoodsResp;
import com.jnby.application.corp.dto.request.BatchAgentStockReq;
import com.jnby.application.corp.dto.request.BatchProductPriceReq;
import com.jnby.application.corp.dto.request.SameSpuProductReq;
import com.jnby.base.entity.AgentStockEntity;
import com.jnby.base.entity.EbStorageSkuEntity;
import com.jnby.base.service.IMProductService;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.infrastructure.bojun.model.Dim;
import com.jnby.infrastructure.bojun.model.MProduct;
import com.jnby.module.order.entity.SettlementProduct;
import com.jnby.module.order.enums.CalcPriceIfUseEnum;
import com.jnby.module.order.enums.CalcPriceTypeEnum;
import com.jnby.module.order.service.IOrderService;
import com.jnby.module.product.context.PromotionProductContext;
import com.jnby.module.product.entity.PromotionProductEntity;
import com.jnby.module.product.repository.ICategoryRepository;
import com.jnby.module.product.service.IProductService;
import org.springcenter.product.api.dto.ProductAgentStockResp;
import org.springcenter.product.api.dto.SameSpuProductResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/7/215:49
 */
@RestController
@RequestMapping("/corp/product")
@Api(value = "CorpProduct",tags = "商品接口")
@Slf4j
public class CorpProductController extends BaseController {
    @Autowired
    private IProductService productService;

    @Autowired
    private IMProductService imProductService;

    @Autowired
    private ICategoryRepository categoryRepository;

    @Autowired
    private IOrderService orderService;

    @ApiOperation(value = "查询单品促销价格")
    @ResponseBody
    @RequestMapping(value = "/batchGetProductPromotionPrice",method = RequestMethod.POST)
    public ResponseResult batchGetProductPromotionPrice(@RequestBody CommonRequest<BatchProductPriceReq> request){
        BatchProductPriceReq requestData = request.getRequestData();
        PromotionProductContext context = new PromotionProductContext();
        BeanUtils.copyProperties(requestData,context);
        context.setShopDiscount(1F);
        context.setShopVip(CalcPriceIfUseEnum.NON.getKeyWord());
        context.setUseIntegral(CalcPriceIfUseEnum.NON.getKeyWord());
        context.setType(CalcPriceTypeEnum.unitCalc.getKeyWord());
        try {
            PromotionProductEntity entity = productService.batchGetProductPrice(context, request.getUser_id());
            return ResponseResult.success(entity);
        }catch (Exception e){
            log.error("批量查询商品单品优惠异常,param={}", JSON.toJSONString(requestData),e);
            return ResponseResult.error(-1,e.getMessage());
        }

    }

    @ApiOperation(value = "批量查询sku内淘库存")
    @ResponseBody
    @RequestMapping(value = "/batchGetProductEbNum",method = RequestMethod.POST)
    public ResponseResult< List<EbStorageSkuEntity>> batchGetProductEbNum(@RequestBody CommonRequest<List<String>> request){
        List<String> skuIds = request.getRequestData();
        List<EbStorageSkuEntity> skuEbStorageList = productService.getSkuBoxEbStorageByIds(skuIds);
        return ResponseResult.success(skuEbStorageList);
    }

    @ApiOperation(value = "批量查询商品属性")
    @ResponseBody
    @RequestMapping(value = "/batchGetProductAttr",method = RequestMethod.POST)
    public ResponseResult< List<SettlementProduct>> batchGetProductAttr(@RequestBody CommonRequest<List<Long>> request){
        List<Long> spuIds = request.getRequestData();
        if (Objects.isNull(spuIds) || spuIds.isEmpty()){
            return ResponseResult.error(-1, "商品ID不能为空");
        }
        QueryWrapper<MProduct> mProductQueryWrapper = new QueryWrapper<>();
        mProductQueryWrapper.in("id",spuIds);
        mProductQueryWrapper.select("id","PRODUCT_LIFE_CYCLE as productLifeCycle","M_DIM14_ID mDim14Id");
        List<MProduct> list = imProductService.list(mProductQueryWrapper);
        List<Long> attrIds = list.stream().map(MProduct::getmDim14Id).filter(ObjectUtils::isNotEmpty).collect(Collectors.toList());
        List<Dim> dimList = categoryRepository.findDimsByTypeAndIds("DIM14", attrIds);
        List<SettlementProduct> skuEbStorageList = list.stream().map(e -> {
            SettlementProduct settlementProduct = new SettlementProduct();
            settlementProduct.setSpuId(e.getId());
            Dim dim = new Dim();
            if(ObjectUtils.isNotEmpty(e.getmDim14Id())) {
                    dim = dimList.stream().filter(a -> {
                    return a.getId().equals(e.getmDim14Id());
                }).findFirst().orElse(null);
            }
            settlementProduct.setProductAttr(orderService.checkProAttr(e,dim));
            return settlementProduct;
        }).collect(Collectors.toList());
        return ResponseResult.success(skuEbStorageList);
    }


    @ApiOperation(value = "批量查询经销库存")
    @ResponseBody
    @RequestMapping(value = "/batchGetProductAgentNum",method = RequestMethod.POST)
    public ResponseResult<List<AgentStockEntity>> batchGetProductAgentNum(@RequestBody @Validated CommonRequest<BatchAgentStockReq> request){
        HashMap map = productService
                .getSkuAgentStorageByIds(request.getRequestData().getStoreIds(), request.getRequestData().getSkuIds());
        List<AgentStockEntity> rets = new ArrayList<>();
        request.getRequestData().getSkuIds().forEach(v -> {
            AgentStockEntity agentStockEntity = new AgentStockEntity();
            if (CollectionUtils.isEmpty(map)) {
                agentStockEntity.setSkuId(v);
                agentStockEntity.setQty(0L);
            } else {
                ProductAgentStockResp entity = (ProductAgentStockResp) map.get(v);
                if (entity == null) {
                    agentStockEntity.setSkuId(v);
                    agentStockEntity.setQty(0L);
                } else  {
                    agentStockEntity.setSkuId(v);
                    agentStockEntity.setQty(entity.getQty());
                }
            }
            rets.add(agentStockEntity);
        });
        return ResponseResult.success(rets);
    }

    @ApiOperation(value = "查看同款商品")
    @ResponseBody
    @RequestMapping(value = "/getSameSpuProduct",method = RequestMethod.POST)
    public ResponseResult<List<SameSpuProductResp>> getSameSpuProduct(@RequestBody @Validated CommonRequest<SameSpuProductReq> request){
        return ResponseResult.success(productService.searchSameSpuProduct(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/querySimilarGoods")
    @ApiOperation(value = "查询相似商品")
    public ResponseResult<List<SimilarGoodsResp>> querySimilarGoods(@Validated @RequestBody CommonRequest<SimilarGoodsReq> request){
        return ResponseResult.success(productService.findSimilarGoods(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/queryGoodsTactics")
    @ApiOperation(value = "查询商品策略")
    public ResponseResult<List<GoodsTacticsResp>> queryGoodsTactics(@Validated @RequestBody CommonRequest<GoodsTacticsReq> request){
        return ResponseResult.success(productService.queryGoodsTactics(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/judgeProductIsSale")
    @ApiOperation(value = "判断商品是否可售")
    public ResponseResult<JudgeProductIsSaleResp> judgeProductIsSale(@Validated @RequestBody CommonRequest<JudgeProductIsSaleReq> request){
        return ResponseResult.success(productService.judgeProductIsSale(request.getRequestData()));
    }

}
