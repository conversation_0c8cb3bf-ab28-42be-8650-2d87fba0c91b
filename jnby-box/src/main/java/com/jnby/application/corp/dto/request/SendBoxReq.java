package com.jnby.application.corp.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Auther: lwz
 * @Date: 2021/10/19 13:45
 * @Description: SendBoxReq
 * @Version 1.0.0
 */
@Data
@ApiModel(value="导购端发货 请求信息 入参", description="导购端发货 请求信息相关入参")
public class SendBoxReq {

    @ApiModelProperty(value = "type值 自提0，顺丰预约取件1")
    @NotBlank(message = "type 不能为空")
    private String type;

    @ApiModelProperty(value = "Box Id")
    @NotBlank(message = "id 不能为空")
    private String id;
}
