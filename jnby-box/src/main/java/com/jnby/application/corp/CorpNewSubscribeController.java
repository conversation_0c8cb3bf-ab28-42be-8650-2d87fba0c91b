package com.jnby.application.corp;

import com.jnby.application.corp.dto.request.ClerkListReq;
import com.jnby.application.corp.dto.request.NewSalesReq;
import com.jnby.application.corp.dto.response.ClerkListResp;
import com.jnby.application.corp.dto.response.PopularizePlanSuccessCustomerListResp;
import com.jnby.base.service.IBSalesPopularizeLogService;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/9/12 17:49
 */
@Slf4j
@Controller
@RequestMapping("/corp/new/subscribe")
@Api(value = "corpNewSubscribe", tags = "新订阅接口")
public class CorpNewSubscribeController {

    @Autowired
    private IBSalesPopularizeLogService bSalesPopularizeLogService;

    @ApiOperation(value = "导购推广发盒计划成功的会员列表")
    @ResponseBody
    @RequestMapping(value = "/newPopularizePlanSuccessCustomers", method = RequestMethod.POST)
    public ResponseResult<PopularizePlanSuccessCustomerListResp> newPopularizePlanSuccessCustomers(@RequestBody CommonRequest<NewSalesReq> request){
        Page page = request.getPage();
        PopularizePlanSuccessCustomerListResp resp = bSalesPopularizeLogService.newPopularizePlanSuccessCustomers(request.getRequestData(), page);
        return ResponseResult.success(resp, page);
    }


    @ApiOperation(value = "店员列表")
    @ResponseBody
    @RequestMapping(value = "/getClerkList", method = RequestMethod.POST)
    public ResponseResult<List<ClerkListResp>> getClerkList(@RequestBody CommonRequest<ClerkListReq> request){
        List<ClerkListResp> resp = bSalesPopularizeLogService.getClerkList(request.getRequestData());
        return ResponseResult.success(resp);
    }

    @ApiOperation(value = "处理推广数据")
    @ResponseBody
    @RequestMapping(value = "/dealPopuLog", method = RequestMethod.POST)
    public ResponseResult<Boolean> dealPopuLog(@RequestBody CommonRequest<String> request){
        return ResponseResult.success(bSalesPopularizeLogService.dealPopuLog(request.getRequestData()));
    }


    @ApiOperation(value = "刷占用库存数据")
    @ResponseBody
    @RequestMapping(value = "/dealStoreUsedNum", method = RequestMethod.POST)
    public ResponseResult<Boolean> dealStoreUsedNum(@RequestBody CommonRequest<String> request){
        return ResponseResult.success(bSalesPopularizeLogService.dealStoreUsedNum(request.getRequestData()));
    }


    @ApiOperation(value = "直接调用异步推广 -- 缺省的数据自测")
    @ResponseBody
    @RequestMapping(value = "/async/sub/popu", method = RequestMethod.POST)
    public ResponseResult<Boolean> asyncSubPopu(@RequestBody CommonRequest<String> request){
        return ResponseResult.success(bSalesPopularizeLogService.asyncSubPopu(request.getRequestData()));
    }


    @ApiOperation(value = "保存门店奖励人数限制，传入实例 418227,15;421150,4;416446,8;421150,3")
    @ResponseBody
    @RequestMapping(value = "/saveStoreLimited", method = RequestMethod.POST)
    public ResponseResult<Boolean> saveStoreLimited(@RequestBody CommonRequest<String> request){
        return ResponseResult.success(bSalesPopularizeLogService.saveStoreLimited(request.getRequestData()));
    }


    @ApiOperation(value = "根据linkId补偿导购信息")
    @ResponseBody
    @RequestMapping(value = "/compensateSalesInfo", method = RequestMethod.POST)
    public ResponseResult<Boolean> compensateSalesInfo(@RequestBody CommonRequest<String> request){
        return ResponseResult.success(bSalesPopularizeLogService.dealSalesPopLogCompensate(request.getRequestData()));
    }

}
