package com.jnby.application.corp.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IndexResp implements Serializable {

    @ApiModelProperty(value = "未处理")
    private IndexInnerResp unProcessed;
    @ApiModelProperty(value = "超期")
    private IndexInnerResp timeOut;
    @ApiModelProperty(value = "今日完成")
    private IndexInnerResp toDayFinished;
    @ApiModelProperty(value = "本月完成")
    private IndexInnerResp thisMonthFinished;
    @ApiModelProperty(value = "banner")
    private String banner;



    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IndexInnerResp{
        private String status;

        private String desc;

        private Integer count;

    }
}

