package com.jnby.application.corp.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Auther: lwz
 * @Date: 2021/12/10 10:35
 * @Description: GuideBoxSendReturnReq
 * @Version 1.0.0
 */
@Data
@ApiModel(value="导购寄回 请求信息 入参", description="导购寄回 请求信息相关入参")
public class GuideBoxSendReturnReq {
    @ApiModelProperty(value = "boxId",required = true)
    @NotBlank(message = "boxId 不能为空")
    private String boxId;

    @ApiModelProperty(value = "导购userId",required = true)
    @NotBlank(message = "userId 不能为空")
    private String userId;

    @ApiModelProperty(value = "日期")
    private String getDate;

    @ApiModelProperty(value = "客户地址Id")
    private String customerLogisticsId;

    @ApiModelProperty(value = "sendType   1 为快递  2 为自行发货", example = "1", required = true)
    @NotNull(message = "sendType 不能为空")
    private String sendType;

    @ApiModelProperty(value = "expressType 0 为申通 1 为中通")
    private String expressType;

    @ApiModelProperty(value = "快递单号")
    private String trackingNumber;

    @ApiModelProperty(value = "快递公司")
    private String expressCompany;

    @ApiModelProperty(value = "导购类型 0 为自营导购 1 为经销导购")
    private String guideType;
}
