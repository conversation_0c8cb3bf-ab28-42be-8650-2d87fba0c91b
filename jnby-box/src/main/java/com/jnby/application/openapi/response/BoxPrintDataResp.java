package com.jnby.application.openapi.response;

import com.jnby.infrastructure.box.model.*;
import com.jnby.module.order.entity.StylingBaseDetailApi;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BoxPrintDataResp implements Serializable {
    private BoxWithBLOBs box;

    private CustomerDetails customer;

    private Fashioner fashioner;

    private List<StylingBaseDetailApi> lookList;

    private Scene scene;

    private String randomMemo;

    private List<StylingBaseDetail> shopList;

    private List<BoxDetailsWithBLOBs> productList;


}
