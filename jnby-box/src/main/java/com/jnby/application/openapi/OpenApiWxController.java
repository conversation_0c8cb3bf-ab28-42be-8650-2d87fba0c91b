package com.jnby.application.openapi;

import com.alibaba.fastjson.JSONObject;
import com.jnby.application.BaseController;
import com.jnby.application.minapp.dto.response.WxMaSchema;
import com.jnby.application.openapi.request.*;
import com.jnby.application.openapi.response.AccessTokenResp;
import com.jnby.base.service.IEventMessageService;
import com.jnby.base.service.ISceneQrcodeService;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.common.enums.SceneTypeEnum;
import com.jnby.common.util.GenerateShortUrlUtil;
import com.jnby.config.WxMaConfig;
import com.jnby.config.WxMpConfig;
import com.jnby.infrastructure.box.model.EventMessage;
import com.jnby.infrastructure.box.model.Scene;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Yuan
 * @Date 2022/1/11 1:52 下午
 * @Version 1.0
 */
@Slf4j
@RequestMapping("/openapi/wx")
@RestController
@Api(value = "OpenApiWx",tags = "开放接口(微信)")
public class OpenApiWxController extends BaseController {

    @Autowired
    private ISceneQrcodeService iSceneQrcodeService;

    @Autowired
    private GenerateShortUrlUtil generateShortUrlUtil;

    @Resource
    private IEventMessageService iEventMessageService;

    @ApiOperation(value = "获取公众号token")
    @RequestMapping(value = "/getMpAccessToken",method = RequestMethod.GET)
    @ResponseBody
    public ResponseResult<AccessTokenResp> getMpAccessToken(){
        String access_token = null;
        try {
            access_token = WxMpConfig.getWxMpService().getAccessToken();
        } catch (WxErrorException e) {
            log.error("获取微信accesstoken异常 e = {}", e.getError().getErrorMsg());
            try {
                access_token = WxMpConfig.getWxMpService().getAccessToken(true);
            } catch (WxErrorException wxErrorException) {
                log.error("第二次获取微信token异常 e = {}", wxErrorException.getError().getErrorMsg());
            }
        }
        AccessTokenResp resp = new AccessTokenResp();
        resp.setAccess_token(access_token);
        return ResponseResult.success(resp);
    }

    @ApiOperation(value = "获取小程序token")
    @RequestMapping(value = "/getMaAccessToken",method = RequestMethod.GET)
    @ResponseBody
    public ResponseResult<AccessTokenResp> getMaAccessToken(){
        String access_token = null;
        try {
            access_token = WxMaConfig.getMaService().getAccessToken();
        } catch (WxErrorException e) {
            log.error("获取微信accesstoken异常 e = {}", e.getError().getErrorMsg());
            try {
                access_token = WxMaConfig.getMaService().getAccessToken(true);
            } catch (WxErrorException wxErrorException) {
                log.error("第二次获取微信token异常 e = {}", wxErrorException.getError().getErrorMsg());
            }
        }
        AccessTokenResp resp = new AccessTokenResp();
        resp.setAccess_token(access_token);
        return ResponseResult.success(resp);
    }

    @ApiOperation(value = "创建场景码信息")
    @RequestMapping(value = "/createScene",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<Scene> createScene(@RequestBody CommonRequest<CreateSceneReq> request){
        Scene scene = iSceneQrcodeService.getScene(request.getRequestData().getParams(), SceneTypeEnum.getByValue(request.getRequestData().getCode()), request.getRequestData().isIfCreateQrCode());
        return ResponseResult.success(scene);
    }

    @ApiOperation(value = "创建公众号场景码")
    @RequestMapping(value = "/createMpScene",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<String> createMpScene(@RequestBody CommonRequest<CreateQrcodeReq> request){
        String qrcodeUrl = iSceneQrcodeService.createWxMpQrcode(request.getRequestData().getScene());
        return ResponseResult.success(qrcodeUrl);
    }

    @ApiOperation(value = "通过OpenId获取公众号信息")
    @RequestMapping(value = "/getMpUserInfo",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<WxMpUser> getMpUserInfo(@RequestBody CommonRequest<QryWxUserInfoReq> request){
        WxMpUser wxMpUser = null;
        try {
            wxMpUser = WxMpConfig.getWxMpService().getUserService().userInfo(request.getRequestData().getOpenId());
        } catch (WxErrorException e) {
            log.error("获取微信公众号用户信息异常 e = {}", e.getError().getErrorMsg());
        }
        return ResponseResult.success(wxMpUser);
    }


    @ApiOperation(value = "获取小程序schema码")
    @RequestMapping(value = "/getMinappSchema",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<WxMaSchema> getMinappSchema(@RequestBody CommonRequest<MinappSchemaReq> request){
        MinappSchemaReq schemaReq = request.getRequestData();
        WxMaSchema maSchema = iSceneQrcodeService.generateMinappScheme(schemaReq.getPath(), schemaReq.getQuery());
        return ResponseResult.success(maSchema);
    }

    @ApiOperation(value = "获取小程序短链接")
    @RequestMapping(value = "/getMiniShortUrl",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<String> getMiniShortUrl(@RequestBody CommonRequest<WxGenerateUrlReq> request) throws WxErrorException {
        String s = generateShortUrlUtil.generateShortUrl(request.getRequestData().getUrl());
        return ResponseResult.success(s);
    }
    @ApiOperation(value = "获取小程序长链接")
    @RequestMapping(value = "/getMiniLongUrl",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<String> getMiniLongUrl(@RequestBody CommonRequest<WxGenerateUrlReq> request) throws WxErrorException {
        String s = generateShortUrlUtil.generateLongUrl(request.getRequestData());
        return ResponseResult.success(s);
    }
    @ApiOperation(value = "获取生产小程序长链接")
    @RequestMapping(value = "/getProMiniLongUrl",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<String> getProMiniLongUrl(@RequestBody CommonRequest<WxGenerateUrlReq> request) {
        //pages/box_/index/index 服务单页
        String s = generateShortUrlUtil.generateProLongUrl(request.getRequestData().getUrl());
        return ResponseResult.success(s);
    }


    @ApiOperation(value = "订阅消息事件推送")
    @RequestMapping(value = "/pushMsg", method = RequestMethod.POST)
    @ResponseBody
    public String pushMsg(@RequestBody Map<String, Object> eventData) {
        log.info("收到订阅消息事件:{}", JSONObject.toJSONString(eventData));
        String msgType = MapUtils.getString(eventData, "MsgType");
        String event = MapUtils.getString(eventData, "Event");
        if (!"event".equalsIgnoreCase(msgType) ) {
            log.info("非事件，跳过处理");
            return "success";
        }
        List<Map<String, Object>> list = (List<Map<String, Object>>) eventData.get("List");

        String fromUserName = MapUtils.getString(eventData, "FromUserName");
        String toUserName = MapUtils.getString(eventData, "ToUserName");
        Long createTime = MapUtils.getLong(eventData, "CreateTime");

        List<EventMessage> eventMessageList = new ArrayList<>();
        for (Map<String, Object> e : list) {
            EventMessage eventMessage = new EventMessage();

            String templateId = MapUtils.getString(e, "TemplateId");
            String subscribeStatus = MapUtils.getString(e, "SubscribeStatusString");

            // 生成唯一 ID：templateId + fromUserName 的 MD5 值
            eventMessage.setId(DigestUtils.md5Hex(templateId + ":" + fromUserName));
            eventMessage.setTemplateId(templateId);
            eventMessage.setFromUserName(fromUserName);
            eventMessage.setToUserName(toUserName);
            eventMessage.setSubscribeStatusString(subscribeStatus);
            eventMessage.setCreateTime(new Date());
            eventMessage.setUpdateTime(new Date());
            eventMessageList.add(eventMessage);
        }

        // 4. 批量保存/更新 EventMessage
        if (CollectionUtils.isNotEmpty(eventMessageList)) {
            iEventMessageService.saveOrUpdateBatch(eventMessageList);
        }
        return "success";
    }

}
