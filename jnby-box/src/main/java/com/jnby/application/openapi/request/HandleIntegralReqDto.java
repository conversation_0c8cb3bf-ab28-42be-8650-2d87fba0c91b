package com.jnby.application.openapi.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Data
public class HandleIntegralReqDto {
    @ApiModelProperty(value = "unionId")
    @NotEmpty(message = "unionId不存在")
    private String unionId;

    @ApiModelProperty(value = "积分")
    @NotEmpty(message = "积分不存在")
    private String point;

    @ApiModelProperty(value = "订阅id")
    private String subId;
}
