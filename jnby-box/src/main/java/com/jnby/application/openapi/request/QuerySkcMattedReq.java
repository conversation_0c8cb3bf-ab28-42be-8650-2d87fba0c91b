package com.jnby.application.openapi.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/7/29 4:23 下午
 * @Version 1.0
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = {"handler"})
public class QuerySkcMattedReq implements Serializable {
    private List<ProductColor> productColors;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(value = {"handler"})
    public static class ProductColor implements Serializable{
        private Long productId;
        private String colorNo;
    }

    public List<Long> getProductIds(){
        return productColors.stream().map(item -> item.getProductId()).collect(Collectors.toList());
    }

    public List<String> getColors(){
        return productColors.stream().map(item -> item.getColorNo()).collect(Collectors.toList());
    }
}
