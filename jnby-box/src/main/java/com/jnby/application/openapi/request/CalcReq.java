package com.jnby.application.openapi.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/1/10 15:04
 */
@Data
public class CalcReq implements Serializable {

    private String unionId;

    @ApiModelProperty(value = "是否参与商场折扣",required = true)
    private boolean ifJoinShopDiscount;

    @ApiModelProperty(value = "渠道: BOX,VIP,STORE,POS",required = true)
    private String channel;

    @ApiModelProperty("商场折扣")
    private BigDecimal shopDiscount;

    @ApiModelProperty(value = "门店id",required = true)
    @NotNull(message = "门店不能为空")
    private Long storeId;

    @ApiModelProperty("是否使用积分抵现")
    private boolean useIntegral;

    @ApiModelProperty("商品列表")
    @NotEmpty(message = "商品不能为空")
    private List<Product> productList;

    @ApiModelProperty("是否参与策略")
    private boolean ifStrategy;

    @ApiModel("CalcReq.Product")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Product{
        @ApiModelProperty("编号")
        private Integer rowNo;

        @ApiModelProperty("数量")
        private Integer qty;

        private Long skuId;

        @ApiModelProperty("是否改价")
        private boolean change;

        @ApiModelProperty("改价金额")
        private BigDecimal changePrice;

        @ApiModelProperty("该商品是否参与商场折扣")
        private boolean ifJoinShopDiscount;
    }
}
