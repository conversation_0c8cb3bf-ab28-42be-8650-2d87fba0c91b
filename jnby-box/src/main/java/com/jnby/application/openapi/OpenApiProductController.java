package com.jnby.application.openapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.jnby.application.admin.dto.request.ProductReq;
import com.jnby.application.admin.dto.response.*;
import com.jnby.application.admin.dto.response.AttrResp;
import com.jnby.application.admin.dto.response.ProductStockResp;
//import com.jnby.material.api.IMaterialApi;
//import com.jnby.material.api.dto.StylingReq;
//import com.jnby.material.api.dto.StylingListResp;
import com.jnby.application.openapi.request.*;
import com.jnby.application.openapi.request.QueryProductByAIReq;
import com.jnby.application.openapi.request.QuerySkcMattedReq;
import com.jnby.application.openapi.request.QueryStockReq;
import com.jnby.application.openapi.response.*;
import com.jnby.application.openapi.response.ProductSkcMattedImgResp;
import com.jnby.application.openapi.response.SampleProductFilterOptionResp;
import com.jnby.application.openapi.response.SkuResp;
import com.jnby.base.context.ProductApiContext;
import com.jnby.base.entity.ConditionAttr;
import com.jnby.base.entity.ProductApiEntity;
import com.jnby.base.service.ICStoreService;
import com.jnby.base.service.IcArcBrandService;
import com.jnby.common.*;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.common.enums.CityWarehouseTypeEnum;
import com.jnby.common.enums.StatusEnum;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.StrUtil;
import com.jnby.infrastructure.bojun.model.BoxMProduct;
import com.jnby.infrastructure.box.model.*;
import com.jnby.infrastructure.wx.model.CityWarehouseMapping;
import com.jnby.module.eb.service.ICityWarehouseMappingService;
import com.jnby.module.marketing.styling.repository.IStylingRepository;
import com.jnby.module.marketing.styling.service.IStylingService;
import com.jnby.module.order.service.box.IBoxService;
import com.jnby.module.product.context.GoodsContext;
import com.jnby.module.product.entity.*;
import com.jnby.module.product.service.*;
import org.springcenter.product.api.IProductApi;
import org.springcenter.product.api.IProductLabelApi;
import org.springcenter.product.api.IProductStoreApi;
import org.springcenter.product.api.dto.*;
import com.jnbyframework.boot.api.ISysBaseAPI;
import com.jnbyframework.boot.common.system.vo.SysCategoryModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.material.api.IMaterialApi;
import org.springcenter.material.api.dto.StylingListResp;
import org.springcenter.material.api.dto.StylingReq;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/6/2116:57
 */
@Slf4j
@RequestMapping("/openapi/product")
@RestController
@Api(value = "OpenApiProduct",tags = "开放接口(商品)")
public class OpenApiProductController {

    @Autowired
    private IProductApi iProductApi;

    @Autowired
    private IProductStoreApi iProductStoreApi;

    @Autowired
    private IProductService productService;

    @Autowired
    private IStockService iStockService;

    @Autowired
    private ICategoryService iCategoryService;

    @Autowired
    private IStylingService iStylingService;

    @Autowired
    private IBoxService boxService;

    @Autowired
    private IProductLabelService productLabelService;

    @Autowired
    private RedisPoolUtil redisPoolUtil;
    @Autowired
    private QiniuUtil qiniuUtil;

    @Autowired
    private IProductPreAssignedService productPreAssignedService;

    @Autowired
    private IdLeafService idLeaf;

    @Autowired
    private IcArcBrandService icArcBrandService;

    @Autowired
    private IProductPreAssignedDetailService productPreAssignedDetailService;


    @Autowired
    private ICStoreService icStoreService;

    @Autowired
    private ISysBaseAPI iSysBaseAPI;

    @Autowired
    private IProductLabelApi iProductLabelApi;

    @Autowired
    private IMaterialApi iMaterialApi;


    @ResponseBody
    @PostMapping("/queryDim")
    @ApiOperation(value = "导购-查询分类、品牌等数据")
    public ResponseResult<DimResp> queryDim() {
        List<AttrEntity> brands = iCategoryService.getBrands();
        List<AttrEntity> categoryEntities = iCategoryService.getBigCategorys();
        List<AttrEntity> smallCategorys = iCategoryService.getSmallCategorys();
        List<AttrEntity> years = iCategoryService.getYears();
        List<AttrEntity> seasons = iCategoryService.getSeasons();
        List<AttrEntity> bands = iCategoryService.getBands();

        DimResp result = new DimResp().buildBands(bands)
                .buildBrands(brands)
                .buildYears(years)
                .buildCategorys(categoryEntities)
                .buildSeasons(seasons)
                .buildSCategorys(smallCategorys);
        result.setCategoryTrees(iCategoryService.getCategoryTree());
        return ResponseResult.success(result);
    }

    @ResponseBody
    @PostMapping("/queryPreBand")
    @ApiOperation(value = "后台查询预分波段")
    public ResponseResult<List<AttrResp>> queryPreBand(){
        List<AttrResp> resps = new ArrayList<>();
        List<BandGroup> bands = productPreAssignedDetailService.selectBandGroupList();
        List<AttrEntity> entities = iCategoryService.getBands();
        for (int i = 0; i < bands.size(); i++) {
            BandGroup bandGroup = bands.get(i);
            AttrEntity entity = entities.stream().filter(item -> item.getId().equals(Long.valueOf(bandGroup.getBandId()))).findFirst().orElse(null);
            if (entity != null){
                AttrResp resp = new AttrResp();
                resp.setId(entity.getId());
                resp.setAttribname(entity.getAttribname());
                resps.add(resp);
            }
        }
        return ResponseResult.success(resps);
    }

    @ApiOperation(value = "根据条件获取商品数据")
    @RequestMapping(value = "/getProductListByPara", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult getProductListByPara(@RequestBody CommonRequest<ProductListReq> request) {
        ProductListReq requestDate = Optional.ofNullable(request.getRequestData()).orElseThrow(() -> new BoxException(ErrorConstants.PARAMS_ERROR.getCode(), "参数不可为空"));
        ProductApiContext context = new ProductApiContext();
        BeanUtils.copyProperties(requestDate, context);
        Page page = request.getPage();
        List<ProductApiEntity> list = productService.getProductApiList(page, context);
        return ResponseResult.success(list, page);
    }

    @ApiOperation(value = "获取门店下商品数据")
    @RequestMapping(value = "/getGoodsListByStore", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult getGoodsListByStore(@RequestBody CommonRequest<StoreGoodsReq> request) {
        return iProductStoreApi.getGoodsListByStore(request);
    }

    @ApiOperation(value = "搜索门店下商品和搭配数据")
    @RequestMapping(value = "/queryGoodsListByCode", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult queryGoodsListByCode(@RequestBody CommonRequest<QueryByProductCodeReq> request) {
        //productCode
        //判断前台传入的产品编码是否是国际码或者产品码
        SearchGoodsAndStylingResp resp = new SearchGoodsAndStylingResp();
        CommonRequest<StoreGoodsReq> reqStore = new CommonRequest<>();
        StoreGoodsReq storeGoodsReq = new StoreGoodsReq();

        String productCode = request.getRequestData().getProductCode();
        Page page = request.getPage();

        storeGoodsReq.setProductCode(productCode);
        storeGoodsReq.setStoreId(request.getRequestData().getStoreId());
        storeGoodsReq.setEbQty(request.getRequestData().isEbQty());
        storeGoodsReq.setQty(request.getRequestData().isQty());
        storeGoodsReq.setMallQty(request.getRequestData().isMallQty());
        storeGoodsReq.setQwQty(request.getRequestData().isQwQty());
        storeGoodsReq.setCityQty(request.getRequestData().isCityQty());
        if (StringUtils.isNotBlank(storeGoodsReq.getProductCode())) {
            storeGoodsReq.setEbQty(false);
            storeGoodsReq.setQty(false);
            storeGoodsReq.setMallQty(false);
            storeGoodsReq.setQwQty(false);
            storeGoodsReq.setCityQty(false);
        }
        reqStore.setRequestData(storeGoodsReq);
        reqStore.setPage(request.getPage());
        ResponseResult<List<StoreGoodSpuResp>> resultGoodsSpuList = iProductStoreApi.getGoodsListByStore(reqStore);
        if (resultGoodsSpuList.getData().isEmpty()) return ResponseResult.success(resp, resultGoodsSpuList.getPage());

        String productName = resultGoodsSpuList.getData().get(0).getName();

        resp.setStoreGoodSpu(resultGoodsSpuList.getData());
        // 通过 lookproduct反向查询到look
//        List<Integer> lookIds = sysLookProductService.getLookIdsByProductCode(request.getRequestData().getProductCode());
        ResponseResult<List<Integer>> lookIds = iMaterialApi.getLookIdsByProductCode(request.getRequestData().getProductCode());
        if (lookIds.getData().isEmpty()) {
            resp.setStylings(new ArrayList<>());
            return ResponseResult.success(resp);
        }

        StylingReq stylingReq = new StylingReq();
        List<Integer> lookIdsInteger = new ArrayList<>();
        if(lookIds.getData().size()>1000){
            // 超过1000，取前1000条
            lookIdsInteger = Lists.partition(lookIds.getData(), 1000).get(0);
        }
        stylingReq.setLookIds(lookIdsInteger);
        List<Long> storeIds = request.getRequestData().getStoreId().stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        stylingReq.setStoreIds(storeIds);
        stylingReq.setProductCode(productName);
        resp.setProductCode(productName);
        stylingReq.setSource(request.getRequestData().getSource());
        stylingReq.setIsPublish(1);

        CommonRequest<StylingReq> imaterRequest = new CommonRequest<StylingReq>();
        imaterRequest.setPage(page);
        imaterRequest.setRequestData(stylingReq);

        ResponseResult<List<StylingListResp>> looksByParams = iMaterialApi.getLooksByParams(imaterRequest);
//        List<StylingListResp> stylingEntities = iStylingService.stylingListForSales(page, stylingReq);
        resp.setStylings(looksByParams.getData());


//        StylingReq stylingReq = new StylingReq();
//        if(lookIds.size()>1000){
//            // 超过1000，取前1000条
//            lookIds = Lists.partition(lookIds, 1000).get(0);
//        }
//        stylingReq.setLookIds(lookIds);
//        List<Long> storeIds = request.getRequestData().getStoreId().stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
//        stylingReq.setStoreIds(storeIds);
//        stylingReq.setProductCode(resultGoodsSpuList.getData().get(0).getName());
//        stylingReq.setSource(request.getRequestData().getSource());
//        List<StylingListResp> stylingEntities = iStylingService.stylingListForSales(page, stylingReq);
//        resp.setStylings(stylingEntities);
        return ResponseResult.success(resp, page);

    }

    @ResponseBody
    @PostMapping("/goodsDetail")
    @ApiOperation(value = "查询商品详情")
    public ResponseResult<GoodSpuEntity> goodsDetail(@RequestBody CommonRequest<ProductReq> request) {
        GoodSpuEntity entity = null;
        if (request.getRequestData().getProductCode() != null) {
            entity = productService.findGoodByProductCode(request.getRequestData().getProductCode());
        } else {
            entity = productService.findGoodByProductId(request.getRequestData().getProductId());
        }
        if (request.getRequestData().getSkuId() != null && request.getRequestData().getSkuId() != "") {
            List<GoodSpuEntity.Sku> skus = entity.getSkus();
            if (skus != null) {
                entity.setSkus(skus.stream().filter(item -> item.getId().equals(request.getRequestData().getSkuId())).collect(Collectors.toList()));
            }
        }
        return ResponseResult.success(entity);
    }

    @ResponseBody
    @PostMapping("/goodsSkcDetail")
    @ApiOperation(value = "查询商品SKC详情")
    public ResponseResult<ProductSkcResp> goodSkcDetail(@RequestBody CommonRequest<org.springcenter.product.api.dto.ProductReq> request) {
        return iProductApi.goodSkcDetail(request);
    }

    @ResponseBody
    @PostMapping("/queryBatchProductSkcMatted")
    @ApiOperation(value = "批量查询商品SKC背景抠图")
    public ResponseResult<List<ProductSkcMattedImgResp>> queryBatchProductSkcMatted(@RequestBody CommonRequest<QuerySkcMattedReq> request) {
        QuerySkcMattedReq mattedReq = request.getRequestData();
        List<ProductSkcMattedImage> entity = productService.batchQueryProductSkcMatedImages(request.getRequestData().getProductIds(), request.getRequestData().getColors());

        List<ProductSkcMattedImgResp> resps = entity.stream().filter(item -> {
            QuerySkcMattedReq.ProductColor productColor = mattedReq.getProductColors().stream()
                    .filter(it -> it.getProductId().equals(item.getProductId()) && it.getColorNo().equals(item.getColorNo()))
                    .findFirst().orElse(null);
            if (productColor != null) return true;
            return false;
        }).map(item -> {
            ProductSkcMattedImgResp resp = new ProductSkcMattedImgResp();
            resp.setProductId(item.getProductId());
            resp.setImgurl(item.getMattedUrl());
            resp.setType(item.getProBigTypeLabel().intValue());
            resp.setColorNo(item.getColorNo());
            if (item.getSizeLabel() != null) {
                String[] size = item.getSizeLabel().split("\\*");
                resp.setWidth(size[0]);
                resp.setHeight(size[1]);
            }
            return resp;
        }).collect(Collectors.toList());
        //将集合resps中的元素按照入参mattedReq中的getProductIds方法返回的元素顺序保持一致
        List<ProductSkcMattedImgResp> respsSort = new ArrayList<>();
        for (QuerySkcMattedReq.ProductColor productColor : mattedReq.getProductColors()) {
            for (ProductSkcMattedImgResp resp : resps) {
                if (productColor.getProductId().equals(resp.getProductId()) && productColor.getColorNo().equals(resp.getColorNo())) {
                    respsSort.add(resp);
                    break;
                }
            }
        }
        return ResponseResult.success(respsSort);
    }

    public static void main(String[] args) {
        String str = "50*70";
        String[] size = str.split("\\*");
        System.out.println(size[0]);
    }

    @ResponseBody
    @PostMapping("/queryRecGoods")
    @ApiOperation(value = "查询商品推荐")
    public ResponseResult<List<GoodSkcResp>> queryRecGoods(@RequestBody CommonRequest<ProductReq> request) {
        long productId = Long.valueOf(request.getRequestData().getProductId());
        long colorCode = Long.valueOf(request.getRequestData().getColorCode());
        return ResponseResult.success(productService.getRecStylingGoodSkcList(productId, colorCode));
    }

    @ResponseBody
    @PostMapping("/batchQueryStock")
    @ApiOperation(value = "批量查询商品库存")
    public ResponseResult<List<ProductStockResp>> batchQueryStock(@RequestBody CommonRequest<QueryStockReq> request) {
        QueryStockReq req = request.getRequestData();
        log.info("批量查询商品库存 req={}", JSON.toJSONString(req));
        List<ProductStockResp> storages = iStockService.getStorages(req.getSkuIds(), req.getStoreId(), req.isNeedCityWarehouseStock());
        log.info("批量查询商品库存 结果={}", JSON.toJSONString(storages));
        return ResponseResult.success(storages);
    }

    @ResponseBody
    @PostMapping("/queryStock")
    @ApiOperation(value = "查询单个商品库存")
    public ResponseResult<ProductStockResp> queryStock(@RequestBody CommonRequest<QueryStockReq> request) {
        QueryStockReq req = request.getRequestData();
        List<ProductStockEntity> stockEntities = iStockService.getStorages(req.getSkuIds(), req.getStoreId());

        if (stockEntities.isEmpty()) {
            return ResponseResult.success(new ProductStockResp());
        }

        ProductStockResp resp = new ProductStockResp();
        resp.setSkuId(req.getSkuIds().get(0));
        resp.setStock(stockEntities.get(0).getQty());
        return ResponseResult.success(resp);
    }

    @ResponseBody
    @PostMapping("/batchQuerySpuStock")
    @ApiOperation(value = "批量查询SPU商品库存")
    public ResponseResult<List<SpuStockEntity>> batchQuerySpuStock(@RequestBody CommonRequest<QueryStockReq> request) {
        QueryStockReq req = request.getRequestData();
        log.info("批量查询SPU商品库存 req={}", JSON.toJSONString(req));
        List<SpuStockEntity> stockEntities = iStockService.getSpuStorages(req.getProductIds(), req.getStoreId(), req.isNeedCityWarehouseStock());
        log.info("批量查询SPU商品库存 结果={}", JSON.toJSONString(stockEntities));
        return ResponseResult.success(stockEntities);
    }


    @ResponseBody
    @PostMapping("/getBoxPrintData")
    @ApiOperation(value = "获取box打印数据")
    public ResponseResult getBoxPrintDate(@RequestBody CommonRequest<BoxPrintDataReq> request) {
        BoxPrintDataReq requestData = request.getRequestData();
        BoxPrintDataResp resp = boxService.getBoxPrintData(requestData.getBoxId());
        return ResponseResult.success(resp);
    }

    @ResponseBody
    @PostMapping("/markingLabel")
    @ApiOperation(value = "商品打标签")
    public ResponseResult markingLabel(@RequestBody CommonRequest<MarkingLabelReq> request) {
        iProductLabelApi.markingLabel(request);
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/queryGoods")
    @ApiOperation(value = "查询商品(按照标签、类目、品牌维度)")
    public ResponseResult<List<GoodSpuEntity>> queryGoods(@RequestBody CommonRequest<QueryGoodsReq> request) {
        Page page = request.getPage();
        QueryGoodsReq goodsReq = request.getRequestData();
        GoodsContext context = new GoodsContext();
        BeanUtils.copyProperties(goodsReq, context);
        if (goodsReq != null) {
            context.buildMap(goodsReq);
        }
        List<GoodSpuEntity> goodSpuEntities = productService.searchGoods(context, page);
        if (goodSpuEntities.isEmpty()) {
            return ResponseResult.success(new ArrayList<>());
        }
        return ResponseResult.success(goodSpuEntities, page);
    }

    @ResponseBody
    @PostMapping("/queryGoodSkc")
    @ApiOperation(value = "查询商品SKC")
    public ResponseResult<List<ProductSkcResp>> queryGoodSkc(@RequestBody CommonRequest<org.springcenter.product.api.dto.QueryGoodsReq> request) {
        org.springcenter.product.api.dto.QueryGoodsReq goodsReq = request.getRequestData();
        if (!goodsReq.getMustLabels().isEmpty()){
            goodsReq.setMustLabelLevels(processLabel(goodsReq.getMustLabels()));
        }
        request.setRequestData(goodsReq);
        //组装父标签和子标签
        return iProductApi.queryGoodSkc(request);
    }

    private List<String> processLabel(List<String> mustLabels){
        //查询标签信息
        List<SysCategoryModel> categoryModels = iSysBaseAPI.queryDSysCateByCodes(mustLabels);
        return categoryModels.stream().map(item -> item.getPidCode()).distinct().collect(Collectors.toList());
    }

    @ResponseBody
    @PostMapping("/querySampleProductFilter")
    @ApiOperation(value = "查询样衣商品筛选数据")
    public ResponseResult<SampleProductFilterOptionResp> querySampleProductFilter() {
        SampleProductFilterOptionResp resp = productService.querySampleProductFilter();
        return ResponseResult.success(resp);
    }

    @ResponseBody
    @PostMapping("/querySampleGoodSkc")
    @ApiOperation(value = "查询样衣商品SKC")
    public ResponseResult<List<org.springcenter.product.api.dto.SampleProductSkcResp>> querySampleGoodSkc(@RequestBody CommonRequest<org.springcenter.product.api.dto.SampleProductReq> request) {
        Page page = request.getPage();
        org.springcenter.product.api.dto.SampleProductReq goodsReq = request.getRequestData();
        if (Objects.nonNull(goodsReq.getMustLabels()) && goodsReq.getMustLabels().size() > 0){
            goodsReq.setMustLabelLevels(processLabel(goodsReq.getMustLabels()));
        }
        request.setRequestData(goodsReq);
        log.info("查询样衣商品SKC 查询商品库前参数{}", JSONObject.toJSONString(request));
        return iProductApi.searchSampleProductSkc(request);
    }

    @ResponseBody
    @PostMapping("/exportFile")
    @ApiOperation(value = "导出商品")
    public ResponseResult exportFile(@RequestBody CommonRequest<QueryGoodsReq> request) {
        QueryGoodsReq goodsReq = request.getRequestData();
        GoodsContext context = new GoodsContext();
        if (goodsReq != null) {
            context.buildMap(goodsReq);
        }
        File file = productService.exportProduct(context);
        String exportUrl = qiniuUtil.upload(file.getPath(), StrUtil.getUUID() + ".xlsx");
        file.delete();
        String uuid = StrUtil.getUUID();
        RedisTemplateUtil.setex(redisPoolUtil, uuid, exportUrl, 60);
        return ResponseResult.success(uuid);
    }

    @ResponseBody
    @GetMapping("/batchLabelingByUrl")
    @ApiOperation(value = "根据url读取文件批量打标签")
    public ResponseResult batchLabelingByUrl(@RequestParam("url") String url) {
        try{
            ParseLabelEntity parseLabelEntity = productLabelService.downFileTransferLabel(url);
            String param = "1"; //1表示成功
            if(parseLabelEntity.getErrorSpu().size() > 0){
                File file = productService.exportErrorSpu(parseLabelEntity.getErrorSpu());
                param = qiniuUtil.upload(file.getPath(), "异常商品"+System.currentTimeMillis() + ".xlsx");
                file.delete();
            }
            String uuid = StrUtil.getUUID();
            RedisTemplateUtil.setex(redisPoolUtil,uuid, param,60);
            if(parseLabelEntity.getList().size() > 0){
                productLabelService.batchLabeling(parseLabelEntity.getList());
            }else{
                throw new RuntimeException("解析异常");
            }
            return ResponseResult.success(uuid);
        }catch (Exception e){
            log.error("解析异常,url={}",url,e);
            return ResponseResult.error(-1,"解析异常");
        }

    }


    @ResponseBody
    @GetMapping("/getAnalysisDataByKeys")
    @ApiOperation(value = "根据keys判断文件是否解析完毕")
    public ResponseResult getAnalysisDataByKeys(@RequestParam("keys") String keys) {
        if(RedisTemplateUtil.exists(redisPoolUtil, keys+"error")){
            return ResponseResult.error(-1,"解析异常");
        }
        JSONObject jsonObject = new JSONObject();
        boolean exists = RedisTemplateUtil.exists(redisPoolUtil, keys);
        jsonObject.put("success",exists);
        if(exists){
            String url = RedisTemplateUtil.get(redisPoolUtil, keys);
            if(!"1".equals(url)){
                jsonObject.put("url",url);
            }
        }
        return ResponseResult.success(jsonObject);
    }



    @ResponseBody
    @PostMapping("/queryProductByAI")
    @ApiOperation(value = "根据图片获取AI推荐商品")
    public ResponseResult queryProductByAI(@RequestBody CommonRequest<QueryProductByAIReq> request) {
        QueryProductByAIReq requestData = request.getRequestData();
        if(StringUtils.isNotBlank(requestData.getImg()) && StringUtils.isNotBlank(requestData.getQuery())){
            return ResponseResult.error(-1,"请确认请求数据是否正确");
        }
        Page page = request.getPage();
        List<StoreGoodSpuEntity> list = productService.queryAIProduct(requestData, page);
        return ResponseResult.success(list,page);
    }

    @ResponseBody
    @PostMapping("/saveProductPreAssigned")
    @ApiOperation(value = "保存预分表数据")
    public ResponseResult saveProductPreAssigned(@RequestBody CommonRequest<SaveAssignedReq> request) {
        SaveAssignedReq requestData = request.getRequestData();
        ProductPreAssigned assigned = new ProductPreAssigned();
        BeanUtils.copyProperties(requestData,assigned);
        if(StringUtils.isBlank(assigned.getId())){
            assigned.setId(idLeaf.getId());
            assigned.setStatus(StatusEnum.NORMAL.getCode());
            assigned.setCreateTime(new Date());
            assigned.setSerialNo(productPreAssignedService.getNewestSerialNo(assigned.getYear()+assigned.getSeason()));
        }
        assigned.setUpdateTime(new Date());
        productPreAssignedService.saveOrUpdate(assigned);
        return ResponseResult.success();
    }
    @ResponseBody
    @PostMapping("/delProductPreAssigned")
    @ApiOperation(value = "删除预分表数据")
    public ResponseResult delProductPreAssigned(@RequestBody CommonRequest<AssignedIdReq> request) {
        AssignedIdReq requestData = request.getRequestData();
        ProductPreAssigned assigned = new ProductPreAssigned();
        assigned.setId(requestData.getAssignedId());
        assigned.setStatus(StatusEnum.FORBID.getCode());
        productPreAssignedService.updateById(assigned);
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/delProductPreAssignedDetail")
    @ApiOperation(value = "删除预分表明细数据")
    public ResponseResult delProductPreAssignedDetail(@RequestBody CommonRequest<AssignedIdReq> request) {
        AssignedIdReq requestData = request.getRequestData();
        ProductPreAssignedDetail assigned = new ProductPreAssignedDetail();
        assigned.setId(requestData.getAssignedDetailId());
        assigned.setStatus(StatusEnum.FORBID.getCode());
        productPreAssignedDetailService.updateById(assigned);
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/queryProductPreAssigned")
    @ApiOperation(value = "查询预分表数据")
    public ResponseResult<List<ProductPreAssignedResp>> queryProductPreAssigned(@RequestBody CommonRequest<QueryAssignedReq> request) {
        QueryAssignedReq requestData = request.getRequestData();
        Page returnPage = request.getPage();
        IPage<ProductPreAssigned> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(returnPage.getPageNo(), returnPage.getPageSize());
        ProductPreAssigned para = new ProductPreAssigned();
        BeanUtils.copyProperties(requestData,para);
        para.setStatus(StatusEnum.NORMAL.getCode());
        QueryWrapper<ProductPreAssigned> wrapper = new QueryWrapper<>(para);
        wrapper.orderByDesc("create_time");
        productPreAssignedService.page(page,wrapper);
        List<ProductPreAssignedResp> list = new ArrayList();
        if(page.getRecords().size() > 0){
            // 品牌赋值
            List<ConditionAttr> arcBrand = icArcBrandService.getAllBrandAttr("品牌");
            Map<String, List<ConditionAttr>> brandMap = arcBrand.stream().collect(Collectors.groupingBy(ConditionAttr::getCode));
            list = page.getRecords().stream().map(e -> {
                ProductPreAssignedResp productPreAssignedResp = new ProductPreAssignedResp();
                BeanUtils.copyProperties(e, productPreAssignedResp);
                productPreAssignedResp.setBrandName(Optional.ofNullable(brandMap.get(e.getBrand()))
                        .map(conditionAttrs -> conditionAttrs.get(0).getName())
                        .orElse(null));
                return productPreAssignedResp;
            }).collect(Collectors.toList());
        }
        returnPage.setPages(Long.valueOf(page.getPages()).intValue());
        returnPage.setCount(Long.valueOf(page.getTotal()).intValue());
        return ResponseResult.success(list,returnPage);
    }

    @ResponseBody
    @PostMapping("/queryBrand")
    @ApiOperation(value = "查询品牌")
    public ResponseResult<List<ConditionAttr>> queryBrand() {
        List<ConditionAttr> arcBrand = icArcBrandService.getAllBrandAttr("品牌");
        return ResponseResult.success(arcBrand);
    }

    @ResponseBody
    @PostMapping("/queryProductPreAssignedById")
    @ApiOperation(value = "根据id查询预分数据")
    public ResponseResult<ProductPreAssigned> queryProductPreAssignedById(@RequestBody CommonRequest<AssignedIdReq> request) {
        AssignedIdReq requestData = request.getRequestData();
        ProductPreAssigned assigned = productPreAssignedService.getById(requestData.getAssignedId());
        return ResponseResult.success(assigned);
    }

    @ResponseBody
    @GetMapping("/parseAssignedProduct")
    @ApiOperation(value = "解析预分详情表")
    public ResponseResult parseAssignedProduct(@RequestParam("assignedId") String assignedId,
                                                                   @RequestParam("url") String url) {
        productPreAssignedDetailService.downFileTransferAssignedDetail(assignedId,url);
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/queryAssignedDetailById")
    @ApiOperation(value = "根据id查询预分详情数据")
    public ResponseResult<ProductPreAssignedDetail> queryAssignedDetailById(@RequestBody CommonRequest<AssignedDetailIdReq> request) {
        AssignedDetailIdReq requestData = request.getRequestData();
        ProductPreAssignedDetail assignedDetail = productPreAssignedDetailService.getById(requestData.getAssignedDetailId());
        return ResponseResult.success(assignedDetail);
    }

    @ResponseBody
    @PostMapping("/delAssignedDetailById")
    @ApiOperation(value = "根据id删除详情数据")
    public ResponseResult delAssignedDetailById(@RequestBody CommonRequest<AssignedDetailIdReq> request) {
        AssignedDetailIdReq requestData = request.getRequestData();
        ProductPreAssignedDetail assignedDetail = new ProductPreAssignedDetail();
        assignedDetail.setId(requestData.getAssignedDetailId());
        assignedDetail.setStatus(StatusEnum.FORBID.getCode());
        productPreAssignedDetailService.updateById(assignedDetail);
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/batchUpdateAssignedDetail")
    @ApiOperation(value = "批量修改调整数量")
    public ResponseResult batchUpdateAssignedDetail(@RequestBody CommonRequest<List<BatchUpdateAssignedDetailReq>> request) {
        List<BatchUpdateAssignedDetailReq> requestData = request.getRequestData();
        List<ProductPreAssignedDetail> list = requestData.stream().map(e -> {
            ProductPreAssignedDetail assignedDetail = new ProductPreAssignedDetail();
            assignedDetail.setId(e.getId());
            assignedDetail.setAdjustQty(e.getAdJustQty());
            assignedDetail.setQty(e.getPreQty() + e.getAdJustQty());
            return assignedDetail;
        }).collect(Collectors.toList());
        productPreAssignedDetailService.updateBatchById(list);
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/queryAssignedDetailList")
    @ApiOperation(value = "根据预分表id查询预分详情数据")
    public ResponseResult queryAssignedDetailList(@RequestBody CommonRequest<QueryAssignedDetailListReq> request) {
        QueryAssignedDetailListReq requestData = request.getRequestData();
        Page returnPage = request.getPage();
        List<ProductPreAssignedDetail> list = productPreAssignedDetailService.selectProductDetailList(returnPage, requestData);
        return ResponseResult.success(list,returnPage);
    }

    @ResponseBody
    @PostMapping("/saveAssignedDetail")
    @ApiOperation(value = "保存预分表明细数据")
    public ResponseResult saveAssignedDetail(@RequestBody CommonRequest<ProductPreAssignedDetail> request) {
        ProductPreAssignedDetail requestData = request.getRequestData();
        ProductPreAssignedDetail assignedDetail = new ProductPreAssignedDetail();
        BeanUtils.copyProperties(requestData,assignedDetail);
        if(StringUtils.isBlank(assignedDetail.getId())){
            assignedDetail.setId(idLeaf.getId());
            assignedDetail.setStatus(StatusEnum.NORMAL.getCode());
            assignedDetail.setCreateTime(new Date());
        }
        // 计算实际生成量
        assignedDetail.setQty(assignedDetail.getPreQty() + assignedDetail.getAdjustQty());
        assignedDetail.setUpdateTime(new Date());
        productPreAssignedDetailService.saveOrUpdate(assignedDetail);
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/batchQuerySkuByIds")
    @ApiOperation(value = "批量查询SKU信息")
    public ResponseResult<List<SkuResp>> batchQuerySkuByIds(@RequestBody CommonRequest<BatchQuerySkuReq> request) {
        List<BoxMProduct> products = productService.selectGoodsListBySkuIds(request.getRequestData().getSkuId());
        if (products.isEmpty()) return ResponseResult.success();

        return ResponseResult.success(products.stream().map(item -> {
            SkuResp resp = new SkuResp();
            resp.setId(item.getCodeid());
            resp.setName(item.getName());
            resp.setValue(item.getValue());
            resp.setColor_name(item.getColorName());
            resp.setSize_name(item.getSizeName());
            resp.setProduct_id(item.getId());
            resp.setPrice(item.getPrice().doubleValue());
            resp.setImgUrl(item.getImgurl());
            return resp;
        }).collect(Collectors.toList()));
    }

    @ResponseBody
    @PostMapping("/miniGoodsDetail")
    @ApiOperation(value = "小程序查询商品详情")
    public ResponseResult<List<MiniGoodsDetailResp>> miniGoodsDetail(@RequestBody CommonRequest<MiniGoodsDetailReq> request) {
        MiniGoodsDetailReq miniGoodsDetailReq = request.getRequestData();
        List<MiniGoodsDetailResp> miniGoodsDetailResps = new ArrayList<>();

        for (String productId : miniGoodsDetailReq.getProductIdList()) {
            GoodSpuEntity entity = productService.findGoodByProductId(productId);
            MiniGoodsDetailResp miniGoodsDetailResp = new MiniGoodsDetailResp();
            if (entity != null) {
                miniGoodsDetailResp.setSkus(entity.getSkus());
                miniGoodsDetailResp.setPrice(entity.getPrice());
            }
            miniGoodsDetailResp.setProductId(productId);
            miniGoodsDetailResps.add(miniGoodsDetailResp);
        }
        return ResponseResult.success(miniGoodsDetailResps);
    }
}
