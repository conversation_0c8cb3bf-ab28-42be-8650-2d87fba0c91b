package com.jnby.application.openapi;

import com.alibaba.fastjson.JSONObject;
import com.jnby.application.openapi.request.ProcessStatusChangeReq;
import com.jnby.application.openapi.request.ZtoPushDataReq;
import com.jnby.application.openapi.request.ZtoPushReq;
import com.jnby.application.openapi.response.SFStatusChangeResp;
import com.jnby.application.openapi.response.ZtoPushResp;
import com.jnby.base.entity.SFStatusEntity;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.RedissonUtil;
import com.jnby.common.util.XMLPOJOParseUtil;
import com.jnby.module.process.service.IBProcessFlowService;
import com.jnby.module.sf.service.ISFExpressService;
import com.jnby.module.zto.service.ZtoExpressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @description:
 * @date 2021/6/289:25
 */
@Slf4j
@RequestMapping("/openapi/getPush")
@RestController
@Api(value = "OpenApi",tags = "开放接口(用于接收第三方推送)")
public class OpenApiGetPushController {
    @Resource
    private ISFExpressService sfParseService;

    @Resource
    private ZtoExpressService ztoExpressService;

    @Resource
    private IBProcessFlowService ibProcessFlowService;

    @Resource
    private RedissonUtil redissonUtil;


    @ApiOperation(value = "获取顺丰物流状态变化推送数据")
    @RequestMapping(value = "/SFStatusChangeCallBack",method = RequestMethod.POST)
    @ResponseBody
    public String SFStatusChangeCallBack(String content){
        SFStatusChangeResp resp;
        try {
            if(StringUtils.isBlank(content)){
                resp = SFStatusChangeResp.error("请求参数为空");
                return XMLPOJOParseUtil.Bean2XML(resp);
            }
            log.info("=========顺丰推送订单状态变化,请求xml={}",content);
            SFStatusEntity sfStatusEntity = (SFStatusEntity)XMLPOJOParseUtil.XML2Bean(content, SFStatusEntity.class);
            if(StringUtils.isBlank(sfStatusEntity.getOrderNo())){
                resp = SFStatusChangeResp.error("订单号为空");
                return XMLPOJOParseUtil.Bean2XML(resp);
            }
            sfParseService.statusChange(sfStatusEntity);
            resp = SFStatusChangeResp.isSuccess();
            return XMLPOJOParseUtil.Bean2XML(resp);
        }catch (Exception e) {
            log.error("=========顺丰推送订单状态变化,处理异常",e);
        }
        return "<Response>\n" +
                "    <success>false</success>\n" +
                "    <msg>订单状态接收异常</msg>\n" +
                "</Response>";
    }

    @ApiOperation(value = "获取中通物流状态变化推送数据")
    @RequestMapping(value = "/ZtoStatusChangeCallBack",method = RequestMethod.POST)
    @ResponseBody
    public ZtoPushResp ZtoStatusChangeCallBack(@RequestBody ZtoPushReq ztoPushReq){
        try {
            if (ztoPushReq == null || StringUtils.isBlank(ztoPushReq.getData())) {
                return ZtoPushResp.fail("中通请求参数异常");
            }
            //将data字符串封装成对象
            ZtoPushDataReq data = JSONObject.parseObject(ztoPushReq.getData(), ZtoPushDataReq.class);
            ztoExpressService.statusChange(data);
            return ZtoPushResp.success();
        } catch (Exception e) {
            log.error("=========中通推送订单状态变化, 处理异常", e);
        }
        return ZtoPushResp.fail("中通订单接收异常");
    }


    @ApiOperation(value = "接收审批状态变化推送数据")
    @RequestMapping(value = "/processStatusChangeCallBack",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<?> processStatusChangeCallBack(@Validated @RequestBody CommonRequest<ProcessStatusChangeReq> request){
        ProcessStatusChangeReq requestData = request.getRequestData();
        String key = requestData.getBizId();
        if(!redissonUtil.tryLock(key)){
            return ResponseResult.error(-1,"正在处理中");
        }
        try{
            ibProcessFlowService.processStatusChange(requestData);
            return ResponseResult.success();
        }catch (Exception e){
            log.error("接收审批状态变化推送数据处理异常,bizId={}",requestData.getBizId(),e);
            return ResponseResult.error(-1,e.getMessage());
        }finally {
            redissonUtil.unlock(key);
        }


    }

}
