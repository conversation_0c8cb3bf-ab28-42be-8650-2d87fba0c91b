package com.jnby.application.openapi;


import com.jnby.module.order.service.box.IBoxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springcenter.marketing.api.dto.CallTaskDetailResp;
import org.springcenter.marketing.api.dto.admin.AiCallBaseResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RequestMapping("/openapi/openAi")
@RestController
@RefreshScope
@Api(value = "OpenApi",tags = "开放接口(AI外呼)")
@Slf4j
public class OpenAiController {


    @Autowired
    private IBoxService boxService;



    @ApiOperation("获取box ai外呼  (服务单跟进)")
    @RequestMapping(value = "/getAiCallBox",method = RequestMethod.POST)
    @ResponseBody
    public AiCallBaseResp<List<CallTaskDetailResp>> getAiCallBox(@RequestBody Map<String,Object> params){
        AiCallBaseResp aiCallBaseResp = new AiCallBaseResp();
        if(params == null){
            aiCallBaseResp.setCode(1001);
            aiCallBaseResp.setMsg("参数为空");
            return aiCallBaseResp;
        }
        // 查询基本参数
        Object fashioner = params.get("fashioner");   // 用户类型   0 搭配师   1 直营导购   2 经销导购
        Object status = params.get("status");         // 状态   0  发货中  1 已签收   2 待还货   3 换货中
        Object sendUser = params.get("sendUser");     //  发给谁   0 收件人，    1 下盒人 导购/搭配师
        Object day = params.get("day");               //  天数

        List<CallTaskDetailResp> result = boxService.getAiCallBox(fashioner,status,sendUser,day);
        aiCallBaseResp.setCode(200);
        aiCallBaseResp.setData(result);
        return aiCallBaseResp;
    }

}
