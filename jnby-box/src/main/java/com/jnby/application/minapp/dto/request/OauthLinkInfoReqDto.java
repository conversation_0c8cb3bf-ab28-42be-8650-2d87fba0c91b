package com.jnby.application.minapp.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class OauthLinkInfoReqDto {
    @ApiModelProperty(value = "unionid",required = true)
    @NotBlank(message = "unionid不能为空")
    private String unionid;

    @ApiModelProperty(value = "openid",required = true)
    @NotBlank(message = "openid不能为空")
    private String openid;

    @ApiModelProperty(value = "手机号",required = true)
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "头像",required = true)
    @NotBlank(message = "头像不能为空")
    private String avatar;


    @ApiModelProperty(value = "昵称",required = true)
    @NotBlank(message = "昵称不能为空")
    private String nickName;

    /**
     * 小类（0微信 1支付宝）
     */
    @ApiModelProperty(value = "小类（0微信 1支付宝）",required = true)
    @NotNull(message = "小类不能为空")
    private Long type;

    /**
     * 大类（0Box ）
     */
    @ApiModelProperty(value = "大类（0Box ）",required = true)
    @NotNull(message = "大类不能为空")
    private Long subType;

    @ApiModelProperty(value = "小程序当前版本号")
    private String version;

}
