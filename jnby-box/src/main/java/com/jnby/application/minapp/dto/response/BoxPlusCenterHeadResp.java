package com.jnby.application.minapp.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class BoxPlusCenterHeadResp implements Serializable {
    @ApiModelProperty("盒子状态 0:未要盒，1首个盒子，2非首个盒子")
    private int status;

    @ApiModelProperty("绑定的搭配师/信息,如果未绑定，则为null")
    private PlusFashionerResp plusFashionerResp;

    @ApiModelProperty("盒子消费信息, status = 2时有效")
    private BoxConsumeInfo boxConsumeInfo;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("订阅到期信息")
    private SubscribeExpireInfo subscribeExpireInfo;

    @ApiModelProperty("是否已恢复首个盒子")
    private Boolean isHaveRecovered;

    @Data
    public static class PlusFashionerResp{
        @ApiModelProperty("搭配师/导购头像")
        private String headimgurl;

        @ApiModelProperty("搭配师/导购昵称")
        private String nickname;

        @ApiModelProperty("搭配师/导购类型 1:搭配师，2:导购")
        private int fashionerType;

        @ApiModelProperty("搭配师/导购ID")
        private String fashionerId;

        @ApiModelProperty(value = "搭配师微信二维码")
        private String fashionerWechatQrCode;

        public static PlusFashionerResp builder() {
            return new PlusFashionerResp();
        }
    }

    @Data
    public static class BoxConsumeInfo{
        @ApiModelProperty("盒子支付总价")
        private float payPrice;

        @ApiModelProperty("节省总价")
        private float savePrice;

        @ApiModelProperty("盒子积分")
        private int obtainIntegral;//将OBTAIN_INTEGRAL变为驼峰命名法 obtainIntegral

        @ApiModelProperty("累计收盒")
        private int totalBoxCount;
    }

    @Data
    public static class SubscribeExpireInfo{
        @ApiModelProperty("累计已节约金额")
        private float totalSavePrice;

        @ApiModelProperty("累计收盒")
        private int totalBoxCount;

        @ApiModelProperty("累计试穿")
        private int totalTryCount;

        @ApiModelProperty("累计购买")
        private int totalBuyCount;

        @ApiModelProperty("已获得收盒礼")
        private BigDecimal totalBoxGiftCount;
    }

}
