package com.jnby.application.minapp;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.application.minapp.dto.request.GetCoinReqDto;
import com.jnby.application.minapp.dto.request.ReadAllCoinRecordReqDto;
import com.jnby.application.minapp.dto.request.RewardRecordReqDto;
import com.jnby.base.repository.impl.CustomerDetailsRepository;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.infrastructure.box.mapper.CoinRecordMapper;
import com.jnby.infrastructure.box.model.CoinRecord;
import com.jnby.infrastructure.box.model.CustomerDetails;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/minapp/Coin")
@RefreshScope
@Api(value = "Coin", tags = "金币")
public class CoinController {
    @Resource
    private CustomerDetailsRepository customerDetailsRepository;

    @Resource
    private CoinRecordMapper coinRecordMapper;


    @ApiOperation(value = "获取用户金币数")
    @RequestMapping(value = "/getCoin", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult getCoin(@Validated  @RequestBody CommonRequest<GetCoinReqDto> request){
        String unionId = request.getRequestData().getUnionId();
        CustomerDetails customerDetails = customerDetailsRepository.findByUnionId(unionId);
        return ResponseResult.success(customerDetails.getCoin());
    }


    @ApiOperation(value = "奖励金记录")
    @RequestMapping(value = "/rewardRecord", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult rewardRecord(@Validated  @RequestBody CommonRequest<RewardRecordReqDto> request) {
        String custId = request.getRequestData().getCustId();

        Page page = request.getPage();
        com.github.pagehelper.Page<CoinRecord> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        coinRecordMapper.selectListByCustId(custId);
        PageInfo<CoinRecord> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return ResponseResult.success(pageInfo.getList(), page);
    }

    @ApiOperation(value = "更新所有金币奖励为已读")
    @RequestMapping(value = "/readAllCoinRecord", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult readAllCoinRecord(@Validated  @RequestBody CommonRequest<ReadAllCoinRecordReqDto> request) {
        String unionId =  request.getRequestData().getUnionId();
        CustomerDetails customerDetails =  customerDetailsRepository.findByUnionId(unionId);
        if (customerDetails == null) {
            return ResponseResult.success();
        }
        coinRecordMapper.readAllRecord(customerDetails.getId());
        return ResponseResult.success();
    }
}
