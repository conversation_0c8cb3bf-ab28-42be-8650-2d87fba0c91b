package com.jnby.application.minapp.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class GetSubPlanListBySubId {
    @NotBlank(message = "subId不能为空")
    @ApiModelProperty(value = "subId",required = true)
    private String subId;

    @NotBlank(message = "unionId不能为空")
    @ApiModelProperty(value = "unionId",required = true)
    private String unionId;

    @ApiModelProperty(value = "appId",required = true)
    private String appId;
}
