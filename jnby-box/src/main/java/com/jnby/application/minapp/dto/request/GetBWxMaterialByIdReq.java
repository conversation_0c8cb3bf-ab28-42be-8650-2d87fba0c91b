package com.jnby.application.minapp.dto.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "根据id获取微信素材 请求信息 入参", description = "根据id获取微信素材 请求信息相关入参")
public class GetBWxMaterialByIdReq {


    @ApiModelProperty(value = "素材id", required = true)
    @NotBlank(message = "素材id不能为空")
    private String BWxMaterialId;
}
