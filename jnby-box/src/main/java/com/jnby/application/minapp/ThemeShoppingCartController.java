package com.jnby.application.minapp;

import com.google.common.base.Preconditions;
import com.jnby.application.minapp.dto.request.*;
import com.jnby.application.minapp.dto.response.GetShoppingCartCountResp;
import com.jnby.base.service.IAskBoxService;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseCodeEnum;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.RedissonUtil;
import com.jnby.module.order.service.box.DefaultBox;
import com.jnby.module.order.service.box.IBoxService;
import com.jnby.module.order.service.box.IThemeActivityBoxService;
import com.jnby.module.themeshopcart.service.IThemeShopCartService;
import com.jnby.module.themeshopcart.entity.StylingBoxCartWithBrand;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * @Auther: lwz
 * @Date: 2022/4/27 14:21
 * @Description: ThemeShoppingCartController
 * @Version 1.0.0
 */
@RestController
@RequestMapping("/minapp/theme/shopCart")
@Api(value = "themeShoppingCart", tags = "小程序主题购物车接口")
@Slf4j
public class ThemeShoppingCartController {

    @Resource
    private IThemeShopCartService iThemeShopCartService;

    @Resource
    private RedissonUtil redissonUtil;

    @Resource
    private IThemeActivityBoxService iThemeActivityBoxService;

    @Resource
    private IBoxService iBoxService;


    @Resource
    private IAskBoxService iAskBoxService;


    @ApiOperation(value = "购物车是否有搭配")
    @RequestMapping(value = "/shopCartHasMatch", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult shopCartHasMatch(@Validated @RequestBody CommonRequest<ShopCartHasMatchReq> request) {
        ShopCartHasMatchReq shopCartHasMatchReq = request.getRequestData();
        return ResponseResult.success(iThemeShopCartService.shopCartHasMatch(shopCartHasMatchReq.getUnionId(), shopCartHasMatchReq.getThemeMatchId()));
    }


    @ApiOperation(value = "批量添加购物车")
    @RequestMapping(value = "/batchInsert", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult batchInsert(@Validated @RequestBody CommonRequest<BatchInsertThemeCartReq> request) {
        String key = "batchInsert:" + request.getRequestData().getUnionId();
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "正在操作，请勿重试");
        }

        try {
            iThemeShopCartService.batchInsertThemeShopCart(request.getRequestData());
            return ResponseResult.success();
        } finally {
            redissonUtil.unlock(key);
        }
    }


    @ApiOperation(value = "查看购物车数量")
    @RequestMapping(value = "/getShoppingCartCount", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<GetShoppingCartCountResp> getShoppingCartCount(@Validated @RequestBody CommonRequest<GetShoppingCartCountReq> request) {
        GetShoppingCartCountReq getShoppingCartReq = request.getRequestData();
        return ResponseResult.success(iThemeShopCartService.getThemeShopCartCount(getShoppingCartReq.getUnionId()));
    }


    @ApiOperation(value = "查看购物车")
    @RequestMapping(value = "/getShoppingCart", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<StylingBoxCartWithBrand>> getShoppingCart(@Validated @RequestBody CommonRequest<GetShoppingCartReq> request) {
        GetShoppingCartReq getShoppingCartReq = request.getRequestData();
        return ResponseResult.success(iThemeShopCartService.getThemeShopCartWithBrandList(getShoppingCartReq.getUnionId()));
    }


    @ApiOperation(value = "删除购物车商品")
    @RequestMapping(value = "/delItemShoppingCart", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult delItemShoppingCart(@Validated @RequestBody CommonRequest<DelItemShoppingCartReq> request) {
        DelItemShoppingCartReq delItemShoppingCartReq = request.getRequestData();
        String key = "delItemShoppingCart:" + delItemShoppingCartReq.getUnionId();
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "正在操作，请勿重试");
        }
        try {
            iThemeShopCartService.delItemShoppingCart(delItemShoppingCartReq.getUnionId(), delItemShoppingCartReq.getCartBaseId(), delItemShoppingCartReq.getThemeMatchIds());
            return ResponseResult.success();
        } finally {
            redissonUtil.unlock(key);
        }
    }


    @ApiOperation(value = "编辑购物车商品")
    @RequestMapping(value = "/updateItemShoppingCart", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult updateItemShoppingCart(@Validated @RequestBody CommonRequest<BatchUpdateThemeCartReq> request) {
        BatchUpdateThemeCartReq batchUpdateThemeCartReq = request.getRequestData();
        String key = "updateItemShoppingCart:" + batchUpdateThemeCartReq.getUnionId();
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "正在操作，请勿重试");
        }
        try {
            iThemeShopCartService.updateItemShoppingCart(batchUpdateThemeCartReq);
            return ResponseResult.success();
        } finally {
            redissonUtil.unlock(key);
        }
    }


    @ApiOperation(value = "购物车提交搭配师订单")
    @RequestMapping(value = "/shoppingCartToSubmit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult shoppingCartToDressSubmit(@Validated @RequestBody CommonRequest<ShoppingCartToSubmitReq> request) {
        log.info("购物车提交搭配师订单 入参：{}",request);
        ShoppingCartToSubmitReq requestData = request.getRequestData();

        String key = "shoppingCartToDressSubmit:" + requestData.getUnionId();
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "正在操作，请勿重试");
        }
        try {
            ResponseResult responseResult = iThemeActivityBoxService.checkThemeSavePara(requestData.getUnionId(), requestData.getFashionerId(), request.getRequestData());
            if (responseResult.getCode() != 0) {
                return responseResult;
            }

            DefaultBox defaultBox = iThemeActivityBoxService.packageSaveThemeBoxPara(requestData);
            // 校验商品明细
            ResponseResult checkProductResult = iBoxService.checkProductDetails(defaultBox, null);
            if (checkProductResult.getCode() != 0) {
                return checkProductResult;
            }

            iThemeActivityBoxService.themeActivityCheckBoxLjStoreProduct(defaultBox);
            List<String> cartItemIds = requestData.getCartItemIds();
            String cartBaseId = requestData.getCartBaseId();
            String fashionerId = requestData.getFashionerId();
            iThemeActivityBoxService.createActivityWithMatchBox(defaultBox, cartBaseId, cartItemIds, fashionerId);
            return ResponseResult.success();

        } finally {
            redissonUtil.unlock(key);
        }
    }


    @ApiOperation(value = "购物车提交导购订单")
    @RequestMapping(value = "/shoppingCartToGuideSubmit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult shoppingCartToGuideSubmit(@Validated @RequestBody CommonRequest<ShoppingCartToSubmitReq> request) {
        ShoppingCartToSubmitReq requestData = request.getRequestData();
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(requestData.getStoreIds()), "导购门店不能为空");

        String key = "shoppingCartToGuideSubmit:" + requestData.getUnionId();
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "正在操作，请勿重试");
        }

        try {
            ResponseResult responseResult = iThemeActivityBoxService.checkThemeSavePara(requestData.getUnionId(), requestData.getFashionerId(),request.getRequestData());
            if (responseResult.getCode() != 0) {
                return responseResult;
            }

            DefaultBox defaultBox = iThemeActivityBoxService.packageSaveThemeBoxPara(requestData);
            // 校验商品明细
            ResponseResult checkProductResult = iBoxService.checkProductDetails(defaultBox, requestData.getStoreIds());
            if (checkProductResult.getCode() != 0) {
                return checkProductResult;
            }

            List<String> cartItemIds = requestData.getCartItemIds();
            String cartBaseId = requestData.getCartBaseId();
            String fashionerId = requestData.getFashionerId();
            iThemeActivityBoxService.createActivityWithMatchBox(defaultBox, cartBaseId, cartItemIds, fashionerId);

            return ResponseResult.success();

        } finally {
            redissonUtil.unlock(key);
        }
    }


    @ApiOperation(value = "添加购物车前校验")
    @RequestMapping(value = "/validBeforeAddShopCart", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult validBeforeAddShopCart(@Validated @RequestBody CommonRequest<ValidBeforeAddShopCartReq> request) {

        ValidBeforeAddShopCartReq validBeforeAddShopCartReq = request.getRequestData();
        return iThemeActivityBoxService.validBeforeAddShopCart(validBeforeAddShopCartReq.getUnionId(), validBeforeAddShopCartReq.getBrandId(), validBeforeAddShopCartReq.getBrandName());
    }


    @ApiOperation(value = "是否可以要盒")
    @RequestMapping(value = "/validAskBox", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult validAskBox(@Validated @RequestBody CommonRequest<ValidAskBoxReq> request) {
        ValidAskBoxReq validAskBoxReq = request.getRequestData();
        return iAskBoxService.validDataBeforeAddShopCart(validAskBoxReq.getUnionId());
    }
}
