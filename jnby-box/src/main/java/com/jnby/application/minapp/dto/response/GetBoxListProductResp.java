package com.jnby.application.minapp.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/1/19 17:18
 */
@Data
public class GetBoxListProductResp implements Serializable {

    private String boxId;

    @ApiModelProperty("商品图")
    private List<String> productList;

    @ApiModelProperty("商品总数")
    private Integer productTotNum;

    @ApiModelProperty("补货数")
    private Long supplyNum;

    @ApiModelProperty("购买数")
    private Long payNum;

    @ApiModelProperty("未处理数（可购买和退回）")
    private Long unPayNum;
}
