package com.jnby.application.minapp.dto.response;

import com.jnby.module.order.context.CalcPriceContext;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/12/6 14:51
 */
@Data
public class CalcBoxPriceResp implements Serializable {

     @ApiModelProperty("计算日志单号")
     private String billNo;

    @ApiModelProperty("状态")
    private Integer ret;

    @ApiModelProperty("msg")
    private String msg;

    @ApiModelProperty("返回商品")
    private CalcPriceContext.OutGoods outGoods;

    @ApiModelProperty("返回优惠")
    private CalcPriceContext.OutDiscount outDiscount;

    @ApiModelProperty("返回券")
    private List<Voucher> voucherList;

    @ApiModelProperty("返回积分")
    private CalcPriceContext.Integral integral;

    @ApiModelProperty("本单可用的储值卡最大金额")
    private BigDecimal useBalanceAmt;

    @ApiModelProperty("商场代金券面额")
    private BigDecimal shopVouAmt;

    @Data
    public static class Voucher{
        @ApiModelProperty("券id")
        private Integer rowNo;
        @ApiModelProperty("券号")
        private String voucherNo;
        @ApiModelProperty("券类型:VOU3现金券 VOU4折扣券 VOU5兑换券")
        private String voucherType;
        @ApiModelProperty("券名称")
        private String voucherName;
        @ApiModelProperty("券状态：Y选中的券 S可选的券 N暂不可选的券 F不可用的券")
        private String flag;
        @ApiModelProperty("券折扣")
        private BigDecimal discount;
        @ApiModelProperty("券金额")
        private BigDecimal voucherAmount;
        @ApiModelProperty("金额门槛")
        private BigDecimal voucherLimit;
        @ApiModelProperty("截止日期")
        private String endDate;
        @ApiModelProperty("券描述")
        private String memo;
        @ApiModelProperty("不可用原因")
        private String remark;
    }

}
