package com.jnby.application.minapp.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/11/11 15:55
 */
@Data
public class BoxDetailPriceConditionResp implements Serializable{

    @ApiModelProperty("box详情Id")
    private String boxDetailsId;

    @ApiModelProperty("优惠价")
    private BigDecimal priceActual;

    private Long lockPromoId;

}
