package com.jnby.application.minapp.dto.response;

import com.jnby.infrastructure.box.model.StylingBoxCartBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Auther: lwz
 * @Date: 2022/4/27 17:30
 * @Description: GetShoppingCartCountResp
 * @Version 1.0.0
 */
@Data
public class GetShoppingCartCountResp {

    @ApiModelProperty("购物车数量")
    private int shoppingCartCount;

    @ApiModelProperty("购物车id")
    private String  stylingBoxCartBaseId;

    @ApiModelProperty("搭配ids")
    private List<String> themeMatchIds;
}
