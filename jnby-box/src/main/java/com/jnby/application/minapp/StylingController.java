package com.jnby.application.minapp;

import com.jnby.application.minapp.dto.request.*;
import com.jnby.application.minapp.dto.response.CalcPriceByProductCodeResp;
import com.jnby.application.minapp.dto.response.GetYdFashionerInfoAndThemeResp;
import com.jnby.base.entity.TagGroupEntity;
import com.jnby.base.service.*;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.enums.SearchRecordTypeEnum;
import com.jnby.common.enums.ThemeTypeEnum;
import com.jnby.common.util.StrUtil;
import com.jnby.infrastructure.box.model.Scene;
import com.jnby.infrastructure.box.model.SearchRecord;
import com.jnby.module.marketing.match.fashionerMatch.service.IBFashionerMatchService;
import com.jnby.module.marketing.styling.entity.StylingEntity;
import com.jnby.module.marketing.styling.service.IStylingService;
import com.jnby.module.shopcart.service.IBoxShoppingCartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springcenter.material.api.dto.StylingListContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/4/20
 */
@Controller
@RequestMapping("/minapp/styling")
@Api(value = "styling",tags = "有搭小程序接口")
public class StylingController {

    @Autowired
    IStylingService iStylingService;

    @Autowired
    ITagGroupService iTagGroupService;

    @Autowired
    ICustomerDetailsService iCustomerDetailsService;

    @Autowired
    IFashionerService iFashionerService;

    @Autowired
    ISearchRecordService iSearchRecordService;

    @Autowired
    private IBFashionerMatchService ibFashionerMatchService;

    @Autowired
    private ISceneQrcodeService sceneQrcodeService;

    @Autowired
    private IBoxShoppingCartService boxShoppingCartService;


    @ApiOperation(value = "有搭列表")
    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseResult list(@RequestBody CommonRequest<QueryStyingReq> request) {
        QueryStyingReq requestData = request.getRequestData();
        StylingListContext stylingListContext = new StylingListContext();
        BeanUtils.copyProperties(requestData,stylingListContext);
        List<String> dpsex = new ArrayList();
        if(requestData.getDpsex() == 0){
            dpsex.add("71");
            dpsex.add("72");
            dpsex.add("73");
        }else{
            dpsex.add(requestData.getDpsex().toString());
        }
        stylingListContext.setDpsex(dpsex);

        // 搜索
        if(StrUtil.isNotBlank(requestData.getSearch())){
            SearchRecord searchRecord = new SearchRecord();
            searchRecord.setContent(requestData.getSearch());
            searchRecord.setUnionid(requestData.getUnionId());
            searchRecord.setType(SearchRecordTypeEnum.THEME.getCode().shortValue());
            iSearchRecordService.insert(searchRecord);
        }

        // 标签
        if(StrUtil.isNotBlank(requestData.getStyle())){
            String[] tags = requestData.getStyle().split(",");
            stylingListContext.setLabelIds(Arrays.asList(tags));
        }

        Page page = request.getPage();
        List<StylingEntity> stylingEntities = iStylingService.stylingList(page, stylingListContext);

        return ResponseResult.success(stylingEntities,page);
    }


    @ApiOperation(value = "搜索条件加载")
    @ResponseBody
    @RequestMapping(value = "/SearchCondition/{unionId}", method = RequestMethod.GET)
    public ResponseResult list(@PathVariable String unionId) {
        Map map = new HashMap<>();
        Integer ifFashioner = 0;
        Integer isSales = Optional.ofNullable(iCustomerDetailsService.findByUnionId(unionId))
                .map(customer -> iFashionerService.findByPhone(customer.getPhone()))
                .map(fashioner -> iFashionerService.findIsSalesById(fashioner.getId()))
                .orElse(null);
        if(isSales != null && isSales == 0){
            ifFashioner = 1;
        }
        map.put("ifFashioner",ifFashioner);
        List<TagGroupEntity> groupList = iTagGroupService.findEntityListByType(ThemeTypeEnum.STYLE.getCode());
        map.put("groupList",groupList);
        return ResponseResult.success(map);

    }

    @ApiOperation(value = "历史搜索记录")
    @ResponseBody
    @RequestMapping(value = "/ydHistorySearch", method = RequestMethod.POST)
    public ResponseResult<List<SearchRecord>> ydHistorySearch(@Validated @RequestBody CommonRequest<YdHistorySearchReq> commonRequest){
        Page page = commonRequest.getPage();
        return ResponseResult.success(iSearchRecordService.ydHistorySearch(commonRequest.getRequestData(),page),page);
    }


    @ApiOperation(value = "删除历史搜索记录")
    @ResponseBody
    @RequestMapping(value = "/delHistorySearch", method = RequestMethod.POST)
    public ResponseResult delHistorySearch(@RequestBody CommonRequest<YdHistorySearchReq> commonRequest){
        SearchRecord del = new SearchRecord();
        del.setType(SearchRecordTypeEnum.THEME.getCode().shortValue());
        del.setIsDel((short)0);
        del.setUnionid(commonRequest.getRequestData().getUnionId());
        iSearchRecordService.del(del);
        return ResponseResult.success();
    }



    @ApiOperation(value = "搭配详情搭配师信息")
    @ResponseBody
    @RequestMapping(value = "/getYdFashionerInfoAndTheme", method = RequestMethod.POST)
    public ResponseResult<GetYdFashionerInfoAndThemeResp> getYdFashionerInfoAndTheme(@RequestBody CommonRequest<GetYdFashionerInfoAndThemeReq> commonRequest){
        return ResponseResult.success(ibFashionerMatchService.getYdFashionerInfoAndTheme(commonRequest.getRequestData()));
    }



    @ApiOperation(value = "更新海报图信息")
    @ResponseBody
    @RequestMapping(value = "/updateScene", method = RequestMethod.POST)
    public ResponseResult updateScene(@RequestBody CommonRequest<Scene> commonRequest){
        sceneQrcodeService.updateScene(commonRequest.getRequestData());
        return ResponseResult.success();
    }

    @ApiOperation(value = "搭配详情商品信息")
    @ResponseBody
    @RequestMapping(value = "/getYdDetails", method = RequestMethod.POST)
    public ResponseResult<GetYdDetailsResp> getYdDetails(@RequestBody CommonRequest<GetYdDetailsReq> request){
        GetYdDetailsResp resp = ibFashionerMatchService.getYdDetails(request.getRequestData());
        return ResponseResult.success(resp);
    }


    @ApiOperation(value = "多增加接口  ：  根据前台传递的产品code product_code 去es中查询sku  skuid 然后做价格操作  计算单品价格")
    @ResponseBody
    @RequestMapping(value = "/calcPriceByProductCode", method = RequestMethod.POST)
    public ResponseResult<CalcPriceByProductCodeResp> calcPriceByProductCode(@Validated @RequestBody CommonRequest<CalcPriceByProductCodeReq> request){
        CalcPriceByProductCodeResp resp = ibFashionerMatchService.calcPriceByProductCode(request.getRequestData());
        if(resp == null){
            return ResponseResult.error(-1,"计算价格出错");
        }
        return ResponseResult.success(resp);
    }
}
