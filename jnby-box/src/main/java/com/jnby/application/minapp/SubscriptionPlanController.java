package com.jnby.application.minapp;

//EffectiveForBox

import com.alibaba.fastjson.JSONObject;
import com.jnby.application.BaseController;
import com.jnby.application.minapp.dto.request.SubscriptionPlanReq;
import com.jnby.base.repository.*;
import com.jnby.base.service.ISubscriptionService;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.infrastructure.box.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 用户的订阅服务计划
 * @date 2021/8/16
 */
@Slf4j
@Controller
@RequestMapping("/minapp/subscription")
@Api(value = "SubscriptionApp", tags = "订阅计划接口")
public class SubscriptionPlanController extends BaseController {

    @Autowired
    private ISubscriptionService iSubscriptionService;

    @Autowired
    private IScheduleQuestionRepository iScheduleQuestionRepository;




    @ApiOperation(value = "小程序获取场景值码，跳转小程序")
    @RequestMapping(value = "/getMinAppScheme/{nowTime}", method = RequestMethod.GET)
    @ResponseBody
    public ResponseResult getMinAppScheme(@PathVariable String nowTime) {
        JSONObject minAppScheme = iSubscriptionService.getMinAppScheme(nowTime.equals("null") ? null : nowTime);
        return ResponseResult.success(minAppScheme);

    }

    @ApiOperation(value = "输入订阅服务计划的问题及答复")
    @RequestMapping(value = "/getQuestionAndAnswer", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult getQuestionAndAnswer(@RequestBody CommonRequest<List<BScheduleQuestion>> request) {

        List<BScheduleQuestion> requestData = request.getRequestData();
        requestData.forEach(question -> {
            iScheduleQuestionRepository.insertSelective(question);
        });

        return ResponseResult.success();

    }

}
