package com.jnby.application.minapp;

import com.alibaba.fastjson.JSONObject;
import com.jnby.application.BaseController;
import com.jnby.application.admin.dto.request.BoxRefundSaveReq;
import com.jnby.base.entity.ConditionAttr;
import com.jnby.base.service.IAttrService;
import com.jnby.common.BoxException;
import com.jnby.common.CommonRequest;
import com.jnby.common.ErrorConstants;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.RedissonUtil;
import com.jnby.infrastructure.box.model.AttrValue;
import com.jnby.infrastructure.box.model.Order;
import com.jnby.module.marketing.coupon.service.ILotteryService;
import com.jnby.module.order.enums.BoxRefundRemarkEnum;
import com.jnby.module.order.service.IOrderService;
import com.jnby.module.order.service.IRefundService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


@RequestMapping("/minapp/refund")
@RestController
@Api(value = "refundApi",tags = "小程序售后")
@RefreshScope
@Slf4j
public class MinappRefundController extends BaseController {

    @Autowired
    private IRefundService refundService;

    @Autowired
    private IOrderService orderService;

    @Resource
    private RedissonUtil redissonUtil;

    @Autowired
    private IAttrService attrService;

    @Autowired
    private ILotteryService lotteryService;


    /**
     * 小程序端创建售后单
     */
    @PostMapping(value = "/refundSave")
    @ResponseBody
    @ApiOperation(value = "小程序端创建售后单")
    public ResponseResult refundSave(@Validated @RequestBody CommonRequest<BoxRefundSaveReq> request){
        BoxRefundSaveReq req = Optional.ofNullable(request.getRequestData()).orElseThrow(() -> new BoxException(ErrorConstants.PARAMS_ERROR.getCode(), "参数不可为空"));
        //
        Order order = new Order();
        order.setOrderSn(req.getOrderSn());
        List<Order> orders = orderService.selectOrderListBySelective(order);
        if(CollectionUtils.isEmpty(orders)){
            return ResponseResult.error("该orderSn未查询到数据");
        }

        String key = "admin.refund.saveRefundOrder:" + orders.get(0).getId();
        try {
            if(!redissonUtil.tryLock(key)){
                return ResponseResult.error("请稍后再试!");
            }
            refundService.save(request.getRequestData());

            /**
             * 重置抽奖资格
             */
            try {
                lotteryService.resetDrawOrder(orders.get(0).getBoxSn(),orders.get(0).getCustomerId());
            }catch (Exception e){
                log.error("refundSave 小程序端重置抽奖资格报错 = {}",e);
            }

            return ResponseResult.success();
        }catch (Exception e){
            log.error("refundSave 小程序端创建售后单出错  params = {} ,  e = {} ",JSONObject.toJSONString(request.getRequestData()),e);
            return ResponseResult.error(-1,e.getMessage());
        }finally {
            redissonUtil.unlock(key);
        }
    }





    @ApiOperation(value = "售后原因列表")
    @ResponseBody
    @RequestMapping(value = "/refundReasonCondition",method = RequestMethod.POST)
    public ResponseResult<List<ConditionAttr>> refundReasonCondition(){
        List<AttrValue> gender = attrService.selectByParentId("94");
        List<ConditionAttr> conditionAttrs = attrService.transCheckData(gender);
        //去除买贵退差
        List<ConditionAttr> notHaveBuy = conditionAttrs.stream().filter(r -> !r.getName().equals(BoxRefundRemarkEnum.REFUND_THE_PRICE_DIFFERENCE.getCode())
        && !r.getName().equals(BoxRefundRemarkEnum.REFUND_AFTER_EXCHANGE.getCode())
        ).collect(Collectors.toList());
        return ResponseResult.success(notHaveBuy);
    }


    /**
     * 取消售后
     */
    @ApiOperation(value = "取消售后  requestData里面直接传refundId  退款id  ")
    @ResponseBody
    @RequestMapping(value = "/cancelRefund",method = RequestMethod.POST)
    public ResponseResult cancelRefund(@RequestBody CommonRequest<String> request){
        refundService.cancelRefund(request.getRequestData());
        return  ResponseResult.success();
    }


    @ApiOperation(value = "获取  售后地址   requestData里面直接传refundId  退款id")
    @ResponseBody
    @RequestMapping(value = "/getLogistics",method = RequestMethod.POST)
    public ResponseResult<Map<String,Object>> getLogistics(@RequestBody CommonRequest<String> request){
        String requestData = request.getRequestData();
        if(StringUtils.isBlank(requestData)){
            return ResponseResult.error(-1,"退款id不能为空!");
        }
        Map<String, Object> logistics = refundService.getLogistics(request.getRequestData(),null);
        return  ResponseResult.success(logistics);
    }

}
