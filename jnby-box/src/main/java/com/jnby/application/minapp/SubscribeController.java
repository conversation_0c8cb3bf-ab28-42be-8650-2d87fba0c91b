package com.jnby.application.minapp;


import com.jnby.application.minapp.dto.request.*;
import com.jnby.application.minapp.dto.response.MiniAppGetSubscribeTypeResp;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseCodeEnum;
import com.jnby.common.ResponseResult;
import com.jnby.common.cache.RedisKeysEnum;
import com.jnby.common.util.RedissonUtil;
import com.jnby.module.order.service.box.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @Date: 2022/6/1 10:01
 * @Description: SubscribeController
 * @Version 1.0.0
 */
@Slf4j
@Controller
@RequestMapping("/minapp/subscribe")
@Api(value = "subscribe", tags = "订阅接口")
public class SubscribeController {

    @Resource
    private ISubscribeByWeiXinService iSubscribeByWeiXinService;



    @Resource
    private RedissonUtil redissonUtil;



    @ApiOperation(value = "微信支付订阅")
    @PostMapping(value = "/subscribeByWeiXinPay")
    @ResponseBody
    public ResponseResult subscribeByWeiXinPay(@Validated @RequestBody CommonRequest<SubscribeByWeiXinPayReq> request) {
        String key = "subscribeByWeiXinPay:" + request.getRequestData().getUnionId();
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "正在操作，请勿重试");
        }
        try {
            return ResponseResult.success(iSubscribeByWeiXinService.subscribeByWeiXinPay(request.getRequestData()));
        } finally {
            redissonUtil.unlock(key);
        }

    }


    @ApiOperation(value = "微信支付分订阅")
    @PostMapping(value = "/paySourceBySubscribe")
    @ResponseBody
    public ResponseResult paySourceBySubscribe(@Validated @RequestBody CommonRequest<PaySourceBySubscribeReq> request) {
        String key = "paySourceBySubscribe:" + request.getRequestData().getUnionId();
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "正在操作，请勿重试");
        }
        try {
            return ResponseResult.success(iSubscribeByWeiXinService.subscribeByPaySource(request.getRequestData().getUnionId(), request.getRequestData().getChannelId()));
        } finally {
            redissonUtil.unlock(key);
        }
    }


    @ApiOperation(value = "取消支付")
    @PostMapping(value = "/cancelPay")
    @ResponseBody
    public ResponseResult cancelPay(@Validated @RequestBody CommonRequest<CancelPayReq> request) {
        CancelPayReq cancelPayReq = request.getRequestData();

        String key = "cancelPay:" + cancelPayReq.getUnionId();
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "正在操作，请勿重试");
        }
        try {
            iSubscribeByWeiXinService.cancelPay(cancelPayReq.getUnionId(), cancelPayReq.getPaymentSn());
            return ResponseResult.success();
        } finally {
            redissonUtil.unlock(key);
        }

    }


    @ApiOperation(value = "查询微信支付订阅订单状态")
    @PostMapping(value = "/queryWePayCreditOrder")
    @ResponseBody
    public ResponseResult queryWePayCreditOrder(@Validated @RequestBody CommonRequest<QueryWePayCreditOrderReq> request) {
        QueryWePayCreditOrderReq queryWePayCreditOrderReq = request.getRequestData();

        // 加锁,避免重复推送
        String key = RedisKeysEnum.PAY_NOTIFY.join(queryWePayCreditOrderReq.getOutTradeNo());
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "正在操作，请勿重试");
        }
        try {
            iSubscribeByWeiXinService.queryWePayCreditOrder(queryWePayCreditOrderReq.getOutTradeNo());
            return ResponseResult.success();
        } finally {
            redissonUtil.unlock(key);
        }

    }




    @ApiOperation(value = "查询支付分订单是否确认")
    @PostMapping(value = "/queryWxBillOrder")
    @ResponseBody
    public ResponseResult queryWxBillOrder(@Validated @RequestBody CommonRequest<QueryWxBillOrderReq> request) {
        QueryWxBillOrderReq queryWxBillOrderReq = request.getRequestData();
        // 加锁,避免重复推送
        String key = RedisKeysEnum.PAY_NOTIFY.join(queryWxBillOrderReq.getOrderNo());
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "正在操作，请勿重试");
        }
        try {
            iSubscribeByWeiXinService.queryWxBillOrder(queryWxBillOrderReq.getOrderNo(), queryWxBillOrderReq.getQueryId());
            return ResponseResult.success();
        }finally {
            redissonUtil.unlock(key);
        }
    }






    @ApiOperation(value = "查询SQB支付订阅订单状态")
    @PostMapping(value = "/queryOrderBySqbPay")
    @ResponseBody
    public ResponseResult queryOrderBySqbPay(@Validated @RequestBody CommonRequest<QueryOrderBySqbPayReq> request) {
        QueryOrderBySqbPayReq queryOrderBySqbPayReq = request.getRequestData();
        // 加锁,避免重复推送
        String key = RedisKeysEnum.PAY_NOTIFY.join(queryOrderBySqbPayReq.getSqbSn());
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.error(ResponseCodeEnum.FAIL.getCode(), "正在操作，请勿重试");
        }
        try {
            iSubscribeByWeiXinService.queryOrderBySqbPay(queryOrderBySqbPayReq.getSqbSn());
            return ResponseResult.success();
        } finally {
            redissonUtil.unlock(key);
        }

    }

}
