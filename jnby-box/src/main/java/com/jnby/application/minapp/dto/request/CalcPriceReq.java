package com.jnby.application.minapp.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/12/6 13:29
 */
@Data
public class CalcPriceReq implements Serializable {

    @ApiModelProperty("是否序列化")
    private Boolean ifSerialization = false;

    @ApiModelProperty(value = "是否参与商场折扣",required = true)
    private Boolean ifJoinShopDiscount;

    @ApiModelProperty("商场折扣")
    private BigDecimal shopDiscount;

    @ApiModelProperty("商品详情")
    private List<Detail> boxDetailList;

    @ApiModelProperty("选中券列表")
    private List<Long> vouIds;

    @ApiModelProperty("选中积分策略列表")
    private List<Long> integralActIds;

    @ApiModelProperty("是否系统推荐优惠券")
    private Boolean ifRecommendVou = false;


    @ApiModelProperty("扫入优惠券")
    private List<Long> scannedVouIds;

    @ApiModelProperty("使用的余额")
    private BigDecimal useBalance;

    @NotEmpty(message = "服务单id不能为空")
    private String boxId;

    @ApiModelProperty("是否来自小程序")
    private Boolean fromMiniApp = false;

    @ApiModelProperty("满赠活动id")
    private String fullActivityId;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Detail implements Serializable {
        @ApiModelProperty("服务单详情id")
        private String id;

        @ApiModelProperty("是否使用系统推荐")
        private Boolean ifUseRecommend = true;

        @ApiModelProperty("改价价格")
        private String changePrice;

        @ApiModelProperty("该商品是否参与商场折扣")
        private Boolean ifJoinShopDiscount;

        @ApiModelProperty("是否参与会员折扣")
        private Boolean ifJoinVipDis = true;

        @ApiModelProperty("选择的促销策略id")
        private List<Long> manualPromoIdList;

    }
}
