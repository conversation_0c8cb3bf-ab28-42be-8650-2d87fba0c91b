package com.jnby.application.minapp;


import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.jnby.application.minapp.dto.request.FaqListReqDto;
import com.jnby.application.minapp.dto.request.FaqSolvedReqDto;

import com.jnby.application.minapp.dto.response.FaqListResp;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.infrastructure.box.mapper.CommonQuestionCategoryMapper;
import com.jnby.infrastructure.box.mapper.SysConfigMapper;
import com.jnby.infrastructure.box.model.*;
import com.jnby.module.order.service.ICommonQuestionService;
import com.jnby.module.order.service.ICustomerQuestionCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/minapp/faq")
@RefreshScope
@Api(value = "faq", tags = "常见问题")
public class FaqController {
    @Resource
    private SysConfigMapper sysConfigMapper;

    @Resource
    private ICommonQuestionService iCommonQuestionService;

    @Resource
    private IdLeafService idLeafService;

    @Resource
    private ICustomerQuestionCategoryService iCustomerQuestionCategoryService;

    @Resource
    private CommonQuestionCategoryMapper commonQuestionCategoryMapper;



    @ApiOperation(value = "常见问题列表")
    @RequestMapping(value = "/faqList", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<FaqListResp>> faqList(@RequestBody CommonRequest<FaqListReqDto> request) {
        String search= request.getRequestData().getSearch();

        List<Question> questions =  commonQuestionCategoryMapper.getQuestionList(search);
        List<FaqListResp> faqListRespList = new ArrayList<>();
        if (CollectionUtils.isEmpty(questions)) {
            return ResponseResult.success(faqListRespList);
        }

        Multimap<String,Question > questionMultiMap= ArrayListMultimap.create();
        questions.forEach(e -> questionMultiMap.put(e.getCategoryName(), e));
        Map<String, Collection<Question>> map = questionMultiMap.asMap();

        map.keySet().forEach(e -> {
            FaqListResp faqListResp = new FaqListResp();
            faqListResp.setQuestionList((List<Question>)map.get(e));
            faqListResp.setCategoryName(e);
            if(CollectionUtils.isNotEmpty(faqListResp.getQuestionList())){
                faqListResp.setImg(faqListResp.getQuestionList().get(0).getImg());
            }
            faqListRespList.add(faqListResp);
        });
        return ResponseResult.success(faqListRespList);
    }


    @ApiOperation(value = "获取系统配置")
    @RequestMapping(value = "/config", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<SysConfigWithBLOBs> config() {
        return ResponseResult.success(sysConfigMapper.selectOne());
    }


    @ApiOperation(value = "是否解决")
    @RequestMapping(value = "/faqSolved", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult faqSolved(@RequestBody CommonRequest<FaqSolvedReqDto> request) {
        FaqSolvedReqDto faqSolvedReqDto = request.getRequestData();

        String unionId = faqSolvedReqDto.getUnionId();
        String customer_question_category_id = faqSolvedReqDto.getCustomerQuestionCategoryId();
        Integer isSolve = faqSolvedReqDto.getIsSolve();

        CommonQuestion commonQuestionCategory = iCommonQuestionService.getById(customer_question_category_id);
        if (ObjectUtils.isEmpty(isSolve)) {
            return ResponseResult.success(commonQuestionCategory);
        }

        Date nowDate = new Date();
        CustomerQuestionCategory customerQuestionCategory = new CustomerQuestionCategory();
        customerQuestionCategory.setId(idLeafService.getId());
        customerQuestionCategory.setUnionid(unionId);
        customerQuestionCategory.setIssolve(isSolve);
        customerQuestionCategory.setCommonQuestionId(customer_question_category_id);
        customerQuestionCategory.setCreateTime(nowDate);
        customerQuestionCategory.setUpdateTime(nowDate);
        iCustomerQuestionCategoryService.save(customerQuestionCategory);
        return ResponseResult.success(commonQuestionCategory);
    }


}
