package com.jnby.application.minapp.dto.response;

import com.jnby.common.BaseSerializable;
import com.jnby.infrastructure.box.model.Fashioner;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(value="auth 返回信息 出参", description="auth 返回信息相关出参")
public class CustomerAuthResp extends BaseSerializable {

    @ApiModelProperty(value = "用户信息")
    private UserInfo userInfo;

    @ApiModelProperty(value = "sessionId信息")
    private String sessionId;

    @ApiModelProperty(value = "status信息")
    private Integer status;


    @Data
    public static class UserInfo extends BaseSerializable {

        private String id;

        private String openId;

        private String unionId;

        private String nickName;

        private Long gender;
        private String avatarUrl;

        private String phone;

    }

}
