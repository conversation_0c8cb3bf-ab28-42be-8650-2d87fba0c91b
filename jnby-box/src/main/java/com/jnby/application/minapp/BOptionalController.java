package com.jnby.application.minapp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jnby.application.BaseController;
import com.jnby.application.minapp.dto.request.FindStylingByOptionalIdReq;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.cache.RedisKeysEnum;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.infrastructure.box.model.BOptionalChannel;
import com.jnby.module.marketing.styling.entity.StylingEntity;
import com.jnby.module.marketing.styling.service.IBOptionalChannelService;
import com.jnby.module.marketing.styling.service.IStylingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/19
 */
@Controller
@RequestMapping("/minapp/optionalChannel")
@Api(value = "bOptional",tags = "自选频道小程序接口")
public class BOptionalController extends BaseController {

    @Autowired
    IBOptionalChannelService ibOptionalChannelService;

    @Autowired
    IStylingService iStylingService;

    @Autowired
    RedisPoolUtil redisPoolUtil;

    @ApiOperation(value = "自选频道列表")
    @ResponseBody
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    public ResponseResult list(){
        BOptionalChannel optionalChannel = new BOptionalChannel();
        optionalChannel.setStatus(1);
        List<BOptionalChannel> list = ibOptionalChannelService.findList(optionalChannel);
        return ResponseResult.success(list);
    }

    @ApiOperation(value = "根据频道ID查询有搭列表")
    @ResponseBody
    @RequestMapping(value = "/findStylingByOptionalId",method = RequestMethod.POST)
    public ResponseResult findStylingByOptionalId(@RequestBody CommonRequest<FindStylingByOptionalIdReq> request){
        FindStylingByOptionalIdReq requestData = request.getRequestData();
        String id = requestData.getId();
        Page page = request.getPage();
//        String redisKey = RedisKeysEnum.OPTIONAL_STYING_LIST.join(id,page.getPageNo());
        // 获取缓存数据
//        if(page.getPageNo() == 1){
//            String jsonStr = RedisTemplateUtil.get(redisPoolUtil, redisKey);
//            if(StringUtils.isNotBlank(jsonStr)){
//                HashMap hashMap = JSONObject.parseObject(jsonStr, HashMap.class);
//                ObjectMapper objectMapper = new ObjectMapper();
//                Page cachePage = objectMapper.convertValue(hashMap.get("page"), Page.class);
//                return ResponseResult.success(hashMap.get("themeList"),cachePage);
//            }
//        }
        List<StylingEntity> themeList = ibOptionalChannelService.getStyleListByChannleId(page, id);
        // 缓存第一页数据，5分钟
//        if(page.getPageNo() == 1 && CollectionUtils.isNotEmpty(themeList)){
//            HashMap map = new HashMap<>();
//            map.put("themeList",themeList);
//            map.put("page",page);
//            RedisTemplateUtil.setex(redisPoolUtil,redisKey, JSON.toJSONString(map),60*5);
//        }
        return ResponseResult.success(themeList,page);
    }



}
