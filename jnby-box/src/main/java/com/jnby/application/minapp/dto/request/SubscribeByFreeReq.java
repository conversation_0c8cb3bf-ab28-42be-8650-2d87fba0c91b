package com.jnby.application.minapp.dto.request;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SubscribeByFreeReq {
    @NotBlank(message = "unionId不能为空")
    @ApiModelProperty(value = "unionId",required = true)
    private String unionId;

    @NotBlank(message = "openId不能为空")
    @ApiModelProperty(value = "openId",required = true)
    private String openId;

    @ApiModelProperty(value = "搭配师id")
    private String fashionerId;


    private String channelId;
}
