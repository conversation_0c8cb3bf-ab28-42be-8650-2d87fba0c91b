package com.jnby.application.minapp.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class CancelOthersPayReq {
    @ApiModelProperty(value = "id")
    @NotBlank(message = "id 不能为空")
    private String id;


    @ApiModelProperty(value = "ownerUnionId")
    @NotBlank(message = "ownerUnionId 不能为空")
    private String ownerUnionId;


    @ApiModelProperty(value = "orderSn")
    @NotBlank(message = "orderSn 不能为空")
    private String orderSn;

}
