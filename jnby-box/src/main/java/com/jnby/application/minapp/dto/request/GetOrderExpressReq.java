package com.jnby.application.minapp.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/11/17 9:25
 */
@Data
public class GetOrderExpressReq implements Serializable {

    @ApiModelProperty("来源id,服务单为boxId，有搭为订单id")
    private String sourceId;
    @ApiModelProperty("类型(用户角度)：0全部 1收到的 2发出的")
    private Integer type;
    @ApiModelProperty("是否有搭")
    private boolean isYd = false;
}
