package com.jnby.application.minapp.dto.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "微信支付分订阅 请求信息 入参", description = "微信支付分订阅   请求信息相关入参")
public class PaySourceBySubscribeReq {

    @ApiModelProperty(value = "unionId")
    @NotBlank(message = "unionId 不能为空")
    private  String unionId;

    private String channelId;

}
