package com.jnby.application.minapp.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="根据BoxId获取微盟转换 请求信息 入参", description="根据BoxId获取微盟转换 请求信息相关入参")
public class ConvertByBoxIdReq {

    @ApiModelProperty(value = "boxId", required = true)
    @NotEmpty(message = "boxId不能为空")
    private String boxId;
}
