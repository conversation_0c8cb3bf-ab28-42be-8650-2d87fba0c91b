package com.jnby.application.minapp.dto.response;

import com.jnby.infrastructure.box.model.Logistics;
import com.jnby.module.order.entity.SettlementProduct;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/23 1:37 下午
 * @Version 1.0
 */
@Data
public class SettlementProductResp implements Serializable {

    /**
     * 是否有还货商品
     */
    private int hasReturn;

    private String activityMsg;

    private String boxSn;

    private String ifJoinShop;

    private String shopDiscount;

    private Long sourceType;

    /**
     * 物流信息
     */
    private Logistics logistics;

    /**
     * 商品信息
     */
    private List<SettlementProduct> productList;
}
