package com.jnby.application.minapp.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


@Data
public class NewOrderDetailReqDto {
    @ApiModelProperty(value = "boxSn")
    @NotBlank(message = "boxSn不能为空")
    private String boxSn;



    @ApiModelProperty(value = "unionId")
    @NotBlank(message = "unionId不能为空")
    private String unionId;



    @ApiModelProperty(value = "customerId")
    @NotBlank(message = "customerId不能为空")
    private String customerId;
}
