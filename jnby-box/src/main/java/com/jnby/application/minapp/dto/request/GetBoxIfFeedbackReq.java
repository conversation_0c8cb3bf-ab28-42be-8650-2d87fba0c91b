package com.jnby.application.minapp.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GetBoxIfFeedbackReq {
    @ApiModelProperty(value = "boxIdList")
    @NotEmpty(message = "boxIdList不能为空")
    private List<String> boxIdList;
}
