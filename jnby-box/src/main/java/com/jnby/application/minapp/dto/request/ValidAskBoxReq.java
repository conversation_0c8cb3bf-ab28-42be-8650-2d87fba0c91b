package com.jnby.application.minapp.dto.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "校验主题能否要盒 请求信息 入参", description = "校验主题能否要盒   请求信息相关入参")
public class ValidAskBoxReq {
    @NotBlank(message = "unionId不能为空")
    @ApiModelProperty(value = "unionId")
    private String unionId;
}
