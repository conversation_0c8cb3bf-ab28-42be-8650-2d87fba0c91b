package com.jnby.application.minapp.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class EditFavorReq {

    @NotBlank(message = "用户id必传")
    @ApiModelProperty(value = "unionId")
    private String unionId;

    @NotNull(message = "bFashionerMatchIds 必传")
    @ApiModelProperty(value = "bFashionerMatchIds")
    private List<String> bFashionerMatchIds;

    @NotNull(message = "是否收藏必传")
    @ApiModelProperty(value = "0  取消收藏   1 收藏")
    private Integer status;
}
