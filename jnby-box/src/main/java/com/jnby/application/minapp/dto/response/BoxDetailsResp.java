package com.jnby.application.minapp.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/11/9 13:41
 */
@Data
public class  BoxDetailsResp implements Serializable {
    @ApiModelProperty("服务主单id")
    private String boxId;

    @ApiModelProperty("box单号")
    private String boxSn;

    @ApiModelProperty("服务单类型")
    private Long type;

    @ApiModelProperty("下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("状态")
    private Long status;

    @ApiModelProperty("搭配介绍")
    private String matchMemo;

    @ApiModelProperty("兑换券提示")
    private String coinVou = "您有%s金币，可兑换%s元代金券";

    @ApiModelProperty("活动提醒")
    private String syncStore = "已与您会员卡所在门店同步活动";

    @ApiModelProperty("试穿到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date tryOutTime;

    @ApiModelProperty("已购买商品")
    private List<PayProduct> payProductList;

    @ApiModelProperty("未购买商品")
    private List<Product> unPayProductList;

    @ApiModelProperty("无货商品")
    private List<Product> noProductList;

    @ApiModelProperty("是否可以申请退款")
    private boolean isOpenRefundMenu;

    @ApiModelProperty("搭配师或导购名称")
    private String fashionerName;

    @ApiModelProperty("来源类型： 0免费盒子  1订阅主动要盒 2单次主动要盒")
    private Long sourceType;

    @ApiModelProperty("创建来源id")
    private String sourceId;

    @ApiModelProperty("信用订单id")
    private String creditId;


    @Data
    public static class PayProduct implements Serializable{
        @ApiModelProperty("订单号")
        private String orderSn;

        @ApiModelProperty("订单id")
        private String orderId;

        private List<Product> productList;
    }

    @Data
    public static class Product implements Serializable{
        private String id;

        @ApiModelProperty("商品编号")
        private String productNo;

        @ApiModelProperty("款号")
        private String productCode;

        @ApiModelProperty("名称")
        private String productName;

        @ApiModelProperty("品牌")
        private String productBrand;

        @ApiModelProperty("颜色")
        private String productColor;

        @ApiModelProperty("颜色码")
        private String productColorNo;

        @ApiModelProperty("尺码")
        private String productSize;

        @ApiModelProperty("spuId")
        private String spuId;

        @ApiModelProperty("sku")
        private String sku;

        @ApiModelProperty("skuId")
        private String skuId;

        @ApiModelProperty("商品图")
        private String imgUrl;

        @ApiModelProperty("状态")
        private Long status;

        @ApiModelProperty("售后状态")
        private Long refundStatus;

        @ApiModelProperty("购买状态")
        private Long orderStatus;

        @ApiModelProperty("标准价")
        private String productPrice;

        @ApiModelProperty("实付价(已购买商品)")
        private String priceActual;

        @ApiModelProperty("换码状态: 10可以换尺码   20已换尺码  30已更换为该尺码")
        private Long type;

        @ApiModelProperty("换码备注")
        private String remark;

        @ApiModelProperty("换码前detailId")
        private String originalId;

        @ApiModelProperty("是否补货")
        private Boolean ifSupply = false;


        @ApiModelProperty("退款原因  ：  买贵退差  或者 不是 买贵退差")
        private String refundRemark;
    }

}
