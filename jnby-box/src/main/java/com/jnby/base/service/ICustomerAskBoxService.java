package com.jnby.base.service;

import com.jnby.application.admin.dto.request.*;
import com.jnby.application.admin.dto.response.BatchCheckDealerResp;
import com.jnby.application.admin.dto.response.CustomerAskBoxInfoByIdResp;
import com.jnby.application.admin.dto.response.ShoppingGuideOvertimeListResp;
import com.jnby.application.admin.dto.response.StaticsticFashionerDataResp;
import com.jnby.application.minapp.dto.response.CustomerAskBoxResp;
import com.jnby.base.entity.CompletionCustomerAskBoxEntity;
import com.jnby.base.entity.CustomerAskBoxListEntity;
import com.jnby.base.entity.FashionerInfoEntity;
import com.jnby.base.entity.SetSub2AskBoxEntity;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.infrastructure.box.model.BoxWithBLOBs;
import com.jnby.infrastructure.box.model.CustomerAskBox;
import com.jnby.infrastructure.box.model.CustomerAskBoxLog;
import com.jnby.infrastructure.box.model.CustomerDetails;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/27
 */
public interface ICustomerAskBoxService {

    CustomerAskBox findById(String id);


    Integer checkGender(List<String> brandList);


    List<CustomerAskBoxListEntity> getList(Page page, AskBoxListReq req,String userId);

    /**
     * 按主键更新
     * @param record
     * @return
     */
    void updateByPrimaryKey(CustomerAskBox record);


    /**
     * 获取主动要盒留言
     * @param customerAskBoxId
     * @return
     */
    Map findMsg(String customerAskBoxId);


    /**
     * 取消主动要盒
     * @param req
     * @param userId
     */
    void cancelAskBox(CancelAskBoxReq req,String userId);

    void recoverSubPlan(CustomerAskBox customerAskBox,  Long operateType, String operatePeople, CustomerDetails customerDetails);

    /**
     * 获取可更换搭配师
     * @param id
     */
    List<FashionerInfoEntity> getChangeFashioner(String id);

    /**
     * 更换搭配师
     * @param req
     * @param userId
     * @param code
     * @return
     */
    ResponseResult changeFashioner(ChangeFashionerReq req, String userId, Integer code);

    /**
     * 失效主动要盒搭盒数据
     */
    void invalidBoxMakingRecord(CustomerAskBox askBox);


    /**
     * 查询列表
     * @param customerAskBox
     * @return
     */
    List<CustomerAskBox> selectListBySelective(CustomerAskBox customerAskBox);

    /**
     * 查询用户要盒记录
     * @param unionId
     * @return
     */
    List<CustomerAskBoxListEntity> findCustomerAskList(String unionId);


    /**
     * 导购超时要盒
     * @param page
     * @param requestData
     * @param userId
     * @return
     */
    List<ShoppingGuideOvertimeListResp> getShoppingGuideOvertimeList(Page page, ShoppingGuideOvertimeListReq requestData, String userId);

    /**
     * 抢单逻辑
     * @param requestData
     * @param user_id
     * @return
     */
    ResponseResult grabOrder(GrabOrderReq requestData, String user_id,String message);

    /**
     * 分配单
     * @param requestData
     * @param user_id
     * @return
     */
    ResponseResult distributionOrdersReq(DistributionOrdersReq requestData, String user_id);

    /**
     * 验证当前是否同一人
     * @param requestData
     * @param user_id
     * @return
     */
    ResponseResult checkFasSame(GrabOrderReq requestData, String user_id);


    /**
     * 批量查询是否经销
     * @param unionIdList
     * @return
     */
    List<BatchCheckDealerResp> batchCheckDealer(List<String> unionIdList);


    /**
     * @Description: 获取指定要盒数据
     * @Author: brian
     * @Date: 2021/11/8 10:22
     * @params: [unionId]
     * @return: com.jnby.application.minapp.dto.response.CustomerAskBoxResp
     */
    CustomerAskBoxResp getByUnionId(String unionId);

    /**
     * 要盒需求  二期
     * @param id
     * @param unionid
     * @return
     */
    CustomerAskBoxInfoByIdResp customerAskBoxInfoById(String id, String unionid);

    /**
     * 统计信息  搭配师
     * @param fashionerId
     * @return
     */
    StaticsticFashionerDataResp staticsticFashionerData(String fashionerId);

    /**
     * 恢复主动要盒
     * @param boxId
     */
    void restCustomerAskBox(String boxId);

    /**
     * 查找90天未要盒的数据
     * @return 返回
     */
    List<String> selectReminder();


    /**
     * 生效主动要盒
     * @param setSub2AskBoxEntity
     */
    void activeAskBox(SetSub2AskBoxEntity setSub2AskBoxEntity);


    /**
     * 判断将要使用的是单次盒子还是订阅盒子
     */
    Long checkSubOrSingle(CheckSubOrSingleReq checkSubOrSingleReq);

    /**
     * 获取是否需要使用新的节点及节点对应的月份
     */
    CheckUsePlanMonthResp checkUsePlanMonth(CheckSubOrSingleReq checkSubOrSingleReq);


    /**
     * 补全主动要盒
     */
    CustomerAskBox completionCustomerAskBox(CompletionCustomerAskBoxEntity completionCustomerAskBoxEntity);

    /**
     * 批量获取订阅期内已发货的服务单
     */
    Map<String/* unionId */,List<BoxWithBLOBs>> batchGetSendBox(List<String> subIds);
}
