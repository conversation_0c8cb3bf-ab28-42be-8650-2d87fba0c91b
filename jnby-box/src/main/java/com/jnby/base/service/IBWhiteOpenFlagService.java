package com.jnby.base.service;

import com.jnby.application.corp.dto.request.VerifyWhiteUserReq;
import com.jnby.infrastructure.box.model.BWhiteOpenFlag;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @auther lwz
 * @create 2022-03-07 13:53:05
 * @describe 服务类
 */
public interface IBWhiteOpenFlagService extends IService<BWhiteOpenFlag> {

    /**
     * 校验是否开启白名单
     * @param verifyWhiteUserReq
     * @return
     */
    Boolean verifyWhiteTypeOpen(VerifyWhiteUserReq verifyWhiteUserReq);

}
