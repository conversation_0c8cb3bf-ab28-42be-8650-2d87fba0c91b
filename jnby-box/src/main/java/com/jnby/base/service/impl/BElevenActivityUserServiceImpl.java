package com.jnby.base.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.jnby.application.minapp.dto.response.CheckElevenAccessRespDto;
import com.jnby.base.repository.impl.CustomerDetailsRepository;
import com.jnby.common.Page;
import com.jnby.common.enums.CouponRecordEnum;
import com.jnby.config.ElevenActivityProperties;
import com.jnby.infrastructure.box.mapper.CouponActivityMapper;
import com.jnby.infrastructure.box.mapper.CouponRecordMapper;
import com.jnby.infrastructure.box.mapper.CustomerDetailsMapper;
import com.jnby.infrastructure.box.model.*;
import com.jnby.infrastructure.box.mapper.BElevenActivityUserMapper;
import com.jnby.base.service.IBElevenActivityUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jnby.module.marketing.coupon.service.ICouponActivityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * @auther lwz
 * @create 2024-09-26 14:25:19
 * @describe 服务实现类
 */
@Service
@Slf4j
public class BElevenActivityUserServiceImpl extends ServiceImpl<BElevenActivityUserMapper, BElevenActivityUser> implements IBElevenActivityUserService {

    @Resource
    private CouponRecordMapper couponRecordMapper;

    @Resource
    private CustomerDetailsRepository customerDetailsRepository;

    @Resource
    private ICouponActivityService iCouponActivityService;

    @Override
    public CheckElevenAccessRespDto validUser(String unionId, String elevenActivityId)  {
        log.info("校验用户unionId:{} elevenActivityId:{}", unionId, elevenActivityId);
        CheckElevenAccessRespDto checkElevenAccessRespDto = new CheckElevenAccessRespDto();
        checkElevenAccessRespDto.setActivityId(elevenActivityId);
        checkElevenAccessRespDto.setFlag(false);
        checkElevenAccessRespDto.setUnionId(unionId);

        CouponActivity couponActivity = iCouponActivityService.getById(elevenActivityId);
        if (couponActivity == null) {
            log.info("活动不存在unionId:{} elevenActivityId:{}", unionId, elevenActivityId);
            return checkElevenAccessRespDto;
        }
        if (couponActivity.getStatus() == 0) {
            log.info("活动非上架unionId:{} elevenActivityId:{}", unionId, elevenActivityId);
            return checkElevenAccessRespDto;
        }
        Date now = new Date();
        if (now.before(couponActivity.getStartTime())) {
            log.info("活动未开始unionId:{} elevenActivityId:{}", unionId, elevenActivityId);
            return checkElevenAccessRespDto;
        }
        if (now.after(couponActivity.getEndTime())) {
            log.info("活动已结束unionId:{} elevenActivityId:{}", unionId, elevenActivityId);
            return checkElevenAccessRespDto;
        }

        List<BElevenActivityUser> bElevenActivityUserList = this.baseMapper.selectListByUnionId(unionId);
        if (CollectionUtils.isEmpty(bElevenActivityUserList)) {
            log.info("elevenActivityUser列表不存在unionId:{}", unionId);
            return checkElevenAccessRespDto;
        }
        BElevenActivityUser bElevenActivityUser = bElevenActivityUserList.get(0);
        if (bElevenActivityUser.getStatus().equals(Long.valueOf(1))) {
            log.info("已领取unionId:{}", unionId);
            return checkElevenAccessRespDto;
        }
        CustomerDetails customerDetails = customerDetailsRepository.findByUnionId(bElevenActivityUser.getUnionid());
        if (customerDetails == null) {
            log.info("box不存在当前用户unionId:{}", customerDetails.getUnionid());
            return checkElevenAccessRespDto;
        }
        // 查询
        CouponRecord couponRecordSearch = new CouponRecord();
        couponRecordSearch.setSourceid(elevenActivityId);
        couponRecordSearch.setUnionid(customerDetails.getUnionid());
        couponRecordSearch.setCustomerId(customerDetails.getId());
        couponRecordSearch.setType(CouponRecordEnum.Type.couponactivity.getCode().longValue());
        List<CouponRecord> couponRecords = couponRecordMapper.selectListBySelective(couponRecordSearch);
        if (CollectionUtils.isNotEmpty(couponRecords)) {
            log.info("已经领取unionId:{} couponRecordId:{}", unionId, couponRecords.get(0).getId());
            return checkElevenAccessRespDto;
        }
        checkElevenAccessRespDto.setFlag(true);
        return checkElevenAccessRespDto;
    }


    @Override
    public List<BElevenActivityUser> noReceive(Page page) {
        com.github.pagehelper.Page<BSubscribePlan> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        this.baseMapper.noReceiveList();
        PageInfo<BElevenActivityUser> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }


    @Override
    public int modifyStatusByIds(List<String> ids) {
        return this.baseMapper.updateStatusByIds(ids);
    }

}
