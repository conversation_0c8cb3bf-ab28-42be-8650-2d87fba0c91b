package com.jnby.base.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jnby.base.service.IBoxConcatLogService;
import com.jnby.infrastructure.bojun.mapper.CCustomerMapper;
import com.jnby.infrastructure.box.mapper.BoxConcatLogMapper;
import com.jnby.infrastructure.box.model.BoxConcatLog;
import com.jnby.infrastructure.box.model.CustomerDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/3/8
 */
@Service
public class BoxConcatLogServiceImpl extends ServiceImpl<BoxConcatLogMapper, BoxConcatLog> implements IBoxConcatLogService {

    @Autowired
    private BoxConcatLogMapper boxConcatLogMapper;

    @Override
    public List<BoxConcatLog> selectForTalkLog(String customerId) {
        return boxConcatLogMapper.selectForTalkLog(customerId);
    }

}

