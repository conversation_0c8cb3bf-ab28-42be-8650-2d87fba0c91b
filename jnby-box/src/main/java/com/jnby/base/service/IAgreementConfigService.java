package com.jnby.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jnby.infrastructure.box.model.AgreementConfig;

import java.util.List;

public interface IAgreementConfigService extends IService<AgreementConfig> {

    String create(AgreementConfig config);

    /**
     * 查询协议内容
     * @param agreementType
     * @return
     */
    AgreementConfig queryByType(String agreementType);
}
