package com.jnby.base.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.application.admin.dto.request.MemberConsumeReq;
import com.jnby.application.admin.dto.response.MemberBrowseInfoResp;
import com.jnby.common.Page;
import com.jnby.infrastructure.box.model.BMemberRecentBrowse;
import com.jnby.infrastructure.box.mapper.BMemberRecentBrowseMapper;
import com.jnby.base.service.IBMemberRecentBrowseService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jnby.module.product.entity.GoodSpuEntity;
import com.jnby.module.product.service.IProductService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @auther yui
 * @create 2022-09-06 14:55:18
 * @describe 会员最近浏览服务实现类
 */
@Service
public class BMemberRecentBrowseServiceImpl extends ServiceImpl<BMemberRecentBrowseMapper, BMemberRecentBrowse> implements IBMemberRecentBrowseService {

    @Autowired
    private BMemberRecentBrowseMapper bMemberRecentBrowseMapper;

    @Autowired
    private IProductService iProductService;

    @Override
    public List<MemberBrowseInfoResp> searchMemberRecBrowse(MemberConsumeReq requestData, Page page) {
        if (StringUtils.isBlank(requestData.getUnionId()) || StringUtils.isBlank(requestData.getWeId())) {
            throw new RuntimeException("会员id或者品牌id错误");
        }
        //先查询浏览信息
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<BMemberRecentBrowse> bMemberRecentBrowses =
                bMemberRecentBrowseMapper.selectListByUnionIdAndWeId(requestData.getUnionId(), requestData.getWeId());
        PageInfo<MemberBrowseInfoResp> retPage = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(retPage.getPages());
        if (bMemberRecentBrowses == null || bMemberRecentBrowses.size() == 0) {
            return Collections.emptyList();
        }
        //根据款号，查询商品相关信息
        List<String> nameList = bMemberRecentBrowses.stream()
                .map(BMemberRecentBrowse::getStyleNumber)
                .collect(Collectors.toList());
        List<GoodSpuEntity> goodSpuEntities = iProductService.findGoodByNameList(nameList);
        List<MemberBrowseInfoResp> respList = buildRetList(bMemberRecentBrowses, goodSpuEntities);
        retPage.setList(respList);
        return respList;
    }

    private List<MemberBrowseInfoResp> buildRetList(List<BMemberRecentBrowse> bMemberRecentBrowses,
                                                    List<GoodSpuEntity> goodSpuEntities) {
        List<MemberBrowseInfoResp> retList = new ArrayList<>();
        Map<String, GoodSpuEntity> goodsMap = new HashMap<>();
        if (!goodSpuEntities.isEmpty()) {
            goodsMap = goodSpuEntities.stream().collect(HashMap::new,
                    (k, v) -> k.put(v.getName(), v), HashMap::putAll);
        }

        Map<String, GoodSpuEntity> finalGoodsMap = goodsMap;
        bMemberRecentBrowses.forEach(v -> {
            MemberBrowseInfoResp memberBrowseInfoResp = new MemberBrowseInfoResp();
            memberBrowseInfoResp.setBrowseDuration(v.getBrowseDuration());
            memberBrowseInfoResp.setBrowseTime(v.getBrowseTime());
            memberBrowseInfoResp.setBrowseNumber(v.getBrowseNumber());
            memberBrowseInfoResp.setGoodName(v.getGoodName());
            memberBrowseInfoResp.setName(v.getStyleNumber());
            memberBrowseInfoResp.setPrice(goodSpuEntities.isEmpty() ? new BigDecimal(0) :
                    BigDecimal.valueOf(finalGoodsMap.get(v.getStyleNumber()).getPrice()));
            memberBrowseInfoResp.setCoverPicture(goodSpuEntities.isEmpty() ? "" :
                    finalGoodsMap.get(v.getStyleNumber()).getCover_imgs());
            memberBrowseInfoResp.setProductId(goodSpuEntities.isEmpty() ? "" :
                    String.valueOf(finalGoodsMap.get(v.getStyleNumber()).getId()));
            memberBrowseInfoResp.setBrand(goodSpuEntities.isEmpty() ? "" :
                    finalGoodsMap.get(v.getStyleNumber()).getBrand());
            String colorNo = "";
            if (finalGoodsMap.get(v.getStyleNumber()) != null &&
                    finalGoodsMap.get(v.getStyleNumber()).getSkus() != null &&
                    finalGoodsMap.get(v.getStyleNumber()).getSkus().size() > 0) {
                colorNo = finalGoodsMap.get(v.getStyleNumber()).getSkus().get(0).getColorno();
            }
            memberBrowseInfoResp.setColorNo(colorNo);
            retList.add(memberBrowseInfoResp);
        });
        return retList;
    }
}
