package com.jnby.base.service;

import com.jnby.infrastructure.box.model.Fashioner;
import com.jnby.infrastructure.box.model.Task;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/9/26 14:48
 */
public interface ITemplateMsgService {
    /**
     * @Description: 构建源搭配师remark
     * @Author: brian
     * @Date: 2021/9/26 15:21
     * @params: [fashionerName, nicName]
     * @return: java.lang.String
     */
    String buildOrigFashionerRemark(String fashionerName,String nickName);

    /**
     * @Description: 构建目标搭配师remark
     * @Author: brian
     * @Date: 2021/9/26 15:21
     * @params: [fashionerName, nicName]
     * @return: java.lang.String
     */
    String buildDestFashionerRemark(String fashionerName,String nickName);


    /**
     * @Description: 更换搭配师发送消息给用户（用于构建不保存）
     * @Author: brian
     * @Date: 2021/9/26 15:22
     * @params: [openId]
     * @return: Task
     */
    Task changeFashionerWXMsgToCustomer(String openId, Fashioner fashioner);

}
