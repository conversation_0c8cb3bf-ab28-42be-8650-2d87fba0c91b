package com.jnby.base.service.scene.minapp.impl;

import com.jnby.base.service.scene.minapp.IMinappSceneOperation;
import com.jnby.common.enums.SceneTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class UserSignAgreementSceneOperationImpl implements IMinappSceneOperation {
    @Override
    public SceneTypeEnum getType() {
        return SceneTypeEnum.SUBSCRIBE_AGREEMENT;
    }

    @Override
    public Map apply(String unionId, Map paramsMap) {
        Map map = new HashMap();
        map.put("type", getType().getCode());
        map.put("platform", paramsMap.containsKey("platform") ? paramsMap.get("platform") : "sales");
        return map;
    }
}
