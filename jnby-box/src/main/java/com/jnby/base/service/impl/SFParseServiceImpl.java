package com.jnby.base.service.impl;

import com.jnby.base.entity.*;
import com.jnby.base.repository.ILogisticsRepository;
import com.jnby.base.service.ISFParseService;
import com.jnby.common.enums.SFStatusEnum;
import com.jnby.common.util.DateUtil;
import com.jnby.common.util.ParseSFXmlUtil;
import com.jnby.infrastructure.box.mapper.SysConfigMapper;
import com.jnby.infrastructure.box.model.*;
import com.sf.csim.express.service.CallExpressServiceTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
/**
 * <AUTHOR>
 * @description:
 * @date 2021/6/2810:50
 */
@Slf4j
@Service
public class SFParseServiceImpl implements ISFParseService {
    @Autowired
    private ILogisticsRepository logisticsRepository;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private ParseSFXmlUtil parseSFXmlUtil;

    @Override
    public void statusChange(SFStatusEntity entity) {
        if (SFStatusEnum.B.getCode().equals(entity.getOrderStateCode())) {
            // 调度成功。更新快递员信息
            Logistics para = new Logistics();
            para.setRefundId(entity.getOrderNo());
            List<Logistics> list = logisticsRepository.findByRecord(para);
            if (list.size() > 0) {
                Logistics updateLogistics = new Logistics();
                updateLogistics.setId(list.get(0).getId());
                updateLogistics.setEmpCode(entity.getEmpCode());
                updateLogistics.setEmpPhone(entity.getEmpPhone());
                updateLogistics.setUpdateTime(new Date());
                logisticsRepository.updateById(updateLogistics);
            }
        }
    }

    @Override
    public boolean cancelSf(String orderId, String mailNo) {
        log.info("into SFService cancelSf");
        try {
            String realXml = parseSFXmlUtil.appCancelXmlString(orderId, mailNo);
            log.info("请求报文：" + realXml);
            SysConfigWithBLOBs config = sysConfigMapper.selectOne();
            if(config.getDev() == 2L){
                return true;
            }
            CallExpressServiceTools client = CallExpressServiceTools.getInstance();
            String respXml = CallExpressServiceTools.callSfExpressServiceByCSIM(parseSFXmlUtil.getMAIN_URL(),
                    realXml, parseSFXmlUtil.getCLIENTCODE(), parseSFXmlUtil.getCHECKWORD());
            if (respXml != null) {
                log.info("返回报文: " + respXml);
                Map<String, String> resultMap = parseSFXmlUtil.analysisResult(respXml);
                String code = resultMap.get("code").toString();
                //取消成功 或者已取消
                if ("OK".equals(code) || "8037".equals(resultMap.get("error_code").toString())
                        || "8024".equals(resultMap.get("error_code").toString())) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("CancelSf error", e);
            return false;
        }
    }

    @Override
    public SFReturnData sendSf(String orderId, SFLogisticsEntity send, SFLogisticsEntity receive) throws RuntimeException{
        SFOrderDto dto = new SFOrderDto();
        dto.setOrderid(orderId);
        dto.setParcel_quantity(1);
        dto.setExpress_type("1");
        dto.setJ_country(parseSFXmlUtil.getD_COUNTRY());
        dto.setJ_province(send.getProvince());
        dto.setJ_city(send.getCity());
        dto.setJ_county(send.getDistrict());
        dto.setJ_address(send.getAddress());
        dto.setJ_company(send.getCompany());
        dto.setJ_contact(send.getContact()==null ? "" : send.getContact());
        dto.setJ_tel(send.getTel());
        dto.setD_country(parseSFXmlUtil.getD_COUNTRY());
        dto.setD_province(receive.getProvince());
        dto.setD_city(receive.getCity());
        dto.setD_county(receive.getDistrict());
        dto.setD_address(receive.getAddress());
        dto.setD_company(send.getCompany());
        dto.setD_contact(receive.getContact());
        dto.setD_tel(receive.getTel());
        String sendStartTime = DateUtil.formatToStr(DateUtil.formatToDate(send.getGetDate(), "yyyy-M-dd HH:mm"), "yyyy-M-dd HH:mm:ss");
        dto.setSendstarttime(sendStartTime);
        dto.setPay_method(3);
        return placeAnSf(dto);
    }

    private SFReturnData placeAnSf(SFOrderDto dto) throws RuntimeException{
        log.info("into SFService placeAnSf");
        SFReturnData returnDate = new SFReturnData();
        SysConfigWithBLOBs config = sysConfigMapper.selectOne();
        if(config.getDev() == 2L){
            returnDate.setMailNo("TEST987654321");
            returnDate.setOrderId(dto.getOrderid());
            returnDate.setCode("OK");
            return returnDate;
        }
        try {
            String realXml = parseSFXmlUtil.appXmlString(dto);
            log.info("请求报文：" + realXml);
            CallExpressServiceTools client = CallExpressServiceTools.getInstance();
            String respXml = CallExpressServiceTools.callSfExpressServiceByCSIM(parseSFXmlUtil.getMAIN_URL(), realXml,
                    parseSFXmlUtil.getCLIENTCODE(), parseSFXmlUtil.getCHECKWORD());
            if (respXml != null) {
                log.info("返回报文: " + respXml);
                Map<String, String> resultMap = parseSFXmlUtil.analysisResult(respXml);
                if ("OK".equals(resultMap.get("code"))) {
                    returnDate.setOrderId(resultMap.get("orderid"));
                    returnDate.setDestCode(resultMap.get("destcode"));
                    returnDate.setMailNo(resultMap.get("mailno"));
                    returnDate.setCode(resultMap.get("code"));
                    if ("3".equals(resultMap.get("filter_result").toString())) {
                        returnDate.setMessage("该地址超出收派范围");
                    }
                } else {
                    returnDate.setCode(resultMap.get("error_code"));
                    returnDate.setMessage(resultMap.get("error_message"));
                }
            }
        } catch (RuntimeException e) {
            log.error("placeAnSf error", e);
            throw new RuntimeException(e.getMessage());
        }
        return returnDate;
    }

    @Override
    public List<SFRouteEntity> routeService(String trackingNumber) {
        try {
            CallExpressServiceTools client = CallExpressServiceTools.getInstance();
            String xmlStr = parseSFXmlUtil.appRouteServiceXml(trackingNumber);
            String respXml = CallExpressServiceTools.callSfExpressServiceByCSIM(parseSFXmlUtil.getMAIN_URL(), xmlStr,
                    parseSFXmlUtil.getCLIENTCODE(), parseSFXmlUtil.getCHECKWORD());
            if (respXml != null) {
                return parseSFXmlUtil.analysisRouteServiceResult(trackingNumber, respXml);
            }
        } catch (Exception e) {
            log.error("sf routeService error,trackingNumber={}", trackingNumber, e);
        }
        return null;
    }

}
