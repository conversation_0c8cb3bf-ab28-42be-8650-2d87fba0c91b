package com.jnby.base.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jnby.base.service.IMessageForCustomer;
import com.jnby.infrastructure.box.mapper.MessageForCustomerMapper;
import com.jnby.infrastructure.box.mapper.MessageOrderMapper;
import com.jnby.infrastructure.box.model.MessageForCustomer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/14
 */
@Service
public class MessageForCustomerImpl extends ServiceImpl<MessageForCustomerMapper, MessageForCustomer> implements IMessageForCustomer {

    @Autowired
    private MessageForCustomerMapper messageForCustomerMapper;

    @Override
    public List<MessageForCustomer> findMessage(String messageOrderId) {
        //findMessageForCustomer
        return messageForCustomerMapper.findMessageForCustomer(messageOrderId);
    }
}
