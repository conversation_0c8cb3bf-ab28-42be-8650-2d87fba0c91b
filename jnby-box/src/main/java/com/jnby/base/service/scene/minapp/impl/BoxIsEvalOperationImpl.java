package com.jnby.base.service.scene.minapp.impl;

import com.jnby.base.service.scene.minapp.IMinappSceneOperation;
import com.jnby.common.enums.SceneTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class BoxIsEvalOperationImpl implements IMinappSceneOperation {

    @Override
    public SceneTypeEnum getType() {
        return SceneTypeEnum.BOX_EVALUATE;
    }

    @Override
    public Map apply(String unionId, Map paramsMap) {
        Map map = new HashMap();
        map.put("type", getType().getCode());
        map.put("box_id", paramsMap.containsKey("boxId") ? paramsMap.get("boxId") : null);

        map.put("union_id", paramsMap.containsKey("unionId") ? paramsMap.get("unionId") : null);
        return map;
    }
}

