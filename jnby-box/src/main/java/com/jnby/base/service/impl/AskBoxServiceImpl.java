package com.jnby.base.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.application.admin.dto.request.ChangeFashionerReq;
import com.jnby.application.admin.dto.request.CheckUserLogisticsAddressInfoReq;
import com.jnby.application.admin.dto.response.ImportAskBoxErrorResp;
import com.jnby.application.corp.dto.request.IndexReq;
import com.jnby.application.corp.dto.response.IndexResp;
import com.jnby.application.minapp.dto.request.*;
import com.jnby.application.minapp.dto.response.*;
import com.jnby.base.entity.CCollectionStore;
import com.jnby.base.entity.FashionerInfoEntity;
import com.jnby.base.entity.MemberCardEntity;
import com.jnby.base.repository.*;
import com.jnby.base.repository.impl.CustomerDetailsRepository;
import com.jnby.base.service.*;
import com.jnby.base.service.enums.AskTypeEnum;
import com.jnby.common.BoxException;
import com.jnby.common.ProducerUtil;
import com.jnby.common.ResponseResult;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.enums.*;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.common.leaf.baidu.fsg.uid.utils.DateUtils;
import com.jnby.common.listener.router.msg.util.MsgTagUtil;
import com.jnby.common.util.FileParseUtil;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.common.util.excel.IWriteDataExcel;
import com.jnby.config.MsgTemplateConfigProperties;
import com.jnby.config.WxMaProperties;
import com.jnby.infrastructure.bojun.mapper.*;
import com.jnby.infrastructure.bojun.model.*;
import com.jnby.infrastructure.box.mapper.*;
import com.jnby.infrastructure.box.model.*;
import com.jnby.module.cusRights.service.ICusRightsService;
import com.jnby.module.customer.information.entity.BCustomerInformation;
import com.jnby.module.customer.information.entity.CustomerInfoDataExpand;
import com.jnby.module.customer.information.service.IBCustomerInformationService;
import com.jnby.module.customer.vip.service.IExternalCustomerVipService;
import com.jnby.module.facade.service.INewRightsV3Service;
import com.jnby.module.jic.entity.RulesList;
import com.jnby.module.marketing.coupon.service.ICouponActivityService;
import com.jnby.module.marketing.coupon.service.IShareVoucherService;
import com.jnby.module.message.entity.SendMsgCpEntity;
import com.jnby.module.message.service.ISysMessageService;
import com.jnby.module.oauth.oss.service.IUserInfoService;
import com.jnby.module.order.repository.impl.BoxRepository;
import com.jnby.module.subscribe.service.ICombineSubscribeSettingService;
import com.jnby.module.workbench.enums.WorkBenchTaskTriggerEnum;
import com.jnby.module.workbench.enums.WorkBenchTaskTypeEnum;
import com.jnbyframework.boot.common.api.dto.message.TemplateMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springcenter.marketing.api.entity.CheckRightsEntity;
import org.springcenter.marketing.api.enums.CardTypeEnum;
import org.springcenter.marketing.api.enums.RightsTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service
@RefreshScope
@Slf4j
public class AskBoxServiceImpl implements IAskBoxService {

    protected final Logger logger = LoggerFactory.getLogger(AskBoxServiceImpl.class);


    // 经销限制品牌
    @Value("${dealer.brand}")
    private String dealerBrand;

    //开放区域
    @Value("${dealer.open.zone}")
    private String dealerOpenZone;

    //江南布衣+和奥莱
    @Value("${jnby.outlets}")
    private String jnbyAndOlai;

    @Value("${default.fashionerid}")
    private String defaultFashionerId;

    @Autowired
    private IFileParseService fileParseService;


    private String olaiBrand = "江南布衣+优享";

    public static final int LIMIT_ASK_BOX = 1; //限制主动要盒
    //经销开放区域
    private static final String[] womenBrand = {"JNBY","less",};
    private static final String[] manBrand = {"CROQUIS","APN73",};
    // 童装
    private static List<String> childBrand = Arrays.asList("jnby by JNBY", "Pomme de terre", "OUTLETS");


    @Autowired
    private CustomerDetailsRepository customerDetailsRepository;
    @Autowired
    private BoxRepository boxRepository;
    @Autowired
    private ICustomerAskBoxRepository customerAskBoxRepository;
    @Autowired
    private CustomerCategoryMapper customerCategoryMapper;
    @Autowired
    private BCustomerInformationMapper bCustomerInformationMapper;

    @Autowired
    private CclientVipMapper cclientVipMapper;
    @Autowired
    private CVipTypeMapper cVipTypeMapper;
    @Autowired
    private CCustomerMapper cCustomerMapper;
    @Autowired
    private ICustomerAskBoxLogService customerAskBoxLogService;
    @Autowired
    private AttrValueMapper attrValueMapper;
    @Autowired
    private CustomerContactMapper customerContactMapper;
    @Autowired
    private AskBoxAttrMapper askBoxAttrMapper;
    @Autowired
    private CustomerLogisticsMapper customerLogisticsMapper;
    @Autowired
    private IFashionerRepository fashionerRepository;
    @Autowired
    private ISysUserRepository sysUserRepository;
    @Autowired
    private AttrValueExtendsMapper attrValueExtendsMapper;
    @Autowired
    private ServiceAreaMapper serviceAreaMapper;
    @Autowired
    private IServiceAreaService serviceAreaService;
    @Autowired
    private IdLeafService idLeaf;
    @Autowired
    private ICouponSettingRepository iCouponSettingRepository;
    @Autowired
    private ICustomerDetailsRepository iCustomerDetailsRepository;
    @Autowired
    private ICouponActivityService iCouponActivityService;
    @Autowired
    private IShareVoucherService iShareVoucherService;

    @Autowired
    private IWorkBenchService workBenchService;

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private SnapshotdMapper snapshotdMapper;
    @Autowired
    private SubscribeAskMapper subscribeAskMapper;
    @Autowired
    private IAttrService attrService;
    @Autowired
    private ICustomerAskBoxService customerAskBoxService;
    @Autowired
    private ISysMessageService sysMessageService;
    @Autowired
    private CStoreMapper cStoreMapper;

    @Autowired
    private IBCustomerInformationService ibCustomerInformationService;
    @Autowired
    private ICustomerLogisticsService customerLogisticsService;
    @Autowired
    private IFashionerChangeService fashionerChangeService;

    @Autowired
    private ISysMessageService iSysMessageService;

    @Autowired
    private WeiXinFansMapper weiXinFansMapper;

    /**
     * 90天要盒
     */
    @Autowired
    private MsgTemplateConfigProperties msgTemplateConfigProperties;


    @Autowired
    private IWorkService workService;

    @Autowired
    private EmployeeBaseMapper employeeBaseMapper;

    @Value("${jump.url}")
    private String jumpUrl;

    @Autowired
    private IExternalCustomerVipService iExternalCustomerVipService;

    @Autowired
    WxMaProperties wxMaProperties;

    @Value("${send.message.jic.appId}")
    private String sendMessageAppid;

    @Value("${send.message.jic.agentId}")
    private String sendMessageAgentId;

    @Autowired
    private CustomerDetailsMapper customerDetailsMapper;

    @Autowired
    private BCusServiceAutoAskBoxMapper bCusServiceAutoAskBoxMapper;

    @Autowired
    @Qualifier("boxTransactionTemplate")
    private TransactionTemplate template;

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private RedisPoolUtil redisPoolUtil;

    @Autowired
    private CustomerCategoryMapper categoryMapper;

    @Autowired
    private ICustomerDetailsService customerDetailsService;

    @Autowired
    private ProducerUtil producerUtil;


    @Autowired
    private ICusRightsService cusRightsService;

    @Resource
    private INewRightsV3Service newRightsV3Service;

    @Resource
    private ICombineSubscribeSettingService combineSubscribeSettingService;


    @Autowired
    private IFashionerService fashionerService;


    @Autowired
    private QiniuUtil qiniuUtil;
    @Override
    public ResponseResult validData(AskStatusReq requestData) {

        //效验用户是否存在
        if(StringUtils.isBlank(requestData.getUnionId())){
            return ResponseResult.error(1,"用户不存在!");
        }

        CustomerDetails customerDetails = customerDetailsRepository.findByUnionId(requestData.getUnionId());
        if(ObjectUtils.isEmpty(customerDetails)){
            return ResponseResult.error(1,"用户未注册!");
        }

        //是否是黑名单
        Boolean isBlack = customerDetailsService.checkUserIsBlack(customerDetails.getId(), null);
        if(isBlack){
            return ResponseResult.error(6,"您的会员暂时无法享受BOX服务，如有疑问请联系客服！");
        }


        //查询是否有未完结的盒子
        List<BoxWithBLOBs> onGoingBoxByUnionId = boxRepository.findOnGoingBoxByUnionId(requestData.getUnionId());
        if(CollectionUtils.isNotEmpty(onGoingBoxByUnionId)){
            return ResponseResult.error(5,"您还有未完结的定制盒子，暂时无法要新的盒子哦！");
        }

        //查询是否有未完结的要盒申请
        CustomerAskBox customerAskBox = new CustomerAskBox();
        customerAskBox.setUnionid(requestData.getUnionId());
        customerAskBox.setStatus(CustomerAskBoxStatusEnum.READY.getCode().shortValue());
        List<CustomerAskBox> unFinishAskBox = customerAskBoxRepository.selectListBySelective(customerAskBox);
        if(CollectionUtils.isNotEmpty(unFinishAskBox) && !requestData.getId().equals(unFinishAskBox.get(0).getId())){
            return ResponseResult.error(5,"您还有未完结的定制盒子，暂时无法要新的盒子哦！");
        }

        //是否限制主动要盒
        if(StringUtils.isNotBlank(customerDetails.getCategoryId())){
            CustomerCategory customerCategory = customerCategoryMapper.selectByPrimaryKey(customerDetails.getCategoryId());
            if(ObjectUtils.isNotEmpty(customerCategory)
                    && ObjectUtils.isNotEmpty(customerCategory.getAskBox())
                    && LIMIT_ASK_BOX == customerCategory.getAskBox()){
                return ResponseResult.error(6,"您的会员暂时无法享受BOX服务，如有疑问请联系客服！");
            }
        }


//        BCustomerInformation bCustomerInformation = bCustomerInformationMapper.selectById(customerDetails.getId());
//        if(Objects.nonNull(bCustomerInformation) && bCustomerInformation.getbCategoryId() == 57L){
//            return ResponseResult.error(6,"您的会员暂时无法享受BOX服务，如有疑问请联系客服！");
//        }

        //是否使用经销卡，并且经销商未开通box服务
        if(checkCustomerAskBoxJx(customerDetails.getUnionid())){
            return ResponseResult.error(6,"您暂时无法发起要盒子，如有异议请联系客服！");
        }

        //
      /*  if(ObjectUtils.isEmpty(customerDetails) || (customerDetails.getSubExpireTime() == null)){
            return ResponseResult.error(-1,"用户未订阅！");
        }  else if (customerDetails.getSubExpireTime().before(new Date())||"1".equals(customerDetails.getEndtype())) {
            return ResponseResult.error(-1,"订阅已过期！");
        }

        //剩余服务次数是否>0
        BUserRights adapterUserRights = iUserRightsService.findAdapterUserRights(customerDetails.getId());
        if((adapterUserRights.getTotalNum() - adapterUserRights.getUsedNum() - adapterUserRights.getHoldNum()) == 0){
            return ResponseResult.error(-1,"订阅次数为0！");
        }*/
        return ResponseResult.success();
    }

    @Override
    public ResponseResult validData2(SaveBoxAskReq requestData) {


        //效验用户是否存在
        if(StringUtils.isBlank(requestData.getUnionid())){
            return ResponseResult.error(1,"用户不存在!");
        }

        CustomerDetails customerDetails = customerDetailsRepository.findByUnionId(requestData.getUnionid());
        if(ObjectUtils.isEmpty(customerDetails)){
            return ResponseResult.error(1,"用户未注册!");
        }


        if(StringUtils.isNotBlank(requestData.getLogisticsId())){
            // 验证用户地址信息中的手机号是否在黑名单中， 校验用户地址是否
//            Boolean checkUserCustomerLogistics = customerDetailsService.checkUserCustomerLogistics(requestData.getLogisticsId(),false);
//            if(checkUserCustomerLogistics){
//                return ResponseResult.error(15, "您的账号存在风险，暂时无法提交要盒申请!");
//            }

            // 验证用户地址信息中的地址
//            CheckUserLogisticsAddressInfoReq checkUserLogisticsAddressInfoReq = new CheckUserLogisticsAddressInfoReq();
//            checkUserLogisticsAddressInfoReq.setCustomerLogisticsId(requestData.getLogisticsId());
//            Boolean checkUserLogisticsAddressInfo = customerDetailsService.checkUserLogisticsAddressInfo(checkUserLogisticsAddressInfoReq);
//            if(checkUserLogisticsAddressInfo){
//                return ResponseResult.error(16, "您的账号存在风险，暂时无法提交要盒申请!");
//            }
        }

        if(StringUtils.isNotBlank(requestData.getCreateFasId())){
            //是否限制主动要盒
            if(StringUtils.isNotBlank(customerDetails.getCategoryId())){
                CustomerCategory customerCategory = customerCategoryMapper.selectByPrimaryKey(customerDetails.getCategoryId());
                if(ObjectUtils.isNotEmpty(customerCategory)
                        && ObjectUtils.isNotEmpty(customerCategory.getAskBox())
                        && LIMIT_ASK_BOX == customerCategory.getAskBox()){
                    return ResponseResult.error(6,"您的会员暂时无法享受BOX服务，如有疑问请联系客服！");
                }
            }
            //是否是黑名单
            Boolean isBlack = customerDetailsService.checkUserIsBlack(customerDetails.getId(), null);
            if(isBlack){
                return ResponseResult.error(6,"您的会员暂时无法享受BOX服务，如有疑问请联系客服！");
            }

            //查询是否有未完结的盒子
            List<BoxWithBLOBs> onGoingBoxByUnionId = boxRepository.findOnGoingBoxByUnionId(requestData.getUnionid());
            if(CollectionUtils.isNotEmpty(onGoingBoxByUnionId)){
                if(StringUtils.isNotBlank(requestData.getChannelId()) &&
                        CustomerAskBoxChannelEnum.CROP_ASK_BOX.getCode().equals(requestData.getChannelId())){
                    return ResponseResult.error(5,"该会员还有未完成的定制盒子，暂时无法要新的盒子哦！");
                }
                return ResponseResult.error(5,"您还有未完结的定制盒子，暂时无法要新的盒子哦！");
            }

            //查询是否有未完结的要盒申请
            CustomerAskBox customerAskBox = new CustomerAskBox();
            customerAskBox.setUnionid(requestData.getUnionid());
            customerAskBox.setStatus(CustomerAskBoxStatusEnum.READY.getCode().shortValue());
            List<CustomerAskBox> unFinishAskBox = customerAskBoxRepository.selectListBySelective(customerAskBox);
            if(CollectionUtils.isNotEmpty(unFinishAskBox) && !StringUtils.equals(requestData.getId(),unFinishAskBox.get(0).getId())){
                if(StringUtils.isNotBlank(requestData.getChannelId()) &&
                        CustomerAskBoxChannelEnum.CROP_ASK_BOX.getCode().equals(requestData.getChannelId())){
                    return ResponseResult.error(5,"该会员还有未完成的定制盒子，暂时无法要新的盒子哦！");
                }
                return ResponseResult.error(5,"您还有未完结的定制盒子，暂时无法要新的盒子哦！");
            }
            List<String> ids = Optional.ofNullable(unFinishAskBox)
                    .map(a -> a.stream().map(CustomerAskBox::getId).collect(Collectors.toList()))
                    .orElse(new ArrayList<>());
            // 无待搭配的主动要盒,或新建的主动要盒不在待搭配的列表，进行权益校验
            if(AskTypeEnum.SUB.getCode().equals(requestData.getAskType()) && (CollectionUtils.isEmpty(unFinishAskBox) || (CollectionUtils.isNotEmpty(unFinishAskBox) && !ids.contains(requestData.getId())))){
                // 要盒权益校验
                CheckRightsEntity checkRightsEntity = new CheckRightsEntity();
                checkRightsEntity.setRightsTypeEnum(RightsTypeEnum.APPLY_BOX_SUBCRIBE);
                checkRightsEntity.setCardTypeEnum(CardTypeEnum.SUB);
                checkRightsEntity.setUnionid(requestData.getUnionid());
                if(!newRightsV3Service.checkRights(checkRightsEntity)){
                    return ResponseResult.error(-1,"当前无要盒权益");
                }
            }
        }
        return ResponseResult.success();
    }

    @Override
    public List<AttrValue> findAttrValue() {
        return attrValueMapper.selectCancelReason();
    }

    @Override
    public AskBoxDataNewResp askBoxDataNew(AskBoxDataNewReq requestData, CustomerDetails customerDetails) {
        // 获取可选择的男装或者女装
        GenderAttrResp genderAttrResp = null;
        CustomerAskBox askBoxReturn = null;
        AskBoxDataResp askBoxDataResp = new AskBoxDataResp();
        CustomerLogistics logistic = null;
        try {
            genderAttrResp  = findGenderAttr(requestData.getUnionId(),customerDetails,requestData);
        }catch (Exception e){
            logger.error("findGenderAttr = {}",e);
            logger.error("获取主动要和默认标签异常，unionId={}",requestData.getUnionId());
        }
        // 返回体
        AskBoxDataNewResp askBoxDataNewResp = new AskBoxDataNewResp();
        if(StringUtils.isBlank(requestData.getId())){
            if(customerDetails.getFirstAskBox() == 1) {
                // 首次要盒
                askBoxDataResp.setWeight(customerDetails.getWeight());
                askBoxDataResp.setCennectPhone(customerDetails.getPhone());
                askBoxDataResp.setHeight(customerDetails.getHeight());
                // 首次要盒图片
                String img = getFirstImg();
                askBoxDataNewResp.setImg(img);
            }else{
                // 非首次，获取上一次要盒数据
                askBoxReturn = getLastAskBoxByUnionId(customerDetails.getUnionid());
            }
        }else{
            // 修改
            askBoxReturn = customerAskBoxRepository.findById(requestData.getId());
            // 获取非本次的上一次要盒数据
            CustomerAskBox lastAskBox = getLastAskBoxByUnionId(customerDetails.getUnionid(), requestData.getId());
            if(ObjectUtils.isNotEmpty(lastAskBox)) {
                askBoxDataNewResp.setLastNotTheFashionerId(lastAskBox.getCreateFasId());
            }
        }

        if(ObjectUtils.isNotEmpty(askBoxReturn)){
            BeanUtils.copyProperties(askBoxReturn, askBoxDataResp,new String[]{"imgs"});
            askBoxDataResp.setSceneTags(StringUtils.isNotEmpty(askBoxReturn.getSceneTag()) ? askBoxReturn.getSceneTag().split(",") : new String[]{});
            askBoxDataResp.setGoodsTags(StringUtils.isNotEmpty(askBoxReturn.getGoodsTag()) ? askBoxReturn.getGoodsTag().split(",") : new String[]{});
            askBoxDataResp.setImgs(StringUtils.isNotEmpty(askBoxReturn.getImgs()) ? askBoxReturn.getImgs().split(",") : new String[]{});
            if (StringUtils.isNotBlank(askBoxReturn.getLogisticsId())) {
                CustomerLogistics customerLogistics = customerLogisticsMapper.selectById(askBoxReturn.getLogisticsId());
                if (ObjectUtils.isNotEmpty(customerLogistics) && ObjectUtils.isNotEmpty(customerLogistics.getDel()) && customerLogistics.getDel() == 0L) {
                    logistic = customerLogistics;
                }
            }
            if(StringUtils.isNotEmpty(requestData.getId())){
                List<AskBoxAttr> list = askBoxAttrMapper.selectByAskBoxId(requestData.getId());
                askBoxDataResp.setSelectData(list);
            }

        }
        // 最近三次申请留言
        List<String> msgs = customerAskBoxRepository.findLastThreeMsg(requestData.getUnionId());
        //搭配师，导购
        List<FashionerInfoEntity> list = getFashioerList(customerDetails);
        // 排除不参与订阅3.0的门店导购
        if(1L == requestData.getAskType()){
            List<Integer> storeIds = list.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getStoreId()))
                    .map(a -> Integer.valueOf(a.getStoreId()))
                    .collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(storeIds)){
                List<Integer> adaptStore = combineSubscribeSettingService.getAdaptStore(storeIds);
                list = list.stream()
                        .filter(e -> StringUtils.isBlank(e.getStoreId()) || adaptStore.contains(Integer.valueOf(e.getStoreId())))
                        .collect(Collectors.toList());
            }
        }
        askBoxDataNewResp.setAskBoxDataResp(askBoxDataResp);
        askBoxDataNewResp.setGenderAttrResp(genderAttrResp);
        askBoxDataNewResp.setSalesList(list);
        askBoxDataNewResp.setCustomerLogistics(logistic);
        askBoxDataNewResp.setMsgs(msgs);
        askBoxDataNewResp.setPhone(customerDetails.getPhone()== null? "":customerDetails.getPhone());
        return askBoxDataNewResp;
    }

    @Override
    public GetFashionersResp getFashioners(GetFashionersReq requestData) {
        String brand = null;
        if(StringUtils.isNotBlank(requestData.getAttrThemeId())){
            AttrValueExtends attrValueExtends = attrValueExtendsMapper.selectByPrimaryKey(requestData.getAttrThemeId());
            brand = attrValueExtends.getBrand();
        }

        CustomerDetails customerDetails = customerDetailsRepository.findByUnionId(requestData.getUnionId());
        if (ObjectUtils.isNotEmpty(customerDetails)) {
            GetFashionersResp getFashionersResp = null;
            if(requestData.getIsScanEnter()){
                getFashionersResp = getFashionerByCustomerAndFashioner(customerDetails,requestData, brand);
            }else{
                getFashionersResp= getFashioners2(customerDetails,requestData.getGender(),brand);
            }
            // 排除不参与订阅3.0的门店导购
            if(1L == requestData.getAskType()){
                List<FashionerInfoEntity> fashionerInfoEntities = getFashionersResp.getFashionerInfoEntities();
                List<Integer> storeIds = fashionerInfoEntities.stream()
                        .filter(e -> StringUtils.isNotBlank(e.getStoreId()))
                        .map(a -> Integer.valueOf(a.getStoreId()))
                        .collect(Collectors.toList());

                if(CollectionUtils.isNotEmpty(storeIds)){
                    List<Integer> adaptStore = combineSubscribeSettingService.getAdaptStore(storeIds);
                    List<FashionerInfoEntity> okFashionerInfoList = getFashionersResp.getFashionerInfoEntities().stream()
                            .filter(e -> StringUtils.isBlank(e.getStoreId()) || adaptStore.contains(Integer.valueOf(e.getStoreId())))
                            .collect(Collectors.toList());
                    getFashionersResp.setFashionerInfoEntities(okFashionerInfoList);
                }
            }
            return getFashionersResp;
        }
        return null;
    }

    private GetFashionersResp getFashionerByCustomerAndFashioner(CustomerDetails customerDetails, GetFashionersReq requestData, String brand) {
        GetFashionersResp getFashionersResp = new GetFashionersResp();
        getFashionersResp.setCode(2);
        //当前绑定的搭配师
        String fashionerId = customerDetails.getFashionerId();
        List<FashionerInfoEntity> resultList =  new ArrayList<>();
        // 如果相同的搭配师  返回一个
            if(StringUtils.isNotBlank(fashionerId)){
                if(fashionerId.equals(requestData.getFashionerId())){
                    //相同搭配师 返回扫码搭配师
                    getFashionersResp.setFashionerInfoEntities(getScanFashioner(requestData));
                    return getFashionersResp;
                }else{
                    //返回绑定的搭配师和扫码dapeishi
//                    GetFashionersResp resp =  getFashioners2(customerDetails,requestData.getGender(),brand);
//                    resultList.addAll(resp.getFashionerInfoEntities());
//                    //判断resultList中是否包含requestData.getFashionerId()
//                    if(resp.getFashionerInfoEntities().stream().filter(e -> e.getId().equals(requestData.getFashionerId())).count() == 0){
//                        Fashioner fashionerById = fashionerRepository.findById(requestData.getFashionerId());
//                        if(fashionerById != null){
//                            resultList.add(setFashionerInfo(fashionerById));
//                        }
//                    }
                    Fashioner bindFashioner = fashionerRepository.findById(fashionerId);
                    if(bindFashioner != null){
                        resultList.add(setFashionerInfo(bindFashioner));
                    }
                    if(requestData.getFashionerId() != null){
                        Fashioner scanFashioner = fashionerRepository.findById(requestData.getFashionerId());
                        resultList.add(setFashionerInfo(scanFashioner));
                    }

                    getFashionersResp.setFashionerInfoEntities(resultList);
                    return getFashionersResp;
                }
            }else{
                //无绑定搭配师只返回扫码搭配师
                getFashionersResp.setFashionerInfoEntities(getScanFashioner(requestData));
                return getFashionersResp;
        }
    }

    private List<FashionerInfoEntity> getScanFashioner(GetFashionersReq requestData) {
        List<FashionerInfoEntity> resultList =  new ArrayList<>();
        Fashioner fashionerById = fashionerRepository.findById(requestData.getFashionerId());
        FashionerInfoEntity fashionerInfoEntity = setFashionerInfo(fashionerById);
        resultList.add(fashionerInfoEntity);
        return resultList;
    }

    @Override
    public Boolean checkCustomerTheme(CheckCustomerThemeReq requestData) {
        CustomerDetails customerDetails = customerDetailsRepository.findByUnionId(requestData.getUnionId());
        AttrValueExtends extend = attrValueExtendsMapper.selectByPrimaryKey(requestData.getId());
        CclientVip vip = getVipByBrand(customerDetails.getUnionid(),customerDetails.getPhone(),extend.getBrand());
        Long storage = 0L;
        if(vip == null){
            // 无此品牌卡，校验搭配师库存
            storage = extend.getSurplusFashionerStorage();
        }else{
            Long cCustomerId = vip.getcCustomerId();
            // 导购是否开通下盒子
            String linId = null;
            if(vip.getSalesrepId() != null){
                SysUser sysUser = new SysUser();
                sysUser.setStatus(1L);
                sysUser.setcEmployeeId(vip.getSalesrepId());
                List<SysUser> bySelective = sysUserRepository.findBySelective(sysUser);
                if(CollectionUtils.isNotEmpty(bySelective)){
                    linId = bySelective.get(0).getcEmployeeLinkid();
                }
            }

            if(cCustomerId == null || cCustomerId == 176L){
                // 直营
                if(StringUtils.isNotBlank(linId)){
                    storage = extend.getSurplusFashionerStorage() + extend.getSalesStorage();
                }else{
                    storage = extend.getSurplusFashionerStorage();
                }
            }else{
                if(StringUtils.isNotBlank(linId)){
                    storage = extend.getSalesStorage();
                }
            }
        }
        if(storage > 0){
            return true;
        }
        return false;
    }

    @Override
    public Long checkThemeStorage(CheckThemeStorageReq requestData) {
        AttrValueExtends attrValueExtends = attrValueExtendsMapper.selectByPrimaryKey(requestData.getId());
        Integer type = fashionerRepository.fashionerOrSales(requestData.getFashionerId());
        Long storage = 0L;
        if(type == 0){
            storage = attrValueExtends.getSurplusFashionerStorage();
        }else{
            storage = attrValueExtends.getSurplusSalesStorage();
        }
        return storage;
    }

    @Override
    public Boolean checkLogistic(CheckLogisticReq requestData) {
        CustomerLogistics customerLogistics = customerLogisticsMapper.selectById(requestData.getLogisticsId());
        boolean flag = true;
        if(serviceAreaService.unArea(customerLogistics.getProvince(), customerLogistics.getCity(), customerLogistics.getDistrict(), null)){
            flag = false;
        }
        return flag;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object oneStepSaveAskBox(SaveBoxAskReq requestData, String userId) {
        //封装参数
        SaveBoxAskResp saveBoxAskResp = new SaveBoxAskResp();
        List<RulesList> rulesLists = new ArrayList();

        //新增
        CustomerDetails customerDetails = customerDetailsRepository.findByUnionId(requestData.getUnionid());
        //验证是否存在多个进行中的要盒
        CustomerAskBox params = new CustomerAskBox();
        params.setUnionid(requestData.getUnionid());
        params.setStatus(CustomerAskBoxStatusEnum.READY.getCode().shortValue());
        List<CustomerAskBox> customerAskBoxes = customerAskBoxRepository.selectListBySelective(params);
        if(CollectionUtils.isNotEmpty(customerAskBoxes)){
            throw new BoxException("不能重复要盒!");
        }

        CustomerAskBox insertCustomerAskBox = new CustomerAskBox();
        BeanUtils.copyProperties(requestData,insertCustomerAskBox);
        insertCustomerAskBox.setUpdateTime(new Date());
        insertCustomerAskBox.setStatus(CustomerAskBoxStatusEnum.READY.getCode().shortValue());

        if (StringUtils.isNotBlank(requestData.getCreateFasId())){
            // 添加数据上次服务搭配师数据
            Fashioner fashioner = fashionerRepository.findById(requestData.getCreateFasId());
            requestData.setIsSales(fashioner.getIsSales() == null ? null : fashioner.getIsSales().intValue());
            insertCustomerAskBox.setIsSales(requestData.getIsSales());
        }


        boolean ifSend = false;

        // 是否发券
        if (StringUtils.isNotBlank(requestData.getCreateFasId())) {

            boolean isSend = false;

            //调用首次发券接口
            //查询有效期的活动
            List<CouponActivity> validActivity = iCouponActivityService.queryValidActivity(CouponRecordEnum.Type.first_ask_box.getCode());
            if (validActivity.isEmpty()){
                isSend = false;
            }else{
                //查询当前活动是否参与过
                CouponActivity currentAcitivty = validActivity.get(0);
                List<String> voucherIds = Arrays.asList(currentAcitivty.getVouchers().split(","));
                voucherIds.forEach(voucherId -> rulesLists.add(new RulesList(Long.valueOf(voucherId),1L)));
                if (voucherIds.isEmpty()) {
                    isSend = false;
                }else{
                    CustomerDetails customerDetailsCoup = iCustomerDetailsRepository.findById(customerDetails.getId());
                    if (customerDetailsCoup == null){
                        isSend =false;
                    }else{
                        //查询当前活动是否参与过
                        isSend = iShareVoucherService.sendCouponByType(rulesLists, CouponRecordEnum.Type.first_ask_box, currentAcitivty.getId(), customerDetails.getId(), customerDetails.getUnionid(), false);
                    }
                }
            }


            saveBoxAskResp.setIsSend(isSend);
            //订阅服务计划相关
            if (ObjectUtils.isNotEmpty(requestData) && StringUtils.isNotEmpty(requestData.getId())) {
                //2.3 待搭配：（用户通过推送消息/前端入口发起主动要盒）工作台生成搭配单
                //  2.5 已占用：（工单状态为待服务状态下）
                CustomerDetails customerDetailsByUnionId = customerDetailsRepository.findByUnionId(requestData.getUnionid());
                String customerId = customerDetailsByUnionId.getId();

                //顾客订阅要盒月份
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
                calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH));
                calendar.set(Calendar.DAY_OF_MONTH, 15);  //年月日
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                Date date = calendar.getTime();//date就是你需要的时间
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formatNow = dateFormat.format(date);
            }

            String voucherAmount = ifSendCoupon(requestData.getUnionid(),customerDetails,requestData.getId());
            saveBoxAskResp.setVoucherAmount(voucherAmount);
            ifSend = true;

            BCustomerInformation paramsBcustomer = new BCustomerInformation();
            paramsBcustomer.setUserId(customerDetails.getId());
            List<BCustomerInformation> bCustomerInformations = bCustomerInformationMapper.selectBySelective(paramsBcustomer);
            if(CollectionUtils.isNotEmpty(bCustomerInformations)){
                insertCustomerAskBox.setLastFashionerId(bCustomerInformations.get(0).getLastFashionerId());
                insertCustomerAskBox.setLastFashionerName(bCustomerInformations.get(0).getLastFashionerName());
            }

            // 更新用户累计消费金额
            BigDecimal orderAmount = orderMapper.sumOrderAmount(customerDetails.getId());
            insertCustomerAskBox.setTotBuyAmount(orderAmount);
            //保存快照
            saveSnapshotData(insertCustomerAskBox);

            updateCustomerAnswer(customerDetails.getId(),requestData.getHeight(),requestData.getWeight());

            // work-生成会员要盒待处理任务制
            workBenchService.executorWorkBench(null, insertCustomerAskBox.getCreateFasId(), insertCustomerAskBox.getId(),
                    insertCustomerAskBox.getUnionid(), customerDetails.getNickName(), customerDetails.getPhone(),
                    WorkBenchTaskTriggerEnum.TASK_TRIGGER_ENUM_MEMBER_CREATE_BOX.getValue(),
                    WorkBenchTaskTypeEnum.TASK_TYPE_ENUM_MEMBER_BOX.getValue());

        }


        //异步数据处理,发送消息
        if (ifSend) {
            Fashioner fashioner = fashionerRepository.findById(requestData.getCreateFasId());
            SysUser sysUser = sysUserRepository.findById(fashioner.getUserId());
            String phone = fashioner.getPhone();
            if(ObjectUtils.isNotEmpty(sysUser) && sysUser.getIsSales() == 1){
                phone = sysUser.getUsername();
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("phone", phone);

            TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
            templateMessageDTO.setToUser(phone);
            templateMessageDTO.setTemplateCode(SysTemplateCodeEnum.SMS_CODE_1170.getCode());
            templateMessageDTO.setTemplateParam((Map<String, String>) JSON.parse(JSONObject.toJSONString(jsonObject)));
            String msgId = sysMessageService.sendMessageByTemplate(templateMessageDTO);
        }

        //同步身高体重(修改身高体重时才会有值)
        if (ObjectUtils.isNotEmpty(insertCustomerAskBox.getCusGender())) {
            CustomerDetails  updateCustomer = new CustomerDetails();
            updateCustomer.setId(customerDetails.getId());
            updateCustomer.setHeight(insertCustomerAskBox.getHeight());
            updateCustomer.setWeight(insertCustomerAskBox.getWeight());
            customerDetailsRepository.updateByPrimaryKeySelective(updateCustomer);
        }

        //修改用户绑定的搭配师
        updateCustomerCreateFash(customerDetails,requestData);
//        insertCustomerAskBox.setId(idLeaf.getId());
        customerAskBoxRepository.insertSelective(insertCustomerAskBox);
        return null;
    }

    @Override
    public SaveBoxAskReq updateSaveBoxAskReqData(SaveBoxAskReq requestData) {
        log.info("updateSaveBoxAskReqData = {}",JSONObject.toJSONString(requestData));
        //获取id  随机生成即可    获取搭配师  用户绑定的  如果没有 那么使用默认的
        requestData.setId(idLeaf.getId());
        CustomerDetails customerDetailsRepositoryByUnionId = customerDetailsRepository.findByUnionId(requestData.getUnionid());
        if(customerDetailsRepositoryByUnionId == null){
            throw  new RuntimeException("用户不存在!");
        }
        if(StringUtils.isBlank(requestData.getCreateFasId())){
            if(ObjectUtils.isNotEmpty(customerDetailsRepositoryByUnionId)
                    && StringUtils.isNotBlank(customerDetailsRepositoryByUnionId.getFashionerId())){
                Fashioner fashionerRepositoryById = fashionerRepository.findById(customerDetailsRepositoryByUnionId.getFashionerId());
                // 绑定是搭配师
                if(fashionerRepositoryById != null && fashionerRepositoryById.getIsSales() == 0L){
                    requestData.setCreateFasId(customerDetailsRepositoryByUnionId.getFashionerId());
                }
            }
            log.info("updateSaveBoxAskReqData = {}",JSONObject.toJSONString(requestData));

            // 那么设置默认 用户绑定的不是搭配师 或者是没有绑定
            if(StringUtils.isBlank(requestData.getCreateFasId())){
                log.info("updateSaveBoxAskReqData = {}",JSONObject.toJSONString(requestData));
                // 处理 根据 当前导购的门店iD 查询匹配的数据   先查询所有的搭配师  搭配师必须配置了门店包，排除或者包含
                String storeId = requestData.getStoreId();
                if(StringUtils.isNotBlank(storeId)){
                    log.info("updateSaveBoxAskReqData = {}",JSONObject.toJSONString(requestData));
                    //查询搭配师
                    List<Fashioner> canDistributeFashoners = new ArrayList<>();
                    try {
                        canDistributeFashoners = fashionerService.getCanDistributeFashioners(storeId);
                    }catch (Exception e){
                        log.info("getCanDistributeFashioners出现错误  storeId = {}",storeId,e);
                    }

                    if(CollectionUtils.isNotEmpty(canDistributeFashoners)){
                        Random random  = new Random();
                        // 随机数
                        int i = random.nextInt(canDistributeFashoners.size());
                        Fashioner fashioner = canDistributeFashoners.get(i);
                        requestData.setCreateFasId(fashioner.getId());
                    }else{
                        requestData.setCreateFasId(defaultFashionerId);
                        log.info("updateSaveBoxAskReqData1 = {}",JSONObject.toJSONString(requestData));
                    }
                }else{
                    requestData.setCreateFasId(defaultFashionerId);
                    log.info("updateSaveBoxAskReqData2 = {}",JSONObject.toJSONString(requestData));
                }
            }
        }

        // 获取性别
        AskBoxDataNewReq askBoxDataNewReq = new AskBoxDataNewReq();
        askBoxDataNewReq.setUnionId(requestData.getUnionid());
        //获取selectGender
        if(requestData.getCusGender() == null){
            AskBoxDataNewResp askBoxDataNewResp = askBoxDataNew(askBoxDataNewReq, customerDetailsRepositoryByUnionId);
            if(askBoxDataNewResp.getGenderAttrResp() != null){
                if(askBoxDataNewResp.getGenderAttrResp().getSelectGender() == 2){
                    requestData.setCusGender((short)0);
                }else{
                    requestData.setCusGender(askBoxDataNewResp.getGenderAttrResp().getSelectGender().shortValue());
                }
            }
        }

        // 设置身高体重
        if(StringUtils.isBlank(requestData.getHeight())){
            requestData.setHeight(customerDetailsRepositoryByUnionId.getHeight());
        }
        if(StringUtils.isBlank(requestData.getWeight())){
            requestData.setWeight(customerDetailsRepositoryByUnionId.getWeight());
        }

        requestData.setContactType((short)1);
        requestData.setPlanStatus("0");
        requestData.setSkip((short)1);
        if(StringUtils.isBlank(requestData.getCennectPhone())){
            requestData.setCennectPhone(customerDetailsRepositoryByUnionId.getPhone());
        }
        if(StringUtils.isBlank(requestData.getChannelId())){
            requestData.setChannelId(CustomerAskBoxChannelEnum.CROP_ASK_BOX.getCode());
        }

        // 获取用户的默认地址  如果 没有地址  直接空地址
        if(StringUtils.isBlank(requestData.getLogisticsId())){
            CustomerLogistics para = new CustomerLogistics();
            para.setUnionId(requestData.getUnionid());
            para.setDel(0L);
            para.setLogDefault(1L);
            List<CustomerLogistics> list = customerLogisticsService.findListPara(para);
            if(CollectionUtils.isNotEmpty(list)){
                requestData.setLogisticsId(list.get(0).getId());
            }
        }
        return requestData;
    }

    @Override
    public void importExcelForAskBox(String filePath) {
        // build数据
        List<String> titleList = new ArrayList<>();
        titleList.add("unionid");  //
        titleList.add("gender");  //性别
        titleList.add("weight"); //体重
        titleList.add("height"); // 身高
        titleList.add("fashionerid");  //搭配师id
        titleList.add("logisticsid");  //地址
        titleList.add("phone");  //联系电话


        //返回数据
        List<Map<String,Object>> importData = fileParseService.readExcelByUrl(titleList,filePath);
        List<SaveBoxAskReq> saveBoxAskReqs = buildData(importData);
        for (SaveBoxAskReq saveBoxAskReq : saveBoxAskReqs) {
            try {
                oneStepSaveAskBox(saveBoxAskReq,null);
            }catch (Exception e){
                logger.error("oneStepSaveAskBox 导入数据报错  e= {}",e);
                continue;
            }
        }
    }



    @Override
    public void reminderBox() {
        // 查询90天没有要盒的用户 && 查询90天没有服务单用户 && 近31天没有发过此消息 && 用户没有发送过四次
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, 10000);
        List<String> unionIds = customerAskBoxService.selectReminder();
        if (CollectionUtils.isEmpty(unionIds)) {
            return;
        }

        unionIds.forEach(v -> {
            producerUtil.send(JSON.toJSONBytes(v), MsgTagUtil.NINETY_DAY_USER_NO_FASHION_BOX_MSG_TAG);
        });

    }

    @Override
    public CustomerAskBox getLastAskBoxByUnionId(String unionId) {
        CustomerAskBox para = new CustomerAskBox();
        para.setUnionid(unionId);
        para.setCreateBy(-1L);
        Page<Object> hPage = PageHelper.startPage(1, 10);
        customerAskBoxService.selectListBySelective(para);
        PageInfo<CustomerAskBox> pageInfo = new PageInfo(hPage);
        List<CustomerAskBox> list = pageInfo.getList();
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    @Override
    public CustomerAskBox getLastAskBoxByUnionId(String unionId,String askId) {
        CustomerAskBox para = new CustomerAskBox();
        para.setUnionid(unionId);
        para.setCreateBy(-1L);
        Page<Object> hPage = PageHelper.startPage(1, 10);
        customerAskBoxService.selectListBySelective(para);
        PageInfo<CustomerAskBox> pageInfo = new PageInfo(hPage);
        List<CustomerAskBox> list = pageInfo.getList();
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        List<CustomerAskBox> collect = list.stream().filter(e -> !e.getId().equals(askId)).collect(Collectors.toList());
        if(ObjectUtils.isEmpty(collect)){
            return null;
        }
        return collect.get(0);
    }

    @Override
    public String corpAutoImportAskBox(String url) {
        // 校验数量  1000条以内
        List<Map<String,String>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {
            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,String> result = new HashMap<>();
                    result.put("unionid",integerStringMap.get(0));
                    result.put("fashionerId",integerStringMap.get(1));
                    importData.add(result);
                }
            }
        });

        if(importData.size() >= 1000){
            throw  new RuntimeException("导入主动要盒最多1000条以内,不含1000!");
        }
        //封装数据和插入数据
        List<ImportAskBoxErrorResp> list = buildAndInsertAskBoxData(importData);
        // 上传七牛云 将地址发给前端
        if (CollectionUtils.isNotEmpty(list)) {
            String fileName = System.currentTimeMillis() + ".xlsx";
            EasyExcelUtil.write(fileName, ImportAskBoxErrorResp.class, new IWriteDataExcel<ImportAskBoxErrorResp>() {
                @Override
                public List<ImportAskBoxErrorResp> getData() {
                    return list.stream().map(item -> {
                        ImportAskBoxErrorResp data = new ImportAskBoxErrorResp();
                        data.setUnionid(item.getUnionid());
                        data.setFashionerId(item.getFashionerId());
                        data.setErrorMsg(item.getErrorMsg());
                        return data;
                    }).collect(Collectors.toList());
                }
            });
            File file = new File(fileName);
            String param = qiniuUtil.upload(file.getPath(), "异常检测报告编号"+System.currentTimeMillis() + ".xlsx");
            Integer success = importData.size() - list.size();
            Integer fail = list.size();
            Integer sum = success + fail;
            param = param + ";" + sum + ";" + success + ";" + fail;
            file.delete();
            log.info("redis的key{}", param);
            return param;
        }
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
        return "";
    }

    private List<ImportAskBoxErrorResp> buildAndInsertAskBoxData(List<Map<String, String>> importData) {
        List<ImportAskBoxErrorResp> errorRespList = new ArrayList<>();

        for (Map<String, String> importDatum : importData) {
            String unionid = importDatum.get("unionid");
            String fashionerId = importDatum.get("fashionerId");

            try {
                if(StringUtils.isBlank(unionid) || StringUtils.isBlank(fashionerId)){
                    ImportAskBoxErrorResp importAskBoxErrorResp = new ImportAskBoxErrorResp();
                    importAskBoxErrorResp.setUnionid(unionid);
                    importAskBoxErrorResp.setFashionerId(fashionerId);
                    importAskBoxErrorResp.setErrorMsg("unionid或者搭配师id为空！");
                    errorRespList.add(importAskBoxErrorResp);
                    continue;
                }
                // 封装数据
                SaveBoxAskReq saveBoxAskReq = new SaveBoxAskReq();
                saveBoxAskReq.setUnionid(unionid);
                saveBoxAskReq.setCreateFasId(fashionerId);
                saveBoxAskReq.setChannelId(CustomerAskBoxChannelEnum.CROP_AUTO_ASK_BOX.getCode());
                saveBoxAskReq.setCreateBy(0L);
                updateSaveBoxAskReqData(saveBoxAskReq);
                saveBoxAskReq.setId(null);
                saveBoxAskReq.setAskType(1L);
                ResponseResult responseResult = validData2(saveBoxAskReq);
                if(responseResult.getCode() != 0){
                    ImportAskBoxErrorResp importAskBoxErrorResp = new ImportAskBoxErrorResp();
                    importAskBoxErrorResp.setUnionid(unionid);
                    importAskBoxErrorResp.setFashionerId(fashionerId);
                    importAskBoxErrorResp.setErrorMsg(responseResult.getMsg());
                    errorRespList.add(importAskBoxErrorResp);
                    continue;
                }
                // 进行要盒
                onceSaveBoxAsk(saveBoxAskReq, "系统自动创建");
            }catch (Exception e){
                ImportAskBoxErrorResp importAskBoxErrorResp = new ImportAskBoxErrorResp();
                importAskBoxErrorResp.setUnionid(unionid);
                importAskBoxErrorResp.setFashionerId(fashionerId);
                importAskBoxErrorResp.setErrorMsg(e.getMessage());
                errorRespList.add(importAskBoxErrorResp);
                log.info("buildAndInsertAskBoxData 导入导购自动要盒出现错误 = {}",JSONObject.toJSONString(importDatum),e);
            }

        }
        return errorRespList;
    }

    private WeiXinFans getWeiXinFans(String unionId) {
        List<WeiXinFans> weiXinFansTemp = weiXinFansMapper.findWeiXinFans(unionId);
        List<WeiXinFans> weiXinFansList = weiXinFansTemp.stream().filter(weiXinFans -> Long.valueOf(1).equals(weiXinFans.getSubscribe())).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(weiXinFansList) ? weiXinFansList.get(0) : null;
    }

    private List<SaveBoxAskReq> buildData(List<Map<String, Object>> importData) {
        List<SaveBoxAskReq> result = new ArrayList<>();
        int i = 0;

        for (Map<String, Object> importDatum : importData) {
            i++;
            try{
                SaveBoxAskReq saveBoxAskReq = new SaveBoxAskReq();
                saveBoxAskReq.setUnionid(importDatum.get("unionid").toString());
                saveBoxAskReq.setCusGender(Short.parseShort(importDatum.get("gender").toString()));
                saveBoxAskReq.setWeight(importDatum.get("weight").toString());
                saveBoxAskReq.setHeight(importDatum.get("height").toString());
                saveBoxAskReq.setCreateFasId(importDatum.get("fashionerid").toString());
                saveBoxAskReq.setLogisticsId(importDatum.get("logisticsid").toString());
                saveBoxAskReq.setCennectPhone(importDatum.get("phone").toString());

                saveBoxAskReq = updateSaveBoxAskReqData(saveBoxAskReq);

                //验证
                ResponseResult responseResult = validData2(saveBoxAskReq);
                if (responseResult.getCode() != 0) {
                    logger.error("第"+i+"行,用户已经有未完成的盒子,用户id = {},e = {}",importDatum.get("unionid").toString(),responseResult.getMsg());
                    continue;
                }
                result.add(saveBoxAskReq);
            }catch (Exception e){
                logger.error("第"+i+"行,出现错误,用户id = {},e = {}",importDatum.get("unionid").toString(),e);
                continue;
            }
        }
        return result;
    }

    /**
     * 保存主动要盒第一步
     * @param saveBoxAskResp
     * @param requestData
     * @param userId
     */
    private void firstStepSaveAskBox(SaveBoxAskResp saveBoxAskResp, SaveBoxAskReq requestData, String userId){
        //验证是否存在多个进行中的要盒
        CustomerAskBox params = new CustomerAskBox();
        params.setUnionid(requestData.getUnionid());
        params.setStatus(CustomerAskBoxStatusEnum.READY.getCode().shortValue());
        List<CustomerAskBox> customerAskBoxes = customerAskBoxRepository.selectListBySelective(params);
        if(CollectionUtils.isNotEmpty(customerAskBoxes)){
            throw new BoxException("不能重复要盒!");
        }
        // 第一次保存
        CustomerAskBox insertCustomerAskBOx = new CustomerAskBox();
        BeanUtils.copyProperties(requestData,insertCustomerAskBOx);
        insertCustomerAskBOx.setId(idLeaf.getId());
        insertCustomerAskBOx.setStatus(CustomerAskBoxStatusEnum.INEFFECTIVE.getCode().shortValue());
        customerAskBoxRepository.insertSelective(insertCustomerAskBOx);
        saveBoxAskResp.setCustomerAskBox(insertCustomerAskBOx);
    }

    private void lastStepSaveAskBox(SaveBoxAskResp saveBoxAskResp, SaveBoxAskReq requestData, CustomerDetails customerDetails, String userId){
        CustomerAskBox insertCustomerAskBox = new CustomerAskBox();
        BeanUtils.copyProperties(requestData,insertCustomerAskBox);

        String createFasId = requestData.getCreateFasId();
        log.info("lastStepSaveAskBox requestData:{} ", JSONObject.toJSONString(requestData));
        CustomerAskBox customerAskBoxBefore = customerAskBoxRepository.findById(requestData.getId());
        if(customerAskBoxBefore.getStatus() > 0){
            throw  new BoxException("该状态不能修改!");
        }

        if (StringUtils.isNotBlank(requestData.getCreateFasId())){
            // 添加数据上次服务搭配师数据
            Fashioner fashioner = fashionerRepository.findById(requestData.getCreateFasId());
            requestData.setIsSales(fashioner.getIsSales() == null ? null : fashioner.getIsSales().intValue());
            insertCustomerAskBox.setIsSales(requestData.getIsSales());
        }
        // 标记最后一次保存
        if (StringUtils.isNotBlank(requestData.getCreateFasId()) && customerAskBoxBefore.getStatus() == -1){
            if(ObjectUtils.isEmpty(insertCustomerAskBox.getAskType())){
                throw  new BoxException("要盒类型为空,请核实");
            }

            // 新增
            if(AskTypeEnum.SUB.getCode().equals(insertCustomerAskBox.getAskType())) {
                insertCustomerAskBox.setStatus(CustomerAskBoxStatusEnum.READY.getCode().shortValue());
                // 消耗主动权益和订阅计划
                cusRightsService.checkAndUseRights(insertCustomerAskBox);
            }
            // 更新当前用户为非首次主动要盒
            if(customerDetails.getFirstAskBox() == 1){
                CustomerDetails updateCustomer = new CustomerDetails();
                updateCustomer.setId(customerDetails.getId());
                updateCustomer.setFirstAskBox(0L);
                iCustomerDetailsRepository.updateByPrimaryKeySelective(updateCustomer);
            }

        }
        customerAskBoxRepository.updateByPrimaryKeySelective(insertCustomerAskBox);
        insertCustomerAskBox.setCreateFasId(createFasId);

        // 调整主题搭配库存
        changeAskAttrStorage(insertCustomerAskBox,customerAskBoxBefore);
        //同步身高体重(修改身高体重时才会有值)
        if (ObjectUtils.isNotEmpty(insertCustomerAskBox.getCusGender()) && ObjectUtils.isNotEmpty(insertCustomerAskBox.getCusGender())) {
            CustomerDetails  updateCustomer = new CustomerDetails();
            updateCustomer.setId(customerDetails.getId());
            updateCustomer.setHeight(insertCustomerAskBox.getHeight());
            updateCustomer.setWeight(insertCustomerAskBox.getWeight());
            customerDetailsRepository.updateByPrimaryKeySelective(updateCustomer);
            updateCustomerAnswer(customerDetails.getId(),insertCustomerAskBox.getHeight(),insertCustomerAskBox.getWeight());
        }
        //修改用户绑定的搭配师
        updateCustomerCreateFash(customerDetails,requestData);
        //返回搭配师/导购信息
        FashionerInfoEntity fashionerInfoEntity = getSalesByfashionId(insertCustomerAskBox.getUnionid(), requestData.getCreateFasId());
        saveBoxAskResp.setSales(fashionerInfoEntity);
        // 标签处理
        parseAttr(insertCustomerAskBox);
        saveBoxAskResp.setCustomerAskBox(insertCustomerAskBox);
        //处理后续事件
        processAskBoxSuccessEvent(saveBoxAskResp, customerAskBoxBefore,insertCustomerAskBox, requestData, customerDetails, userId);
    }




    private void parseAttr(CustomerAskBox insertCustomerAskBox) {
        if(StringUtils.isNotBlank(insertCustomerAskBox.getAttrValueIds())){
            // 删除标签
            askBoxAttrMapper.delByCustomerAskId(insertCustomerAskBox.getId());
            // 保存标签
            String[] attrVIds = insertCustomerAskBox.getAttrValueIds().split(",");
            for(String id:attrVIds){
                AskBoxAttr aba = new AskBoxAttr();
                aba.setId(idLeaf.getId());
                aba.setAskBoxId(insertCustomerAskBox.getId());
                aba.setAttrValueId(id);
                askBoxAttrMapper.insertSelective(aba);
            }
        }
    }


    // 主题搭配库存调整
    private void changeAskAttrStorage(CustomerAskBox insertCustomerAskBox,CustomerAskBox customerAskBoxBefore){
        //更换主题搭配
        if (StringUtils.isNotBlank(insertCustomerAskBox.getThemeAttrId()) &&
                StringUtils.isNotBlank(customerAskBoxBefore.getCreateFasId())) {
            if (!insertCustomerAskBox.getThemeAttrId().equals(customerAskBoxBefore.getThemeAttrId())) {
                int type = fashionerRepository.fashionerOrSales(customerAskBoxBefore.getCreateFasId());
                // 释放原来搭配的库存
                attrService.recoveryStorage(customerAskBoxBefore.getThemeAttrId(), type);
                // 使用新搭配的库存
                attrService.useStorage(insertCustomerAskBox.getThemeAttrId(), type);
            }
        }
        // 更换搭配师或保存要盒
        if (StringUtils.isNotBlank(insertCustomerAskBox.getCreateFasId()) &&
                StringUtils.isNotBlank(customerAskBoxBefore.getThemeAttrId())) {
            if (StringUtils.isNoneBlank(customerAskBoxBefore.getCreateFasId())) {
                int type = fashionerRepository.fashionerOrSales(customerAskBoxBefore.getCreateFasId());
                // 释放原来选择的搭配师的库存
                attrService.recoveryStorage(customerAskBoxBefore.getThemeAttrId(), type);
            }
            // 根据当前选择搭配师使用库存
            int type = fashionerRepository.fashionerOrSales(insertCustomerAskBox.getCreateFasId());
            attrService.useStorage(customerAskBoxBefore.getThemeAttrId(), type);
        }
    }

    private void useAskAttrStorage(CustomerAskBox customerAskBox){
        if (StringUtils.isBlank(customerAskBox.getCreateFasId()) ||
                StringUtils.isBlank(customerAskBox.getThemeAttrId())) {
            return;
        }
        // 根据当前选择搭配师使用库存
        int type = fashionerRepository.fashionerOrSales(customerAskBox.getCreateFasId());
        attrService.useStorage(customerAskBox.getThemeAttrId(), type);
    }


    private void processAskBoxSuccessEvent(SaveBoxAskResp saveBoxAskResp, CustomerAskBox customerAskBoxBefore,CustomerAskBox insertCustomerAskBox, SaveBoxAskReq requestData, CustomerDetails customerDetails, String userId){
        if (StringUtils.isNotBlank(requestData.getCreateFasId())
                && customerAskBoxBefore.getStatus() == -1){
            //活动发券
            triggerActivitySendCoupon(saveBoxAskResp, customerDetails);
            //更新用户常用信息并触发任务制
            triggerModifyUserInfoAndWorkTask(requestData, customerDetails, saveBoxAskResp, insertCustomerAskBox, customerAskBoxBefore,"");
            //推送消息
            triggerSendTplSms(requestData);
            //给邀请人发券
            logger.info("主动要盒给邀请人发券 askUserUnionId = {}, askBoxId = {}", customerDetails.getUnionid(), customerAskBoxBefore.getId());
            iShareVoucherService.dealSendInviteUserAward(null, customerDetails, customerAskBoxBefore.getId());
        }
        //更换搭配师// 是否发券 --- 第三步
        if (StringUtils.isNotBlank(requestData.getCreateFasId())) {
            ChangeFashionerReq changeFashionerReq = new ChangeFashionerReq();
            changeFashionerReq.setId(requestData.getId());
            changeFashionerReq.setFashionerId(requestData.getCreateFasId());
            //3 为 小程序修改搭配师
            customerAskBoxService.changeFashioner(changeFashionerReq,userId, 3);
            workBenchService.changeWorkFashioner(requestData.getId(),requestData.getCreateFasId());
        }

    }

    private void triggerActivitySendCoupon(SaveBoxAskResp saveBoxAskResp, CustomerDetails customerDetails){
        LinkedList<RulesList> rulesLists = new LinkedList<>();
        //查询有效期的活动
        List<CouponActivity> validActivity = iCouponActivityService.queryValidActivity(CouponRecordEnum.Type.first_ask_box.getCode());
        if (validActivity.isEmpty()){
            saveBoxAskResp.setIsSend(false);
            return;
        }

        //查询当前活动是否参与过
        CouponActivity currentAcitivty = validActivity.get(0);
        List<String> voucherIds = Arrays.asList(currentAcitivty.getVouchers().split(","));
        voucherIds.forEach(voucherId -> rulesLists.add(new RulesList(Long.valueOf(voucherId),1L)));
        if (rulesLists.isEmpty()) {
            saveBoxAskResp.setIsSend(false);
            return;
        }

        //查询当前活动是否参与过
        iShareVoucherService.sendCouponByType(rulesLists, CouponRecordEnum.Type.first_ask_box, currentAcitivty.getId(), customerDetails.getId(), customerDetails.getUnionid(), false);
        saveBoxAskResp.setIsSend(true);
    }

    private void triggerSendTplSms(SaveBoxAskReq requestData){
        Fashioner fashioner = fashionerRepository.findById(requestData.getCreateFasId());
        SysUser sysUser = sysUserRepository.findById(fashioner.getUserId());
        String phone = fashioner.getPhone();
        if(ObjectUtils.isNotEmpty(sysUser) && sysUser.getIsSales() == 1L){
            phone = sysUser.getUsername();
            // 推送企业微信信息
            sendCompanyWx(requestData);
        }
        CustomerAskBox byId = customerAskBoxRepository.findById(requestData.getId());
        if(StringUtils.isEmpty(byId.getCennectPhone())){
            return;
        }
        Map<String,String> map = new HashMap<>();
        map.put("phone", byId.getCennectPhone());

        TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
        templateMessageDTO.setToUser(phone);
        templateMessageDTO.setTemplateCode(SysTemplateCodeEnum.SMS_CODE_1170.getCode());
        templateMessageDTO.setTemplateParam(map);
        sysMessageService.sendMessageByTemplate(templateMessageDTO);
    }

    private void sendCompanyWx(SaveBoxAskReq requestData) {
        List<MemberCardEntity> clientVip = iExternalCustomerVipService.clientVipVoList(requestData.getUnionid());
        CustomerAskBox customerAskBox = customerAskBoxRepository.findById(requestData.getId());
        CustomerDetails byUnionId = customerDetailsRepository.findByUnionId(requestData.getUnionid());
        // 导购待处理的要盒
        String createFasId = requestData.getCreateFasId();
        IndexReq index  = new IndexReq();
        index.setFashionerId(createFasId);
        index.setDateType("3");
        IndexResp indexResp = workService.newIndex(index);
        Integer count = indexResp.getUnProcessed().getCount();

        try {
            Fashioner fashioner = fashionerRepository.findById(createFasId);
            SysUser sysUser = sysUserRepository.findById(fashioner.getUserId());
            MemberCardEntity temp = null;
            if(CollectionUtils.isNotEmpty(clientVip)){
                for (MemberCardEntity memberCardEntity : clientVip) {
                    Integer salesRepId = memberCardEntity.getSalesRepId();
                    if(salesRepId != null &&sysUser.getcEmployeeId().toString().equals(salesRepId.toString())){
                        temp = memberCardEntity;
                    }
                }
            }
            if(temp == null){
                return ;
            }

            if(sysUser != null && StringUtils.isNotBlank(sysUser.getcEmployeeLinkid())){
                //去empanly_base查询  linkid
                EmployeeBase employeeBase = employeeBaseMapper.selectByLinkId(sysUser.getcEmployeeLinkid());
                if(employeeBase != null){
                    SendMsgCpEntity sendMsgCpEntity = new SendMsgCpEntity();
                    sendMsgCpEntity.setMsgId(200L);
                    List<SendMsgCpEntity.SendMsgCpInner> sendList = new ArrayList<>();
                    SendMsgCpEntity.SendMsgCpInner sendMsgCpInner = new SendMsgCpEntity.SendMsgCpInner();
                    String join1 = String.join(",", sysUser.getcEmployeeLinkid() + "", temp.getCardNo() + "", requestData.getId(), customerAskBox.getLogisticsId());
                    sendMsgCpInner.setPageParams(join1);
                    sendMsgCpInner.setDescriptionParams(""+com.jnby.common.leaf.baidu.fsg.uid.utils.DateUtils.formatDate(new Date(),"yyyy-MM-dd"));
                    String join = String.join(",", byUnionId.getNickName(), DateUtils.formatByDateTimePattern(new Date()));
                    sendMsgCpInner.setContentParams(join);
                    sendMsgCpInner.setToUser(employeeBase.getId()+"");
                    sendList.add(sendMsgCpInner);
                    sendMsgCpEntity.setParams(sendList);
                    Map<String, String> param = new HashMap<>();
                    param.put("msgId", "200");
                    param.put("descriptionParams", ""+com.jnby.common.leaf.baidu.fsg.uid.utils.DateUtils.formatDate(new Date(),"yyyy-MM-dd"));
                    param.put("pageParams",join1);
                    param.put("contentParams", join);
                    sysMessageService.sendTaskWXCPMsg2(param, employeeBase.getId().toString(), null);


//                    String msgId = sysMessageService.sendMsgCp(sendMsgCpEntity);

//                    String url = jumpUrl + "/from-pos?userCode=" + sysUser.getcEmployeeLinkid() + "@jnby&cardNo=" + temp.getCardNo() + "&customerAskId=" + requestData.getId() + "&logisticsId=" + customerAskBox.getLogisticsId();
//                    String text = "您的会员发起了要盒申请，请及时处理 :</br>"+
//                            ""+com.jnby.common.leaf.baidu.fsg.uid.utils.DateUtils.formatDate(new Date(),"yyyy-MM-dd") +"</br>"+
//                            "会员昵称:【"+byUnionId.getNickName()+"】</br>" +
//                            "手机号  :【"+byUnionId.getPhone()+"】</br>要盒时间:【"+DateUtils.formatByDateTimePattern(new Date())+"】</br>" +
//                            "剩余待处理会员要盒:【"+count+"】</br>";

//                    SendMessageBySdk sendMessageBySdk = new SendMessageBySdk();
//                    List<CpParams> listParams = new ArrayList<>();
//                    CpParams cpParams = new CpParams();
//                    CpTextcard cpTextcard = new CpTextcard();
//                    cpTextcard.setDescription(text);
//                    cpTextcard.setTitle("会员要盒提醒");
//                    cpTextcard.setUrl(url);
//                    cpParams.setCpTextcard(cpTextcard);
//                    cpParams.setToUser(employeeBase.getId()+"");
//                    cpParams.setAgentId(sendMessageAgentId);
//                    listParams.add(cpParams);
//                    sendMessageBySdk.setCpParams(listParams);


                }
            }
        }catch (Exception e){
            logger.error("sendCompanyWx = {}",requestData,e);
        }
    }

    private void triggerModifyUserInfoAndWorkTask(SaveBoxAskReq requestData, CustomerDetails customerDetails, SaveBoxAskResp saveBoxAskResp,
                                                  CustomerAskBox insertCustomerAskBox, CustomerAskBox customerAskBoxBefore,String userId){
        String voucherAmount = ifSendCoupon(requestData.getUnionid(),customerDetails,requestData.getId());

        saveBoxAskResp.setVoucherAmount(voucherAmount);
        BCustomerInformation params = new BCustomerInformation();
        params.setUserId(customerDetails.getId());
        List<BCustomerInformation> bCustomerInformations = bCustomerInformationMapper.selectBySelective(params);
        if(CollectionUtils.isNotEmpty(bCustomerInformations)){
            insertCustomerAskBox.setLastFashionerId(bCustomerInformations.get(0).getLastFashionerId());
            insertCustomerAskBox.setLastFashionerName(bCustomerInformations.get(0).getLastFashionerName());
        }

        // 最后一步保存，将记录改为待搭配
       // insertCustomerAskBox.setStatus(CustomerAskBoxStatusEnum.READY.getCode().shortValue());
        // 更新用户累计消费金额
        BigDecimal orderAmount = orderMapper.sumOrderAmount(customerDetails.getId());
        insertCustomerAskBox.setTotBuyAmount(orderAmount);
        //保存快照
        saveSnapshotData(insertCustomerAskBox);
        // 同步身高体重
        if (ObjectUtils.isNotEmpty(customerAskBoxBefore.getCusGender()) && ObjectUtils.isNotEmpty(customerAskBoxBefore.getCusGender())) {
            CustomerDetails  updateCustomer = new CustomerDetails();
            updateCustomer.setId(customerDetails.getId());
            updateCustomer.setHeight(customerAskBoxBefore.getHeight());
            updateCustomer.setWeight(customerAskBoxBefore.getWeight());
            customerDetailsRepository.updateByPrimaryKeySelective(updateCustomer);
            updateCustomerAnswer(customerDetails.getId(),customerAskBoxBefore.getHeight(),customerAskBoxBefore.getWeight());

        }
        if(ObjectUtils.isEmpty(insertCustomerAskBox.getStatus())){
            return;
        }
        if(CustomerAskBoxStatusEnum.INEFFECTIVE.getCode().equals(insertCustomerAskBox.getStatus().intValue())){
            return;
        }
        log.info("保存主动要盒触发待处理任务制,askBoxId={}",insertCustomerAskBox.getId());
        if(StringUtils.isNotBlank(userId) && userId.equals("系统自动创建")){
            // 系统导入的 生成的任务
            workBenchService.executorWorkBench(null, insertCustomerAskBox.getCreateFasId(), insertCustomerAskBox.getId(),
                    insertCustomerAskBox.getUnionid(), customerDetails.getNickName(), customerDetails.getPhone(),
                    WorkBenchTaskTriggerEnum.TASK_TRIGGER_ENUM_SYSTEM_CREATE_BOX.getValue(),
                    WorkBenchTaskTypeEnum.TASK_TYPE_ENUM_SUB_PLAN_BOX.getValue());
        }else{
            // work-生成会员要盒待处理任务制
            workBenchService.executorWorkBench(null, insertCustomerAskBox.getCreateFasId(), insertCustomerAskBox.getId(),
                    insertCustomerAskBox.getUnionid(), customerDetails.getNickName(), customerDetails.getPhone(),
                    WorkBenchTaskTriggerEnum.TASK_TRIGGER_ENUM_MEMBER_CREATE_BOX.getValue(),
                    WorkBenchTaskTypeEnum.TASK_TYPE_ENUM_MEMBER_BOX.getValue());
        }

    }

    @Override
    public SaveBoxAskResp saveBoxAsk(SaveBoxAskReq requestData, String userId) {
        SaveBoxAskResp saveBoxAskResp = new SaveBoxAskResp();
        //新增first
        CustomerDetails customerDetails = customerDetailsRepository.findByUnionId(requestData.getUnionid());

        template.execute(action -> {
            if (ObjectUtils.isNotEmpty(requestData) && StringUtils.isEmpty(requestData.getId())) {
                firstStepSaveAskBox(saveBoxAskResp, requestData, userId);
                return saveBoxAskResp;
            }

            //last
            lastStepSaveAskBox(saveBoxAskResp, requestData, customerDetails, userId);
            return action;
        });
        return saveBoxAskResp;
    }


    @Override
    public SaveBoxAskResp onceSaveBoxAsk(SaveBoxAskReq requestData, String user_id) {
        CustomerAskBox customerAskBox = new CustomerAskBox();
        String createFasId = requestData.getCreateFasId();
        AtomicBoolean ifChangeFashioner = new AtomicBoolean(false);
        BeanUtils.copyProperties(requestData,customerAskBox);
        SaveBoxAskResp saveBoxAskResp = new SaveBoxAskResp();

        logger.info("onceSaveBoxAsk requestData:{} customerAskBox:{}", JSONObject.toJSONString(requestData), JSONObject.toJSONString(customerAskBox));
        template.execute(action -> {
            if(StringUtils.isNotBlank(customerAskBox.getId())){
                CustomerAskBox customerAskBoxBefore = customerAskBoxService.findById(requestData.getId());
                if(ObjectUtils.isEmpty(customerAskBoxBefore)){
                    throw new RuntimeException("未查询到主动要盒信息,请核实");
                }
                if(!createFasId.equals(customerAskBoxBefore.getCreateFasId())){
                    ifChangeFashioner.set(true);
                }

                // 不修改搭配师，在最后逻辑changeFashioner里触发修改搭配师
                customerAskBox.setCreateFasId(null);
                customerAskBox.setUpdateTime(new Date());
                // 修改
                customerAskBoxService.updateByPrimaryKey(customerAskBox);
            }else{
                customerAskBox.setId(idLeaf.getId());
                customerAskBox.setCreateTime(new Date());
                customerAskBox.setUpdateTime(new Date());
                // 新增
                if(AskTypeEnum.SUB.getCode().equals(customerAskBox.getAskType())){
                    customerAskBox.setStatus(CustomerAskBoxStatusEnum.READY.getCode().shortValue());
                    // 消耗主动权益和订阅计划
                    cusRightsService.checkAndUseRights(customerAskBox);
                }else{
                    customerAskBox.setStatus(CustomerAskBoxStatusEnum.INEFFECTIVE.getCode().shortValue());
                }

                Fashioner fashioner = fashionerRepository.findById(requestData.getCreateFasId());
                customerAskBox.setIsSales(fashioner.getIsSales() == null ? null : fashioner.getIsSales().intValue());
                customerAskBoxRepository.insertSelective(customerAskBox);
                // 使用主题搭配库存
                useAskAttrStorage(customerAskBox);
            }

            // 标签处理
            parseAttr(customerAskBox);
            CustomerDetails customerDetails = customerDetailsService.findByUnionId(customerAskBox.getUnionid());
            //修改用户绑定的搭配师
            updateCustomerCreateFash(customerDetails,requestData);
            // 处理后续事件
            changeOtherBusiness(customerAskBox,customerDetails,requestData,saveBoxAskResp,user_id, ifChangeFashioner.get());
            saveBoxAskResp.setCustomerAskBox(customerAskBox);
            return action;
        });
        return saveBoxAskResp;


    }

    private void changeOtherBusiness(CustomerAskBox customerAskBox,CustomerDetails customerDetails,SaveBoxAskReq requestData,SaveBoxAskResp saveBoxAskResp,String userId,boolean ifChangeFashioner){
        //返回搭配师/导购信息
        FashionerInfoEntity fashionerInfoEntity = getSalesByfashionId(customerAskBox.getUnionid(), requestData.getCreateFasId());
        saveBoxAskResp.setSales(fashionerInfoEntity);
        // 只有新增的时候触发
        if(StringUtils.isBlank(requestData.getId())){
            //活动发券
            triggerActivitySendCoupon(saveBoxAskResp, customerDetails);
            //订阅计划
            requestData.setId(customerAskBox.getId());
            //更新用户常用信息并触发任务制
            triggerModifyUserInfoAndWorkTask(requestData, customerDetails, saveBoxAskResp, customerAskBox, customerAskBox,userId);
            // 系统生成的要盒不推送消息
            if(!Long.valueOf(0L).equals(customerAskBox.getCreateBy())) {
                triggerSendTplSms(requestData);
            }
            //给邀请人发券
            logger.info("主动要盒给邀请人发券 askUserUnionId = {}, askBoxId = {}", customerDetails.getUnionid(), customerAskBox.getId());
            iShareVoucherService.dealSendInviteUserAward(null, customerDetails, customerAskBox.getId());
        }
        // 当搭配师发生改变时触发
        if(ifChangeFashioner){
            ChangeFashionerReq changeFashionerReq = new ChangeFashionerReq();
            changeFashionerReq.setId(requestData.getId());
            changeFashionerReq.setFashionerId(requestData.getCreateFasId());
            customerAskBoxService.changeFashioner(changeFashionerReq, userId, 3);
        }

    }

    private void updateCustomerAnswer(String id, String height, String weight) {
        BCustomerInformation customerInformation = ibCustomerInformationService.getById(id);
        if(customerInformation == null){
            customerInformation = new BCustomerInformation();
            CustomerInfoDataExpand dataExpand = new CustomerInfoDataExpand();
            CustomerInfoForQuestion customerInfoForQuestion  = new CustomerInfoForQuestion();
            if(StringUtils.isNotBlank(height)){
                customerInfoForQuestion.setHeight(height);
            }
            if(StringUtils.isNotBlank(weight)){
                customerInfoForQuestion.setWeight(weight);
            }
            dataExpand.setCustomerInfoForQuestion(customerInfoForQuestion);
            customerInformation.setDataExpand(JSONObject.toJSONString(dataExpand));
            customerInformation.setUserId(id);
            bCustomerInformationMapper.insert(customerInformation);

        }else{
            CustomerInfoDataExpand dataExpand = Optional.ofNullable(customerInformation.getDataExpand())
                    .map(cusJson -> JSON.parseObject(cusJson, CustomerInfoDataExpand.class))
                    .orElse(new CustomerInfoDataExpand());
            CustomerInfoForQuestion customerInfoForQuestion = dataExpand.getCustomerInfoForQuestion();
            if(customerInfoForQuestion == null){
                customerInfoForQuestion = new CustomerInfoForQuestion();
            }

            if(StringUtils.isNotBlank(height)){
                customerInfoForQuestion.setHeight(height);
            }
            if(StringUtils.isNotBlank(weight)){
                customerInfoForQuestion.setWeight(weight);
            }

            dataExpand.setCustomerInfoForQuestion(customerInfoForQuestion);
            customerInformation.setDataExpand(JSONObject.toJSONString(dataExpand));

            ibCustomerInformationService.updateById(customerInformation);
        }
    }


    /**
     * 更换搭配师   customer_details
     */
    private void updateCustomerCreateFash(CustomerDetails customerDetails,SaveBoxAskReq saveBoxAskReq){
        log.info("customerDetails = {}",JSONObject.toJSONString(customerDetails));
        log.info("saveBoxAskReq = {}",JSONObject.toJSONString(saveBoxAskReq));
        if(StringUtils.isNotBlank(saveBoxAskReq.getCreateFasId())){
            CustomerDetails update = new CustomerDetails();
            update.setId(customerDetails.getId());
            update.setFashionerId(saveBoxAskReq.getCreateFasId());

            //扫码进入
            if(saveBoxAskReq.getIsScanEnter()){
                // 获取最后一次要盒
                CustomerAskBox lastAskBox = getLastAskBoxByUnionId(customerDetails.getUnionid());
                // 无上一次要盒
                if(lastAskBox == null){
                    Fashioner fashionerRepositoryById = fashionerRepository.findById(customerDetails.getFashionerId());
                    if(!fashionerRepositoryById.getId().equals(saveBoxAskReq.getCreateFasId())){
                        customerDetailsRepository.updateByPrimaryKeySelective(update);
                        saveFashionerChange(saveBoxAskReq,customerDetails,"用户扫搭配师/导购要盒推广码系统自动绑定");
                    }
                }else{
                    // 有上一次要盒
                    // 使用的是最后一次要盒的搭配师   不是扫码的搭配师
                    if(saveBoxAskReq.getIsScanEnterFashioner()){
                        Fashioner fashionerRepositoryById = fashionerRepository.findById(saveBoxAskReq.getCreateFasId());
                        //搭配师
                        if(fashionerRepositoryById.getIsSales() == 0L && !fashionerRepositoryById.getId().equals(customerDetails.getFashionerId())){
                            customerDetailsRepository.updateByPrimaryKeySelective(update);
                            saveFashionerChange(saveBoxAskReq,customerDetails,"用户扫搭配师/导购要盒推广码系统自动绑定");
                        }
                    }
                }
            }else{
                // 1. 如果没有绑定搭配师   判断为 0  或者 null    则换绑当前的搭配师
                if(StringUtils.isBlank(customerDetails.getFashionerId()) || customerDetails.getFashionerId().equals("0")){
                    //换绑当前搭配师
                    customerDetailsRepository.updateByPrimaryKeySelective(update);
                    saveFashionerChange(saveBoxAskReq,customerDetails,"用户未绑定搭配师情况下要盒自动绑定搭配师");
                }else if(StringUtils.isNotBlank(customerDetails.getFashionerId())){
                    // 判断是否是导购
                    Fashioner fashionerRepositoryById = fashionerRepository.findById(customerDetails.getFashionerId());

                    if(1 == fashionerRepositoryById.getIsSales()){
                        // 判断当前更换的是否是搭配师
                        Fashioner changeFashioner = fashionerRepository.findById(saveBoxAskReq.getCreateFasId());
                        if(changeFashioner.getIsSales() == 0){
                            customerDetailsRepository.updateByPrimaryKeySelective(update);
                            saveFashionerChange(saveBoxAskReq,customerDetails,"用户主动要盒从导购切换到搭配师");
                        }
                    }else if(0L == fashionerRepositoryById.getIsSales()
                            // 这个判断理论上在上面判断了，saveBoxAskReq.getIsScanEnter() 永远是 false，这个分支判断就不会执行
                            // 存在即合理：测试用例评审说这个逻辑下就不应该换绑（负负得正了，666）
                            && saveBoxAskReq.getIsScanEnter()
                            && !fashionerRepositoryById.getId().equals(saveBoxAskReq.getCreateFasId())){
                        // 如果是搭配师  并且 是扫码进来的 ， 并且 用户绑定的搭配师和当前搭配师不一致
                        customerDetailsRepository.updateByPrimaryKeySelective(update);
                        saveFashionerChange(saveBoxAskReq,customerDetails,"用户扫搭配师要盒推广码系统自动绑定");
                    }else if (0L == fashionerRepositoryById.getIsSales()
                            && 2 == saveBoxAskReq.getCusGender()){
                        // 童装类目 且 绑定搭配师是搭配师，绑定搭配师和换绑搭配师不是同一个，换绑是搭配师，换绑。换绑是导购，不换
                        Fashioner needBindFashioner = fashionerRepository.findById(saveBoxAskReq.getCreateFasId());
                        if(needBindFashioner.getIsSales() == 0L && !needBindFashioner.getId().equals(customerDetails.getFashionerId())){
                            customerDetailsRepository.updateByPrimaryKeySelective(update);
                            saveFashionerChange(saveBoxAskReq,customerDetails,"童装类目用户主动要盒换绑搭配师");
                        }
                    }
                }
            }
        }
    }

    private void saveFashionerChange(SaveBoxAskReq saveBoxAskReq, CustomerDetails customerDetails, String remark) {

        List<FashionerChange> batchSaveFashionerChanges = new ArrayList<>();
        FashionerChange fashionerChange = new FashionerChange();
        fashionerChange.setFashionerId(saveBoxAskReq.getCreateFasId());
        fashionerChange.setId(idLeaf.getId());
        fashionerChange.setModifyId(null);
        fashionerChange.setIsSend(0L);
        fashionerChange.setMemo(remark);
        fashionerChange.setUnionid(customerDetails.getUnionid());
        fashionerChange.setCreateTime(new Date());
        fashionerChange.setUpdateTime(new Date());
        fashionerChange.setOriginFashionerId(StringUtils.isNotBlank(customerDetails.getFashionerId())
                ? customerDetails.getFashionerId() : null);
        fashionerChange.setModifyName(null);
        batchSaveFashionerChanges.add(fashionerChange);

        if(CollectionUtils.isNotEmpty(batchSaveFashionerChanges)){
            fashionerChangeService.saveBatch(batchSaveFashionerChanges, batchSaveFashionerChanges.size());
        }
    }


    @Override
    public List<FashionerInfoEntity> getChangeFashioer(String id) {
        CustomerAskBox customerAskBox = customerAskBoxService.findById(id);
        GetFashionersReq getFashionersReq = new GetFashionersReq();
        getFashionersReq.setAttrThemeId(customerAskBox.getThemeAttrId());
        getFashionersReq.setUnionId(customerAskBox.getUnionid());
        getFashionersReq.setGender(customerAskBox.getCusGender().intValue());
        GetFashionersResp fashioners1 = getFashioners(getFashionersReq);
        List<FashionerInfoEntity> fashionerInfoEntities = fashioners1.getFashionerInfoEntities();
        return fashionerInfoEntities;
    }



    private FashionerInfoEntity getSalesByfashionId(String unionid, String createFasId) {
        Fashioner fashioner = fashionerRepository.findById(createFasId);
        FashionerInfoEntity fashionerInfoEntity = null;
        if(fashioner != null){
            SysUser sysUser = sysUserRepository.findById(fashioner.getUserId());
            if(ObjectUtils.isNotEmpty(sysUser) && sysUser.getIsSales() == 0){
                fashionerInfoEntity = setFashionerInfo(fashioner);
            } else {
                List<BoxCustomerGuide> salesBySalesPhone = cclientVipMapper.getSalesBySalesPhone(fashioner.getPhone(), unionid,fashioner.getHrEmpId());
                if(CollectionUtils.isNotEmpty(salesBySalesPhone)){
                    fashionerInfoEntity = setSalesInfo(fashioner, salesBySalesPhone.get(0));
                }
            }
        }
        return fashionerInfoEntity;
    }

    /**
     * 保存数据快照
     * @param insertCustomerAskBox
     */
    private void saveSnapshotData(CustomerAskBox insertCustomerAskBox) {
        Snapshotd snapshotd = new Snapshotd();
        Map<String,String> jsonData = new HashMap<>();
        //剩余/进行box数
        Integer unhandleCount = subscribeAskMapper.getUnhandleCountByUnionid(insertCustomerAskBox.getUnionid());
        Integer undoneBoxCount = boxRepository.unDoneBoxCount(insertCustomerAskBox.getUnionid());
        jsonData.put("data1",unhandleCount+"/"+undoneBoxCount);
        Integer boxCount = boxRepository.getBoxCountByUnionid(insertCustomerAskBox.getUnionid());
        Integer buyCount = orderMapper.getBoxBuyCountByUnionid(insertCustomerAskBox.getUnionid());
        jsonData.put("data2",boxCount+"/"+buyCount);

        snapshotd.setType(1);
        snapshotd.setData(JSONObject.toJSONString(jsonData));
        snapshotd.setSourceId(insertCustomerAskBox.getId());
        snapshotd.setId(idLeaf.getId());
        Integer integer = snapshotdMapper.insertSelective(snapshotd);
    }

    /**
     * 是否发券
     * @return
     */
    private String ifSendCoupon(String unionId, CustomerDetails customerDetails, String customerAskBoxId) {
        LinkedList<RulesList> rulesLists = new LinkedList<>();
        boolean ifFirst = false;
        List<CustomerAskBox> lastByUnionid = customerAskBoxRepository.findLastByUnionid(unionId);
        if(CollectionUtils.isEmpty(lastByUnionid)){
            ifFirst = true;
        }

        List<AttrValue> attrValues = attrValueMapper.findByAttrId("12");

        if(ifFirst && CollectionUtils.isNotEmpty(attrValues)){
            // 发券
            String[] voucherIds = Optional.ofNullable(attrValues.get(0).getName())
                    .map(e -> e.split(","))
                    .orElse(new String[]{});
            logger.info("异步执行首次要盒发券,unionId={},券总面额={}",unionId,attrValues.get(0).getCode());

            List<String> voucherIdList = Arrays.asList(voucherIds);
            voucherIdList.forEach(voucherId -> rulesLists.add(new RulesList(Long.valueOf(voucherId),1L)));
            if(!rulesLists.isEmpty()){
                iShareVoucherService.sendCouponByType(rulesLists,
                        CouponRecordEnum.Type.first_ask_box_config,
                        customerAskBoxId,
                        customerDetails.getId(),
                        customerDetails.getUnionid(), false);
            }
            return attrValues.get(0).getCode();
        }
        return null;
    }

    /**
     * 查品牌卡
     * @param unionid
     * @param phone
     * @param brand
     */
    private CclientVip getVipByBrand(String unionid, String phone, String brand) {
        CclientVip cclientVip = new CclientVip();
        cclientVip.setUnionid(unionid);
        cclientVip.setIsactive("Y");
        if(StringUtils.isNotBlank(phone)){
            cclientVip.setMobil(phone);
        }

        List<CclientVip> cclientVips = cclientVipMapper.selectListByUnionIdOrMobli(cclientVip);

        for (CclientVip vip : cclientVips) {
            CVipType cVipType = cVipTypeMapper.selectByPrimaryKey(vip.getcViptypeId());
            if (cVipType.getDescription().toLowerCase().equals(brand.toLowerCase())) {
                return vip;
            }
        }
        return null;
    }

    /**
     * 获取可选搭配师
     * @param customerDetails
     * @param gender
     * @param brand
     * @return
     */
    private GetFashionersResp getFashioners2(CustomerDetails customerDetails, Integer gender, String brand) {
        // 童装
        if (gender == 2) {
            log.info("当前类目是童装,走新逻辑 param={}", JSON.toJSONString(customerDetails));
            return getChildFashioners(customerDetails);
        }
        log.info("当前类目是男装或女装，走老逻辑");
        GetFashionersResp getFashionersResp = new GetFashionersResp();

        // 指定性别品牌    10 男装  20 女装
        int expert = gender == 0? 20:10;
        List<String> selectBrand = new ArrayList<String> (Arrays.asList(gender == 0 ? womenBrand : manBrand));
        if(StringUtils.isNotBlank(brand)){
            selectBrand = new ArrayList<>();
            selectBrand.add(parseBrandToVip(brand));
        }
        // 获取符合的搭配师
        List<Fashioner> fashionerList = fashionerRepository.findByExpert(expert);
        //排序
        fashionerList = fashionerList.stream().filter(r->r.getIfUseMiniapp() == 1)
                .sorted(new Comparator<Fashioner>() {
                    @Override
                    public int compare(Fashioner o1, Fashioner o2) {
                        if((o1.getPriority() == null ? 999 : o1.getPriority()) - (o2.getPriority() == null ? 999: o2.getPriority()) == 0){
                            if(((o2.getUpdateTime() == null ? 0L :o2.getUpdateTime().getTime())  - (o1.getUpdateTime() == null ? 0L : o1.getUpdateTime().getTime())) == 0){
                                return 0 ;
                            }else if(((o2.getUpdateTime() == null ? 0L :o2.getUpdateTime().getTime())  - (o1.getUpdateTime() == null ? 0L : o1.getUpdateTime().getTime())) > 0){
                                return 1;
                            }else {
                                return -1;
                            }
                        }else{
                            return (o1.getPriority() == null ? 999 : o1.getPriority().intValue()) - (o2.getPriority() == null ? 999: o2.getPriority().intValue());
                        }
                    }
                }).collect(Collectors.toList());

        List<FashionerInfoEntity> fashioners = createSalesListInfo(fashionerList);

        // 查询用户所有拥有的卡
        CclientVip cclientVip = new CclientVip();
        cclientVip.setUnionid(customerDetails.getUnionid());
        if(StringUtils.isNotBlank(customerDetails.getPhone())){
            cclientVip.setMobil(customerDetails.getPhone());
        }
        cclientVip.setIsactive("Y");
        List<CclientVip> records = cclientVipMapper.selectListByUnionIdOrMobli(cclientVip);

        // 当前代码是  没有任何卡
        if(CollectionUtils.isEmpty(records)){
            // 是否绑定搭配师
            getFashionersResp.setCode(0);

            Integer sales = fashionerRepository.fashionerOrSales(customerDetails.getFashionerId());
            if(sales == 0){
                //绑定的搭配师
                Fashioner fashionerById = fashionerRepository.findById(customerDetails.getFashionerId());
                List<FashionerInfoEntity> resultList =  new ArrayList<>();
                FashionerInfoEntity fashionerInfoEntity = setFashionerInfo(fashionerById);
                resultList.add(fashionerInfoEntity);
                getFashionersResp.setFashionerInfoEntities(resultList);
            }else{
                getFashionersResp.setFashionerInfoEntities(fashioners);
            }
            return getFashionersResp;
        }

        // 当前代码是 用户有卡
        boolean flag = true; // 是否全部经销
        int cardJXNum = 0; //经销品牌数量
        //是否有江南布衣卡 或者 奥莱卡
        boolean haveJNByOrOali = false;

        // 可用的品牌
        List<String> brandList = new ArrayList<>();
        // cStoreId
        Long cStoreId = null;


        for(int i = 0; i < records.size(); i++){
            //获取卡的信息
            CVipType cVipType = cVipTypeMapper.selectByPrimaryKey(records.get(i).getcViptypeId());

            //江南布衣+ ka  或者 奥莱   后面那部在判断是否可以用奥莱的导购
            if(Arrays.asList(jnbyAndOlai.split(",")).contains(cVipType.getDescription())){
                brandList.add(cVipType.getDescription());
                if(olaiBrand.equals(cVipType.getDescription())){
                    cStoreId = records.get(i).getcStoreId();
                }
                haveJNByOrOali = true;
            }

            if(records.get(i).getcCustomerId() != null && !records.get(i).getcCustomerId().equals(176L)){
                if(cVipType != null &&  selectBrand.contains(cVipType.getDescription())){
                    cardJXNum ++;
                }
                // 经销用户再校验是否在限制品牌内
                if (cVipType != null && Arrays.asList(dealerBrand.split(",")).contains(cVipType.getDescription())) {
                    CCustomer cCustomer = cCustomerMapper.selectByPrimaryKey(records.get(i).getcCustomerId());
                    // 判断是否在经销开放区域内
                    if (!Arrays.asList(dealerOpenZone.split(",")).contains(cCustomer.getCode())) {
                        continue;
                    }
                }
            }
            //  按  男性 品牌或者 女性品牌插入
            if (selectBrand.contains(cVipType.getDescription())) {
                brandList.add(cVipType.getDescription());
            }
        }

        // 根据品牌获取导购数据
        List<FashionerInfoEntity> salesList = parseData(brandList,customerDetails.getUnionid(),expert,cStoreId);
        getFashionersResp.setCode(1);
        Integer sales = fashionerRepository.fashionerOrSales(customerDetails.getFashionerId());
        if(sales == 0){
            // 搭配师，取绑定搭配师靠前
            Fashioner fashionerById = fashionerRepository.findById(customerDetails.getFashionerId());
            FashionerInfoEntity salesInfo = setFashionerInfo(fashionerById);
            fashioners = new ArrayList<FashionerInfoEntity> ();
            fashioners.add(salesInfo);
            fashioners.addAll(salesList);
            getFashionersResp.setFashionerInfoEntities(fashioners);
        }else{
            // 导购，搭配师靠后
            fashioners.addAll(salesList);
//            salesList.addAll(fashioners);
            getFashionersResp.setFashionerInfoEntities(fashioners);
        }
        return getFashionersResp;
    }

    private GetFashionersResp getChildFashioners(CustomerDetails customerDetails) {
        GetFashionersResp getFashionersResp = new GetFashionersResp();

        // 1. 获取通用搭配师列表
        List<Fashioner> fashionerList = Optional.ofNullable(fashionerRepository.findByExpert(30))
                .orElse(Lists.newArrayList()).stream()
                .filter(r -> r.getIfUseMiniapp() == 1)
                .sorted(Comparator.comparing((Fashioner r) -> (r.getPriority() == null) ? 999 : r.getPriority())
                        .thenComparing((Fashioner r) -> r.getUpdateTime() == null ? 0L : r.getUpdateTime().getTime()))
                .collect(Collectors.toList());
        List<FashionerInfoEntity> fashioners = createSalesListInfo(fashionerList);

        // 2. 查询用户所有拥有的卡
        CclientVip cclientVip = new CclientVip();
        cclientVip.setUnionid(customerDetails.getUnionid());
        if(StringUtils.isNotBlank(customerDetails.getPhone())){
            cclientVip.setMobil(customerDetails.getPhone());
        }
        cclientVip.setIsactive("Y");
        List<CclientVip> records = cclientVipMapper.selectListByUnionIdOrMobli(cclientVip);
        // 如果没有任何卡,直接返回通用搭配师
        if(CollectionUtils.isEmpty(records)){
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(fashioners), "暂无搭配师，请联系客服处理");
            log.info("没有童装品牌卡,直接返回通用搭配师");
            getFashionersResp.setCode(0);
            getFashionersResp.setFashionerInfoEntities(fashioners);
            return getFashionersResp;
        }
        log.info("用户品牌卡查询 结果={}", JSON.toJSONString(records));

        // 3. 过滤出直营的童装品牌卡
        List<String> brandList = new ArrayList<>();
        for(CclientVip vip : records) {
            CVipType cVipType = cVipTypeMapper.selectByPrimaryKey(vip.getcViptypeId());
            // 判断是否是童装品牌且是直营
            if(childBrand.contains(cVipType.getDescription()) &&
               (vip.getcCustomerId() == null || vip.getcCustomerId().equals(176L))) {
                brandList.add(cVipType.getDescription());
            }
        }
        log.info("过滤出直营后的品牌 结果={}", JSON.toJSONString(brandList));
        // 4. 如果没有直营童装品牌卡,返回通用搭配师
        if(CollectionUtils.isEmpty(brandList)){
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(fashioners), "暂无搭配师，请联系客服处理");
            log.info("没有直营童装品牌卡,返回通用搭配师");
            getFashionersResp.setCode(0);
            getFashionersResp.setFashionerInfoEntities(fashioners);
            return getFashionersResp;
        }

        // 5. 根据品牌获取导购列表
        List<FashionerInfoEntity> salesList = new ArrayList<>();
        if (brandList.size() > 1) {
            brandList = brandList.stream().sorted(Comparator.comparingInt(brand -> childBrand.indexOf(brand))).collect(Collectors.toList());
            log.info("重排序品牌 结果={}", JSON.toJSONString(brandList));
        }
        for(String brand : brandList){
            List<BoxCustomerGuide> saleList = cclientVipMapper.getSalesByBrandAndUnionId(brand,customerDetails.getUnionid());
            if(CollectionUtils.isNotEmpty(saleList)){
                BoxCustomerGuide boxCustomerGuide = saleList.get(0);
                Fashioner fashioner = fashionerRepository.findEnableSalsesByPhone(boxCustomerGuide.getMobile(),null,boxCustomerGuide.getHrId());
                if(ObjectUtils.isNotEmpty(fashioner)){
                    // 多品牌同一导购的情况
                    FashionerInfoEntity salesInfo = setSalesInfo(fashioner, boxCustomerGuide);
                    salesList.add(salesInfo);
                }
            }
        }
        // 6. 如果没有导购,返回通用搭配师
        if(CollectionUtils.isEmpty(salesList)){
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(fashioners), "暂无搭配师，请联系客服处理");
            log.info("没有导购,返回通用搭配师");
            getFashionersResp.setCode(0); 
            getFashionersResp.setFashionerInfoEntities(fashioners);
            return getFashionersResp;
        }

        // 7. 有导购的情况,追加通用搭配师
        getFashionersResp.setCode(1);
        salesList.addAll(fashioners);
        getFashionersResp.setFashionerInfoEntities(salesList);
        return getFashionersResp;
    }

    /**
     * 转换品牌卡
     * @param brand
     * @return
     */
    private String parseBrandToVip(String brand) {
        switch (brand) {
            case "LESS":
                return "less";
            case "童装":
                return "jnby by JNBY";
            case "蓬马":
                return "Pomme de terre";
            case "Reverb":
                return "REVERB";
            case "LASU":
                return "LASU MIN SOLA";
            case "A PERSONAL NOTE 73":
                return "APN73";
            default:
                return brand;
        }
    }

    /**
     * 根据品牌获取导购
     * @param brandList
     * @param unionid
     * @param expert
     * @return
     */
    @Override
    public List<FashionerInfoEntity> parseData(List<String> brandList, String unionid, int expert,Long cStoreId) {

        List<FashionerInfoEntity> salesList = new ArrayList<>();

        // 特殊原因  里面奥莱的品牌  奥莱的品牌分为 男装女装 通用
        String olai = olaiBrand;

        boolean contains = brandList.contains(olai);
        //如果有奥莱 移除奥莱  后面做操作
        if(contains){
            brandList.remove(olai);
        }

        // 按照品牌获取导购
        for(String brand : brandList){
            List<BoxCustomerGuide> sales = cclientVipMapper.getSalesByBrandAndUnionId(brand,unionid);
            if(CollectionUtils.isNotEmpty(sales)){
                BoxCustomerGuide boxCustomerGuide = sales.get(0);
                Fashioner fashioner = fashionerRepository.findEnableSalsesByPhone(boxCustomerGuide.getMobile(),null,boxCustomerGuide.getHrId());
                if(ObjectUtils.isNotEmpty(fashioner)){
                    // 多品牌同一导购的情况
                    FashionerInfoEntity salesInfo = setSalesInfo(fashioner, boxCustomerGuide);
                    salesList.add(salesInfo);
                }
            }
        }
        //集合店 直接放入      非集合店判断男女装




        // 处理奥莱卡的
        if(contains){
            // 获取是否是集合店
            //集合店
            List<BoxCustomerGuide> sales = cclientVipMapper.getSalesByBrandAndUnionId(olai,unionid);
            if(CollectionUtils.isNotEmpty(sales)){
                BoxCustomerGuide boxCustomerGuide = sales.get(0);
                Fashioner fashioner = fashionerRepository.findEnableSalsesByPhone(boxCustomerGuide.getMobile(),null,boxCustomerGuide.getHrId());
                if(ObjectUtils.isNotEmpty(fashioner)){
                    // 多品牌同一导购的情况
                    FashionerInfoEntity salesInfo = setSalesInfo(fashioner, boxCustomerGuide);

                    //集合店
                    CCollectionStore collectionStore =  cStoreMapper.selectArcUnionStoreAndNameByCStoreId(cStoreId);
                    //非集合店
                    if(ObjectUtils.isEmpty(collectionStore.getCUnionstoreId())){
                        // 看男女装
                        if(Arrays.asList(manBrand).contains(collectionStore.getName()) && expert == 10){
                            salesList.add(salesInfo);
                        }else if(Arrays.asList(womenBrand).contains(collectionStore.getName()) && expert == 20){
                            salesList.add(salesInfo);
                        }
                    }else{
                        salesList.add(salesInfo);
                    }
                }
            }


        }



        // 排序
        List<FashionerInfoEntity> result = salesList.stream()
                .collect(Collectors.toMap(FashionerInfoEntity::getId, a -> a, (o1,o2)-> {
                    if(!o1.getDescription().equals(o2.getDescription())){
                        o1.setDescription(o1.getDescription() + "、"+o2.getDescription());
                    }
                    o1.setDesc("提供"+o1.getDescription()+"品牌服务");
                    return o1;
                })).values().stream().collect(Collectors.toList());
        return result ;

    }

    /**
     * 获取搭配师导购
     * @param customerDetails
     * @return
     */
    private List<FashionerInfoEntity> getFashioerList(CustomerDetails customerDetails) {
        List<FashionerInfoEntity> list  = new ArrayList();

        String fashionId = customerDetails.getFashionerId();
        if(StringUtils.isNotEmpty(fashionId)){
            Fashioner fashioner = fashionerRepository.findById(fashionId);
            if (fashioner != null) {
                SysUser sysUser = sysUserRepository.findById(fashioner.getUserId());
                if(ObjectUtils.isNotEmpty(sysUser) && sysUser.getIsSales() == 0){
                    FashionerInfoEntity selectFashioner = setFashionerInfo(fashioner);
                    list.add(selectFashioner);
                }
            }
        }
        // 从视图中进行查询
        List<BoxCustomerGuide> guideList = cclientVipMapper.getSalesByUnionId(customerDetails.getPhone(),customerDetails.getUnionid());
        if(ObjectUtils.isNotEmpty(guideList)) {
            guideList.forEach(guide->{
                // 先校验c_customer_id是否是经销
                if (guide.getCCustomerId() != null && !guide.getCCustomerId().equals(176L)) {
                    // 经销用户再校验是否在限制品牌内
                    CVipType vipType = cVipTypeMapper.selectByPrimaryKey(guide.getCViptypeId());;
                    if (vipType != null && Arrays.asList(dealerBrand.split(",")).contains(vipType.getDescription())) {
                        CCustomer cCustomer = cCustomerMapper.selectByPrimaryKey(guide.getCCustomerId());
                        // 判断是否在经销开放区域内
                        if (!Arrays.asList(dealerOpenZone.split(",")).contains(cCustomer.getCode())) {
                            return;
                        }
                    }
                }
                Fashioner fashioner = fashionerRepository.findEnableSalsesByPhone(guide.getMobile(),null,guide.getHrId());
                if(ObjectUtils.isNotEmpty(fashioner)){
                    FashionerInfoEntity selectFashioner = setSalesInfo(fashioner, guide);
                    list.add(selectFashioner);
                }
            });
        }
        return list;
    }


    @Override
    public FashionerInfoEntity setSalesInfo(Fashioner fashioner, BoxCustomerGuide guide){
        FashionerInfoEntity selectFashioner = new FashionerInfoEntity();
        selectFashioner.setId( fashioner.getId());
        selectFashioner.setPhoto( guide.getBrandImgUrl());
        selectFashioner.setName( guide.getName());
        selectFashioner.setQrCode( guide.getQrCode());
        selectFashioner.setIsSale( 1);
        selectFashioner.setDescription( guide.getDescription());
        selectFashioner.setDesc( "提供"+guide.getDescription()+"品牌搭配服务");
        selectFashioner.setStoreId(fashioner.getcStoreId());
        return selectFashioner;
    }

    @Override
    public FashionerInfoEntity setFashionerInfo(Fashioner fashioner) {
        FashionerInfoEntity fashionerInfoEntity = new FashionerInfoEntity();
        fashionerInfoEntity.setId(fashioner.getId());
        fashionerInfoEntity.setPhoto(fashioner.getPhoto());
        fashionerInfoEntity.setWeChat(fashioner.getWechat());
        fashionerInfoEntity.setQrCode(fashioner.getWechatUrl());
        fashionerInfoEntity.setName(fashioner.getName());
        fashionerInfoEntity.setDesc("提供江南布衣旗下全品牌搭配服务");
        fashionerInfoEntity.setIsSale(0);
        fashionerInfoEntity.setStoreId(fashioner.getcStoreId());
        return fashionerInfoEntity;
    }


    @Override
    public List<FashionerInfoEntity> createSalesListInfo(List<Fashioner> fashioners){
        List<FashionerInfoEntity> list = new ArrayList<>();
        for (Fashioner fashioner : fashioners) {
            list.add(setFashionerInfo(fashioner));
        }
        return list;
    }
    
    private String getFirstImg() {
        List<AttrValue> list = attrValueMapper.findByAttrId("12");
        if(CollectionUtils.isNotEmpty(list)){
            return list.get(0).getImg();
        }
        return null;
    }

    //获取可选装
    private GenderAttrResp findGenderAttr(String unionId, CustomerDetails customerDetails, AskBoxDataNewReq askBoxDataNewReq) {
        GenderAttrResp genderAttrResp = new GenderAttrResp();
        List<AttrValueResp> woman = getTagsByGender(0);
        List<AttrValueResp> man = getTagsByGender(1);
        genderAttrResp.setChild(getTagsByGender(2));
        // 扫码进入
        if(askBoxDataNewReq.getIsScanEnter()){
            //  需要获取 当前的扫码进来的搭配师的服务性别
            Fashioner scanFashioner = fashionerRepository.findById(askBoxDataNewReq.getFashionerId());
            if(scanFashioner.getIsSales() == 0L){
                // 2 男女通用   0  女  1 男  擅长搭配 10-男装 20-女装 30-通用
                String expert = scanFashioner.getExpert();
                if(StringUtils.isNotBlank(expert)){
                    if(expert.equals("30")){

                        CustomerAskBox askBoxReturn = null;
                        if(StringUtils.isBlank(askBoxDataNewReq.getId())){
                            askBoxReturn = getLastAskBoxByUnionId(customerDetails.getUnionid());
                        }else{
                            askBoxReturn = getLastAskBoxByUnionId(customerDetails.getUnionid(), askBoxDataNewReq.getId());
                        }
                        if(askBoxReturn != null){
                            Short cusGender = askBoxReturn.getCusGender();
                            if(cusGender != null && cusGender.intValue() == 0){ //女
                                genderAttrResp.setSelectGender(2); //可选的性别 :2 男女通用   0  女  1 男
                                genderAttrResp.setWoman(woman);
                                genderAttrResp.setMan(man);
                                genderAttrResp.setDefaultStyle(0); //默认选中的性别 0女
                            }else if(cusGender != null && cusGender.intValue() == 1) { //1 男
                                genderAttrResp.setSelectGender(2);
                                genderAttrResp.setWoman(woman);
                                genderAttrResp.setMan(man);
                                genderAttrResp.setDefaultStyle(1);
                            }else {
                                genderAttrResp.setSelectGender(2);
                                genderAttrResp.setWoman(woman);
                                genderAttrResp.setMan(man);
                                genderAttrResp.setDefaultStyle(2);
                            }
                        }else{
                            genderAttrResp.setSelectGender(2);
                            genderAttrResp.setWoman(woman);
                            genderAttrResp.setMan(man);
                            genderAttrResp.setDefaultStyle(0);
                        }
                    }else if(expert.equals("20")){
                        genderAttrResp.setSelectGender(0);
                        genderAttrResp.setWoman(woman);
                        genderAttrResp.setMan(man);
                        genderAttrResp.setDefaultStyle(0);
                    }else{
                        genderAttrResp.setSelectGender(1);
                        genderAttrResp.setWoman(woman);
                        genderAttrResp.setMan(man);
                        genderAttrResp.setDefaultStyle(1);
                    }
                }else{
                    genderAttrResp.setSelectGender(0);
                    genderAttrResp.setWoman(woman);
                    genderAttrResp.setMan(man);
                    genderAttrResp.setDefaultStyle(0);
                }
            }else{
                // 导购需要获取门店所属的品牌店
                SysUser byId = sysUserRepository.findById(scanFashioner.getUserId());
                String cStoreId = byId.getcStoreId();
                if(StringUtils.isBlank(cStoreId)){
                    genderAttrResp.setSelectGender(0);
                    genderAttrResp.setWoman(woman);
                    genderAttrResp.setMan(man);
                    genderAttrResp.setDefaultStyle(0);
                }else{
                    // 查询导购门店所属品牌店
                    CStore cStore = cStoreMapper.selectByPrimaryKey(Long.parseLong(cStoreId));
                    // 获取上一次要盒
                    CustomerAskBox askBoxReturn = null;
                    if(StringUtils.isBlank(askBoxDataNewReq.getId())){
                        askBoxReturn = getLastAskBoxByUnionId(customerDetails.getUnionid());
                    }else{
                        askBoxReturn = getLastAskBoxByUnionId(customerDetails.getUnionid(), askBoxDataNewReq.getId());
                    }
                    if(cStore != null){
                        // 判断是否奥莱   Y 奥莱  N不是
                        String default05 = cStore.getDefault05();
                        if(StringUtils.isNotBlank(default05) && default05.equals("是")){
                            setGenderAttrResp(askBoxReturn,genderAttrResp,man,woman);
                        }else{
                            // 判断集合店
                            Long cUnionStoreId = cStore.getcUnionstoreId();
                            if(cUnionStoreId != null){
                                cStore = cStoreMapper.selectByPrimaryKey(cUnionStoreId);
                            }
                            log.info("当前门店为:{},查询出的品牌为:{},是否包含:{}",cStore.getId(), cStore.getcArcbrandId(), Arrays.asList(4L,12L).contains(cStore.getcArcbrandId()));
                            if(cStore.getcArcbrandId() != null){
                                // 对比品牌 JNBY LESS
                                if(Arrays.asList(2L,5L).contains(cStore.getcArcbrandId())){
                                    genderAttrResp.setSelectGender(0);
                                    genderAttrResp.setWoman(woman);
                                    genderAttrResp.setMan(man);
                                    genderAttrResp.setDefaultStyle(0);
                                }else if(Arrays.asList(3L,57L).contains(cStore.getcArcbrandId())){
                                    genderAttrResp.setSelectGender(1);
                                    genderAttrResp.setWoman(woman);
                                    genderAttrResp.setMan(man);
                                    genderAttrResp.setDefaultStyle(1);
                                }else if (Arrays.asList(4L,12L).contains(cStore.getcArcbrandId())){
                                    log.info("童装类目品牌店为:{}",cStore.getcArcbrandId());
                                    genderAttrResp.setSelectGender(3);
                                    genderAttrResp.setMan(man);
                                    genderAttrResp.setWoman(woman);
                                    genderAttrResp.setDefaultStyle(2);
                                }else{
                                    setGenderAttrResp(askBoxReturn,genderAttrResp,man,woman);
                                }
                            }else{
                                genderAttrResp.setSelectGender(2);
                                genderAttrResp.setWoman(woman);
                                genderAttrResp.setMan(man);
                                genderAttrResp.setDefaultStyle(0);
                            }
                        }
                    }else{
                        genderAttrResp.setSelectGender(2);
                        genderAttrResp.setWoman(woman);
                        genderAttrResp.setMan(man);
                        genderAttrResp.setDefaultStyle(0);
                    }
                }
            }
        }else{
            //获取可选装
            Integer selectGender = getSelectGender(customerDetails);
            genderAttrResp.setSelectGender(selectGender);

            if(genderAttrResp.getSelectGender() == null){
                selectGender = 0;
                genderAttrResp.setSelectGender(selectGender);
            }

            if(ObjectUtils.isNotEmpty(selectGender)){
                if(selectGender == 2){
                    genderAttrResp.setMan(man);
                    genderAttrResp.setWoman(woman);
                }else if(selectGender == 0){
                    genderAttrResp.setWoman(woman);
                }else if(selectGender == 1){
                    genderAttrResp.setMan(man);
                }
            }
            Integer def = defaultGender(customerDetails);
            // 男女都可选取默认，否则取可选
            genderAttrResp.setDefaultStyle(selectGender == 2? def:selectGender);
        }
        return genderAttrResp;
    }

    private void setGenderAttrResp(CustomerAskBox askBoxReturn, GenderAttrResp genderAttrResp,List man,List woman) {

        if(askBoxReturn != null){
            Short cusGender = askBoxReturn.getCusGender();
            if(cusGender.intValue() == 0){
                genderAttrResp.setSelectGender(2);
                genderAttrResp.setWoman(woman);
                genderAttrResp.setMan(man);
                genderAttrResp.setDefaultStyle(0);
            }else if(cusGender.intValue() == 1){
                genderAttrResp.setSelectGender(2);
                genderAttrResp.setWoman(woman);
                genderAttrResp.setMan(man);
                genderAttrResp.setDefaultStyle(1);
            } else {
                genderAttrResp.setSelectGender(3);
                genderAttrResp.setWoman(woman);
                genderAttrResp.setMan(man);
                genderAttrResp.setDefaultStyle(2);
            }
        }else{
            genderAttrResp.setSelectGender(2);
            genderAttrResp.setWoman(woman);
            genderAttrResp.setMan(man);
            genderAttrResp.setDefaultStyle(0);
        }


    }

    /**
     * 获取默认default
     * @param customerDetails
     * @return
     */
    private Integer defaultGender(CustomerDetails customerDetails) {
        List<CustomerAskBox> lastList = customerAskBoxRepository.findLastByUnionid(customerDetails.getUnionid());
        CustomerAskBox lastAsk = null;
        if(CollectionUtils.isNotEmpty(lastList)){
            lastAsk  = lastList.get(0);
        }

        // 最近一次要盒选择的性别
        CustomerAskBox finalLastAsk = lastAsk;
        Short gen = Optional.ofNullable(lastAsk)
                .map(gender -> finalLastAsk.getCusGender())
                .orElse(null);
        if(ObjectUtils.isNotEmpty(gen)){
            return gen.intValue();
        }
        // 问卷性别和用户性别同时更新。返回用户性别
        if(ObjectUtils.isNotEmpty(customerDetails.getGender())){
            return customerDetails.getGender().intValue();
        }
        // 最近一次购买的品牌
        if(ObjectUtils.isNotEmpty(customerDetails.getLastBuyBrand())){
            List<String> manBrandList = Arrays.asList(manBrand);
            if(manBrandList.contains(customerDetails.getLastBuyBrand())){
                return 1;
            }
            List<String> womenBrandList = Arrays.asList(womenBrand);
            if(womenBrandList.contains(customerDetails.getLastBuyBrand())){
                return 0;
            }
        }
        return 0;
    }

    /**
     * 根据性别获取主动要盒标签
     * @param gender
     * @return
     */
    private List<AttrValueResp> getTagsByGender(int gender) {
        List<AttrValueResp> list = attrValueMapper.findByParentIdAndGender(7,gender);
        return parseAttr(list);
    }

    /**
     *标签分组，排序
     * @param attrList
     * @return
     */
    private List<AttrValueResp> parseAttr(List<AttrValueResp> attrList) {
        // 分组
        Map<String, List<AttrValueResp>> map = attrList.stream().collect(Collectors.groupingBy(e -> e.getAttrName()));
        List<AttrValueResp> list = new ArrayList<>();
        Set<String> keys = map.keySet();
        for(String key : keys){
            AttrValueResp attr = new AttrValueResp();
            attr.setName(key);
            // 排序
            List<AttrValueResp> priority = map.get(key).stream().sorted((o1,o2) ->
                    (o2.getPriority() == null? 0 : o2.getPriority()) - (o1.getPriority() == null? 0 : o1.getPriority())).collect(Collectors.toList());
            attr.setAttrValue(priority);
            list.add(attr);
        }
        return list;

    }


    public Integer getSelectGender(CustomerDetails customerDetails){

        // 经销限制品牌
        String[] jxBrands = {"JNBY", "CROQUIS", "less", "APN73"};

        //获取用户的卡
        CclientVip cclientVip = new CclientVip();
        cclientVip.setUnionid(customerDetails.getUnionid());
        if(StringUtils.isNotBlank(customerDetails.getPhone())){
            cclientVip.setMobil(customerDetails.getPhone());
        }
        cclientVip.setIsactive("Y");
        List<CclientVip> cclientVips = cclientVipMapper.selectListByUnionIdOrMobli(cclientVip);

        if(CollectionUtils.isEmpty(cclientVips)){
            // 无卡，可全选
            return 2;
        }

        //是否有经销卡  默认没有经销卡
        boolean isHavejxCard = false;

        //是否有奥莱卡或者江南布衣+卡  如果有这两张卡的其中一张  默认全选
        boolean isHaveOlaiJNBY = false;

        // 用户不可服务品牌
        List<String>  unServiceBrandList = new ArrayList<>();

        // 默认女装
        int supportManOrWomen = 0;

        for (CclientVip vip : cclientVips) {
            //卡详情
            CVipType cVipType = cVipTypeMapper.selectByPrimaryKey(vip.getcViptypeId());
            //如果包含
            if(Arrays.asList(jnbyAndOlai.split(",")).contains(cVipType.getDescription())){
                if(olaiBrand.equals(cVipType.getDescription())){
                    // 如果是奥莱的品牌
                    List<BoxCustomerGuide> sales = cclientVipMapper.getSalesByBrandAndUnionId(olaiBrand,customerDetails.getUnionid());
                    if(CollectionUtils.isNotEmpty(sales)){
                        BoxCustomerGuide boxCustomerGuide = sales.get(0);
                        Fashioner fashioner = fashionerRepository.findEnableSalsesByPhone(boxCustomerGuide.getMobile(),null,boxCustomerGuide.getHrId());
                        if(ObjectUtils.isNotEmpty(fashioner)){
                            //集合店
                            CCollectionStore collectionStore =  cStoreMapper.selectArcUnionStoreAndNameByCStoreId(vip.getcStoreId());
                            //非集合店
                            if(ObjectUtils.isEmpty(collectionStore.getCUnionstoreId())){
                                // 看男女装
                                if(Arrays.asList(manBrand).contains(collectionStore.getName())){
                                    supportManOrWomen = 1;
                                }else if(Arrays.asList(womenBrand).contains(collectionStore.getName())){
                                    supportManOrWomen = 0;
                                }
                            }else{
                                return 2;
                            }
                        }
                    }
                }else{
                    //如果有江南布衣+
                    isHaveOlaiJNBY = true;
                    break;
                }
            }
            // 所有的均不包含
            if(vip.getcCustomerId()!= null && !vip.getcCustomerId().equals(176L)){
                // 经销卡
                if(!isHavejxCard) {
                    isHavejxCard = true;
                }

                if (cVipType != null && Arrays.asList(jxBrands).contains(cVipType.getDescription())) {
                    CCustomer cCustomer = cCustomerMapper.selectByPrimaryKey(vip.getcCustomerId());
                    // 判断是否在经销开放区域内
                    if (!Arrays.asList(dealerOpenZone.split(",")).contains(cCustomer.getCode())) {
                        unServiceBrandList.add(cVipType.getDescription());
                    }
                }
            }
        }

        //无经销卡可以全选
        if(!isHavejxCard){
            return 2;
        }

        //如果有一张是江南布衣卡或者奥莱卡 也可全选
        if(isHaveOlaiJNBY){
            return 2;
        }

        if(unServiceBrandList.size() == 0) {
            return 2;
        }
        return parseSelectGender(unServiceBrandList,supportManOrWomen);
    }

    /**
     * 根据不可服务品牌解析用户可选性别（只有男女品牌都为不可服务品牌，对应的性别才可不选）
     * @param umServiceBrandList
     * @param  supportBrand  已经支持的男装 或者女装
     * @return
     */
    public Integer parseSelectGender(List<String> umServiceBrandList,int supportBrand){
        Boolean man = false;
        Boolean women = false;

        if(supportBrand == 0){
            women = true;
        }
        if(supportBrand == 1){
            man = true;
        }

        for(String brand:manBrand){
            if(!umServiceBrandList.contains(brand)){
                man = true;
                break;
            }
        }
        for(String brand:womenBrand){
            if(!umServiceBrandList.contains(brand)){
                women = true;
                break;
            }
        }
        if(man && women){
            return 2;
        }else if(man){
            return 1;
        }else if(women){
            return 0;
        }
        return null;
    }


    /**
     *主动要盒子 经销 效验
     * @param unionid
     * @return
     */
    private boolean checkCustomerAskBoxJx(String unionid) {

        //应该过滤的品牌
        String[] filterBrand = {"CROQUIS","APN73","JNBY","less"};

        // 经销限制品牌
        String[] jxLimitBrand = {"CROQUIS","APN73","JNBY","less"};

        CustomerDetails customerDetails = customerDetailsRepository.findByUnionId(unionid);

        if (ObjectUtils.isEmpty(customerDetails)) {
            // 新用户不限制
            return false;
        } else if (ObjectUtils.isNotEmpty(customerDetails.getRegBrand())) {
            // 品牌用户不限制
            return false;
        } else {
            int cardJXNum = 0;
            CclientVip cclientVip = new CclientVip();
            cclientVip.setUnionid(customerDetails.getUnionid());
            if(StringUtils.isNotBlank(customerDetails.getPhone())){
                cclientVip.setMobil(customerDetails.getPhone());
            }
            cclientVip.setIsactive("Y");
            List<CclientVip> cclientVips = cclientVipMapper.selectListByUnionIdOrMobli(cclientVip);

            for (CclientVip vip : cclientVips) {
                // 排除非男女装,江南布衣+,奥来 卡
                CVipType cVipType = cVipTypeMapper.selectByPrimaryKey(vip.getcViptypeId());

                //判断江南布衣+和奥莱卡 是否是直营
                if(cVipType != null && Arrays.asList(jnbyAndOlai.split(",")).contains(cVipType.getDescription())){
                    if(ObjectUtils.isEmpty(vip.getcCustomerId()) || vip.getcCustomerId().equals(176L)){
                        return false;
                    }
                }

                if(cVipType != null && !Arrays.asList(filterBrand).contains(cVipType.getDescription())){
                    continue;
                }
                //
                if(ObjectUtils.isEmpty(vip.getcCustomerId()) || vip.getcCustomerId().equals(176L)){
                    return false;
                }
                // 经销用户再校验是否在限制品牌内

                if (cVipType != null && Arrays.asList(jxLimitBrand).contains(cVipType.getDescription())) {
                    CCustomer cCustomer = cCustomerMapper.selectByPrimaryKey(vip.getcCustomerId());
                    // 判断是否在经销开放区域内
                    if (!Arrays.asList(dealerOpenZone.split(",")).contains(cCustomer.getCode())) {
                        cardJXNum ++;
                    }
                }
            }

            // 6张男女品牌卡,江南布衣+ , 奥莱 都是非开放区域的经销卡
            if(cardJXNum == filterBrand.length){
                return true;
            }
            return false;
        }
    }


    @Override
    public ResponseResult validDataBeforeAddShopCart(String unionId) {
        CustomerDetails customerDetails = customerDetailsRepository.findByUnionId(unionId);
        if(ObjectUtils.isEmpty(customerDetails)){
            return ResponseResult.error(1,"用户未注册!");
        }

        //是否是黑名单
        Boolean isBlack = customerDetailsService.checkUserIsBlack(customerDetails.getId(), null);
        if(isBlack){
            return ResponseResult.error(6,"您的会员暂时无法享受BOX服务，如有疑问请联系客服！");
        }

        //查询是否有未完结的盒子
        List<BoxWithBLOBs> onGoingBoxByUnionId = boxRepository.findOnGoingBoxByUnionId(unionId);
        if(CollectionUtils.isNotEmpty(onGoingBoxByUnionId)){
            return ResponseResult.error(5,"您还有未完结的定制盒子，暂时无法要新的盒子哦！");
        }

        //查询是否有未完结的要盒申请
        CustomerAskBox customerAskBox = new CustomerAskBox();
        customerAskBox.setUnionid(unionId);
        customerAskBox.setStatus(CustomerAskBoxStatusEnum.READY.getCode().shortValue());
        List<CustomerAskBox> unFinishAskBox = customerAskBoxRepository.selectListBySelective(customerAskBox);
        if(CollectionUtils.isNotEmpty(unFinishAskBox)){
            return ResponseResult.error(5,"您还有未完结的定制盒子，暂时无法要新的盒子哦！");
        }

        //是否限制主动要盒
        if(StringUtils.isNotBlank(customerDetails.getCategoryId())){
            CustomerCategory customerCategory = customerCategoryMapper.selectByPrimaryKey(customerDetails.getCategoryId());
            if(ObjectUtils.isNotEmpty(customerCategory)
                    && ObjectUtils.isNotEmpty(customerCategory.getAskBox())
                    && LIMIT_ASK_BOX == customerCategory.getAskBox()){
                return ResponseResult.error(6,"您的会员暂时无法享受BOX服务，如有疑问请联系客服！");
            }
        }

//        BCustomerInformation bCustomerInformation = bCustomerInformationMapper.selectById(customerDetails.getId());
//        if(Objects.nonNull(bCustomerInformation) && bCustomerInformation.getbCategoryId() == 57L){
//            return ResponseResult.error(6,"您的会员暂时无法享受BOX服务，如有疑问请联系客服！");
//        }
//
//        if (StringUtils.isNotBlank(customerDetails.getCategoryId())) {
//            CustomerCategory customerCategory = categoryMapper.selectByPrimaryKey(customerDetails.getCategoryId());
//            if (customerCategory.getName().equals("违约顾客") || customerCategory.getName().equals("黑名单")) {
//                return ResponseResult.error(6,"您的会员暂时无法享受BOX服务，如有疑问请联系客服！");
//            }
//        }

//        //是否使用经销卡，并且经销商未开通box服务
//        if(checkCustomerAskBoxJx(customerDetails.getUnionid())){
//            return ResponseResult.error(6,"您暂时无法发起要盒子，如有异议请联系客服！");
//        }

        //
        if(ObjectUtils.isEmpty(customerDetails) || (customerDetails.getSubExpireTime() == null)){
            return ResponseResult.error(-1,"用户未订阅！");
        }  else if (customerDetails.getSubExpireTime().before(new Date())||"1".equals(customerDetails.getEndtype())) {
            return ResponseResult.error(-1,"订阅已过期！");
        }
        return ResponseResult.success();

    }


    @Override
    public void importExcel(String filePath, String keys, String createName) {
        // 获取批量手机号
        List<String> titleList = new ArrayList<>();
        titleList.add("phone");  // 名称
        //返回数据
//        RedisTemplateUtil.setex(redisPoolUtil, keys, "true", 60);
        List<Map<String,Object>> importData = fileParseService.readExcelByUrl(titleList,filePath);
        Set<String> phoneSet = new HashSet<>();
        //限制
        for (Map<String, Object> importDatum : importData) {
            Object phone = importDatum.get("phone");
            if(phone!= null && phone.toString().length() == 11 && phone.toString().startsWith("1")){
                phoneSet.add(phone.toString());
            }
        }

        for (String phone : phoneSet) {
            try {
                checkAndCreateAskBox(phone,null,createName);
            }catch (Exception e){
                logger.error("e=",e);
            }
        }


    }

    /**
     * 校验和创建要盒s
     * @param phone
     * @param bCusServiceAutoAskBoxId  如果有值 则是更新
     */
    @Override
    public void checkAndCreateAskBox(String phone,String  bCusServiceAutoAskBoxId,String createName) {
        String createBy = "";
        if(StringUtils.isNotBlank(createName)){
            createBy = createName;
        }else{
            createBy = userInfoService.getUserInfo() == null ? null : userInfoService.getUserInfo().getRealname();
        }

        //表信息
        BCusServiceAutoAskBox bCusServiceAutoAskBox = new BCusServiceAutoAskBox();
        bCusServiceAutoAskBox.setPhone(phone);
        bCusServiceAutoAskBox.setCreateTime(new Date());
        bCusServiceAutoAskBox.setUpdateTime(new Date());
        bCusServiceAutoAskBox.setId(idLeaf.getId());
        bCusServiceAutoAskBox.setStatus(BCusServiceAutoAskBoxStatusEnum.ON_LOADING.getCode());
        bCusServiceAutoAskBox.setCreateBy(createBy);
        bCusServiceAutoAskBox.setIsDel(0);
        //1. 校验是否已经注册
        CustomerDetails customerDetails = new CustomerDetails();
        customerDetails.setPhone(phone);
        List<CustomerDetails> customers = customerDetailsMapper.selectListBySelective(customerDetails);
        if(CollectionUtils.isEmpty(customers)){
            bCusServiceAutoAskBox.setStatus(BCusServiceAutoAskBoxStatusEnum.CANCEL.getCode());
            bCusServiceAutoAskBox.setRemark("用户未注册");
        }else{
            //2. 校验是否有绑定的搭配师
            String fashionerId = customers.get(0).getFashionerId();
            if(StringUtils.isNotBlank(fashionerId) && !fashionerId.equals("0")){
                Fashioner byId = fashionerRepository.findById(fashionerId);
                if(byId.getIsSales() == 1L){
                    bCusServiceAutoAskBox.setStatus(BCusServiceAutoAskBoxStatusEnum.UN_FINISH.getCode());
                }else{
                    bCusServiceAutoAskBox.setFashionerId(byId.getId());
                    bCusServiceAutoAskBox.setFashionerName(byId.getName());
                }
            }else{
                bCusServiceAutoAskBox.setStatus(BCusServiceAutoAskBoxStatusEnum.UN_FINISH.getCode());
            }
        }

        //2. 校验工单
        SaveBoxAskReq saveBoxAskReq = new SaveBoxAskReq();
        if(StringUtils.isNotBlank(bCusServiceAutoAskBox.getFashionerId())){

            saveBoxAskReq.setId(idLeaf.getId());
            saveBoxAskReq.setUnionid(customers.get(0).getUnionid());
            saveBoxAskReq.setChannelId(CustomerAskBoxChannelEnum.CUSTOMER_SERVICE_ASK_BOX.getCode());
            saveBoxAskReq.setCreateFasId(bCusServiceAutoAskBox.getFashionerId());

            ResponseResult responseResult = validData2(saveBoxAskReq);
            if(responseResult.getCode() == -1 || responseResult.getCode() == 0){
                //均可以进行创建盒子
                //4. 校验权益状态  如果用户没有权益了，先免费订阅
                if(responseResult.getCode() == -1){
                    //免费订阅
                    SubscribeByFreeReq subscribeByFreeReq = new SubscribeByFreeReq();
                    subscribeByFreeReq.setChannelId("4");
                    subscribeByFreeReq.setFashionerId(bCusServiceAutoAskBox.getFashionerId());
                    subscribeByFreeReq.setUnionId(customers.get(0).getUnionid());
//                    iSubscribeByFreeService.subscribeByFree(subscribeByFreeReq);
                    bCusServiceAutoAskBox.setRemark("已自动开通搭配师权益");
                }

                CustomerAskBox params = new CustomerAskBox();
                params.setUnionid(customers.get(0).getUnionid());
                params.setStatus(CustomerAskBoxStatusEnum.READY.getCode().shortValue());
                List<CustomerAskBox> customerAskBoxes = customerAskBoxRepository.selectListBySelective(params);
                if(CollectionUtils.isNotEmpty(customerAskBoxes)){
                    bCusServiceAutoAskBox.setStatus(BCusServiceAutoAskBoxStatusEnum.CANCEL.getCode());
                    bCusServiceAutoAskBox.setRemark("有进行中的盒子!");
                }else{
                    bCusServiceAutoAskBox.setStatus(BCusServiceAutoAskBoxStatusEnum.CREATED.getCode());
                }
                //封装数据  进行一步要盒
            }else{
                if(responseResult.getCode() == 5){
                    // 有盒子
                    bCusServiceAutoAskBox.setStatus(BCusServiceAutoAskBoxStatusEnum.CANCEL.getCode());
                    bCusServiceAutoAskBox.setRemark("有进行中的盒子!");
                }else if(responseResult.getCode() == 6){
                    // 黑名单
                    bCusServiceAutoAskBox.setStatus(BCusServiceAutoAskBoxStatusEnum.CANCEL.getCode());
                    bCusServiceAutoAskBox.setRemark("用户为黑名单!");
                }
            }
        }



        // 保存数据
        template.execute(action->{

            if(bCusServiceAutoAskBox.getStatus().equals(BCusServiceAutoAskBoxStatusEnum.CREATED.getCode())){
                updateSaveBoxAskReqData(saveBoxAskReq);
                oneStepSaveAskBox(saveBoxAskReq,null);
            }
            if(null == bCusServiceAutoAskBoxId){
                bCusServiceAutoAskBoxMapper.insertSelective(bCusServiceAutoAskBox);
            }else{
                //更新
                BCusServiceAutoAskBox old = bCusServiceAutoAskBoxMapper.selectByPrimaryKey(bCusServiceAutoAskBoxId);
                BCusServiceAutoAskBox update = new BCusServiceAutoAskBox();
                update.setId(old.getId());
                update.setRemark(bCusServiceAutoAskBox.getRemark());
                update.setUpdateTime(new Date());
                update.setStatus(bCusServiceAutoAskBox.getStatus());
                update.setFashionerId(bCusServiceAutoAskBox.getFashionerId());
                update.setCreateBy(bCusServiceAutoAskBox.getCreateBy());
                update.setFashionerName(bCusServiceAutoAskBox.getFashionerName());
                bCusServiceAutoAskBoxMapper.updateByPrimaryKeySelective(update);
            }

            return action;
        });

    }
}
