package com.jnby.base.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.application.minapp.dto.request.YdHistorySearchReq;
import com.jnby.application.minapp.dto.response.YdHistorySearchResp;
import com.jnby.base.repository.ISearchRecordRepository;
import com.jnby.base.service.ISearchRecordService;
import com.jnby.common.Page;
import com.jnby.common.enums.SearchRecordTypeEnum;
import com.jnby.common.leaf.IdLeafService;
import com.jnby.infrastructure.box.model.SearchRecord;
import com.jnby.module.order.entity.XcxBoxListEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/7
 */
@Service
public class SearchRecordServiceImpl implements ISearchRecordService {

    @Autowired
    private ISearchRecordRepository iSearchRecordRepository;

    @Autowired
    private IdLeafService idLeaf;
    @Override
    public void insert(SearchRecord searchRecord) {
        List<SearchRecord> records = iSearchRecordRepository.findByRecord(searchRecord);
        if(records != null && records.size() > 0){
            SearchRecord record = records.get(0);
            record.setIsDel((short)0);
            record.setSearchNum((short)(record.getSearchNum()+1));
            iSearchRecordRepository.update(record);
        }else{
            searchRecord.setId(idLeaf.getId());
            searchRecord.setIsDel((short)0);
            searchRecord.setSearchNum((short)1);
            iSearchRecordRepository.insert(searchRecord);
        }

    }

    @Override
    public List<SearchRecord> findList(SearchRecord searchRecord, SearchRecordTypeEnum searchRecordTypeEnum) {
        List<SearchRecord> records = iSearchRecordRepository.findByRecord(searchRecord);
        if(records != null && records.size() > searchRecordTypeEnum.getMax()){
            List<SearchRecord> searchRecords = records.subList(0, searchRecordTypeEnum.getMax());
            return searchRecords;
        }
        return records;
    }

    @Override
    public void del(SearchRecord searchRecord) {
        iSearchRecordRepository.del(searchRecord);
    }

    @Override
    public List<SearchRecord> ydHistorySearch(YdHistorySearchReq req, Page page) {
        List<SearchRecord> response  = new ArrayList<>();
        if(StringUtils.isBlank(req.getUnionId())){
            return response;
        }
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        SearchRecord param = new SearchRecord();
        param.setType(SearchRecordTypeEnum.THEME.getCode().shortValue());
        param.setUnionid(req.getUnionId());
        List<SearchRecord> byRecord = iSearchRecordRepository.findByRecord(param);
        PageInfo<SearchRecord> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }
}

