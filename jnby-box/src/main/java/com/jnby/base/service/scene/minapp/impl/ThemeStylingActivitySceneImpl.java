package com.jnby.base.service.scene.minapp.impl;

import com.jnby.base.service.IBWxMaterialService;
import com.jnby.base.service.scene.minapp.IMinappSceneOperation;
import com.jnby.base.service.scene.mp.IMpSceneReplyMsg;
import com.jnby.common.enums.SceneTypeEnum;
import com.jnby.config.WxMaProperties;
import com.jnby.infrastructure.box.model.BWxMaterial;
import com.jnby.infrastructure.box.model.Scene;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.builder.kefu.MiniProgramPageBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class ThemeStylingActivitySceneImpl implements IMinappSceneOperation {

    @Override
    public SceneTypeEnum getType() {
        return SceneTypeEnum.THEME_ACTIVTIY;
    }

    //theme_activity_id=3141524571372806144&theme_match_id=3141728238722039808
    @Override
    public Map apply(String unionId, Map paramsMap) {
        Map map = new HashMap();
        map.put("type", getType().getCode());
        map.put("theme_activity_id", paramsMap.get("theme_activity_id"));
        return map;
    }
}
