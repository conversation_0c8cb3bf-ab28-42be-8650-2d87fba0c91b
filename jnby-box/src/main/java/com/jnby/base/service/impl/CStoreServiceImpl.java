package com.jnby.base.service.impl;

import com.jnby.base.repository.ICStoreRepository;
import com.jnby.base.repository.IEmployeeBaseRepository;
import com.jnby.base.service.ICStoreService;
import com.jnby.common.util.StrUtil;
import com.jnby.infrastructure.bojun.mapper.CStoreMapper;
import com.jnby.infrastructure.bojun.model.CStore;
import com.jnby.infrastructure.bojun.model.EmployeeBase;
import com.jnby.infrastructure.box.model.SysStore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/16
 */
@Service
public class CStoreServiceImpl implements ICStoreService{

    @Autowired
    private ICStoreRepository icStoreRepository;

    @Autowired
    private IEmployeeBaseRepository iEmployeeBaseRepository;

    @Autowired
    private CStoreMapper cStoreMapper;


    @Override
    public List<Integer> findStoreIdsByLinkId(String linkId) {
        EmployeeBase employee = iEmployeeBaseRepository.findByLinkId(linkId);
        if(employee != null && employee.getPosition().endsWith("店长")
                && StringUtils.isNotBlank(employee.getLinkstore())){
            // 是否集合店铺
            CStore para1 = new CStore();
            para1.setCode(employee.getLinkstore());
            List<CStore> cStores = icStoreRepository.findCstoreListBySelective(para1);
            if(cStores != null && cStores.size() >0 ){
                List<Integer> storeIds =  new ArrayList<>();
                if(cStores.get(0).getcUnionstoreId()== null){
                    // 品牌店
                    storeIds.add(cStores.get(0).getId().intValue());
                }else{
                    // 集合店
                    CStore para2 = new CStore();
                    para2.setcUnionstoreId(cStores.get(0).getcUnionstoreId());
                    List<CStore> cStoreList = icStoreRepository.findCstoreListBySelective(para2);
                    cStoreList.forEach(e -> {
                        storeIds.add(e.getId().intValue());
                    });
                }
                return storeIds;
            }

        }
        return null;
    }

    @Override
    public SysStore findSysStoreById(String id) {
        return icStoreRepository.findSysStoreById(id);
    }

    @Override
    public CStore findCStoreById(Long id) {
        return icStoreRepository.findCstoreById(id);
    }

    @Override
    public List<CStore> findCstoreListBySelective(CStore store) {
        return icStoreRepository.findCstoreListBySelective(store);
    }


    @Override
    public List<CStore> getAllStoreByStoreId(Long id) {
        CStore cStore = icStoreRepository.findCstoreById(id);
        if(cStore.getcUnionstoreId() != null){
            CStore para = new CStore();
            para.setcUnionstoreId(cStore.getcUnionstoreId());
            return icStoreRepository.findCstoreListBySelective(para);
        }
        List<CStore> resp = new ArrayList<>();
        resp.add(cStore);
        return resp;
    }

    @Override
    public List<CStore> selectByIds(List<String> storeId) {
        List<Long> ids  = new ArrayList<>();
        for (String id : storeId) {
            ids.add(Long.parseLong(id));
        }
        return icStoreRepository.selectCStoreByIds(ids);
    }

    @Override
    public List<Long> getStoreByStroreIds(List<String> storeIdsList) {
        List<Long> storeIds = new ArrayList<>();
        if(storeIdsList.size() > 900){
            List<List<String>> subLists = StrUtil.getSubLists(storeIdsList,900);
            for (List<String> subList : subLists) {
                Set<Long> collect = cStoreMapper.selectListByCodes(subList).stream().map(r -> r.getId()).collect(Collectors.toSet());
                storeIds.addAll(new ArrayList<>(collect));
            }
        }else{
            Set<Long> collect = cStoreMapper.selectListByCodes(storeIdsList).stream().map(r -> r.getId()).collect(Collectors.toSet());
            storeIds.addAll(new ArrayList<>(collect));
        }
        return storeIds;
    }
}


