package com.jnby.base.repository.impl;

import com.jnby.base.entity.CustomerAskLogEntity;
import com.jnby.base.repository.ICustomerAskBoxLogRepository;
import com.jnby.infrastructure.box.mapper.CustomerAskBoxLogMapper;
import com.jnby.infrastructure.box.model.CustomerAskBoxLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class CustomerAskBoxLogRepositoryImpl implements ICustomerAskBoxLogRepository {
    @Autowired
    private CustomerAskBoxLogMapper customerAskBoxLogMapper;

    @Override
    public void insert(CustomerAskBoxLog log) {
        customerAskBoxLogMapper.insertSelective(log);
    }

    @Override
    public List<CustomerAskLogEntity> batchFindCancelReason(List<String> cancelIds) {
        return customerAskBoxLogMapper.batchFindCancelReason(cancelIds);
    }
}
