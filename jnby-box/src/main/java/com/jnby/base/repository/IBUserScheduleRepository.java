package com.jnby.base.repository;

import com.jnby.common.Page;
import com.jnby.infrastructure.box.model.BUserSchedule;
import com.jnby.infrastructure.box.model.WeiXinFans;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/8/20
 */
public interface IBUserScheduleRepository {

    /**
     * 修改逻辑为只展示0未开始，2待服务，3待搭配的订阅服务计划
     * @param customerId
     * @return
     */
    List<BUserSchedule> querySubscriptionPlanForNewPlan(String customerId);

    //查询需要取消的订阅服务计划
    void selectForNewCancel(String customerId, String customerAskBoxId);

    void updateForUseOrHaveIn(String customerId, String boxId,String formatNow);

    //工单状态为待服务状态下）②用户自主发起主动要盒申请； 2.5 已占用
    void  updateForServiceOn(String customerId, String customerAskBoxId,String formatNow);

    //加一层逻辑，取消后又设置9月份，执行接口，取消就是取消本月了
    int selectForStatusFlag(String customerId);


    List<BUserSchedule> selectRenewMonthFor(String customerId, String date);

    /*查询是否有续订自动取消的订阅计划*/
    List<BUserSchedule> selectRenewByUserId(String customerId, String date);

    //分页查询订阅计划的待服务用户
    List<BUserSchedule> selectUserByPage(Page page,List<String> customerIds);

    //将用户的订阅服务计划状态改为6，续订以后自动删除标识
    int updateBatchForRenew(List<String> scheduleIds);

    //通过主键id更新订阅计划服务状态为待服务2
    int updateStatusForOn(String id);

    //批量取消订阅计划
    int updateBatchForCancel(List<String> scheduleIds);

    //查询需要取消的订阅计划
    List<BUserSchedule> selectPlanForCancel(String customerId, String date);

    int updatePlanForAwakeById(String id);

    int deleteByPrimaryKey(String id);

    int insert(BUserSchedule record);

    int insertSelective(BUserSchedule record);

    BUserSchedule selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(BUserSchedule record);

    int updateByPrimaryKey(BUserSchedule record);

    //查询首次订阅的的用户订阅计划
    List<BUserSchedule> selectCustomerByCustomerId(String customerId);

    //查询首次订阅的的用户订阅计划
    int selectCountForCustomerId(String customerId);

    //查询清空订阅计划记录的订阅计划
    List<BUserSchedule> selectCustomerByCustomerIdForDel(String customerId);
    //批量插入订阅服务计划
    int batchInsertSchedule(List<BUserSchedule> bUserScheduleList);
    //批量更新订阅服务计划
    int updateSchedulePlan(List<BUserSchedule> bUserScheduleList);
    //批量删除订阅服务计划
    int deleteBatchSchedule(List<String> scheduleIds);
    //查询用户的订阅服务计划
    List<BUserSchedule> querySubscriptionPlanByCustomerId(String customerId);

    //查询是否有工单号
    List<BUserSchedule> selectBoxServiceOrElse(String customerId, String date);

    //判断顾客是否已取消了当前订阅月份
    List<BUserSchedule> selectCustomerIdCancelOrElse(String customerId, String date);

    List<BUserSchedule> selectCustomerForOn(String subscriptionMonth,List<String> customerIds);

    /**
     * 查询当前月份is_del=0有记录的且状态为待服务拿出所有的customerId进行推送
     * @return
     */
    List<BUserSchedule> findAllByCustomerIdAndDate(List<String> customerIds);


    /**
     * 批量保存订阅服务计划
     * @param bUserSchedules1
     */
    void insertBatchSelective(List<BUserSchedule> bUserSchedules1);

    List<BUserSchedule> selectTestForMyLife(String customerId);
}
