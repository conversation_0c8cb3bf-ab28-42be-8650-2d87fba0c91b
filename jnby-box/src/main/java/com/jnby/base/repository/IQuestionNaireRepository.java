package com.jnby.base.repository;

import com.jnby.infrastructure.box.model.*;
import com.jnby.module.jic.entity.RulesList;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface IQuestionNaireRepository {
    /**
     * 通过性别获取问卷
     *
     * @param gender
     * @return
     */
    CustomerSurvey getValidPaper(int gender);

    /**
     * 问卷ID获取问卷页面
     *
     * @param surveyId
     * @return
     */
    List<CustomerPage> getPageBySurveyId(String surveyId);


    List<CustomerPage> getPageBySurveyIdForSecond(String surveyId,Integer styleFlag);

    /**
     * 通过页面ID批量获取试题
     *
     * @param pageIds
     * @return
     */
    List<CustomerQuestion> getQuestionsByPageIds(List<String> pageIds);

    /**
     * 统计用户答了几道题
     *
     * @param unionid
     * @param surveyId
     * @return
     */
    int countCustomerAnswers(String unionid, String surveyId);


    /**
     * 页面对应的试卷
     *
     * @param pageId
     * @param unionId
     * @return
     */
    List<CustomerQuestion> getQuestionByPageId(String pageId, String unionId);


    /**
     * 通过pageId获取问卷问题列表
     *
     * @param pageId
     * @return
     */
    List<CustomerQuestion> queryQuestionByPageId(String pageId);


    /**
     * 查询用户对于某个问卷下的某个问题的回答
     *
     * @param unionId
     * @param questionId
     * @return
     */
    CustomerAnswer getAnswer(String unionId, String questionId);

    /**
     * 批量插入答案
     * @param answerMap
     * @param unionId
     * @param pageId
     */
    void createBatchAnswer(Map<String, String> answerMap, String unionId, String pageId);

    int selectCountForQuestion(String surveyId,Integer styleFlag);

    int selectCountForAnswer(String surveyId,Integer styleFlag,String unionId,int countQuestion);

    int selectCountForQuestionForAll(String surveyId);

    int selectCountForAnswerForAll(String surveyId,String unionId,int countAll);

    /**
     * 查询风格问卷的奖卷情况
     * @return
     */
    List<RulesList> findQuestionPageAward();

    HashMap<String,Object> chooseAward(List<CouponSetting> subscriptionPlanAward);


}
