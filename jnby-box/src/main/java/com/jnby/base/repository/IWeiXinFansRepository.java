package com.jnby.base.repository;

import com.jnby.infrastructure.box.model.Task;
import com.jnby.infrastructure.box.model.WeiXinFans;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @date 2021/8/20
 */
public interface IWeiXinFansRepository {

    /**
     * 根据unionId查询关注过公众号的用户
     * @param unionId
     * @return
     */
    WeiXinFans findWeiXinFans(String unionId);

    WeiXinFans findWeiXinFansByOpenId(String openId);
}



