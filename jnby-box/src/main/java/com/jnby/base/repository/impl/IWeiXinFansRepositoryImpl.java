package com.jnby.base.repository.impl;

import com.jnby.base.repository.IWeiXinFansRepository;
import com.jnby.infrastructure.box.mapper.WeiXinFansMapper;
import com.jnby.infrastructure.box.model.WeiXinFans;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/8/20
 */
@Repository
public class IWeiXinFansRepositoryImpl implements IWeiXinFansRepository {

    @Autowired
    private WeiXinFansMapper weiXinFansMapper;


    @Override
    public WeiXinFans findWeiXinFans(String unionId) {

        //根据unionId查询关注过公众号的用户
        List<WeiXinFans> weiXinFans = weiXinFansMapper.findWeiXinFans(unionId);
        return weiXinFans.stream().findFirst().orElse(null);

    }

    @Override
    public WeiXinFans findWeiXinFansByOpenId(String openId) {
        List<WeiXinFans> byOpenid = weiXinFansMapper.findByOpenid(openId);
        return byOpenid.stream().findFirst().orElse(null);
    }
}



