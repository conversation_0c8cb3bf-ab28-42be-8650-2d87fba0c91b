package com.jnby.base.repository;

import com.jnby.infrastructure.box.model.Fashioner;
import com.jnby.infrastructure.box.model.FashionerWithBLOBs;
import com.jnby.infrastructure.box.model.SysUser;

import java.util.List;

/**
 * 搭配师-导购资源层
 * <AUTHOR>
 * @version 1.0
 * @date 3/9/21 2:40 PM
 */
public interface IFashionerRepository {

    /**
     * 通过ID获取详情
     * @param fashionerId
     * @return
     */
    Fashioner findById(String fashionerId);

    /**
     * 根据门店id查询所有导购id
     * @param storeIds
     * @return
     */
    List<String> findIdsByStore(List<Integer> storeIds);


    List<Fashioner> findHeadquartersFashions();


    List<FashionerWithBLOBs> findByRecords(Fashioner fashioner);

    Fashioner findByUserId(String userId);

    List<Fashioner> getFashionerByType(Integer type);

    List<Fashioner> selectFashionerByExpert(Integer expert);


    List<String> findIdsByStoreIds(List<String> storeIds);

    List<Fashioner> getByIds(List<String> fashionIds);

    String selectFashionerBySelective(SysUser sysUser);

    List<FashionerWithBLOBs> findByFashionerName(String name);

    /**
     * 通过角色ID获取系统账号信息
     * @param fashionerId
     * @return
     */
    SysUser findSysUserByFashionerId(String fashionerId);

    Fashioner findEnableByPhone(String mobile);

    List<Fashioner> findByExpert(int expert);

    Integer fashionerOrSales(String fashionerId);

    Fashioner findEnableSalsesByPhone(String mobile,Integer expert,String hrId);

    List<FashionerWithBLOBs> selectListBySelective(FashionerWithBLOBs fashioner);

    void createFashioner(FashionerWithBLOBs fashioner);

    void updateFashionerById(FashionerWithBLOBs fashioner);

    List<String> findByUserIds(List<String> userIds);

    List<FashionerWithBLOBs> selectListBySelectiveAndFashionerTypeIds(FashionerWithBLOBs fashioner,List<Long> fashionerTypeIds);
}
