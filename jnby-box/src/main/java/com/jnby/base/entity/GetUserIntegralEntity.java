package com.jnby.base.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value = "会员积分", description = "会员积分对象")
@Data
public class GetUserIntegralEntity implements Serializable {
    private static final long serialVersionUID = 2146119533484073396L;
    @ApiModelProperty("总积分")
    private Long totalIntegral;
    @ApiModelProperty("即将失效积分")
    private Long willExpireIntegral;
    @ApiModelProperty("积分失效时间")
    private String willExpireDate;

}
