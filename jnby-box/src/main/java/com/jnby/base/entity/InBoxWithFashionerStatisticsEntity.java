package com.jnby.base.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;



/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InBoxWithFashionerStatisticsEntity {
    private String fashionerId;

    private String fashionerName;

    private String linkId;

    private Integer waitSendCount;

    private Integer waitBuyCount;

    private Integer waitStorageCount;

    private Long isResign;
}
