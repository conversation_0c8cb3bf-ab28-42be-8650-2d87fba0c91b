package com.jnby.base.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "SubtractUserIntegralEntity", description = "加减积分返回值")
public class SubtractUserIntegralEntity {

    @ApiModelProperty("返回代码 Y成功 N失败")
    private String result;
    @ApiModelProperty("返回信息")
    private String resultMsg;
}
