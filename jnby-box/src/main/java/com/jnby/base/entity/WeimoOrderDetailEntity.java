package com.jnby.base.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class WeimoOrderDetailEntity {
    /**
     * 小程序appid
     */
    private String appid;

    /**
     * 买家信息
     */
    private BuyerInfo buyerInfo;

    /**
     * 买家备注
     */
    private String buyerRemark;

    /**
     * 渠道类型（ 0-公众号；1-小程序；2-H5；3-QQ；4-微博；5-字节跳动小程序；6-支付宝小程序；7-PC后台；8-安卓app；9-苹果app；10-百度智能小程序；11-PAD；12-自有APP；13-微信小程序webview；14-微信小程序webview-直播；15-大屏扫码；16-企业微信；101-分销供货商订单）
     */
    private Integer channelType;


    /**
     * 订单渠道类型名称
     */
    private String channelTypeName;

    /**
     *
     */
    private String createTime;

    /**
     * 配送详情
     */
    private DeliveryDetail deliveryDetail;

    /**
     * 运费实付金额
     */
    private BigDecimal deliveryPaymentAmount;

    /**
     * 折扣信息
     */
    private DiscountInfo discountInfo;

    /**
     * 是否可发货
     */
    private Integer enableDelivery;

    /**
     * 商品销售模式：1，普通模式，2，预售模式
     */
    private Integer goodsSellMode;

    /**
     * 导购信息
     */
    private GuideInfo guideInfo;


    /**
     * 商品列表
     */
    private List<Item> itemList;


    /**
     * 会员信息
     */
    private MemberDetailInfoVo memberDetailInfoVo;


    /**
     * 商家信息
     */
    private MerchantInfo merchantInfo;


    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 订单状态
     * 真实订单状态。支持的状态类型包括：0-创建；1-部分支付；2-已支付；3-待发货；4-部分发货；5-已发货；7-确认收货；8-完成；9-取消。
     */
    private String orderStatus;

    /**
     * 订单状态名称
     */
    private String orderStatusName;


    /**
     * 订单类型： 1、B2C订单 99、充值订单 97、消费订单 96、虚拟订单 11，全渠道订单
     */
    private Integer orderType;


    /**
     * 订单实付金额
     */
    private String paymentAmount;

    /**
     * 支付信息
     */
    private PaymentInfo paymentInfo;

    /**
     * 预售信息
     */
    private PreSellDetail preSellDetail;


    /**
     * 订单号
     */
    private String processStoreId;


    /**
     * 支付信息
     */
    @Data
    public static class PaymentInfo {
        /**
         * 第三方支付单号
         */
        private String channelTrxNo;

        /**
         * 通道id
         */
        private String interactId;

        /**
         * 支付项详情列表
         */
        private List<PaymentItem> paymentItemList;


        /**
         * 支付方式名称
         */
        private String paymentMethodName;

        /**
         * 支付状态
         */
        private Integer paymentStatus;

        /**
         * 支付时间
         */
        private String paymentTime;

        /**
         * 支付类型
         */
        private String paymentType;


        /**
         * 支付类型名称
         */
        private String paymentTypeName;


        private String tradeId;
    }


    /**
     * 支付项详情
     */
    @Data
    public static class PaymentItem {
        /**
         * 第三方支付单号
         */
        private String channelTrxNo;

        /**
         * 渠道类型
         */
        private String channelType;


        /**
         * 支付拓展信息
         */
        private PayExt payExt;

        /**
         * 支付类型名称
         */
        private BigDecimal paymentAmount;

        /**
         * 支付方式
         */
        private Integer paymentMethodId;

        /**
         * 支付类型名称
         */
        private Integer paymentType;

        /**
         * 支付单号
         */
        private String tradeId;

    }

    /**
     * 支付拓展信息
     */
    @Data
    public static class PayExt {
        /**
         * 支付商户号
         */
        private String channelmid;

    }


    /**
     * 买家信息
     */
    @Data
    public static class BuyerInfo {

        /**
         * 用户id
         */
        private String wid;
    }


    /**
     * 配送详情
     */
    @Data
    public static class DeliveryDetail {
        /**
         * 配送方式(1物流配送(走快递发货流程) 2同城限时达（走即时物流流程）3到店自提（走自提流程）)
         */
        private Integer deliveryType;
        /**
         * 配送方式名称
         */
        private String deliveryTypeName;
        /**
         * 物流配送详情
         */
        private LogisticsDeliveryDetail logisticsDeliveryDetail;
        /**
         * 自提点详情
         */
        private SelfPickupDetail selfPickupDetail;
    }


    /**
     * 物流配送详情
     */
    @Data
    public static class LogisticsDeliveryDetail {
        /**
         * 收货人详细地址 (最多100个字符)
         */
        private String receiverAddress;
        /**
         * 收货人所在乡镇/街道
         */
        private String receiverArea;
        /**
         * 收货人所在城市
         */
        private String receiverCity;
        /**
         * 收货人所在区/县
         */
        private String receiverCounty;
        /**
         * 收货人电话
         */
        private String receiverMobile;
        /**
         * 收货人姓名
         */
        private String receiverName;
        /**
         * 收货人所在省份
         */
        private String receiverProvince;

    }


    /**
     * 自提点详情
     */
    @Data
    public static class SelfPickupDetail {
        /**
         * 收货人所在乡镇/街道
         */
        private String selfPickupArea;
        /**
         * 自提点所在城市
         */
        private String selfPickupCity;
        /**
         * 自提点所在区/县
         */
        private String selfPickupCounty;

        /**
         * 自提人电话
         */
        private String selfPickupMobile;
        /**
         * 自提点所在省份
         */
        private String selfPickupProvince;

        /**
         * 自提点详细地址 (最多100个字符)
         */
        private String selfPickupSiteAddress;

        /**
         * 自提人姓名
         */
        private String selfPickupUser;

    }


    /**
     * 折扣信息
     */
    @Data
    public static class DiscountInfo {
        /**
         * 余额抵扣金额
         */
        private BigDecimal balanceDiscountAmount;

        /**
         * 通用优惠券信息
         */
        private List<CommonDiscountInfo> commonDiscountInfo;
        /**
         * 优惠券信息
         */
        private List<CouponDiscountInfo> couponDiscountInfo;
        /**
         * 通用优惠券信息
         */
        private List<GiftCardDiscountInfo> giftCardDiscountInfo;
        /**
         * 积分抵扣金额
         */
        private BigDecimal memberPoIntegersDiscountAmount;
        /**
         * 会员折扣金额
         */
        private BigDecimal membershipDiscountAmount;
        /**
         * 满减满折金额
         */
        private BigDecimal promotionDiscountAmount;
    }


    /**
     * 通用优惠券信息
     */
    @Data
    public static class CommonDiscountInfo {
        /**
         * 归属类型：0优惠减免，1优惠抵扣
         */
        private Integer attributionType;
        /**
         * 折扣金额
         */
        private BigDecimal discountAmount;
        /**
         * 折扣类型：0商品，1运费，2订单
         */
        private Integer discountObjectType;
        /**
         * 折扣类型，折扣类型，目前涉及：2满减邮，28整单优惠，29抹零 ，33储值卡，37第X件X折
         */
        private Integer discountType;

        /**
         * 优惠券标题
         */
        private String name;
        /**
         * 折扣外部编码
         */
        private String outCode;

        /**
         * 是否参与金额计算：0不参与金额计算，1参与金额计算
         */
        private Integer participate;
    }


    /**
     * 优惠券信息
     */
    @Data
    public static class CouponDiscountInfo {
        /**
         * 卡券code
         */
        private String code;
        /**
         * 卡券模板id
         */
        private Integer couponTemplateId;
        /**
         * 卡券描述
         */
        private String desc;
        /**
         * 分摊的折扣金额
         */
        private BigDecimal discountAmount;

        /**
         * 优惠券标题
         */
        private String name;
        /**
         * 卡券折扣总金额
         */
        private BigDecimal totalDiscountAmount;

    }


    /**
     * 通用优惠券信息
     */
    @Data
    public class GiftCardDiscountInfo {
        /**
         * 卡号
         */
        private String cardNo;
        /**
         * 标题
         */
        private String name;
        /**
         * 折扣金额
         */
        private BigDecimal totalDiscountAmount;
        /**
         * 是否参与金额计算：0不参与金额计算，1参与金额计算
         */
        private Integer type;
    }


    /**
     * 导购信息
     */
    @Data
    public static class GuideInfo {
        /**
         * 销售导购编号
         */
        private String guiderNo;
        /**
         * 销售导购id
         */
        private String guiderWid;
        /**
         * 归属导购编号
         */
        private String personalGuiderNo;

        /**
         * 专属导购名称
         */
        private String privateGuiderName;
        /**
         * 专属导购id
         */
        private String privateGuiderWid;
    }


    @Data
    public static class Item {
        /**
         * spu编码（商品编码 ）
         */
        private String goodsCode;
        /**
         * 商品级折扣信息
         */
        private GoodsDiscountInfo goodsDiscountInfo;

        /**
         * 条码id
         */
        private Integer goodsId;
        /**
         * 商品标题
         */
        private String goodsTitle;
        /**
         * 订单项id
         */
        private String id;

        /**
         * 规格图片
         */
        private String imageUrl;
        /**
         * 商品标签信息
         */
        private List<Label> labelList;

        /**
         * 市场价
         */
        private BigDecimal marketPrice;
        /**
         * 原始价
         */
        private BigDecimal originalPrice;
        /**
         * 实付金额 + 现金券 + 储值卡
         */
        private BigDecimal paymentActual;
        /**
         * 实付金额
         */
        private BigDecimal paymentAmount;
        /**
         * 商品售价
         */
        private BigDecimal price;

        /**
         * 单品类型：0-无单品，1-单品，2-组合品，3-付费券（0、1、2对应普通订单，3对应虚拟订单）
         */
        private Integer productType;

        /**
         * 维权单id
         */
        private String rightsOrderId;
        /**
         * 维权状态
         */
        private Integer rightsStatus;
        /**
         * 维权状态名称
         */
        private String rightsStatusName;

        /**
         * sku金额（skuAmount=price*skuNum）
         */
        private Integer skuAmount;


        /**
         * 家编码（规格编码）
         */
        private String skuCode;
        /**
         * 条码id
         */
        private Integer skuId;

        /**
         * 规格名称
         */
        private String skuName;

        /**
         * 购买数量
         */
        private Integer skuNum;
        /**
         * sku总金额（totalAmount=skuAmount+其他费用）
         */
        private BigDecimal totalAmount;
        /**
         * 订单项抵扣优惠总金额
         */
        private BigDecimal totalDiscountAmount;
        /**
         * 仓库id
         */
        private Integer warehouseId;
        /**
         * 仓库名称
         */
        private String warehouseName;
        /**
         * 仓库类型
         */
        private Integer warehouseType;

    }


    /**
     * 商品级折扣信息
     */
    @Data
    public class GoodsDiscountInfo {
        /**
         * 余额抵扣金额
         */
        private BigDecimal balanceDiscountAmount;

        /**
         * 通用折扣信息
         */
        private List<CommonDiscountInfo> commonDiscountInfo;
        /**
         * 券折扣金额
         */
        private BigDecimal couponDiscountAmount;
        /**
         * 优惠券信息
         */
        private List<CouponDiscountInfo> couponDiscountInfo;

        /**
         *积分抵扣金额
         */
        private BigDecimal memberPoIntegersDiscountAmount;
        /**
         * 会员折扣金额
         */
        private BigDecimal membershipDiscountAmount;
        /**
         * 满减满折金额
         */
        private BigDecimal promotionDiscountAmount;
    }


    @Data
    public class Label {
        /**
         * 业务id
         */
        private String attachId;
        /**
         * 	业务内容
         */
        private Attachment attachment;

        /**
         * 	标签类型
         */
        private String labelType;
    }


    @Data
    public static class Attachment {
        /**
         * 直播房间id
         */
        private String roomId;
    }


    /**
     * 会员信息
     */
    @Data
    public class MemberDetailInfoVo {
        /**
         * 会员列表信息
         */
        private List<MemberInfo> memberInfoList;

    }


    @Data
    public class MemberInfo {
        private String memberCardCode;
    }


    @Data
    public class MerchantInfo {
        /**
         * 商家名称
         */
        private String merchantTitle;
        /**
         * 商家id
         */
        private String pid;

        /**
         * 服务门店id（微商城无需处理）（ps服务门店是实际负责发货和提供自提服务的门店）
         */
        private String processStoreId;

        /**
         * 服务门店名称（微商城无需处理）（ps服务门店是实际负责发货和提供自提服务的门店）
         */
        private String processStoreTitle;

        /**
         * 自提点id
         */
        private String selfPickupSiteId;
        /**
         * 销售门店id（微商城无需处理）
         */
        private String storeId;

        /**
         * 销售门店名称（微商城无需处理）
         */
        private String storeTitle;
    }


    /**
     * 预售信息
     */
    @Data
    public static class PreSellDetail {

        /**
         * 预售方式： 1：全款预售 2：定金预售
         */
        private Integer depositPayType;
    }


}
