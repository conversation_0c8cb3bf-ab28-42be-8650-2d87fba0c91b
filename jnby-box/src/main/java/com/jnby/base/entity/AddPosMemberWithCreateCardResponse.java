package com.jnby.base.entity;


import lombok.Data;


/**
 * gic开卡 返回处理类
 * <AUTHOR>
 * @date 2021/9/7
 */
@Data
public class AddPosMemberWithCreateCardResponse {

    /**
     * 1-成功，0-失败，其他见错误编码
     */
    private String result;

    /**
     * 处理失败时的描述信息
     *
     */
    private String cause;

    /**
     * 请求包中的序列原样返回
     */
    private String transId;

    private String id;

    private String idType;

    private String objUserId;

    private String originalRespose;

    /**
     * 返回信息
     */
    private data data;

    @Data
    public static class data{
        /**
         * 	会员卡号
         */
        private String cardNo;

        /**
         * 会员卡编码id
         */
        private String memberId;

        /**
         * 会员卡编码id
         */
        private String cliqueMemberId;

        /**
         * 	集团卡号
         */
        private String cliqueCardNo;

        /**
         * 微信openid
         */
        private String openid;

        /**
         * 小程序openid
         */
        private String appletOpenid;

        /**
         * 	unionid
         */
        private String unionid;

    }
}
