package com.jnby.base.entity;

import com.google.gson.annotations.SerializedName;
import com.jnby.common.Jic.ValidBizResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class WeimoCalcDiscountWithLastPriceEntity {
    /**
     * 商品列表
     */
    @ApiModelProperty(value = "商品列表")
    private List<Goods> goodsList;


    @Data
    public static class Goods {
        /**
         * 商品id
         */
        @ApiModelProperty(value = "商品id")
        @SerializedName("goodsId")
        private String goodsId;
    }

}
