package com.jnby.base.entity;

import lombok.Data;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @descr: 收钱吧查询返回
 */
@Data
public class QueryEntity {

    /**
     * 收钱吧终端ID
     * Y
     */
    private String terminal_sn;


    /**
     * 收钱吧唯一订单号
     * N
     */
    private String sn;


    /**
     * 商户订单号
     * Y
     */
    private String client_sn;


    /**
     * 流水状态
     * Y
     */
    private String status;


    /**
     * 订单状态
     * Y
     */
    private String order_status;


    /**
     * 支付方式
     * Y
     */
    private String payway;

    /**
     * 支付方式名称
     * Y
     */
    private String payway_name;


    /**
     * 付款人id
     * Y
     */
    private String payer_uid;


    /**
     * 支付平台的订单凭证号
     * Y
     */
    private String trade_no;


    /**
     * 交易总金额
     * Y
     */
    private String total_amount;

    /**
     * 剩余金额
     * Y
     */
    private String net_amount;


    /**
     * 本次操作金额
     * N
     */
    private String settlement_amount;


    /**
     * 上次操作在收钱吧的完成时间
     * N
     */
    private String finish_time;


    /**
     * 上次操作再支付平台完成的时间
     * N
     */
    private String channel_finish_time;


    /**
     *商品概述
     * Y
     */
    private String subject;


    /**
     * 操作员
     * Y
     */
    private String operator;


//    /**
//     * 优惠详情
//     * N
//     */
//    private JSONObject provider_response;
//
//
//    /**
//     * 活动优惠
//     * N
//     */
//    private JSONObject payment_list;

}
