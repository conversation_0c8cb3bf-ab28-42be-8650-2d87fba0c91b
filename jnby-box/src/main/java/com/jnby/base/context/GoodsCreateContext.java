package com.jnby.base.context;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商品创建上下文
 * <AUTHOR>
 * @version 1.0
 * @date 3/5/21 9:14 AM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsCreateContext {

    /**
     * 商品ID
     */
    private String id;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 卡信息ID
     */
    private String memberCardId;
    /**
     * 卡天数ID
     */
    private String memberCardTypeId;

    private String name;

    private String label;

    private String content;

    private String firstImg;

    private String posterImg;

    private String detailImgs;

    private Long price;

    /**
     * 商品类型 2000 会员卡
     */
    private Long goodsType;

    /**
     * 卡类型 1订阅卡，2体验卡
     */
    private Long memberType;

    private Long days;
    private Long status = 1L;

    private String brandIds;

    private Integer applicableParty;


    private List<Integer> brandIdsList;

    @ApiModelProperty(value = "品牌ID")
    private String brandId;

    @ApiModelProperty(value = "适用卡等级")
    private Integer cardLevel;

    private Integer levelId;


}
