package com.jnby.base.context.couponHttp;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class VoucherValidityDelayContext {
    /**
     * 操作类型，1：指定日期，2：顺延x天
     */
    private Integer optType;

    /**
     * 	延期后失效日期
     */
    private Integer delayDays;

    /**
     * 延期原因
     */
    private String remark;

    /**
     * 操作人工号
     */
    private Integer workNo;

    /**
     * 券号
     */
    private List<String> voucherNos;
}
