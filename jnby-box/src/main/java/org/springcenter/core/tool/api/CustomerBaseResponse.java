package org.springcenter.core.tool.api;


import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(description = "会员服务基础返回信息")
@Data
public class CustomerBaseResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "状态码", required = true)
    @SerializedName("code")
    private int code;


    @ApiModelProperty(value = "是否成功", required = true)
    @SerializedName("success")
    private boolean success;


    @ApiModelProperty(value = "返回消息", required = true)
    @SerializedName("msg")
    private String msg;


    @ApiModelProperty("响应数据")
    @SerializedName("data")
    private T data;


}
