package com.jnby.moudle.zto;

import com.alibaba.fastjson.JSON;
import com.jnby.WebApp;
import com.jnby.infrastructure.box.mapper.BoxMapper;
import com.jnby.infrastructure.box.mapper.FashionerMapper;
import com.jnby.infrastructure.box.mapper.SysStoreMapper;
import com.jnby.infrastructure.box.mapper.SysUserMapper;
import com.jnby.infrastructure.box.model.BoxWithBLOBs;
import com.jnby.infrastructure.box.model.FashionerWithBLOBs;
import com.jnby.infrastructure.box.model.SysStore;
import com.jnby.infrastructure.box.model.SysUser;
import com.jnby.module.order.service.box.IExpressService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2022/9/26 18:56
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={WebApp.class})
public class GuideTest {

    @Autowired
    private IExpressService iExpressService;

    @Autowired
    private SysStoreMapper sysStoreMapper;

    @Autowired
    private BoxMapper boxMapper;

    @Autowired
    private FashionerMapper fashionerMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Test
    public void cancelSf() {
        Boolean aBoolean = iExpressService.checkAndCancelExpress("3262262338374828033");
        System.out.println(aBoolean);
    }

    @Test
    public void getDate() {

        List<SysStore> sysStores = sysStoreMapper.selectAll();
        System.out.println(JSON.toJSON(sysStores));
        SysStore sysStore = sysStoreMapper.selectDistributeInfo("********************************");
        System.out.println(sysStore);
    }

    //SELECT CREATE_FAS_ID  FROM BOX b;
    //SELECT USER_ID  FROM FASHIONER f WHERE ID = CREATE_FAS_ID;
    //SELECT STORE_ID  FROM SYS_USER su WHERE ID = USER_ID;
    //SELECT C_STORE_ID,C_STORE_NAME,C_CUSTOMER_ID  FROM SYS_STORE ss WHERE ID  = STORE_ID;
    @Test
    public void getData() {
        BoxWithBLOBs boxWithBLOBs = boxMapper.selectByPrimaryKey("********************************");
        System.out.println(JSON.toJSONString(boxWithBLOBs));
        FashionerWithBLOBs fashionerWithBLOBs = fashionerMapper.selectByPrimaryKey(boxWithBLOBs.getCreateFasId());
        System.out.println(JSON.toJSONString(fashionerWithBLOBs));
        SysUser sysUser = sysUserMapper.selectByPrimaryKey(fashionerWithBLOBs.getUserId());
        System.out.println(JSON.toJSONString(sysUser));
        SysStore sysStore = sysStoreMapper.selectByPrimaryKey(sysUser.getStoreId());
        System.out.println(JSON.toJSONString(sysStore));
        SysStore sysStore1 = sysStoreMapper.selectDistributeInfo("********************************");
        System.out.println(sysStore1);
    }

}
