package com.jnby.message;

import com.alibaba.fastjson.JSONObject;
import com.jnby.WebApp;
import com.jnby.base.context.CardmainInfoQueryContext;
import com.jnby.base.entity.CardmainInfoQueryEntity;
import com.jnby.module.jic.service.IWeiXinService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date:2023/4/1 17:12
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={WebApp.class})
public class BaseInfoTest {

    @Autowired
    private IWeiXinService weiXinService;

    @Test
    public void jicBaseInfo() {
        CardmainInfoQueryContext context = new CardmainInfoQueryContext();
        context.setBrandId("2504948039");
        context.setUnionId("oZpUxswthN4Zg-k4g5GAF-82FhXs");
        CardmainInfoQueryEntity entity = weiXinService.cardBaseInfo(context);
        System.out.println("========" + JSONObject.toJSONString(entity));
    }
}
